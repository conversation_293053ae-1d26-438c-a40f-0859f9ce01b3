# -*- coding: utf-8 -*-
import os
VERSION = "0.5"

mysql_host = os.environ.get('DB_HOST', '127.0.0.1')
mysql_port = int(os.environ.get('DB_PORT', 3306))
mysql_db = os.environ.get('DB_NAME', 'bgpdata')
mysql_tb = os.environ.get('DB_TABLE', 'bgpcountryreach')
mysql_user = os.environ.get('DB_USER', 'root')
mysql_password = os.environ.get('DB_PASSWORD', '')
proxy = os.environ.get('PROXY', '')

# ############### database config ###################
mysql_charset = 'utf8'  # 只能写utf8，不能写utf-8
sql_create_db = f"CREATE TABLE if not exists bgpstat ( \
                time BIGINT(20) DEFAULT NULL, \
                country_name varchar(128) DEFAULT NULL, \
                achievable_rate float(6,5) DEFAULT NULL);"
                #'achievable_rate' float(1,4) DEFAULT NULL);'''

sql_insert_db = f"INSERT INTO bgpstat ( \
                time , country_name , achievable_rate) VALUES (%s, %s, %s);"

sql_fetch_last_time = f"SELECT MAX(time) FROM {mysql_tb}"
sql_fetch_last_N_time = f"SELECT distinct time FROM {mysql_tb} ORDER BY time DESC LIMIT %s"

DB_CONN = "mongodb://*************:27017/"
DB_SET = "IP-GEO"
DB_COL = "AS_info_20230531_083904"

RESULT_DB_CONN = "mongodb://*************:27017/"
RESULT_DB_SET = "BGP"
RESULT_DB_COL = "area_reachable_static"

PROXY = proxy

# datesource
URL = "http://routeviews.org/"

# 最大失败次数
MAX_FAIL_COUNT = 5

TIMEZONE = "UTC"

EMPTY_FILE_SIZE = 241

ITER_FLAG = True