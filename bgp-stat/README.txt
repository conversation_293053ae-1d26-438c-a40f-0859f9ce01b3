# 修改内容
docker exec -it mytomcat /bin/bash
cd webapps/ROOT
rm -f index.jsp
echo hello world > index.html
exit

# 提交为新镜像
docker commit -m="修改了首页" -a="华安" mytomcat huaan/tomcat:v1.0

# 使用新镜像运行容器
docker run --name tom -p 8080:8080 -d huaan/tomcat:v1.0
###########################################
导出依赖包
pip list --format=freeze > requirements.txt
###########################################
# mysql 远程提示 Host is not allowed to connect to this MySQL server
1.use mysql;
2.update user set Host='%' where User='root';
3.flush privileges;

1.GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY 'password' 
##########################################
# 导出导入镜像
sudo docker save -o bgpstat.tar bgpstat:v1
sudo chmod 777 bgpstat.tar

docker load -i bgpstat.tar

##########################################
# 删除镜像
1.docker rmi bgp:v1
docker rmi image_id
2.docker image prune

*3.sudo docker builder prune  # clear metadata
###################################
# 构建新镜像
1.sudo docker image pull caida/bgpstream
2.sudo docker image list
3.sudo docker build -t bgp:v1 .

#when meet "ERR Temporary failure resolving 'mirrors.aliyun.com'"
1.sudo gedit /etc/default/docker
delete the '#' before DOCKER_OPTS
#export http_proxy="http://************:7890/"
2.sudo service docker restart

#when meet "Could not wait for server fd - select"
1.sudo gedit /etc/resolv.conf
add below:
nameserver *******
nameserver *******
2.sudo gedit /etc/default/docker
export http_proxy="http://************:7890/"

################不能联网，不显示网络图标####################
#no network when vm is copyed
1.ifconfig -a  # check 网卡 name
2.sudo gedit /etc/network/interfaces
# 添加如下
auto lo
iface lo inet loopback
auto ens33
iface ens33 inet dhcp

3.sudo nmcli networking on

4.sudo gedit /etc/systemd/resolved.conf
DNS=114.114.114.114

先改为NAT模式
5.sudo service NetworkManager stop
6.sudo rm /var/lib/NetworkManager/NetworkManager.state
7.sudo gedit /etc/NetworkManager/NetworkManager.conf
[ifupdown]
managed=true
8.sudo service NetworkManager status
9.sudo service NetworkManager start
最后改为桥接模式
