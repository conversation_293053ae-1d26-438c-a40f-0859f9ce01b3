# -*- coding: utf-8 -*-
import os
import setting
from util.singleton import Singleton
from util.lazyProperty import Lazy<PERSON>roper<PERSON>
from util.six import reload_six, withMetaclass


class ConfigHandler(withMetaclass(Singleton)):

    def __init__(self):
        pass

    @<PERSON>zy<PERSON>roperty
    def dbConn(self):
        return os.getenv("DB_CONN", setting.DB_CONN)

    @LazyProperty
    def resultDbConn(self):
        return os.getenv("RESULT_DB_CONN", setting.RESULT_DB_CONN)

    @LazyProperty
    def url(self):
        return os.getenv("URL", setting.URL)

    @LazyProperty
    def maxFailCount(self):
        return int(os.getenv("MAX_FAIL_COUNT", setting.MAX_FAIL_COUNT))

    @LazyProperty
    def timezone(self):
        return os.getenv("TIMEZONE", setting.TIMEZONE)

    @LazyProperty
    def emptyFileSize(self):
        return os.getenv("EMPTY_FILE_SIZE", setting.EMPTY_FILE_SIZE)
