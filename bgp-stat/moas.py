from main import *
pfx_peers_asn = {}
info_id = {}
last_id = -1


def get_peer_id(col, ip, asn):
    global info_id
    global last_id
    if col not in info_id:
        info_id[col] = {}
    if ip not in info_id[col]:
        info_id[col][ip] = {}
    if asn not in info_id[col][ip]:
        last_id += 1
        info_id[col][ip][asn] = last_id
    peer_id = info_id[col][ip][asn]
    return peer_id


def add_prefix_peer_origin(ts, pfx, peer, asn):
    global pfx_peers_asn
    global peer_info
    moas = 0
    already_there = 0
    if pfx not in pfx_peers_asn:
        pfx_peers_asn[pfx] = {}
    for p in pfx_peers_asn[pfx]:
        if p != peer and pfx_peers_asn[pfx][p] != asn:
            moas = 1
        if pfx_peers_asn[pfx][p] == asn:
            already_there = 1
    if int(peer) not in pfx_peers_asn[pfx]:
        pfx_peers_asn[pfx][int(peer)] = asn
    else:
        pfx_peers_asn[pfx][int(peer)] = asn
    if already_there == 0 and moas == 1:
        #print(ts, "MOAS", pfx, pfx_peers_asn[pfx])
        pass


def remove_prefix_peer(pfx, peer):
    global pfx_peers_asn
    global peer_info
    if pfx not in pfx_peers_asn:
        return
    if peer not in pfx_peers_asn[pfx]:
        return
    del pfx_peers_asn[pfx][int(peer)]


def remove_peer(peer):
    global pfx_peers_asn
    for pfx in pfx_peers_asn:
        if int(peer) in pfx_peers_asn[pfx]:
            del pfx_peers_asn[pfx][int(peer)]


def detect_moas(datatype, filepath):
    stream = pybgpstream.BGPStream(data_interface="singlefile")
    stream.set_data_interface_option("singlefile", datatype, filepath)
    for rec in stream.records():
        for elem in rec:
            peer_id = get_peer_id(rec.collector, elem.peer_address, elem.peer_asn)
            if elem.type == 'R' or elem.type == 'A':
                path = elem.fields['as-path']
                ases = path.split(" ")
                add_prefix_peer_origin(rec.time, elem.fields['prefix'], peer_id, ases[-1])
            if elem.type == 'W':
                remove_prefix_peer(elem.fields['prefix'], peer_id)
            if elem.type == 'S' and elem.fields['new-state'] != 'established':
                print(elem.__str__())
                remove_peer(peer_id)


if __name__ == '__main__':
    detect_moas('rib-file', 'bgpdata/2023.07/RIBS/rib.20230729.0000.bz2')
    #detect_moas('upd-file', 'bgpdata/2023.07/RIBS/rib.20230729.0000.bz2')