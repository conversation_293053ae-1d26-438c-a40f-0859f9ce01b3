# coding=utf-8
import json
import re
import gc
from threading import Thread
import time
import pybgpstream
import datetime
import setting
from util.six import Queue
import utils
import os
import gdown
from apscheduler.schedulers.blocking import BlockingScheduler
import datetime
from handler.logHandler import LogHandler, INFO, DEBUG
from handler.configHandler import ConfigHandler
from mysql_conn import MysqlDB
import pandas as pd
import multiprocessing as mp
from queue import Empty as QueueEmpty

scheduler_log = LogHandler("scheduler", level=INFO)
NUM_WORKERS = os.environ.get('NUM_WORKERS', 8)
# 2h 刷新矩阵  15min一次更新矩阵  两个相加

#读取各个字典，省份-国家、城市-国家、城市-省份-，国家-IP数量，省份-IP数量，城市-ip数量
with open("input/dictionary_province_country.txt", "r", encoding="utf-8")as fp:
    dictionary_province_country = json.load(fp)
with open("input/dictionary_city_country.txt", "r", encoding="utf-8")as fp:
    dictionary_city_country = json.load(fp)
with open("input/dictionary_city_province.txt", "r", encoding="utf-8")as fp:
    dictionary_city_province = json.load(fp)
with open("input/country_counts_dict.txt", "r", encoding="utf-8")as fp:
    country_counts_dict = json.load(fp)
with open("input/province_counts_dict.txt", "r", encoding="utf-8")as fp:
    province_counts_dict = json.load(fp)
with open("input/city_counts_dict.txt", "r", encoding="utf-8")as fp:
    city_counts_dict = json.load(fp)
with open("input/asn_counts_dict.txt", "r", encoding="utf-8")as fp:
    asn_counts_dict = json.load(fp)
#国家、省份、城市、小写到大写映射字典
with open("input/lower_up_country_dict.txt", "r", encoding="utf-8")as fp:
    lower_up_country_dict = json.load(fp)
with open("input/lower_up_province_dict.txt", "r", encoding="utf-8")as fp:
    lower_up_province_dict = json.load(fp)
with open("input/lower_up_city_dict.txt", "r", encoding="utf-8")as fp:
    lower_up_city_dict = json.load(fp)   

def gen_file_url(datatype, _request_time=''):
    if _request_time:
        request_time = _request_time
    else:
        request_time = datetime.datetime.utcnow().replace(tzinfo=None)  # pare will not update nowtime when use this at fun default para
    if datatype == 'upd-file':
        last_15m_date_str = (utils.floor_date(request_time, minutes=15) - datetime.timedelta(minutes=15)).strftime("%Y%m%d.%H%M")  # datasourse publish last 15min data
        year_month_str = last_15m_date_str[:4] + '.' + last_15m_date_str[4:6]
        _url = "http://routeviews.org/bgpdata/{}/UPDATES/updates.{}.bz2".format(year_month_str, last_15m_date_str)
        output_time = last_15m_date_str
    elif datatype == 'rib-file':
        if request_time.hour % 2 != 0:
            request_time = request_time - datetime.timedelta(hours=1)
        last_2h_date_str = request_time.strftime("%Y%m%d.%H00")
        year_month_str = last_2h_date_str[:4] + '.' + last_2h_date_str[4:6]
        _url = "http://routeviews.org/bgpdata/{}/RIBS/rib.{}.bz2".format(year_month_str, last_2h_date_str)
        output_time = last_2h_date_str
    else:
        scheduler_log.error('datetype input error')
        raise 'datetype input error'

    return _url, output_time


def read_last_filename(filetype):
    log_filepath = 'log/{}.txt'.format(filetype)
    if os.path.exists(log_filepath):
        with open(log_filepath, 'r', encoding='utf-8') as f:
            last_filename = f.read().strip()
    else:
        last_filename = ''
    return last_filename


def write_last_filename(filetype, filename):
    with open('log/{}.txt'.format(filetype), 'w', encoding='utf-8') as f:
        f.write(filename)


def bgp_data_downloader(_url, date_source=setting.URL, _proxy=setting.PROXY, iter=False):
    conf = ConfigHandler()
    output_filepath = _url.replace(date_source, '')
    output_filename = os.path.basename(output_filepath)
    scheduler_log.info(f'now download filename: {output_filename}')
    filetype = output_filename.split('.')[0]
    last_filename = read_last_filename(filetype)
    scheduler_log.info(f'last filename: {last_filename}')
    output_dir = os.path.dirname(output_filepath)
    if iter:
        utils.del_files_in_dir(output_dir, without=last_filename)
        scheduler_log.info('finish delete old files without {}'.format(last_filename))
    # if output_filename == last_filename:
    #     # file has been processed
    #     scheduler_log.info('{} has been processed'.format(output_filename))
    #     return (False, output_filepath)
    if os.path.exists(output_filepath):
        if os.path.getsize(output_filepath) >= conf.emptyFileSize:
            scheduler_log.info('already download and filesize is correct {}'.format(output_filepath))
            return (True, output_filepath)
    utils.my_mkdir(output_dir)
    count = 0
    while count <= conf.maxFailCount:
        count += 1
        try:
            end_output_str = gdown.download(_url, output=output_filepath, use_cookies=False, proxy=_proxy, resume=True)
        except Exception as e:
            scheduler_log.info(f'Error:{e}, sleep 5 mins...')
            time.sleep(5*60)
            continue
        if end_output_str == output_filepath:
            scheduler_log.info('download {} success'.format(_url))
            write_last_filename(filetype, output_filename)
            return (True, output_filepath)
        else:
            scheduler_log.info('retry-{} download {}'.format(count, _url))
            continue

    scheduler_log.error('download {} failed, check network'.format(count, _url))
    return (False, None)


def bgp_data_parser(datatype, filepath):
    if not os.path.exists(filepath):
        return
    prefix_as_queue = Queue()
    filter_set = set()
    stream = pybgpstream.BGPStream(data_interface="singlefile")
    stream.set_data_interface_option("singlefile", datatype, filepath)
    record_count = 0
    st = time.time()
    if datatype == 'rib-file':
        for rec in stream.records():
            for elem in rec:
                record_count += 1
                if record_count%1000000 == 0:
                    scheduler_log.info(f'bgp_data_parser: {record_count}, time: {time.time()-st:.2f}')
                if elem.status != "valid":
                    continue
                if elem.type == 'R':
                    _peer_asn = elem.peer_asn
                    pfx = elem.fields["prefix"]
                    if ':' in pfx:
                        #  only ipv4
                        continue
                    ases = elem.fields["as-path"].split(" ")
                    if len(ases) > 0:
                        origin = ases[-1]
                        #filter_set.add((_peer_asn, pfx, origin, 'A'))
                        filter_set.add((pfx, origin, 'A'))
                else:
                    scheduler_log.warning(elem.__str__())
                    continue
        for msg in filter_set:
            prefix_as_queue.put(msg)
    elif datatype == 'upd-file':
        for rec in stream.records():
            for elem in rec:
                record_count += 1
                if record_count%100000 == 0:
                    scheduler_log.info(f'bgp_data_parser: {record_count}, time: {time.time()-st:.2f}')
                if elem.status != "valid":
                    continue
                pfx = elem.fields["prefix"]
                _peer_asn = elem.peer_asn
                if ':' in pfx:
                    #  only ipv4
                    continue
                if elem.type == 'A':
                    ases = elem.fields["as-path"].split(" ")
                    if len(ases) > 0:
                        origin = ases[-1]
                        #prefix_as_queue.put((_peer_asn, pfx, origin, 'A'))
                        prefix_as_queue.put((pfx, origin, 'A'))
                elif elem.type == 'W':
                    #prefix_as_queue.put((_peer_asn, pfx, None, 'W'))
                    prefix_as_queue.put((pfx, None, 'W'))
    else:
       raise 'error file type'

    scheduler_log.info('parser record count: {}'.format(record_count))
    return prefix_as_queue


def process_reachable_static(datatype):
    _url, file_time = gen_file_url(datatype)
    download_result = bgp_data_downloader(_url, iter=setting.ITER_FLAG)

    if download_result[0]:
        prefix_as_queue = bgp_data_parser(datatype, download_result[1])
        task_count = prefix_as_queue.qsize()
        scheduler_log.info('task count: {}'.format(str(task_count)))
        if task_count == 0:
            return
        
        progress_thread = Thread(target=progress, name='ProgressNew',
                                    args=(task_count, prefix_as_queue), daemon=True)
        progress_thread.start()
        static_thread = Thread(target=area_reachable_static, name=f'StaticNew',
                               args=(prefix_as_queue, file_time), daemon=True)
        static_thread.start()
        progress_thread.join()
        static_thread.join()
        return
    elif not download_result[1]:
        raise 'not finished download, check network'
    else:
        return


def process_reachable_static_continue(datatype):

    missing_time = fetch_last_static_time()
    if not missing_time:
        scheduler_log.info('last 3 days data filled')
        # process_reachable_static(datatype, _as_info, _country_prefix)
        return
    # start_time = missing_time.strftime('%Y%m%d%H%M')
    # time_series = pd.date_range(start=start_time, freq='59T', end=datetime.datetime.utcnow().replace(tzinfo=None).strftime('%Y%m%d%H%M'))
    # scheduler_log.info(f'prcess {time_series.to_list()}')
    scheduler_log.info(f'missing_time={missing_time}')
    missing_time = sorted(missing_time, reverse=True)
    for i in missing_time:
        scheduler_log.info(f'prcess {i}')
        _url, file_time = gen_file_url(datatype, i)
        download_result = bgp_data_downloader(_url, iter=setting.ITER_FLAG)
        if download_result[0]:
            prefix_as_queue = bgp_data_parser(datatype, download_result[1])
            task_count = prefix_as_queue.qsize()
            scheduler_log.info('task count: {}'.format(str(task_count)))
            if task_count == 0:
                continue
            
            progress_thread = Thread(target=progress, name='ProgressHistory',
                                     args=(task_count, prefix_as_queue), daemon=True)
            progress_thread.start()
            static_thread = Thread(target=area_reachable_static, name=f'StaticHistory',
                                args=(prefix_as_queue, file_time), daemon=True)
            static_thread.start()
            progress_thread.join()
            static_thread.join()
        elif not download_result[1]:
            raise 'not finished download, check network'
        else:
            continue
    
    return
        

def process_update_reachable_static():
    pass


def progress(total, _queue):
    st = time.time()
    while True:
        remaining = _queue.qsize()
        done = total - remaining
        if done % 100000 == 0:
            scheduler_log.info(f'progrees: {done}/{total}, time: {time.time()-st:.2f}')
        if remaining == 0:
            break


def lower_dict_to_up_dict(dict1, dict2):
    """
    将字典的键从小写转换成大写
    dict1:待转换的字典
    dict2:小写-大写映射的字典
    return：转换成大写后的字典
    """
    temp_dict = {}
    for key,value in dict1.items():
        try:

            temp_dict[dict2[key]] = value
        except Exception as e:
            #if key not in temp_dict:
                #temp_dict[key] = value
            scheduler_log.error(f"{key}小写转成大写{value}: {e}")

    return temp_dict

#拆分网段
def split_ip_str_gra(ip_str_gra):
    """
    将iP段划分成24网段，返回24网段的ip段list
    ip_str_gra：原始网段，***********/22
    返回24网段list，或者原网段list
    ***********/24,***********/24，***********/24，***********/24
    """
    
    ip_str, net_suffix = ip_str_gra.split("/")[0],ip_str_gra.split("/")[1]
    ip_str_list = ip_str.split(".")
    if int(net_suffix) >= 24:
        # print("net_suffix大于24，无需划分")
        return [ip_str_gra]
    elif int(ip_str_list[0]) == 0:#0.x.x.x类的ip不处理
         return []
    else:
        nums = 2**(24-int(net_suffix))#nums个24网段
        
        ip_str_gra_24_list = []
        for i in range(nums):
            if int(ip_str_list[2])+i > 255:
                temp_ip_str_gra = ip_str_list[0]+"."+str(int(ip_str_list[1])+((int(ip_str_list[2])+i)//255))+"."+str((int(ip_str_list[2])+i)%255)+"."+ip_str_list[3]+"/24"
                ip_str_gra_24_list.append(temp_ip_str_gra)                
            else:
                temp_ip_str_gra = ip_str_list[0]+"."+ip_str_list[1]+"."+str(int(ip_str_list[2])+i)+"."+ip_str_list[3]+"/24"
                ip_str_gra_24_list.append(temp_ip_str_gra)
        return ip_str_gra_24_list
    

#根据iP网段，统计国家和城市的IP出现数量，全局变量，不返回值。
# def area_counts(ip_str_gra, msg_type, asn):
def area_counts(feed_queue: mp.Queue, res_queue: mp.Queue, pid: int):
    # task_toal = feed_queue.qsize()
    # task_complete = 0
    #读取ipv4定位城市csv
    dbip_city_ipv4_df = pd.read_csv("input/ip-location-db-main/dbip-city-7z/dbip-city-ipv4-num.csv",header=None,
                                    names=["ip_str_start","ip_str_end","country","province","4","city","6","7","8","9",])
    dbip_city_ipv4_df = dbip_city_ipv4_df.fillna(value="unknow")

    #根据一个网段，返回省份城市的ip数量
    def area_counts_ip(ip_str_gra):
        """
        param array:ip_str_gra = "***********/24"
        return [(224, 'CN', 'Fujian', 'Amoy'), (32, 'CN', 'Fujian', 'Fuzhou')]
        若网段没有跨城市，则第二个元组为空，即()
        """
        
        # ip_str = ip_str_gra.split("/")
        
        ip_str, net_suffix = ip_str_gra.split("/")[0],ip_str_gra.split("/")[1]
        
        #if net_suffix != "24":
            #print("=============查看下非24网段=======：",net_suffix)

        nums = 2**(32-int(net_suffix))
        ip_long_min  = utils.ipToLong(ip_str)
        ip_max = utils.ipToLong(ip_str) + nums - 1
        #print("最小最大ip_num",ip_long_min,ip_max)

        index = utils.binary_search(dbip_city_ipv4_df["ip_str_end"],ip_max)
        if int(dbip_city_ipv4_df["ip_str_start"][index]) <= ip_long_min:
            res = nums
            #print(res,dbip_city_ipv4_df["country"][index],dbip_city_ipv4_df["province"][index],dbip_city_ipv4_df["city"][index])
            
            return [(res,dbip_city_ipv4_df["country"][index],dbip_city_ipv4_df["province"][index],dbip_city_ipv4_df["city"][index]),()]
        else:
            tmp_1 = dbip_city_ipv4_df["ip_str_start"][index]-ip_long_min #多少个ip不在这个index内，即在前一个ip内
            res = nums - tmp_1 #在当前index内的ip数量
            
            #print(res,dbip_city_ipv4_df["country"][index],dbip_city_ipv4_df["province"][index],dbip_city_ipv4_df["city"][index])
            #print(tmp_1,dbip_city_ipv4_df["country"][index-1],dbip_city_ipv4_df["province"][index-1],dbip_city_ipv4_df["city"][index-1])
            
            return [(res,dbip_city_ipv4_df["country"][index],dbip_city_ipv4_df["province"][index],dbip_city_ipv4_df["city"][index]),
                    (tmp_1,dbip_city_ipv4_df["country"][index-1],dbip_city_ipv4_df["province"][index-1],dbip_city_ipv4_df["city"][index-1])]
    
    while True:
        try:
            ip_str_gra, msg_type, asn = feed_queue.get(True, 10)
            # block为True,就是如果队列中无数据了。
            #   |—————— 若timeout默认是None，那么会一直等待下去。
            #   |—————— 若timeout设置了时间，那么会等待timeout秒后才会抛出Queue.Empty异常
            # block 为False，如果队列中无数据，就抛出Queue.Empty异常
            res_num_counts = area_counts_ip(ip_str_gra)
            try:
                asn_ = re.findall(r'\d+', asn)
                asn = str(asn_[0])
            except:
                scheduler_log.error(f'pid[{pid}], extracting asn error: {e}')
            res_queue.put((asn, msg_type, res_num_counts))
            
            # print("IP没有跨网段counts_province,counts_city:",(counts_province,counts_city))
            # task_complete += 1
            # if task_complete % 100000==0:
            #     scheduler_log.info(f'pid[{pid}], process: {task_complete}/{total_task}')
        except QueueEmpty:
            break

        except Exception as e:
            scheduler_log.error(f'{ip_str_gra}, {asn}, {msg_type}: {e}')
    scheduler_log.info(f'pid[{pid}], exiting...')

def gather_area_count(feed_queue: mp.Queue, res_queue: mp.Queue, task_total: int):
    counts_country, counts_province, counts_city, counts_asn = {}, {}, {}, {}
    task_complete = 0
    def gather_fn():
        if asn not in counts_asn:
            counts_asn[asn] = 0
        if res_num_counts[1] == ():#IP没有跨网段
            if res_num_counts[0][1] not in counts_country:
                counts_country[res_num_counts[0][1]] = 0
            if res_num_counts[0][2] not in counts_province:
                counts_province[res_num_counts[0][2]] = 0
            if res_num_counts[0][3] not in counts_city:
                counts_city[res_num_counts[0][3]] = 0

            if msg_type == 'A':
                counts_country[res_num_counts[0][1]] +=  res_num_counts[0][0]
                counts_province[res_num_counts[0][2]] +=  res_num_counts[0][0]
                counts_city[res_num_counts[0][3]] +=  res_num_counts[0][0]
                counts_asn[asn] += res_num_counts[0][0]
            elif msg_type == 'W':   # update not run this
                counts_country[res_num_counts[0][1]] +=  res_num_counts[0][0]
                counts_province[res_num_counts[0][2]] +=  res_num_counts[0][0]
                counts_city[res_num_counts[0][3]] +=  res_num_counts[0][0]
                counts_asn[asn] += res_num_counts[0][0]
            
            
        else:#IP跨网段，默认只垮了一个网段
            # 跨网段时，最后一个网段
            if res_num_counts[0][1] not in counts_country:
                counts_country[res_num_counts[0][1]] = 0
            if res_num_counts[0][2] not in counts_province:
                counts_province[res_num_counts[0][2]] = 0
            if res_num_counts[0][3] not in counts_city:
                counts_city[res_num_counts[0][3]] = 0
            if res_num_counts[1][1] not in counts_country:
                counts_country[res_num_counts[1][1]] = 0
            if res_num_counts[1][2] not in counts_province:
                counts_province[res_num_counts[1][2]] = 0
            if res_num_counts[1][3] not in counts_city:
                counts_city[res_num_counts[1][3]] = 0

            if msg_type == 'A':
                counts_country[res_num_counts[0][1]] +=  res_num_counts[0][0]
                counts_province[res_num_counts[0][2]] +=  res_num_counts[0][0]
                counts_city[res_num_counts[0][3]] +=  res_num_counts[0][0]
                counts_asn[asn] += res_num_counts[0][0]
                #跨网段时，前一个网段
                counts_country[res_num_counts[1][1]] +=  res_num_counts[1][0]
                counts_province[res_num_counts[1][2]] +=  res_num_counts[1][0]
                counts_city[res_num_counts[1][3]] +=  res_num_counts[1][0]
                counts_asn[asn] += res_num_counts[1][0]
            elif msg_type == 'W':   # update not run this
                counts_country[res_num_counts[0][1]] +=  res_num_counts[0][0]
                counts_province[res_num_counts[0][2]] +=  res_num_counts[0][0]
                counts_city[res_num_counts[0][3]] +=  res_num_counts[0][0]
                counts_asn[asn] += res_num_counts[0][0]
                #跨网段时，前一个网段
                counts_country[res_num_counts[1][1]] +=  res_num_counts[1][0]
                counts_province[res_num_counts[1][2]] +=  res_num_counts[1][0]
                counts_city[res_num_counts[1][3]] +=  res_num_counts[1][0]
                counts_asn[asn] += res_num_counts[1][0]

    while True:
        try:
            asn, msg_type, res_num_counts = feed_queue.get(True, timeout=30) 
            gather_fn()
            task_complete += 1
            if task_complete % 500000==0:
                scheduler_log.info(f'gathering results {task_complete} / {task_total}')

        except QueueEmpty:
            break

        except Exception as e:
            scheduler_log.error(f'{res_num_counts}, {asn}, {msg_type}: {e}')
    res_queue.put((counts_country, counts_province, counts_city, counts_asn))
    scheduler_log.info(f'gathering result, country: {len(counts_country)}, province: {len(counts_province)}, city: {len(counts_city)}, asn: {len(counts_asn)}')
    scheduler_log.info(f'gathering completely, exiting...')


def split_dict_by_count(dictionary, count):
    """
    将字典拆分成多个小字典，每个字典count个键
    """
    sub_dicts = []
    keys = list(dictionary.keys())
    total_keys = len(keys)

    for i in range(0, total_keys, count):
        sub_dict = {k: dictionary[k] for k in keys[i:i + count]}
        sub_dicts.append(sub_dict)

    return sub_dicts



def area_reachable_static(prefix_as_queue, file_time):

    country_reachable, province_reachable, city_reachable, asn_reachable = {}, {}, {}, {}

    prefix_dict_ = {}
    if prefix_as_queue.qsize() == 0:
        return

    while not prefix_as_queue.empty():
        _prefix, _asn, msg_type = prefix_as_queue.get()
        # 简单去重，不能完全去掉包含关系的前缀
        ip_str_gra_24_list = split_ip_str_gra(_prefix)
        if not ip_str_gra_24_list:
            continue
        for ip_str_gra_24 in ip_str_gra_24_list:
            # TODO: msg_type meaning
            prefix_dict_[(ip_str_gra_24, 'A')] = _asn

    prefix_keys = list(prefix_dict_.keys()) 
    task_total = len(prefix_keys)
    scheduler_log.info(f'task start, task_total={task_total}')

    feed_queue = mp.Queue()
    res_queue = mp.Queue()
    gather_res_queue = mp.Queue()
    # area_counts(ip_str_gra, msg_type, asn)
    customer_process = [mp.Process(target=area_counts, args=(feed_queue, res_queue, i+1)) for i in range(NUM_WORKERS)]
    gather_process = mp.Process(target=gather_area_count, args=(res_queue, gather_res_queue, task_total))
    [p.start() for p in customer_process]
    gather_process.start()

    scheduler_log.info(f'{NUM_WORKERS} processes start, starting feed bgp data...')

    # for i in range(0, 1000000):     # for debug
    for i in range(0, task_total):
        k = prefix_keys[i]
        feed_queue.put((k[0], 'A', prefix_dict_[k]))
    scheduler_log.info(f'feed end...')
    [p.join() for p in customer_process]
    counts_country, counts_province, counts_city, counts_asn = gather_res_queue.get()
    gather_process.join()
    scheduler_log.info(f'get result, country: {len(counts_country)}, province: {len(counts_province)}, city: {len(counts_city)}, asn: {len(counts_asn)}')

    # 计算国家，省份，城市，AS的连通率
    scheduler_log.info('merge country')
    for country,nums in counts_country.items():
        tmp_country_reachable_res = nums/(country_counts_dict.get(country, 1) + 1e-6)
        if tmp_country_reachable_res > 1:
            tmp_country_reachable_res = 1.0
        country_reachable[country] = round(tmp_country_reachable_res, 5)
    scheduler_log.info('merge province')
    for province,nums in counts_province.items():
        tmp_province_reachable_res = nums/(province_counts_dict.get(province, 1) + 1e-6)
        if tmp_province_reachable_res > 1:
            tmp_province_reachable_res = 1.0
        province_reachable[province] = round(tmp_province_reachable_res, 5)
    scheduler_log.info('merge city')
    for city, nums in counts_city.items():
        tmp_city_reachable_res = nums/(city_counts_dict.get(city, 1) + 1e-6)
        if tmp_city_reachable_res > 1:
            tmp_city_reachable_res = 1.0
        city_reachable[city] =  round(tmp_city_reachable_res, 5)
    scheduler_log.info('merge asn')
    for asn, nums in counts_asn.items():
        if asn not in asn_counts_dict:
            tmp_asn_reachable_res = 1.0
        else:
            tmp_asn_reachable_res = nums/(asn_counts_dict[asn] + 1e-6)
        if tmp_asn_reachable_res > 1:
            tmp_asn_reachable_res = 1.0
        asn_reachable[asn] =  round(tmp_asn_reachable_res, 5)
    
    #将国家、省份、城市小写转回大写：
    country_reachable = lower_dict_to_up_dict(country_reachable, lower_up_country_dict)
    province_reachable = lower_dict_to_up_dict(province_reachable, lower_up_province_dict)
    city_reachable = lower_dict_to_up_dict(city_reachable, lower_up_city_dict)
    
    
    # save to db
    scheduler_log.info('save db')
    save_db_for_as_reach_count(file_time, asn_reachable, counts_asn, asn_counts_dict)
    save_db_for_country_reach_count(file_time, country_reachable, counts_country, country_counts_dict)
    save_db_for_province_reach_count(file_time, province_reachable, counts_province, province_counts_dict)
    save_db_for_city_reach_count(file_time, city_reachable, counts_city, city_counts_dict)
    scheduler_log.info('save end')
    del counts_country, counts_province, counts_city, counts_asn
    del country_reachable, province_reachable, city_reachable, asn_reachable
    gc.collect()


def fetch_last_static_time():
    days = 3
    check_et = datetime.datetime.utcnow().replace(tzinfo=None)
    check_et = check_et.replace(hour=(check_et.hour//2)*2, minute=0, second=0, microsecond=0)
    scheduler_log.info(f'check now time: {check_et}')
    check_st = check_et - datetime.timedelta(days=days)
    check_time_list = pd.date_range(start=check_st, freq='120T', end=check_et)
    scheduler_log.debug(f'check_time_list {check_time_list}')
    N = 24 * days
    min_last_time = None
    try:
        mysql_DB = MysqlDB()
        # last_time = mysql_DB.get_last_time()
        last_time_list = mysql_DB.get_last_N_time(N)
        scheduler_log.debug(f'exist_last_time_list {last_time_list}')
        # max_exist_time = last_time_list[0]      # 已有的最新数据时间
        last_time = list(set(check_time_list) - set(last_time_list))
        last_time.sort()
        # 3天内有数据缺失
        if last_time:
            min_last_time = min(last_time)  # 缺失数据的最小时间
        else:   # 3天内数据完全
            return
        scheduler_log.info(f'min_last_time: {min_last_time}')
        mysql_DB.close()
    except Exception as e:
        scheduler_log.error(e)
    if not min_last_time:
        return
    # if (datetime.datetime.utcnow().replace(tzinfo=None) - max_exist_time) > datetime.timedelta(days=7):     # 最新数据7天前，从3天前开始下载
    #     return datetime.datetime.utcnow().replace(tzinfo=None) - datetime.timedelta(days=3)
    # elif (datetime.datetime.utcnow().replace(tzinfo=None) - last_time) < datetime.timedelta(hours=2):
    #     return
    # else:
    return last_time


# (file_time, country_reachable, counts_country, country_counts_dict)
def save_db_for_country_reach_count(file_time, reach_ratio, reach_count, total_count):
    reach_count_tb = 'bgpcountryreach'
    sql_create_db = f"CREATE TABLE if not exists {reach_count_tb} ( \
                time BIGINT(20), \
                country_name varchar(128), \
                reach_ratio FLOAT(6,5) DEFAULT NULL, \
                reach_count BIGINT(20) DEFAULT NULL, \
                total_count BIGINT(20) DEFAULT NULL, \
                PRIMARY KEY (time, country_name));"
    sql_insert_db = f"INSERT INTO {reach_count_tb} ( \
                    time, country_name , reach_ratio, reach_count, total_count) VALUES (%s, %s, %s, %s, %s);"
    insert_list = [] # 新建一个空列表用来存储元组数据
    _time = str(utils.targettime_to_timestamp(file_time))
    for country, r in reach_ratio.items():
        country_lower = country.lower()
        tmp_item = (_time, country, r, reach_count.get(country_lower, -1), total_count.get(country_lower, -1)) # 构造元组
        insert_list.append(tmp_item)
    try:
        mysql_DB = MysqlDB()
        mysql_DB.create_table(sql_create_db)
        mysql_DB.insert(insert_list, sql_insert_db)
        mysql_DB.close()
    except Exception as e:
        scheduler_log.error(e)

def save_db_for_province_reach_count(file_time, reach_ratio, reach_count, total_count):
    reach_count_tb = 'bgpprovincereach'
    sql_create_db = f"CREATE TABLE if not exists {reach_count_tb} ( \
                time BIGINT(20), \
                country_name varchar(128), \
                province_name varchar(255), \
                reach_ratio FLOAT(6,5) DEFAULT NULL, \
                reach_count BIGINT(20) DEFAULT NULL, \
                total_count BIGINT(20) DEFAULT NULL, \
                PRIMARY KEY (time, country_name, province_name));"
    sql_insert_db = f"INSERT INTO {reach_count_tb} ( \
                    time, country_name, province_name, reach_ratio, reach_count, total_count) VALUES (%s, %s, %s, %s, %s, %s);"
    insert_list = [] # 新建一个空列表用来存储元组数据
    _time = str(utils.targettime_to_timestamp(file_time))
    for prov, r in reach_ratio.items():
        prov_lower = prov.lower()
        country_ = dictionary_province_country.get(prov_lower, '')
        tmp_item = (_time, country_, prov, r, reach_count.get(prov_lower, -1), total_count.get(prov_lower, -1)) # 构造元组
        insert_list.append(tmp_item)
    try:
        mysql_DB = MysqlDB()
        mysql_DB.create_table(sql_create_db)
        mysql_DB.insert(insert_list, sql_insert_db)
        mysql_DB.close()
    except Exception as e:
        scheduler_log.error(e)

def save_db_for_city_reach_count(file_time, reach_ratio, reach_count, total_count):
    reach_count_tb = 'bgpcityreach'
    sql_create_db = f"CREATE TABLE if not exists {reach_count_tb} ( \
                time BIGINT(20), \
                country_name varchar(128), \
                province_name varchar(255), \
                city_name varchar(255), \
                reach_ratio FLOAT(6,5) DEFAULT NULL, \
                reach_count BIGINT(20) DEFAULT NULL, \
                total_count BIGINT(20) DEFAULT NULL, \
                PRIMARY KEY (time, country_name, province_name, city_name));"
    sql_insert_db = f"INSERT INTO {reach_count_tb} ( \
                    time, country_name, province_name, city_name, reach_ratio, reach_count, total_count) VALUES (%s, %s, %s, %s, %s, %s, %s);"
    insert_list = [] # 新建一个空列表用来存储元组数据
    _time = str(utils.targettime_to_timestamp(file_time))

    for city, r in reach_ratio.items():
        city_short_ = city[:min(64, len(city))]
        city_lower = city.lower()
        country_ = dictionary_city_country.get(city_lower, '') 
        prov_ = dictionary_city_province.get(city_lower, '')
        tmp_item = (_time, country_, prov_, city_short_, r, reach_count.get(city_lower, -1), total_count.get(city_lower, -1)) # 构造元组
        insert_list.append(tmp_item)
    mysql_DB = MysqlDB()
    mysql_DB.create_table(sql_create_db)
    mysql_DB.insert(insert_list, sql_insert_db)
    # chunk_size = 10000
    # for i in range(0, len(insert_list), chunk_size):
    #     try:
    #         # scheduler_log.info(insert_list[i:i+chunk_size])
    #         if i+chunk_size < len(insert_list):
    #             mysql_DB.insert(insert_list[i:i+chunk_size], sql_insert_db)
    #         else:
    #             mysql_DB.insert(insert_list[i:], sql_insert_db)
    #     except Exception as e:
    #         scheduler_log.error(e)
    mysql_DB.close()


def save_db_for_as_reach_count(file_time, reach_ratio, reach_count, total_count):
    reach_count_tb = 'bgpasreach'
    sql_create_db = f"CREATE TABLE if not exists {reach_count_tb} ( \
                time BIGINT(20), \
                asn BIGINT(20), \
                reach_ratio FLOAT(6,5) DEFAULT NULL, \
                reach_count BIGINT(20) DEFAULT NULL, \
                total_count BIGINT(20) DEFAULT NULL, \
                PRIMARY KEY (time, asn));"
    sql_insert_db = f"INSERT INTO {reach_count_tb} ( \
                    time, asn, reach_ratio, reach_count, total_count) VALUES (%s, %s, %s, %s, %s);"
    insert_list = [] # 新建一个空列表用来存储元组数据
    _time = str(utils.targettime_to_timestamp(file_time))
    for asn, r in reach_ratio.items():
        tmp_item = (_time, int(asn), r, reach_count.get(asn, -1), total_count.get(asn, -1)) # 构造元组
        insert_list.append(tmp_item)  # [(),(),()...]
    # scheduler_log.info(insert_list)
    try:
        mysql_DB = MysqlDB()
        mysql_DB.create_table(sql_create_db)
        mysql_DB.insert(insert_list, sql_insert_db)
        mysql_DB.close()
    except Exception as e:
        scheduler_log.error(e)


def run_scheduler():
    #process_reachable_static('upd-file', as_info, country_prefix)    

    timezone = ConfigHandler().timezone
    scheduler = BlockingScheduler(logger=scheduler_log, timezone=timezone)
    start_time = (datetime.datetime.now(datetime.UTC).replace(tzinfo=None) + datetime.timedelta(seconds=10)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(process_reachable_static_continue, 'date', run_date=start_time, id="process_reachable_static_rib_file_his", name="rib_file_continue", args=['rib-file'])
    scheduler.add_job(process_reachable_static, 'cron', minute='10', hour='*/2', misfire_grace_time=120, id="process_reachable_static_rib_file_new", name="rib_file", args=['rib-file'])
    # scheduler.add_job(process_reachable_static, 'interval', minutes=120, id="process_reachable_static_rib_file", name="rib_file", args=['rib-file'])
    job_defaults = {
        'coalesce': False,
        'max_instances': 5  # 设置同时运行的特定作业最大实例数
    }
    scheduler.configure(job_defaults=job_defaults, timezone=timezone)

    scheduler.start()


if __name__ == '__main__':
    run_scheduler()
    '''
    _as_info = load_as_info()
    with open('_as_info.json', 'w', encoding='utf-8') as f:
        f.write(json.dumps(_as_info, ensure_ascii=False, indent=4))'''

