FROM caida/bgpstream:latest

ADD . /bgp
WORKDIR /bgp

# RUN sed -i s@/deb.debian.org/@/mirrors.tuna.tsinghua.edu.cn/@g /etc/apt/sources.list \c
    # && sed -i s@/security.debian.org/@/mirrors.tuna.tsinghua.edu.cn/@g /etc/apt/sources.list
# RUN apt-get update --fix-missing -o Acquire::http::No-Cache=True  --allow-releaseinfo-change
RUN apt-get clean && apt-get update --allow-releaseinfo-change && apt-get --reinstall install -y python3-setuptools python3-wheel python3-pip
RUN pip install -r requirements.txt --break-system-packages -i https://pypi.tuna.tsinghua.edu.cn/simple/

ENTRYPOINT ["/usr/bin/python3"]
CMD ["main.py"]