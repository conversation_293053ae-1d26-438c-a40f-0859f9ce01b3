from main import *
import pandas as pd
import time


def data_parser(datatype, filepath):
    if not os.path.exists(filepath):
        return
    stream = pybgpstream.BGPStream(data_interface="singlefile")
    stream.set_data_interface_option("singlefile", datatype, filepath)
    record_count = 0
    for rec in stream.records():
        for elem in rec:
            record_count += 1

    print(filepath, record_count)


if __name__ == '__main__':

    date = '2023-07-31'
    datatype = 'rib-file'
    #datatype = 'upd-file'
    time_series = pd.date_range(start=date, freq='59T', periods=300)
    for i in time_series.to_list():
        print(gen_file_url('rib-file', i))
        '''
        _query = dict()
        _time = time.time()
        _url, file_time = gen_file_url(datatype, i.to_pydatetime())
        download_result = bgp_data_downloader(_url, iter=True)
        if download_result[0]:
            prefix_as_queue = bgp_data_parser(datatype, download_result[1])
            task_count = prefix_as_queue.qsize()
            print(task_count)
            area_reachable_static(prefix_as_queue, as_info, country_prefix, file_time, datatype)
            #prefix_as_queue = bgp_data_parser(datatype, download_result[1])'''

        '''
        cost_time = time.time() - _time
        print(cost_time)
        print(prefix_as_queue.qsize())
        while not prefix_as_queue.empty():
            item = prefix_as_queue.get()
            if item[0] not in _query:
                _query[item[0]] = item[1]
            else:
                if _query[item[0]] != item[1] and item[1] and _query[item[0]]:
                    print(_query[item[0]], item[1])
        '''