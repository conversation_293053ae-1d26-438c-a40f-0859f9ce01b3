import os
from netaddr import *
import arrow
import datetime
import socket
import struct

def my_mkdir(path):
    path = str(path).replace('\\', '/')
    if not os.path.exists(path):
        exists_flag = False
        iter = 1
        while not exists_flag:
            iter_path = '/'.join(path.split('/')[:iter])
            if iter_path == path:
                exists_flag = True
            if os.path.exists(iter_path):
                iter += 1
            else:
                os.makedirs(iter_path)
    return


def del_files_in_dir(dirpath, filetype='.bz2', without=''):
    if not os.path.exists(dirpath):
        return
    for filename in os.listdir(dirpath):
        if filename.endswith(filetype):
            if filename == without:
                continue
            os.makedirs(dirpath.replace('bgpdata', 'backup'), exist_ok=True)
            os.rename(dirpath + '/' + filename,
                      dirpath.replace('bgpdata', 'backup') + '/' + filename)
            # os.remove(dirpath + '/' + filename)


def CIDR2IP(cidr):
    ip_set = IPSet([cidr])
    return [i.__str__() for i in ip_set]


def IP2CIDR(startip, endip):
    iprange = IPRange(startip, endip)
    return [i.__str__() for i in iprange.cidrs()]


def IP2INT(ip):
    return int(IPAddress(ip))


def INT2IP(ip_int):
    return str(IPAddress(ip_int))


def CIDR2INTrange(cidr):
    ip_range = IPSet([cidr]).iprange()
    return [ip_range.first, ip_range.last]


def targettime_to_utctime(format_time, _timezone='Europe/Kiev'):
    return arrow.get(format_time, tzinfo=_timezone).to('UTC')


def targettime_to_timestamp(format_time, _timezone='UTC', _format='YYYYMMDD.HHmm'):
    return arrow.get(format_time, _format, tzinfo=_timezone).to('UTC').timestamp()


def ceil_date(date, **kwargs):
    secs = datetime.timedelta(**kwargs).total_seconds()
    return datetime.fromtimestamp(date.timestamp() + secs - date.timestamp() % secs)


def floor_date(date, **kwargs):
    secs = datetime.timedelta(**kwargs).total_seconds()
    return datetime.datetime.fromtimestamp(date.timestamp() - date.timestamp() % secs)

#将整数类型的ip地址转成字符串形式。
def LongToip(int_ip):
    #ip_str = socket.inet_ntoa(struct.pack('l',socket.htonl(int_ip)))
    ip_str = socket.inet_ntoa(struct.pack("!I",int_ip))
    return ip_str

#将字符串形式的ip地址转成整数类型。
def ipToLong(ip_str):

    #socket.ntohl(struct.unpack('l',socket.inet_aton("*************"))[0])
    ip_long = 0
    for index,value in enumerate(reversed([int(x) for x in ip_str.split('.')])):
        ip_long += value<<(8*index)
    return ip_long

#二分查找ip的所在行数
def binary_search(array: list,target):
    """
    二分查找 目标ip所在行数
    :param array: 数组列表
    返回第一个大于目标ip的 结尾IP（ip_str_end）所在行
    """
    
    low, high = 0, len(array) - 1     # 数组开头索引(默认为 0), 数组结尾索引
    count = 0                         # 寻找次数计数器
    while low <= high:                # 如果结尾索引大于等于开头索引 则不断循环
        median = (low + high) // 2
        count += 1
        if array[median] > target:    # 如果目标值比中间数小，位于中间数左边，更改high索引
            high = median - 1
            
            #print("high",high)
        elif array[median] < target:  # 如果目标值比中间数大，位于中间数右边，更改high索引
            low = median + 1
            #print("low",low)
        else:
            #print("mind",median)
            #print(f"查找了{count}次找到目标值{target}")
            return median
    return low


if __name__ == '__main__':
    print(targettime_to_timestamp('20230815.0800', _timezone='UTC'))
    import pandas as pd
    print(datetime.datetime.utcnow().strftime('%Y%m%d%H%M'))
    '''time_series = pd.date_range(start='20230815', freq='59T', end=datetime.datetime.utcnow().strftime('%Y%m%d%H%M'))
    for i in time_series.to_list():
        print(i)'''
    start_time = (datetime.datetime.utcnow().replace(tzinfo=None) - datetime.timedelta(days=2)).strftime('%Y%m%d%H%M')
    time_series = pd.date_range(start=start_time, freq='59T', end=datetime.datetime.utcnow().replace(tzinfo=None).strftime('%Y%m%d%H%M'))
    #scheduler_log.info(f'prcess {time_series.to_list()}')
    print(f'prcess {time_series.to_list()}')


