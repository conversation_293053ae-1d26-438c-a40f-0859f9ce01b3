# coding=utf-8
import pymysql
import setting
import json
import arrow

from handler.logHandler import LogHandler
scheduler_log = LogHandler("scheduler")

class MysqlDB:
    def __init__(self):
        # 创建连接对象
        self.conn = pymysql.connect(host=setting.mysql_host, password=setting.mysql_password, user=setting.mysql_user,
                                    port=setting.mysql_port, charset=setting.mysql_charset)
        self.conn.cursor().execute('CREATE DATABASE IF NOT EXISTS %s;' % setting.mysql_db)
        self.conn.select_db(setting.mysql_db)

    def create_table(self, sql=''):
        cur = self.conn.cursor()
        try:
            if not sql:
                sql = setting.sql_create_db
            res = cur.execute(sql)  # 只是帮你执行sql语句，不会返回执行结果
            self.conn.commit()
            scheduler_log.info('create or check table success ')
        except Exception as e:
            scheduler_log.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return

    def insert(self, insertlist, sql=''):
        cur = self.conn.cursor()
        try:
            if not sql:
                sql = setting.sql_insert_db
            cur.executemany(sql, insertlist)  # 只是帮你执行sql语句，不会返回执行结果
            self.conn.commit()
        except Exception as e:
            scheduler_log.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return

    def close(self):
        self.conn.close()

    def get_last_time(self):
        cur = self.conn.cursor()
        last_time = ''
        try:
            # BUG: tmp_last_timestamp.fetchone --> cur.fetchone; int(cur.fetchone()[0])
            cur.execute(setting.sql_fetch_last_time)  # 只是帮你执行sql语句，不会返回执行结果
            last_timestamp = int(cur.fetchone()[0])
            scheduler_log.info(last_timestamp)
            last_time = arrow.get(last_timestamp, tzinfo='UTC').datetime.replace(tzinfo=None)
            self.conn.commit()
        except Exception as e:
            scheduler_log.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return last_time

    def get_last_N_time(self, N):
        cur = self.conn.cursor()
        last_time = []
        try:
            # BUG: tmp_last_timestamp.fetchone --> cur.fetchone; int(cur.fetchone()[0])
            cur.execute(setting.sql_fetch_last_N_time, N)  # 只是帮你执行sql语句，不会返回执行结果
            last_N_timestamp = cur.fetchall()
            scheduler_log.debug(last_N_timestamp)
            for last_timestamp in last_N_timestamp:
                last_time.append(arrow.get(last_timestamp[0], tzinfo='UTC').datetime.replace(tzinfo=None))
            self.conn.commit()
        except Exception as e:
            scheduler_log.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return last_time
        
    @staticmethod
    def gen_insert_list(_time: str, data: json):
        insert_list = [] # 新建一个空列表用来存储元组数据
        for country, rate in data.items():
            tmp_item = (_time, country, rate) # 构造元组
            insert_list.append(tmp_item)  # [(),(),()...]
        return insert_list



if __name__ == '__main__':
    #with open('upd-file_20230812.0915.json', 'r', encoding='utf-8') as f:
    #    tmp_data = json.loads(f.read())
    a = arrow.get(1691172800, tzinfo='UTC').datetime.replace(tzinfo=None)
    print(a)
    import datetime
    print((datetime.datetime.utcnow()-a) > datetime.timedelta(days=7))
    mysql_DB = MysqlDB()
    #mysql_DB.create_table()
    #_data = mysql_DB.gen_insert_list('20230812.0915', tmp_data)
    #mysql_DB.insert(_data)
    mysql_DB.get_last_time()
    mysql_DB.close()

