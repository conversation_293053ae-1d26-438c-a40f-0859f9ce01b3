#!/bin/bash

# Simple script to download citrix_results and sharepoint_results from VPS
# Usage: ./download_results_simple.sh [VPS_IP] [SSH_USER] [SSH_KEY_PATH]

# Default values - modify these according to your VPS setup
VPS_IP="${1:-your_vps_ip}"
SSH_USER="${2:-ubuntu}"
SSH_KEY="${3:-~/.ssh/id_rsa}"
SSH_PORT="${4:-22}"

# Remote paths
REMOTE_BASE_PATH="/home/<USER>/Documents/net-watcher/activeClient/vulAliveLine"
CITRIX_REMOTE_PATH="${REMOTE_BASE_PATH}/citrix_results"
SHAREPOINT_REMOTE_PATH="${REMOTE_BASE_PATH}/sharepoint_results"

# Local paths
LOCAL_BASE_PATH="./downloaded_results"
CITRIX_LOCAL_PATH="${LOCAL_BASE_PATH}/citrix_results"
SHAREPOINT_LOCAL_PATH="${LOCAL_BASE_PATH}/sharepoint_results"

echo "🚀 Starting download from VPS"
echo "=================================="
echo "📡 VPS: ${SSH_USER}@${VPS_IP}:${SSH_PORT}"
echo "🔑 SSH Key: ${SSH_KEY}"
echo "📂 Remote base: ${REMOTE_BASE_PATH}"
echo "💾 Local base: ${LOCAL_BASE_PATH}"
echo "=================================="

# Check if rsync is available
if ! command -v rsync &> /dev/null; then
    echo "❌ rsync is not installed. Please install rsync first:"
    echo "   Ubuntu/Debian: sudo apt-get install rsync"
    echo "   CentOS/RHEL: sudo yum install rsync"
    echo "   macOS: brew install rsync"
    exit 1
fi

# Create local directories
mkdir -p "${CITRIX_LOCAL_PATH}"
mkdir -p "${SHAREPOINT_LOCAL_PATH}"

# Function to download with rsync
download_directory() {
    local remote_path="$1"
    local local_path="$2"
    local description="$3"
    
    echo ""
    echo "📥 Downloading ${description}..."
    echo "   From: ${SSH_USER}@${VPS_IP}:${remote_path}"
    echo "   To: ${local_path}"
    
    rsync -avz --progress --human-readable \
        -e "ssh -p ${SSH_PORT} -i ${SSH_KEY} -o StrictHostKeyChecking=no" \
        "${SSH_USER}@${VPS_IP}:${remote_path}/" \
        "${local_path}/"
    
    if [ $? -eq 0 ]; then
        echo "✅ ${description} download completed"
        
        # List downloaded files
        file_count=$(find "${local_path}" -type f | wc -l)
        if [ ${file_count} -gt 0 ]; then
            echo "📋 Downloaded ${file_count} files:"
            find "${local_path}" -type f -exec ls -lh {} \; | awk '{print "   📄 " $9 " (" $5 ") - " $6 " " $7 " " $8}'
        else
            echo "⚠️  No files found in ${local_path}"
        fi
    else
        echo "❌ ${description} download failed"
        return 1
    fi
}

# Download citrix_results
download_directory "${CITRIX_REMOTE_PATH}" "${CITRIX_LOCAL_PATH}" "Citrix results"
CITRIX_SUCCESS=$?

# Download sharepoint_results
download_directory "${SHAREPOINT_REMOTE_PATH}" "${SHAREPOINT_LOCAL_PATH}" "SharePoint results"
SHAREPOINT_SUCCESS=$?

# Summary
echo ""
echo "📊 Download Summary"
echo "==================="
if [ ${CITRIX_SUCCESS} -eq 0 ]; then
    echo "✅ Citrix results: Success"
else
    echo "❌ Citrix results: Failed"
fi

if [ ${SHAREPOINT_SUCCESS} -eq 0 ]; then
    echo "✅ SharePoint results: Success"
else
    echo "❌ SharePoint results: Failed"
fi

echo ""
echo "📂 All files saved to: $(realpath ${LOCAL_BASE_PATH})"

# Show total downloaded size
if command -v du &> /dev/null; then
    total_size=$(du -sh "${LOCAL_BASE_PATH}" 2>/dev/null | cut -f1)
    echo "💾 Total downloaded size: ${total_size}"
fi

echo ""
if [ ${CITRIX_SUCCESS} -eq 0 ] && [ ${SHAREPOINT_SUCCESS} -eq 0 ]; then
    echo "🎉 All downloads completed successfully!"
    exit 0
else
    echo "⚠️  Some downloads failed. Check the output above for details."
    exit 1
fi
