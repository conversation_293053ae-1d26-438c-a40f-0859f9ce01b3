# VPS Results Download Scripts

这些脚本用于从VPS下载vulAliveLine扫描结果到本地机器。

## 文件说明

### 1. `download_citrix_results.py` - Python版本（功能最全）
- 支持命令行参数
- 支持干运行模式
- 详细的进度显示和错误处理
- 支持同时下载Citrix和SharePoint结果

### 2. `download_results_simple.sh` - 简单Shell脚本
- 最简单易用
- 适合快速下载
- 硬编码配置，需要修改脚本中的变量

### 3. `download_results_advanced.sh` - 高级Shell脚本
- 支持配置文件
- 丰富的命令行选项
- 彩色输出和进度显示
- 支持干运行模式

### 4. `download_config.example` - 配置文件模板
- 复制为 `download_config` 并修改配置
- 供高级脚本使用

## 使用方法

### 方法1：使用Python脚本（推荐）

```bash
# 安装依赖（如果需要）
pip install argparse

# 基本使用
python3 download_citrix_results.py --host YOUR_VPS_IP --user ubuntu --key ~/.ssh/id_rsa

# 包含SharePoint结果
python3 download_citrix_results.py --host YOUR_VPS_IP --user ubuntu --key ~/.ssh/id_rsa --include-sharepoint

# 干运行（查看会下载什么，不实际下载）
python3 download_citrix_results.py --host YOUR_VPS_IP --user ubuntu --key ~/.ssh/id_rsa --dry-run

# 自定义路径
python3 download_citrix_results.py \
    --host YOUR_VPS_IP \
    --user ubuntu \
    --key ~/.ssh/id_rsa \
    --remote-path /custom/path/to/citrix_results \
    --local-path ./my_downloads \
    --include-sharepoint

# 查看帮助
python3 download_citrix_results.py --help
```

### 方法2：使用简单Shell脚本

```bash
# 给脚本执行权限
chmod +x download_results_simple.sh

# 修改脚本中的配置变量
# VPS_IP, SSH_USER, SSH_KEY, SSH_PORT

# 运行脚本
./download_results_simple.sh

# 或者通过参数传递
./download_results_simple.sh YOUR_VPS_IP ubuntu ~/.ssh/id_rsa 22
```

### 方法3：使用高级Shell脚本

```bash
# 给脚本执行权限
chmod +x download_results_advanced.sh

# 创建配置文件
cp download_config.example download_config
# 编辑 download_config 文件，设置你的VPS信息

# 运行脚本
./download_results_advanced.sh

# 或者使用命令行参数
./download_results_advanced.sh -i YOUR_VPS_IP -u ubuntu -k ~/.ssh/id_rsa

# 只下载Citrix结果
./download_results_advanced.sh --citrix-only

# 只下载SharePoint结果
./download_results_advanced.sh --sharepoint-only

# 干运行模式
./download_results_advanced.sh --dry-run

# 查看帮助
./download_results_advanced.sh --help
```

## 配置说明

### 必需配置
- `VPS_IP`: VPS的IP地址
- `SSH_USER`: SSH用户名（通常是ubuntu或root）
- `SSH_KEY`: SSH私钥文件路径

### 可选配置
- `SSH_PORT`: SSH端口（默认22）
- `REMOTE_BASE_PATH`: VPS上的基础路径
- `LOCAL_BASE_PATH`: 本地保存路径

## 默认路径

### VPS上的路径
- Citrix结果: `/home/<USER>/Documents/net-watcher/activeClient/vulAliveLine/citrix_results`
- SharePoint结果: `/home/<USER>/Documents/net-watcher/activeClient/vulAliveLine/sharepoint_results`

### 本地保存路径
- 默认: `./downloaded_results/`
- Citrix: `./downloaded_results/citrix_results/`
- SharePoint: `./downloaded_results/sharepoint_results/`

## 文件类型

下载的文件包括：
- `citrix_scan_*.json` - 详细扫描结果
- `citrix_*.json` - 统计汇总结果
- `sharepoint_scan_*.json` - SharePoint详细扫描结果
- `sharepoint_*.json` - SharePoint统计汇总结果

## 故障排除

### 1. SSH连接问题
```bash
# 测试SSH连接
ssh -i ~/.ssh/id_rsa ubuntu@YOUR_VPS_IP

# 检查SSH密钥权限
chmod 600 ~/.ssh/id_rsa
```

### 2. rsync未安装
```bash
# Ubuntu/Debian
sudo apt-get install rsync

# CentOS/RHEL
sudo yum install rsync

# macOS
brew install rsync
```

### 3. 权限问题
```bash
# 给脚本执行权限
chmod +x download_results_*.sh

# 检查SSH密钥权限
ls -la ~/.ssh/id_rsa
```

### 4. 路径不存在
- 确认VPS上的路径是否正确
- 使用 `--dry-run` 模式测试
- 检查VPS上是否有扫描结果文件

## 示例输出

```
🚀 Starting citrix_results download from VPS
============================================================
📡 VPS: ubuntu@*************:22
📂 Remote path: /home/<USER>/Documents/net-watcher/activeClient/vulAliveLine/citrix_results
💾 Local path: ./downloaded_citrix_results
============================================================
📁 Created local directory: /home/<USER>/downloaded_citrix_results
🔧 Command: rsync -avz --progress --human-readable -e ssh -p 22 -i ~/.ssh/id_rsa ubuntu@*************:/home/<USER>/Documents/net-watcher/activeClient/vulAliveLine/citrix_results/ ./downloaded_citrix_results/

📥 Starting download...
receiving incremental file list
citrix_1751848619.json
          1.23K 100%    0.00kB/s    0:00:00 (xfr#1, to-chk=3/4)
citrix_scan_1751848619.json
         45.67K 100%   44.59MB/s    0:00:00 (xfr#2, to-chk=2/4)

✅ Download completed successfully!

📋 Downloaded 4 files:
   📄 citrix_1751848619.json (1.2 KB) - 2025-01-29 15:30:19
   📄 citrix_scan_1751848619.json (45.7 KB) - 2025-01-29 15:30:19
   📄 citrix_1751935671.json (1.1 KB) - 2025-01-30 16:47:51
   📄 citrix_scan_1751935671.json (42.3 KB) - 2025-01-30 16:47:51

🎉 All downloads completed successfully!
📂 Files saved to: /home/<USER>/downloaded_citrix_results
💾 Total downloaded size: 90.3 KB
```

## 注意事项

1. 确保VPS上的vulAliveLine服务正在运行并生成结果文件
2. 检查网络连接和SSH密钥配置
3. 大文件下载可能需要较长时间，请耐心等待
4. 建议先使用 `--dry-run` 模式测试配置是否正确
5. 定期下载结果文件以避免VPS存储空间不足
