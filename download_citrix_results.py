#!/usr/bin/env python3
"""
Script to download citrix_results files from VPS to local machine
"""
import os
import sys
import subprocess
import argparse
from datetime import datetime
import pathlib

def run_command(command, check=True):
    """Run shell command and return result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {command}")
        print(f"Error: {e.stderr}")
        return None, e.stderr, e.returncode

def download_citrix_results(vps_host, vps_user, vps_path, local_path, ssh_key=None, port=22):
    """Download citrix_results from VPS using rsync over SSH"""
    
    print("🚀 Starting citrix_results download from VPS")
    print("=" * 60)
    print(f"📡 VPS: {vps_user}@{vps_host}:{port}")
    print(f"📂 Remote path: {vps_path}")
    print(f"💾 Local path: {local_path}")
    print("=" * 60)
    
    # Create local directory if it doesn't exist
    local_dir = pathlib.Path(local_path)
    local_dir.mkdir(parents=True, exist_ok=True)
    print(f"📁 Created local directory: {local_dir.absolute()}")
    
    # Build rsync command
    rsync_cmd = [
        "rsync",
        "-avz",  # archive, verbose, compress
        "--progress",  # show progress
        "--human-readable",  # human readable sizes
        "-e"  # specify remote shell
    ]
    
    # SSH options
    ssh_options = [f"ssh -p {port}"]
    if ssh_key:
        ssh_options.append(f"-i {ssh_key}")
    
    rsync_cmd.append(" ".join(ssh_options))
    rsync_cmd.append(f"{vps_user}@{vps_host}:{vps_path}/")
    rsync_cmd.append(f"{local_path}/")
    
    command = " ".join(rsync_cmd)
    print(f"🔧 Command: {command}")
    print()
    
    # Execute rsync
    print("📥 Starting download...")
    stdout, stderr, returncode = run_command(command, check=False)
    
    if returncode == 0:
        print("✅ Download completed successfully!")
        print()
        
        # List downloaded files
        downloaded_files = list(local_dir.glob("*"))
        if downloaded_files:
            print(f"📋 Downloaded {len(downloaded_files)} files:")
            for file in sorted(downloaded_files):
                if file.is_file():
                    size = file.stat().st_size
                    size_str = format_size(size)
                    mtime = datetime.fromtimestamp(file.stat().st_mtime)
                    print(f"   📄 {file.name} ({size_str}) - {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("⚠️  No files found in the downloaded directory")
            
    else:
        print("❌ Download failed!")
        if stderr:
            print(f"Error details: {stderr}")
        if stdout:
            print(f"Output: {stdout}")
        return False
    
    return True

def format_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def download_sharepoint_results(vps_host, vps_user, vps_path, local_path, ssh_key=None, port=22):
    """Download sharepoint_results from VPS using rsync over SSH"""
    
    sharepoint_remote_path = vps_path.replace("citrix_results", "sharepoint_results")
    sharepoint_local_path = local_path.replace("citrix_results", "sharepoint_results")
    
    print("\n🟢 Starting sharepoint_results download from VPS")
    print("=" * 60)
    print(f"📡 VPS: {vps_user}@{vps_host}:{port}")
    print(f"📂 Remote path: {sharepoint_remote_path}")
    print(f"💾 Local path: {sharepoint_local_path}")
    print("=" * 60)
    
    # Create local directory if it doesn't exist
    local_dir = pathlib.Path(sharepoint_local_path)
    local_dir.mkdir(parents=True, exist_ok=True)
    print(f"📁 Created local directory: {local_dir.absolute()}")
    
    # Build rsync command
    rsync_cmd = [
        "rsync",
        "-avz",  # archive, verbose, compress
        "--progress",  # show progress
        "--human-readable",  # human readable sizes
        "-e"  # specify remote shell
    ]
    
    # SSH options
    ssh_options = [f"ssh -p {port}"]
    if ssh_key:
        ssh_options.append(f"-i {ssh_key}")
    
    rsync_cmd.append(" ".join(ssh_options))
    rsync_cmd.append(f"{vps_user}@{vps_host}:{sharepoint_remote_path}/")
    rsync_cmd.append(f"{sharepoint_local_path}/")
    
    command = " ".join(rsync_cmd)
    print(f"🔧 Command: {command}")
    print()
    
    # Execute rsync
    print("📥 Starting download...")
    stdout, stderr, returncode = run_command(command, check=False)
    
    if returncode == 0:
        print("✅ SharePoint download completed successfully!")
        print()
        
        # List downloaded files
        downloaded_files = list(local_dir.glob("*"))
        if downloaded_files:
            print(f"📋 Downloaded {len(downloaded_files)} SharePoint files:")
            for file in sorted(downloaded_files):
                if file.is_file():
                    size = file.stat().st_size
                    size_str = format_size(size)
                    mtime = datetime.fromtimestamp(file.stat().st_mtime)
                    print(f"   📄 {file.name} ({size_str}) - {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("⚠️  No SharePoint files found")
            
    else:
        print("❌ SharePoint download failed!")
        if stderr:
            print(f"Error details: {stderr}")
        return False
    
    return True

def main():
    parser = argparse.ArgumentParser(description="Download citrix_results from VPS to local machine")
    parser.add_argument("--host", required=True, help="VPS hostname or IP address")
    parser.add_argument("--user", default="root", help="SSH username (default: root)")
    parser.add_argument("--port", type=int, default=22, help="SSH port (default: 22)")
    parser.add_argument("--key", help="Path to SSH private key file")
    parser.add_argument("--remote-path", default="/home/<USER>/Documents/net-watcher/activeClient/vulAliveLine/citrix_results", 
                       help="Remote path to citrix_results directory")
    parser.add_argument("--local-path", default="./downloaded_citrix_results", 
                       help="Local path to save files (default: ./downloaded_citrix_results)")
    parser.add_argument("--include-sharepoint", action="store_true", 
                       help="Also download sharepoint_results")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be downloaded without actually downloading")
    
    args = parser.parse_args()
    
    if args.dry_run:
        print("🔍 DRY RUN MODE - No files will be downloaded")
        print("=" * 60)
    
    # Check if rsync is available
    stdout, stderr, returncode = run_command("which rsync", check=False)
    if returncode != 0:
        print("❌ rsync is not installed. Please install rsync first:")
        print("   Ubuntu/Debian: sudo apt-get install rsync")
        print("   CentOS/RHEL: sudo yum install rsync")
        print("   macOS: brew install rsync")
        sys.exit(1)
    
    if args.dry_run:
        # Add --dry-run flag to rsync for dry run
        print("This would download files from:")
        print(f"  Remote: {args.user}@{args.host}:{args.remote_path}")
        print(f"  Local:  {args.local_path}")
        if args.include_sharepoint:
            sharepoint_remote = args.remote_path.replace("citrix_results", "sharepoint_results")
            sharepoint_local = args.local_path.replace("citrix_results", "sharepoint_results")
            print(f"  SharePoint Remote: {args.user}@{args.host}:{sharepoint_remote}")
            print(f"  SharePoint Local:  {sharepoint_local}")
        return
    
    # Download citrix_results
    success = download_citrix_results(
        vps_host=args.host,
        vps_user=args.user,
        vps_path=args.remote_path,
        local_path=args.local_path,
        ssh_key=args.key,
        port=args.port
    )
    
    # Download sharepoint_results if requested
    if args.include_sharepoint and success:
        download_sharepoint_results(
            vps_host=args.host,
            vps_user=args.user,
            vps_path=args.remote_path,
            local_path=args.local_path,
            ssh_key=args.key,
            port=args.port
        )
    
    if success:
        print("\n🎉 All downloads completed successfully!")
        print(f"📂 Files saved to: {pathlib.Path(args.local_path).absolute()}")
    else:
        print("\n❌ Download failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
