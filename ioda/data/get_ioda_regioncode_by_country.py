# encoding = utf-8
import selenium
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import json, time, os, sys
sys.path.append('../')
from queue import Queue
from common import utils
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class Browser:
    def __init__(self, headless=True, timeout=30, chromedriver_path=r'C:\Anaconda3\Scripts', proxies="http://127.0.0.1:7890", non_blocking_page_load=False):
        """
        Configuration for chromedriver
        :param headless:
        :param timeout:
        :param chromedriver_path:
        :param proxies:
        """
        #self.kill_chrome()

        self.c_service = ChromeService(ChromeDriverManager(path=chromedriver_path).install())
        self.c_service.command_line_args()
        self.c_service.start()
        caps = DesiredCapabilities.CHROME
        caps['goog:loggingPrefs'] = {'performance': 'ALL'}
        caps['acceptSslCerts'] = True
        if non_blocking_page_load:
            caps["pageLoadStrategy"] = "none"  # 使得get完直接结束，不再等待网页完全加载完成
        '''
        caps['proxy'] = {
            "httpProxy": proxies,
            "ftpProxy": proxies,
            "sslProxy": proxies,
            "noProxy": None,
            "proxyType": "MANUAL",
            "autodetect": False
        }'''

        driver_options = Options()
        #driver_options.add_argument(r"user-data-dir=Chrome_Data")
        driver_options.add_argument("user-agent={}".format(utils.get_random_ua()))
        driver_options.add_argument("--start-maximized")
        #driver_options.add_argument('--start-fullscreen')
        driver_options.add_argument('--proxy-server=' + proxies)
        driver_options.add_argument('--lang=en-US')
        #driver_options.add_argument("--disable-infobars")
        #driver_options.add_argument("--disable-extensions")
        #driver_options.add_argument('--ignore-certificate-errors')
        #driver_options.add_argument('--ignore-ssl-errors')

        driver_options.add_argument('--single-process')
        driver_options.add_argument('--disable-dev-shm-usage')
        driver_options.add_argument("--incognito")
        driver_options.add_argument('--disable-blink-features')
        driver_options.add_argument('--disable-blink-features=AutomationControlled')
        driver_options.add_experimental_option('useAutomationExtension', False)
        driver_options.add_experimental_option("excludeSwitches", ["enable-automation"])

        #driver_options.add_argument('--hide-scrollbars')  # 隐藏滚动条, 应对一些特殊页面
        # driver_options.add_argument('--blink-settings=imagesEnabled=false')  # 不显示图片，显示位置坐标会不一样
        driver_options.add_argument('--no-sandbox')
        #driver_options.add_argument('--disable-gpu')

        #driver_options.add_experimental_option('w3c', False)  # InvalidArgumentException： get_log('performance')
        #driver_options.add_argument('--disable-javascript')
        # driver_options.add_argument('--allow-insecure-localhost')
        #driver_options.add_argument('--disable-dev-shm-usage')  # linux

        if headless == True:
            driver_options.add_argument('--headless')

        # 禁用浏览器弹窗
        '''
        prefs = {
            'profile.default_content_setting_values': {
                'notifications': 2
            },
            'profile.default_content_settings.popups': 0
        }
        driver_options.add_experimental_option('prefs', prefs)
        '''

        self.driver = webdriver.Chrome(service=self.c_service, options=driver_options, desired_capabilities=caps)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source":
                "const newProto = navigator.__proto__;"
                "delete newProto.webdriver;"
                "navigator.__proto__ = newProto;"
        })
        #self.driver = webdriver.Chrome(executable_path=chromedriver_path, chrome_options=driver_options, desired_capabilities=caps)
        self.driver.set_page_load_timeout(timeout)

    def query(self, _url):
        try:
            self.driver.get(_url)
            time.sleep(1)
        except selenium.common.exceptions.TimeoutException as e:
            print('page load timeout')
            self.close()
            return False
        status_code = self.get_status_code()
        if status_code is None:
            print('cannot open the website')
            self.close()
            return False
        elif status_code[0] >= 400:
            print('server error: %s, %s' % (status_code[0], status_code[1]))
            self.close()
            return False
        return True

    def get_status_code(self):
        for responseReceived in self.driver.get_log('performance'):
            #print(responseReceived)
            try:
                response = json.loads(responseReceived[u'message'])[u'message'][u'params'][u'response']
                if response[u'url'] == self.driver.current_url:
                    return (response[u'status'], response[u'statusText'])
            except:
                pass

        return None

    def close(self):
        """
        close the chrome and tags
        :return:
        """
        self.c_service.stop()
        self.driver.quit()

    def get_cursor_pos(self):
        self.driver.execute_script()

    def kill_chrome(self):
        os.system('taskkill /im chromedriver.exe /F')
        os.system('taskkill /im chrome.exe /F')

    def get_scrolled_screenshot(self, website_name, screenshot_dir='screenshot'):
        """
        save long screenshot for the website
        :param website_name:
        :param screenshot_dir:
        :return:
        """
        import numpy as np
        from PIL import Image
        self.auto_close_popup()
        output_dir = os.path.join(utils.get_pwd(), screenshot_dir)
        tmp_dir = os.path.join(utils.get_pwd(), screenshot_dir, 'tmp')
        utils.my_mkdir(tmp_dir)
        #self.driver.execute_script("scroll(0, -250);")  # 也可以，一样
        self.driver.execute_script("window.scrollTo(0, -250);")
        time.sleep(1)  # 暂时使用时间等待

        window_height = self.driver.get_window_size()['height']  # 窗口高度
        page_height = self.driver.execute_script('return document.documentElement.scrollHeight')  # 页面高度
        self.driver.save_screenshot(os.path.join(tmp_dir, 'screenshot.png'))

        if page_height > window_height:
            n = page_height // window_height  # 需要滚动的次数
            base_mat = np.atleast_2d(
                Image.open(os.path.join(tmp_dir, 'screenshot.png')))  # 打开截图并转为二维矩阵

            for i in range(n):
                self.auto_close_popup()
                self.driver.execute_script(f'document.documentElement.scrollTop={window_height * (i + 1)};')
                time.sleep(0.5)
                self.driver.save_screenshot(os.path.join(tmp_dir, 'screenshot_{}.png'.format(str(i))))  # 保存截图
                mat = np.atleast_2d(
                    Image.open(os.path.join(tmp_dir, 'screenshot_{}.png'.format(str(i)))))  # 打开截图并转为二维矩阵
                base_mat = np.append(base_mat, mat, axis=0)  # 拼接图片的二维矩阵

            file_path = os.path.join(output_dir, utils.clean_filename(str(website_name)) + '.png')
            #print(file_path)
            Image.fromarray(base_mat).save(file_path)

    def get_current_html(self):
        self.auto_close_popup()
        return self.driver.execute_script("return document.documentElement.outerHTML")

    def auto_close_popup(self):
        """
        solution for close the pop-up login or cookies windows
        :return:
        """
        #try:
        #    self.driver.switch_to.alert.dismiss()
        #except:
        #    pass
        popup_windows = self.driver.find_elements('xpath', '//*[text()="×"] | //button[contains(@class, "close")]')
        try:
            popup_windows[-1].click()
        except:
            pass

    def condition_wait(self, condition, _timeout=20):
        try:
            #WebDriverWait(driver=self.driver, timeout=_timeout, poll_frequency=0.5, ignored_exceptions=None).until(EC.visibility_of_element_located((By.XPATH, condition)))
            #WebDriverWait(driver=self.driver, timeout=_timeout, poll_frequency=0.5, ignored_exceptions=None).until(EC.visibility_of_any_elements_located((By.XPATH, condition)))
            WebDriverWait(driver=self.driver, timeout=_timeout, poll_frequency=0.5, ignored_exceptions=None).until(EC.presence_of_all_elements_located((By.XPATH, condition)))
        except TimeoutError as e:
            print('meet TimeoutError, check network')
            return False
        except Exception as e:
            print(e)
            return False

        return True


def get_ioda_region_code():
    country_code_queue = Queue()
    _browser = Browser(False, proxies="http://*************:7890", non_blocking_page_load=False)

    country_code_all = ['IL', 'PS']
    max_fail = 3
    count_dict = dict()
    for i in country_code_all:
        country_code_queue.put(i)
        count_dict[i] = 0

    while not country_code_queue.empty():
        country_code = country_code_queue.get()
        try:
            url = f'https://ioda.inetintel.cc.gatech.edu/country/{country_code}'
            _browser.driver.get(url)
            count_dict[country_code] += 1
            try:
                # _browser.condition_wait('//div[@class="leaflet-tile-container" or @class="related__no-outages"]', _timeout=10)
                _browser.condition_wait('//span[text()="Regional Raw Signals"]', _timeout=20)
            except Exception as e:
                raise e
            # print(_browser.get_status_code())
            pop_wind = _browser.driver.find_element('xpath', '//span[text()="Regional Raw Signals"]')
            pop_wind.click()
            time.sleep(1)

            try:
                # 会出现网络超时，一直不显示的问题
                print(_browser.condition_wait('//input[@class="table__cell-checkbox"]', _timeout=10))
            except Exception as e:
                raise e

                # 获取ioda各国的区域编号
            while True:
                try:
                    code = [i.get_attribute('href').split('/')[-1] for i in _browser.driver.find_elements('xpath',
                                                                                                          '//div[@class="ant-modal-content"]//a[@class="table__cell-link"]')]
                    name = [i.text for i in _browser.driver.find_elements('xpath',
                                                                          '//div[@class="ant-modal-content"]//a[@class="table__cell-link"]/span')]
                    break
                except selenium.common.exceptions.StaleElementReferenceException:
                    time.sleep(1)
                    pass

            with open(f'region-number-{country_code.lower()}.csv', 'w', encoding='utf-8') as f:
                f.write('region,number,wiki name\n')
                for i, j in zip(name, code):
                    f.write(i + ',' + j + ',' + i + '\n')

        except Exception as e:
            if count_dict[country_code] <= max_fail:
                print(e)
                country_code_queue.put(country_code)
                country_code_queue.task_done()
            else:
                raise e

def get_china_gov_web():
    _browser = Browser(False, proxies="http://*************:7890", non_blocking_page_load=True)
    url = 'https://www.gov.cn/home/<USER>/29/content_5748953.htm'  # 国务院
    url = 'https://www.gov.cn/home/<USER>/29/content_5748954.htm'  # 省政府
    _browser.driver.get(url)
    print(1111111)
    output = {}
    for i in _browser.driver.find_elements('xpath', '//div[@class="pages_content"]//tbody//a'):
        url = i.get_attribute('href')
        name = i.text
        output[name] = url

    with open(f'chine_province_gov.json', 'w', encoding='utf-8') as f:
        f.write(json.dumps(output, ensure_ascii=False, indent=4))


def get_chinese_rd_web():
    _browser = Browser(False, proxies='', non_blocking_page_load=False)
    url = 'http://www.npc.gov.cn/npc/index.html'  # 人大

    _browser.driver.get(url)
    print(1111111)
    output = {}
    output['全国人大'] = url

    iframe_element = _browser.driver.find_element('xpath', '//div[@class="friendLinks"]/div[3]/div/iframe')
    _browser.driver.switch_to.frame(iframe_element)

    for i in _browser.driver.find_elements('xpath', '//table[@id="xglj_txt"]//a'):
        url = i.get_attribute('href')
        name = i.accessible_name + '省人大'
        output[name] = url

    with open(f'chine_rd_gov.json', 'w', encoding='utf-8') as f:
        f.write(json.dumps(output, ensure_ascii=False, indent=4))


def get_zx_web():
    # get china gov web
    _browser = Browser(False, proxies='', non_blocking_page_load=True)
    url = 'http://www.cppcc.gov.cn/'  # 人大

    _browser.driver.get(url)
    print(1111111)
    output = {}
    output['全国政协'] = url
    ## 需要手点击展开一下
    for i in _browser.driver.find_elements('xpath', '//div[@class="nav_list"]//li[3]//a'):
        url = i.get_attribute('href')
        name = i.accessible_name.replace('\u3000', '') + '省政协'
        output[name] = url

    for i in _browser.driver.find_elements('xpath', '//div[@class="nav_list"]//li[2]//a'):
        url = i.get_attribute('href')
        name = i.accessible_name
        output[name] = url

    for i in _browser.driver.find_elements('xpath', '//div[@class="nav_list"]//li[5]//a'):
        url = i.get_attribute('href')
        name = i.accessible_name
        output[name] = url

    with open(f'chine_zx.json', 'w', encoding='utf-8') as f:
        f.write(json.dumps(output, ensure_ascii=False, indent=4))


if __name__ == '__main__':
    get_ioda_region_code()
    print(1111)
