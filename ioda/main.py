import json
from requests_html import HTMLSession
import time
from common import utils
from config.log import LOGGER
from config import settings
import requests
import datetime
import os
from common.utils import CipherAdapter
from queue import Queue
from apscheduler.schedulers.blocking import BlockingScheduler
from threading import Thread
from common.mysql_conn import MysqlDB
mylog = LOGGER('results/ioda.log')


def req_thread_count():
    count = settings.thread_count
    if isinstance(count, int):
        count = max(16, count)
    else:
        count = utils.get_request_count()
    mylog.log('DEBUG', f'Number of request threads {count}')
    return count


class IODA:
    def __init__(self, region_type, inputcode, region_dict_path, country_code, *args):
        self.region_type = region_type
        self.region_dict = self.load_region_dict(region_dict_path)
        self.input_code = inputcode
        self.country_code = country_code
        self.request_events = False
        mysql_DB = MysqlDB()
        mysql_DB.create_table(settings.sql_create_merit_tb)
        mysql_DB.create_table(settings.sql_create_bgp_tb)
        mysql_DB.create_table(settings.sql_create_ping_tb)

        if self.region_type == 'region':
            self.area_code = self.region_dict[inputcode]
        elif self.region_type == 'country':
            self.area_code = country_code
        else:
            raise 'import region_type error'

        if len(args) == 1:
            # 以小时形式输入
            self.until_time = utils.get_utc_timestamp()
            self.from_time = utils.get_pastdays_utc_timestamp(args[0])
        elif len(args) == 2:
            self.until_time = args[1]
            self.from_time = args[0]
        elif len(args) == 3:
            self.until_time = args[1]
            self.from_time = args[0]
            self.request_events = args[2]
            mysql_DB.create_table(settings.sql_create_outages_tb)
        elif len(args) > 3:
            raise 'input time error'
        else:
            self.until_time = utils.get_utc_timestamp()
            self.from_time = utils.get_pastdays_utc_timestamp()
        if not self.request_events:
            try:
                last_time_in_db = mysql_DB.get_last_time(self.country_code, self.area_code)
                self.from_time = last_time_in_db + 600
            except TypeError:
                pass
            except Exception as e:
                mylog.log('DEBUG', f'get last_time_in_db meet: {e}')
            finally:
                mysql_DB.close()
        else:
            mysql_DB.close()
        if self.request_events:
            self.url = "https://api.ioda.inetintel.cc.gatech.edu/v2/outages/events?from={}&until={}".format(str(self.from_time), str(self.until_time))
            mylog.log('INFOR', 'outage events: {}'.format(self.url))
        else:
            self.url = "https://api.ioda.inetintel.cc.gatech.edu/v2/signals/raw/{}/{}?from={}&until={}".format(self.region_type, self.area_code, str(self.from_time), str(self.until_time))
            mylog.log('INFOR', '{}: {}'.format(self.input_code, self.url))

        self.Session = HTMLSession()
        self.Session.trust_env = False
        # change ssl fingerprint
        self.Session.mount(utils.extract_root_page(self.url), CipherAdapter())
        self.header = dict()
        self.proxy = None
        self.start = time.time()  # 模块开始执行时间
        self.end = None  # 模块结束执行时间
        self.elapse = None  # 模块执行耗时
        self.output_dir = settings.result_save_dir
        self.tmp_dir = os.path.join(self.output_dir, 'tmp')

    @staticmethod
    def load_region_dict(region_dict_path):
        tmp_region_dict = dict()
        with open(region_dict_path, 'r', encoding='utf-8') as region_f:
            flag = 0
            for i in region_f.readlines():
                flag += 1
                if flag == 1:
                    continue
                tmp_region_dict[i.strip().split(',')[2]] = str(i.strip().split(',')[1])
        return tmp_region_dict

    def _query(self):
        try:
            r = self.Session.get(self.url, headers=self.header, proxies=self.proxy, verify=False, timeout=15)
            if r.text.startswith('<!DOCTYPE html>'):
                mylog.log('ALERT', f'servers error')
                return None
            return r
        except Exception as e:
            if isinstance(e, requests.exceptions.ConnectTimeout) or isinstance(e,  requests.exceptions.ConnectionError) or isinstance(e, requests.exceptions.ReadTimeout):
                mylog.log('ALERT', f'use {self.proxy} to access {self.url} meet: {e.args[0]}')
                if settings.proxy:
                    utils.add_blackproxy_and_delete(self.proxy)

        return None

    def get_header(self):
        headers = utils.gen_fake_header()
        if isinstance(headers, dict):
            self.header = headers
            return headers
        return self.header

    def get_proxy(self):
        return utils.get_proxy()

    def save_txt(self, _str):
        if _str:
            filepath = '{}\\{}_{}-{}.json'.format(self.output_dir, '_'.join(self.input_code.split(' ')), utils.utctimestamp_to_targetarea_fmt(self.from_time), utils.utctimestamp_to_targetarea_fmt(self.until_time))
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(_str)
            mylog.log('DEBUG', f'Finished save txt')
            return filepath

        mylog.log('DEBUG', f'Nothing to save txt')
        return None

    def begin(self, tips):
        mylog.log('DEBUG', f'Start scrape country: {tips}')

    def finish(self):
        self.end = time.time()
        self.elapse = round(self.end - self.start, 1)
        mylog.log('DEBUG', f'Finished, took {self.elapse} seconds.')

    def search(self):
        self.header = self.get_header()
        #self.proxy = {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
        self.proxy = self.get_proxy()
        r = self._query()
        return r

    def save_db(self, _str):
        _data = json.loads(_str)
        mysql_DB = MysqlDB()

        try:
            if self.request_events:
                insert_data = self.parser_events_data(_data)
                if insert_data:
                    mysql_DB.insert(settings.sql_insert_outages_tb, insert_data)
            else:
                insert_data = self.parser_area_data(_data)
                if insert_data:
                    for insert_key, insert_value_list in insert_data.items():
                        mysql_DB.insert(settings.insert_table_dict[insert_key], insert_value_list)
        except Exception as e:
            mysql_DB.close()
            if self.request_events:
                mylog.log('ERROR', f'insert events data to table meets: {e}')
            else:
                mylog.log('ERROR', f'insert {self.input_code} data to table meets: {e}')
            raise e
        finally:
            mysql_DB.close()
        return

    def parser_area_data(self, _data):
        tmp_data = {}
        for datasource_i in _data['data'][-1]:
            region_code = datasource_i['entityCode']
            region_name = self.input_code
            datasource = datasource_i['datasource'].split('-')[0]
            if datasource not in settings.insert_table_dict:
                continue
            time_from = datasource_i['from']
            time_until = datasource_i['until']
            time_step = datasource_i['step']
            values = datasource_i['values']
            tmp_data[datasource] = []
            # response_time, country_code, area_name, area_code, response_value
            for index, _value in enumerate(values):
                response_time = time_from + index*time_step
                if not type(_value) == type(None):  # isinstance(type(None), type(None)) 一直是False
                    tmp_data[datasource].append((response_time, self.country_code, region_name, str(region_code), _value))
                # 包含None的情况，不插入
        return tmp_data

    def parser_events_data(self, _data):
        tmp_data = []
        if not _data['data']:
            return tmp_data
        for datasource_i in _data['data']:
            # response_time, location_type, location_code, duration, datasource, location_name
            response_time = datasource_i['start']
            location_type = datasource_i['location'].split('/')[0]  # region country asn
            location_code = datasource_i['location'].split('/')[1]
            duration = datasource_i['duration']
            datasource = datasource_i['datasource'].split('-')[0]
            location_name = datasource_i['location_name']
            tmp_data.append((response_time, location_type, str(location_code), duration, datasource, location_name))
        return tmp_data

    def run(self):
        ret = 0
        while ret < 5:
            ret += 1
            self.begin(self.input_code)
            r = self.search()
            if r:
                #filepath = self.save_txt(r.text)
                try:
                    self.save_db(r.text)
                except Exception as e:
                    return False
                self.finish()
                return True
            else:
                self.finish()
            utils.time_sleep()
        return False


def myjob(region_queue, region_dict_path, country_code, from_time, until_time):
    while not region_queue.empty():
        region_name = region_queue.get()
        IODA('region', region_name, region_dict_path, country_code, from_time, until_time).run()
        region_queue.task_done()


def main(country_code, region_dict_path, from_time, until_time):
    country_ioda = IODA('country', country_code, region_dict_path, country_code, from_time, until_time)
    country_ioda.run()
    _region_dict = country_ioda.region_dict
    region_queue = Queue()
    task_count = len(_region_dict)
    for region, region_code in _region_dict.items():
        if region:
            region_queue.put(region)
    thread_count = req_thread_count()
    if task_count <= thread_count:
        # 如果请求任务数很小不用创建很多线程了
        thread_count = task_count
    #thread_count = 1   ## for debug
    for i in range(thread_count):
        request_thread = Thread(target=myjob, name=f'RequestThread-{i}',
                                args=(region_queue, region_dict_path, country_code, from_time, until_time), daemon=True)
        request_thread.start()
    region_queue.join()

    mylog.log('INFOR', f'{country_code} finished!')


def multi_main():
    from_time = utils.get_pastdays_utc_timestamp(144)
    until_time = utils.get_utc_timestamp()
    for country_code in settings.country_list:
        region_dict_path = settings.data_storage_dir.joinpath('region-number-{}.csv'.format(country_code.lower()))
        main(country_code, region_dict_path, from_time, until_time)


def get_all_events_data():
    from_time = utils.get_pastdays_utc_timestamp(72)
    until_time = utils.get_utc_timestamp()
    events_last_req_timestamp_datapath = settings.temp_save_dir.joinpath('events_last_req_timestamp.txt')
    if os.path.exists(events_last_req_timestamp_datapath):
        tmp_time = utils.get_data(events_last_req_timestamp_datapath)
        if tmp_time:
            from_time = int(tmp_time[0])
    region_dict_path = settings.data_storage_dir.joinpath('region-number-{}.csv'.format('US'.lower()))
    finish_flag = IODA('country', 'US', region_dict_path, 'US', from_time, until_time, True).run()
    if finish_flag:
        with open(events_last_req_timestamp_datapath, 'w', encoding='utf-8') as f:
            f.write(str(until_time))
    elif until_time-from_time > 72*60*60:
        with open(events_last_req_timestamp_datapath, 'w', encoding='utf-8') as f:
            f.write(str(until_time))


def run_scheduler():
    scheduler = BlockingScheduler(timezone=settings.TIMEZONE)
    start_time = (datetime.datetime.utcnow().replace(tzinfo=None) + datetime.timedelta(seconds=10)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(multi_main, 'date', run_date=start_time, id="ioda_init", name="ioda_init")
    scheduler.add_job(multi_main, 'interval', hours=4, id="ioda", name="ioda")
    scheduler.add_job(get_all_events_data,  'date', run_date=start_time, id="ioda_events_init", name="ioda_events_init")
    scheduler.add_job(get_all_events_data, 'interval', hours=24, id="ioda_events", name="ioda_events")
    job_defaults = {
        'coalesce': False,
        'max_instances': 20  # 设置同时运行的特定作业最大实例数
    }
    scheduler.configure(job_defaults=job_defaults, timezone=settings.TIMEZONE)
    scheduler.start()


if __name__ == '__main__':
    run_scheduler()
    #get_all_events_data()
    #multi_main()

