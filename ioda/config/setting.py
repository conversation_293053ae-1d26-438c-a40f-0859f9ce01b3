# coding=utf-8
import pathlib
import warnings
import os

# 配置文件传参
mysql_host = os.environ.get('DB_HOST', '127.0.0.1')
mysql_port = int(os.environ.get('DB_PORT', 3306))
mysql_db = os.environ.get('DB_NAME', 'ioda')
mysql_merit_tb = os.environ.get('MERIT_DB_TABLE', 'merit_nt')
mysql_bgp_tb = os.environ.get('BGP_DB_TABLE', 'bgp')
mysql_ping_tb = os.environ.get('PING_DB_TABLE', 'ping_slash24')
mysql_outages_tb = os.environ.get('OUTAGES_DB_TABLE', 'outages')
mysql_user = os.environ.get('DB_USER', 'root')
mysql_password = os.environ.get('DB_PASSWORD', '1qaz2wsx')
proxy = os.environ.get('PROXY', '')
#country_list = ['CN']
country_list = ['AU', 'CN', 'GB', 'IN', 'JP', 'KR', 'RU', 'TW', 'UA', 'US', 'IL', 'PS']
thread_count = 12

# 代理设置
enable_request_proxy = True
enable_proxy_pool = False
request_proxy_ls = [{'http': proxy, 'https': proxy}]
proxy_pool_url = 'http://*************:5010'

# 禁用所有警告信息
warnings.filterwarnings("ignore")

# 路径设置
relative_directory = pathlib.Path(__file__).parent.parent  # 代码相对路径
data_storage_dir = relative_directory.joinpath('data')  # 数据存放目录
result_save_dir = relative_directory.joinpath('results')  # 结果保存目录
temp_save_dir = relative_directory.joinpath('temp')
USER_AGENT_FILE = data_storage_dir.joinpath('chrome.txt')

# 请求设置
request_thread_count = None  # 请求线程数量(默认None，则根据情况自动设置)
request_timeout_second = (13, 27)  # 请求超时秒数(默认connect timout推荐略大于3秒)
request_ssl_verify = False  # 请求SSL验证(默认False)
request_allow_redirect = True  # 请求允许重定向(默认True)
request_redirect_limit = 20  # 请求跳转限制(默认20次)
# 默认请求头 可以在headers里添加自定义请求头
request_default_headers = {
    'Accept': 'text/html,application/xhtml+xml,'
              'application/xml;q=0.9,*/*;q=0.8',
    'Accept-Encoding': 'gzip, deflate',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Cache-Control': 'max-age=0',
    'DNT': '1',
    'Referer': 'https://www.google.com/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                  '(KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36',
    'Upgrade-Insecure-Requests': '1',
    'X-Forwarded-For': '127.0.0.1'
}
enable_random_ua = True  # 使用随机UA(默认True，开启可以覆盖request_default_headers的UA)
enable_banner_identify = False

# 数据库操作
TIMEZONE = "UTC"
mysql_charset = 'utf8'  # 只能写utf8，不能写utf-8
sql_create_merit_tb = f"CREATE TABLE if not exists {mysql_merit_tb} ( \
                response_time BIGINT(20), \
                country_code varchar(12), \
                area_name varchar(256) DEFAULT NULL, \
                area_code varchar(12), \
                response_value float(10,2) DEFAULT NULL, \
                PRIMARY KEY (response_time, country_code, area_code));"

sql_create_bgp_tb = f"CREATE TABLE if not exists {mysql_bgp_tb} ( \
                response_time BIGINT(20), \
                country_code varchar(12), \
                area_name varchar(256) DEFAULT NULL, \
                area_code varchar(12), \
                response_value float(10,2) DEFAULT NULL, \
                PRIMARY KEY (response_time, country_code, area_code));"

sql_create_ping_tb = f"CREATE TABLE if not exists {mysql_ping_tb} ( \
                response_time BIGINT(20), \
                country_code varchar(12), \
                area_name varchar(256) DEFAULT NULL, \
                area_code varchar(12), \
                response_value float(10,2) DEFAULT NULL, \
                PRIMARY KEY (response_time, country_code, area_code));"

sql_create_outages_tb = f"CREATE TABLE if not exists {mysql_outages_tb} ( \
                response_time BIGINT(20), \
                location_type varchar(12), \
                location_code varchar(20), \
                duration BIGINT(20) DEFAULT NULL, \
                datasource varchar(20), \
                location_name varchar(256) DEFAULT NULL, \
                PRIMARY KEY (response_time, location_type, location_code, datasource));"

create_table_dict = {'merit': sql_create_merit_tb, 'bgp':sql_create_bgp_tb, 'ping':sql_create_ping_tb}

sql_insert_merit_tb = f"INSERT INTO {mysql_merit_tb} ( \
                response_time, country_code, area_name, area_code, response_value) VALUES (%s, %s, %s, %s, %s);"

sql_insert_bgp_tb = f"INSERT INTO {mysql_bgp_tb} ( \
                response_time, country_code, area_name, area_code, response_value) VALUES (%s, %s, %s, %s, %s);"

sql_insert_ping_tb = f"INSERT INTO {mysql_ping_tb} ( \
                response_time, country_code, area_name, area_code, response_value) VALUES (%s, %s, %s, %s, %s);"

sql_insert_outages_tb = f"INSERT INTO {mysql_outages_tb} ( \
                response_time, location_type, location_code, duration, datasource, location_name) VALUES (%s, %s, %s, %s, %s, %s);"

insert_table_dict = {'merit': sql_insert_merit_tb, 'bgp': sql_insert_bgp_tb, 'ping': sql_insert_ping_tb}

sql_fetch_last_time = f"SELECT MAX(response_time) FROM {mysql_merit_tb} WHERE country_code=%s AND area_code=%s;"