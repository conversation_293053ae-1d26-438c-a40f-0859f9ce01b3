# coding=utf-8
import pymysql
from config import setting
import json
import arrow


def get_db_connect(host, port, user, password, db, charset='utf8mb4'):
    """连接数据库并返回数据库连接-数据库不存在则创建数据库"""
    conn = pymysql.connect(host=host, port=port, user=user, password=password,
                           charset=charset, autocommit=True, cursorclass=pymysql.cursors.DictCursor)
    conn.cursor().execute('CREATE DATABASE IF NOT EXISTS %s;' % db)
    conn.select_db(db)
    return conn


class MysqlDB:
    def __init__(self):
        # 创建连接对象
        self.conn = get_db_connect(host=setting.mysql_host, password=setting.mysql_password, user=setting.mysql_user,
                               db=setting.mysql_db, port=setting.mysql_port, charset=setting.mysql_charset)

    def create_table(self, sql_command):
        cur = self.conn.cursor()
        try:
            res = cur.execute(sql_command)  # 只是帮你执行sql语句，不会返回执行结果
            self.conn.commit()
            #print('create or check table success')
        except Exception as e:
            print(f'create table meet {e}')
            self.conn.rollback()
        finally:
            cur.close()
        return

    def insert(self, sql_command, insertlist):
        cur = self.conn.cursor()
        try:
            cur.executemany(sql_command, insertlist)
            self.conn.commit()
        except Exception as e:
            print(f'insert table meet {e}')
            self.conn.rollback()
        finally:
            cur.close()
        return

    def insert_one(self, sql_command, item):
        cur = self.conn.cursor()
        try:
            cur.execute(sql_command, item)
            self.conn.commit()
        except:
            self.conn.rollback()
        finally:
            cur.close()
        return

    def close(self):
        self.conn.close()

    def get_last_time(self, country_code, area_name):
        cur = self.conn.cursor()
        last_time = 0
        try:
            cur.execute(setting.sql_fetch_last_time, (country_code, area_name))  # 只是帮你执行sql语句，不会返回执行结果
            last_time = int(cur.fetchone()['MAX(response_time)'])
            #last_time = arrow.get(last_timestamp, tzinfo='UTC').datetime.replace(tzinfo=None)
            self.conn.commit()
        except TypeError:
            self.conn.rollback()
            pass
        except Exception as e:
            print(f'get_last_time meets {e}')
            self.conn.rollback()
        finally:
            cur.close()
        if last_time:
            return last_time
        else:
            return
        
    @staticmethod
    def gen_insert_list(_time: str, data: json):
        insert_list = [] # 新建一个空列表用来存储元组数据
        for country, rate in data.items():
            tmp_item = (_time, country, rate) # 构造元组
            insert_list.append(tmp_item)  # [(),(),()...]
        return insert_list


if __name__ == '__main__':
    #with open('upd-file_20230812.0915.json', 'r', encoding='utf-8') as f:
    #    tmp_data = json.loads(f.read())
    with open(r'E:\works\ioda\results\Beijing_20230927_074946-20231003_074946.json', 'r', encoding='utf-8') as f:
        tmp_data = json.loads(f.read())
    print('end')
    '''
    a = arrow.get(1691172800, tzinfo='UTC').datetime.replace(tzinfo=None)
    print(a)
    import datetime
    print((datetime.datetime.utcnow()-a) > datetime.timedelta(days=7))
    mysql_DB = MysqlDB()
    #mysql_DB.create_table()
    #_data = mysql_DB.gen_insert_list('20230812.0915', tmp_data)
    #mysql_DB.insert(_data)
    mysql_DB.get_last_time()
    mysql_DB.close()'''

