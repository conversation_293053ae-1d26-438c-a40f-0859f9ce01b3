version: '3'
services:
  db:
    build: mysql
    image: mysql:latest
    container_name: bgp-mysql
    ports:
      - "23306:3306"
    volumes:
      - ./bgp-mysql/init:/docker-entrypoint-initdb.d
      - ./bgp-mysql/data:/var/lib/mysql
      - ./bgp-mysql/conf:/etc/mysql/conf.d
    environment:
      MYSQL_ROOT_PASSWORD: bgproot
      MYSQL_USER: bgp
      MYSQL_PASSWORD: bgpuser
    restart: always
    healthcheck:
        test: ["CMD", "mysqladmin" ,"ping", "-h", "localhost"]
        timeout: 20s
        retries: 10
    command:
      - --sql_mode=
  # crawler:
  #   build: python
  #   image: bgp-crawler:latest
  #   container_name: bgp-crawler
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
  #   volumes:
  #     - ./bgp-python:/app
  #   environment:
  #     DB_HOST: db
  #     DB_PORT: 3306
  #     DB_USER: root
  #     DB_PASSWORD: bgproot
  #     DB_NAME: bgpdata
  #     HTTP_PROXY: http://*************:10809
  #     HTTPS_PROXY: http://*************:10809
  #     NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  #   ports:
  #     - "7900:7900"
  #     - "4444:4444"
  #   privileged: true
  #   shm_size: 2g
  #   restart: always
  stat:
    build: python
    image: bgpstat:v5
    container_name: bgpstat
    depends_on:
      db:
        condition: service_healthy
    links:
      - db
    volumes:
      - ./bgp-stat:/bgp
    environment:
      DB_HOST: db
      DB_PORT: 3306
      DB_USER: root
      DB_PASSWORD: bgproot
      DB_NAME: bgpdata
      DB_TABLE: bgpcountryreach
      HTTP_PROXY: 
      HTTPS_PROXY: 
      NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
    restart: always
  # rss:
  #   image: damoeb/rss-proxy:2.1
  #   container_name: bgp-rss
  #   ports:
  #     - "28080:8080"
  #   environment:
  #     APP_API_GATEWAY_URL: https://127.0.0.1
  #     HTTP_PROXY: http://*************:10809
  #     HTTPS_PROXY: http://*************:10809
  #     NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  # front:
  #   build: grafana
  #   image: grafana/grafana-oss:latest
  #   container_name: bgp-grafana
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
  #     - rss
  #   ports:
  #     - "23000:3000"
  #   volumes:
  #     - ./bgp-grafana/data:/var/lib/grafana
  #   restart: always
    # environment:
    #   GF_SECURITY_ALLOW_EMBEDDING: true
    #   GF_PANELS_DISABLE_SANITIZE_HTML: true
      # HTTP_PROXY: http://*************:10809
      # HTTPS_PROXY: http://*************:10809
      # NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*

  # jsonhub:
  #   build: jsonhub
  #   image: jsonhub:latest
  #   container_name: jsonhub
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
  #   ports:
  #     - "8082:80"
  #   volumes:
  #     - ./json-hub/app:/code/app
  #   restart: always
  #   environment:
  #     HTTP_PROXY: http://*************:10809
  #     HTTPS_PROXY: http://*************:10809
  #     NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  # scan_server:
  #   build: ./aliveScanServer
  #   image: alive_scan_server:latest
  #   container_name: alive_scan_server
  #   volumes:
  #     - ./aliveScanServer:/app
  #   environment:  # used during container running
  #     DB_HOST: db
  #     DB_PORT: 3306
  #     DB_USER: root
  #     DB_PASSWORD: bgproot
  #     VPS_HOST: **************
  #     VPS_PORT: 17022
  #     VPS_USER: root
  #     VPS_KEY: 9b1e6b373865
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
  #   restart: always
  # ioda:
  #   build: ./ioda
  #   image: ioda:latest
  #   container_name: ioda
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
  #   volumes:
  #     - ./ioda:/app
  #   environment:
  #     DB_HOST: db
  #     DB_PORT: 3306
  #     DB_USER: root
  #     DB_PASSWORD: bgproot
  #     HTTP_PROXY: http://*************:10809
  #     HTTPS_PROXY: http://*************:10809
  #     NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  #   restart: always
  # active_server:
  #   build: ./activeServer
  #   image: active_server:latest
  #   container_name: active_server
  #   volumes:
  #     - ./activeServer:/app
  #   environment:  # used during container running
  #     DB_HOST: db
  #     DB_PORT: 3306
  #     DB_USER: root
  #     DB_PASSWORD: bgproot
  #     VPS_HOST: **************
  #     VPS_USER: root
  #     VPS_PORT: 17022
  #     VPS_KEY: 9b1e6b373865
  #     DEBUG_FLAG: false
  #   restart: always
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
volumes:
  db:
    driver: local