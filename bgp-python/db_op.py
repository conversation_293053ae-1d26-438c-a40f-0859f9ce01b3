from sqlalchemy import create_engine
import pymysql
from loguru import logger
from sqlalchemy.dialects.mysql import insert
import os
import pandas as pd

mysql_host = os.environ.get('DB_HOST', '127.0.0.1')
mysql_port = int(os.environ.get('DB_PORT', 23306))
mysql_db = os.environ.get('DB_NAME', 'bgpdata')
mysql_user = os.environ.get('DB_USER', 'root')
mysql_password = os.environ.get('DB_PASSWORD', 'bgproot')
mysql_charset = 'utf8mb4'

db_eng = f'''mysql+mysqldb://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db}?charset={mysql_charset}'''

def insert_on_conflict_update(table, conn, keys, data_iter):
    # update columns "b" and "c" on primary key conflict
    data = [dict(zip(keys, row)) for row in data_iter]
    stmt = (
        insert(table.table)
        .values(data)
    )
    stmt = stmt.on_duplicate_key_update(
        **{
            column_name: getattr(stmt.inserted, column_name)
            for column_name in keys
            })
    result = conn.execute(stmt)
    return result.rowcount

# 保存查询的结果
def save_db(data: pd.DataFrame, tb_name: str, inx_label: list, **args):
    try:
        engine = create_engine(db_eng)

        # Execute the to_sql for writting DF into SQL
        data.to_sql(tb_name, engine, if_exists='append', index=True,
                    index_label=inx_label, method=insert_on_conflict_update, **args)
    except Exception as e:
        logger.error(f'save_db error: {e}')
# id,dataSource,description,scope,startDate,endDate,locations,asns,eventType,linkedUrl,asnsDetails,locationsDetails,outage
outage_tb_name = 'radar_outage'
sql_create_outage_tb = f"CREATE TABLE if not exists {outage_tb_name} ( \
    outage_id varchar(20), dataSource varchar(20), description varchar(255), \
        scope varchar(255), startDate varchar(20), endDate varchar(20), \
            locations varchar(100),asns varchar(100),eventType varchar(64), \
                linkedUrl varchar(255), asns_asn varchar(255), asns_name varchar(255), \
                    asns_location_code varchar(255),asns_location_name varchar(255), \
                        locations_name varchar(255),locations_code varchar(100), \
                            outagecause varchar(100),outagetype varchar(100), \
                                PRIMARY KEY (outage_id));"

def create_table(create_sql=sql_create_outage_tb):
    conn = pymysql.connect(host=mysql_host, password=mysql_password, user=mysql_user,
                           db=mysql_db,port=mysql_port, charset=mysql_charset)
    cur = conn.cursor()
    try:
        res = cur.execute(create_sql)  # 只是帮你执行sql语句，不会返回执行结果
        conn.commit()
        logger.info('create or check table success ')
    except Exception as e:
        logger.error(e)
        conn.rollback()
    finally:
        cur.close()
    return

# 检查是否已有数据并读取
def check_and_read_db():
    engine = create_engine(db_eng)
    pass


    
