def get_location_name(x):
    res = []
    for i in x:
        res.append(i.get('name', 'UNKNOW'))
    if res:
        return ','.join(res)
    return 'UNKNOW'

def get_location_code(x):
    res = []
    for i in x:
        res.append(i.get('code', 'UNKNOW'))
    if res:
        return ','.join(res)
    return 'UNKNOW'

# [{'asn': '60353', 'name': 'DCC_RAFAH_ASN', 'location': {'code': 'PS', 'name': 'Palestine'}}]
def get_asn_num(x):
    res = []
    for i in x:
        res.append(i.get('asn', ''))
    if res:
        return ','.join(res)
    return ''

def get_asn_name(x):
    res = []
    for i in x:
        res.append(i.get('name', ''))
    if res:
        return ','.join(res)
    return ''

def get_asn_location_code(x):
    res = []
    for i in x:
        res.append(i.get('location', {'code': '', 'name': ''}).get('code', ''))
    if res:
        return ','.join(res)
    return ''

def get_asn_location_name(x):
    res = []
    for i in x:
        res.append(i.get('location', {'code': '', 'name': ''}).get('name', ''))
    if res:
        return ','.join(res)
    return ''