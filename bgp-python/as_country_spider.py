from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
import time
import pymysql
import os

db_host = os.environ.get('DB_HOST', '127.0.0.1')
db_port = int(os.environ.get('DB_PORT', 3306))
db_name = os.environ.get('DB_NAME', 'bgp_data')
db_user = os.environ.get('DB_USER', 'root')
db_passwd = os.environ.get('DB_PASSWORD', '')

def GetDriver(url):
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')
    driver = webdriver.Chrome(options = options)
    driver.get(url)
    return driver

def GetTableElements(driver):
    # 参考：selenium 根据 tag名 选择元素
    elements = driver.find_element(By.TAG_NAME, 'table')
    return elements

def _GetOneRow(tr):
    res = ''
    tds = tr.find_elements(By.TAG_NAME, 'td')
    for i,td in enumerate(tds):
        #res[heads[i]] = td.text
        #print('tds:' + str(i) + ' ' + str(td.text))
        if i == 0:
            res = ''
        elif i == 1:
            res = td.text
        else:
            res = res + '|' + td.text

        #print('tds:' + res)
        #if i+1 == len(tds):
            #res[heads[i]]= aTag

    return res

def GetTable(table):
    '''
    功能：返回表格信息
    参数：table：对应的表格元素
    '''
    res = []
    # 获得表格头
    #heads = _GetHeadList(table)
    num = 0

    tbody = table.find_element(By.TAG_NAME, 'tbody')
    trs = tbody.find_elements(By.TAG_NAME, 'tr')
    for tr in trs:
        num = num + 1
        #row = _GetOneRow(tr, heads)
        print('Get ' + str(num) + " Line!")
        row = _GetOneRow(tr)
        #print('nowpoint:'+ str(nowpoint))
        #print('lastpoint:'+ str(lastpoint))
        res.append(row)
        print('Update Line:' + str(num))
        #print('nowpoint > lastpoint')
        #GetPage2(row.rsplit('/',1)[1].strip())
        #if num >= 5: break
    return res

def Write2Mysql(list,line):
    try:
        db = pymysql.connect(user=db_user, password=db_passwd, host=db_host, port=db_port, db=db_name)
        cursor = db.cursor()
        insertnum = 0
        insertlist = []
        for eachline in list:
            insertnum += 1
            eachWord = eachline.split('|')
            if len(eachWord)==4:
                #print("list*************")
                asn = eachWord[0].strip('AS')
                country = line.strip()
                organ = eachWord[1]
                ipnum = eachWord[2].strip('个')
                reg_date = eachWord[3]
                insertlist.append((asn,country,organ,ipnum,reg_date))
        sql = "insert into as_country(asn,abb,organ,ipnum,reg_date) values(%s,%s,%s,%s,%s)"
        #print('list3333:'+str(insertlist))
        cursor.executemany(sql, insertlist)
        db.commit()
        db.close()
    except:
        print('insert mysql error!')


#第一页
print('------------------------------------------------------------')
print('********* Start to crawl for AS2Country Data! *********')

url_base = 'http://as.chacuo.net/'
f = open("data/country.txt", 'r', encoding='utf-8')
lines = f.readlines()
for line in lines:
    print('abb:'+line)
    updateline = url_base + line
    arr = []
    driver = GetDriver(updateline)
    tables = GetTableElements(driver)
    arr = GetTable(tables)
    Write2Mysql(arr,line)
    print('AS2Country Data crawling end!')
f.close()
print('------------------------------------------------------------')