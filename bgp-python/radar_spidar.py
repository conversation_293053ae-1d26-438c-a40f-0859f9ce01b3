import requests
import pandas as pd
from utils import *
import os
import datetime
from loguru import logger
import sys
from apscheduler.schedulers.blocking import BlockingScheduler
from db_op import save_db, create_table

min_level = "INFO"

def my_filter(record):
    return record["level"].no >= logger.level(min_level).no

logger.remove()
logger.add(sys.stderr, filter=my_filter)
logger.add('/app/logs/radar_spidar_{time}.log', 
           enqueue=True, 
           encoding='utf-8',
           rotation='100MB',
           retention="15 days")

db_host = os.environ.get('DB_HOST', '127.0.0.1')
db_port = int(os.environ.get('DB_PORT', 3306))
db_name = os.environ.get('DB_NAME', 'bgp_data')
db_user = os.environ.get('DB_USER', 'root')
db_passwd = os.environ.get('DB_PASSWORD', '')
outage_tb_name = 'radar_outage'

# def Write2Mysql(insertlist):
#     try:
#         db = pymysql.connect(user=db_user, password=db_passwd, host=db_host, port=db_port, db=db_name)
#         cursor = db.cursor()    
#         # sql_qk = "TRUNCATE TABLE radar_outage_list"
#         # cursor.execute(sql_qk)
#         # db.commit()
#         # time.sleep(2)cc
#         sql = "replace into radar_outage_list(id,dataSource,description,scope,starttime,endtime,locations,asns,eventtype,linkurl,asns_asn,asns_name,asns_location_code,asns_location_name,locations_name,locations_code,outagecause,outagetype) values(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
#         cursor.executemany(sql, insertlist)
#         db.commit()
#         db.close()
#     except Exception as e:
#         logger.error(f"{insertlist} Exception: {e}")

# Api-endpoint do JIRA
cf_api_url = "https://api.cloudflare.com/client/v4"
# Paramentros da Request / Request Params
#PARAMS = {'X-Auth-Key':'h2IUjl00uAuyF70wfKOR1Mf8LLbHQStY2rw5xYbQ'}
params = "dateRange=14d&format=json&limit=100"
'''
verstr = {
    "X-Auth-Email":"<EMAIL>",
    "X-Auth-Key":"h2IUjl00uAuyF70wfKOR1Mf8LLbHQStY2rw5xYbQ"
}
'''
my_token = "h2IUjl00uAuyF70wfKOR1Mf8LLbHQStY2rw5xYbQ" # TODO replace
create_table()
# dataSource,description,scope,starttime,endtime,locations,asns,eventtype,linkurl,asns_asn,asns_name,asns_location_code,asns_location_name,locations_name,locations_code,outagecause,outagetype

@logger.catch
def radar_spider():
    logger.info("*****************************************************")
    try:
        r = requests.get(f"{cf_api_url}/radar/annotations/outages?{params}",
                        headers={"Authorization": f"Bearer {my_token}",
                                "Content-Type": "application/json"})
        # logger.info("requests:" + r.text)
        df = pd.DataFrame(r.json()['result']['annotations'])
        logger.info(f'get outage events: {len(df)}')
        df['locations'] = df['locations'].apply(lambda x: ','.join(x))
        df['asns'] = df['asns'].apply(lambda x: ','.join(map(str, x)))
        # [{'name': 'Sudan', 'code': 'SD'}]
        df['locations_name'] = df['locationsDetails'].apply(get_location_name)
        df['locations_code'] = df['locationsDetails'].apply(get_location_code)
        # [{'asn': '60353', 'name': 'DCC_RAFAH_ASN', 'location': {'code': 'PS', 'name': 'Palestine'}}]
        df['asns_asn'] = df['asnsDetails'].apply(get_asn_num)
        df['asns_name'] = df['asnsDetails'].apply(get_asn_name)
        df['asns_location_code'] = df['asnsDetails'].apply(get_asn_location_code)
        df['asns_location_name'] = df['asnsDetails'].apply(get_asn_location_name)
        # {'outageCause': 'POWER_OUTAGE', 'outageType': 'NETWORK'}
        df['outagecause'] = df['outage'].apply(lambda x: x.get('outageCause', 'UNKNOW'))
        df['outagetype'] = df['outage'].apply(lambda x: x.get('outageType', 'UNKNOW'))
        logger.info(f'extract info: {len(df)}')
        df.drop(['locationsDetails', 'asnsDetails', 'outage'], axis=1, inplace=True)
        df['endDate'] = df['endDate'].fillna('')
        df.rename({'id': 'outage_id'}, axis=1, inplace=True)
        df.set_index('outage_id', inplace=True, drop=True)
        print(df.columns)
        save_db(df, 'radar_outage', ['outage_id'])
        logger.info(f'write & update db: {len(df)}')
    except Exception as e:
        logger.error(f'radar_spider: {e}')


def run_scheduler():
    #process_reachable_static('upd-file', as_info, country_prefix)    
    timezone = "UTC"
    scheduler = BlockingScheduler(timezone=timezone)
    start_time = (datetime.datetime.utcnow().replace(tzinfo=None) + datetime.timedelta(seconds=10)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(radar_spider, 'date', run_date=start_time, id="init_radar_spider", name="radar_spider")
    scheduler.add_job(radar_spider, 'interval', minutes=60, id="period_radar_spider", name="period_radar_spider")
    job_defaults = {
        'coalesce': True,
        'max_instances': 10  # 设置同时运行的特定作业最大实例数
    }
    scheduler.configure(job_defaults=job_defaults, timezone=timezone)

    scheduler.start()


if __name__ == '__main__':
    run_scheduler()