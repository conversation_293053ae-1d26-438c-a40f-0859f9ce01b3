import requests
import pandas as pd
import io
from db_op import save_db, create_table

def get_location_name(x):
    res = []
    for i in x:
        res.append(i.get('name', 'UNKNOW'))
    if res:
        return ','.join(res)
    return 'UNKNOW'

def get_location_code(x):
    res = []
    for i in x:
        res.append(i.get('code', 'UNKNOW'))
    if res:
        return ','.join(res)
    return 'UNKNOW'

# [{'asn': '60353', 'name': 'DCC_RAFAH_ASN', 'location': {'code': 'PS', 'name': 'Palestine'}}]
def get_asn_num(x):
    res = []
    for i in x:
        res.append(i.get('asn', ''))
    if res:
        return ','.join(res)
    return ''

def get_asn_name(x):
    res = []
    for i in x:
        res.append(i.get('name', ''))
    if res:
        return ','.join(res)
    return ''

def get_asn_location_code(x):
    res = []
    for i in x:
        res.append(i.get('location', {'code': '', 'name': ''}).get('code', ''))
    if res:
        return ','.join(res)
    return ''

def get_asn_location_name(x):
    res = []
    for i in x:
        res.append(i.get('location', {'code': '', 'name': ''}).get('name', ''))
    if res:
        return ','.join(res)
    return ''

# Api-endpoint do JIRA
cf_api_url = "https://api.cloudflare.com/client/v4"
# Paramentros da Request / Request Params
#PARAMS = {'X-Auth-Key':'h2IUjl00uAuyF70wfKOR1Mf8LLbHQStY2rw5xYbQ'}
params = "dateRange=7d&format=json&limit=100"
'''
verstr = {
    "X-Auth-Email":"<EMAIL>",
    "X-Auth-Key":"h2IUjl00uAuyF70wfKOR1Mf8LLbHQStY2rw5xYbQ"
}
'''
my_token = "h2IUjl00uAuyF70wfKOR1Mf8LLbHQStY2rw5xYbQ" # TODO replace

create_table()
# r = requests.get(f"{cf_api_url}/radar/annotations/outages?{params}",
#                  headers={"Authorization": f"Bearer {my_token}",
#                           "Content-Type": "application/json"})

# df = pd.DataFrame(r.json()['result']['annotations'])
# df.to_parquet('test.pq')
df = pd.read_parquet('test.pq')
df['locations'] = df['locations'].apply(lambda x: ','.join(x))
df['asns'] = df['asns'].apply(lambda x: ','.join(map(str, x)))
# [{'name': 'Sudan', 'code': 'SD'}]
df['locations_name'] = df['locationsDetails'].apply(get_location_name)
df['locations_code'] = df['locationsDetails'].apply(get_location_code)
# [{'asn': '60353', 'name': 'DCC_RAFAH_ASN', 'location': {'code': 'PS', 'name': 'Palestine'}}]
df['asns_asn'] = df['asnsDetails'].apply(get_asn_num)
df['asns_name'] = df['asnsDetails'].apply(get_asn_name)
df['asns_location_code'] = df['asnsDetails'].apply(get_asn_location_code)
df['asns_location_name'] = df['asnsDetails'].apply(get_asn_location_name)
# {'outageCause': 'POWER_OUTAGE', 'outageType': 'NETWORK'}
df['outagecause'] = df['outage'].apply(lambda x: x.get('outageCause', 'UNKNOW'))
df['outagetype'] = df['outage'].apply(lambda x: x.get('outageType', 'UNKNOW'))
df.drop(['locationsDetails', 'asnsDetails', 'outage'], axis=1, inplace=True)
df['endDate'] = df['endDate'].fillna('')
df.rename({'id': 'outage_id'}, axis=1, inplace=True)
df.set_index('outage_id', inplace=True, drop=True)
print(df.columns)
save_db(df, 'radar_outage', ['outage_id'])
# print(df.head())

#df = df.where(df.notnull(),None)
# df = df.fillna(value='None')
# asns_asn varchar(256), asns_name varchar(256), \
#                 asns_location_code varchar(256),asns_location_name varchar(256), \
#                     locations_name varchar(256),locations_code varchar(128), \
#                         outagecause varchar(128),outagetype varchar(128)
# df.to_csv('test.csv')