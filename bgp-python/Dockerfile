# For more information, please refer to https://aka.ms/vscode-docker-python
# FROM selenium/standalone-chrome:latest
FROM python:3.9

# Keeps Python from generating .pyc files in the container
ENV PYTHONDONTWRITEBYTECODE=1

# Turns off buffering for easier container logging
ENV PYTHONUNBUFFERED=1

# Install pip requirements
COPY requirements.txt .
# RUN apt-get -y update && sudo apt-get -y install cron vim python3-pip
# RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
# COPY entry_point.sh /opt/bin/
# RUN sudo bash -c "echo 'bash /app/run.sh&&wait ${SUPERVISOR_PID}'>>/opt/bin/entry_point.sh
WORKDIR /app
# COPY . /app

# During debugging, this entry point will be overridden. For more information, please refer to https://aka.ms/vscode-docker-python-debug
# CMD ["bash", "run.sh"]
ENTRYPOINT ["python3"]
CMD ["radar_spidar.py"]