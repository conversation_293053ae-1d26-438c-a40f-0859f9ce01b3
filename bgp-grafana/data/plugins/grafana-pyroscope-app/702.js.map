{"version": 3, "file": "702.js", "mappings": "+MAKA,MAAMA,EAAaC,IAA0B,CAC3CC,UAAWC,EAAAA,GAAG;;;;WAILF,EAAMG,QAAQ;;IAGvBC,OAAQF,EAAAA,GAAG;;MAUN,SAASG,GAAa,KAAEC,EAAI,MAAEC,IACnC,MAAMC,GAASC,EAAAA,EAAAA,YAAWV,GAE1B,OACE,kBAACW,MAAAA,CAAIC,UAAWH,EAAOP,WACrB,kBAACS,MAAAA,CAAIC,UAAWH,EAAOJ,QAASE,GAChC,kBAACI,MAAAA,CAAIC,UAAWH,EAAOJ,QAASG,GAGtC,C,8VC3BA,MAAMK,UAA2BC,EAAAA,EACzBC,GAAAA,CAAIC,EAAmBC,G,kBAA7B,eACE,MAAMC,QAAiB,EAAKC,MAAM,4CAA6C,CAC7EC,OAAQ,OACRC,KAAMC,KAAKC,UAAU,CACnBC,GAAIR,EACJS,aAAcR,MAIZS,QAAaR,EAASQ,OAE5B,MAAO,CACLF,GAAIE,EAAKF,GACTG,KAAMD,EAAKC,KACXC,aAAcF,EAAKE,aACnBC,QAASP,KAAKQ,MAAMJ,EAAKK,oBAE7B,GAjBA,E,CAmBMC,YAAAA,CAAaC,G,kBAAnB,eACE,MAAMJ,QAAgB,EAAKK,iBAAiBD,GAEtCf,QAAiB,EAAKC,MAAM,+CAAgD,CAChFC,OAAQ,OACRC,KAAMC,KAAKC,UAAU,CACnBI,KAAMM,EAAKN,KACXE,cAIEH,QAAaR,EAASQ,OAE5B,MAAO,CACLF,GAAIE,EAAKF,GACTG,KAAMM,EAAKN,KACXC,aAAcF,EAAKE,aAEnBC,QAASP,KAAKQ,MAAMJ,EAAKK,oBAE7B,GApBA,E,CAuBMI,UAAAA,G,OAAN,eACE,MAAO,CACLX,GAAI,IACJG,KAAM,KACNC,aAAc,GACdC,QAAS,KAEb,GAPA,E,CASMK,gBAAAA,CAAiBD,G,OAAvB,eACE,OAAO,IAAIG,SAAQ,CAACC,EAASC,KAC3B,MAAMC,EAAa,IAAIC,WAEvBD,EAAWE,iBAAiB,QAAQ,KAClC,IACEJ,EC/DH,SAA2BK,GAChC,MAAO,CAAEC,GAAiBD,EAAYE,MAAM,YAE5C,IAAKD,EACH,MAAM,IAAIE,MAAM,iDAGlB,GAAIH,IAAgBC,EAClB,MAAM,IAAIE,MAAM,sBAGlB,OAAOF,CACT,CDmDkBG,CAAkBP,EAAWQ,QACvC,CAAE,MAAOC,GACPV,EAAOU,EACT,KAGFT,EAAWE,iBAAiB,SAAS,KAGnCH,EAAO,IAAIO,MAAM,6BAA6BZ,EAAKN,UAAQ,IAG7DY,EAAWU,cAAchB,EAAK,GAElC,GApBA,E,EAuBK,MAAMiB,EAAqB,IAAIrC,E,osCEzEtC,MAAMsC,EAAqC,CACzC3B,GAAI,GACJG,KAAM,GACNC,aAAc,GACdC,QAAS,MAGJ,SAASuB,IACd,MAAOC,EAAWC,IAAgBC,EAAAA,EAAAA,WAAS,IACpCC,EAAaC,IAAkBF,EAAAA,EAAAA,UAASJ,IAE/CO,EAAAA,EAAAA,YAAU,IACD,KACLR,EAAmBS,OAAO,GAE3B,IAEH,MAAMC,GAAaC,EAAAA,EAAAA,cAAY,KAC7BX,EAAmBS,QAEnBL,GAAa,GACbG,EAAeN,EAAqB,GACnC,IAEGW,GAAcD,EAAAA,EAAAA,aAAWA,W,MAC7B,aAAO5B,GACL2B,IAEA,IACEN,GAAa,GAEb,MAAMS,QAAab,EAAmBlB,aAAaC,GAEnDwB,EAAeM,EACjB,CAAE,MAAOf,GACPS,EAAeN,GAEVD,EAAmBc,aAAahB,KACnCiB,EAAAA,EAAAA,IAAajB,EAAgB,CAAC,iCAAmCA,EAAgBkB,SAErF,CAEAZ,GAAa,EACf,I,gBAlBOrB,G,gCADsB4B,GAoB7B,CAACD,IAUGO,GAAoBN,EAAAA,EAAAA,aAAWA,W,MACnC,aAAOO,GACL,MAAMnD,EAAcmD,EAAOC,MAE3B,GAAKpD,GAAgBuC,EAAYhC,IAAOgC,EAAY5B,aAAa0C,SAASrD,GAA1E,CAVFiC,EAAmBS,QAEnBL,GAAa,GACbG,GAAgBc,GAAc,OAAKA,GAAAA,CAAU1C,QAAS,SAapDyB,GAAa,GAEb,IACE,MAAMS,QAAab,EAAmBnC,IAAIyC,EAAYhC,GAAIP,GAE1DwC,GAAgBc,GAAc,OACzBA,GAAAA,CACH1C,QAASkC,EAAKlC,WAElB,CAAE,MAAOmB,GACFE,EAAmBc,aAAahB,KACnCiB,EAAAA,EAAAA,IAAajB,EAAgB,CAAC,gCAAkCA,EAAgBkB,SAEpF,CAEAZ,GAAa,EAnBb,CAoBF,I,gBAzBOc,G,gCAD4BP,GA2BnC,CAACL,EAAYhC,GAAIgC,EAAY5B,eAG/B,MAAO,CACLkC,cACAlC,aAAc4B,EAAY5B,aAC1BuC,oBACAtC,QAAS2B,EAAY3B,QACrB+B,aACAP,YAEJ,C,w4BC7FA,MAAMmB,EAAU,CACdC,OCPiC,CACjC,mBAAoB,CAAC,OACrB,mBAAoB,CAAC,SACrB,oBAAqB,CAAC,MAAO,WDK7BC,UAAU,EAEVC,OAAAA,CAAQ3B,IACNiB,EAAAA,EAAAA,IAAajB,EAAO,CAAC,8BAA+BA,EAAM4B,YAC5D,GAQK,SAASC,GAAkB,cAAEC,EAAa,aAAEC,IACjD,MAAMC,GAAiBnB,EAAAA,EAAAA,cACrB,SAAUoB,GACRH,EAAcG,EAAM,GACtB,GACA,CAACH,IAGH,OACE,kBAACI,EAAAA,aAAYA,CACXV,QAAS,OACJA,GAAAA,CACHQ,mBAEFD,aAAcA,GAGpB,C,cE9BA,MAAM/E,EAAaC,IAA0B,CAC3CkF,WAAYhF,EAAAA,GAAG;kBACCF,EAAMG,QAAQ;MAIzB,SAASgF,GAAgB,QAAEvD,EAAO,KAAEwD,IACzC,MAAM5E,GAASC,EAAAA,EAAAA,YAAWV,GAE1B,OACE,kBAACW,MAAAA,CAAIC,UAAWH,EAAO0E,WAAYG,cAAY,cAC7C,kBAACC,EAAAA,EAAUA,CAAC1D,QAASA,EAASwD,KAAMA,IAG1C,CCXA,MAAMrF,EAAaC,IAA0B,CAC3CuF,kBAAmBrF,EAAAA,GAAG;;;qBAGHF,EAAMG,QAAQ;MAI5B,SAASqF,GAAyB,aAAE7D,EAAY,SAAE8D,IACvD,MAAMjF,GAASC,EAAAA,EAAAA,YAAWV,GAEpBwE,GAAUmB,EAAAA,EAAAA,UAAQ,IAAM/D,EAAagE,KAAKC,IAAU,CAAExB,MAAOwB,EAAMC,MAAOD,OAAU,CAACjE,KACpFwC,EAAQ2B,IAAaxC,EAAAA,EAAAA,YAEtByC,GAAYnC,EAAAA,EAAAA,cACfoC,IACCF,EAAUE,GACVP,EAASO,EAAE,GAEb,CAACP,IASH,OANAhC,EAAAA,EAAAA,YAAU,KAGRqC,EAAUvB,EAAQ,GAAG,GACpB,CAACA,IAGF,kBAAC7D,MAAAA,CAAIC,UAAWH,EAAO+E,mBACrB,kBAACU,EAAAA,eAAcA,KACb,kBAACC,EAAAA,YAAWA,CAACL,MAAM,UAAUM,UAAW5B,EAAQ6B,OAAQf,cAAY,0BAElE,kBAACgB,EAAAA,OAAMA,CAACC,IAAKnC,aAAAA,EAAAA,EAAQC,MAAOA,MAAOD,EAAQI,QAASA,EAASkB,SAAUM,EAAWQ,MAAO,OAKnG,CC3CA,MAAMxG,EAAaC,IAA0B,CAC3CwG,QAAStG,EAAAA,GAAG;;kBAEIF,EAAMG,QAAQ;MAIzB,SAASsG,IACd,MAAMjG,GAASC,EAAAA,EAAAA,YAAWV,GAE1B,OACE,kBAACW,MAAAA,CAAIC,UAAWH,EAAOgG,SACrB,kBAACE,EAAAA,QAAOA,CAACC,KAAM,KAGrB,CCVA,SAASC,IACP,MAAM,YAAE/C,EAAW,aAAElC,EAAY,kBAAEuC,EAAiB,QAAEtC,EAAO,WAAE+B,EAAU,UAAEP,GAAcD,IAiBzF,OACE,kBAACzC,MAAAA,KACC,kBAAC8E,EAAwBA,CAAC7D,aAAcA,EAAc8D,SAjB7BlB,KAC3BsC,EAAAA,EAAAA,GAAkB,kDAClB3C,EAAkBK,EAAQ,IAgBxB,kBAACK,EAAiBA,CAACC,cAbA7C,KACrB6E,EAAAA,EAAAA,GAAkB,sCAAuC,CAAEC,SAAU9E,EAAK4D,OAC1E/B,EAAY7B,EAAK,EAWkC8C,aAR/B,MACpB+B,EAAAA,EAAAA,GAAkB,uCAClBlD,GAAY,IAOTP,IAAcxB,EAAU,kBAAC6E,EAAYA,MAAM,KAC3C7E,GAAW,kBAACuD,EAAeA,CAACvD,QAASA,IAG5C,CAEO,MAAMmF,GAAcC,EAAAA,EAAAA,MAAKJ,GCjCzB,SAASK,IACd,OAAO,kBAAC5G,EAAYA,CAACC,KAAM,kBAACyG,EAAWA,MAAKxG,MAAO,kBAACwG,EAAWA,OACjE,CCGA,MAAMhH,EAAaC,IAA0B,CAC3CkH,WAAYhH,EAAAA,GAAG;eACFF,EAAMG,QAAQ;cACfH,EAAMG,QAAQ;MAIrB,SAASgH,IACd,MAAM3G,GAASC,EAAAA,EAAAA,YAAWV,IACnBqH,EAAgBC,IAAqB/D,EAAAA,EAAAA,UAAS,GAErD,OACE,kBAAC5C,MAAAA,KACC,kBAAC4G,EAAAA,QAAOA,KACN,kBAACC,EAAAA,IAAGA,CAAC1B,MAAM,eAAe2B,OAA2B,IAAnBJ,EAAsBK,YAAa,IAAMJ,EAAkB,KAC7F,kBAACE,EAAAA,IAAGA,CAAC1B,MAAM,mBAAmB2B,OAA2B,IAAnBJ,EAAsBK,YAAa,IAAMJ,EAAkB,MAOnG,kBAACK,EAAAA,WAAUA,CAAC/G,UAAWH,EAAO0G,YACR,IAAnBE,GAAwB,kBAACL,EAAWA,MACjB,IAAnBK,GAAwB,kBAACH,EAAeA,OAKjD,CClCe,SAASU,IACtB,OACE,oCACE,kBAACC,EAAAA,EAASA,CAACC,MAAM,gBACjB,kBAACV,EAASA,MAGhB,C,kCCZO,eAAKW,G,uDAAAA,C,CAAL,C,8QCSP,MAAMC,UAAkCC,EAAAA,EAOhCC,MAAAA,CAAOvG,EAAcE,G,sBAA3B,YACE,MAAMX,QAAiB,EAAKC,MAAM,aAAc,CAC9CC,OAAQ,OACRC,KAAMC,KAAKC,UAAU,CACnBI,OACAE,QAASsG,KAAK7G,KAAKC,UAAUM,IAC7BuG,aAAc,CACZC,MAAOxG,EAAQyG,SAASD,MACxBE,QAAS1G,EAAQyG,SAASC,SAE5B1C,KAAM,WAMV,aAFmB3E,EAASQ,MAG9B,E,iLAvBA8G,WAAAA,GACEC,MAAM,6BAA8B,CAClC,eAAgB,oBAEpB,EAsBK,MAAMC,EAA4B,IAAIV,E,cCvB7C,MAAMW,EAAqB,IAAIC,KAAKC,eAAe,QAAS,CAC1DC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,QAAQ,EACRC,KAAM,UACNC,OAAQ,UACRC,OAAQ,YAGV,SAASC,EAAOC,GACd,MAAMC,EAAQZ,EAAmBa,cAAcF,GAAMG,QAAO,CAACC,GAAO7D,OAAMxB,YACxEqF,EAAI7D,GAA2BxB,EACxBqF,IACN,CAAC,GAEJ,MAAO,GAAGH,EAAMT,QAAQS,EAAMR,SAASQ,EAAMP,OAAOO,EAAML,OAAOK,EAAMJ,QACzE,CAEA,SAASQ,EAAsBC,GAC7B,MAAMC,EAAO,IAAIC,KAAKC,KAAKC,MAA8B,IAAxBJ,EAAUC,KAAKI,SAC1CC,EAAK,IAAIJ,KAAKC,KAAKC,MAA4B,IAAtBJ,EAAUM,GAAGD,SAE5C,MAAO,GAAGZ,EAAOQ,SAAYR,EAAOa,IACtC,CAEA,SAASC,EAAaC,GACpB,MAAOC,EAASC,GAASF,IAAWrC,EAAAA,EAAcwC,SAAW,CAAC,WAAY,UAAY,CAAC,aAAc,YAE/FC,EAAe,IAAIC,gBAAgBC,OAAOC,SAASC,QACnDf,EAAOW,EAAazJ,IAAIsJ,GACxBH,EAAKM,EAAazJ,IAAIuJ,GAE5B,MAAO,CACLO,IAAK,CAAEhB,OAAMK,MACbL,MAAMiB,EAAAA,EAAAA,eAAcjB,GACpBK,IAAIY,EAAAA,EAAAA,eAAcZ,GAEtB,CAEO,SAASa,EAAkBC,GAChC,MAAMC,EAAa,CACjB,WACAtB,EAAsBQ,EAAapC,EAAAA,EAAcwC,WACjD,aACAZ,EAAsBQ,EAAapC,EAAAA,EAAcmD,cAGnD,OAAOF,EAAU,CAACA,KAAYC,GAAYE,KAAK,KAAO,CAAC,gBAAiBF,GAAYE,KAAK,IAC3F,C,yHCpDO,SAASC,GAAc,QAAEvJ,EAAO,6BAAEwJ,IACvC,MAmCMC,EAAAA,W,WAA2B,aAC/BxE,EAAAA,EAAAA,GAAkB,iCAAkC,CAAEuC,OAAQ,mBAE9D,MAAMkC,EAAmBR,EAAkBlJ,EAAQyG,SAAS0C,SAE5D,IAAI9J,EAEJ,IACEA,QAAiBwH,EAA0BR,OAAOqD,EAAkB1J,EACtE,CAAE,MAAOmB,GAEP,YADAiB,EAAAA,EAAAA,IAAajB,EAAgB,CAAC,sCAAwCA,EAAgBkB,SAExF,CAEA,MAAMsH,EAASC,SAASC,cAAc,KACtCF,EAAOpB,OAAS,SAChBoB,EAAOG,KAAOzK,EAAS0K,IACvBH,SAASpK,KAAKwK,YAAYL,GAC1BA,EAAOM,QACPL,SAASpK,KAAK0K,YAAYP,EAC5B,E,iOApBMF,GAsBN,MAAO,CACLvH,KAAM,CACJiI,8BAA+BC,QAAQZ,IAEzCa,QAAS,CACPC,YA9DgB,MAClBrF,EAAAA,EAAAA,GAAkB,iCAAkC,CAAEuC,OAAQ,QAE9D,MACM+C,EAAW,GADQrB,EAAkBlJ,EAAQyG,SAAS0C,eAItCS,SAASY,cAAc,oCAE/BC,QAAQC,IACpB,GAAKA,EAMLC,IAAOD,EAAMH,OANb,CACE,MAAMpJ,EAAQ,IAAIH,MAAM,0CACxBoB,EAAAA,EAAAA,IAAajB,EAAO,CAAC,2BAA4BA,EAAMkB,SAEzD,CAEsB,GACrB,YAAY,EA8CbuI,aA3CiB,MACnB3F,EAAAA,EAAAA,GAAkB,iCAAkC,CAAEuC,OAAQ,SAE9D,MACM+C,EAAW,GADQrB,EAAkBlJ,EAAQyG,SAAS0C,gBAEtD0B,EAAU,gCAAgCC,mBAAmBrL,KAAKC,UAAUM,MAElF,IACE2K,IAAOE,EAASN,EAClB,CAAE,MAAOpJ,GAEP,YADAiB,EAAAA,EAAAA,IAAajB,EAAgB,CAAC,4BAA8BA,EAAgBkB,SAE9E,GAgCEoH,4BAGN,CCxEO,SAASsB,EAAWC,GACzB,MAAM,QAAEX,GAAYd,EAAcyB,GAElC,OACE,kBAACC,EAAAA,KAAIA,KACH,kBAACA,EAAAA,KAAKC,KAAI,CAACjH,MAAM,MAAMkH,QAASd,EAAQC,cACxC,kBAACW,EAAAA,KAAKC,KAAI,CAACjH,MAAM,OAAOkH,QAASd,EAAQO,eAG/C,CCJA,SAASQ,EAAoBJ,GAC3B,MAAM,QAAEhL,EAAO,6BAAEwJ,GAAiCwB,EAElD,OACE,kBAACK,EAAAA,SAAQA,CAACC,QAAS,kBAACP,EAAUA,CAAC/K,QAASA,EAASwJ,6BAA8BA,KAC7E,kBAAC+B,EAAAA,OAAMA,CACLC,KAAK,eACLzG,KAAK,KACL0G,QAAQ,YACRC,KAAK,UACLC,aAAW,sBACXC,QAAQ,wBAIhB,CAEO,MAAMC,GAAazG,EAAAA,EAAAA,MAAKgG,GCzB/B,SAASU,EAASC,EAAiBC,EAAiBxI,GAClD,MAAMyI,EAAQ,GACRC,EAAa1I,EAAO,EAAI,EAE9B,IAAK,IAAI2I,EAAI,EAAGA,EAAIJ,EAAMvH,OAAQ2H,GAAKD,EACrCD,EAAMG,KAAK,CACTL,MAAO,EACP9H,MAAOT,EAAOwI,EAAMD,EAAMI,EAAI,IAAMH,EAAMD,EAAMI,EAAI,IACpDE,OAAQN,EAAMI,GACdG,IAAKP,EAAMI,EAAI,GACfI,KAAMR,EAAMI,EAAI,GAChBK,UAAWhJ,EAAOuI,EAAMI,EAAI,GAAK,EACjCM,SAAUjJ,EAAOuI,EAAMI,EAAI,GAAK,EAChCO,SAAUlJ,EAAOuI,EAAMI,EAAI,GAAKJ,EAAMI,EAAI,GAAKJ,EAAMI,EAAI,GACzDQ,YAAanJ,EAAOuI,EAAMI,EAAI,GAAK,EACnCS,YAAapJ,EAAOuI,EAAMI,GAAKJ,EAAMI,EAAI,GAAKJ,EAAMI,GACpDU,SAAU,KAId,OAAOZ,CACT,CCNA,SAASa,GAAoB,QAC3B9M,EAAO,KACPwD,EAAI,SACJuJ,EAAQ,6BACRvD,EAA4B,qBAC5BwD,EAAoB,2BACpBC,IAEA,MAAM,QAAEC,IAAYC,EAAAA,EAAAA,aAGdC,GAAYtJ,EAAAA,EAAAA,UAChB,IDHG,SAAmCuJ,EAAoBrB,EAAiBsB,EAAc9J,GAC3F,IAAK6J,EAAO7I,OACV,OAGF,MAAM+I,EAAsB,GAE5B,IAAK,IAAIpB,EAAI,EAAGA,EAAIkB,EAAO7I,OAAQ2H,IAAK,CACtCoB,EAAWpB,GAAK,GAEhB,IAAK,MAAMqB,KAAQ1B,EAASuB,EAAOlB,GAAIH,EAAOxI,GAI5C,GAHAgK,EAAKzB,MAAQI,EACboB,EAAWpB,GAAGC,KAAKoB,GAEfrB,EAAI,EAAG,CACT,MACMsB,EADmBF,EAAWpB,GAAGuB,MAAM,GAAI,GAE9B9F,QAAO,CAACC,EAAK8F,IAAMA,EAAEf,YAAce,EAAEjB,SAAW7E,GAAK,GAAK2F,EAAKZ,YAE5EgB,EAAYL,EAAWpB,EAAI,GACjC,IAAI0B,EAAkB,EAEtB,IAAK,MAAMC,KAAiBF,EAAW,CACrC,MAAMG,EAAkBF,EAAkBC,EAAclB,YAClDoB,EAAgBD,EAAkBD,EAAcpB,SAEtD,GAAIqB,GAAmBN,GAAoBO,EAAgBP,EAAkB,CAC3EK,EAAcjB,SAAST,KAAKoB,GAC5B,KACF,CACEK,GAAmBC,EAAclB,YAAckB,EAAcpB,QAEjE,CACF,CAEJ,CAEA,MACMuB,EAAQ,CADDV,EAAW,GAAG,IAGrBW,EAAc,GACdC,EAAc,GACdC,EAAa,GACbC,EAAc,GACdC,EAAkB,GAClBC,EAAmB,GAEzB,KAAON,EAAMzJ,QAAQ,CACnB,MAAMgJ,EAAOS,EAAMO,QACnBN,EAAY9B,KAAKoB,EAAKvJ,OACtBkK,EAAY/B,KAAKoB,EAAKzB,OACtBqC,EAAWhC,KAAKoB,EAAKjB,MACrB8B,EAAYjC,KAAKoB,EAAKlB,KACtBgC,EAAgBlC,KAAKoB,EAAKhB,WAC1B+B,EAAiBnC,KAAKoB,EAAKf,UAC3BwB,EAAMQ,WAAWjB,EAAKX,SACxB,CAEA,IAAI6B,EAAY,QAGhB,OAAQpB,GACN,IAAK,UACL,IAAK,gBACL,IAAK,mBACL,IAAK,cACHoB,EAAY,KACZ,MACF,IAAK,QACHA,EAAY,QAIhB,MAAMC,EAAS,CACb,CAAE7O,KAAM,QAAS8O,OAAQT,GACzB,CAAErO,KAAM,QAAS8O,OAAQV,EAAalK,KAAM6K,EAAAA,UAAUC,QACtD,CAAEhP,KAAM,OAAQ8O,OAAQR,EAAYW,OAAQ,CAAEzB,KAAMoB,IACpD,CAAE5O,KAAM,QAAS8O,OAAQP,EAAaU,OAAQ,CAAEzB,KAAMoB,KAGpDlL,GACFmL,EAAOvC,KAEH,CACEtM,KAAM,YACN8O,OAAQN,EACRS,OAAQ,CAAEzB,KAAMoB,IAElB,CACE5O,KAAM,aACN8O,OAAQL,EACRQ,OAAQ,CAAEzB,KAAMoB,KAMxB,MAAMM,EAAsB,CAC1BlP,KAAM,WACNmP,KAAM,CAAEC,2BAA4B,cACpCP,UAGF,OAAOQ,EAAAA,EAAAA,iBAAgBH,EACzB,CCpGMI,CACEpP,EAAQqP,YAAYhC,OACpBrN,EAAQqP,YAAYrD,MACpBhM,EAAQyG,SAASD,MACjB4D,QAAQ5G,KAEZ,CAACxD,EAASwD,IAGZ,OACE,kBAAC8L,EAAAA,EAAiBA,CAChBpN,KAAMkL,EACNmC,mBAAoBvC,EACpBwC,oBAAqB,kBAAC3D,EAAUA,CAAC7L,QAASA,EAASwJ,6BAA8BA,IACjFuD,SAAUA,EACV0C,SAnBa,KAAMC,EAAAA,EAAAA,aAAY,CAAEC,OAAQ,CAAEC,KAAM1C,EAAU,QAAU,UAoBrED,2BAA4BA,EAC5B4C,uBAAAA,GAGN,CAEO,MAAMnM,GAAa0B,EAAAA,EAAAA,MAAK0H,E,upBCjDxB,SAAS1K,EAAajB,EAAc2O,GACzC,MAAMC,EAAUD,EAAKlI,QAAO,CAACC,EAAKmI,EAAK7D,IAAO,E,sUAAA,IAAKtE,GAAAA,CAAK,CAAC,OAAOsE,EAAI,KAAM6D,KAAQ,CAAEC,WAAY,iBAEhGC,EAAAA,EAAO/O,MAAMA,EAAO4O,IAEpBI,EAAAA,EAAAA,gBAAeC,QAAQ,CACrBpM,KAAMqM,EAAAA,UAAUC,WAAWxQ,KAC3ByQ,QAAST,GAEb,CAEO,SAASU,EAAeV,GAC7BI,EAAAA,EAAOO,KAAKX,IAEZK,EAAAA,EAAAA,gBAAeC,QAAQ,CACrBpM,KAAMqM,EAAAA,UAAUK,aAAa5Q,KAC7ByQ,QAAST,GAEb,CAEO,SAASa,EAAeb,IAC7BK,EAAAA,EAAAA,gBAAeC,QAAQ,CACrBpM,KAAMqM,EAAAA,UAAUO,aAAa9Q,KAC7ByQ,QAAST,GAEb,C,oECwCA,MAAMe,EAA8BC,EAAAA,GAAOC,QAAQrD,MAAM,GAEzD,SAASsD,IACP,MAAM,SAAEC,GAAa,IAAIC,IAAIrI,OAAOC,SAAS/F,YAC7C,OAAOkO,EAASlQ,MAAM,KAAKoQ,OAAS,EACtC,CAEA,SAASC,IACP,MAAMnC,EAA4B,CAEhCoC,WAAYtC,EAAAA,OAAOuC,KAAKC,EAAAA,IAAkBC,QAC1CC,WAAYC,EAAAA,EACZC,KAAMX,KAQR,OALI/B,EAAK0C,OAASd,IAEhB5B,EAAK2C,KAAO,IAAIhJ,gBAAgBC,OAAOC,SAASC,QAAQ7J,IAAI,oBAAsB,IAG7E+P,CACT,CAEO,SAAShK,EACd4M,EACA7G,IAEA8G,EAAAA,EAAAA,mBAAyBD,EAAiB,CACxC7G,QACAiE,KAAMmC,KAEV,C", "sources": ["webpack://grafana-pyroscope-app/./pages/AdHocView/ui/AdHocColums.tsx", "webpack://grafana-pyroscope-app/./pages/AdHocView/infrastructure/adHocProfileClient.ts", "webpack://grafana-pyroscope-app/./pages/AdHocView/infrastructure/helpers/stripBase64Prefix.ts", "webpack://grafana-pyroscope-app/./pages/AdHocView/domain/useUploadFile.ts", "webpack://grafana-pyroscope-app/./pages/AdHocView/ui/AdHocFileDropZone.tsx", "webpack://grafana-pyroscope-app/./pages/AdHocView/domain/accepted-file-types.ts", "webpack://grafana-pyroscope-app/./pages/AdHocView/ui/AdHocFlameGraph.tsx", "webpack://grafana-pyroscope-app/./pages/AdHocView/ui/AdHocProfileTypeSelector.tsx", "webpack://grafana-pyroscope-app/./pages/AdHocView/ui/AdHocSpinner.tsx", "webpack://grafana-pyroscope-app/./pages/AdHocView/ui/tabs/AdHocSingle.tsx", "webpack://grafana-pyroscope-app/./pages/AdHocView/ui/tabs/AdHocComparison.tsx", "webpack://grafana-pyroscope-app/./pages/AdHocView/ui/AdHocTabs.tsx", "webpack://grafana-pyroscope-app/./pages/AdHocView/AdHocView.tsx", "webpack://grafana-pyroscope-app/./pages/ProfilesExplorerView/components/SceneExploreDiffFlameGraph/domain/types.ts", "webpack://grafana-pyroscope-app/./shared/components/FlameGraph/components/infrastructure/flamegraphDotComApiClient.ts", "webpack://grafana-pyroscope-app/./shared/components/FlameGraph/components/domain/getExportFilename.ts", "webpack://grafana-pyroscope-app/./shared/components/FlameGraph/components/domain/useExportMenu.ts", "webpack://grafana-pyroscope-app/./shared/components/FlameGraph/components/ExportMenu.tsx", "webpack://grafana-pyroscope-app/./shared/components/FlameGraph/components/ExportData.tsx", "webpack://grafana-pyroscope-app/./shared/components/FlameGraph/domain/flamebearerToDataFrameDTO.ts", "webpack://grafana-pyroscope-app/./shared/components/FlameGraph/FlameGraph.tsx", "webpack://grafana-pyroscope-app/./shared/domain/displayStatus.ts", "webpack://grafana-pyroscope-app/./shared/domain/reportInteraction.ts"], "sourcesContent": ["import { css } from '@emotion/css';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  container: css`\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    gap: ${theme.spacing(1)};\n    width: 100%;\n  `,\n  column: css`\n    width: 50%;\n  `,\n});\n\ntype AdHocColumnsProps = {\n  left: React.ReactNode;\n  right: React.ReactNode;\n};\n\nexport function AdHocColumns({ left, right }: AdHocColumnsProps) {\n  const styles = useStyles2(getStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.column}>{left}</div>\n      <div className={styles.column}>{right}</div>\n    </div>\n  );\n}\n", "import { ApiClient } from '@shared/infrastructure/http/ApiClient';\n\nimport { AdHocProfile } from '../domain/AdHocProfile';\nimport { stripBase64Prefix } from './helpers/stripBase64Prefix';\n\nclass AdHocProfileClient extends ApiClient {\n  async get(profileId: string, profileType: string): Promise<AdHocProfile> {\n    const response = await this.fetch('/adhocprofiles.v1.AdHocProfileService/Get', {\n      method: 'POST',\n      body: JSON.stringify({\n        id: profileId,\n        profile_type: profileType,\n      }),\n    });\n\n    const json = await response.json();\n\n    return {\n      id: json.id,\n      name: json.name,\n      profileTypes: json.profileTypes,\n      profile: JSON.parse(json.flamebearerProfile),\n    };\n  }\n\n  async uploadSingle(file: File): Promise<AdHocProfile> {\n    const profile = await this._readProfileFile(file);\n\n    const response = await this.fetch('/adhocprofiles.v1.AdHocProfileService/Upload', {\n      method: 'POST',\n      body: JSON.stringify({\n        name: file.name,\n        profile,\n      }),\n    });\n\n    const json = await response.json();\n\n    return {\n      id: json.id,\n      name: file.name,\n      profileTypes: json.profileTypes,\n      // when the uploaded file contains multiple sample types, the 1st is always returned by the API\n      profile: JSON.parse(json.flamebearerProfile),\n    };\n  }\n\n  // TODO\n  async uploadDiff() {\n    return {\n      id: '?',\n      name: '??',\n      profileTypes: [],\n      profile: null,\n    };\n  }\n\n  async _readProfileFile(file: File): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const fileReader = new FileReader();\n\n      fileReader.addEventListener('load', () => {\n        try {\n          resolve(stripBase64Prefix(fileReader.result as string));\n        } catch (error) {\n          reject(error);\n        }\n      });\n\n      fileReader.addEventListener('error', (/*event: ProgressEvent<FileReader>*/) => {\n        // TODO: upgrade TS lib compiler option to support latest JS features\n        // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause\n        reject(new Error(`Error while reading file \"${file.name}\"!` /*, { cause: event }*/));\n      });\n\n      fileReader.readAsDataURL(file);\n    });\n  }\n}\n\nexport const adHocProfileClient = new AdHocProfileClient();\n", "export function stripBase64Prefix(fileContent: string): string {\n  const [, base64Content] = fileContent.split(';base64,');\n\n  if (!base64Content) {\n    throw new Error('No content after stripping the base64 prefix.');\n  }\n\n  if (fileContent === base64Content) {\n    throw new Error('No base64 prefix?!');\n  }\n\n  return base64Content;\n}\n", "import { SelectableValue } from '@grafana/data';\nimport { displayError } from '@shared/domain/displayStatus';\nimport { useCallback, useEffect, useState } from 'react';\n\nimport { adHocProfileClient } from '../infrastructure/adHocProfileClient';\nimport { AdHocProfile } from './AdHocProfile';\n\nconst DEFAULT_PROFILE_DATA: AdHocProfile = {\n  id: '',\n  name: '',\n  profileTypes: [],\n  profile: null,\n};\n\nexport function useUploadFile() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [profileData, setProfileData] = useState(DEFAULT_PROFILE_DATA);\n\n  useEffect(() => {\n    return () => {\n      adHocProfileClient.abort();\n    };\n  }, []);\n\n  const removeFile = useCallback(() => {\n    adHocProfileClient.abort();\n\n    setIsLoading(false);\n    setProfileData(DEFAULT_PROFILE_DATA);\n  }, []);\n\n  const processFile = useCallback(\n    async (file: File) => {\n      removeFile();\n\n      try {\n        setIsLoading(true);\n\n        const data = await adHocProfileClient.uploadSingle(file);\n\n        setProfileData(data);\n      } catch (error) {\n        setProfileData(DEFAULT_PROFILE_DATA);\n\n        if (!adHocProfileClient.isAbortError(error)) {\n          displayError(error as Error, ['Error while uploading profile!', (error as Error).message]);\n        }\n      }\n\n      setIsLoading(false);\n    },\n    [removeFile]\n  );\n\n  const removeProfile = () => {\n    adHocProfileClient.abort();\n\n    setIsLoading(false);\n    setProfileData((prevData) => ({ ...prevData, profile: null }));\n  };\n\n  const selectProfileType = useCallback(\n    async (option: SelectableValue<string>) => {\n      const profileType = option.value;\n\n      if (!profileType || !profileData.id || !profileData.profileTypes.includes(profileType)) {\n        return;\n      }\n\n      removeProfile();\n\n      setIsLoading(true);\n\n      try {\n        const data = await adHocProfileClient.get(profileData.id, profileType);\n\n        setProfileData((prevData) => ({\n          ...prevData,\n          profile: data.profile,\n        }));\n      } catch (error) {\n        if (!adHocProfileClient.isAbortError(error)) {\n          displayError(error as Error, ['Error while fetching profile!', (error as Error).message]);\n        }\n      }\n\n      setIsLoading(false);\n    },\n    [profileData.id, profileData.profileTypes]\n  );\n\n  return {\n    processFile,\n    profileTypes: profileData.profileTypes,\n    selectProfileType,\n    profile: profileData.profile,\n    removeFile,\n    isLoading,\n  };\n}\n", "import { DropzoneFile, FileDropzone } from '@grafana/ui';\nimport { displayError } from '@shared/domain/displayStatus';\nimport React, { useCallback } from 'react';\n\nimport { ACCEPTED_FILE_TYPES } from '../domain/accepted-file-types';\n\nconst options = {\n  accept: ACCEPTED_FILE_TYPES,\n  multiple: false,\n  // maxSize: 42, // TODO?\n  onError(error: Error) {\n    displayError(error, ['Error while uploading file!', error.toString()]);\n  },\n};\n\ntype AdHocFileDropZoneProps = {\n  onFileDropped: (file: File) => void;\n  onFileRemove: (file: DropzoneFile) => void;\n};\n\nexport function AdHocFileDropZone({ onFileDropped, onFileRemove }: AdHocFileDropZoneProps) {\n  const onDropAccepted = useCallback(\n    function (files: File[]) {\n      onFileDropped(files[0]);\n    },\n    [onFileDropped]\n  );\n\n  return (\n    <FileDropzone\n      options={{\n        ...options,\n        onDropAccepted,\n      }}\n      onFileRemove={onFileRemove}\n    />\n  );\n}\n", "export const ACCEPTED_FILE_TYPES = {\n  'application/gzip': ['.gz'],\n  'application/json': ['.json'],\n  'application/proto': ['.pb', '.pprof'],\n};\n", "import { css } from '@emotion/css';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport { FlameGraph } from '@shared/components/FlameGraph/FlameGraph';\nimport { FlamebearerProfile } from '@shared/types/FlamebearerProfile';\nimport React from 'react';\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  flamegraph: css`\n    margin-top: ${theme.spacing(2)};\n  `,\n});\n\nexport function AdHocFlameGraph({ profile, diff }: { profile: FlamebearerProfile; diff?: boolean }) {\n  const styles = useStyles2(getStyles);\n\n  return (\n    <div className={styles.flamegraph} data-testid=\"flamegraph\">\n      <FlameGraph profile={profile} diff={diff} />\n    </div>\n  );\n}\n", "import { css } from '@emotion/css';\nimport { GrafanaTheme2, SelectableValue } from '@grafana/data';\nimport { InlineField, InlineFieldRow, Select, useStyles2 } from '@grafana/ui';\nimport React, { useCallback, useEffect, useMemo, useState } from 'react';\n\ntype ProfileSelectorProps = {\n  profileTypes: string[];\n  onChange: (options: SelectableValue<string>) => void;\n};\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  selectorContainer: css`\n    display: flex;\n    justify-content: center;\n    margin-bottom: ${theme.spacing(2)};\n  `,\n});\n\nexport function AdHocProfileTypeSelector({ profileTypes, onChange }: ProfileSelectorProps) {\n  const styles = useStyles2(getStyles);\n\n  const options = useMemo(() => profileTypes.map((type) => ({ value: type, label: type })), [profileTypes]);\n  const [option, setOption] = useState<SelectableValue<string>>();\n\n  const _onChange = useCallback(\n    (o: SelectableValue<string>) => {\n      setOption(o);\n      onChange(o);\n    },\n    [onChange]\n  );\n\n  useEffect(() => {\n    // when the uploaded file contains multiple sample types, the 1st is always returned by the API\n    // so, we select it automatically\n    setOption(options[0]);\n  }, [options]);\n\n  return (\n    <div className={styles.selectorContainer}>\n      <InlineFieldRow>\n        <InlineField label=\"Profile\" disabled={!options.length} data-testid=\"profile-types-dropdown\">\n          {/* added a key to ensure the dropdown is properly reset */}\n          <Select key={option?.value} value={option} options={options} onChange={_onChange} width={16} />\n        </InlineField>\n      </InlineFieldRow>\n    </div>\n  );\n}\n", "import { css } from '@emotion/css';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Spinner, useStyles2 } from '@grafana/ui';\nimport React from 'react';\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  spinner: css`\n    text-align: center;\n    margin-top: ${theme.spacing(2)};\n  `,\n});\n\nexport function AdHocSpinner() {\n  const styles = useStyles2(getStyles);\n\n  return (\n    <div className={styles.spinner}>\n      <Spinner size={36} />\n    </div>\n  );\n}\n", "import { SelectableValue } from '@grafana/data';\nimport { reportInteraction } from '@shared/domain/reportInteraction';\nimport React, { memo } from 'react';\n\nimport { useUploadFile } from '../../domain/useUploadFile';\nimport { AdHocFileDropZone } from '../AdHocFileDropZone';\nimport { AdHocFlameGraph } from '../AdHocFlameGraph';\nimport { AdHocProfileTypeSelector } from '../AdHocProfileTypeSelector';\nimport { AdHocSpinner } from '../AdHocSpinner';\n\nfunction AdHocSingleComponent() {\n  const { processFile, profileTypes, selectProfileType, profile, removeFile, isLoading } = useUploadFile();\n\n  const onChangeProfileType = (options: SelectableValue<string>) => {\n    reportInteraction('g_pyroscope_app_ad_hoc_profile_metric_selected');\n    selectProfileType(options);\n  };\n\n  const onFileDropped = (file: File) => {\n    reportInteraction('g_pyroscope_app_ad_hoc_file_dropped', { fileType: file.type });\n    processFile(file);\n  };\n\n  const onFileRemoved = () => {\n    reportInteraction('g_pyroscope_app_ad_hoc_file_removed');\n    removeFile();\n  };\n\n  return (\n    <div>\n      <AdHocProfileTypeSelector profileTypes={profileTypes} onChange={onChangeProfileType} />\n      <AdHocFileDropZone onFileDropped={onFileDropped} onFileRemove={onFileRemoved} />\n      {isLoading && !profile ? <AdHocSpinner /> : null}\n      {profile && <AdHocFlameGraph profile={profile} />}\n    </div>\n  );\n}\n\nexport const AdHocSingle = memo(AdHocSingleComponent);\n", "import React from 'react';\n\nimport { AdHocColumns } from '../AdHocColums';\nimport { AdHocSingle } from './AdHocSingle';\n\nexport function AdHocComparison() {\n  return <AdHocColumns left={<AdHocSingle />} right={<AdHocSingle />} />;\n}\n", "import { css } from '@emotion/css';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Tab, TabContent, TabsBar, useStyles2 } from '@grafana/ui';\nimport React, { useState } from 'react';\n\nimport { AdHocComparison } from './tabs/AdHocComparison';\nimport { AdHocSingle } from './tabs/AdHocSingle';\n\n// import { AdHocDiff } from './tabs/AdHocDiff';\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  tabContent: css`\n    padding: ${theme.spacing(2)};\n    margin: ${theme.spacing(2)};\n  `,\n});\n\nexport function AdHocTabs() {\n  const styles = useStyles2(getStyles);\n  const [activeTabIndex, setActiveTabIndex] = useState(0);\n\n  return (\n    <div>\n      <TabsBar>\n        <Tab label=\" Single view\" active={activeTabIndex === 0} onChangeTab={() => setActiveTabIndex(0)} />\n        <Tab label=\" Comparison view\" active={activeTabIndex === 1} onChangeTab={() => setActiveTabIndex(1)} />\n        {/* <Tab\n          label=\" Diff view\"\n          active={activeTabIndex === 2}\n          onChangeTab={onChangeTab(2)}\n        /> */}\n      </TabsBar>\n      <TabContent className={styles.tabContent}>\n        {activeTabIndex === 0 && <AdHocSingle />}\n        {activeTabIndex === 1 && <AdHocComparison />}\n        {/* {activeTabIndex === 2 && <AdHocDiff  />} */}\n      </TabContent>\n    </div>\n  );\n}\n", "import { PageTitle } from '@shared/ui/PageTitle';\nimport React from 'react';\n\nimport { AdHocTabs } from './ui/AdHocTabs';\n\nexport default function AdHocView() {\n  return (\n    <>\n      <PageTitle title=\"Ad hoc view\" />\n      <AdHocTabs />\n    </>\n  );\n}\n", "export enum CompareTarget {\n  BASELINE = 'baseline',\n  COMPARISON = 'comparison',\n}\n", "import { HttpClient } from '@shared/infrastructure/http/HttpClient';\nimport { FlamebearerProfile } from '@shared/types/FlamebearerProfile';\n\ntype UploadResponse = {\n  key: string;\n  url: string;\n  subProfiles: any[]; // TODO: define them, what are they?\n};\n\nclass FlamegraphDotComApiClient extends HttpClient {\n  constructor() {\n    super('https://flamegraph.com/api', {\n      'content-type': 'application/json',\n    });\n  }\n\n  async upload(name: string, profile: FlamebearerProfile): Promise<UploadResponse> {\n    const response = await this.fetch('/upload/v1', {\n      method: 'POST',\n      body: JSON.stringify({\n        name,\n        profile: btoa(JSON.stringify(profile)),\n        fileTypeData: {\n          units: profile.metadata.units,\n          spyName: profile.metadata.spyName,\n        },\n        type: 'json',\n      }),\n    });\n\n    const json = await response.json();\n\n    return json;\n  }\n}\n\nexport const flamegraphDotComApiClient = new FlamegraphDotComApiClient();\n", "import { dateTimeParse, TimeRange } from '@grafana/data';\n\nimport { CompareTarget } from '../../../../../pages/ProfilesExplorerView/components/SceneExploreDiffFlameGraph/domain/types';\n\ntype DateParts = {\n  year: string;\n  month: string;\n  day: string;\n  hour: string;\n  minute: string;\n  second: string;\n};\n\nconst DATETIME_FORMATTER = new Intl.DateTimeFormat('fr-CA', {\n  year: 'numeric',\n  month: '2-digit',\n  day: '2-digit',\n  hour12: false,\n  hour: '2-digit',\n  minute: '2-digit',\n  second: '2-digit',\n});\n\nfunction format(date: Date): string {\n  const parts = DATETIME_FORMATTER.formatToParts(date).reduce((acc, { type, value }) => {\n    acc[type as keyof DateParts] = value;\n    return acc;\n  }, {} as DateParts);\n\n  return `${parts.year}-${parts.month}-${parts.day}_${parts.hour}${parts.minute}`;\n}\n\nfunction dateForExportFilename(timeRange: TimeRange) {\n  const from = new Date(Math.round(timeRange.from.unix() * 1000));\n  const to = new Date(Math.round(timeRange.to.unix() * 1000));\n\n  return `${format(from)}-to-${format(to)}`;\n}\n\nfunction getTimeRange(target: CompareTarget) {\n  const [fromKey, toKey] = target === CompareTarget.BASELINE ? ['diffFrom', 'diffTo'] : ['diffFrom-2', 'diffTo-2'];\n\n  const searchParams = new URLSearchParams(window.location.search);\n  const from = searchParams.get(fromKey) as string;\n  const to = searchParams.get(toKey) as string;\n\n  return {\n    raw: { from, to },\n    from: dateTimeParse(from),\n    to: dateTimeParse(to),\n  };\n}\n\nexport function getExportFilename(appName?: string) {\n  const timeRanges = [\n    'baseline',\n    dateForExportFilename(getTimeRange(CompareTarget.BASELINE)),\n    'comparison',\n    dateForExportFilename(getTimeRange(CompareTarget.COMPARISON)),\n  ];\n\n  return appName ? [appName, ...timeRanges].join('_') : ['flamegraph', ...timeRanges].join('_');\n}\n", "import { displayError } from '@shared/domain/displayStatus';\nimport { reportInteraction } from '@shared/domain/reportInteraction';\nimport 'compression-streams-polyfill';\nimport saveAs from 'file-saver';\n\nimport { ExportDataProps } from '../ExportData';\nimport { flamegraphDotComApiClient } from '../infrastructure/flamegraphDotComApiClient';\nimport { getExportFilename } from './getExportFilename';\n\n/* Note: no pprof export, as the underlying API only accepts a single query (see PprofApiClient) */\nexport function useExportMenu({ profile, enableFlameGraphDotComExport }: ExportDataProps) {\n  const downloadPng = () => {\n    reportInteraction('g_pyroscope_app_export_profile', { format: 'png' });\n\n    const customExportName = getExportFilename(profile.metadata.appName);\n    const filename = `${customExportName}.png`;\n\n    // TODO use ref, this won't work for comparison side by side (??!)\n    const canvasElement = document.querySelector('canvas[data-testid=\"flameGraph\"]') as HTMLCanvasElement;\n\n    canvasElement.toBlob((blob) => {\n      if (!blob) {\n        const error = new Error('No Blob, the image cannot be created.');\n        displayError(error, ['Failed to export to png!', error.message]);\n        return;\n      }\n\n      saveAs(blob, filename);\n    }, 'image/png');\n  };\n\n  const downloadJson = () => {\n    reportInteraction('g_pyroscope_app_export_profile', { format: 'json' });\n\n    const customExportName = getExportFilename(profile.metadata.appName);\n    const filename = `${customExportName}.json`;\n    const dataStr = `data:text/json;charset=utf-8,${encodeURIComponent(JSON.stringify(profile))}`;\n\n    try {\n      saveAs(dataStr, filename);\n    } catch (error) {\n      displayError(error as Error, ['Failed to export to JSON!', (error as Error).message]);\n      return;\n    }\n  };\n\n  const uploadToFlamegraphDotCom = async () => {\n    reportInteraction('g_pyroscope_app_export_profile', { format: 'flamegraph.com' });\n\n    const customExportName = getExportFilename(profile.metadata.appName);\n\n    let response;\n\n    try {\n      response = await flamegraphDotComApiClient.upload(customExportName, profile);\n    } catch (error) {\n      displayError(error as Error, ['Failed to export to flamegraph.com!', (error as Error).message]);\n      return;\n    }\n\n    const dlLink = document.createElement('a');\n    dlLink.target = '_blank';\n    dlLink.href = response.url;\n    document.body.appendChild(dlLink);\n    dlLink.click();\n    document.body.removeChild(dlLink);\n  };\n\n  return {\n    data: {\n      shouldDisplayFlamegraphDotCom: Boolean(enableFlameGraphDotComExport),\n    },\n    actions: {\n      downloadPng,\n      downloadJson,\n      uploadToFlamegraphDotCom,\n    },\n  };\n}\n", "import { Menu } from '@grafana/ui';\nimport React from 'react';\n\nimport { useExportMenu } from './domain/useExportMenu';\nimport { ExportDataProps } from './ExportData';\n\nexport function ExportMenu(props: ExportDataProps) {\n  const { actions } = useExportMenu(props);\n\n  return (\n    <Menu>\n      <Menu.Item label=\"png\" onClick={actions.downloadPng} />\n      <Menu.Item label=\"json\" onClick={actions.downloadJson} />\n    </Menu>\n  );\n}\n", "import { Button, Dropdown } from '@grafana/ui';\nimport { FlamebearerProfile } from '@shared/types/FlamebearerProfile';\nimport React, { memo } from 'react';\n\nimport { ExportMenu } from './ExportMenu';\n\nexport type ExportDataProps = {\n  profile: FlamebearerProfile;\n  enableFlameGraphDotComExport?: boolean;\n};\n\nfunction ExportDataComponent(props: ExportDataProps) {\n  const { profile, enableFlameGraphDotComExport } = props;\n\n  return (\n    <Dropdown overlay={<ExportMenu profile={profile} enableFlameGraphDotComExport={enableFlameGraphDotComExport} />}>\n      <Button\n        icon=\"download-alt\"\n        size=\"sm\"\n        variant=\"secondary\"\n        fill=\"outline\"\n        aria-label=\"Export profile data\"\n        tooltip=\"Export profile data\"\n      />\n    </Dropdown>\n  );\n}\n\nexport const ExportData = memo(ExportDataComponent);\n", "import { createDataFrame, DataFrameDTO, FieldType } from '@grafana/data';\n\n// eslint-disable-next-line sonarjs/cognitive-complexity\nfunction getNodes(level: number[], names: string[], diff: boolean) {\n  const nodes = [];\n  const itemOffset = diff ? 7 : 4;\n\n  for (let i = 0; i < level.length; i += itemOffset) {\n    nodes.push({\n      level: 0,\n      label: diff ? names[level[i + 6]] : names[level[i + 3]],\n      offset: level[i],\n      val: level[i + 1],\n      self: level[i + 2],\n      selfRight: diff ? level[i + 5] : 0,\n      valRight: diff ? level[i + 4] : 0,\n      valTotal: diff ? level[i + 1] + level[i + 4] : level[i + 1],\n      offsetRight: diff ? level[i + 3] : 0,\n      offsetTotal: diff ? level[i] + level[i + 3] : level[i],\n      children: [],\n    });\n  }\n\n  return nodes;\n}\n\n// eslint-disable-next-line sonarjs/cognitive-complexity\nexport function flamebearerToDataFrameDTO(levels: number[][], names: string[], unit: string, diff: boolean) {\n  if (!levels.length) {\n    return;\n  }\n\n  const nodeLevels: any[][] = [];\n\n  for (let i = 0; i < levels.length; i++) {\n    nodeLevels[i] = [];\n\n    for (const node of getNodes(levels[i], names, diff)) {\n      node.level = i;\n      nodeLevels[i].push(node);\n\n      if (i > 0) {\n        const prevNodesInLevel = nodeLevels[i].slice(0, -1);\n        const currentNodeStart =\n          prevNodesInLevel.reduce((acc, n) => n.offsetTotal + n.valTotal + acc, 0) + node.offsetTotal;\n\n        const prevLevel = nodeLevels[i - 1];\n        let prevLevelOffset = 0;\n\n        for (const prevLevelNode of prevLevel) {\n          const parentNodeStart = prevLevelOffset + prevLevelNode.offsetTotal;\n          const parentNodeEnd = parentNodeStart + prevLevelNode.valTotal;\n\n          if (parentNodeStart <= currentNodeStart && parentNodeEnd > currentNodeStart) {\n            prevLevelNode.children.push(node);\n            break;\n          } else {\n            prevLevelOffset += prevLevelNode.offsetTotal + prevLevelNode.valTotal;\n          }\n        }\n      }\n    }\n  }\n\n  const root = nodeLevels[0][0];\n  const stack = [root];\n\n  const labelValues = [];\n  const levelValues = [];\n  const selfValues = [];\n  const valueValues = [];\n  const selfRightValues = [];\n  const valueRightValues = [];\n\n  while (stack.length) {\n    const node = stack.shift();\n    labelValues.push(node.label);\n    levelValues.push(node.level);\n    selfValues.push(node.self);\n    valueValues.push(node.val);\n    selfRightValues.push(node.selfRight);\n    valueRightValues.push(node.valRight);\n    stack.unshift(...node.children);\n  }\n\n  let valueUnit = 'short';\n\n  // See format.ts#getFormatter. We have to use Grafana unit string here though.\n  switch (unit) {\n    case 'samples':\n    case 'trace_samples':\n    case 'lock_nanoseconds':\n    case 'nanoseconds':\n      valueUnit = 'ns';\n      break;\n    case 'bytes':\n      valueUnit = 'bytes';\n      break;\n  }\n\n  const fields = [\n    { name: 'level', values: levelValues },\n    { name: 'label', values: labelValues, type: FieldType.string },\n    { name: 'self', values: selfValues, config: { unit: valueUnit } },\n    { name: 'value', values: valueValues, config: { unit: valueUnit } },\n  ];\n\n  if (diff) {\n    fields.push(\n      ...[\n        {\n          name: 'selfRight',\n          values: selfRightValues,\n          config: { unit: valueUnit },\n        },\n        {\n          name: 'valueRight',\n          values: valueRightValues,\n          config: { unit: valueUnit },\n        },\n      ]\n    );\n  }\n\n  const frame: DataFrameDTO = {\n    name: 'response',\n    meta: { preferredVisualisationType: 'flamegraph' },\n    fields,\n  };\n\n  return createDataFrame(frame);\n}\n", "import { createTheme } from '@grafana/data';\nimport { FlameGraph as GrafanaFlameGraph, Props } from '@grafana/flamegraph';\nimport { useTheme2 } from '@grafana/ui';\nimport React, { memo, useMemo } from 'react';\n\nimport type { FlamebearerProfile } from '../../types/FlamebearerProfile';\nimport { ExportData } from './components/ExportData';\nimport { flamebearerToDataFrameDTO } from './domain/flamebearerToDataFrameDTO';\n\ntype FlameGraphProps = {\n  profile: FlamebearerProfile;\n  diff?: boolean;\n  vertical?: boolean;\n  enableFlameGraphDotComExport?: boolean;\n  collapsedFlamegraphs?: boolean;\n  getExtraContextMenuButtons?: Props['getExtraContextMenuButtons'];\n};\n\nfunction FlameGraphComponent({\n  profile,\n  diff,\n  vertical,\n  enableFlameGraphDotComExport,\n  collapsedFlamegraphs,\n  getExtraContextMenuButtons,\n}: FlameGraphProps) {\n  const { isLight } = useTheme2();\n  const getTheme = () => createTheme({ colors: { mode: isLight ? 'light' : 'dark' } });\n\n  const dataFrame = useMemo(\n    () =>\n      flamebearerToDataFrameDTO(\n        profile.flamebearer.levels,\n        profile.flamebearer.names,\n        profile.metadata.units,\n        Boolean(diff)\n      ),\n    [profile, diff]\n  );\n\n  return (\n    <GrafanaFlameGraph\n      data={dataFrame as any}\n      disableCollapsing={!collapsedFlamegraphs}\n      extraHeaderElements={<ExportData profile={profile} enableFlameGraphDotComExport={enableFlameGraphDotComExport} />}\n      vertical={vertical}\n      getTheme={getTheme as any}\n      getExtraContextMenuButtons={getExtraContextMenuButtons}\n      keepFocusOnDataChange\n    />\n  );\n}\n\nexport const FlameGraph = memo(FlameGraphComponent);\n", "import { AppEvents } from '@grafana/data';\nimport { getAppEvents } from '@grafana/runtime';\nimport { logger } from '@shared/infrastructure/tracking/logger';\n\nexport function displayError(error: Error, msgs: string[]) {\n  const context = msgs.reduce((acc, msg, i) => ({ ...acc, [`info${i + 1}`]: msg }), { handheldBy: 'displayError' });\n\n  logger.error(error, context);\n\n  getAppEvents().publish({\n    type: AppEvents.alertError.name,\n    payload: msgs,\n  });\n}\n\nexport function displayWarning(msgs: string[]) {\n  logger.warn(msgs);\n\n  getAppEvents().publish({\n    type: AppEvents.alertWarning.name,\n    payload: msgs,\n  });\n}\n\nexport function displaySuccess(msgs: string[]) {\n  getAppEvents().publish({\n    type: AppEvents.alertSuccess.name,\n    payload: msgs,\n  });\n}\n", "import { config, reportInteraction as grafanaReportInteraction } from '@grafana/runtime';\nimport { ScaleDistribution } from '@grafana/schema';\nimport { ActionType } from 'xstate';\n\nimport { PYROSCOPE_APP_ID, ROUTES } from '../../constants';\nimport { LayoutType } from '../../pages/ProfilesExplorerView/components/SceneByVariableRepeaterGrid/components/SceneLayoutSwitcher';\nimport { PanelType } from '../../pages/ProfilesExplorerView/components/SceneByVariableRepeaterGrid/components/ScenePanelTypeSwitcher';\nimport { GIT_COMMIT } from '../../version';\n\n// hey future dev: don't forget to add any new value to our features tracking dashboard!\nexport type Interactions = {\n  g_pyroscope_app_ad_hoc_file_dropped: {\n    fileType: string;\n  };\n  g_pyroscope_app_ad_hoc_file_removed: {};\n  g_pyroscope_app_ad_hoc_profile_metric_selected: {};\n  g_pyroscope_app_ad_hoc_profile: {};\n  g_pyroscope_app_compare_link_clicked: {};\n  g_pyroscope_app_diff_auto_select_clicked: {};\n  g_pyroscope_app_diff_choose_preset_clicked: {};\n  g_pyroscope_app_diff_learn_how_clicked: {};\n  g_pyroscope_app_diff_preset_save_clicked: {};\n  g_pyroscope_app_diff_preset_selected: {\n    value: string;\n  };\n  g_pyroscope_app_exclude_action_clicked: {};\n  g_pyroscope_app_explain_flamegraph_clicked: {};\n  g_pyroscope_app_exploration_type_clicked: {\n    explorationType: string;\n  };\n  g_pyroscope_app_export_profile: {\n    format: 'png' | 'json' | 'pprof' | 'flamegraph.com';\n  };\n  g_pyroscope_app_fav_action_clicked: {\n    favAfterClick: boolean;\n  };\n  g_pyroscope_app_filters_changed: {\n    name: string;\n    count: number;\n    operators: string[];\n  };\n  g_pyroscope_app_function_details_clicked: {};\n  g_pyroscope_app_group_by_label_clicked: {};\n  g_pyroscope_app_hide_no_data_changed: {\n    hideNoData: 'on' | 'off';\n  };\n  g_pyroscope_app_include_action_clicked: {};\n  g_pyroscope_app_layout_changed: {\n    layout: LayoutType;\n  };\n  g_pyroscope_app_open_in_explore_clicked: {};\n  g_pyroscope_app_optimize_code_clicked: {};\n  g_pyroscope_app_panel_type_changed: {\n    panelType: PanelType;\n  };\n  g_pyroscope_app_profile_metric_selected: {};\n  g_pyroscope_app_quick_filter_focused: {};\n  g_pyroscope_app_select_action_clicked: {\n    type: ActionType;\n  };\n  g_pyroscope_app_service_name_selected: {};\n  g_pyroscope_app_share_link_clicked: {};\n  g_pyroscope_app_timeseries_scale_changed: {\n    scale: ScaleDistribution;\n  };\n  g_pyroscope_app_upload_ad_hoc_clicked: {};\n  g_pyroscope_app_user_settings_clicked: {};\n};\n\nconst PROFILES_EXPLORER_PAGE_NAME = ROUTES.EXPLORE.slice(1);\n\nfunction getCurrentPage(): string {\n  const { pathname } = new URL(window.location.toString());\n  return pathname.split('/').pop() || '';\n}\n\nfunction getMetaProperties() {\n  const meta: Record<string, any> = {\n    // same naming as Faro (see src/shared/infrastructure/tracking/faro/faro.ts)\n    appRelease: config.apps[PYROSCOPE_APP_ID].version,\n    appVersion: GIT_COMMIT,\n    page: getCurrentPage(),\n  };\n\n  if (meta.page === PROFILES_EXPLORER_PAGE_NAME) {\n    // same naming as Faro (see src/shared/infrastructure/tracking/faro/faro.ts)\n    meta.view = new URLSearchParams(window.location.search).get('explorationType') || '';\n  }\n\n  return meta;\n}\n\nexport function reportInteraction<E extends keyof Interactions, P extends Interactions[E]>(\n  interactionName: E,\n  props?: P\n) {\n  grafanaReportInteraction(interactionName, {\n    props,\n    meta: getMetaProperties(),\n  });\n}\n"], "names": ["getStyles", "theme", "container", "css", "spacing", "column", "AdHocColumns", "left", "right", "styles", "useStyles2", "div", "className", "AdHocProfileClient", "ApiClient", "get", "profileId", "profileType", "response", "fetch", "method", "body", "JSON", "stringify", "id", "profile_type", "json", "name", "profileTypes", "profile", "parse", "flamebearerProfile", "uploadSingle", "file", "_readProfileFile", "uploadDiff", "Promise", "resolve", "reject", "fileReader", "FileReader", "addEventListener", "fileContent", "base64Content", "split", "Error", "stripBase64Prefix", "result", "error", "readAsDataURL", "adHocProfileClient", "DEFAULT_PROFILE_DATA", "useUploadFile", "isLoading", "setIsLoading", "useState", "profileData", "setProfileData", "useEffect", "abort", "removeFile", "useCallback", "processFile", "data", "isAbortError", "displayError", "message", "selectProfileType", "option", "value", "includes", "prevData", "options", "accept", "multiple", "onError", "toString", "AdHocFileDropZone", "onFileDropped", "onFileRemove", "onDropAccepted", "files", "FileDropzone", "flamegraph", "AdHocFlameGraph", "diff", "data-testid", "FlameGraph", "selectorC<PERSON>r", "AdHocProfileTypeSelector", "onChange", "useMemo", "map", "type", "label", "setOption", "_onChange", "o", "InlineFieldRow", "InlineField", "disabled", "length", "Select", "key", "width", "spinner", "AdHocSpinner", "Spinner", "size", "AdHocSingleComponent", "reportInteraction", "fileType", "AdHocSingle", "memo", "AdHocComparison", "tab<PERSON>ontent", "AdHocTabs", "activeTabIndex", "setActiveTabIndex", "TabsBar", "Tab", "active", "onChangeTab", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AdHocView", "Page<PERSON><PERSON>le", "title", "CompareTarget", "FlamegraphDotComApiClient", "HttpClient", "upload", "btoa", "fileTypeData", "units", "metadata", "spyName", "constructor", "super", "flamegraphDotComApiClient", "DATETIME_FORMATTER", "Intl", "DateTimeFormat", "year", "month", "day", "hour12", "hour", "minute", "second", "format", "date", "parts", "formatToParts", "reduce", "acc", "dateForExportFilename", "timeRange", "from", "Date", "Math", "round", "unix", "to", "getTimeRange", "target", "fromKey", "to<PERSON><PERSON>", "BASELINE", "searchParams", "URLSearchParams", "window", "location", "search", "raw", "dateTimeParse", "getExportFilename", "appName", "timeRanges", "COMPARISON", "join", "useExportMenu", "enableFlameGraphDotComExport", "uploadToFlamegraphDotCom", "customExportName", "dlLink", "document", "createElement", "href", "url", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "shouldDisplayFlamegraphDotCom", "Boolean", "actions", "downloadPng", "filename", "querySelector", "toBlob", "blob", "saveAs", "downloadJson", "dataStr", "encodeURIComponent", "ExportMenu", "props", "<PERSON><PERSON>", "<PERSON><PERSON>", "onClick", "ExportDataComponent", "Dropdown", "overlay", "<PERSON><PERSON>", "icon", "variant", "fill", "aria-label", "tooltip", "ExportData", "getNodes", "level", "names", "nodes", "itemOffset", "i", "push", "offset", "val", "self", "selfRight", "valRight", "valTotal", "offsetRight", "offsetTotal", "children", "FlameGraphComponent", "vertical", "collapsedFlamegraphs", "getExtraContextMenuButtons", "isLight", "useTheme2", "dataFrame", "levels", "unit", "nodeLevels", "node", "currentNodeStart", "slice", "n", "prevLevel", "prevLevelOffset", "prevLevelNode", "parentNodeStart", "parentNodeEnd", "stack", "labelValues", "levelValues", "self<PERSON><PERSON><PERSON>", "valueValues", "selfRightValues", "valueRightValues", "shift", "unshift", "valueUnit", "fields", "values", "FieldType", "string", "config", "frame", "meta", "preferredVisualisationType", "createDataFrame", "flamebearerToDataFrameDTO", "flamebearer", "GrafanaFlameGraph", "disableCollapsing", "extraHeaderElements", "getTheme", "createTheme", "colors", "mode", "keepFocusOnDataChange", "msgs", "context", "msg", "handheldBy", "logger", "getAppEvents", "publish", "AppEvents", "alertError", "payload", "displayWarning", "warn", "alertWarning", "displaySuccess", "alertSuccess", "PROFILES_EXPLORER_PAGE_NAME", "ROUTES", "EXPLORE", "getCurrentPage", "pathname", "URL", "pop", "getMetaProperties", "appRelease", "apps", "PYROSCOPE_APP_ID", "version", "appVersion", "GIT_COMMIT", "page", "view", "interactionName", "grafanaReportInteraction"], "sourceRoot": ""}