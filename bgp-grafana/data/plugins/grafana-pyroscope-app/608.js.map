{"version": 3, "file": "608.js", "mappings": "uKASA,MAAMA,EAAM,CAACC,EAAGC,IAAMD,EAAIC,EAAI,EAAID,EAAIC,GAAK,EAAI,EAEzCC,EAAMC,IAGNC,EAAeC,GAAOA,EAAIC,QAAQ,sBAAuB,QAGzDC,EAAa,aAEbC,EAAW,UAKXC,EAAe,CAAC,KAAM,CAAEC,SAAS,EAAMC,YAAa,SAEpDC,EAAY,CAACP,EAAKQ,EAAOC,IAAUT,EAAIC,QALzB,MAK8CO,GAAOP,QAJrD,MAI0EQ,GAExFC,EAAO,CAEZC,SAAS,EAETC,MAAO,KAGPC,WAAY,iBACZC,WAAY,aAGZC,WAAY,eAEZC,WAAY,qCAMZC,SAAU,EACVC,SAAU,EAGVC,WAAY,IACZC,SAAUvB,EAGVwB,WAAY,YACZC,SAAU,KAEVC,WAAY,iBAGZC,UAAW,EAGXC,WAAY,CAAC,EAAG5B,GAGhB6B,SAAU,KACVC,SAAU,KACVC,SAAU,KAIVC,UAAW,CAACC,EAAMC,EAAOC,KAAU,EAEnCC,QAASjC,GAAOA,EAAIkC,oBACpBC,QAASnC,GAAOA,EAAIoC,oBACpBC,QAAS,KAGTC,KAAM,CAACC,EAAMC,EAAUC,EAAQJ,EAAU3C,KACxC,IAAI,IACHgD,EAAG,MACHC,EAAK,MACLC,EAAK,UACLC,EAAS,UACTC,EAAS,MAGTC,EAAK,SACLzB,EAAQ,SACRF,EAAQ,MACR4B,GACGT,EAEJ,OAAOG,EAAIO,KAAI,CAACC,EAAGC,IAAMA,IAAGb,MAAK,CAACc,EAAIC,IAErCV,EAAMU,GAAMV,EAAMS,IAElB9B,EAAS8B,GAAM9B,EAAS+B,IAGtBT,EAAMS,GAAMR,EAAUQ,GAAM,GAAMP,EAAUO,IAC5CT,EAAMQ,GAAMP,EAAUO,GAAM,GAAMN,EAAUM,KAK9ChC,EAASgC,GAAMhC,EAASiC,IAExBN,EAAMK,GAAML,EAAMM,IAElBL,EAAMK,GAAML,EAAMI,IAElBf,EAAQG,EAASE,EAAIU,IAAMZ,EAASE,EAAIW,MACvC,GAIEC,EAAa,CAACX,EAAOY,IACjB,GAATA,EAAe,GACN,GAATA,EAAeZ,EAAQ,KACvBY,GAAS1D,EAAM8C,EAAQ,KACRA,EAAQ,MAAMY,MAGxBC,EAAW,YAEjB,SAASC,EAAOC,GACfA,EAAOC,OAAOC,OAAO,CAAC,EAAGlD,EAAMgD,GAE/B,IAAI,QACH/C,EAAO,SACPM,EAAQ,SACRC,EAAQ,UACRM,EAAS,WACTC,EAAU,SACVH,EAAQ,SACRI,EAAQ,SACRC,EAAQ,SACRC,EAAQ,WACRL,EACAT,WAAY+C,EACZhD,WAAYiD,EACZ9C,WAAY+C,EACZhD,WAAYiD,EAAW,WACvB3C,EAAU,QACVY,EAAO,QACPE,EAAO,QACPE,GACGqB,EAEJpC,IAAaE,EACbE,IAAaF,EACbG,IAAaH,EACbI,IAAaJ,EAEba,IAA2B,oBAAR4B,KAAsBvE,EAAM,IAAIuE,KAAKC,YAAY9D,GAAciC,QAElF,IAAIzB,EAAQ8C,EAAKS,SAAWT,EAAK9C,MAEjC,GAAa,MAATA,EAAe,CAClB,IAAIJ,EAAQyB,EAAQrB,GAChBH,EAAQ0B,EAAQvB,GAEpBkD,EAAcvD,EAAUuD,EAAatD,EAAOC,GAC5CoD,EAActD,EAAUsD,EAAarD,EAAOC,GAC5CuD,EAAczD,EAAUyD,EAAaxD,EAAOC,GAC5CsD,EAAcxD,EAAUwD,EAAavD,EAAOC,GAC5CY,EAAad,EAAUc,EAAYb,EAAOC,GAC1Cc,EAAahB,EAAUgB,EAAYf,EAAOC,EAC3C,CAEA,IAAI2D,EAAQzD,EAAU,IAAM,GAE5B,MAAM0D,EAAY,QACZC,EAAY,IAAIC,OAAOF,EAAW,KAAOD,GACzCI,EAAU,IAAID,OAAO,iBAAiBlD,MAAegD,KAAc,KAAOD,GAEhF,IAAI,WAAEK,GAAef,EAEH,MAAde,IACHA,EAAaC,IAEZ,IAAIC,EAAcjE,EAAKe,WACtBmD,EAAY,EACZC,EAAY,EACZC,EAAY,EACZC,EAAY,EAGb,GAAI,QAAQC,KAAKN,GAAI,CACpB,IAAIO,EAAOP,EAAEQ,OAGTD,GAAQ,EACPA,GAAQ,IAEXH,EAAYK,KAAKC,IAAIzD,EAAU,GAGnB,GAARsD,IACHL,EAAYO,KAAKC,IAAI9D,EAAU,MAMjCqD,EAAclD,EACdmD,EAAYtD,EACZuD,EAAYnD,EACZoD,EAAYnD,EACZoD,EAAYnD,EAEd,CAEA,MAAO,CACNH,WAAYkD,EACZrD,SAAUsD,EACVlD,SAAUmD,EACVlD,SAAUmD,EACVlD,SAAUmD,EACV,GAIH,IAAIM,IAAmBxB,EAEnB/C,EAAa,IAAIyD,OAAOV,EAAa,IAAMO,GAC3CvD,EAAa,IAAI0D,OAAOT,EAAa,IAAMM,GAE3CkB,EAAS,IAAIf,OAAO,IAAMT,EAAc,IAAMA,EAAc,IAAK,IAAMM,GACvEmB,EAAW,IAAIhB,OAAOhD,EAAY,KAAO6C,GAE7C,MAAMoB,EAAQ,CAAC/C,EAAQgD,GAAW,KACjC,IAAIC,EAAS,GAObjD,GALAA,EAASA,EAAOxC,QAAQqE,GAAWqB,IAClCD,EAAOE,KAAKD,GACLzF,MAGQD,QAAQqF,EAAQ,IAE3BG,IACJhD,EAASN,EAAQM,IAEd4C,IACH5C,EAASA,EAAOxC,QAAQa,GAAY6E,GAAKA,EAAE,GAAK,IAAMA,EAAE,MAEzD,IAAIE,EAAI,EACR,OAAOpD,EAAO+C,MAAM3E,GAAYiF,QAAOC,GAAU,IAALA,IAAS9C,KAAIC,GAAKA,IAAMhD,EAAawF,EAAOG,KAAO3C,GAAE,EAG5F8C,EAAkB,cAElBC,EAAY,CAACxD,EAAQyD,EAAO,EAAGC,GAAU,KAE9C,IAAIC,EAAQZ,EAAM/C,GAElB,GAAoB,GAAhB2D,EAAMlB,OACT,MAAO,GAGR,IAOImB,EAPAC,EAASC,MAAMH,EAAMlB,QAAQsB,KAAK,IAUtC,GATAJ,EAAQA,EAAMnD,KAAI,CAACyB,EAAG+B,IAAO/B,EAAEzE,QAAQsF,GAAUI,IAChDW,EAAOG,GAAMd,EACN,QAOS,GAAbnE,EACH6E,EAAQD,EAAMnD,KAAI,CAACyB,EAAG+B,KACrB,GAAa,MAAT/B,EAAE,GACL,OAAO3E,EAAa2E,EAAEgC,MAAM,GAAI,IAEjC,IAAIL,EAAQ,GAGZ,IAAK,IAAIV,KAAKjB,EAAEiC,SAASX,GAAkB,CAC1C,IAAItB,EAAIiB,EAAE,IAEN,WACHlE,EAAU,SACVH,EAAQ,SACRI,EAAQ,SACRC,EAAQ,SACRC,GACG6C,EAAWC,GAEf,GAAIpD,EAAWI,EAAWC,EAAWC,GAAY,EAChDyE,GAAS3B,EAAI4B,EAAOG,OAChB,CACJ,IAAKG,EAAQC,GAAUpF,EACnBqF,EAAUpC,EAAEgC,MAAM,EAAGE,GACrBG,EAAUrC,EAAEgC,MAAMG,GAElBlE,EAAQ+B,EAAEgC,MAAME,EAAQC,GAIZ,GAAZvF,GAAmC,GAAlBwF,EAAQ5B,QAAe4B,GAAWnE,EAAM,KAC5DmE,GAAW,MAAQA,EAAU,KAE9B,IAAIE,EAAWrE,EAAMuC,OAEjB+B,EAAW,CAACvC,GAGhB,GAAIhD,EACH,IAAK,IAAIyB,EAAI,EAAGA,EAAI6D,EAAU7D,IAC7B8D,EAASrB,KAAKkB,EAAUnE,EAAM+D,MAAM,EAAGvD,GAAK9B,EAAasB,EAAM+D,MAAMvD,EAAI,GAAK4D,GAIhF,GAAIpF,EACH,IAAK,IAAIwB,EAAI,EAAGA,EAAI6D,EAAW,EAAG7D,IAC7BR,EAAMQ,IAAMR,EAAMQ,EAAE,IACvB8D,EAASrB,KAAKkB,EAAUnE,EAAM+D,MAAM,EAAGvD,GAAKR,EAAMQ,EAAE,GAAKR,EAAMQ,GAAKR,EAAM+D,MAAMvD,EAAI,GAAK4D,GAK5F,GAAInF,EACH,IAAK,IAAIuB,EAAI,EAAGA,EAAI6D,EAAU7D,IAC7B8D,EAASrB,KAAKkB,EAAUnE,EAAM+D,MAAM,EAAGvD,EAAI,GAAK,IAAMR,EAAM+D,MAAMvD,EAAI,GAAK4D,GAI7E,GAAIzF,EAAU,CACb,IAAI4F,EAAc5D,EAAWjC,EAAY,GAEzC,IAAK,IAAI8B,EAAI,EAAGA,EAAI6D,EAAU7D,IAC7B8D,EAASrB,KAAKkB,EAAUnE,EAAM+D,MAAM,EAAGvD,GAAK+D,EAAcvE,EAAM+D,MAAMvD,GAAK4D,EAC7E,CAEAV,GAAS,MAAQY,EAASE,KAAK,KAAO,IAAMb,EAAOG,EACpD,CACD,CAIA,OAAOJ,CAAK,QAGT,CACJ,IAAIa,EAAc5D,EAAWjC,EAAYC,GAG7B,GAAR4E,GAAa5E,EAAW,IAG3B4F,EAAc,KAAOA,EAAc,MAGpCb,EAAQD,EAAMnD,KAAI,CAACyB,EAAG+B,IAAgB,MAAT/B,EAAE,GAAa3E,EAAa2E,EAAEgC,MAAM,GAAI,IAAOhC,EAAEc,MAAM,IAAIvC,KAAI,CAACmE,EAAGjE,EAAGR,KAGlF,GAAZrB,GAAsB,GAAL6B,GAAUR,EAAMuC,OAAS,GAAKkC,GAAKzE,EAAMQ,EAAE,KAC/DiE,GAAK,MAAQA,EAAI,KAEXA,KACLD,KAAKD,GAAeZ,EAAOG,IAC/B,CAMA,IAAIY,EAAqB,GAAZpG,EAAgBuC,EAAW,GACpC8D,EAAqB,GAAZpG,EAAgBsC,EAAW,GAEpC+D,EAAgBD,EAAShE,EAAWI,EAAKvC,WAAYuC,EAAKtC,UAAYiG,EAsB1E,OAnBInB,EAAO,EACNC,EAEHE,EAAQgB,EAAS,IAAMhB,EAAMc,KAAK,IAAMG,EAAS,IAAMD,EAAS,KAAO,IAAMC,GAK7EjB,EAAQ,IAAMA,EAAMc,KAAK,KAAOI,EAAgB,MAAQ,IACxDlB,EAAQ,OAASgB,EAAS,IAAMhB,EAAQ,IAAMiB,EAAS,QAIxDjB,EAAQA,EAAMc,KAAKI,GACnBlB,EAAQgB,EAAShB,EAAQiB,GAKnB,CAAC,IAAI/C,OAAO8B,EAAO,IAAMjC,GAAQgC,EAAOE,EAAO,EAGjDR,EAAS,CAACtD,EAAUC,EAAQ+E,KAEjC,IAAKC,GAASxB,EAAUxD,GAExB,GAAa,MAATgF,EACH,OAAO,KAER,IAAIC,EAAM,GAEV,GAAY,MAARF,EACH,IAAK,IAAIrE,EAAI,EAAGA,EAAIqE,EAAKtC,OAAQ/B,IAAK,CACrC,IAAIT,EAAM8E,EAAKrE,GACfsE,EAAMzC,KAAKxC,EAASE,KAASgF,EAAI9B,KAAKlD,EACvC,MAGA,IAAK,IAAIS,EAAI,EAAGA,EAAIX,EAAS0C,OAAQ/B,IACpCsE,EAAMzC,KAAKxC,EAASW,KAAOuE,EAAI9B,KAAKzC,GAGtC,OAAOuE,CAAG,EAGX,IAAIC,IAAmB5D,EAEnBhD,EAAa,IAAIwD,OAAOP,EAAaI,GACrCpD,EAAa,IAAIuD,OAAOR,EAAaK,GAEzC,MAAM7B,EAAO,CAACiF,EAAMhF,EAAUC,KAE7B,IAAKgF,EAAOrB,EAAOE,GAAUL,EAAUxD,EAAQ,GAC3CmF,EAAapC,EAAM/C,GAAQ,IAC1BoF,GAAU5B,EAAUxD,EAAQ,GAC7BqF,EAAW1B,EAAMlB,OAEjB6C,EAAcxB,MAAMuB,GACpBE,EAAczB,MAAMuB,GAExB,IAAK,IAAIjC,EAAI,EAAGA,EAAIiC,EAAUjC,IAAK,CAClC,IAAIoC,EAAY7B,EAAMP,GAClBqC,EAAYN,EAAW/B,GAEvB/D,EAA4B,KAAhBmG,EAAK,GAAiBA,EAAKvB,MAAM,GAAI,GAAUuB,EAAY3B,EAAOT,GAC9EsC,EAA4B,KAAhBD,EAAU,GAAYA,EAAUxB,MAAM,GAAI,GAAKwB,EAAY5B,EAAOT,GAElFkC,EAAOlC,GAAU/D,EACjBkG,EAAYnC,GAAKsC,CAClB,CAEA,IAAIC,EAAMZ,EAAKtC,OAEXmD,EAAQ9B,MAAM6B,GAAK5B,KAAK,GAExBjE,EAAO,CAEVG,IAAK6D,MAAM6B,GAGXrF,MAAOsF,EAAM3B,QAKb/D,MAAO0F,EAAM3B,QAGb1D,MAAOqF,EAAM3B,QAIb9D,MAAOyF,EAAM3B,QAGbtF,SAAUiH,EAAM3B,QAChBpF,SAAU+G,EAAM3B,QAGhB7D,UAAWwF,EAAM3B,QACjB4B,UAAWD,EAAM3B,QACjB5D,UAAWuF,EAAM3B,QACjB6B,UAAWF,EAAM3B,QAEjB8B,OAAQjC,MAAM6B,IAIXK,EAAyB,GAAZxH,GAA6B,GAAZC,EAE9BwH,EAAK,EAET,IAAK,IAAIvF,EAAI,EAAGA,EAAIqE,EAAKtC,OAAQ/B,IAAK,CACrC,IAAIwF,EAAQnG,EAASgF,EAAKrE,IAGtBwC,EAAIgD,EAAM5G,MAAM0F,GAGhB1E,EAAQ4C,EAAE3D,MAAQ2D,EAAE,GAAGT,OAEvB0D,EAAS7F,EAGT8F,GAAO,EACPC,EAAO,EACPC,EAAO,EACPC,EAAO,EACPC,EAAO,EACPtG,EAAQ,EACRC,EAAQ,EACRI,EAAQ,EACRkG,EAAQ,EACRC,EAAQ,EAERC,EAAS,GAEb,IAAK,IAAIvD,EAAI,EAAGwD,EAAI,EAAGxD,EAAIiC,EAAUjC,IAAKwD,GAAG,EAAG,CAC/C,IAAIC,EAAYnH,EAAQwD,EAAE0D,IACtBvH,EAAYiG,EAAOlC,GACnBsC,EAAYH,EAAYnC,GACxB0D,EAAYzH,EAAKoD,OACjBsE,EAAYF,EAAMpE,OAClBuE,EAAYH,GAASxH,EAOzB,GALI6D,EAAE0D,IAAMlB,GACXnF,KAIIyG,GAAa9D,EAAE0D,EAAE,GAAGnE,QAAUqE,EAAS,CAE3C,IAAIG,EAAQvH,EAAQwD,EAAE0D,EAAE,IAAIM,QAAQ7H,GAEhC4H,GAAS,IACZN,EAAOxD,KAAKgD,EAAQY,EAAUE,EAAOH,GACrCX,GAAUgB,EAAYjE,EAAG0D,EAAGK,EAAOH,GACnCD,EAAQxH,EACR0H,EAAWD,EACXE,GAAY,EAEH,GAAL5D,IACH9C,EAAQ6F,GAEX,CAEA,GAAIH,GAAcgB,EAAW,CAE5B,IAAII,EAAajB,EAAS,EACtBkB,EAAalB,EAASY,EAEtBO,GAAQ,EACRC,GAAQ,EAGZ,IAAmB,GAAfH,GAA8B9I,EAAWiE,KAAK2D,EAAMkB,IACvDJ,GAAaX,IACbiB,GAAQ,MAEJ,CACJ,GAAgB,GAAZ9I,EAAe,CAClB4H,GAAO,EACP,KACD,CAEA,GAAIlB,GAAkB3G,EAAWgE,KAAK2D,EAAMkB,GAAclB,EAAMkB,EAAa,IAC5EJ,GAAaV,IACbgB,GAAQ,OAGR,GAAgB,GAAZ9I,EAAe,CAElB,IAAIgJ,EAAOtE,EAAE0D,EAAE,GACXa,EAAUtB,EAASY,EAEvB,GAAIS,EAAK/E,QAAUqE,EAAS,CAC3B,IAIIY,EAJAT,EAAQ,EACRU,GAAQ,EACRC,EAAK,IAAI9F,OAAOzC,EAAM,KAAOsC,GAGjC,KAAO+F,EAAKE,EAAGC,KAAKL,IAAO,CAC1BP,EAAQS,EAAGnI,MAEX,IAAIuI,EAAUL,EAAUR,EACpBG,EAAaU,EAAU,EAE3B,IAAmB,GAAfV,GAAoB9I,EAAWiE,KAAK2D,EAAMkB,IAAc,CAC3Df,IACAsB,GAAQ,EACR,KACD,CACK,GAAIpJ,EAAWgE,KAAK2D,EAAMkB,GAAclB,EAAM4B,IAAW,CAC7DxB,IACAqB,GAAQ,EACR,KACD,CACD,CAEIA,IACHL,GAAQ,EAGRX,EAAOxD,KAAKgD,EAAQY,EAAUE,EAAOH,GACrCX,GAAUgB,EAAYjE,EAAG0D,EAAGK,EAAOH,GACnCD,EAAQxH,EACR0H,EAAWD,EACXE,GAAY,EAEH,GAAL5D,IACH9C,EAAQ6F,GAEX,CAEA,IAAKmB,EAAO,CACXlB,GAAO,EACP,KACD,CACD,CAEF,CAGA,GAAIiB,GAAcnB,EAAMzD,QAAUnE,EAAWiE,KAAK2D,EAAMmB,IACvDL,GAAaT,IACbgB,GAAQ,MAEJ,CACJ,GAAgB,GAAZ9I,EAAe,CAClB2H,GAAO,EACP,KACD,CAEA,GAAIlB,GAAkB3G,EAAWgE,KAAK2D,EAAMmB,EAAa,GAAKnB,EAAMmB,IACnEL,GAAaR,IACbe,GAAQ,OAGR,GAAgB,GAAZ9I,EAAe,CAClB2H,GAAO,EACP,KACD,CAEF,CAEIY,IACH9G,GAAS4G,EAELQ,GAASC,GACZpH,IAEH,CASA,GAPI4G,EAAWD,IACdJ,GAASK,EAAWD,GAEjB1D,EAAI,IACPqD,GAASvD,EAAE0D,EAAE,GAAGnE,SAGZxB,EAAK7B,UAAUC,EAAMwH,EAAOV,GAAS,CACzCC,GAAO,EACP,KACD,CAEIhD,EAAIiC,EAAW,IAClBc,GAAUY,EAAW7D,EAAE0D,EAAE,GAAGnE,OAC9B,CAEA,IAAK2D,EAAM,CACVtG,EAAKG,IAAIgG,GAAYlB,EAAKrE,GAC1BZ,EAAKM,UAAU6F,GAAMI,EACrBvG,EAAKO,UAAU4F,GAAMK,EACrBxG,EAAK+F,UAAUI,GAAMM,EACrBzG,EAAKgG,UAAUG,GAAMO,EACrB1G,EAAKI,MAAM+F,GAAU/F,EACrBJ,EAAKK,MAAM8F,GAAU9F,EACrBL,EAAKS,MAAM0F,GAAU1F,EACrBT,EAAKnB,SAASsH,GAAOQ,EACrB3G,EAAKjB,SAASoH,GAAOS,EAErB5G,EAAKQ,MAAM2F,GAAM3F,EAIjB,IAAI4C,EAAIgD,EAAM5G,MAAM8F,GAEhBe,EAASjD,EAAE3D,MAAQ2D,EAAE,GAAGT,OAExBsF,EAASpB,EAAOlE,OAChBuF,EAAKD,EAAS,EAAI,EAAI1K,IACtB4K,EAASF,EAAS,EAEtB,IAAK,IAAIrH,EAAI,EAAGA,EAAIwC,EAAET,QAAS,CAC9B,IAAIkD,EAAMzC,EAAExC,GAAG+B,OAEf,GAAIuF,GAAMC,GAAUtB,EAAOqB,IAAO7B,EAAQ,CACzC,IAAIY,EAAWJ,EAAOqB,EAAG,GACrBf,EAAWN,EAAOqB,EAAG,GACrBlB,EAAWH,EAAOqB,EAAG,GAGrB5E,EAAI1C,EACJD,EAAI,GACR,IAAK,IAAIyH,EAAO,EAAGA,EAAOnB,EAAU3D,IACnC3C,GAAKyC,EAAEE,GACP8E,GAAQhF,EAAEE,GAAGX,OAGdS,EAAEiF,OAAOzH,EAAG0C,EAAI1C,EAAGD,GAEnB0F,GAAUgB,EAAYjE,EAAGxC,EAAGuG,EAAOH,GAEnCkB,GAAM,CACP,MAEC7B,GAAUR,EACVjF,GAEF,CAEAyF,EAASjD,EAAE3D,MAAQ2D,EAAE,GAAGT,OAExB,IAAIsD,EAASjG,EAAKiG,OAAOE,GAAM,GAC3BmC,EAAOjC,EACPkC,EAAKlC,EAET,IAAK,IAAIzF,EAAI,EAAGA,EAAIwC,EAAET,OAAQ/B,IAAK,CAClC,IAAIiF,EAAMzC,EAAExC,GAAG+B,OAEf0D,GAAUR,EAENjF,EAAI,GAAK,EACZ2H,EAAKlC,EACGR,EAAM,IACdI,EAAO5C,KAAKiF,EAAMC,GAClBD,EAAOC,EAAKlC,EAEd,CAEIkC,EAAKD,GACRrC,EAAO5C,KAAKiF,EAAMC,GAEnBpC,GACD,CACD,CAGA,GAAIA,EAAKlB,EAAKtC,OACb,IAAK,IAAImE,KAAK9G,EACbA,EAAK8G,GAAK9G,EAAK8G,GAAG3C,MAAM,EAAGgC,GAG7B,OAAOnG,CAAI,EAGNqH,EAAc,CAACjE,EAAG0D,EAAG0B,EAAWxB,KAErC,IAAIyB,EAAUrF,EAAE0D,GAAK1D,EAAE0D,EAAE,GAAG3C,MAAM,EAAGqE,GAIrC,OAHApF,EAAE0D,EAAE,IAAM2B,EACVrF,EAAE0D,GAAQ1D,EAAE0D,EAAE,GAAG3C,MAAMqE,EAAWA,EAAYxB,GAC9C5D,EAAE0D,EAAE,GAAM1D,EAAE0D,EAAE,GAAG3C,MAAMqE,EAAYxB,GAC5ByB,EAAQ9F,MAAM,EAyJtB,MAAO,CACN+F,OAAQ,IAAIC,IApJG,EAAC1I,EAAUC,EAAQ0I,EAAYC,EAAa,IAAKC,KAChEF,EAAcA,GAAgC,IAAfA,EAJR,EAIgDA,EAA5C,EAE3B,IAAIG,EAAU,KACVC,EAAU,KAEVC,EAAO,GAEX/I,EAASA,EAAOxC,QAAQuE,GAASmB,IAChC,IAAI8F,EAAM9F,EAAE+F,OAAOhF,MAAM,GAOzB,OALA+E,EAAiB,MAAXA,EAAI,GAAa1L,EAAa0L,EAAI/E,MAAM,GAAG,IAAO+E,EAAIxL,QAAQE,EAAU,IAEnE,IAAPsL,GACHD,EAAK5F,KAAK6F,GAEJ,EAAE,IAGV,IAEIE,EAFA/I,EAAQ4C,EAAM/C,GAIlB,GAAI+I,EAAKtG,OAAS,GAGjB,GAFAyG,EAAS,IAAIpH,OAAOiH,EAAKrE,KAAK,KAAM,IAAM/C,GAEtB,GAAhBxB,EAAMsC,OAAa,CACtB,IAAIsC,EAAO,GAEX,IAAK,IAAIrE,EAAI,EAAGA,EAAIX,EAAS0C,OAAQ/B,IAC/BwI,EAAO3G,KAAKxC,EAASW,KACzBqE,EAAK5B,KAAKzC,GAGZ,MAAO,CAACqE,EAAM,KAAM,KACrB,OAIA,GAAoB,GAAhB5E,EAAMsC,OACT,MAAO,CAAC,KAAM,KAAM,MAMtB,GAAIiG,EAAa,EAAG,CAInB,IAAIvI,EAAQ4C,EAAM/C,GAElB,GAAIG,EAAMsC,OAAS,EAAG,CAErB,IAAI0G,EAAShJ,EAAM8D,QAAQpE,MAAK,CAAC3C,EAAGC,IAAMA,EAAEsF,OAASvF,EAAEuF,SAEvD,IAAK,IAAI2G,EAAK,EAAGA,EAAKD,EAAO1G,OAAQ2G,IAAM,CAE1C,GAA2B,GAAvBR,GAAanG,OAChB,MAAO,CAAC,GAAI,KAAM,MAEnBmG,EAAcvF,EAAOtD,EAAUoJ,EAAOC,GAAKR,EAC5C,CAIA,GAAIzI,EAAMsC,OAASiG,EAClB,MAAO,CAACE,EAAa,KAAM,MAE5BC,EAAUQ,EAAQlJ,GAAOK,KAAI8I,GAAQA,EAAK5E,KAAK,OAG/CoE,EAAU,GAGV,IAAIS,EAAc,IAAIC,IAEtB,IAAK,IAAIC,EAAK,EAAGA,EAAKZ,EAAQpG,OAAQgH,IACrC,GAAIF,EAAYG,KAAOd,EAAYnG,OAAQ,CAE1C,IAAIkH,EAAef,EAAYvF,QAAOpD,IAAQsJ,EAAYK,IAAI3J,KAE1D4J,EAAUxG,EAAOtD,EAAU8I,EAAQY,GAAKE,GAE5C,IAAK,IAAIvG,EAAI,EAAGA,EAAIyG,EAAQpH,OAAQW,IACnCmG,EAAYO,IAAID,EAAQzG,IAEzB0F,EAAQ3F,KAAK0G,EACd,MAECf,EAAQ3F,KAAK,GAEhB,CACD,CAMe,MAAX0F,IACHA,EAAU,CAAC7I,GACX8I,EAAU,CAACF,GAAanG,OAAS,EAAImG,EAAcvF,EAAOtD,EAAUC,KAGrE,IAAI+J,EAAU,KACVC,EAAW,KAQf,GANIjB,EAAKtG,OAAS,IACjBqG,EAAUA,EAAQtI,KAAIuE,GAAQA,EAAK1B,QAAOpD,IAAQiJ,EAAO3G,KAAKxC,EAASE,SAEvD6I,EAAQmB,QAAO,CAACC,EAAKnF,IAASmF,EAAMnF,EAAKtC,QAAQ,IAGhDkG,EAAY,CAC7BoB,EAAU,CAAC,EACXC,EAAW,GAEX,IAAK,IAAIP,EAAK,EAAGA,EAAKX,EAAQrG,OAAQgH,IAAM,CAC3C,IAAI1E,EAAO+D,EAAQW,GAEnB,GAAY,MAAR1E,GAA+B,GAAfA,EAAKtC,OACxB,SAED,IAAIzC,EAAS6I,EAAQY,GACjBU,EAAQrK,EAAKiF,EAAMhF,EAAUC,GAC7BoK,EAAQnJ,EAAKpB,KAAKsK,EAAOpK,EAAUC,EAAQJ,GAG/C,GAAI6J,EAAK,EACR,IAAK,IAAI/I,EAAI,EAAGA,EAAI0J,EAAM3H,OAAQ/B,IACjC0J,EAAM1J,IAAMsJ,EAASvH,OAGvB,IAAK,IAAImE,KAAKuD,EACbJ,EAAQnD,IAAMmD,EAAQnD,IAAM,IAAIyD,OAAOF,EAAMvD,IAE9CoD,EAAWA,EAASK,OAAOD,EAC5B,CACD,CAEA,MAAO,CACN,GAAGC,UAAUvB,GACbiB,EACAC,EACA,EAKUM,IAAW7B,GAGtB1F,QACAM,SACAvD,OACAD,KAAMoB,EAAKpB,KAEb,CAEA,MAAM0K,EAAW,MAChB,IAAIC,EAAU,CACbC,EAAG,SACHvN,EAAG,SACHwN,EAAG,QACHC,EAAG,QACHC,EAAG,QACHlK,EAAG,QACHmK,EAAG,QACHC,EAAG,QACHC,EAAG,SACHC,EAAG,SACHC,EAAG,MACHtG,EAAG,MACHuG,EAAG,IACHC,EAAG,IACHC,EAAG,KACHC,EAAG,KACHC,EAAG,KACHC,EAAG,KACHC,EAAG,KACHC,EAAG,MAGAC,EAAa,IAAIC,IACjBC,EAAa,GAEjB,IAAK,IAAIC,KAAKrB,EACbA,EAAQqB,GAAG9I,MAAM,IAAI+I,SAAQ5O,IAC5B0O,GAAc1O,EACdwO,EAAWK,IAAI7O,EAAG2O,EAAE,IAItB,IAAIG,EAAY,IAAIlK,OAAO,IAAI8J,KAAe,KAC1CK,EAAW/I,GAAKwI,EAAWQ,IAAIhJ,GAEnC,OAAOiJ,IACN,GAAsB,iBAAXA,EACV,OAAOA,EAAQ3O,QAAQwO,EAAWC,GAEnC,IAAIhH,EAAMnB,MAAMqI,EAAQ1J,QACxB,IAAK,IAAI/B,EAAI,EAAGA,EAAIyL,EAAQ1J,OAAQ/B,IACnCuE,EAAIvE,GAAKyL,EAAQzL,GAAGlD,QAAQwO,EAAWC,GACxC,OAAOhH,CAAG,CAEX,EA9CgB,GAiDjB,SAASoE,EAAQ+C,GAGhB,IAGQxF,EAAG3E,EAHPQ,GAFJ2J,EAAMA,EAAInI,SAEOxB,OAChB4J,EAAS,CAACD,EAAInI,SACdU,EAAI,IAAIb,MAAMrB,GAAQsB,KAAK,GAC3BrD,EAAI,EAEL,KAAOA,EAAI+B,GACNkC,EAAEjE,GAAKA,GACVkG,EAAIlG,EAAI,GAAKiE,EAAEjE,GACfuB,EAAImK,EAAI1L,GACR0L,EAAI1L,GAAK0L,EAAIxF,GACbwF,EAAIxF,GAAK3E,IACP0C,EAAEjE,GACJA,EAAI,EACJ2L,EAAOlJ,KAAKiJ,EAAInI,WAEhBU,EAAEjE,GAAK,IACLA,GAIJ,OAAO2L,CACR,CAEA,MAAMC,EAAQ,CAAC9G,EAAMqE,IAAYA,EAAU,SAASrE,WAAgBA,EAC9D+G,EAAU,CAACrC,EAAK1E,IAAS0E,EAAM1E,EAoBrCxE,EAAOuJ,SAAWA,EAClBvJ,EAAOqI,QAAU+C,GACL/C,EAAQ,IAAIvF,MAAMsI,EAAI3J,QAAQ+J,SAAS3M,MAAK,CAAC3C,EAAEC,KACzD,IAAK,IAAIuD,EAAI,EAAGA,EAAIxD,EAAEuF,OAAQ/B,IAC7B,GAAIxD,EAAEwD,IAAMvD,EAAEuD,GACb,OAAOxD,EAAEwD,GAAKvD,EAAEuD,GAElB,OAAO,CAAC,IAGGF,KAAIwD,GAAMA,EAAGxD,KAAIE,GAAK0L,EAAI1L,OAEvCM,EAAOyL,UA9BP,SAAmBlP,EAAKwI,EAAQ2G,EAAOJ,EAAOK,EAAQ,GAAIC,EAASL,GAClEI,EAAQC,EAAOD,EAAOD,EAAKnP,EAAIsP,UAAU,EAAG9G,EAAO,KAAK,KAAW4G,EAEnE,IAAK,IAAIjM,EAAI,EAAGA,EAAIqF,EAAOtD,OAAQ/B,GAAG,EAAG,CACxC,IAAIoM,EAAK/G,EAAOrF,GACZ2H,EAAKtC,EAAOrF,EAAE,GAElBiM,EAAQC,EAAOD,EAAOD,EAAKnP,EAAIsP,UAAUC,EAAIzE,IAAK,KAAUsE,EAExDjM,EAAIqF,EAAOtD,OAAS,IACvBkK,EAAQC,EAAOD,EAAOD,EAAKnP,EAAIsP,UAAU9G,EAAOrF,EAAE,GAAIqF,EAAOrF,EAAE,KAAK,KAAWiM,EACjF,CAIA,OAFAA,EAAQC,EAAOD,EAAOD,EAAKnP,EAAIsP,UAAU9G,EAAOA,EAAOtD,OAAS,KAAK,KAAWkK,CAGjF,E,cC3/BWI,EAAO,WAAc,EAmBzB,IAAIC,EAA8B,oBAAXC,OChB9B,QADgCD,EAAY,EAAAE,gBAAkB,EAAAC,UCC9D,IAAIC,EAAe,CACfC,EAAG,EACHC,EAAG,EACHC,MAAO,EACPC,OAAQ,EACRC,IAAK,EACLC,KAAM,EACNC,OAAQ,EACRC,MAAO,GAuBX,QAAeZ,QAA8C,IAA1BC,OAAOY,eArB1C,WACI,IAAIC,GAAK,IAAAC,UAAS,MAAOC,EAAUF,EAAG,GAAIG,EAAMH,EAAG,GAC/CI,GAAK,IAAAH,UAASX,GAAee,EAAOD,EAAG,GAAIE,EAAUF,EAAG,GACxDG,GAAW,IAAAC,UAAQ,WACnB,OAAO,IAAIrB,OAAOY,gBAAe,SAAUU,GACvC,GAAIA,EAAQ,GAAI,CACZ,IAAIT,EAAKS,EAAQ,GAAGC,YAAanB,EAAIS,EAAGT,EAAGC,EAAIQ,EAAGR,EAAGC,EAAQO,EAAGP,MAAOC,EAASM,EAAGN,OAAQiB,EAAQX,EAAGL,IAAKC,EAAOI,EAAGJ,KAAMC,EAASG,EAAGH,OAAQC,EAAQE,EAAGF,MAC1JQ,EAAQ,CAAEf,EAAGA,EAAGC,EAAGA,EAAGC,MAAOA,EAAOC,OAAQA,EAAQC,IAAKgB,EAAOf,KAAMA,EAAMC,OAAQA,EAAQC,MAAOA,GACvG,CACJ,GACJ,GAAG,IASH,OARA,GAA0B,WACtB,GAAKI,EAGL,OADAK,EAASK,QAAQV,GACV,WACHK,EAASM,YACb,CACJ,GAAG,CAACX,IACG,CAACC,EAAKE,EACjB,EAGM,WAAe,MAAO,CAACpB,EAAMK,EAAgB,E,cCpCnD,MAAMwB,EAAmB,GAAK3B,OAAO4B,iBAC/BC,EAAiB,GAAK7B,OAAO4B,iBAC7BE,EAAiB,GAAM9B,OAAO4B,iBAC9BG,EAAkB,GAAK/B,OAAO4B,iBAC9BI,EAAmB,GAAMhC,OAAO4B,iBAChCK,EAAwB,EAAIjC,OAAO4B,iBACnCM,EAAoB,EAAIlC,OAAO4B,iBAC/BO,EAAsB,EAAInC,OAAO4B,iBACjCQ,EAA0B,EAAIpC,OAAO4B,iBACrCS,EAAoB,EAAIrC,OAAO4B,iBAE/BU,EAAyB,ICRzBC,EAAwB,EAC5BC,OACAC,WACAC,kBACAC,cACAC,aACAC,iBACAC,gBACAC,kBACAC,oBACAC,sBACAC,6BACAC,aACAC,oBACAC,qBACAC,eACA/H,aA8FuB,IAAAgI,KAAI,MAAO,CAAE,cAAe,cAAeC,UAA0B,IAAAD,KAC1F,EAAAE,YACA,CACEC,gBA/FJ,WACE,MAAMC,GAA8C,MAA9BT,OAAqC,EAASA,EAA2BT,EAAUD,EAAKA,KAAM,CAClHc,eACAM,OAAQpB,EAAKqB,mBACbtI,SACAsH,qBACK,GACP,OAAuB,IAAAiB,MAAK,EAAAC,SAAU,CAAEP,SAAU,EAChC,IAAAD,KACd,EAAAS,SACA,CACEC,MAAO,cACPC,KAAM,MACNC,QAAS,KACPxB,IACAD,GAAiB,KAIP,IAAAa,KACd,EAAAS,SACA,CACEC,MAAO,qBACPC,KAAM,OACNC,QAAS,KACPC,UAAUC,UAAUC,UAAU7B,EAASwB,OAAOM,MAAK,KACjD7B,GAAiB,GACjB,KAIQ,IAAAa,KACd,EAAAS,SACA,CACEC,MAAO,gBACPC,KAAM,kBACNC,QAAS,KACPvB,IACAF,GAAiB,IAIvBiB,EAAapQ,KAAI,EAAG0Q,QAAOC,OAAMC,cACR,IAAAZ,KAAI,EAAAS,SAAU,CAAEC,QAAOC,OAAMC,QAAS,IAAMA,KAAaF,KAElFd,IAA8B,IAAAW,MAAK,EAAAU,UAAW,CAAEP,MAAO,WAAYT,SAAU,CAC3EX,EAAiBA,EAAe4B,WAA4B,IAAAlB,KAC1D,EAAAS,SACA,CACEC,MAAO,eACPC,KAAM,oBACNC,QAAS,KACPrB,IACAJ,GAAiB,KAGH,IAAAa,KAClB,EAAAS,SACA,CACEC,MAAO,iBACPC,KAAM,kBACNC,QAAS,KACPpB,IACAL,GAAiB,IAGnB,MACHU,IAAqC,IAAAG,KACpC,EAAAS,SACA,CACEC,MAAO,oBACPC,KAAM,oBACNC,QAAS,KACPnB,IACAN,GAAiB,KAItBW,IAAsC,IAAAE,KACrC,EAAAS,SACA,CACEC,MAAO,sBACPC,KAAM,kBACNC,QAAS,KACPlB,IACAP,GAAiB,SAM7B,EAKItC,EAAGqC,EAASiC,KAAO,GACnBrE,EAAGoC,EAASkC,KACZC,aAAa,M,cClHnB,MAAMC,EAAoB,EAAGrC,OAAMsC,OAAMC,aAAYC,WAAUnC,qBAC7D,MAAMoC,GAAS,IAAAC,YAAWC,GAC1B,IAAML,IAAQE,EACZ,OAAO,KAET,IAAII,EACJ,GAAI5C,EAAKqB,mBAAoB,CAC3B,MAAMwB,EAAYC,EAAmB9C,EAAMsC,EAAMC,GACjDK,GAA0B,IAAA7B,KACxB,EAAAgC,iBACA,CACEC,UAAWP,EAAOQ,aAClBC,QAAS,CACP,CAAEC,GAAI,QAASC,OAAQ,IACvB,CAAED,GAAI,WAAYC,OAAQ,YAC1B,CAAED,GAAI,aAAcC,OAAQ,cAC5B,CAAED,GAAI,OAAQC,OAAQ,SAExBpD,KAAM6C,EACNQ,SAAWC,GAAgBA,EAAYC,OAG7C,KAAO,CACL,MAAMC,EAAcC,EAAezD,EAAMsC,EAAMC,GAC/CK,GAA0B,IAAAtB,MAAK,IAAK,CAAE0B,UAAWP,EAAOiB,cAAe1C,SAAU,CAC/EwC,EAAYG,WACI,IAAA5C,KAAI,KAAM,CAAC,GAC3B,WACgB,IAAAA,KAAI,IAAK,CAAEC,SAAUwC,EAAYI,YACjD,KACAJ,EAAYK,aACZ,MACgB,IAAA9C,KAAI,KAAM,CAAC,GAC3B,UACgB,IAAAA,KAAI,IAAK,CAAEC,SAAUwC,EAAYM,WACjD,KACAN,EAAYO,YACZ,MACgB,IAAAhD,KAAI,KAAM,CAAC,GAC3B,aACgB,IAAAA,KAAI,IAAK,CAAEC,SAAUwC,EAAYQ,YAErD,CACA,OAAuB,IAAAjD,KAAI,EAAAkD,OAAQ,CAAEjD,UAA0B,IAAAD,KAAI,EAAAmD,oBAAqB,CAAElB,UAAWP,EAAO0B,iBAAkB3B,WAAU4B,OAAQ,CAAExG,EAAG,GAAIC,EAAG,GAAKmD,UAA0B,IAAAM,MAAK,MAAO,CAAE0B,UAAWP,EAAO4B,eAAgBrD,SAAU,EACnO,IAAAM,MAAK,IAAK,CAAE0B,UAAWP,EAAO6B,YAAatD,SAAU,CACnEhB,EAAKuE,SAASjC,EAAKkC,YAAY,IAC/BnE,GAAkBA,EAAe4B,WAA4B,IAAAX,MAAK,OAAQ,CAAEN,SAAU,EACpE,IAAAD,KAAI,KAAM,CAAC,GAC3B,OACAV,EAAeoE,MAAMzR,OACrB,oBACK,MAET4P,QACQ,EAENa,EAAiB,CAACzD,EAAMsC,EAAMC,KAClC,MAAMmC,EAAe1E,EAAK2E,sBAAsBrC,EAAKsC,OAC/CC,EAAc7E,EAAK8E,eAAexC,EAAKkC,aACvCX,EAAe5Q,KAAK8R,MAAaL,EAAavW,QAAUoU,EAA9B,KAA6C,IACvEwB,EAAc9Q,KAAK8R,MAAaF,EAAY1W,QAAUoU,EAA7B,KAA4C,IAC3E,IAAIqB,EAAYc,EAAaM,KAAON,EAAaO,OAC7CnB,EAAWe,EAAYG,KAAOH,EAAYI,OAC9C,MAAMtB,EAAY3D,EAAKkF,eASvB,MARkB,UAAdvB,IACGe,EAAaO,SAChBrB,EAAYc,EAAaM,MAEtBH,EAAYI,SACfnB,EAAWe,EAAYG,OAGpB,CACLnB,eACAE,cACAJ,YACAC,YACAE,WACAE,QAASU,EAAavW,QAAQgX,iBAC/B,EAEGrC,EAAqB,CAAC9C,EAAMsC,EAAMC,KACtC,MACM6C,EADSpF,EAAKqF,YACW,GAAG,GAAGC,WAC/BC,EAAiBhD,EAAa6C,EAC9BI,EAAYlD,EAAKsC,MAAQtC,EAAKgD,WAC9BG,EAAiBxS,KAAK8R,MAAM,IAAMS,EAAYD,GAAkB,IAChEG,EAAkBzS,KAAK8R,MAAM,IAAMzC,EAAKgD,WAAaF,GAAmB,IACxEO,GAAQD,EAAkBD,GAAkBA,EAAiB,IAC7DG,EAAmBC,EAAiB7F,EAAMA,EAAK2E,sBAAsBa,IACrEM,EAAoBD,EAAiB7F,EAAMA,EAAK2E,sBAAsBrC,EAAKgD,aAC3ES,GAAiB,IAAAC,gBAAe,SACtC,MAAO,CACL,CACEzC,MAAO,IACP9B,MAAO,aACPwE,SAAUR,EAAiB,IAC3BS,WAAYR,EAAkB,IAC9BC,KAAMI,EAAeJ,GAAMX,KAAO,KAEpC,CACEzB,MAAO,IACP9B,MAAO,QACPwE,SAAUL,EACVM,WAAYJ,EACZH,KAAME,EAAiB7F,EAAMA,EAAK2E,sBAAsBrC,EAAKgD,WAAaE,KAE5E,CACEjC,MAAO,IACP9B,MAAO,UACPwE,SAAUF,EAAeP,GAAWR,KACpCkB,WAAYH,EAAezD,EAAKgD,YAAYN,KAC5CW,KAAMI,EAAezD,EAAKgD,WAAaE,GAAWR,MAErD,EAEH,SAASa,EAAiB7F,EAAM0E,GAC9B,IAAId,EAAYc,EAAaM,KAAON,EAAaO,OAOjD,MALkB,UADAjF,EAAKkF,iBAEhBR,EAAaO,SAChBrB,EAAYc,EAAaM,OAGtBpB,CACT,CACA,MAAMjB,EAAawD,IAAU,CAC3BhC,kBAAkB,IAAAiC,KAAI,CACpBC,MAAO,mBACPC,SAAU,WAEZjC,gBAAgB,IAAA+B,KAAI,CAClBC,MAAO,iBACPE,SAAUJ,EAAMK,WAAWC,UAAUF,SACrCzI,MAAO,SAETwG,aAAa,IAAA8B,KAAI,CACfC,MAAO,cACPK,UAAW,EACXC,UAAW,cAEbjD,eAAe,IAAA0C,KAAI,CACjBC,MAAO,gBACPO,aAAc,IAEhBC,MAAM,IAAAT,KAAI,CACRC,MAAO,OACPO,aAAc,SAEhB3D,cAAc,IAAAmD,KAAI,CAChBC,MAAO,eACPS,SAAU,YC3Jd,SAASC,EAAQC,GAGf,OAAOD,EAAU,mBAAqBE,QAAU,iBAAmBA,OAAOC,SAAW,SAAUF,GAC7F,cAAcA,CAChB,EAAI,SAAUA,GACZ,OAAOA,GAAO,mBAAqBC,QAAUD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,CAC1H,EAAGD,EAAQC,EACb,CAKA,IAAIK,EAAW,OACXC,EAAY,OAChB,SAASC,EAAUC,EAAOhW,GAKxB,GAHAA,EAAOA,GAAQ,CAAC,GADhBgW,EAAQA,GAAgB,cAIHD,EACnB,OAAOC,EAGT,KAAMC,gBAAgBF,GACpB,OAAO,IAAIA,EAAUC,EAAOhW,GAE9B,IAAIkW,EAmRN,SAAoBF,GAClB,IAAIE,EAAM,CACRtL,EAAG,EACHuL,EAAG,EACHja,EAAG,GAEDD,EAAI,EACJqO,EAAI,KACJ9K,EAAI,KACJ0K,EAAI,KACJkM,GAAK,EACLC,GAAS,EACO,iBAATL,IACTA,EAmuBJ,SAA6BA,GAC3BA,EAAQA,EAAMzZ,QAAQsZ,EAAU,IAAItZ,QAAQuZ,EAAW,IAAIQ,cAC3D,IAkBIjY,EAlBAkY,GAAQ,EACZ,GAAIC,GAAMR,GACRA,EAAQQ,GAAMR,GACdO,GAAQ,OACH,GAAa,eAATP,EACT,MAAO,CACLpL,EAAG,EACHuL,EAAG,EACHja,EAAG,EACHD,EAAG,EACHoa,OAAQ,QASZ,GAAIhY,EAAQoY,GAASP,IAAItP,KAAKoP,GAC5B,MAAO,CACLpL,EAAGvM,EAAM,GACT8X,EAAG9X,EAAM,GACTnC,EAAGmC,EAAM,IAGb,GAAIA,EAAQoY,GAASC,KAAK9P,KAAKoP,GAC7B,MAAO,CACLpL,EAAGvM,EAAM,GACT8X,EAAG9X,EAAM,GACTnC,EAAGmC,EAAM,GACTpC,EAAGoC,EAAM,IAGb,GAAIA,EAAQoY,GAASE,IAAI/P,KAAKoP,GAC5B,MAAO,CACLY,EAAGvY,EAAM,GACTiM,EAAGjM,EAAM,GACT6L,EAAG7L,EAAM,IAGb,GAAIA,EAAQoY,GAASI,KAAKjQ,KAAKoP,GAC7B,MAAO,CACLY,EAAGvY,EAAM,GACTiM,EAAGjM,EAAM,GACT6L,EAAG7L,EAAM,GACTpC,EAAGoC,EAAM,IAGb,GAAIA,EAAQoY,GAASK,IAAIlQ,KAAKoP,GAC5B,MAAO,CACLY,EAAGvY,EAAM,GACTiM,EAAGjM,EAAM,GACTmB,EAAGnB,EAAM,IAGb,GAAIA,EAAQoY,GAASM,KAAKnQ,KAAKoP,GAC7B,MAAO,CACLY,EAAGvY,EAAM,GACTiM,EAAGjM,EAAM,GACTmB,EAAGnB,EAAM,GACTpC,EAAGoC,EAAM,IAGb,GAAIA,EAAQoY,GAASO,KAAKpQ,KAAKoP,GAC7B,MAAO,CACLpL,EAAGqM,GAAgB5Y,EAAM,IACzB8X,EAAGc,GAAgB5Y,EAAM,IACzBnC,EAAG+a,GAAgB5Y,EAAM,IACzBpC,EAAGib,GAAoB7Y,EAAM,IAC7BgY,OAAQE,EAAQ,OAAS,QAG7B,GAAIlY,EAAQoY,GAASU,KAAKvQ,KAAKoP,GAC7B,MAAO,CACLpL,EAAGqM,GAAgB5Y,EAAM,IACzB8X,EAAGc,GAAgB5Y,EAAM,IACzBnC,EAAG+a,GAAgB5Y,EAAM,IACzBgY,OAAQE,EAAQ,OAAS,OAG7B,GAAIlY,EAAQoY,GAASW,KAAKxQ,KAAKoP,GAC7B,MAAO,CACLpL,EAAGqM,GAAgB5Y,EAAM,GAAK,GAAKA,EAAM,IACzC8X,EAAGc,GAAgB5Y,EAAM,GAAK,GAAKA,EAAM,IACzCnC,EAAG+a,GAAgB5Y,EAAM,GAAK,GAAKA,EAAM,IACzCpC,EAAGib,GAAoB7Y,EAAM,GAAK,GAAKA,EAAM,IAC7CgY,OAAQE,EAAQ,OAAS,QAG7B,GAAIlY,EAAQoY,GAASY,KAAKzQ,KAAKoP,GAC7B,MAAO,CACLpL,EAAGqM,GAAgB5Y,EAAM,GAAK,GAAKA,EAAM,IACzC8X,EAAGc,GAAgB5Y,EAAM,GAAK,GAAKA,EAAM,IACzCnC,EAAG+a,GAAgB5Y,EAAM,GAAK,GAAKA,EAAM,IACzCgY,OAAQE,EAAQ,OAAS,OAG7B,OAAO,CACT,CAx0BYe,CAAoBtB,IAER,UAAlBT,EAAQS,KACNuB,GAAevB,EAAMpL,IAAM2M,GAAevB,EAAMG,IAAMoB,GAAevB,EAAM9Z,IA2CjE0O,EA1CGoL,EAAMpL,EA0CNuL,EA1CSH,EAAMG,EA0CZja,EA1Ce8Z,EAAM9Z,EAAvCga,EA2CG,CACLtL,EAAqB,IAAlB4M,GAAQ5M,EAAG,KACduL,EAAqB,IAAlBqB,GAAQrB,EAAG,KACdja,EAAqB,IAAlBsb,GAAQtb,EAAG,MA7CZka,GAAK,EACLC,EAAwC,MAA/BoB,OAAOzB,EAAMpL,GAAG8M,QAAQ,GAAa,OAAS,OAC9CH,GAAevB,EAAMY,IAAMW,GAAevB,EAAM1L,IAAMiN,GAAevB,EAAMxW,IACpF8K,EAAIqN,GAAoB3B,EAAM1L,GAC9B9K,EAAImY,GAAoB3B,EAAMxW,GAC9B0W,EA6JN,SAAkBU,EAAGtM,EAAG9K,GACtBoX,EAAsB,EAAlBY,GAAQZ,EAAG,KACftM,EAAIkN,GAAQlN,EAAG,KACf9K,EAAIgY,GAAQhY,EAAG,KACf,IAAIC,EAAIgC,KAAKmW,MAAMhB,GACjBiB,EAAIjB,EAAInX,EACRuB,EAAIxB,GAAK,EAAI8K,GACbwN,EAAItY,GAAK,EAAIqY,EAAIvN,GACjBjI,EAAI7C,GAAK,GAAK,EAAIqY,GAAKvN,GACvByN,EAAMtY,EAAI,EACVmL,EAAI,CAACpL,EAAGsY,EAAG9W,EAAGA,EAAGqB,EAAG7C,GAAGuY,GACvB5B,EAAI,CAAC9T,EAAG7C,EAAGA,EAAGsY,EAAG9W,EAAGA,GAAG+W,GACvB7b,EAAI,CAAC8E,EAAGA,EAAGqB,EAAG7C,EAAGA,EAAGsY,GAAGC,GACzB,MAAO,CACLnN,EAAO,IAAJA,EACHuL,EAAO,IAAJA,EACHja,EAAO,IAAJA,EAEP,CA/KY8b,CAAShC,EAAMY,EAAGtM,EAAG9K,GAC3B4W,GAAK,EACLC,EAAS,OACAkB,GAAevB,EAAMY,IAAMW,GAAevB,EAAM1L,IAAMiN,GAAevB,EAAM9L,KACpFI,EAAIqN,GAAoB3B,EAAM1L,GAC9BJ,EAAIyN,GAAoB3B,EAAM9L,GAC9BgM,EAgFN,SAAkBU,EAAGtM,EAAGJ,GACtB,IAAIU,EAAGuL,EAAGja,EAIV,SAAS+b,EAAQjX,EAAG8W,EAAGzV,GAGrB,OAFIA,EAAI,IAAGA,GAAK,GACZA,EAAI,IAAGA,GAAK,GACZA,EAAI,EAAI,EAAUrB,EAAc,GAAT8W,EAAI9W,GAASqB,EACpCA,EAAI,GAAcyV,EAClBzV,EAAI,EAAI,EAAUrB,GAAK8W,EAAI9W,IAAM,EAAI,EAAIqB,GAAK,EAC3CrB,CACT,CACA,GAXA4V,EAAIY,GAAQZ,EAAG,KACftM,EAAIkN,GAAQlN,EAAG,KACfJ,EAAIsN,GAAQtN,EAAG,KASL,IAANI,EACFM,EAAIuL,EAAIja,EAAIgO,MACP,CACL,IAAI4N,EAAI5N,EAAI,GAAMA,GAAK,EAAII,GAAKJ,EAAII,EAAIJ,EAAII,EACxCtJ,EAAI,EAAIkJ,EAAI4N,EAChBlN,EAAIqN,EAAQjX,EAAG8W,EAAGlB,EAAI,EAAI,GAC1BT,EAAI8B,EAAQjX,EAAG8W,EAAGlB,GAClB1a,EAAI+b,EAAQjX,EAAG8W,EAAGlB,EAAI,EAAI,EAC5B,CACA,MAAO,CACLhM,EAAO,IAAJA,EACHuL,EAAO,IAAJA,EACHja,EAAO,IAAJA,EAEP,CA3GYgc,CAASlC,EAAMY,EAAGtM,EAAGJ,GAC3BkM,GAAK,EACLC,EAAS,OAEPL,EAAMmC,eAAe,OACvBlc,EAAI+Z,EAAM/Z,IAyBhB,IAAkB2O,EAAGuL,EAAGja,EArBtB,OADAD,EAAImc,GAAWnc,GACR,CACLma,GAAIA,EACJC,OAAQL,EAAMK,QAAUA,EACxBzL,EAAGnJ,KAAKC,IAAI,IAAKD,KAAK4W,IAAInC,EAAItL,EAAG,IACjCuL,EAAG1U,KAAKC,IAAI,IAAKD,KAAK4W,IAAInC,EAAIC,EAAG,IACjCja,EAAGuF,KAAKC,IAAI,IAAKD,KAAK4W,IAAInC,EAAIha,EAAG,IACjCD,EAAGA,EAEP,CAjUYqc,CAAWtC,GACrBC,KAAKsC,eAAiBvC,EAAOC,KAAKuC,GAAKtC,EAAItL,EAAGqL,KAAKwC,GAAKvC,EAAIC,EAAGF,KAAKhJ,GAAKiJ,EAAIha,EAAG+Z,KAAKpJ,GAAKqJ,EAAIja,EAAGga,KAAKyC,QAAUjX,KAAK8R,MAAM,IAAM0C,KAAKpJ,IAAM,IAAKoJ,KAAK0C,QAAU3Y,EAAKqW,QAAUH,EAAIG,OACnLJ,KAAK2C,cAAgB5Y,EAAK6Y,aAMtB5C,KAAKuC,GAAK,IAAGvC,KAAKuC,GAAK/W,KAAK8R,MAAM0C,KAAKuC,KACvCvC,KAAKwC,GAAK,IAAGxC,KAAKwC,GAAKhX,KAAK8R,MAAM0C,KAAKwC,KACvCxC,KAAKhJ,GAAK,IAAGgJ,KAAKhJ,GAAKxL,KAAK8R,MAAM0C,KAAKhJ,KAC3CgJ,KAAK6C,IAAM5C,EAAIE,EACjB,CA8UA,SAAS2C,EAASnO,EAAGuL,EAAGja,GACtB0O,EAAI4M,GAAQ5M,EAAG,KACfuL,EAAIqB,GAAQrB,EAAG,KACfja,EAAIsb,GAAQtb,EAAG,KACf,IAEI0a,EACFtM,EAHE+N,EAAM5W,KAAK4W,IAAIzN,EAAGuL,EAAGja,GACvBwF,EAAMD,KAAKC,IAAIkJ,EAAGuL,EAAGja,GAGrBgO,GAAKmO,EAAM3W,GAAO,EACpB,GAAI2W,GAAO3W,EACTkV,EAAItM,EAAI,MACH,CACL,IAAI0O,EAAIX,EAAM3W,EAEd,OADA4I,EAAIJ,EAAI,GAAM8O,GAAK,EAAIX,EAAM3W,GAAOsX,GAAKX,EAAM3W,GACvC2W,GACN,KAAKzN,EACHgM,GAAKT,EAAIja,GAAK8c,GAAK7C,EAAIja,EAAI,EAAI,GAC/B,MACF,KAAKia,EACHS,GAAK1a,EAAI0O,GAAKoO,EAAI,EAClB,MACF,KAAK9c,EACH0a,GAAKhM,EAAIuL,GAAK6C,EAAI,EAGtBpC,GAAK,CACP,CACA,MAAO,CACLA,EAAGA,EACHtM,EAAGA,EACHJ,EAAGA,EAEP,CAuCA,SAAS+O,EAASrO,EAAGuL,EAAGja,GACtB0O,EAAI4M,GAAQ5M,EAAG,KACfuL,EAAIqB,GAAQrB,EAAG,KACfja,EAAIsb,GAAQtb,EAAG,KACf,IAEI0a,EACFtM,EAHE+N,EAAM5W,KAAK4W,IAAIzN,EAAGuL,EAAGja,GACvBwF,EAAMD,KAAKC,IAAIkJ,EAAGuL,EAAGja,GAGrBsD,EAAI6Y,EACFW,EAAIX,EAAM3W,EAEd,GADA4I,EAAY,IAAR+N,EAAY,EAAIW,EAAIX,EACpBA,GAAO3W,EACTkV,EAAI,MACC,CACL,OAAQyB,GACN,KAAKzN,EACHgM,GAAKT,EAAIja,GAAK8c,GAAK7C,EAAIja,EAAI,EAAI,GAC/B,MACF,KAAKia,EACHS,GAAK1a,EAAI0O,GAAKoO,EAAI,EAClB,MACF,KAAK9c,EACH0a,GAAKhM,EAAIuL,GAAK6C,EAAI,EAGtBpC,GAAK,CACP,CACA,MAAO,CACLA,EAAGA,EACHtM,EAAGA,EACH9K,EAAGA,EAEP,CA8BA,SAAS0Z,EAAStO,EAAGuL,EAAGja,EAAGid,GACzB,IAAIC,EAAM,CAACC,GAAK5X,KAAK8R,MAAM3I,GAAG0O,SAAS,KAAMD,GAAK5X,KAAK8R,MAAM4C,GAAGmD,SAAS,KAAMD,GAAK5X,KAAK8R,MAAMrX,GAAGod,SAAS,MAG3G,OAAIH,GAAcC,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,GAC3HH,EAAI,GAAGG,OAAO,GAAKH,EAAI,GAAGG,OAAO,GAAKH,EAAI,GAAGG,OAAO,GAEtDH,EAAI3V,KAAK,GAClB,CAmBA,SAAS+V,EAAc5O,EAAGuL,EAAGja,EAAGD,GAE9B,MADU,CAACod,GAAKI,GAAoBxd,IAAKod,GAAK5X,KAAK8R,MAAM3I,GAAG0O,SAAS,KAAMD,GAAK5X,KAAK8R,MAAM4C,GAAGmD,SAAS,KAAMD,GAAK5X,KAAK8R,MAAMrX,GAAGod,SAAS,MAC9H7V,KAAK,GAClB,CAqBA,SAASiW,EAAY1D,EAAO2D,GAC1BA,EAAoB,IAAXA,EAAe,EAAIA,GAAU,GACtC,IAAIhD,EAAMZ,EAAUC,GAAO4D,QAG3B,OAFAjD,EAAIrM,GAAKqP,EAAS,IAClBhD,EAAIrM,EAAIuP,GAAQlD,EAAIrM,GACbyL,EAAUY,EACnB,CACA,SAASmD,GAAU9D,EAAO2D,GACxBA,EAAoB,IAAXA,EAAe,EAAIA,GAAU,GACtC,IAAIhD,EAAMZ,EAAUC,GAAO4D,QAG3B,OAFAjD,EAAIrM,GAAKqP,EAAS,IAClBhD,EAAIrM,EAAIuP,GAAQlD,EAAIrM,GACbyL,EAAUY,EACnB,CACA,SAASoD,GAAW/D,GAClB,OAAOD,EAAUC,GAAOgE,WAAW,IACrC,CACA,SAASC,GAASjE,EAAO2D,GACvBA,EAAoB,IAAXA,EAAe,EAAIA,GAAU,GACtC,IAAIhD,EAAMZ,EAAUC,GAAO4D,QAG3B,OAFAjD,EAAIzM,GAAKyP,EAAS,IAClBhD,EAAIzM,EAAI2P,GAAQlD,EAAIzM,GACb6L,EAAUY,EACnB,CACA,SAASuD,GAAUlE,EAAO2D,GACxBA,EAAoB,IAAXA,EAAe,EAAIA,GAAU,GACtC,IAAIzD,EAAMH,EAAUC,GAAOmE,QAI3B,OAHAjE,EAAItL,EAAInJ,KAAK4W,IAAI,EAAG5W,KAAKC,IAAI,IAAKwU,EAAItL,EAAInJ,KAAK8R,OAAcoG,EAAS,IAAjB,OACrDzD,EAAIC,EAAI1U,KAAK4W,IAAI,EAAG5W,KAAKC,IAAI,IAAKwU,EAAIC,EAAI1U,KAAK8R,OAAcoG,EAAS,IAAjB,OACrDzD,EAAIha,EAAIuF,KAAK4W,IAAI,EAAG5W,KAAKC,IAAI,IAAKwU,EAAIha,EAAIuF,KAAK8R,OAAcoG,EAAS,IAAjB,OAC9C5D,EAAUG,EACnB,CACA,SAASkE,GAAQpE,EAAO2D,GACtBA,EAAoB,IAAXA,EAAe,EAAIA,GAAU,GACtC,IAAIhD,EAAMZ,EAAUC,GAAO4D,QAG3B,OAFAjD,EAAIzM,GAAKyP,EAAS,IAClBhD,EAAIzM,EAAI2P,GAAQlD,EAAIzM,GACb6L,EAAUY,EACnB,CAIA,SAAS0D,GAAMrE,EAAO2D,GACpB,IAAIhD,EAAMZ,EAAUC,GAAO4D,QACvBU,GAAO3D,EAAIC,EAAI+C,GAAU,IAE7B,OADAhD,EAAIC,EAAI0D,EAAM,EAAI,IAAMA,EAAMA,EACvBvE,EAAUY,EACnB,CAOA,SAAS4D,GAAYvE,GACnB,IAAIW,EAAMZ,EAAUC,GAAO4D,QAE3B,OADAjD,EAAIC,GAAKD,EAAIC,EAAI,KAAO,IACjBb,EAAUY,EACnB,CACA,SAAS6D,GAAOxE,EAAOyE,GACrB,GAAIC,MAAMD,IAAWA,GAAU,EAC7B,MAAM,IAAIE,MAAM,gDAKlB,IAHA,IAAIhE,EAAMZ,EAAUC,GAAO4D,QACvBxO,EAAS,CAAC2K,EAAUC,IACpB4E,EAAO,IAAMH,EACRhb,EAAI,EAAGA,EAAIgb,EAAQhb,IAC1B2L,EAAOlJ,KAAK6T,EAAU,CACpBa,GAAID,EAAIC,EAAInX,EAAImb,GAAQ,IACxBtQ,EAAGqM,EAAIrM,EACPJ,EAAGyM,EAAIzM,KAGX,OAAOkB,CACT,CACA,SAASyP,GAAiB7E,GACxB,IAAIW,EAAMZ,EAAUC,GAAO4D,QACvBhD,EAAID,EAAIC,EACZ,MAAO,CAACb,EAAUC,GAAQD,EAAU,CAClCa,GAAIA,EAAI,IAAM,IACdtM,EAAGqM,EAAIrM,EACPJ,EAAGyM,EAAIzM,IACL6L,EAAU,CACZa,GAAIA,EAAI,KAAO,IACftM,EAAGqM,EAAIrM,EACPJ,EAAGyM,EAAIzM,IAEX,CACA,SAAS4Q,GAAW9E,EAAO+E,EAASC,GAClCD,EAAUA,GAAW,EACrBC,EAASA,GAAU,GACnB,IAAIrE,EAAMZ,EAAUC,GAAO4D,QACvBrV,EAAO,IAAMyW,EACbC,EAAM,CAAClF,EAAUC,IACrB,IAAKW,EAAIC,GAAKD,EAAIC,GAAKrS,EAAOwW,GAAW,GAAK,KAAO,MAAOA,GAC1DpE,EAAIC,GAAKD,EAAIC,EAAIrS,GAAQ,IACzB0W,EAAI/Y,KAAK6T,EAAUY,IAErB,OAAOsE,CACT,CACA,SAASC,GAAelF,EAAO+E,GAC7BA,EAAUA,GAAW,EAOrB,IANA,IAAIjE,EAAMf,EAAUC,GAAOmF,QACvBvE,EAAIE,EAAIF,EACVtM,EAAIwM,EAAIxM,EACR9K,EAAIsX,EAAItX,EACNyb,EAAM,GACNG,EAAe,EAAIL,EAChBA,KACLE,EAAI/Y,KAAK6T,EAAU,CACjBa,EAAGA,EACHtM,EAAGA,EACH9K,EAAGA,KAELA,GAAKA,EAAI4b,GAAgB,EAE3B,OAAOH,CACT,CA1nBAlF,EAAUH,UAAY,CACpByF,OAAQ,WACN,OAAOpF,KAAKqF,gBAAkB,GAChC,EACAC,QAAS,WACP,OAAQtF,KAAKoF,QACf,EACAG,QAAS,WACP,OAAOvF,KAAK6C,GACd,EACA2C,iBAAkB,WAChB,OAAOxF,KAAKsC,cACd,EACAmD,UAAW,WACT,OAAOzF,KAAK0C,OACd,EACAgD,SAAU,WACR,OAAO1F,KAAKpJ,EACd,EACAyO,cAAe,WAEb,IAAIpF,EAAMD,KAAKkE,QACf,OAAgB,IAARjE,EAAItL,EAAkB,IAARsL,EAAIC,EAAkB,IAARD,EAAIha,GAAW,GACrD,EACA0f,aAAc,WAEZ,IACIC,EAAOC,EAAOC,EADd7F,EAAMD,KAAKkE,QAQf,OANA0B,EAAQ3F,EAAItL,EAAI,IAChBkR,EAAQ5F,EAAIC,EAAI,IAChB4F,EAAQ7F,EAAIha,EAAI,IAIT,OAHH2f,GAAS,OAAaA,EAAQ,MAAepa,KAAKua,KAAKH,EAAQ,MAAS,MAAO,MAG/D,OAFhBC,GAAS,OAAaA,EAAQ,MAAera,KAAKua,KAAKF,EAAQ,MAAS,MAAO,MAElD,OAD7BC,GAAS,OAAaA,EAAQ,MAAeta,KAAKua,KAAKD,EAAQ,MAAS,MAAO,KAErF,EACAE,SAAU,SAAkB7I,GAG1B,OAFA6C,KAAKpJ,GAAKuL,GAAWhF,GACrB6C,KAAKyC,QAAUjX,KAAK8R,MAAM,IAAM0C,KAAKpJ,IAAM,IACpCoJ,IACT,EACAkF,MAAO,WACL,IAAIrE,EAAMmC,EAAShD,KAAKuC,GAAIvC,KAAKwC,GAAIxC,KAAKhJ,IAC1C,MAAO,CACL2J,EAAW,IAARE,EAAIF,EACPtM,EAAGwM,EAAIxM,EACP9K,EAAGsX,EAAItX,EACPvD,EAAGga,KAAKpJ,GAEZ,EACAqP,YAAa,WACX,IAAIpF,EAAMmC,EAAShD,KAAKuC,GAAIvC,KAAKwC,GAAIxC,KAAKhJ,IACtC2J,EAAInV,KAAK8R,MAAc,IAARuD,EAAIF,GACrBtM,EAAI7I,KAAK8R,MAAc,IAARuD,EAAIxM,GACnB9K,EAAIiC,KAAK8R,MAAc,IAARuD,EAAItX,GACrB,OAAkB,GAAXyW,KAAKpJ,GAAU,OAAS+J,EAAI,KAAOtM,EAAI,MAAQ9K,EAAI,KAAO,QAAUoX,EAAI,KAAOtM,EAAI,MAAQ9K,EAAI,MAAQyW,KAAKyC,QAAU,GAC/H,EACAkB,MAAO,WACL,IAAIjD,EAAMoC,EAAS9C,KAAKuC,GAAIvC,KAAKwC,GAAIxC,KAAKhJ,IAC1C,MAAO,CACL2J,EAAW,IAARD,EAAIC,EACPtM,EAAGqM,EAAIrM,EACPJ,EAAGyM,EAAIzM,EACPjO,EAAGga,KAAKpJ,GAEZ,EACAsP,YAAa,WACX,IAAIxF,EAAMoC,EAAS9C,KAAKuC,GAAIvC,KAAKwC,GAAIxC,KAAKhJ,IACtC2J,EAAInV,KAAK8R,MAAc,IAARoD,EAAIC,GACrBtM,EAAI7I,KAAK8R,MAAc,IAARoD,EAAIrM,GACnBJ,EAAIzI,KAAK8R,MAAc,IAARoD,EAAIzM,GACrB,OAAkB,GAAX+L,KAAKpJ,GAAU,OAAS+J,EAAI,KAAOtM,EAAI,MAAQJ,EAAI,KAAO,QAAU0M,EAAI,KAAOtM,EAAI,MAAQJ,EAAI,MAAQ+L,KAAKyC,QAAU,GAC/H,EACA0D,MAAO,SAAejD,GACpB,OAAOD,EAASjD,KAAKuC,GAAIvC,KAAKwC,GAAIxC,KAAKhJ,GAAIkM,EAC7C,EACAkD,YAAa,SAAqBlD,GAChC,MAAO,IAAMlD,KAAKmG,MAAMjD,EAC1B,EACAmD,OAAQ,SAAgBC,GACtB,OAgZJ,SAAmB3R,EAAGuL,EAAGja,EAAGD,EAAGsgB,GAC7B,IAAInD,EAAM,CAACC,GAAK5X,KAAK8R,MAAM3I,GAAG0O,SAAS,KAAMD,GAAK5X,KAAK8R,MAAM4C,GAAGmD,SAAS,KAAMD,GAAK5X,KAAK8R,MAAMrX,GAAGod,SAAS,KAAMD,GAAKI,GAAoBxd,KAG1I,GAAIsgB,GAAcnD,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,IAAMH,EAAI,GAAGG,OAAO,GAC1K,OAAOH,EAAI,GAAGG,OAAO,GAAKH,EAAI,GAAGG,OAAO,GAAKH,EAAI,GAAGG,OAAO,GAAKH,EAAI,GAAGG,OAAO,GAEhF,OAAOH,EAAI3V,KAAK,GAClB,CAxZW+Y,CAAUvG,KAAKuC,GAAIvC,KAAKwC,GAAIxC,KAAKhJ,GAAIgJ,KAAKpJ,GAAI0P,EACvD,EACAE,aAAc,SAAsBF,GAClC,MAAO,IAAMtG,KAAKqG,OAAOC,EAC3B,EACApC,MAAO,WACL,MAAO,CACLvP,EAAGnJ,KAAK8R,MAAM0C,KAAKuC,IACnBrC,EAAG1U,KAAK8R,MAAM0C,KAAKwC,IACnBvc,EAAGuF,KAAK8R,MAAM0C,KAAKhJ,IACnBhR,EAAGga,KAAKpJ,GAEZ,EACA6P,YAAa,WACX,OAAkB,GAAXzG,KAAKpJ,GAAU,OAASpL,KAAK8R,MAAM0C,KAAKuC,IAAM,KAAO/W,KAAK8R,MAAM0C,KAAKwC,IAAM,KAAOhX,KAAK8R,MAAM0C,KAAKhJ,IAAM,IAAM,QAAUxL,KAAK8R,MAAM0C,KAAKuC,IAAM,KAAO/W,KAAK8R,MAAM0C,KAAKwC,IAAM,KAAOhX,KAAK8R,MAAM0C,KAAKhJ,IAAM,KAAOgJ,KAAKyC,QAAU,GACvO,EACAiE,gBAAiB,WACf,MAAO,CACL/R,EAAGnJ,KAAK8R,MAA8B,IAAxBiE,GAAQvB,KAAKuC,GAAI,MAAc,IAC7CrC,EAAG1U,KAAK8R,MAA8B,IAAxBiE,GAAQvB,KAAKwC,GAAI,MAAc,IAC7Cvc,EAAGuF,KAAK8R,MAA8B,IAAxBiE,GAAQvB,KAAKhJ,GAAI,MAAc,IAC7ChR,EAAGga,KAAKpJ,GAEZ,EACA+P,sBAAuB,WACrB,OAAkB,GAAX3G,KAAKpJ,GAAU,OAASpL,KAAK8R,MAA8B,IAAxBiE,GAAQvB,KAAKuC,GAAI,MAAc,MAAQ/W,KAAK8R,MAA8B,IAAxBiE,GAAQvB,KAAKwC,GAAI,MAAc,MAAQhX,KAAK8R,MAA8B,IAAxBiE,GAAQvB,KAAKhJ,GAAI,MAAc,KAAO,QAAUxL,KAAK8R,MAA8B,IAAxBiE,GAAQvB,KAAKuC,GAAI,MAAc,MAAQ/W,KAAK8R,MAA8B,IAAxBiE,GAAQvB,KAAKwC,GAAI,MAAc,MAAQhX,KAAK8R,MAA8B,IAAxBiE,GAAQvB,KAAKhJ,GAAI,MAAc,MAAQgJ,KAAKyC,QAAU,GACrW,EACAmE,OAAQ,WACN,OAAgB,IAAZ5G,KAAKpJ,GACA,gBAELoJ,KAAKpJ,GAAK,KAGPiQ,GAAS5D,EAASjD,KAAKuC,GAAIvC,KAAKwC,GAAIxC,KAAKhJ,IAAI,MAAU,EAChE,EACA8P,SAAU,SAAkBC,GAC1B,IAAIC,EAAa,IAAMzD,EAAcvD,KAAKuC,GAAIvC,KAAKwC,GAAIxC,KAAKhJ,GAAIgJ,KAAKpJ,IACjEqQ,EAAmBD,EACnBpE,EAAe5C,KAAK2C,cAAgB,qBAAuB,GAC/D,GAAIoE,EAAa,CACf,IAAI1S,EAAIyL,EAAUiH,GAClBE,EAAmB,IAAM1D,EAAclP,EAAEkO,GAAIlO,EAAEmO,GAAInO,EAAE2C,GAAI3C,EAAEuC,GAC7D,CACA,MAAO,8CAAgDgM,EAAe,iBAAmBoE,EAAa,gBAAkBC,EAAmB,GAC7I,EACA5D,SAAU,SAAkBjD,GAC1B,IAAI8G,IAAc9G,EAClBA,EAASA,GAAUJ,KAAK0C,QACxB,IAAIyE,GAAkB,EAClBC,EAAWpH,KAAKpJ,GAAK,GAAKoJ,KAAKpJ,IAAM,EAEzC,OADwBsQ,IAAaE,GAAwB,QAAXhH,GAA+B,SAAXA,GAAgC,SAAXA,GAAgC,SAAXA,GAAgC,SAAXA,GAAgC,SAAXA,GAS3I,QAAXA,IACF+G,EAAkBnH,KAAKyG,eAEV,SAAXrG,IACF+G,EAAkBnH,KAAK2G,yBAEV,QAAXvG,GAA+B,SAAXA,IACtB+G,EAAkBnH,KAAKoG,eAEV,SAAXhG,IACF+G,EAAkBnH,KAAKoG,aAAY,IAEtB,SAAXhG,IACF+G,EAAkBnH,KAAKwG,cAAa,IAEvB,SAAXpG,IACF+G,EAAkBnH,KAAKwG,gBAEV,SAAXpG,IACF+G,EAAkBnH,KAAK4G,UAEV,QAAXxG,IACF+G,EAAkBnH,KAAKkG,eAEV,QAAX9F,IACF+G,EAAkBnH,KAAKiG,eAElBkB,GAAmBnH,KAAKoG,eAhCd,SAAXhG,GAAiC,IAAZJ,KAAKpJ,GACrBoJ,KAAK4G,SAEP5G,KAAKyG,aA8BhB,EACAY,MAAO,WACL,OAAOvH,EAAUE,KAAKqD,WACxB,EACAiE,mBAAoB,SAA4BC,EAAIhW,GAClD,IAAIwO,EAAQwH,EAAGC,MAAM,KAAM,CAACxH,MAAM7M,OAAO,GAAGpG,MAAM0a,KAAKlW,KAKvD,OAJAyO,KAAKuC,GAAKxC,EAAMwC,GAChBvC,KAAKwC,GAAKzC,EAAMyC,GAChBxC,KAAKhJ,GAAK+I,EAAM/I,GAChBgJ,KAAKgG,SAASjG,EAAMnJ,IACboJ,IACT,EACA0H,QAAS,WACP,OAAO1H,KAAKsH,mBAAmBtD,GAAU2D,UAC3C,EACAC,SAAU,WACR,OAAO5H,KAAKsH,mBAAmBrD,GAAW0D,UAC5C,EACAE,OAAQ,WACN,OAAO7H,KAAKsH,mBAAmBnD,GAASwD,UAC1C,EACA5D,WAAY,WACV,OAAO/D,KAAKsH,mBAAmB7D,EAAakE,UAC9C,EACAG,SAAU,WACR,OAAO9H,KAAKsH,mBAAmBzD,GAAW8D,UAC5C,EACAI,UAAW,WACT,OAAO/H,KAAKsH,mBAAmBxD,GAAY6D,UAC7C,EACAK,KAAM,WACJ,OAAOhI,KAAKsH,mBAAmBlD,GAAOuD,UACxC,EACAM,kBAAmB,SAA2BV,EAAIhW,GAChD,OAAOgW,EAAGC,MAAM,KAAM,CAACxH,MAAM7M,OAAO,GAAGpG,MAAM0a,KAAKlW,IACpD,EACA2W,UAAW,WACT,OAAOlI,KAAKiI,kBAAkBpD,GAAY8C,UAC5C,EACAQ,WAAY,WACV,OAAOnI,KAAKiI,kBAAkB3D,GAAaqD,UAC7C,EACAS,cAAe,WACb,OAAOpI,KAAKiI,kBAAkBhD,GAAgB0C,UAChD,EACAU,gBAAiB,WACf,OAAOrI,KAAKiI,kBAAkBrD,GAAkB+C,UAClD,EAKAW,MAAO,WACL,OAAOtI,KAAKiI,kBAAkB1D,GAAQ,CAAC,GACzC,EACAgE,OAAQ,WACN,OAAOvI,KAAKiI,kBAAkB1D,GAAQ,CAAC,GACzC,GAKFzE,EAAU0I,UAAY,SAAUzI,EAAOhW,GACrC,GAAsB,UAAlBuV,EAAQS,GAAoB,CAC9B,IAAI0I,EAAW,CAAC,EAChB,IAAK,IAAIjf,KAAKuW,EACRA,EAAMmC,eAAe1Y,KAErBif,EAASjf,GADD,MAANA,EACYuW,EAAMvW,GAENkY,GAAoB3B,EAAMvW,KAI9CuW,EAAQ0I,CACV,CACA,OAAO3I,EAAUC,EAAOhW,EAC1B,EA+PA+V,EAAU4I,OAAS,SAAUC,EAAQC,GACnC,SAAKD,IAAWC,IACT9I,EAAU6I,GAAQlC,eAAiB3G,EAAU8I,GAAQnC,aAC9D,EACA3G,EAAU+I,OAAS,WACjB,OAAO/I,EAAU0I,UAAU,CACzB7T,EAAGnJ,KAAKqd,SACR3I,EAAG1U,KAAKqd,SACR5iB,EAAGuF,KAAKqd,UAEZ,EAiIA/I,EAAUgJ,IAAM,SAAUH,EAAQC,EAAQlF,GACxCA,EAAoB,IAAXA,EAAe,EAAIA,GAAU,GACtC,IAAIqF,EAAOjJ,EAAU6I,GAAQzE,QACzB8E,EAAOlJ,EAAU8I,GAAQ1E,QACzBnZ,EAAI2Y,EAAS,IAOjB,OAAO5D,EANI,CACTnL,GAAIqU,EAAKrU,EAAIoU,EAAKpU,GAAK5J,EAAIge,EAAKpU,EAChCuL,GAAI8I,EAAK9I,EAAI6I,EAAK7I,GAAKnV,EAAIge,EAAK7I,EAChCja,GAAI+iB,EAAK/iB,EAAI8iB,EAAK9iB,GAAK8E,EAAIge,EAAK9iB,EAChCD,GAAIgjB,EAAKhjB,EAAI+iB,EAAK/iB,GAAK+E,EAAIge,EAAK/iB,GAGpC,EAQA8Z,EAAUmJ,YAAc,SAAUN,EAAQC,GACxC,IAAIM,EAAKpJ,EAAU6I,GACfQ,EAAKrJ,EAAU8I,GACnB,OAAQpd,KAAK4W,IAAI8G,EAAGvD,eAAgBwD,EAAGxD,gBAAkB,MAASna,KAAKC,IAAIyd,EAAGvD,eAAgBwD,EAAGxD,gBAAkB,IACrH,EAYA7F,EAAUsJ,WAAa,SAAUT,EAAQC,EAAQS,GAC/C,IACIC,EAAYvb,EADZkb,EAAcnJ,EAAUmJ,YAAYN,EAAQC,GAIhD,OAFA7a,GAAM,GACNub,EAqbF,SAA4BC,GAG1B,IAAIC,EAAOhX,EAKXgX,IAJAD,EAAQA,GAAS,CACfC,MAAO,KACPhX,KAAM,UAEOgX,OAAS,MAAMC,cAC9BjX,GAAQ+W,EAAM/W,MAAQ,SAAS6N,cACjB,OAAVmJ,GAA4B,QAAVA,IACpBA,EAAQ,MAEG,UAAThX,GAA6B,UAATA,IACtBA,EAAO,SAET,MAAO,CACLgX,MAAOA,EACPhX,KAAMA,EAEV,CAzcekX,CAAmBL,IACbG,MAAQF,EAAW9W,MACpC,IAAK,UACL,IAAK,WACHzE,EAAMkb,GAAe,IACrB,MACF,IAAK,UACHlb,EAAMkb,GAAe,EACrB,MACF,IAAK,WACHlb,EAAMkb,GAAe,EAGzB,OAAOlb,CACT,EAWA+R,EAAU6J,aAAe,SAAUC,EAAWC,EAAWtY,GACvD,IAEI0X,EACAa,EAAuBN,EAAOhX,EAH9BuX,EAAY,KACZC,EAAY,EAIhBF,GADAvY,EAAOA,GAAQ,CAAC,GACauY,sBAC7BN,EAAQjY,EAAKiY,MACbhX,EAAOjB,EAAKiB,KACZ,IAAK,IAAIhJ,EAAI,EAAGA,EAAIqgB,EAAUte,OAAQ/B,KACpCyf,EAAcnJ,EAAUmJ,YAAYW,EAAWC,EAAUrgB,KACvCwgB,IAChBA,EAAYf,EACZc,EAAYjK,EAAU+J,EAAUrgB,KAGpC,OAAIsW,EAAUsJ,WAAWQ,EAAWG,EAAW,CAC7CP,MAAOA,EACPhX,KAAMA,MACDsX,EACEC,GAEPxY,EAAKuY,uBAAwB,EACtBhK,EAAU6J,aAAaC,EAAW,CAAC,OAAQ,QAASrY,GAE/D,EAKA,IAAIgP,GAAQT,EAAUS,MAAQ,CAC5B0J,UAAW,SACXC,aAAc,SACdC,KAAM,MACNC,WAAY,SACZC,MAAO,SACPC,MAAO,SACPC,OAAQ,SACRC,MAAO,MACPC,eAAgB,SAChBC,KAAM,MACNC,WAAY,SACZC,MAAO,SACPC,UAAW,SACXC,YAAa,SACbC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,MAAO,SACPC,eAAgB,SAChBC,SAAU,SACVC,QAAS,SACTC,KAAM,MACNC,SAAU,SACVC,SAAU,SACVC,cAAe,SACfC,SAAU,SACVC,UAAW,SACXC,SAAU,SACVC,UAAW,SACXC,YAAa,SACbC,eAAgB,SAChBC,WAAY,SACZC,WAAY,SACZC,QAAS,SACTC,WAAY,SACZC,aAAc,SACdC,cAAe,SACfC,cAAe,SACfC,cAAe,SACfC,cAAe,SACfC,WAAY,SACZC,SAAU,SACVC,YAAa,SACbC,QAAS,SACTC,QAAS,SACTC,WAAY,SACZC,UAAW,SACXC,YAAa,SACbC,YAAa,SACbC,QAAS,MACTC,UAAW,SACXC,WAAY,SACZC,KAAM,SACNC,UAAW,SACXC,KAAM,SACNC,MAAO,SACPC,YAAa,SACbC,KAAM,SACNC,SAAU,SACVC,QAAS,SACTC,UAAW,SACXC,OAAQ,SACRC,MAAO,SACPC,MAAO,SACPC,SAAU,SACVC,cAAe,SACfC,UAAW,SACXC,aAAc,SACdC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,qBAAsB,SACtBC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,UAAW,SACXC,YAAa,SACbC,cAAe,SACfC,aAAc,SACdC,eAAgB,MAChBC,eAAgB,MAChBC,eAAgB,SAChBC,YAAa,SACbC,KAAM,MACNC,UAAW,SACXC,MAAO,SACPC,QAAS,MACTC,OAAQ,SACRC,iBAAkB,SAClBC,WAAY,SACZC,aAAc,SACdC,aAAc,SACdC,eAAgB,SAChBC,gBAAiB,SACjBC,kBAAmB,SACnBC,gBAAiB,SACjBC,gBAAiB,SACjBC,aAAc,SACdC,UAAW,SACXC,UAAW,SACXC,SAAU,SACVC,YAAa,SACbC,KAAM,SACNC,QAAS,SACTC,MAAO,SACPC,UAAW,SACXC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,cAAe,SACfC,UAAW,SACXC,cAAe,SACfC,cAAe,SACfC,WAAY,SACZC,UAAW,SACXC,KAAM,SACNC,KAAM,SACNC,KAAM,SACNC,WAAY,SACZC,OAAQ,SACRC,cAAe,SACfC,IAAK,MACLC,UAAW,SACXC,UAAW,SACXC,YAAa,SACbC,OAAQ,SACRC,WAAY,SACZC,SAAU,SACVC,SAAU,SACVC,OAAQ,SACRC,OAAQ,SACRC,QAAS,SACTC,UAAW,SACXC,UAAW,SACXC,UAAW,SACXC,KAAM,SACNC,YAAa,SACbC,UAAW,SACXC,IAAK,SACLC,KAAM,SACNC,QAAS,SACTC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,MAAO,SACPC,MAAO,MACPC,WAAY,SACZC,OAAQ,MACRC,YAAa,UAIXxM,GAAW/G,EAAU+G,SAMzB,SAAcjT,GACZ,IAAI0f,EAAU,CAAC,EACf,IAAK,IAAI9pB,KAAKoK,EACRA,EAAEsO,eAAe1Y,KACnB8pB,EAAQ1f,EAAEpK,IAAMA,GAGpB,OAAO8pB,CACT,CAdoCC,CAAKhT,IAiBzC,SAAS4B,GAAWnc,GAKlB,OAJAA,EAAIwtB,WAAWxtB,IACXye,MAAMze,IAAMA,EAAI,GAAKA,EAAI,KAC3BA,EAAI,GAECA,CACT,CAGA,SAASub,GAAQpN,EAAGiO,IA+BpB,SAAwBjO,GACtB,MAAmB,iBAALA,IAAoC,GAAnBA,EAAEnE,QAAQ,MAAgC,IAAlBwjB,WAAWrf,EACpE,EAhCMsf,CAAetf,KAAIA,EAAI,QAC3B,IAAIuf,EAkCN,SAAsBvf,GACpB,MAAoB,iBAANA,IAAqC,GAAnBA,EAAEnE,QAAQ,IAC5C,CApCuB2jB,CAAaxf,GASlC,OARAA,EAAI3I,KAAKC,IAAI2W,EAAK5W,KAAK4W,IAAI,EAAGoR,WAAWrf,KAGrCuf,IACFvf,EAAIyf,SAASzf,EAAIiO,EAAK,IAAM,KAI1B5W,KAAKqoB,IAAI1f,EAAIiO,GAAO,KACf,EAIFjO,EAAIiO,EAAMoR,WAAWpR,EAC9B,CAGA,SAASwB,GAAQkQ,GACf,OAAOtoB,KAAKC,IAAI,EAAGD,KAAK4W,IAAI,EAAG0R,GACjC,CAGA,SAAS9S,GAAgB8S,GACvB,OAAOF,SAASE,EAAK,GACvB,CAcA,SAAS1Q,GAAK3V,GACZ,OAAmB,GAAZA,EAAElC,OAAc,IAAMkC,EAAI,GAAKA,CACxC,CAGA,SAASiU,GAAoBvN,GAI3B,OAHIA,GAAK,IACPA,EAAQ,IAAJA,EAAU,KAETA,CACT,CAGA,SAASqP,GAAoBT,GAC3B,OAAOvX,KAAK8R,MAAsB,IAAhBkW,WAAWzQ,IAAUM,SAAS,GAClD,CAEA,SAASpC,GAAoBN,GAC3B,OAAOK,GAAgBL,GAAK,GAC9B,CACA,IAQMoT,GAKAC,GACAC,GAdFzT,IAaEwT,GAAoB,eALpBD,GAAW,8CAKoC,aAAeA,GAAW,aAAeA,GAAW,YACnGE,GAAoB,cAAgBF,GAAW,aAAeA,GAAW,aAAeA,GAAW,aAAeA,GAAW,YAC1H,CACLA,SAAU,IAAInpB,OAAOmpB,IACrB9T,IAAK,IAAIrV,OAAO,MAAQopB,IACxBvT,KAAM,IAAI7V,OAAO,OAASqpB,IAC1BvT,IAAK,IAAI9V,OAAO,MAAQopB,IACxBpT,KAAM,IAAIhW,OAAO,OAASqpB,IAC1BpT,IAAK,IAAIjW,OAAO,MAAQopB,IACxBlT,KAAM,IAAIlW,OAAO,OAASqpB,IAC1B7S,KAAM,uDACNF,KAAM,uDACNC,KAAM,uEACNJ,KAAM,yEAOV,SAASO,GAAevB,GACtB,QAASS,GAASuT,SAASpjB,KAAKoP,EAClC,CC1hCA,IAAImU,GAA6B,CAAEC,IACjCA,EAAmB,MAAI,QACvBA,EAAmB,MAAI,QACvBA,EAAyB,YAAI,KACtBA,GAJwB,CAK9BD,IAAc,CAAC,GACdE,GAA+B,CAAEC,IACnCA,EAAwB,SAAI,WAC5BA,EAA0B,WAAI,aAC9BA,EAAoB,KAAI,OACjBA,GAJ0B,CAKhCD,IAAgB,CAAC,GAChBE,GAA8B,CAAEC,IAClCA,EAAyB,WAAI,aAC7BA,EAA2B,aAAI,eACxBA,GAHyB,CAI/BD,IAAe,CAAC,GACfE,GAAkC,CAAEC,IACtCA,EAA0B,QAAI,UAC9BA,EAAiC,eAAI,iBAC9BA,GAH6B,CAInCD,IAAmB,CAAC,G,WChBvB,MAAME,GAAgB,CACpB,EAAM,CAAE/T,EAAG,GAAItM,EAAG,GAAIJ,EAAG,KACzB,EAAM,CAAE0M,EAAG,GAAItM,EAAG,GAAIJ,EAAG,KACzB,EAAM,CAAE0M,EAAG,IAAKtM,EAAG,GAAIJ,EAAG,KAC1B,EAAM,CAAE0M,EAAG,IAAKtM,EAAG,GAAIJ,EAAG,KAC1B,EAAM,CAAE0M,EAAG,IAAKtM,EAAG,GAAIJ,EAAG,KAC1B,EAAM,CAAE0M,EAAG,IAAKtM,EAAG,GAAIJ,EAAG,KAC1B,EAAM,CAAE0M,EAAG,IAAKtM,EAAG,GAAIJ,EAAG,KAC1B,EAAM,CAAE0M,EAAG,GAAItM,EAAG,IAAKJ,EAAG,KAC1B,EAAM,CAAEU,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,GAAIuL,EAAG,IAAKja,EAAG,MAC1B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,KAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,KAC3B,EAAM,CAAE0O,EAAG,GAAIuL,EAAG,IAAKja,EAAG,MAC1B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,MAC3B,EAAM,CAAE0O,EAAG,IAAKuL,EAAG,IAAKja,EAAG,OAIvB0uB,GAAkB,0BAFAC,GAAmB,EAAG,IAAK,EAAG,UAC9BA,GAAmB,IAAK,IAAK,EAAG,WAElDC,GAAoB,0BAA0BH,GAAc,UAAUA,GAAc,WAAWA,GAAc,WAAWA,GAAc,WAAWA,GAAc,WACrK,SAASE,GAAmBzX,EAAOrC,EAAYga,EAAUC,GACvD,MAAMC,EAAYxpB,KAAKC,IAAI,EAAG0R,EAAQrC,GAAcia,EAAWD,IAG/D,OAAO,EAAM,CAAEnU,EAFL,GAAK,GAAKqU,EAEF3gB,EAAG,IAAKJ,EADhB,GAAK,EAAI+gB,GAErB,CACA,SAASC,GAAqBjb,EAAO0E,GACnC,MAEMwW,EC5CR,SAA2BC,EAAKC,EAAO,GACrC,IAAIC,EACAC,EACAC,EACAC,EACAtM,EACAC,EACAsM,EACAjsB,EAOJ,IANA6rB,EAAyB,EAAbF,EAAI5pB,OAChB+pB,EAAQH,EAAI5pB,OAAS8pB,EACrBE,EAAKH,EACLlM,EAAK,WACLC,EAAK,UACL3f,EAAI,EACGA,EAAI8rB,GACTG,EAAyB,IAApBN,EAAIO,WAAWlsB,IAAkC,IAAtB2rB,EAAIO,aAAalsB,KAAa,GAA2B,IAAtB2rB,EAAIO,aAAalsB,KAAa,IAA4B,IAAtB2rB,EAAIO,aAAalsB,KAAa,KACnIA,EACFisB,GAAW,MAALA,GAAcvM,KAAQuM,IAAO,IAAMvM,EAAK,QAAU,IAAM,WAC9DuM,EAAKA,GAAM,GAAKA,IAAO,GACvBA,GAAW,MAALA,GAActM,KAAQsM,IAAO,IAAMtM,EAAK,QAAU,IAAM,WAC9DoM,GAAME,EACNF,EAAKA,GAAM,GAAKA,IAAO,GACvBC,EAAqB,GAAT,MAALD,KAAkC,GAAbA,IAAO,IAAU,QAAU,IAAM,WAC7DA,EAAqB,OAAT,MAANC,KAAwC,OAAdA,IAAQ,IAAc,QAAU,IAGlE,OADAC,EAAK,EACGJ,GACN,KAAK,EACHI,IAA+B,IAAxBN,EAAIO,WAAWlsB,EAAI,KAAa,GAEzC,KAAK,EACHisB,IAA+B,IAAxBN,EAAIO,WAAWlsB,EAAI,KAAa,EAEzC,KAAK,EACHisB,GAA0B,IAApBN,EAAIO,WAAWlsB,GAEvB,QACEisB,GAAW,MAALA,GAAcvM,KAAQuM,IAAO,IAAMvM,EAAK,QAAU,IAAM,WAC9DuM,EAAKA,GAAM,GAAKA,IAAO,GACvBA,GAAW,MAALA,GAActM,KAAQsM,IAAO,IAAMtM,EAAK,QAAU,IAAM,WAC9DoM,GAAME,EAQV,OANAF,GAAMJ,EAAI5pB,OACVgqB,GAAMA,IAAO,GACbA,EAAoB,YAAT,MAALA,KAA2C,YAAbA,IAAO,IAAmB,QAAU,IAAM,WAC9EA,GAAMA,IAAO,GACbA,EAAoB,YAAT,MAALA,KAA2C,YAAbA,IAAO,IAAmB,QAAU,IAAM,WAC9EA,GAAMA,IAAO,GACNA,IAAO,CAChB,CDPeI,CA4Cf,SAAwBvW,GACtB,IAAIxI,EACJ,IAAK,MAAOgf,EAAGC,KAAY,GAAU,CACnC,MAAMztB,EAAQgX,EAAKhX,MAAMytB,GACzB,GAAIztB,EACF,OAA+B,OAAtBwO,EAAKxO,EAAM0tB,aAAkB,EAASlf,EAAGmf,cAAgB,EAEtE,CACA,MACF,CAtDsBC,CAAehc,IACW,GAAI,GACxB0a,GAAcnpB,OACxC,IAAI0qB,EAAevB,GAAcQ,GAAY7N,QAI7C,OAHI3I,EAAM4G,UACR2Q,EAAeA,EAAarO,SAAS,KAEhCqO,CACT,CACA,MAAMC,GAAoB,CAAC,iBAAkB,qBAAsB,kBAC7DC,GAAsB,0BAA0BD,GAAkB,UAAUA,GAAkB,WAAWA,GAAkB,WAC3HE,GAAuB,CAAC,oBAAqB,qBAAsB,oBACnEC,GAAyB,0BAA0BD,GAAqB,UAAUA,GAAqB,WAAWA,GAAqB,WAgB7I,MAAM,GAAW,CACf,CAAC,SAAU,sEACX,CAAC,QAAS,qEACV,CAAC,QAAS,qEACV,CACE,UACA,mHAEF,CAAC,QAAS,4DAEV,CAAC,UAAW,8DACZ,CAAC,YAAa,0CACd,CAAC,UAAW,4BACZ,CAAC,eAAgB,2BACjB,CAAC,UAAW,wBACZ,CAAC,UAAW,yBE9Ed,SAASE,GAAeC,GACtB,MAAM,UACJC,EAAS,KACTje,EAAI,KACJke,EAAI,MACJC,EAAK,UACLC,EAAS,aACTC,EAAY,SACZ9B,EAAQ,SACRC,EAAQ,cACR8B,EAAa,UACbC,EAAS,eACTC,EAAc,gBACdC,EAAe,gBACfrZ,EAAe,YACfsZ,EAAW,gBACXC,EAAe,aACfC,GACEZ,EACEa,EA8KR,SAAwBZ,EAAWI,EAAcS,GAC/C,MAAOD,EAAKE,IAAU,IAAAzgB,YAgBtB,OAfA,IAAAZ,YAAU,KACR,IAAMohB,IAAkBb,EAAUe,QAChC,OAEF,MAAMC,EAAOhB,EAAUe,QAAQE,WAAW,MACpCnhB,EAASoB,EAAmB2f,EAClCb,EAAUe,QAAQlhB,MAAQ7K,KAAK8R,MAAMsZ,EAAe7gB,OAAO4B,kBAC3D6e,EAAUe,QAAQjhB,OAAS9K,KAAK8R,MAAMhH,GACtCkgB,EAAUe,QAAQG,MAAMrhB,MAAQ,GAAGugB,MACnCJ,EAAUe,QAAQG,MAAMphB,OAAYA,EAASP,OAAO4B,iBAAnB,KACjC6f,EAAKG,aAAe,SACpBH,EAAKI,KAAO,GAAK7hB,OAAO4B,iBAAmB,eAC3C6f,EAAKK,YAAc,QACnBP,EAAOE,EAAK,GACX,CAAChB,EAAWc,EAAQV,EAAcS,IAC9BD,CACT,CAhMcU,CAAetB,EAAWI,EAAcF,GAC9ChY,GAAQ,IAAAqZ,aACRC,GAAa,IAAA5gB,UAAQ,KACzB,MAAM6gB,EAAgB,EAAMvZ,EAAMwZ,OAAOC,WAAWC,WACpD,OAAO1Z,EAAM4G,QAAU2S,EAAcpQ,OAAO,IAAIzB,cAAgB6R,EAAcvQ,QAAQ,IAAItB,aAAa,GACtG,CAAC1H,IACE2Z,EAyJR,SAA0Bvd,EAAY6C,EAAiBsZ,EAAavY,EAAOsZ,EAAYlD,EAAUC,EAAU8B,EAAeyB,GACxH,OAAO,IAAAC,cACL,SAAkB1d,EAAMb,EAAOwe,GAC7B,GAAIA,IAAU3B,EACZ,OAAOmB,EAET,MAAMS,OAA+B,IAApB5d,EAAKgD,YAA0BoZ,IAAgBzC,GAAgBkE,SAAWzB,IAAgBzC,GAAgBmE,eAA6G1B,IAAgB3C,GAAYsE,WAAahE,GAAmB/Z,EAAKsC,MAAOrC,EAAYga,EAAUC,GAAYE,GAAqBjb,EAAO0E,GFxIpX,SAA2Bma,EAAOC,EAAYhe,EAAY6C,EAAiBsZ,GACzE,MAAM8B,EAAQ9B,IAAgBzC,GAAgBkE,QAAUxC,GAAoBE,GACtE4C,GAAa,KAAAC,eAAcC,OAAO,EAAE,IAAK,EAAG,MAAMH,MAAMA,GACxDI,EAAYN,EAAQC,EACpBhb,EAAiBhD,EAAa6C,EACpC,GAAwB,IAApBA,GAA4C,IAAnBG,EAE3B,OAAO,EADYkb,EAAW,IAGhC,MAAMhb,EAAiBxS,KAAK8R,MAAM,IAAM6b,EAAYrb,GAAkB,IAItE,OAAO,EADWkb,GAFMxtB,KAAK8R,MAAM,IAAMwb,EAAanb,GAAmB,IACzCK,GAAkBA,EAAiB,KAGrE,CE0HmJob,CAAkBve,EAAKsC,MAAOtC,EAAKgD,WAAY/C,EAAY6C,EAAiBsZ,GACzN,OAAIJ,EACKA,EAAcnkB,IAAIsH,GAASye,EAASvS,cAAgB8R,EAEtDnd,EAAK2O,MAAQ8O,EAAW,EAAIG,EAASvS,cAAgBuS,EAAS/Q,QAAQ,IAAIxB,aACnF,GACA,CAACpL,EAAY6C,EAAiBsZ,EAAavY,EAAOoW,EAAUC,EAAU8B,EAAeyB,EAAUN,GAEnG,CAvKsBqB,CAClBrC,EACArZ,EACAsZ,EACAvY,EACAsZ,EACAlD,EACAC,EACA8B,EACAK,EAAkBA,EAAgBrc,KAAK2O,MAAQ,GAE3C8P,EAwCR,SAAuBlC,EAAK7e,EAAM8f,EAAavB,EAAWK,GACxD,OAAO,IAAA/f,UAAQ,KACb,IAAKggB,EACH,MAAO,OAiCT,MA9BmB,CAACvc,EAAM1E,EAAGC,EAAGC,EAAOC,EAAQ0D,KAC7Cod,EAAImC,YACJnC,EAAIngB,KAAKd,EAAI4B,EAAkB3B,EAAGC,EAAOC,GACzC8gB,EAAIoC,UAAYnB,EAAYxd,EAAMb,GAAO,GACzCod,EAAIqC,SACJrC,EAAIvqB,OACJ,MAAM6sB,EAAsBvC,EAAaniB,IAAI6F,GAC7C,IAAI8e,EAAa3f,EACjB,GAAI0f,GAAuBA,EAAoBlf,UAAW,CAExDmf,EAAa,IADkBD,EAAoB1c,MAAMzR,WACXyO,CAChD,CACI3D,GAASyB,IACP4hB,GACFE,GACExC,EACA7e,EACAohB,EACA9e,EACAxE,EACc,SAAdygB,EAAuB3gB,EAAIgC,EAA0BC,EAAoBjC,EACzEC,EACA0gB,GAWZ,SAA6BM,EAAKjhB,EAAGC,EAAGE,EAAQuE,EAAM6e,GACpD,MAAMG,EAAc1jB,EAAIgC,EACxBif,EAAImC,YACJnC,EAAIngB,KAAKd,EAAGC,EAAGyjB,EAAc1jB,EAAI8B,EAAoBC,EAAqB5B,GAC1E8gB,EAAIvqB,OACJuqB,EAAImC,YACAG,EAAoBlf,UACtB4c,EAAIngB,KAAK4iB,EAAazjB,EAAIE,EAAS,EAAG2B,EAAmB3B,EAAS,GAE9DojB,EAAoB1c,MAAM,KAAOnC,EACnCuc,EAAIngB,KAAK4iB,EAAazjB,EAAIE,EAAS,EAAG2B,EAAmB3B,EAAS,GACzDojB,EAAoB1c,MAAM0c,EAAoB1c,MAAMzR,OAAS,KAAOsP,EAC7Euc,EAAIngB,KAAK4iB,EAAazjB,EAAG6B,EAAmB3B,EAAS,GAErD8gB,EAAIngB,KAAK4iB,EAAazjB,EAAG6B,EAAmB3B,GAGhD8gB,EAAIoC,UAAY,OAChBpC,EAAIvqB,MACN,CA5BUitB,CAAoB1C,EAAKjhB,EAAGC,EAAGE,EAAQuE,EAAM6e,IAE7CE,GAAYxC,EAAK7e,EAAMohB,EAAY9e,EAAMxE,EAAOF,EAAGC,EAAG0gB,GAE1D,CAEe,GAChB,CAACM,EAAKiB,EAAavB,EAAWve,EAAM4e,GACzC,CA9EqB4C,CAAc3C,EAAK7e,EAAM8f,EAAavB,EAAWK,IACpE,IAAAlhB,YAAU,KACR,IAAKmhB,EACH,OAEFA,EAAI4C,UAAU,EAAG,EAAG5C,EAAI6C,OAAO5jB,MAAO+gB,EAAI6C,OAAO3jB,QACjD,MAAM4jB,EAAc,IAAIC,QA6F5B,SAAkB1D,EAAME,EAAWpe,EAAMwe,EAAgBjC,EAAUC,EAAU6B,EAAcO,EAAcmC,GACvG,MAAMc,EAAQ,GACdA,EAAMnuB,KAAK,CAAE4O,KAAM4b,EAAM4D,YAAa,IACtC,MAAMC,EAAgB1D,EAAe7gB,OAAO4B,iBAAmBof,GAAkBhC,EAAWD,GAC5F,IAAIyF,EACJ,KAAOH,EAAM7uB,OAAS,GAAG,CACvB,MAAM,KAAEsP,EAAI,YAAEwf,GAAgBD,EAAMI,QACpC,IAAIC,EAAc5f,EAAKsC,MACvB,MAAMqb,EAAQiC,EAAcH,GAAiB1iB,EACvCvB,EAAQokB,EAAcH,GAAiB9B,EAAQ,EAAuB,EAAnBzgB,GACnDzB,EAASoB,EACf,GAAIrB,EAAQwB,EACV,SAEF,IAAI6iB,EAAiB,EACjBC,GAAa,EACjB,MAAMjB,EAAsBvC,EAAaniB,IAAI6F,GACvC+f,EAAkBlB,GAAuBA,EAAoBlf,UAWnE,GAVIogB,GACEL,IAA0Bb,EAAoB1c,MAAM,IACtD0d,EAA+B,aAAd/D,GAA4B,EAAI,EACjDgE,GAAa,GAKfJ,OAAwB,GAErBI,EAAY,CAIXC,IACFL,EAAwB1f,GAE1Bye,EAAWze,EANEggB,GAAQhgB,EAAKzR,MAAO2tB,EAAgBjC,EAAUwF,IAC7Czf,EAAK2O,MAAQ6Q,GAAe3iB,EAKbrB,EAAOC,EAJxBiC,EAAKuE,SAASjC,EAAKkC,YAAY,IAIQyb,EACrD,CACA,MAAMsC,EAAyB,aAAdnE,EAA2B9b,EAAKtB,SAAWsB,EAAKkgB,QAC7DD,GACFV,EAAMY,WAAWF,EAASxxB,KAAKmE,IAAM,CAAGoN,KAAMpN,EAAG4sB,YAAaA,EAAcK,MAEhF,CACF,CAtIIO,CACExE,EACAE,EACApe,EACAwe,EACAjC,EACAC,EACA6B,EACAO,GACA,CAACtc,EAAM1E,EAAGC,EAAGC,EAAOC,EAAQ0D,EAAOwe,KAC7BA,EACF0B,EAAYjjB,KAAKd,EAAGC,EAAGC,EAAOC,GAE9BgjB,EAAWze,EAAM1E,EAAGC,EAAGC,EAAOC,EAAQ0D,EACxC,IAGJod,EAAIoC,UAAYxB,EAChBZ,EAAIvqB,KAAKqtB,EAAY,GACpB,CACD9C,EACA7e,EACAke,EACAG,EACA9B,EACAC,EACAgC,EACAJ,EACA2C,EACAnC,EACAa,GAEJ,CAyIA,SAAS4B,GAAYxC,EAAK7e,EAAMyB,EAAOa,EAAMxE,EAAOF,EAAGC,EAAG0gB,GACxDM,EAAI8D,OACJ9D,EAAI+D,OACJ/D,EAAIoC,UAAY,OAChB,MAAMvc,EAAe1E,EAAK2E,sBAAsBrC,EAAKsC,OAC/Cie,EAAOne,EAAaO,OAASP,EAAaM,KAAON,EAAaO,OAASP,EAAaM,KACpF8d,EAAUjE,EAAIkE,YAAYthB,GAC1BuhB,EAAqBllB,EAAQ2B,EACnC,IAAIwjB,EAAY,GAAGxhB,MAAUohB,KACzBK,EAASjwB,KAAK4W,IAAIjM,EAAG,GAAK6B,EAC1BqjB,EAAQhlB,MAAQklB,IAClBnE,EAAIN,UAAYA,EACE,UAAdA,IACF0E,EAAYxhB,EACZyhB,EAAStlB,EAAIE,EAAQ2B,IAGzBof,EAAIsE,SAASF,EAAWC,EAAQrlB,EAAIsB,EAAmB,EAAI,GAC3D0f,EAAIuE,SACN,CACA,SAASd,GAAQle,EAAQ7B,EAAYga,EAAUwF,GAC7C,OAAQ3d,EAAS7B,EAAaga,GAAYwF,CAC5C,CCxOA,MAAMsB,GAAmB,EACvBrjB,OACAuc,WACAC,WACA8B,gBACAgF,cACAC,cACAC,gBACA7E,kBACAJ,YACAne,aACAse,cACA+E,oBACAC,yBACAlF,iBACAN,OACAE,YACAD,QACAwF,qBACA/E,eACAgF,kBACAjjB,aACAD,6BACAI,eACA/H,aAEA,MAAM0J,EAAS,MACRohB,GAAW/lB,MAAOugB,IAAkB,IACrCyF,GAAW,IAAAC,QAAO,OACjBC,EAAaC,IAAkB,IAAA3lB,aAC/B4lB,EAAiBC,IAAsB,IAAA7lB,YAC9Cyf,GAAe,CACbE,UAAW6F,EACXpF,cACA1e,OACA2e,kBACAT,OACAE,YACAD,QACA3B,WACAD,WACA+B,gBACAC,YACAC,iBAEAC,gBAAiBze,EAAKqB,mBAAqBoiB,EAAoBjF,EAC/DpZ,gBAAiBse,EACjBrF,eACAO,iBAEF,MAAMwF,GAAe,IAAApE,cAClB9kB,IACC+oB,OAAe,GACf,MAAMlC,EAAgB+B,EAAS9E,QAAQqF,YAAc7F,GAAkBhC,EAAWD,GAC5Eja,EAAOgiB,GACX,CAAE1mB,EAAG1C,EAAEqpB,YAAYC,QAAS3mB,EAAG3C,EAAEqpB,YAAYE,SAC7CvG,EACAE,EACAD,EACA4D,EACAvD,EACAjC,EACAqC,GAGAuF,EADE7hB,EACiB,CACjBH,KAAMjH,EAAEwpB,QACRxiB,KAAMhH,EAAEypB,QACRriB,OACAb,MAAOzB,EAAKuE,SAASjC,EAAKkC,YAAY,UAGrB,EACrB,GAEF,CAACxE,EAAMuc,EAAUC,EAAUgC,EAAgBN,EAAME,EAAWD,EAAOS,KAE9DgG,EAAeC,IAAoB,IAAAvmB,YACpCwmB,GAAmB,IAAA9E,cACtB9kB,IACC,QAAwB,IAApBgpB,EAA4B,CAC9BD,OAAe,GACfY,OAAiB,GACjB,MAAM9C,EAAgB+B,EAAS9E,QAAQqF,YAAc7F,GAAkBhC,EAAWD,GAC5Eja,EAAOgiB,GACX,CAAE1mB,EAAG1C,EAAEqpB,YAAYC,QAAS3mB,EAAG3C,EAAEqpB,YAAYE,SAC7CvG,EACAE,EACAD,EACA4D,EACAvD,EACAjC,EACAqC,GAEEtc,IACFuiB,EAAiB,CAAEjnB,EAAG1C,EAAEypB,QAAS9mB,EAAG3C,EAAEwpB,UACtCT,EAAe3hB,GAEnB,IAEF,CAACia,EAAUC,EAAUgC,EAAgB0F,EAAiBW,EAAkB3G,EAAME,EAAWD,EAAOS,IAE5FmG,GAAoB,IAAA/E,cAAY,KACpCiE,OAAe,EAAO,GACrB,IAWH,OAVA,IAAAvmB,YAAU,KACR,MAAMsnB,EAAiB9pB,IACrB,IAAImD,EACAnD,EAAE+pB,kBAAkBC,aAA4E,iDAA3B,OAAhC7mB,EAAKnD,EAAE+pB,OAAOE,oBAAyB,EAAS9mB,EAAG8E,KAC1FghB,OAAmB,EACrB,EAGF,OADA3mB,OAAO4nB,iBAAiB,QAASJ,GAC1B,IAAMxnB,OAAO6nB,oBAAoB,QAASL,EAAc,GAC9D,CAACb,KACmB,IAAA7iB,MAAK,MAAO,CAAE0B,UAAWP,EAAO6iB,MAAOtkB,SAAU,EACtD,IAAAD,KAAI,MAAO,CAAEiC,UAAWP,EAAO8iB,cAAepiB,GAAI,8CAA+C3E,IAAKqlB,EAAS7iB,UAA0B,IAAAD,KACvJ,SACA,CACEvC,IAAKslB,EACL,cAAe,aACfniB,QAASyiB,EACToB,YAAaV,EACbW,aAAcV,OAGF,IAAAhkB,KACdsB,EACA,CACEG,SAAUoiB,EACVtiB,KAAM0hB,EACNhkB,OACAuC,WAAYic,EACZne,eAAgB2jB,EAAcpF,EAAaniB,IAAIunB,QAAe,KAGjEL,GAAsBO,IAAmC,IAAAnjB,KACxDhB,EACA,CACEC,OACAC,SAAUikB,EACVvjB,aACAN,eAAgBue,EAAaniB,IAAIynB,EAAgB5hB,MACjDpC,gBAAiB,KACfikB,OAAmB,EAAO,EAE5BhkB,YAAa,KACXmjB,EAAYY,EAAgB5hB,KAAKzR,MAAQ2tB,GACzC+E,GAAaW,EAAgB5hB,KAAKzR,MAAQqzB,EAAgB5hB,KAAKsC,OAAS4Z,GACxEgF,EAAcU,EAAgB,EAEhC9jB,WAAY,KACVA,EAAWJ,EAAKuE,SAAS2f,EAAgB5hB,KAAKkC,YAAY,IAAI,EAEhElE,cAAe,KACbsjB,EAAgBhF,EAAa8G,mBAAmBxB,EAAgB5hB,MAAM,GAAO,EAE/E/B,gBAAiB,KACfqjB,EAAgBhF,EAAa8G,mBAAmBxB,EAAgB5hB,MAAM,GAAM,EAE9E9B,kBAAmB,KACjBojB,EAAgBhF,EAAa+G,uBAAsB,GAAO,EAE5DllB,oBAAqB,KACnBmjB,EAAgBhF,EAAa+G,uBAAsB,GAAM,EAE3D9kB,mBAAoBxM,MAAMsE,KAAKimB,EAAagH,UAAUC,OAAO50B,GAAMA,EAAEgR,YACrErB,kBAAmBvM,MAAMsE,KAAKimB,EAAagH,UAAUC,OAAO50B,IAAOA,EAAEgR,YACrEvB,6BACAI,eACA/H,aAGF,EAEA,GAAY,KAAM,CACtBusB,OAAO,IAAAlf,KAAI,CACT3E,MAAO,QACP6E,SAAU,OACVwf,SAAU,EACVC,UAAW,QAEbC,iBAAiB,IAAA5f,KAAI,CACnB3E,MAAO,kBACPwkB,QAAS,SAEXV,eAAe,IAAAnf,KAAI,CACjB3E,MAAO,gBACPykB,OAAQ,UACRC,KAAM,EACN7f,SAAU,WAEZ8f,gBAAgB,IAAAhgB,KAAI,CAClB3E,MAAO,iBACP4kB,YAAa,cACbC,UAAW,iBACXhgB,SAAU,SACVigB,WAAY,WAEdC,oBAAoB,IAAApgB,KAAI,CACtB3E,MAAO,qBACPglB,cAAe,eAGbnC,GAA0C,CAACoC,EAAKxI,EAAME,EAAWD,EAAO4D,EAAexf,EAAYga,EAAUqC,KACjH,IAAI+H,EAAOzI,EACP0I,EAA6B,aAAdxI,EAA2B,EAAID,EAAQ,EAC1D,MAAM0I,EAAa5zB,KAAKmW,MAAMsd,EAAI7oB,GAAKsB,EAAmB3B,OAAO4B,mBACjE,IAAIlH,EACJ,KAAOyuB,GAAM,CACX,MAAMG,EAAOH,EAEb,GADAA,OAAO,EACHC,IAAiBC,EAAY,CAC/B3uB,EAAQ4uB,EACR,KACF,CACA,MAAMvE,EAAyB,aAAdnE,EAA2B0I,EAAK9lB,SAAW8lB,EAAKtE,SAAW,GAC5E,IAAK,MAAMuE,KAASxE,EAAU,CAC5B,MAAMyE,EAAS1E,GAAQyE,EAAMl2B,MAAO0R,EAAYga,EAAUwF,GACpDkF,EAAO3E,GAAQyE,EAAMl2B,MAAQk2B,EAAMniB,MAAOrC,EAAYga,EAAUwF,GACtE,GAAIiF,GAAUN,EAAI9oB,GAAK8oB,EAAI9oB,EAAIqpB,EAAM,CACnCN,EAAOI,EACP,MAAMG,EAAkBtI,EAAaniB,IAAIsqB,GACpCG,GAAoBA,EAAgBjlB,WAAailB,EAAgBziB,MAAM,KAAOsiB,IACjFH,GAA6C,aAAdxI,EAA2B,GAAK,GAEjE,KACF,CACF,CACF,CACA,OAAOlmB,CAAK,ECzORivB,IAAqB,IAAAC,OACzB,EAAGpnB,OAAMqnB,cAAa9kB,aAAY+kB,kBAAiBC,mBAAkBC,0BACnE,MAAM/kB,GAAS,IAAAC,YAAW,IACpBxO,EAAQ,GACRuzB,GAAW,IAAAzhB,gBAAe,QAAf,CAAwBzD,GACnCmC,EAAe1E,EAAK2E,sBAAsBpC,GAChD,IAAIqB,EAAYc,EAAaM,KAAON,EAAaO,OACjD,MAAMtB,EAAY3D,EAAKkF,eAwCvB,GAvCkB,UAAdvB,IACGe,EAAaO,SAChBrB,EAAYc,EAAaM,OAG7B9Q,EAAMR,MACY,IAAA4N,MAAK,MAAO,CAAE0B,UAAWP,EAAOilB,aAAc1mB,SAAU,CACtE4C,EACA,MACA6jB,EAASziB,KACTyiB,EAASxiB,OACT,aACAtB,EACA,MACG,YAEH2jB,GACFpzB,EAAMR,MACY,IAAAqN,KAAI,EAAA4mB,QAAS,CAAE/kB,QAAS0kB,EAAiBM,UAAW,MAAO5mB,UAA0B,IAAAM,MAAK,MAAO,CAAEN,SAAU,EAC3G,IAAAD,KAAI,EAAA8mB,KAAM,CAAE5tB,KAAM,KAAM4M,KAAM,iBAC9B,IAAAvF,MAAK,MAAO,CAAE0B,UAAWP,EAAOilB,aAAc1mB,SAAU,EACtD,IAAAD,KAAI,EAAA8mB,KAAM,CAAE5tB,KAAM,KAAM4M,KAAM,oBAC9C,KACgB,IAAA9F,KAAI,OAAQ,CAAEiC,UAAWP,EAAOqlB,iBAAkB9mB,SAAUsmB,EAAgBlqB,UAAUkqB,EAAgBS,YAAY,KAAO,MACzH,IAAAhnB,KACd,EAAAinB,WACA,CACEhlB,UAAWP,EAAOwlB,gBAClBphB,KAAM,QACN5M,KAAM,KACN0H,QAAS6lB,EACTU,QAAS,uBACT,aAAc,gCAId,aAGRb,EAAa,CACf,MAAMxjB,EAAetB,EAAa,EAAItP,KAAK8R,MAAasiB,EAAY/kB,KAAKsC,MAAQrC,EAAhC,KAA+C,IAAM,EAChG4lB,EAAWtkB,EAAe,EAAI,MAAQ,qBAC5C3P,EAAMR,MACY,IAAAqN,KAAI,EAAA4mB,QAAS,CAAE/kB,QAASykB,EAAY5lB,MAAOmmB,UAAW,MAAO5mB,UAA0B,IAAAM,MAAK,MAAO,CAAEN,SAAU,EAC7G,IAAAD,KAAI,EAAA8mB,KAAM,CAAE5tB,KAAM,KAAM4M,KAAM,iBAC9B,IAAAvF,MAAK,MAAO,CAAE0B,UAAWP,EAAOilB,aAAc1mB,SAAU,EACtD,IAAAD,KAAI,EAAA8mB,KAAM,CAAE5tB,KAAM,KAAM4M,KAAMshB,IAC9C,IACAtkB,EACA,cACgB,IAAA9C,KACd,EAAAinB,WACA,CACEhlB,UAAWP,EAAOwlB,gBAClBphB,KAAM,QACN5M,KAAM,KACN0H,QAAS4lB,EACTW,QAAS,eACT,aAAc,wBAId,SAEZ,CACA,OAAuB,IAAAnnB,KAAI,MAAO,CAAEiC,UAAWP,EAAO2lB,SAAUpnB,SAAU9M,GAAQ,IAGtFizB,GAAmBkB,YAAc,qBACjC,MAAM,GAAaliB,IAAU,CAC3BuhB,cAAc,IAAAthB,KAAI,CAChB3E,MAAO,eACPwkB,QAAS,cACTqC,WAAY,SACZ1I,WAAYzZ,EAAMwZ,OAAOC,WAAWC,UACpC0I,aAAcpiB,EAAMqiB,MAAMD,aAAa,GACvCE,QAAStiB,EAAMuiB,QAAQ,GAAK,GAC5BniB,SAAUJ,EAAMK,WAAWC,UAAUF,SACrCoiB,WAAYxiB,EAAMK,WAAWoiB,iBAC7BC,WAAY1iB,EAAMK,WAAWC,UAAUoiB,WACvCrhB,MAAOrB,EAAMwZ,OAAO3a,KAAK6a,YAE3BoI,iBAAiB,IAAA7hB,KAAI,CACnB3E,MAAO,kBACPglB,cAAe,cACfqC,OAAQ3iB,EAAMuiB,QAAQ,EAAG,MAE3BN,UAAU,IAAAhiB,KAAI,CACZ6f,QAAS,OACTqC,WAAY,SACZS,eAAgB,SAChBD,OAAQ,UAEVhB,kBAAkB,IAAA1hB,KAAI,CACpB3E,MAAO,mBACPqF,SAAU,QACVR,SAAU,SACV0iB,aAAc,WACdzC,WAAY,SACZ0C,WAAY9iB,EAAMuiB,QAAQ,QCzGxBQ,GAAa,EACjBlpB,OACAuc,WACAC,WACA8B,gBACAgF,cACAC,cACAC,gBACA7E,kBACAJ,YACAne,aACA+oB,eACA5B,mBACAC,sBACA9I,cACAiF,qBACAjjB,6BACAC,aACAG,eACA/H,SACA6lB,eACAgF,sBAEA,MAAMnhB,EAAS,MACR2mB,EAAQC,IAAa,IAAA/qB,aACrBgrB,EAAeC,IAAoB,IAAAjrB,aACnCmlB,EAAmB+F,IAAwB,IAAAlrB,UAAS,IACpDolB,EAAwB+F,IAA6B,IAAAnrB,aACrDkgB,EAAgBkL,IAAqB,IAAAprB,UAAS,GAsBrD,IArBA,IAAAZ,YAAU,KACR,IAAIW,EAAII,EAAIkrB,EACZ,GAAI3pB,EAAM,CACR,IAII4pB,EAJAC,EAAU7pB,EAAKqF,YACfykB,EAAqBD,EAAQ72B,OAAS62B,EAAQ,GAAG,GAAGjlB,MAAQ,EAC5DmlB,EAA0BF,EAAQ72B,OAAS62B,EAAQ,GAAG,GAAGvkB,gBAAa,EACtE0kB,EAAkBF,EAEtB,GAAIX,EAAc,CAChB,MAAOc,EAASC,GAAWlqB,EAAKmqB,kBAAkBhB,GAClDU,EAAUK,EACVN,EAAiBK,EACjBD,EAA0G,OAAvFL,EAA0D,OAApDlrB,EAA0B,OAApBJ,EAAK6rB,EAAQ,SAAc,EAAS7rB,EAAG,SAAc,EAASI,EAAGmG,OAAiB+kB,EAAK,CACxH,CACAN,EAAUQ,GACVN,EAAiBK,GACjBJ,EAAqBM,GACrBL,EAA0BM,GAC1BL,EAAkBM,EACpB,IACC,CAAChqB,EAAMmpB,KACLC,EACH,OAAO,KAET,MAAMgB,EAAoB,CACxBpqB,OACAuc,WACAC,WACA8B,gBACAgF,cACAC,cACAC,gBACA7E,kBACAJ,YACAne,aACAse,cACA+E,oBACAC,yBACAlF,iBACAmF,qBACA/E,eACAgF,kBACAljB,6BACAC,aACA5H,SACA+H,gBAEF,IAAI4gB,EAAS,KAuCb,OAtCqB,MAAjB4H,OAAwB,EAASA,EAAct2B,QACjD0uB,GAAyB,IAAApgB,MAAK,EAAAC,SAAU,CAAEP,SAAU,EAClC,IAAAM,MAAK,MAAO,CAAE0B,UAAWP,EAAO4nB,sBAAuBrpB,SAAU,EAC/D,IAAAM,MAAK,MAAO,CAAE0B,UAAWP,EAAO2jB,eAAgBplB,SAAU,CACxE,WACgB,IAAAD,KAAI,EAAA8mB,KAAM,CAAE7kB,UAAWP,EAAO+jB,mBAAoB3f,KAAM,mBAE1D,IAAA9F,KACdsiB,GACA,IACK+G,EACHlM,KAAMoL,EAAcA,EAAct2B,OAAS,GAAG,GAC9CmrB,MAAOmL,EAAct2B,OACrBorB,UAAW,UACXzd,YAAY,QAIF,IAAAW,MAAK,MAAO,CAAE0B,UAAWP,EAAO4nB,sBAAuBrpB,SAAU,EAC/D,IAAAM,MAAK,MAAO,CAAE0B,WAAW,IAAAsnB,IAAG7nB,EAAO2jB,eAAgB3jB,EAAO8nB,sBAAuBvpB,SAAU,EACzF,IAAAD,KAAI,EAAA8mB,KAAM,CAAE7kB,UAAWP,EAAO+jB,mBAAoB3f,KAAM,aACxE,cAEc,IAAA9F,KACdsiB,GACA,IACK+G,EACHlM,KAAMkL,EAAO,GAAG,GAChBjL,MAAOiL,EAAOp2B,OACdorB,UAAW,WACXzd,YAAY,WAKD,MAAVyoB,OAAiB,EAASA,EAAOp2B,UAC1C0uB,GAAyB,IAAA3gB,KAAIsiB,GAAkB,IAAK+G,EAAmBlM,KAAMkL,EAAO,GAAG,GAAIjL,MAAOiL,EAAOp2B,OAAQorB,UAAW,eAEvG,IAAA9c,MAAK,MAAO,CAAE0B,UAAWP,EAAO6iB,MAAOtkB,SAAU,EACtD,IAAAD,KACdomB,GACA,CACEnnB,OACAqnB,YAAa1I,EACb2I,gBAAiB6B,EACjB5mB,WAAYic,EACZ+I,mBACAC,wBAGJ9F,IACE,EAEA,GAAY,KAAM,CACtB4D,OAAO,IAAAlf,KAAI,CACT3E,MAAO,QACP6E,SAAU,OACVwf,SAAU,EACVC,UAAW,QAEbsE,uBAAuB,IAAAjkB,KAAI,CACzB3E,MAAO,wBACPwkB,QAAS,OACTrf,aAAiBzH,EAAmB3B,OAAO4B,iBAA7B,OAEhBgnB,gBAAgB,IAAAhgB,KAAI,CAClB3E,MAAO,iBACP4kB,YAAa,cACbC,UAAW,iBACXhgB,SAAU,SACVigB,WAAY,WAEdgE,sBAAsB,IAAAnkB,KAAI,CACxB3E,MAAO,uBACP8c,UAAW,UAEbiI,oBAAoB,IAAApgB,KAAI,CACtB3E,MAAO,qBACPglB,cAAe,e,eC9HnB,SAAS+D,GAAcC,EAAOzqB,EAAMoe,EAAY,YAC9C,IAAI/f,EACJ,MAAMqsB,EAAkC,YAAdtM,EAA0B,WAAa,UAC3DgL,EAAS,GACTvH,EAAQ,CACZ,CAAE8I,cAAU,EAAQlmB,MAAOgmB,EAAOxZ,MAAO,IAE3C,KAAO4Q,EAAM7uB,QAAQ,CACnB,MAAMgG,EAAO6oB,EAAMI,QACb2I,EAAU5xB,EAAKyL,MAAMomB,SAAS55B,GAAMA,EAAEuT,cACtCsmB,EAAU,CAEdlmB,MAAO5L,EAAKyL,MAAMjK,QAAO,CAACC,EAAKxJ,IAAMwJ,EAAMxJ,EAAE2T,OAAO,GACpDJ,YAAaomB,EAEb5pB,SAAU,GACVwhB,QAAS,GACT3xB,MAAO,EACPogB,MAAOjY,EAAKiY,OAId,GAFAmY,EAAOpwB,EAAKiY,OAASmY,EAAOpwB,EAAKiY,QAAU,GAC3CmY,EAAOpwB,EAAKiY,OAAOvd,KAAKo3B,GACpB9xB,EAAK2xB,SAAU,CACjBG,EAAQJ,GAAqB,CAAC1xB,EAAK2xB,UACnC,MAAMI,GAAsD,OAAlC1sB,EAAKrF,EAAK2xB,SAASvM,SAAsB,EAAS/f,EAAG7D,QAAO,CAACC,EAAKqsB,IACnFrsB,EAAMqsB,EAAKliB,OACjB,KAAO,EACVkmB,EAAQj6B,MAAQmI,EAAK2xB,SAAS95B,MAAQk6B,EACtC/xB,EAAK2xB,SAASvM,GAAW1qB,KAAKo3B,EAChC,CACA,MAAME,EAAYhyB,EAAKyL,MAAMomB,SAAS55B,GAAMA,EAAEmtB,IAAc,KACtD6M,GAAa,KAAAC,SAAQF,GAAY91B,GAAM8K,EAAKuE,SAASrP,EAAEsP,YAAY,MACzE,IAAK,MAAMmD,KAAKlW,OAAOm0B,OAAOqF,GAC5BpJ,EAAMnuB,KAAK,CAAEi3B,SAAUG,EAASrmB,MAAOkD,EAAGsJ,MAAOjY,EAAKiY,MAAQ,GAElE,CASA,MARkB,YAAdmN,IACFgL,EAAO+B,UACP/B,EAAO/sB,SAAQ,CAAC4U,EAAOnhB,KACrBmhB,EAAM5U,SAASiG,IACbA,EAAK2O,MAAQnhB,CAAK,GAClB,KAGCs5B,CACT,CCtCA,MAAMgC,GACJ,WAAAjkB,CAAYpW,GAGV0W,KAAK1W,IAAsB,IAAImL,IAC/BuL,KAAK1W,IAAMA,GAAuB,IAAImL,GACxC,CACA,GAAAO,CAAI6F,GACF,OAAOmF,KAAK1W,IAAI0L,IAAI6F,EACtB,CACA,IAAAvF,GACE,OAAO0K,KAAK1W,IAAIgM,MAClB,CACA,MAAA6oB,GACE,OAAOne,KAAK1W,IAAI60B,QAClB,CACA,IAAA3rB,GACE,OAAOwN,KAAK1W,IAAIkJ,IAClB,CACA,kBAAAyrB,CAAmBpjB,EAAML,GACvB,MAAMopB,EAAS,IAAInvB,IAAIuL,KAAK1W,KACtBm2B,EAAkBzf,KAAK1W,IAAI0L,IAAI6F,GAC/BgpB,EAAY,IAAKpE,EAAiBjlB,aACxC,IAAK,MAAMspB,KAASrE,EAAgBziB,MAClC4mB,EAAO/uB,IAAIivB,EAAOD,GAEpB,OAAO,IAAIF,GAAaC,EAC1B,CACA,qBAAA1F,CAAsB1jB,GACpB,MAAMopB,EAAS,IAAInvB,IAAIuL,KAAK1W,KAC5B,IAAK,MAAMuR,KAAQmF,KAAK1W,IAAIgM,OAAQ,CAClC,MACMuuB,EAAY,IADM7jB,KAAK1W,IAAI0L,IAAI6F,GACGL,aACxCopB,EAAO/uB,IAAIgG,EAAMgpB,EACnB,CACA,OAAO,IAAIF,GAAaC,EAC1B,EAEF,MAAMG,GACJ,WAAArkB,CAAYskB,GACVhkB,KAAK1W,IAAsB,IAAImL,IAC/BuL,KAAKgkB,UAAY,SACC,IAAdA,IACFhkB,KAAKgkB,UAAYA,EAErB,CACA,OAAAC,CAAQxN,GACN,IAAI7f,EACJ,MAAMwjB,EAAQ,CAAC3D,GACf,KAAO2D,EAAM7uB,QAAQ,CACnB,MAAMgsB,EAAU6C,EAAMI,SACQ,OAAzB5jB,EAAK2gB,EAAQwD,cAAmB,EAASnkB,EAAGrL,SAC/CyU,KAAKkkB,QAAQ3M,EAASA,EAAQwD,QAAQ,IAEpCxD,EAAQhe,SAAShO,QACnB6uB,EAAMY,WAAWzD,EAAQhe,SAE7B,CACF,CAIA,OAAA2qB,CAAQrpB,EAAMspB,GACZ,GAAIA,GAAUtpB,EAAKsC,MAAQgnB,EAAOhnB,MAAQ6C,KAAKgkB,WAAwC,IAA3BG,EAAO5qB,SAAShO,OAC1E,GAAIyU,KAAK1W,IAAIoJ,IAAIyxB,GAAS,CACxB,MAAMC,EAASpkB,KAAK1W,IAAI0L,IAAImvB,GAC5BnkB,KAAK1W,IAAIuL,IAAIgG,EAAMupB,GACnBA,EAAOpnB,MAAM/Q,KAAK4O,EACpB,KAAO,CACL,MAAMupB,EAAS,CAAEpnB,MAAO,CAACmnB,EAAQtpB,GAAOL,WAAW,GACnDwF,KAAK1W,IAAIuL,IAAIsvB,EAAQC,GACrBpkB,KAAK1W,IAAIuL,IAAIgG,EAAMupB,EACrB,CAEJ,CACA,eAAAC,GACE,OAAO,IAAIV,GAAa3jB,KAAK1W,IAC/B,EAuCF,MAAMg7B,GACJ,WAAA5kB,CAAYnH,EAAMge,EAAS7X,GAAQ,IAAA6lB,gBACjC,IAAI3tB,EAAII,EAAIkrB,EACZliB,KAAKzH,KAAOA,EACZyH,KAAKuW,QAAUA,EACf,MAAMiO,EAjCV,SAAqBjsB,GACnB,MAAMksB,EAAS,CACb,CAAC,QAAS,CAAC,EAAAC,UAAUC,OAAQ,EAAAD,UAAUE,OACvC,CAAC,QAAS,CAAC,EAAAF,UAAUlgB,SACrB,CAAC,QAAS,CAAC,EAAAkgB,UAAUlgB,SACrB,CAAC,OAAQ,CAAC,EAAAkgB,UAAUlgB,UAEhBqgB,EAAgB,GAChBC,EAAkB,GACxB,IAAK,MAAMp2B,KAAS+1B,EAAQ,CAC1B,MAAOrlB,EAAM2lB,GAASr2B,EAChBs2B,EAAqB,MAARzsB,OAAe,EAASA,EAAKksB,OAAOQ,MAAMrjB,GAAMA,EAAExC,OAASA,IACzE4lB,EAIAD,EAAMG,SAASF,EAAWG,OAC7BL,EAAgB74B,KAAK,CAAEmT,OAAMgmB,cAAeL,EAAOI,KAAMH,EAAWG,OAJpEN,EAAc54B,KAAKmT,EAMvB,CACA,GAAIylB,EAAct5B,OAAS,GAAKu5B,EAAgBv5B,OAAS,EACvD,MAAO,CACLu5B,kBACAD,gBAIN,CAMwBQ,CAAY9sB,GAChC,GAAIisB,EACF,MAAM,IAAI9f,MA5ChB,SAAqC8f,GACnC,OAAIA,EAAYK,cAAct5B,OACrB,2BAA2Bi5B,EAAYK,cAAcr3B,KAAK,QAE/Dg3B,EAAYM,gBAAgBv5B,OACvB,kCAAkCi5B,EAAYM,gBAAgBx7B,KAAKsY,GAAM,GAAGA,EAAExC,iBAAiBwC,EAAEujB,sBAAsBvjB,EAAEwjB,cAAc53B,KAAK,YAAWA,KAAK,QAE9J,EACT,CAoCsB83B,CAA4Bd,IAQ9C,GANAxkB,KAAKulB,WAAahtB,EAAKksB,OAAOQ,MAAMrjB,GAAiB,UAAXA,EAAExC,OAC5CY,KAAKwlB,WAAajtB,EAAKksB,OAAOQ,MAAMrjB,GAAiB,UAAXA,EAAExC,OAC5CY,KAAKylB,WAAaltB,EAAKksB,OAAOQ,MAAMrjB,GAAiB,UAAXA,EAAExC,OAC5CY,KAAK0lB,UAAYntB,EAAKksB,OAAOQ,MAAMrjB,GAAiB,SAAXA,EAAExC,OAC3CY,KAAK2lB,gBAAkBptB,EAAKksB,OAAOQ,MAAMrjB,GAAiB,eAAXA,EAAExC,OACjDY,KAAK4lB,eAAiBrtB,EAAKksB,OAAOQ,MAAMrjB,GAAiB,cAAXA,EAAExC,QAC3CY,KAAKylB,YAAczlB,KAAK0lB,cAAgB1lB,KAAKylB,aAAczlB,KAAK0lB,WACnE,MAAM,IAAIhhB,MACR,mGAGJ,MAAMmhB,EAA6G,OAA/F3D,EAAmE,OAA7DlrB,EAA+B,OAAzBJ,EAAKoJ,KAAKulB,iBAAsB,EAAS3uB,EAAGwtB,aAAkB,EAASptB,EAAGmuB,WAAgB,EAASjD,EAAG0C,KAClIiB,GACF7lB,KAAK8lB,uBAAwB,IAAAC,qBAAoB,CAAEr3B,MAAOsR,KAAKulB,WAAY7mB,UAC3EsB,KAAKgmB,aAAeH,EAAWtoB,MAAQ,KAEvCyC,KAAK8lB,sBAAyB3oB,IAAU,CACtCI,KAAMJ,EAAQ,GACdzW,QAAS,IAEXsZ,KAAKgmB,aAAe,IAAI,IAAI1zB,IAAI0N,KAAKulB,WAAWpH,UAElDne,KAAK9C,uBAAwB,IAAA6oB,qBAAoB,CAC/Cr3B,MAAOsR,KAAKylB,WACZ/mB,SAEJ,CACA,gBAAA9E,GACE,OAAOqsB,QAAQjmB,KAAK2lB,iBAAmB3lB,KAAK4lB,eAC9C,CACA,QAAA9oB,CAASzU,GACP,OAAO2X,KAAK8lB,sBAAsB9lB,KAAKulB,WAAWpH,OAAO91B,IAAQkV,IACnE,CACA,QAAA2oB,CAAS79B,GACP,OAAO2X,KAAKwlB,WAAWrH,OAAO91B,EAChC,CACA,QAAA89B,CAAS99B,GACP,OAAO+9B,GAAcpmB,KAAKylB,WAAYp9B,EACxC,CACA,aAAAg+B,CAAch+B,GACZ,OAAO+9B,GAAcpmB,KAAK2lB,gBAAiBt9B,EAC7C,CACA,OAAAi+B,CAAQj+B,GACN,OAAO+9B,GAAcpmB,KAAK0lB,UAAWr9B,EACvC,CACA,YAAAk+B,CAAal+B,GACX,OAAO+9B,GAAcpmB,KAAK4lB,eAAgBv9B,EAC5C,CACA,cAAAgV,CAAehV,GACb,OAAO2X,KAAK9C,sBAAsB8C,KAAKsmB,QAAQj+B,GACjD,CACA,eAAAm+B,GACE,OAAOxmB,KAAKgmB,YACd,CACA,YAAAvoB,GACE,OAAQuC,KAAKylB,WAAWrB,OAAOhJ,MAC7B,KAAKlH,GAAWuS,MACd,MAAO,MACT,KAAKvS,GAAWwS,YACd,MAAO,OAEX,MAAO,OACT,CACA,SAAA9oB,GAEE,OADAoC,KAAK2mB,aACE3mB,KAAK2hB,MACd,CACA,iBAAAe,CAAkB1oB,GAChB,MAAM4sB,EAAQ5mB,KAAK6mB,kBAAkB7sB,GACrC,KAAe,MAAT4sB,OAAgB,EAASA,EAAMr7B,QACnC,MAAO,CAAC,GAAI,ID7OlB,IAAoCgN,ECiPhC,MAAO,EDjPyBA,EC+OWyH,KD7OtC+iB,GAET,SAA2BC,GACzB,OAAOA,EAAM15B,KAAKqL,IAChB,IAAIiC,EAAII,EACR,KAA0B,OAAnBJ,EAAKjC,EAAEomB,cAAmB,EAASnkB,EAAGrL,QAC3C,OAAOoJ,EAET,MAAMmyB,EAAU,IACXnyB,EACH4E,SAAU,IAEN6gB,EAAQ,CACZ,CAAEkF,MAAOwH,EAAS3C,OAAQxvB,EAAEomB,QAAQ,KAEtC,KAAOX,EAAM7uB,QAAQ,CACnB,MAAMgG,EAAO6oB,EAAMI,QACbuM,EAAU,IACXx1B,EAAK4yB,OACR5qB,SAAUhI,EAAK+tB,MAAQ,CAAC/tB,EAAK+tB,OAAS,GACtCvE,QAAS,IAEPxpB,EAAK+tB,QACPyH,EAAQ5pB,MAAQ5L,EAAK+tB,MAAMniB,MAC3B5L,EAAK+tB,MAAMvE,QAAU,CAACgM,KAEU,OAA7B/vB,EAAKzF,EAAK4yB,OAAOpJ,cAAmB,EAAS/jB,EAAGzL,SACnD6uB,EAAMnuB,KAAK,CAAEqzB,MAAOyH,EAAS5C,OAAQ5yB,EAAK4yB,OAAOpJ,QAAQ,IAE7D,CACA,OAAO+L,CAAO,GAElB,CAjCmBE,CC8OqBJ,GD7OPruB,EAAM,YC8OnBwqB,GAAc6D,EAAO5mB,MAEvC,CACA,iBAAA6mB,CAAkB7sB,GAEhB,OADAgG,KAAK2mB,aACE3mB,KAAKinB,gBAAgBjtB,EAC9B,CACA,eAAAqqB,GAEE,OADArkB,KAAK2mB,aACE3mB,KAAKmX,YACd,CACA,UAAAwP,GACE,IAAK3mB,KAAK2hB,OAAQ,CAChB,MAAOA,EAAQsF,EAAiB9P,GA3PtC,SAA2B+P,EAAW3Q,GACpC,MAAMoL,EAAS,GACf,IACIwC,EADAxnB,EAAS,EAEb,MAAMqpB,EAAe,CAAC,EACtB,IAAK,IAAIx8B,EAAI,EAAGA,EAAI09B,EAAU3uB,KAAKhN,OAAQ/B,IAAK,CAC9C,MAAM21B,EAAe+H,EAAUhB,SAAS18B,GAClC29B,EAAY39B,EAAI,EAAI09B,EAAUhB,SAAS18B,EAAI,QAAK,EAEtD,GADAm4B,EAAOxC,GAAgBwC,EAAOxC,IAAiB,GAC3CgI,GAAaA,GAAahI,EAAc,CAC1C,MAAMiI,EAAczF,EAAOxC,GAAcwC,EAAOxC,GAAc5zB,OAAS,GACvEoR,EAASyqB,EAAYh+B,MAAQ89B,EAAUf,SAASiB,EAAYrqB,YAAY,IAAMmqB,EAAUb,cAAce,EAAYrqB,YAAY,IAC9HonB,EAASiD,EAAYrM,QAAQ,EAC/B,CACA,MAAMsI,EAAU,CACdtmB,YAAa,CAACvT,GACd2T,MAAO+pB,EAAUf,SAAS38B,GAAK09B,EAAUb,cAAc78B,GACvDqU,WAAYqpB,EAAUttB,mBAAqBstB,EAAUb,cAAc78B,QAAK,EACxEJ,MAAOuT,EACPoe,QAASoJ,GAAU,CAACA,GACpB5qB,SAAU,GACViQ,MAAO2V,GAEL6G,EAAakB,EAAUpqB,SAAStT,IAClCw8B,EAAakB,EAAUpqB,SAAStT,IAAIyC,KAAKo3B,GAEzC2C,EAAakB,EAAUpqB,SAAStT,IAAM,CAAC65B,GAErCc,GACFA,EAAO5qB,SAAStN,KAAKo3B,GAEvBc,EAASd,EACT1B,EAAOxC,GAAclzB,KAAKo3B,EAC5B,CACA,MAAMgE,EAAwB,IAAItD,GAA+B,MAAXxN,OAAkB,EAASA,EAAQ+Q,qBAIzF,OAHe,MAAX/Q,OAAkB,EAASA,EAAQrd,aACrCmuB,EAAsBpD,QAAQtC,EAAO,GAAG,IAEnC,CAACA,EAAQqE,EAAcqB,EAAsBhD,kBACtD,CAoNsDkD,CAAkBvnB,KAAMA,KAAKuW,SAC7EvW,KAAK2hB,OAASA,EACd3hB,KAAKinB,gBAAkBA,EACvBjnB,KAAKmX,aAAeA,CACtB,CACF,EAEF,SAASiP,GAAc13B,EAAOrG,GAC5B,IAAKqG,EACH,OAAO,EAGT,OADkC,iBAAVrG,EAAqB,CAACA,GAASA,GACrC0K,QAAO,CAACC,EAAKw0B,IACtBx0B,EAAMtE,EAAMyvB,OAAOqJ,IACzB,EACL,C,0BCpQA,MAAMC,GAAmB,EACvBn2B,SACAo2B,YACAruB,eACAsuB,kBACAC,iBACAC,UACA/Q,YACAgR,oBACAC,kBACA9Q,cACA+Q,sBACAC,eACAC,sBACAC,WACAC,aACAjM,kBACAhF,mBAEA,MAAMnc,GAAS,IAAAC,YAAW,KACnBotB,EAAaC,GA4JtB,SAAwBh3B,EAAQo2B,GAC9B,MAAOa,EAAkBC,IAAuB,IAAA3xB,UAASvF,GACnDm3B,GAAa,EAAAC,GAAA,GAAYp3B,GAa/B,OAZA,EAAAq3B,GAAA,IACE,KACEjB,EAAUa,EAAiB,GAE7B,IACA,CAACA,KAEH,IAAAtyB,YAAU,KACJwyB,IAAen3B,GAAUA,IAAWi3B,GACtCC,EAAoBl3B,EACtB,GACC,CAACA,EAAQm3B,EAAYF,IACjB,CAACA,EAAkBC,EAC5B,CA5KwCI,CAAet3B,EAAQo2B,GACvDlqB,EAAyB,KAAhB6qB,GAAqC,IAAA/uB,KAClD,EAAAuvB,OACA,CACE5uB,KAAM,QACNpN,KAAM,OACN2F,KAAM,KACN0H,QAAS,KACPwtB,EAAU,IACVY,EAAe,GAAG,EAEpB/uB,SAAU,UAEV,KACJ,OAAuB,IAAAM,MAAK,MAAO,CAAE0B,WAAW,IAAAsnB,IAAG7nB,EAAOW,OAAQ,CAAE,CAACX,EAAOitB,cAAeA,IAAiB1uB,SAAU,EACpG,IAAAD,KAAI,MAAO,CAAEiC,UAAWP,EAAO8tB,eAAgBvvB,UAA0B,IAAAD,KACvF,EAAAyvB,MACA,CACE5rB,MAAOkrB,GAAe,GACtBW,SAAWz/B,IACT++B,EAAe/+B,EAAE0/B,cAAc9rB,MAAM,EAEvC+rB,YAAa,YACb1rB,cAGY,IAAA3D,MAAK,MAAO,CAAE0B,UAAWP,EAAOmuB,eAAgB5vB,SAAU,CACxEwuB,IAAmC,IAAAzuB,KACjC,EAAAuvB,OACA,CACEO,QAAS,YACTv8B,KAAM,UACN2F,KAAM,KACNyH,KAAM,cACNwmB,QAAS,iCACTvmB,QAAS,KACP2tB,GAAS,EAEXtsB,UAAWP,EAAOquB,cAClB,aAAc,oCAGF,IAAA/vB,KAAIgwB,GAAmB,CAAEnsB,MAAO8Z,EAAa+R,SAAUhB,EAAqBI,gBAC5E,IAAAvuB,MAAK,EAAA0vB,YAAa,CAAEhuB,UAAWP,EAAOquB,cAAe9vB,SAAU,EAC7D,IAAAD,KACd,EAAAuvB,OACA,CACEO,QAAS,YACTv8B,KAAM,UACN2F,KAAM,KACNiuB,QAAS,oBACTvmB,QAAS,KACPiiB,EAAgBhF,EAAa+G,uBAAsB,GAAO,EAE5D,aAAc,oBACdjkB,KAAM,oBACNuvB,SAAUnwB,IAAiB+a,GAAaqV,YAG5B,IAAAnwB,KACd,EAAAuvB,OACA,CACEO,QAAS,YACTv8B,KAAM,UACN2F,KAAM,KACNiuB,QAAS,sBACTvmB,QAAS,KACPiiB,EAAgBhF,EAAa+G,uBAAsB,GAAM,EAE3D,aAAc,sBACdjkB,KAAM,kBACNuvB,SAAUnwB,IAAiB+a,GAAaqV,eAI9B,IAAAnwB,KACd,EAAAowB,iBACA,CACEl3B,KAAM,KACNg3B,SAAUnwB,IAAiB+a,GAAaqV,SACxClT,QAASoT,GACTxsB,MAAO2Z,EACPkS,SAAUlB,EACVvsB,UAAWP,EAAOquB,iBAGN,IAAA/vB,KACd,EAAAowB,iBACA,CACEl3B,KAAM,KACN+jB,QAASqT,GAAehC,EAAgBO,GACxChrB,MAAO9D,EACP2vB,SAAUrB,IAGdO,IAAuC,IAAA5uB,KAAI,MAAO,CAAEiC,UAAWP,EAAO6uB,cAAetwB,SAAU2uB,SAE/F,EAEN,SAASoB,GAAkBQ,GACzB,MAAM9uB,GAAS,IAAAC,YAAW,IAC1B,IAAI8uB,GAAuB,IAAAlwB,MAAK,EAAAmwB,KAAM,CAAEzwB,SAAU,EAChC,IAAAD,KAAI,EAAA0wB,KAAKC,KAAM,CAAEjwB,MAAO,kBAAmBE,QAAS,IAAM4vB,EAAMd,SAAS1U,GAAY4V,iBACrF,IAAA5wB,KAAI,EAAA0wB,KAAKC,KAAM,CAAEjwB,MAAO,WAAYE,QAAS,IAAM4vB,EAAMd,SAAS1U,GAAYsE,iBAEhG,MAAMuR,EAAgB,CACpB,CAAC7V,GAAYsE,YAAa5d,EAAOovB,gBACjC,CAAC9V,GAAY4V,cAAelvB,EAAOqvB,kBACnC,CAAC7V,GAAgBmE,gBAAiB3d,EAAOsvB,uBACzC,CAAC9V,GAAgBkE,SAAU1d,EAAOuvB,qBAClCT,EAAM3sB,QAAUnC,EAAOovB,gBACzB,IAAII,GAA2B,IAAAlxB,KAAI,OAAQ,CAAEiC,WAAW,IAAAsnB,IAAG7nB,EAAOyvB,SAAUN,KAY5E,OAXIL,EAAM1B,aACR2B,GAAuB,IAAAlwB,MAAK,EAAAmwB,KAAM,CAAEzwB,SAAU,EAC5B,IAAAD,KAAI,EAAA0wB,KAAKC,KAAM,CAAEjwB,MAAO,yBAA0BE,QAAS,IAAM4vB,EAAMd,SAASxU,GAAgBkE,YAChG,IAAApf,KAAI,EAAA0wB,KAAKC,KAAM,CAAEjwB,MAAO,4BAA6BE,QAAS,IAAM4vB,EAAMd,SAASxU,GAAgBmE,qBAErH6R,GAA2B,IAAA3wB,MAAK,MAAO,CAAE0B,WAAW,IAAAsnB,IAAG7nB,EAAO0vB,aAAcP,GAAgB5wB,SAAU,EACpF,IAAAD,KAAI,MAAO,CAAEC,SAAU,qBACvB,IAAAD,KAAI,MAAO,CAAEC,SAAU,QACvB,IAAAD,KAAI,MAAO,CAAEC,SAAU,uBAGpB,IAAAD,KAAI,EAAAqxB,SAAU,CAAEC,QAASb,EAAMxwB,UAA0B,IAAAD,KAC9E,EAAAuvB,OACA,CACEO,QAAS,YACTv8B,KAAM,UACN2F,KAAM,KACNiuB,QAAS,sBACTvmB,QAAS,OAETqB,UAAWP,EAAOquB,cAClB,aAAc,sBACd9vB,SAAUixB,KAGhB,CACA,MAAMb,GAAe,CACnB,CAAExsB,MAAO,OAAQ0tB,YAAa,kBAAmB5wB,KAAM,cACvD,CAAEkD,MAAO,QAAS0tB,YAAa,mBAAoB5wB,KAAM,gBAE3D,SAAS2vB,GAAevzB,EAAO8xB,GAC7B,IAAI2C,EAAc,CAChB,CAAE3tB,MAAOiX,GAAaqV,SAAUzvB,MAAO,YAAa6wB,YAAa,uBACjE,CAAE1tB,MAAOiX,GAAaqN,WAAYznB,MAAO,cAAe6wB,YAAa,0BASvE,OAPIx0B,GbvKiD,KauKU8xB,IAC7D2C,EAAY7+B,KAAK,CACfkR,MAAOiX,GAAa2W,KACpB/wB,MAAO,OACP6wB,YAAa,4CAGVC,CACT,CAkBA,MAAM,GAAapsB,IAAU,CAC3B/C,QAAQ,IAAAgD,KAAI,CACV3E,MAAO,SACPwkB,QAAS,OACTwM,SAAU,OACV1J,eAAgB,gBAChBjrB,MAAO,OACPE,IAAK,EACL00B,IAAKvsB,EAAMuiB,QAAQ,GACnBhiB,UAAWP,EAAMuiB,QAAQ,KAE3BgH,cAAc,IAAAtpB,KAAI,CAChBusB,OAAQxsB,EAAMwsB,OAAOC,YACrBpwB,SAAU,SACVod,WAAYzZ,EAAMwZ,OAAOC,WAAWiT,UAEtCtC,gBAAgB,IAAAnqB,KAAI,CAClB3E,MAAO,iBACPqkB,SAAU,EACVgN,SAAU,QACVhsB,SAAU,UAEZ8pB,gBAAgB,IAAAxqB,KAAI,CAClB3E,MAAO,iBACPwkB,QAAS,OACTqC,WAAY,aACZmK,SAAU,SAEZ3B,eAAe,IAAA1qB,KAAI,CACjB3E,MAAO,gBACPsxB,YAAa5sB,EAAMuiB,QAAQ,KAE7BsK,aAAa,IAAA5sB,KAAI,CACf3E,MAAO,cACPwkB,QAAS,OACT8M,YAAa5sB,EAAMuiB,QAAQ,KAE7BuK,wBAAwB,IAAA7sB,KAAI,CAC1B3E,MAAO,kBACPgnB,QAAS,QACTjhB,MAAOrB,EAAMwZ,OAAO3a,KAAKisB,WAE3BiB,UAAU,IAAA9rB,KAAI,CACZ3E,MAAO,WACPwkB,QAAS,eACTnoB,MAAO,OACPC,OAAQ,OAERwqB,aAAc,QAEhB4J,cAAc,IAAA/rB,KAAI,CAChB3E,MAAO,eACPwkB,QAAS,OACTnoB,MAAO,QACPC,OAAQ,OACRyJ,MAAO,QACPjB,SAAU,EACVsiB,WAAY,IACZF,WAAY,IACZI,eAAgB,gBAChBN,QAAS,QAGTF,aAAc,QAEhBsJ,iBAAiB,IAAAzrB,KAAI,CACnB3E,MAAO,kBACPme,WAAYxD,KAEd0V,mBAAmB,IAAA1rB,KAAI,CACrB3E,MAAO,oBACPme,WAAYtD,KAEd0V,qBAAqB,IAAA5rB,KAAI,CACvB3E,MAAO,sBACPme,WAAYhC,KAEdmU,wBAAwB,IAAA3rB,KAAI,CAC1B3E,MAAO,yBACPme,WAAY9B,KAEdwT,eAAe,IAAAlrB,KAAI,CACjB3E,MAAO,gBACPwnB,WAAY9iB,EAAMuiB,QAAQ,OC9Q9B,IAAIwK,GAEFA,GADoB,oBAAX11B,OACMA,OAGU,oBAAT21B,KAEDA,KAEA,EAAAxrB,EAEjB,IAAIyrB,GAAc,KACdC,GAAe,KACnB,MACMC,GAAiBJ,GAAaK,aAC9BC,GAAeN,GAAaO,WAC5BC,GAAyBR,GAAaS,sBAAwBT,GAAaU,yBAA2BV,GAAaW,2BACnHC,GAA0BZ,GAAaa,uBAAyBb,GAAac,0BAA4Bd,GAAae,4BA4B5H,SAASC,GAA0BC,GACjC,IAAIC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACJ,MAAMC,EAAkC,oBAAbC,UAA4BA,SAASD,YAChE,IAAKA,EAAa,CAChBF,EAAgB,SAAUl2B,GACxB,MAAMs2B,EAAWt2B,EAAQu2B,mBACvBC,EAASF,EAASG,kBAClBC,EAAWJ,EAASK,iBACpBC,EAAcJ,EAAOC,kBACvBC,EAASG,WAAaH,EAASI,YAC/BJ,EAASK,UAAYL,EAASM,aAC9BJ,EAAYhW,MAAMrhB,MAAQi3B,EAAOS,YAAc,EAAI,KACnDL,EAAYhW,MAAMphB,OAASg3B,EAAOU,aAAe,EAAI,KACrDV,EAAOK,WAAaL,EAAOM,YAC3BN,EAAOO,UAAYP,EAAOQ,YAC5B,EACAf,EAAgB,SAAUj2B,GACxB,OAAOA,EAAQi3B,cAAgBj3B,EAAQm3B,eAAe53B,OAASS,EAAQk3B,eAAiBl3B,EAAQm3B,eAAe33B,MACjH,EACA22B,EAAiB,SAAUx5B,GAEzB,GAAIA,EAAE+pB,OAAOjiB,WAAmD,mBAA/B9H,EAAE+pB,OAAOjiB,UAAUvL,SAA0ByD,EAAE+pB,OAAOjiB,UAAUvL,QAAQ,oBAAsB,GAAKyD,EAAE+pB,OAAOjiB,UAAUvL,QAAQ,kBAAoB,EACjL,OAEF,MAAM8G,EAAUkJ,KAChBgtB,EAAchtB,MACVA,KAAKkuB,eACPvC,GAAY3rB,KAAKkuB,eAEnBluB,KAAKkuB,cAAgBtC,IAAa,WAC5BmB,EAAcj2B,KAChBA,EAAQm3B,eAAe53B,MAAQS,EAAQi3B,YACvCj3B,EAAQm3B,eAAe33B,OAASQ,EAAQk3B,aACxCl3B,EAAQq3B,oBAAoBv5B,SAAQ,SAA+B2S,GACjEA,EAAGE,KAAK3Q,EAASrD,EACnB,IAEJ,GACF,EAGA,IAAI26B,GAAY,EACZC,EAAiB,GACrBxB,EAAsB,iBACtB,MAAMyB,EAAc,kBAAkBziC,MAAM,KAC5C,IAAI0iC,EAAc,uEAAuE1iC,MAAM,KAC3F2iC,EAAM,GACV,CACE,MAAMC,EAAMtB,SAASuB,cAAc,eAInC,QAHgCC,IAA5BF,EAAI/W,MAAMkV,gBACZwB,GAAY,IAEI,IAAdA,EACF,IAAK,IAAI5kC,EAAI,EAAGA,EAAI8kC,EAAY/iC,OAAQ/B,IACtC,QAAoDmlC,IAAhDF,EAAI/W,MAAM4W,EAAY9kC,GAAK,iBAAgC,CAC7DglC,EAAMF,EAAY9kC,GAClB6kC,EAAiB,IAAMG,EAAInuB,cAAgB,IAC3CwsB,EAAsB0B,EAAY/kC,GAClC4kC,GAAY,EACZ,KACF,CAGN,CACAxB,EAAgB,aAChBD,EAAqB,IAAM0B,EAAiB,aAAezB,EAAgB,gDAC3EE,EAAiBuB,EAAiB,kBAAoBzB,EAAgB,IACxE,CA6EA,MAAO,CACLgC,kBA1DwB,SAAU93B,EAASyQ,GAC3C,GAAI2lB,EACFp2B,EAAQo2B,YAAY,WAAY3lB,OAC3B,CACL,IAAKzQ,EAAQu2B,mBAAoB,CAC/B,MAAMwB,EAAM/3B,EAAQg4B,cACdC,EAAetD,GAAauD,iBAAiBl4B,GAC/Ci4B,GAA0C,WAA1BA,EAAah0B,WAC/BjE,EAAQ4gB,MAAM3c,SAAW,YA3BZ,SAAU8zB,GAC7B,IAAKA,EAAII,eAAe,uBAAwB,CAE9C,MAAMtwB,GAAOguB,GAA0C,IAAM,uBAAyBG,GAAkC,IAA5G,6VACVoC,EAAOL,EAAIK,MAAQL,EAAIM,qBAAqB,QAAQ,GACpDzX,EAAQmX,EAAIH,cAAc,SAC5BhX,EAAMhc,GAAK,sBACXgc,EAAMyN,KAAO,WACA,MAATuH,GACFhV,EAAM0X,aAAa,QAAS1C,GAE1BhV,EAAM2X,WACR3X,EAAM2X,WAAWC,QAAU3wB,EAE3B+Y,EAAM6X,YAAYV,EAAIW,eAAe7wB,IAEvCuwB,EAAKK,YAAY7X,EACnB,CACF,CAWM+X,CAAaZ,GACb/3B,EAAQm3B,eAAiB,CAAC,EAC1Bn3B,EAAQq3B,oBAAsB,IAC7Br3B,EAAQu2B,mBAAqBwB,EAAIH,cAAc,QAAQnzB,UAAY,kBACpE,MAAMm0B,EAAgBb,EAAIH,cAAc,OACxCgB,EAAcn0B,UAAY,iBAC1Bm0B,EAAcH,YAAYV,EAAIH,cAAc,QAC5C,MAAMiB,EAAkBd,EAAIH,cAAc,OAC1CiB,EAAgBp0B,UAAY,mBAC5BzE,EAAQu2B,mBAAmBkC,YAAYG,GACvC54B,EAAQu2B,mBAAmBkC,YAAYI,GACvC74B,EAAQy4B,YAAYz4B,EAAQu2B,oBAC5BL,EAAcl2B,GACdA,EAAQ6mB,iBAAiB,SAAUsP,GAAgB,GAG/CJ,IACF/1B,EAAQu2B,mBAAmBuC,sBAAwB,SAA2Bn8B,GACxEA,EAAEm5B,gBAAkBA,GACtBI,EAAcl2B,EAElB,EACAA,EAAQu2B,mBAAmB1P,iBAAiBkP,EAAqB/1B,EAAQu2B,mBAAmBuC,uBAEhG,CACA94B,EAAQq3B,oBAAoBliC,KAAKsb,EACnC,CACF,EAsBEsoB,qBArB2B,SAAU/4B,EAASyQ,GAC9C,GAAI2lB,EACFp2B,EAAQg5B,YAAY,WAAYvoB,QAGhC,GADAzQ,EAAQq3B,oBAAoBl9B,OAAO6F,EAAQq3B,oBAAoBn+B,QAAQuX,GAAK,IACvEzQ,EAAQq3B,oBAAoB5iC,OAAQ,CACvCuL,EAAQ8mB,oBAAoB,SAAUqP,GAAgB,GAClDn2B,EAAQu2B,mBAAmBuC,wBAC7B94B,EAAQu2B,mBAAmBzP,oBAAoBiP,EAAqB/1B,EAAQu2B,mBAAmBuC,uBAC/F94B,EAAQu2B,mBAAmBuC,sBAAwB,MAErD,IACE94B,EAAQu2B,oBAAsBv2B,EAAQi5B,YAAYj5B,EAAQu2B,mBAC5D,CAAE,MAAO55B,GAET,CACF,CAEJ,EAKF,CArL8B,MAA1Bw4B,IAA6D,MAA3BI,IAGpCV,GAAcE,GACdD,GAAe,SAA4CoE,GACzD,OAAOjE,GAAaiE,EAVC,GAWvB,IAKArE,GAAc,UAAsBsE,EAAkBC,IACpDjE,GAAuBgE,GACvBpE,GAAeqE,EACjB,EACAtE,GAAe,SAAqDoE,GAClE,MAAMC,EAAmB5D,IAAwB,WAC/CR,GAAeqE,GACfF,GACF,IACME,EAAYnE,IAAa,WAC7BE,GAAuBgE,GACvBD,GACF,GA5BqB,IA6BrB,MAAO,CAACC,EAAkBC,EAC5B,GA8JF,MAAMC,WAAkB,EAAAC,UACtB,WAAA1wB,IAAenO,GACb8+B,SAAS9+B,GACTyO,KAAKswB,MAAQ,CACXh6B,OAAQ0J,KAAK8pB,MAAMyG,eAAiB,EACpCC,aAAcxwB,KAAK8pB,MAAMyG,eAAiB,EAC1CE,YAAazwB,KAAK8pB,MAAM4G,cAAgB,EACxCr6B,MAAO2J,KAAK8pB,MAAM4G,cAAgB,GAEpC1wB,KAAK2wB,WAAa,KAClB3wB,KAAK4wB,qBAAuB,KAC5B5wB,KAAK6wB,YAAc,KACnB7wB,KAAK8wB,gBAAkB,KACvB9wB,KAAK+wB,WAAa,KAClB/wB,KAAKgxB,UAAY,KACfhxB,KAAK+wB,WAAa,KAClB,MAAM,cACJE,EAAa,aACbC,EAAY,SACZC,GACEnxB,KAAK8pB,MACT,GAAI9pB,KAAK6wB,YAAa,CAKpB,MAAMnZ,EAAQ3hB,OAAOi5B,iBAAiBhvB,KAAK6wB,cAAgB,CAAC,EACtDO,EAAc5d,WAAWkE,EAAM0Z,aAAe,KAC9CC,EAAe7d,WAAWkE,EAAM2Z,cAAgB,KAChDC,EAAa9d,WAAWkE,EAAM4Z,YAAc,KAC5CC,EAAgB/d,WAAWkE,EAAM6Z,eAAiB,KAClDt6B,EAAO+I,KAAK6wB,YAAYW,wBACxBhB,EAAev5B,EAAKX,OAASg7B,EAAaC,EAC1Cd,EAAcx5B,EAAKZ,MAAQ+6B,EAAcC,EACzC/6B,EAAS0J,KAAK6wB,YAAY7C,aAAesD,EAAaC,EACtDl7B,EAAQ2J,KAAK6wB,YAAY9C,YAAcqD,EAAcC,GACtDJ,GAAkBjxB,KAAKswB,MAAMh6B,SAAWA,GAAU0J,KAAKswB,MAAME,eAAiBA,KAAkBU,GAAiBlxB,KAAKswB,MAAMj6B,QAAUA,GAAS2J,KAAKswB,MAAMG,cAAgBA,KAC7KzwB,KAAKyxB,SAAS,CACZn7B,SACAD,QACAm6B,eACAC,gBAEsB,mBAAbU,GACTA,EAAS,CACP76B,SACAk6B,eACAC,cACAp6B,UAIR,GAEF2J,KAAK0xB,QAAUC,IACb3xB,KAAK2wB,WAAagB,CAAS,CAE/B,CACA,iBAAAC,GACE,MAAM,MACJlF,GACE1sB,KAAK8pB,MACH+H,EAAa7xB,KAAK2wB,WAAa3wB,KAAK2wB,WAAWkB,WAAa,KAClE,GAAkB,MAAdA,GAAsBA,EAAW/C,eAAiB+C,EAAW/C,cAAcgD,aAAeD,aAAsBA,EAAW/C,cAAcgD,YAAYrU,YAAa,CAIpKzd,KAAK6wB,YAAcgB,EAInB,MAAME,EAAyBF,EAAW/C,cAAcgD,YAAYn7B,eACtC,MAA1Bo7B,GACF/xB,KAAK8wB,gBAAkB,IAAIiB,GAAuB,KAIhD/xB,KAAK+wB,WAAa/E,WAAWhsB,KAAKgxB,UAAW,EAAE,IAEjDhxB,KAAK8wB,gBAAgBt5B,QAAQq6B,KAI7B7xB,KAAK4wB,qBAAuBnE,GAA0BC,GACtD1sB,KAAK4wB,qBAAqBhC,kBAAkBiD,EAAY7xB,KAAKgxB,YAE/DhxB,KAAKgxB,WACP,CACF,CACA,oBAAAgB,GACMhyB,KAAK6wB,cACH7wB,KAAK4wB,sBACP5wB,KAAK4wB,qBAAqBf,qBAAqB7vB,KAAK6wB,YAAa7wB,KAAKgxB,WAEhD,OAApBhxB,KAAK+wB,YACPjF,aAAa9rB,KAAK+wB,YAEhB/wB,KAAK8wB,iBACP9wB,KAAK8wB,gBAAgBr5B,aAG3B,CACA,MAAAw6B,GACE,MAAM,SACJ14B,EAAQ,cACRg3B,EAAa,aACbG,EAAY,cACZO,GAAgB,EAAK,aACrBC,GAAe,EAAK,4BACpBgB,GAA8B,EAAK,MACnCxF,EAAK,SACLyE,EAAQ,MACRzZ,EAAQ,CAAC,EAAC,QACVya,EAAU,SACPC,GACDpyB,KAAK8pB,OACH,OACJxzB,EAAM,aACNk6B,EAAY,YACZC,EAAW,MACXp6B,GACE2J,KAAKswB,MAKH+B,EAAa,CACjBxzB,SAAU,WAENyzB,EAAc,CAAC,EAIrB,IAAIC,GAAoB,EAoBxB,OAnBKtB,IACY,IAAX36B,IACFi8B,GAAoB,GAEtBF,EAAW/7B,OAAS,EACpBg8B,EAAYh8B,OAASA,EACrBg8B,EAAY9B,aAAeA,GAExBU,IACW,IAAV76B,IACFk8B,GAAoB,GAEtBF,EAAWh8B,MAAQ,EACnBi8B,EAAYj8B,MAAQA,EACpBi8B,EAAY7B,YAAcA,GAExByB,IACFK,GAAoB,IAEf,IAAA7D,eAAcyD,EAAS,CAC5Bp7B,IAAKiJ,KAAK0xB,QACVha,MAAO,IACF2a,KACA3a,MAEF0a,IACDG,GAAqBh5B,EAAS+4B,GACpC,EChXF,MAAME,IAA8B,IAAA7S,OAClC,EACEpnB,OACAk6B,gBACAnhC,SACAulB,gBACA6b,WACAhR,eACA/oB,aACAg6B,cACA1b,kBAEA,MAAM2b,GAAQ,IAAAx7B,UAAQ,KACpB,IAAIy7B,EAAgB,CAAC,EACrB,IAAK,IAAIrpC,EAAI,EAAGA,EAAI+O,EAAKA,KAAKhN,OAAQ/B,IAAK,CACzC,MAAM2T,EAAQ5E,EAAK4tB,SAAS38B,GACtBqU,EAAatF,EAAK8tB,cAAc78B,GAChCkiC,EAAOnzB,EAAK+tB,QAAQ98B,GACpBwQ,EAAQzB,EAAKuE,SAAStT,GACvBqtB,IAAiBA,EAAcnkB,IAAIsH,KACtC64B,EAAc74B,GAAS64B,EAAc74B,IAAU,CAAC,EAChD64B,EAAc74B,GAAO0xB,KAAOmH,EAAc74B,GAAO0xB,KAAOmH,EAAc74B,GAAO0xB,KAAOA,EAAOA,EAC3FmH,EAAc74B,GAAO84B,MAAQD,EAAc74B,GAAO84B,MAAQD,EAAc74B,GAAO84B,MAAQ31B,EAAQA,EAC/F01B,EAAc74B,GAAO+4B,WAAaF,EAAc74B,GAAO+4B,WAAaF,EAAc74B,GAAO+4B,WAAal1B,EAAaA,EAEvH,CACA,OAAOg1B,CAAa,GACnB,CAACt6B,EAAMse,IACJ7b,GAAS,IAAAC,YAAW,IACpByD,GAAQ,IAAAqZ,cACPpvB,EAAMqqC,IAAW,IAAAn8B,UAAS,CAAC,CAAE+pB,YAAa,OAAQqS,MAAM,KAC/D,OAAuB,IAAA35B,KAAI,MAAO,CAAEiC,UAAWP,EAAOk4B,kBAAmB,cAAe,WAAY35B,UAA0B,IAAAD,KAAI62B,GAAW,CAAEzY,MAAO,CAAErhB,MAAO,QAAUkD,SAAU,EAAGlD,QAAOC,aAC3L,GAAID,EAAQ,GAAKC,EAAS,EACxB,OAAO,KAET,MAAM68B,EA+BZ,SAA6B56B,EAAMq6B,EAAOv8B,EAAOo8B,EAAeC,EAAU/5B,EAAY+F,EAAOuY,EAAa3lB,EAAQowB,GAChH,MAAM0R,EA8FR,SAA2Bz6B,EAAY+5B,EAAUphC,EAAQowB,GACvD,MAAMnL,EAAU,CACd4O,KAAM,EAAAkO,qBAAqBC,OAC3BC,cAAgBzJ,IACS,IAAAxwB,KACrBk6B,GACA,CACEL,MAAOrJ,EAAMqJ,MACbx6B,aACA+5B,WACAphC,SACAowB,eACA+R,SAAU3J,EAAM2J,YAKlBC,EAAyB,CAC7BC,YAAY,EACZt9B,MAAOu9B,GACPC,YAAY,EACZC,SAAS,EACTC,MAAO,OACPC,YAAazd,GAEf,MAAO,CACL4O,KAAM,EAAAT,UAAUlgB,OAChBpF,KAAM,UACN+e,OAAQ,GACRiG,OAAQ,CACN6P,OAAQP,GAGd,CA/HsBQ,CAAkBv7B,EAAY+5B,EAAUphC,EAAQowB,GAC9DyS,EAAc,CAClBhP,KAAM,EAAAT,UAAUC,OAChBvlB,KAAM,SACN+e,OAAQ,GACRiG,OAAQ,CACN6P,OAAQ,CAAE59B,MAAOA,EAAQu9B,GAA6C,EAAzBv7B,GAC7C+7B,MAAO,CACL,CACEx1B,MAAO,mBACPy1B,IAAK,GACLn6B,QAAUzG,IACR,MACM0J,EADQ1J,EAAE6gC,OAAO5lC,MACHyvB,OAAO1qB,EAAE6gC,OAAOb,UACpChB,EAAct1B,EAAM,MAM9B,IAAIg2B,EACJ,GAAI56B,EAAKqB,mBAAoB,CAC3Bu6B,EAAY/P,OAAO6P,OAAO59B,MAAQA,EAAQu9B,GAA6C,EAAzBv7B,EAC9D,MAAMk8B,EAAgBC,GAAkB,WAAY,WAC9CC,EAAkBD,GAAkB,aAAc,WAClDE,EAAYF,GAAkB,OAAQ,WAC5CE,EAAUtQ,OAAO6P,OAAOD,YAAY7O,KAAO,EAAAkO,qBAAqBsB,UAChE,MAAOC,EAAaC,GAAY5d,IAAgBzC,GAAgBmE,eAAiB,CAACvC,GAAqB,GAAIA,GAAqB,IAAM,CAACF,GAAkB,GAAIA,GAAkB,IAC/Kwe,EAAUtQ,OAAO0Q,SAAW,CAC1B,CAAE3P,KAAM,EAAA4P,YAAYC,YAAaze,QAAS,CAAE,CAACpwB,KAAW,CAAEoX,KAAM,MAAOwC,MAAO80B,KAC9E,CAAE1P,KAAM,EAAA4P,YAAYC,YAAaze,QAAS,CAAE,EAAE,KAAM,CAAEhZ,KAAM,UAAWwC,MAAO60B,KAC9E,CAAEzP,KAAM,EAAA4P,YAAYE,YAAa1e,QAAS,CAAErlB,KAAM,EAAGC,GAAIhL,IAAUgP,OAAQ,CAAE4K,MAAO80B,KACpF,CAAE1P,KAAM,EAAA4P,YAAYE,YAAa1e,QAAS,CAAErlB,MAAO/K,IAAUgL,GAAI,EAAGgE,OAAQ,CAAE4K,MAAO60B,MAEvF,MAAMjT,EAASppB,EAAKqF,YACd9C,EAAa6mB,EAAOp2B,OAASo2B,EAAO,GAAG,GAAGxkB,MAAQ,EAClDQ,EAAkBgkB,EAAOp2B,OAASo2B,EAAO,GAAG,GAAG9jB,gBAAa,EAClE,IAAK,IAAIsX,KAAOyd,EAAO,CACrBQ,EAAYjV,OAAOlyB,KAAK,MACxBkoC,EAAYhW,OAAOlyB,KAAKkpB,GACxB,MAAMgE,EAAYyZ,EAAMzd,GAAK2d,MACvBha,EAAa8Z,EAAMzd,GAAK4d,WACxBj1B,EAAiBhD,EAAa6C,EAC9BK,EAAiBxS,KAAK8R,MAAM,IAAM6b,EAAYrb,GAAkB,IAChEG,EAAkBzS,KAAK8R,MAAM,IAAMwb,EAAanb,GAAmB,IACnEO,GAAQD,EAAkBD,GAAkBA,EAAiB,IACnE02B,EAAUvW,OAAOlyB,KAAKiS,GACtBq2B,EAAcpW,OAAOlyB,KAAK+R,GAC1By2B,EAAgBtW,OAAOlyB,KAAKgS,EAC9B,CACAk1B,EAAQ,CACN1O,OAAQ,CAAC2O,EAAae,EAAaI,EAAeE,EAAiBC,GACnEnpC,OAAQ4oC,EAAYhW,OAAO5yB,OAE/B,KAAO,CACL,MAAMm6B,EAAY8O,GAAkB,OAAQj8B,EAAKmtB,UAAUtB,OAAOhJ,MAC5D8Z,EAAaV,GAAkB,QAASj8B,EAAKktB,WAAWrB,OAAOhJ,MACrE,IAAK,IAAIjG,KAAOyd,EACdQ,EAAYjV,OAAOlyB,KAAK,MACxBkoC,EAAYhW,OAAOlyB,KAAKkpB,GACxBuQ,EAAUvH,OAAOlyB,KAAK2mC,EAAMzd,GAAKuW,MACjCwJ,EAAW/W,OAAOlyB,KAAK2mC,EAAMzd,GAAK2d,OAEpCK,EAAQ,CAAE1O,OAAQ,CAAC2O,EAAae,EAAazO,EAAWwP,GAAa3pC,OAAQ4oC,EAAYhW,OAAO5yB,OAClG,CACA,MAAM4pC,GAAa,IAAAC,qBAAoB,CACrC78B,KAAM,CAAC46B,GACPkC,YAAa,CACXC,SAAU,CAAC,EACXC,UAAW,IAEbC,iBAAmBr4B,GAAUA,EAC7BuB,UAEF,OAAOy2B,EAAW,EACpB,CA3GoBM,CACZl9B,EACAq6B,EACAv8B,EACAo8B,EACAC,EACA/5B,EACA+F,EACAuY,EACA3lB,EACAowB,GAEF,OAAuB,IAAApoB,KACrB,EAAAo8B,MACA,CACEC,cAAehtC,EACfitC,eAAiBvhC,IACXA,GAAKA,EAAE9I,SACM,MAAfonC,GAA+BA,EAAYt+B,EAAE,GAAGusB,YAAc,KAAOvsB,EAAE,GAAG4+B,KAAO,OAAS,SAE5FD,EAAQ3+B,EAAE,EAEZkE,KAAM46B,EACN98B,QACAC,UAEH,KACI,IAiFX,SAASk+B,GAAkBp1B,EAAMgc,GAC/B,MAAMya,EAAoB,CACxBx/B,MAAOgC,EACP07B,MAAO,OACPD,SAAS,EACTE,YAAa,CAAE7O,KAAM,EAAAkO,qBAAqByC,OAE5C,MAAO,CACL3Q,KAAM,EAAAT,UAAUlgB,OAChBpF,OACA+e,OAAQ,GACRiG,OAAQ,CACNhJ,OACA6Y,OAAQ4B,GAGd,CA9FArD,GAA4B5R,YAAc,8BA+F1C,MAAMgT,GAAoB,GAmC1B,SAASJ,GAAW1J,GAClB,IAAIlzB,EACJ,MAAMoE,EAAS+6B,KACTC,EAAuE,OAA7Dp/B,EAAKkzB,EAAMqJ,MAAM1O,OAAOQ,MAAMrjB,GAAiB,WAAXA,EAAExC,aAA8B,EAASxI,EAAGunB,OAAO2L,EAAM2J,UACvGwC,EAAanM,EAAMx4B,SAAW0kC,EAC9BE,EAAepM,EAAMpI,eAAiBsU,EAC5C,OAAuB,IAAAn8B,MAAK,MAAO,CAAE0B,UAAWP,EAAOm7B,kBAAmB58B,SAAU,EAClE,IAAAD,KACd,EAAAinB,WACA,CACEhlB,UAAWP,EAAOo7B,iBAClBh3B,KAAM,SACNgqB,QAAS6M,EAAa,UAAY,YAClCxV,QAASwV,EAAa,oBAAsB,oBAC5C,aAAcA,EAAa,oBAAsB,oBACjD/7B,QAAS,KACP4vB,EAAM4I,SAASuD,EAAa,GAAKD,EAAO,KAI9B,IAAA18B,KACd,EAAAinB,WACA,CACEhlB,UAAWP,EAAOo7B,iBAClBh3B,KAAM,kBACNqhB,QAASyV,EAAe,4BAA8B,wBACtD9M,QAAS8M,EAAe,UAAY,YACpC,aAAcA,EAAe,4BAA8B,wBAC3Dh8B,QAAS,KACP4vB,EAAMnxB,WAAWu9B,OAAe,EAASF,EAAO,MAK1D,CACA,MAAM,GAAat3B,IACV,CACLw0B,mBAAmB,IAAAv0B,KAAI,CACrB3E,MAAO,oBACPgnB,QAAStiB,EAAMuiB,QAAQ,GACvBoV,gBAAiB33B,EAAMwZ,OAAOC,WAAWC,UACzC9hB,OAAQ,WAIRy/B,GAAsB,KACnB,CACLI,mBAAmB,IAAAx3B,KAAI,CACrB3E,MAAO,oBACPwkB,QAAS,OACTloB,OAAQ,SAEV8/B,kBAAkB,IAAAz3B,KAAI,CACpB3E,MAAO,mBACPsxB,YAAa,EACbj1B,MAAO,WCvPPigC,GAAS,IAAIxsC,EACbysC,GAAsB,EAC1Bh+B,OACAi+B,qBACAC,iBACAC,sBACA/D,cACAgE,WACA1O,eACAC,sBACAC,WACAjM,qBACA0a,oBACAC,wBACA59B,iCAEA,MAAOie,EAAiB4f,IAAsB,IAAAjgC,aACvCie,EAAU+G,IAAe,IAAAhlB,UAAS,IAClCke,EAAU+G,IAAe,IAAAjlB,UAAS,IAClCvF,EAAQo2B,IAAa,IAAA7wB,UAAS,KAC9BwC,EAAcsuB,IAAmB,IAAA9wB,UAASud,GAAa2W,OACvD3O,GAAW/lB,MAAOuxB,IAAoB,KACtC9Q,EAAWigB,IAAgB,IAAAlgC,UAAS,SACpC6qB,EAAcsV,IAAmB,IAAAngC,aACjCsgB,EAAcgF,IAAmB,IAAAtlB,UAAS,IAAI8sB,IAC/CjlB,GAAQ,IAAAtH,UAAQ,IAAMu/B,KAAY,CAACA,IACnCM,GAAgB,IAAA7/B,UAAQ,KAC5B,IAAKmB,EACH,OAEF,MAAM2uB,EAAY,IAAI5C,GAAwB/rB,EAAM,CAAEW,YAAa09B,GAAqBl4B,GAExF,OADAyd,EAAgB+K,EAAU7C,mBACnB6C,CAAS,GACf,CAAC3uB,EAAMmG,EAAOk4B,KACV3f,EAAaigB,GAkKtB,SAAwBD,GACtB,MAAME,GAAuC,MAAjBF,OAAwB,EAASA,EAAcr9B,oBAAsB4a,GAAgBkE,QAAUpE,GAAY4V,cAChIjT,EAAaigB,IAAkB,IAAArgC,UAASsgC,GAI/C,OAHA,IAAAlhC,YAAU,KACRihC,EAAeC,EAAmB,GACjC,CAACA,IACG,CAAClgB,EAAaigB,EACvB,CAzKwCE,CAAeH,GAC/Cj8B,EAwLR,SAAmB0D,GACjB,MAAO,CACLwoB,WAAW,IAAAvoB,KAAI,CACb3E,MAAO,YACP6E,SAAU,OACVvI,OAAQ,OACRkoB,QAAS,OACTE,KAAM,QACN2Y,cAAe,SACfC,UAAW,EACXrM,IAAKvsB,EAAMuiB,QAAQ,KAErBsW,MAAM,IAAA54B,KAAI,CACR3E,MAAO,OACPqkB,SAAU,IAEZmZ,gBAAgB,IAAA74B,KAAI,CAGlBrI,OAAQ,MAEVmhC,qBAAqB,IAAA94B,KAAI,CACvB3E,MAAO,sBACPwkB,QAAS,OACT8Y,UAAW,EACXD,cAAe,MACfK,UAAWh5B,EAAMuiB,QAAQ,GACzB5qB,MAAO,SAETshC,0BAA0B,IAAAh5B,KAAI,CAC5B2f,UAAW,QAEbsZ,0BAA0B,IAAAj5B,KAAI,CAC5B2f,UAAW,MACXuZ,UAAW,MAEbC,wBAAwB,IAAAn5B,KAAI,CAC1BQ,aAAcT,EAAMuiB,QAAQ,KAE9B8W,wBAAwB,IAAAp5B,KAAI,CAC1BrI,OAAQ,MAGd,CAnOiB,CAAUoI,GACnBmY,EAwKR,SAAwBvlB,EAAQiH,GAC9B,OAAO,IAAAnB,UAAQ,KACb,GAAI9F,GAAUiH,EAAM,CAClB,MAAMy/B,EAA8B,IAAI1lC,IACxC,IAAIzE,EAAOyoC,GAAOnqC,OAAOoM,EAAKiuB,kBAAmBl1B,GACjD,GAAIzD,EACF,IAAK,IAAI9E,KAAO8E,EACdmqC,EAAYplC,IAAI2F,EAAKiuB,kBAAkBz9B,IAG3C,OAAOivC,CACT,CACa,GACZ,CAAC1mC,EAAQiH,GACd,CAtLwB0/B,CAAe3mC,EAAQ2lC,IAC7C,IAAAhhC,YAAU,KACJ2xB,EAAiB,GAAKA,EhBzCyB,KgByC0CvuB,IAAiB+a,GAAa2W,OAAS5C,GAClIR,EAAgBvT,GAAaqN,WAC/B,GACC,CAACpoB,EAAcsuB,EAAiBC,EAAgBO,IACnD,MAAM+P,GAAa,IAAA3f,cAAY,KAC7Bue,OAAmB,GACnBjb,EAAY,GACZC,EAAY,EAAE,GACb,CAACgb,EAAoBhb,EAAaD,IAC/Bsc,GAAgB,IAAA5f,cAAY,KAChCye,OAAgB,EAAO,GACtB,CAACA,KACJ,IAAA/gC,YAAU,KACR,IAAIW,EACJ,IAAKigC,EAGH,OAFAqB,SACAC,IAGF,GAAIlB,GAAiB/f,EAAiB,CACpC,MAAMrc,EAAwE,OAAhEjE,EAAKqgC,EAAcpQ,kBAAkB3P,EAAgBld,aAAkB,EAASpD,EAAG,GACjG,GAAIiE,EAAM,CACRi8B,EAAmB,IAAK5f,EAAiBrc,SACzC,MAAM8mB,EAASsV,EAAcr5B,YACvBmZ,EAAiB4K,EAAOp2B,OAASo2B,EAAO,GAAG,GAAGxkB,MAAQ,EAC5D0e,EAAYhhB,EAAKzR,MAAQ2tB,GACzB+E,GAAajhB,EAAKzR,MAAQyR,EAAKsC,OAAS4Z,EAC1C,MACE+f,EAAmB,IACd5f,EACHrc,KAAM,CACJzR,MAAO,EACP+T,MAAO,EACPJ,YAAa,GACbxD,SAAU,GACViQ,MAAO,KAGXqS,EAAY,GACZC,EAAY,EAEhB,IACC,CAACmb,EAAeJ,IACnB,MAAMpE,GAAgB,IAAAla,cACnByd,IACK1kC,IAAW0kC,EACbtO,EAAU,KAEY,MAAtB8O,GAAsCA,EAAmBR,GACzDtO,EAAUsO,GACVkC,IACF,GAEF,CAACxQ,EAAWwQ,EAAY1B,EAAoBllC,IAE9C,IAAK2lC,EACH,OAAO,KAET,MAAMmB,GAA6B,IAAA9+B,KACjCmoB,GACA,CACElpB,KAAM0+B,EACNniB,WACAC,WACA8B,gBACAgF,cACAC,cACAC,cAAgBsc,GAAUvB,EAAmBuB,GAC7CnhB,kBACAJ,YACA4K,eACA/oB,WAAaqB,IACXk+B,IACAlB,EAAgBh9B,EAAM,EAExB8lB,iBAAkBoY,EAClBnY,oBAAqBoY,EACrBlhB,cACAiF,qBACAhjB,YAAa09B,EACb39B,6BACAI,eACA/H,SACA6lB,eACAgF,oBAGEyW,GAAwB,IAAAt5B,KAC5Bk5B,GACA,CACEj6B,KAAM0+B,EACNxE,gBACAnhC,SACAulB,gBACA6K,eACA/oB,WAAYq+B,EACZtE,SAAUhL,EACViL,cACA1b,gBAGJ,IAAIsgB,EAkBJ,OAjBIrb,GAAsB7iB,IAAiB+a,GAAaqN,WACtD8V,EAAOa,EACE/+B,IAAiB+a,GAAaqV,SACvC8N,GAAuB,IAAAj+B,KAAI,MAAO,CAAEiC,UAAWP,EAAOw8B,eAAgBj+B,SAAUq5B,IACvEv5B,IAAiB+a,GAAa2W,OAErCwM,EADEpP,GACqB,IAAAtuB,MAAK,MAAO,CAAEN,SAAU,EAC7B,IAAAD,KAAI,MAAO,CAAEiC,UAAWP,EAAO88B,uBAAwBv+B,SAAU6+B,KACjE,IAAA9+B,KAAI,MAAO,CAAEiC,UAAWP,EAAO+8B,uBAAwBx+B,SAAUq5B,QAG5D,IAAA/4B,MAAK,MAAO,CAAE0B,UAAWP,EAAOy8B,oBAAqBl+B,SAAU,EACpE,IAAAD,KAAI,MAAO,CAAEiC,UAAWP,EAAO48B,yBAA0Br+B,SAAUq5B,KACnE,IAAAt5B,KAAI,MAAO,CAAEiC,UAAWP,EAAO28B,yBAA0Bp+B,SAAU6+B,SAOvE,IAAA9+B,KAAI,EAAAg/B,aAAaC,SAAU,CAAEp7B,MAAOuB,EAAOnF,UAA0B,IAAAM,MAAK,MAAO,CAAE9C,IAAKqlB,EAAS7gB,UAAWP,EAAOksB,UAAW3tB,SAAU,EACrJ2iB,IAAsC,IAAA5iB,KACrCmuB,GACA,CACEn2B,SACAo2B,YACAruB,eACAsuB,gBAAkB6Q,IAChB7Q,EAAgB6Q,GACE,MAAlB/B,GAAkCA,EAAe+B,EAAK,EAExD5Q,iBACAC,QAAS,KACPqQ,IACAC,GAAe,EAEjBrhB,YACAgR,kBAAoBiM,IAClBgD,EAAahD,GACU,MAAvB2C,GAAuCA,EAAoB3C,EAAM,EAEnEhM,gBAAiB9B,QAAQ/O,GAAmBwK,GAC5CzK,cACA+Q,oBAAqBkP,EACrBjP,aAAchC,QAAQgC,GACtBC,sBACAC,WACAC,WAAY6O,EAAcr9B,mBAC1BuiB,kBACAhF,kBAGY,IAAA7d,KAAI,MAAO,CAAEiC,UAAWP,EAAOu8B,KAAMh+B,SAAUg+B,QAElE,C,uBC/MH,UAAwD,EAAO,QAAI,0BAAF,EAA8F,WAAW,aAAa,SAAStxC,EAAED,EAAEC,GAAG,YAAM,IAAoBA,EAAEA,EAAE,CAACwyC,SAAQ,GAAI,iBAAiBxyC,IAAIyyC,QAAQC,KAAK,sDAAsD1yC,EAAE,CAACwyC,SAASxyC,IAAIA,EAAEwyC,SAAS,6EAA6EptC,KAAKrF,EAAEm/B,MAAM,IAAIyT,KAAK,CAAC,SAAS5yC,GAAG,CAACm/B,KAAKn/B,EAAEm/B,OAAOn/B,CAAC,CAAC,SAASyH,EAAEzH,EAAEC,EAAEwH,GAAG,IAAIsV,EAAE,IAAI81B,eAAe91B,EAAE+1B,KAAK,MAAM9yC,GAAG+c,EAAEg2B,aAAa,OAAOh2B,EAAEi2B,OAAO,WAAW94B,EAAE6C,EAAEk2B,SAAShzC,EAAEwH,EAAE,EAAEsV,EAAEm2B,QAAQ,WAAWR,QAAQS,MAAM,0BAA0B,EAAEp2B,EAAEq2B,MAAM,CAAC,SAASr2B,EAAE/c,GAAG,IAAIC,EAAE,IAAI4yC,eAAe5yC,EAAE6yC,KAAK,OAAO9yC,GAAE,GAAI,IAAIC,EAAEmzC,MAAM,CAAC,MAAMpzC,GAAG,CAAC,OAAO,KAAKC,EAAEozC,QAAQ,KAAKpzC,EAAEozC,MAAM,CAAC,SAAS5lC,EAAEzN,GAAG,IAAIA,EAAEszC,cAAc,IAAIC,WAAW,SAAS,CAAC,MAAM9rC,GAAG,IAAIxH,EAAEknC,SAASqM,YAAY,eAAevzC,EAAEwzC,eAAe,SAAQ,GAAG,EAAG1jC,OAAO,EAAE,EAAE,EAAE,GAAG,IAAG,GAAG,GAAG,GAAG,EAAG,EAAE,MAAM/P,EAAEszC,cAAcrzC,EAAE,CAAC,CAAC,IAAI2b,EAAE,iBAAiB7L,QAAQA,OAAOA,SAASA,OAAOA,OAAO,iBAAiB21B,MAAMA,KAAKA,OAAOA,KAAKA,KAAK,iBAAiB,EAAAxrB,GAAQ,EAAAA,EAAOw5B,SAAS,EAAAx5B,EAAO,EAAAA,OAAO,EAAOla,EAAE4b,EAAEzH,WAAW,YAAY9O,KAAK8O,UAAUw/B,YAAY,cAActuC,KAAK8O,UAAUw/B,aAAa,SAAStuC,KAAK8O,UAAUw/B,WAAWz5B,EAAE0B,EAAEg4B,SAAS,iBAAiB7jC,QAAQA,SAAS6L,EAAE,WAAW,EAAE,aAAai4B,kBAAkBl6B,YAAY3Z,EAAE,SAASC,EAAEia,EAAES,GAAG,IAAInX,EAAEoY,EAAEk4B,KAAKl4B,EAAEm4B,UAAU7tC,EAAEihC,SAASuB,cAAc,KAAKxuB,EAAEA,GAAGja,EAAEmZ,MAAM,WAAWlT,EAAE8tC,SAAS95B,EAAEhU,EAAE+tC,IAAI,WAAW,iBAAiBh0C,GAAGiG,EAAEguC,KAAKj0C,EAAEiG,EAAEooC,SAAS6F,SAAS7F,OAAO7gC,EAAEvH,GAAG6W,EAAE7W,EAAEguC,MAAMzsC,EAAExH,EAAEia,EAAES,GAAGlN,EAAEvH,EAAEA,EAAEsxB,OAAO,YAAYtxB,EAAEguC,KAAK1wC,EAAE4wC,gBAAgBn0C,GAAG+lC,YAAW,WAAWxiC,EAAE6wC,gBAAgBnuC,EAAEguC,KAAK,GAAE,KAAKlO,YAAW,WAAWv4B,EAAEvH,EAAE,GAAE,GAAG,EAAE,qBAAqBiO,UAAU,SAASyH,EAAE1B,EAAES,GAAG,GAAGT,EAAEA,GAAG0B,EAAExC,MAAM,WAAW,iBAAiBwC,EAAEzH,UAAUmgC,iBAAiBr0C,EAAE2b,EAAEjB,GAAGT,QAAQ,GAAG6C,EAAEnB,GAAGnU,EAAEmU,EAAE1B,EAAES,OAAO,CAAC,IAAInX,EAAE2jC,SAASuB,cAAc,KAAKllC,EAAE0wC,KAAKt4B,EAAEpY,EAAEg0B,OAAO,SAASwO,YAAW,WAAWv4B,EAAEjK,EAAE,GAAE,CAAC,EAAE,SAASvD,EAAE8c,EAAEtP,EAAEyM,GAAG,IAAGA,EAAEA,GAAG44B,KAAK,GAAG,aAAc54B,EAAEitB,SAASvuB,MAAMsB,EAAEitB,SAASoK,KAAKgD,UAAU,kBAAkB,iBAAiBt0C,EAAE,OAAOwH,EAAExH,EAAE8c,EAAEtP,GAAG,IAAIkN,EAAE,6BAA6B1a,EAAEk/B,KAAK37B,EAAE,eAAe6B,KAAKuW,EAAE6b,cAAc7b,EAAE44B,OAAOtuC,EAAE,eAAeb,KAAK8O,UAAUw/B,WAAW,IAAIztC,GAAGyU,GAAGnX,GAAGxD,IAAI,oBAAoBy0C,WAAW,CAAC,IAAI/qC,EAAE,IAAI+qC,WAAW/qC,EAAEgrC,UAAU,WAAW,IAAI10C,EAAE0J,EAAEyF,OAAOnP,EAAEkG,EAAElG,EAAEA,EAAEM,QAAQ,eAAe,yBAAyB4Z,EAAEA,EAAEi6B,SAASD,KAAKl0C,EAAEm0C,SAASn0C,EAAEka,EAAE,IAAI,EAAExQ,EAAEirC,cAAc10C,EAAE,KAAK,CAAC,IAAIgO,EAAE2N,EAAEk4B,KAAKl4B,EAAEm4B,UAAU/tC,EAAEiI,EAAEmmC,gBAAgBn0C,GAAGia,EAAEA,EAAEi6B,SAASnuC,EAAEmuC,SAASD,KAAKluC,EAAEkU,EAAE,KAAK8rB,YAAW,WAAW/3B,EAAEomC,gBAAgBruC,EAAE,GAAE,IAAI,CAAC,GAAG4V,EAAEg4B,OAAO15B,EAAE05B,OAAO15B,EAA+B06B,EAAOC,QAAQ36B,CAAE,GAA5kF,8B,8BCEnE,IAAI46B,EAAU,EAAQ,MAClBC,EAAU,EAAQ,MAClBC,EAAiBF,EAAQG,gBAAgB,EAAQ,OAQrDJ,EAAQ,EAPR,SAAqBtzB,EAAI2zB,EAAIC,QACd,IAAPD,IAAiBA,EAAK,QACb,IAATC,IAAmBA,EAAO,IAC9B,IAAIvkC,EAAKokC,EAAeI,QAAQ7zB,EAAI2zB,GAAKG,EAAUzkC,EAAG,GAAI0kC,EAAS1kC,EAAG,GAAI2kC,EAAQ3kC,EAAG,GAErF,OADAmkC,EAAQ9kC,UAAUslC,EAAOJ,GAClB,CAACE,EAASC,EACrB,C,8BCTA,IAAIP,EAAU,EAAQ,MAQtBF,EAAQ,EAPR,SAAqBvK,GACjB,IAAIv5B,EAAMgkC,EAAQze,SAIlB,OAHAye,EAAQ9kC,WAAU,WACdc,EAAIwgB,QAAU+Y,CAClB,IACOv5B,EAAIwgB,OACf,C,8BCRAvtB,OAAOwxC,eAAeX,EAAS,aAAc,CAAE19B,OAAO,IACtD,IAAI49B,EAAU,EAAQ,MA8BtBF,EAAA,QA7BA,SAAsBtzB,EAAI2zB,QACX,IAAPA,IAAiBA,EAAK,GAC1B,IAAIO,EAAQV,EAAQze,QAAO,GACvBof,EAAUX,EAAQze,SAClB0T,EAAW+K,EAAQze,OAAO/U,GAC1B8zB,EAAUN,EAAQxiB,aAAY,WAAc,OAAOkjB,EAAMlkB,OAAS,GAAG,IACrE1iB,EAAMkmC,EAAQxiB,aAAY,WAC1BkjB,EAAMlkB,SAAU,EAChBmkB,EAAQnkB,SAAWuU,aAAa4P,EAAQnkB,SACxCmkB,EAAQnkB,QAAUyU,YAAW,WACzByP,EAAMlkB,SAAU,EAChByY,EAASzY,SACb,GAAG2jB,EACP,GAAG,CAACA,IACAS,EAAQZ,EAAQxiB,aAAY,WAC5BkjB,EAAMlkB,QAAU,KAChBmkB,EAAQnkB,SAAWuU,aAAa4P,EAAQnkB,QAC5C,GAAG,IAUH,OARAwjB,EAAQ9kC,WAAU,WACd+5B,EAASzY,QAAUhQ,CACvB,GAAG,CAACA,IAEJwzB,EAAQ9kC,WAAU,WAEd,OADApB,IACO8mC,CACX,GAAG,CAACT,IACG,CAACG,EAASM,EAAO9mC,EAC5B,C,yBCtBA,IAAI+mC,EAAM,CAAC,EAqBPC,EAAKC,WAAYC,EAAMC,YAAaC,EAAMC,WAE1CC,EAAO,IAAIN,EAAG,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAgB,EAAG,EAAoB,IAE1IO,EAAO,IAAIP,EAAG,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAiB,EAAG,IAEjIQ,EAAO,IAAIR,EAAG,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,KAE7ES,EAAO,SAAUC,EAAInzC,GAErB,IADA,IAAInD,EAAI,IAAI81C,EAAI,IACPvyC,EAAI,EAAGA,EAAI,KAAMA,EACtBvD,EAAEuD,GAAKJ,GAAS,GAAKmzC,EAAG/yC,EAAI,GAGhC,IAAImL,EAAI,IAAIsnC,EAAIh2C,EAAE,KAClB,IAASuD,EAAI,EAAGA,EAAI,KAAMA,EACtB,IAAK,IAAI0C,EAAIjG,EAAEuD,GAAI0C,EAAIjG,EAAEuD,EAAI,KAAM0C,EAC/ByI,EAAEzI,GAAOA,EAAIjG,EAAEuD,IAAO,EAAKA,EAGnC,MAAO,CAAEvD,EAAGA,EAAG0O,EAAGA,EACtB,EACIiC,EAAK0lC,EAAKH,EAAM,GAAIK,EAAK5lC,EAAG3Q,EAAGw2C,EAAQ7lC,EAAGjC,EAE9C6nC,EAAG,IAAM,IAAKC,EAAM,KAAO,GAI3B,IAHA,IAAIzlC,EAAKslC,EAAKF,EAAM,GAAIM,EAAK1lC,EAAG/Q,EAAG02C,EAAQ3lC,EAAGrC,EAE1CioC,EAAM,IAAIb,EAAI,OACTvyC,EAAI,EAAGA,EAAI,QAASA,EAAG,CAE5B,IAAI2M,GAAU,MAAJ3M,IAAe,GAAW,MAAJA,IAAe,EAE/C2M,GAAU,OADVA,GAAU,MAAJA,IAAe,GAAW,MAAJA,IAAe,KACtB,GAAW,KAAJA,IAAe,EAC3CymC,EAAIpzC,KAAY,MAAJ2M,IAAe,GAAW,IAAJA,IAAe,IAAO,CAC5D,CAIA,IAAI0mC,EAAO,SAAWC,EAAIC,EAAIpoC,GAO1B,IANA,IAAIN,EAAIyoC,EAAGvxC,OAEP/B,EAAI,EAEJyK,EAAI,IAAI8nC,EAAIgB,GAETvzC,EAAI6K,IAAK7K,EACRszC,EAAGtzC,MACDyK,EAAE6oC,EAAGtzC,GAAK,GAGpB,IAIIwzC,EAJAC,EAAK,IAAIlB,EAAIgB,GACjB,IAAKvzC,EAAI,EAAGA,EAAIuzC,IAAMvzC,EAClByzC,EAAGzzC,GAAMyzC,EAAGzzC,EAAI,GAAKyK,EAAEzK,EAAI,IAAO,EAGtC,GAAImL,EAAG,CAEHqoC,EAAK,IAAIjB,EAAI,GAAKgB,GAElB,IAAIG,EAAM,GAAKH,EACf,IAAKvzC,EAAI,EAAGA,EAAI6K,IAAK7K,EAEjB,GAAIszC,EAAGtzC,GAQH,IANA,IAAI2zC,EAAM3zC,GAAK,EAAKszC,EAAGtzC,GAEnB4zC,EAAML,EAAKD,EAAGtzC,GAEdD,EAAI0zC,EAAGH,EAAGtzC,GAAK,MAAQ4zC,EAElBpxC,EAAIzC,GAAM,GAAK6zC,GAAO,EAAI7zC,GAAKyC,IAAKzC,EAEzCyzC,EAAGJ,EAAIrzC,IAAM2zC,GAAOC,CAIpC,MAGI,IADAH,EAAK,IAAIjB,EAAI1nC,GACR7K,EAAI,EAAGA,EAAI6K,IAAK7K,EACbszC,EAAGtzC,KACHwzC,EAAGxzC,GAAKozC,EAAIK,EAAGH,EAAGtzC,GAAK,OAAU,GAAKszC,EAAGtzC,IAIrD,OAAOwzC,CACV,EAEGK,EAAM,IAAIxB,EAAG,KACjB,IAASryC,EAAI,EAAGA,EAAI,MAAOA,EACvB6zC,EAAI7zC,GAAK,EACb,IAASA,EAAI,IAAKA,EAAI,MAAOA,EACzB6zC,EAAI7zC,GAAK,EACb,IAASA,EAAI,IAAKA,EAAI,MAAOA,EACzB6zC,EAAI7zC,GAAK,EACb,IAASA,EAAI,IAAKA,EAAI,MAAOA,EACzB6zC,EAAI7zC,GAAK,EAEb,IAAI8zC,EAAM,IAAIzB,EAAG,IACjB,IAASryC,EAAI,EAAGA,EAAI,KAAMA,EACtB8zC,EAAI9zC,GAAK,EAEb,IAAI+zC,EAAoBV,EAAKQ,EAAK,EAAG,GAAIG,EAAqBX,EAAKQ,EAAK,EAAG,GAEvEI,EAAoBZ,EAAKS,EAAK,EAAG,GAAII,EAAqBb,EAAKS,EAAK,EAAG,GAEvEl7B,EAAM,SAAUpc,GAEhB,IADA,IAAIgG,EAAIhG,EAAE,GACDwD,EAAI,EAAGA,EAAIxD,EAAEuF,SAAU/B,EACxBxD,EAAEwD,GAAKwC,IACPA,EAAIhG,EAAEwD,IAEd,OAAOwC,CACX,EAEI2xC,EAAO,SAAU56B,EAAGhY,EAAGiB,GACvB,IAAI4H,EAAK7I,EAAI,EAAK,EAClB,OAASgY,EAAEnP,GAAMmP,EAAEnP,EAAI,IAAM,KAAY,EAAJ7I,GAAUiB,CACnD,EAEI4xC,EAAS,SAAU76B,EAAGhY,GACtB,IAAI6I,EAAK7I,EAAI,EAAK,EAClB,OAASgY,EAAEnP,GAAMmP,EAAEnP,EAAI,IAAM,EAAMmP,EAAEnP,EAAI,IAAM,MAAa,EAAJ7I,EAC5D,EAEI8yC,EAAO,SAAU9yC,GAAK,OAASA,EAAI,GAAK,EAAK,CAAG,EAGhD+yC,EAAM,SAAUv0C,EAAG8K,EAAGZ,GAMtB,OALS,MAALY,GAAaA,EAAI,KACjBA,EAAI,IACC,MAALZ,GAAaA,EAAIlK,EAAEgC,UACnBkI,EAAIlK,EAAEgC,QAEH,IAAIswC,EAAGtyC,EAAEw0C,SAAS1pC,EAAGZ,GAChC,EAsBIuqC,EAAK,CACL,iBACA,qBACA,yBACA,mBACA,kBACA,oBACA,CACA,cACA,qBACA,uBACA,8BACA,oBACA,mBACA,oBAIAC,EAAM,SAAUC,EAAKC,EAAKC,GAC1B,IAAI3qC,EAAI,IAAIiR,MAAMy5B,GAAOH,EAAGE,IAI5B,GAHAzqC,EAAE4qC,KAAOH,EACLx5B,MAAM45B,mBACN55B,MAAM45B,kBAAkB7qC,EAAGwqC,IAC1BG,EACD,MAAM3qC,EACV,OAAOA,CACX,EAEI8qC,EAAQ,SAAUC,EAAKC,EAAIC,EAAKC,GAEhC,IAAIC,EAAKJ,EAAIjzC,OAAQszC,EAAKF,EAAOA,EAAKpzC,OAAS,EAC/C,IAAKqzC,GAAMH,EAAG78B,IAAM68B,EAAGxqC,EACnB,OAAOyqC,GAAO,IAAI7C,EAAG,GACzB,IAAIiD,GAASJ,EAETK,EAASD,GAAiB,GAARL,EAAGj1C,EAErBw1C,EAAOP,EAAGj1C,EAEVs1C,IACAJ,EAAM,IAAI7C,EAAQ,EAAL+C,IAEjB,IAAIK,EAAO,SAAUhrC,GACjB,IAAIirC,EAAKR,EAAInzC,OAEb,GAAI0I,EAAIirC,EAAI,CAER,IAAIC,EAAO,IAAItD,EAAGrwC,KAAK4W,IAAS,EAAL88B,EAAQjrC,IACnCkrC,EAAKtqC,IAAI6pC,GACTA,EAAMS,CACV,CACJ,EAEIC,EAAQX,EAAG78B,GAAK,EAAGqd,EAAMwf,EAAG1zC,GAAK,EAAGs0C,EAAKZ,EAAGx4C,GAAK,EAAGq5C,EAAKb,EAAGxqC,EAAGsrC,EAAKd,EAAG17B,EAAGy8B,EAAMf,EAAGzyC,EAAGyzC,EAAMhB,EAAGtqC,EAE/FurC,EAAY,EAALd,EACX,EAAG,CACC,IAAKU,EAAI,CAELF,EAAQzB,EAAKa,EAAKvf,EAAK,GAEvB,IAAIkG,EAAOwY,EAAKa,EAAKvf,EAAM,EAAG,GAE9B,GADAA,GAAO,GACFkG,EAAM,CAEP,IAAuBlxB,EAAIuqC,GAAvBnqC,EAAIwpC,EAAK5e,GAAO,GAAe,GAAMuf,EAAInqC,EAAI,IAAM,EAAIjI,EAAIiI,EAAIJ,EACnE,GAAI7H,EAAIwyC,EAAI,CACJI,GACAf,EAAI,GACR,KACJ,CAEIc,GACAE,EAAKI,EAAKprC,GAEdyqC,EAAI7pC,IAAI2pC,EAAIT,SAAS1pC,EAAGjI,GAAIizC,GAE5BZ,EAAGx4C,EAAIo5C,GAAMprC,EAAGwqC,EAAG1zC,EAAIk0B,EAAU,EAAJ7yB,EAAOqyC,EAAG78B,EAAIw9B,EAC3C,QACJ,CACK,GAAY,GAARja,EACLma,EAAK9B,EAAM+B,EAAK7B,EAAM8B,EAAM,EAAGC,EAAM,OACpC,GAAY,GAARta,EAAW,CAEhB,IAAIwa,EAAOhC,EAAKa,EAAKvf,EAAK,IAAM,IAAK2gB,EAAQjC,EAAKa,EAAKvf,EAAM,GAAI,IAAM,EACnE4gB,EAAKF,EAAOhC,EAAKa,EAAKvf,EAAM,EAAG,IAAM,EACzCA,GAAO,GAKP,IAHA,IAAI6gB,EAAM,IAAIjE,EAAGgE,GAEbE,EAAM,IAAIlE,EAAG,IACRryC,EAAI,EAAGA,EAAIo2C,IAASp2C,EAEzBu2C,EAAI1D,EAAK7yC,IAAMm0C,EAAKa,EAAKvf,EAAU,EAAJz1B,EAAO,GAE1Cy1B,GAAe,EAAR2gB,EAEP,IAAII,EAAM59B,EAAI29B,GAAME,GAAU,GAAKD,GAAO,EAEtCE,EAAMrD,EAAKkD,EAAKC,EAAK,GACzB,IAASx2C,EAAI,EAAGA,EAAIq2C,GAAK,CACrB,IAIIxrC,EAJAM,EAAIurC,EAAIvC,EAAKa,EAAKvf,EAAKghB,IAM3B,GAJAhhB,GAAW,GAAJtqB,GAEHN,EAAIM,GAAK,GAEL,GACJmrC,EAAIt2C,KAAO6K,MAEV,CAED,IAAI5G,EAAI,EAAG0G,EAAI,EAOf,IANS,IAALE,GACAF,EAAI,EAAIwpC,EAAKa,EAAKvf,EAAK,GAAIA,GAAO,EAAGxxB,EAAIqyC,EAAIt2C,EAAI,IACvC,IAAL6K,GACLF,EAAI,EAAIwpC,EAAKa,EAAKvf,EAAK,GAAIA,GAAO,GACxB,IAAL5qB,IACLF,EAAI,GAAKwpC,EAAKa,EAAKvf,EAAK,KAAMA,GAAO,GAClC9qB,KACH2rC,EAAIt2C,KAAOiE,CACnB,CACJ,CAEA,IAAI0yC,EAAKL,EAAI/B,SAAS,EAAG4B,GAAOS,EAAKN,EAAI/B,SAAS4B,GAElDH,EAAMp9B,EAAI+9B,GAEVV,EAAMr9B,EAAIg+B,GACVd,EAAKzC,EAAKsD,EAAIX,EAAK,GACnBD,EAAK1C,EAAKuD,EAAIX,EAAK,EACvB,MAEIxB,EAAI,GACR,GAAIhf,EAAMygB,EAAM,CACRV,GACAf,EAAI,GACR,KACJ,CACJ,CAGIc,GACAE,EAAKI,EAAK,QAGd,IAFA,IAAIgB,GAAO,GAAKb,GAAO,EAAGc,GAAO,GAAKb,GAAO,EACzCc,EAAOthB,GACHshB,EAAOthB,EAAK,CAEhB,IAAoCuhB,IAAhC/yC,EAAI6xC,EAAG1B,EAAOY,EAAKvf,GAAOohB,KAAiB,EAE/C,IADAphB,GAAW,GAAJxxB,GACGiyC,EAAM,CACRV,GACAf,EAAI,GACR,KACJ,CAGA,GAFKxwC,GACDwwC,EAAI,GACJuC,GAAM,IACN9B,EAAIW,KAAQmB,OACX,IAAW,KAAPA,GAAY,CACjBD,EAAOthB,EAAKqgB,EAAK,KACjB,KACJ,CAEI,IAAI1sC,GAAM4tC,GAAM,IAEhB,GAAIA,GAAM,IAAK,CAEX,IAAmBv6C,GAAIk2C,EAAnB3yC,EAAIg3C,GAAM,KACd5tC,GAAM+qC,EAAKa,EAAKvf,GAAM,GAAKh5B,IAAK,GAAKu2C,EAAGhzC,GACxCy1B,GAAOh5B,EACX,CAEA,IAAI8c,GAAIw8B,EAAG3B,EAAOY,EAAKvf,GAAOqhB,GAAMG,GAAO19B,IAAK,EAC3CA,IACDk7B,EAAI,GACRhf,GAAW,GAAJlc,GACHq9B,EAAK1D,EAAG+D,IACZ,GAAIA,GAAO,EAAG,CACNx6C,GAAIm2C,EAAKqE,IACbL,GAAMxC,EAAOY,EAAKvf,IAAQ,GAAKh5B,IAAK,EAAGg5B,GAAOh5B,EAClD,CACA,GAAIg5B,EAAMygB,EAAM,CACRV,GACAf,EAAI,GACR,KACJ,CACIc,GACAE,EAAKI,EAAK,QACd,IAAIqB,GAAMrB,EAAKzsC,GACf,GAAIysC,EAAKe,EAAI,CACT,IAAI5lB,GAAQqkB,EAAKuB,EAAIO,GAAOn1C,KAAKC,IAAI20C,EAAIM,IAGzC,IAFIlmB,GAAQ6kB,EAAK,GACbpB,EAAI,GACDoB,EAAKsB,KAAQtB,EAChBX,EAAIW,GAAMV,EAAKnkB,GAAQ6kB,EAC/B,CACA,KAAOA,EAAKqB,KAAOrB,EACfX,EAAIW,GAAMX,EAAIW,EAAKe,EAC3B,CACJ,CACA3B,EAAGxqC,EAAIqrC,EAAIb,EAAG1zC,EAAIw1C,EAAM9B,EAAGx4C,EAAIo5C,EAAIZ,EAAG78B,EAAIw9B,EACtCE,IACAF,EAAQ,EAAGX,EAAGzyC,EAAIwzC,EAAKf,EAAG17B,EAAIw8B,EAAId,EAAGtqC,EAAIsrC,EACjD,QAAUL,GAEV,OAAOC,GAAMX,EAAInzC,QAAUuzC,EAAQhB,EAAIY,EAAK,EAAGW,GAAMX,EAAIX,SAAS,EAAGsB,EACzE,EAEIuB,EAAQ,SAAU79B,EAAGhY,EAAGxB,GACxBA,IAAU,EAAJwB,EACN,IAAI6I,EAAK7I,EAAI,EAAK,EAClBgY,EAAEnP,IAAMrK,EACRwZ,EAAEnP,EAAI,IAAMrK,GAAK,CACrB,EAEIs3C,EAAU,SAAU99B,EAAGhY,EAAGxB,GAC1BA,IAAU,EAAJwB,EACN,IAAI6I,EAAK7I,EAAI,EAAK,EAClBgY,EAAEnP,IAAMrK,EACRwZ,EAAEnP,EAAI,IAAMrK,GAAK,EACjBwZ,EAAEnP,EAAI,IAAMrK,GAAK,EACrB,EAEIu3C,EAAQ,SAAU/9B,EAAGg6B,GAGrB,IADA,IAAI3wC,EAAI,GACC5C,EAAI,EAAGA,EAAIuZ,EAAExX,SAAU/B,EACxBuZ,EAAEvZ,IACF4C,EAAEH,KAAK,CAAEoI,EAAG7K,EAAGoY,EAAGmB,EAAEvZ,KAE5B,IAAI6K,EAAIjI,EAAEb,OACNw1C,EAAK30C,EAAEW,QACX,IAAKsH,EACD,MAAO,CAAEjI,EAAG40C,EAAI/sC,EAAG,GACvB,GAAS,GAALI,EAAQ,CACR,IAAI9K,EAAI,IAAIsyC,EAAGzvC,EAAE,GAAGiI,EAAI,GAExB,OADA9K,EAAE6C,EAAE,GAAGiI,GAAK,EACL,CAAEjI,EAAG7C,EAAG0K,EAAG,EACtB,CACA7H,EAAEzD,MAAK,SAAU3C,EAAGC,GAAK,OAAOD,EAAE4b,EAAI3b,EAAE2b,CAAG,IAG3CxV,EAAEH,KAAK,CAAEoI,GAAI,EAAGuN,EAAG,QACnB,IAAI3N,EAAI7H,EAAE,GAAIuI,EAAIvI,EAAE,GAAI60C,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAO7C,IANA/0C,EAAE,GAAK,CAAEiI,GAAI,EAAGuN,EAAG3N,EAAE2N,EAAIjN,EAAEiN,EAAG3N,EAAGA,EAAGU,EAAGA,GAMhCusC,GAAM7sC,EAAI,GACbJ,EAAI7H,EAAEA,EAAE60C,GAAIr/B,EAAIxV,EAAE+0C,GAAIv/B,EAAIq/B,IAAOE,KACjCxsC,EAAIvI,EAAE60C,GAAMC,GAAM90C,EAAE60C,GAAIr/B,EAAIxV,EAAE+0C,GAAIv/B,EAAIq/B,IAAOE,KAC7C/0C,EAAE80C,KAAQ,CAAE7sC,GAAI,EAAGuN,EAAG3N,EAAE2N,EAAIjN,EAAEiN,EAAG3N,EAAGA,EAAGU,EAAGA,GAE9C,IAAIysC,EAASL,EAAG,GAAG1sC,EACnB,IAAS7K,EAAI,EAAGA,EAAI6K,IAAK7K,EACjBu3C,EAAGv3C,GAAG6K,EAAI+sC,IACVA,EAASL,EAAGv3C,GAAG6K,GAGvB,IAAIgtC,EAAK,IAAItF,EAAIqF,EAAS,GAEtBE,EAAMC,EAAGn1C,EAAE80C,EAAK,GAAIG,EAAI,GAC5B,GAAIC,EAAMvE,EAAI,CAINvzC,EAAI,EAAR,IAAW42C,EAAK,EAEZoB,EAAMF,EAAMvE,EAAI0E,EAAM,GAAKD,EAE/B,IADAT,EAAGp4C,MAAK,SAAU3C,EAAGC,GAAK,OAAOo7C,EAAGp7C,EAAEoO,GAAKgtC,EAAGr7C,EAAEqO,IAAMrO,EAAE4b,EAAI3b,EAAE2b,CAAG,IAC1DpY,EAAI6K,IAAK7K,EAAG,CACf,IAAIk4C,EAAOX,EAAGv3C,GAAG6K,EACjB,KAAIgtC,EAAGK,GAAQ3E,GAKX,MAJAqD,GAAMqB,GAAO,GAAMH,EAAMD,EAAGK,IAC5BL,EAAGK,GAAQ3E,CAInB,CAEA,IADAqD,IAAOoB,EACApB,EAAK,GAAG,CACX,IAAIuB,EAAOZ,EAAGv3C,GAAG6K,EACbgtC,EAAGM,GAAQ5E,EACXqD,GAAM,GAAMrD,EAAKsE,EAAGM,KAAU,IAE5Bn4C,CACV,CACA,KAAOA,GAAK,GAAK42C,IAAM52C,EAAG,CACtB,IAAIo4C,EAAOb,EAAGv3C,GAAG6K,EACbgtC,EAAGO,IAAS7E,MACVsE,EAAGO,KACHxB,EAEV,CACAkB,EAAMvE,CACV,CACA,MAAO,CAAE3wC,EAAG,IAAIyvC,EAAGwF,GAAKptC,EAAGqtC,EAC/B,EAEIC,EAAK,SAAUptC,EAAGF,EAAG8O,GACrB,OAAe,GAAR5O,EAAEE,EACH7I,KAAK4W,IAAIm/B,EAAGptC,EAAEF,EAAGA,EAAG8O,EAAI,GAAIw+B,EAAGptC,EAAEQ,EAAGV,EAAG8O,EAAI,IAC1C9O,EAAEE,EAAEE,GAAK0O,CACpB,EAEI8+B,EAAK,SAAUp0C,GAGf,IAFA,IAAI4G,EAAI5G,EAAElC,OAEH8I,IAAM5G,IAAI4G,KAMjB,IAJA,IAAIytC,EAAK,IAAI/F,IAAM1nC,GAEf0tC,EAAM,EAAGC,EAAMv0C,EAAE,GAAIw0C,EAAM,EAC3BC,EAAI,SAAU34C,GAAKu4C,EAAGC,KAASx4C,CAAG,EAC7BC,EAAI,EAAGA,GAAK6K,IAAK7K,EACtB,GAAIiE,EAAEjE,IAAMw4C,GAAOx4C,GAAK6K,IAClB4tC,MACD,CACD,IAAKD,GAAOC,EAAM,EAAG,CACjB,KAAOA,EAAM,IAAKA,GAAO,IACrBC,EAAE,OACFD,EAAM,IACNC,EAAED,EAAM,GAAOA,EAAM,IAAO,EAAK,MAAUA,EAAM,GAAM,EAAK,OAC5DA,EAAM,EAEd,MACK,GAAIA,EAAM,EAAG,CAEd,IADAC,EAAEF,KAAQC,EACHA,EAAM,EAAGA,GAAO,EACnBC,EAAE,MACFD,EAAM,IACNC,EAAID,EAAM,GAAM,EAAK,MAAOA,EAAM,EAC1C,CACA,KAAOA,KACHC,EAAEF,GACNC,EAAM,EACND,EAAMv0C,EAAEjE,EACZ,CAEJ,MAAO,CAAEiE,EAAGq0C,EAAG/D,SAAS,EAAGgE,GAAM5tC,EAAGE,EACxC,EAEI8tC,EAAO,SAAUC,EAAIN,GAErB,IADA,IAAI7tC,EAAI,EACCzK,EAAI,EAAGA,EAAIs4C,EAAGv2C,SAAU/B,EAC7ByK,GAAKmuC,EAAG54C,GAAKs4C,EAAGt4C,GACpB,OAAOyK,CACX,EAGIouC,EAAQ,SAAUt0C,EAAKkxB,EAAKuf,GAE5B,IAAInqC,EAAImqC,EAAIjzC,OACRqI,EAAIiqC,EAAK5e,EAAM,GACnBlxB,EAAI6F,GAAS,IAAJS,EACTtG,EAAI6F,EAAI,GAAKS,GAAK,EAClBtG,EAAI6F,EAAI,GAAc,IAAT7F,EAAI6F,GACjB7F,EAAI6F,EAAI,GAAkB,IAAb7F,EAAI6F,EAAI,GACrB,IAAK,IAAIpK,EAAI,EAAGA,EAAI6K,IAAK7K,EACrBuE,EAAI6F,EAAIpK,EAAI,GAAKg1C,EAAIh1C,GACzB,OAAqB,GAAboK,EAAI,EAAIS,EACpB,EAEIiuC,EAAO,SAAU9D,EAAKzwC,EAAKqxC,EAAOmD,EAAMC,EAAIC,EAAIlG,EAAImG,EAAIC,EAAIzD,EAAIn0C,GAChE61C,EAAM7yC,EAAKhD,IAAKq0C,KACdoD,EAAG,KAML,IALA,IAAI5rC,EAAKkqC,EAAM0B,EAAI,IAAKI,EAAMhsC,EAAGxK,EAAGy2C,EAAMjsC,EAAG3C,EACzC+C,EAAK8pC,EAAM2B,EAAI,IAAKK,EAAM9rC,EAAG5K,EAAG22C,EAAM/rC,EAAG/C,EACzCiuB,EAAK2f,EAAGe,GAAMI,EAAO9gB,EAAGz0B,EAAGw1C,EAAM/gB,EAAG/tB,EACpC+uC,EAAKrB,EAAGiB,GAAMK,EAAOD,EAAGz1C,EAAG21C,EAAMF,EAAG/uC,EACpCkvC,EAAS,IAAItH,EAAI,IACZvyC,EAAI,EAAGA,EAAIw5C,EAAKz3C,SAAU/B,IAC7B65C,EAAiB,GAAVL,EAAKx5C,IAClB,IAASA,EAAI,EAAGA,EAAI25C,EAAK53C,SAAU/B,IAC7B65C,EAAiB,GAAVF,EAAK35C,IAGlB,IAFA,IAAI85C,EAAKxC,EAAMuC,EAAQ,GAAIE,EAAMD,EAAGl3C,EAAGo3C,EAAOF,EAAGrvC,EAC7CwvC,EAAO,GACJA,EAAO,IAAMF,EAAIlH,EAAKoH,EAAO,MAAOA,GAE3C,IAKInE,EAAIoE,EAAInE,EAAIV,EALZ8E,EAAQzE,EAAK,GAAM,EACnB0E,EAAQzB,EAAKK,EAAInF,GAAO8E,EAAKM,EAAInF,GAAOf,EACxCsH,EAAQ1B,EAAKK,EAAII,GAAOT,EAAKM,EAAIK,GAAOvG,EAAK,GAAK,EAAIkH,EAAOtB,EAAKkB,EAAQE,GAAO,EAAIF,EAAO,IAAM,EAAIA,EAAO,IAAM,EAAIA,EAAO,IAClI,GAAIV,GAAM,GAAKgB,GAAQC,GAASD,GAAQE,EACpC,OAAOxB,EAAMt0C,EAAKhD,EAAGyzC,EAAIT,SAAS4E,EAAIA,EAAKzD,IAG/C,GADA0B,EAAM7yC,EAAKhD,EAAG,GAAK84C,EAAQD,IAAS74C,GAAK,EACrC84C,EAAQD,EAAO,CACftE,EAAKzC,EAAK+F,EAAKC,EAAK,GAAIa,EAAKd,EAAKrD,EAAK1C,EAAKiG,EAAKC,EAAK,GAAIlE,EAAKiE,EAC/D,IAAIgB,EAAMjH,EAAK0G,EAAKC,EAAM,GAC1B5C,EAAM7yC,EAAKhD,EAAGk4C,EAAM,KACpBrC,EAAM7yC,EAAKhD,EAAI,EAAGq4C,EAAM,GACxBxC,EAAM7yC,EAAKhD,EAAI,GAAI04C,EAAO,GAC1B14C,GAAK,GACL,IAASvB,EAAI,EAAGA,EAAIi6C,IAAQj6C,EACxBo3C,EAAM7yC,EAAKhD,EAAI,EAAIvB,EAAG+5C,EAAIlH,EAAK7yC,KACnCuB,GAAK,EAAI04C,EAET,IADA,IAAIM,EAAO,CAACf,EAAMG,GACTa,EAAK,EAAGA,EAAK,IAAKA,EACvB,KAAIC,GAAOF,EAAKC,GAChB,IAASx6C,EAAI,EAAGA,EAAIy6C,GAAK14C,SAAU/B,EAAG,CAClC,IAAIiF,GAAgB,GAAVw1C,GAAKz6C,GACfo3C,EAAM7yC,EAAKhD,EAAG+4C,EAAIr1C,KAAO1D,GAAKw4C,EAAI90C,IAC9BA,GAAM,KACNmyC,EAAM7yC,EAAKhD,EAAIk5C,GAAKz6C,IAAM,EAAK,KAAMuB,GAAKk5C,GAAKz6C,IAAM,GAC7D,CANmB,CAQ3B,MAEI81C,EAAK/B,EAAKmG,EAAKrG,EAAKkC,EAAK9B,EAAKoB,EAAKvB,EAEvC,IAAS9zC,EAAI,EAAGA,EAAIk5C,IAAMl5C,EAAG,CACzB,IAAIg3C,GAAM+B,EAAK/4C,GACf,GAAIg3C,GAAM,IAAK,CAEXK,EAAQ9yC,EAAKhD,EAAGu0C,GADZ7wC,GAAO+xC,IAAO,GAAM,IACC,MAAOz1C,GAAK24C,EAAGj1C,GAAM,KAC1CA,GAAM,IACNmyC,EAAM7yC,EAAKhD,EAAIy1C,IAAO,GAAM,IAAKz1C,GAAKoxC,EAAK1tC,KAC/C,IAAIy1C,GAAY,GAAN1D,GACVK,EAAQ9yC,EAAKhD,EAAGw0C,EAAG2E,KAAOn5C,GAAK8zC,EAAGqF,IAC9BA,GAAM,IACNrD,EAAQ9yC,EAAKhD,EAAIy1C,IAAO,EAAK,MAAOz1C,GAAKqxC,EAAK8H,IACtD,MAEIrD,EAAQ9yC,EAAKhD,EAAGu0C,EAAGkB,KAAOz1C,GAAK24C,EAAGlD,GAE1C,CAEA,OADAK,EAAQ9yC,EAAKhD,EAAGu0C,EAAG,MACZv0C,EAAI24C,EAAG,IAClB,EAEIS,EAAoB,IAAIlI,EAAI,CAAC,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,QAAS,QAAS,QAAS,UAE/F+E,EAAmB,IAAInF,EAAG,GAE1BuI,EAAO,SAAU5F,EAAK6F,EAAKC,EAAMC,EAAKC,EAAM/F,GAC5C,IAAIpqC,EAAIoqC,EAAGlqC,GAAKiqC,EAAIjzC,OAChBqI,EAAI,IAAIioC,EAAG0I,EAAMlwC,EAAI,GAAK,EAAI7I,KAAKi5C,KAAKpwC,EAAI,MAASmwC,GAErDtC,EAAItuC,EAAEmqC,SAASwG,EAAK3wC,EAAErI,OAASi5C,GAC/BE,EAAMjG,EAAGxqC,EACTgrB,EAAoB,GAAbwf,EAAG9pC,GAAK,GACnB,GAAI0vC,EAAK,CACDplB,IACAijB,EAAE,GAAKzD,EAAG9pC,GAAK,GAenB,IAdA,IAAIgwC,EAAMR,EAAIE,EAAM,GAChBlwC,EAAIwwC,GAAO,GAAIl3C,EAAU,KAANk3C,EACnBC,GAAS,GAAKN,GAAQ,EAEtBO,EAAOpG,EAAG1zC,GAAK,IAAIgxC,EAAI,OAAQ7M,EAAOuP,EAAG99B,GAAK,IAAIo7B,EAAI6I,EAAQ,GAC9DE,EAAQt5C,KAAKi5C,KAAKH,EAAO,GAAIS,EAAQ,EAAID,EACzCE,EAAM,SAAUx7C,GAAK,OAAQg1C,EAAIh1C,GAAMg1C,EAAIh1C,EAAI,IAAMs7C,EAAUtG,EAAIh1C,EAAI,IAAMu7C,GAAUH,CAAO,EAG9FrC,EAAO,IAAItG,EAAI,MAEfuG,EAAK,IAAIzG,EAAI,KAAM0G,EAAK,IAAI1G,EAAI,IAEhCkJ,EAAO,EAAG1I,EAAK,EAAG/yC,EAAIi1C,EAAGj1C,GAAK,EAAGk5C,EAAK,EAAGwC,EAAKzG,EAAGyD,GAAK,EAAGS,EAAK,EAC3Dn5C,EAAI,EAAI6K,IAAK7K,EAAG,CAEnB,IAAI27C,EAAKH,EAAIx7C,GAET47C,EAAW,MAAJ57C,EAAW67C,EAAQnW,EAAKiW,GAKnC,GAJAN,EAAKO,GAAQC,EACbnW,EAAKiW,GAAMC,EAGPF,GAAM17C,EAAG,CAET,IAAI87C,EAAMjxC,EAAI7K,EACd,IAAKy7C,EAAO,KAAQvC,EAAK,SAAW4C,EAAM,MAAQZ,GAAM,CACpDzlB,EAAMqjB,EAAK9D,EAAK0D,EAAG,EAAGK,EAAMC,EAAIC,EAAIlG,EAAImG,EAAIC,EAAIn5C,EAAIm5C,EAAI1jB,GACxDyjB,EAAKuC,EAAO1I,EAAK,EAAGoG,EAAKn5C,EACzB,IAAK,IAAI0C,EAAI,EAAGA,EAAI,MAAOA,EACvBs2C,EAAGt2C,GAAK,EACZ,IAASA,EAAI,EAAGA,EAAI,KAAMA,EACtBu2C,EAAGv2C,GAAK,CAChB,CAEA,IAAI+H,EAAI,EAAG8O,EAAI,EAAGwiC,EAAO93C,EAAG+3C,EAAMJ,EAAOC,EAAQ,MACjD,GAAIC,EAAM,GAAKH,GAAMH,EAAIx7C,EAAIg8C,GAMzB,IALA,IAAIC,EAAOj6C,KAAKC,IAAI0I,EAAGmxC,GAAO,EAC1BI,EAAOl6C,KAAKC,IAAI,MAAOjC,GAGvBm8C,EAAKn6C,KAAKC,IAAI,IAAK65C,GAChBE,GAAOE,KAAUH,GAAQH,GAAQC,GAAO,CAC3C,GAAI7G,EAAIh1C,EAAIyK,IAAMuqC,EAAIh1C,EAAIyK,EAAIuxC,GAAM,CAEhC,IADA,IAAII,EAAK,EACFA,EAAKD,GAAMnH,EAAIh1C,EAAIo8C,IAAOpH,EAAIh1C,EAAIo8C,EAAKJ,KAAQI,GAEtD,GAAIA,EAAK3xC,EAAG,CAGR,GAFAA,EAAI2xC,EAAI7iC,EAAIyiC,EAERI,EAAKH,EACL,MAIJ,IAAII,GAAMr6C,KAAKC,IAAI+5C,EAAKI,EAAK,GACzBE,GAAK,EACT,IAAS55C,EAAI,EAAGA,EAAI25C,KAAO35C,EAAG,CAC1B,IAAIgG,GAAK1I,EAAIg8C,EAAMt5C,EAAI,MAEnB4wC,GAAK5qC,GADC2yC,EAAK3yC,IACK,MAChB4qC,GAAKgJ,KACLA,GAAKhJ,GAAIuI,EAAQnzC,GACzB,CACJ,CACJ,CAGAszC,IADAJ,EAAOC,IAAOA,EAAQR,EAAKO,IACL,KAC1B,CAGJ,GAAIriC,EAAG,CAGHw/B,EAAKG,KAAQ,UAAajG,EAAMxoC,IAAM,GAAM0oC,EAAM55B,GAClD,IAAIgjC,GAAiB,GAAXtJ,EAAMxoC,GAAS+xC,GAAiB,GAAXrJ,EAAM55B,GACrCw5B,GAAMJ,EAAK4J,IAAO3J,EAAK4J,MACrBxD,EAAG,IAAMuD,MACTtD,EAAGuD,IACLd,EAAK17C,EAAIyK,IACPgxC,CACN,MAEI1C,EAAKG,KAAQlE,EAAIh1C,KACfg5C,EAAGhE,EAAIh1C,GAEjB,CACJ,CACA,IAAKA,EAAIgC,KAAK4W,IAAI5Y,EAAG07C,GAAK17C,EAAI6K,IAAK7K,EAC/B+4C,EAAKG,KAAQlE,EAAIh1C,KACfg5C,EAAGhE,EAAIh1C,IAEby1B,EAAMqjB,EAAK9D,EAAK0D,EAAGwC,EAAKnC,EAAMC,EAAIC,EAAIlG,EAAImG,EAAIC,EAAIn5C,EAAIm5C,EAAI1jB,GACrDylB,IACDjG,EAAG9pC,EAAW,EAANsqB,EAAWijB,EAAGjjB,EAAM,EAAK,IAAM,EAEvCA,GAAO,EACPwf,EAAG99B,EAAIuuB,EAAMuP,EAAG1zC,EAAI85C,EAAMpG,EAAGj1C,EAAIA,EAAGi1C,EAAGyD,EAAIgD,EAEnD,KACK,CACD,IAAS17C,EAAIi1C,EAAGyD,GAAK,EAAG14C,EAAI6K,EAAIqwC,EAAKl7C,GAAK,MAAO,CAE7C,IAAIiK,GAAIjK,EAAI,MACRiK,IAAKY,IAEL6tC,EAAGjjB,EAAM,EAAK,GAAKylB,EACnBjxC,GAAIY,GAER4qB,EAAMojB,EAAMH,EAAGjjB,EAAM,EAAGuf,EAAIT,SAASv0C,EAAGiK,IAC5C,CACAgrC,EAAGj1C,EAAI6K,CACX,CACA,OAAOypC,EAAIlqC,EAAG,EAAG2wC,EAAM1G,EAAK5e,GAAOulB,EACvC,EAEIyB,EAAqB,WAErB,IADA,IAAI75C,EAAI,IAAI8vC,WAAW,KACd1yC,EAAI,EAAGA,EAAI,MAAOA,EAAG,CAE1B,IADA,IAAIiE,EAAIjE,EAAGkG,EAAI,IACNA,GACLjC,GAAU,EAAJA,IAAW,WAAcA,IAAM,EACzCrB,EAAE5C,GAAKiE,CACX,CACA,OAAOrB,CACV,CATwB,GAWrB85C,EAAM,WACN,IAAIz4C,GAAK,EACT,MAAO,CACH1C,EAAG,SAAUgY,GAGT,IADA,IAAIojC,EAAK14C,EACAjE,EAAI,EAAGA,EAAIuZ,EAAExX,SAAU/B,EAC5B28C,EAAKF,EAAW,IAALE,EAAYpjC,EAAEvZ,IAAO28C,IAAO,EAC3C14C,EAAI04C,CACR,EACApjC,EAAG,WAAc,OAAQtV,CAAG,EAEpC,EAEI24C,EAAQ,WACR,IAAIpgD,EAAI,EAAGC,EAAI,EACf,MAAO,CACH8E,EAAG,SAAUgY,GAIT,IAFA,IAAI5O,EAAInO,EAAGgG,EAAI/F,EACXgO,EAAe,EAAX8O,EAAExX,OACD/B,EAAI,EAAGA,GAAKyK,GAAI,CAErB,IADA,IAAIR,EAAIjI,KAAKC,IAAIjC,EAAI,KAAMyK,GACpBzK,EAAIiK,IAAKjK,EACZwC,GAAKmI,GAAK4O,EAAEvZ,GAChB2K,GAAS,MAAJA,GAAa,IAAMA,GAAK,IAAKnI,GAAS,MAAJA,GAAa,IAAMA,GAAK,GACnE,CACAhG,EAAImO,EAAGlO,EAAI+F,CACf,EACA+W,EAAG,WAEC,OAAY,KADZ/c,GAAK,SACe,IAAU,MAAJA,IAAe,GAAS,KADtCC,GAAK,SACyC,EAAKA,GAAK,CACxE,EAER,EAGIogD,EAAO,SAAU7H,EAAKmG,EAAKJ,EAAKC,EAAM/F,GACtC,IAAKA,IACDA,EAAK,CAAExqC,EAAG,GACN0wC,EAAI2B,YAAY,CAChB,IAAI3H,EAAOgG,EAAI2B,WAAWvI,UAAU,OAChCwI,EAAS,IAAI1K,EAAG8C,EAAKpzC,OAASizC,EAAIjzC,QACtCg7C,EAAO1xC,IAAI8pC,GACX4H,EAAO1xC,IAAI2pC,EAAKG,EAAKpzC,QACrBizC,EAAM+H,EACN9H,EAAGyD,EAAIvD,EAAKpzC,MAChB,CAEJ,OAAO64C,EAAK5F,EAAkB,MAAbmG,EAAIn7B,MAAgB,EAAIm7B,EAAIn7B,MAAkB,MAAXm7B,EAAI6B,IAAe/H,EAAGxqC,EAAIzI,KAAKi5C,KAAuD,IAAlDj5C,KAAK4W,IAAI,EAAG5W,KAAKC,IAAI,GAAID,KAAKi7C,IAAIjI,EAAIjzC,WAAmB,GAAO,GAAKo5C,EAAI6B,IAAMjC,EAAKC,EAAM/F,EACtL,EAEIiI,EAAM,SAAU1gD,EAAGC,GACnB,IAAI2N,EAAI,CAAC,EACT,IAAK,IAAIlE,KAAK1J,EACV4N,EAAElE,GAAK1J,EAAE0J,GACb,IAAK,IAAIA,KAAKzJ,EACV2N,EAAElE,GAAKzJ,EAAEyJ,GACb,OAAOkE,CACX,EAQI+yC,EAAO,SAAUp/B,EAAIq/B,EAAOC,GAI5B,IAHA,IAAIzG,EAAK74B,IACLk3B,EAAKl3B,EAAGlE,WACRyjC,EAAKrI,EAAG1xC,MAAM0xC,EAAGzuC,QAAQ,KAAO,EAAGyuC,EAAGne,YAAY,MAAMh6B,QAAQ,OAAQ,IAAIuF,MAAM,KAC7ErC,EAAI,EAAGA,EAAI42C,EAAG70C,SAAU/B,EAAG,CAChC,IAAID,EAAI62C,EAAG52C,GAAIkG,EAAIo3C,EAAGt9C,GACtB,GAAgB,mBAALD,EAAiB,CACxBq9C,GAAS,IAAMl3C,EAAI,IACnB,IAAIq3C,EAAOx9C,EAAE8Z,WACb,GAAI9Z,EAAEoW,UAEF,IAAsC,GAAlConC,EAAK/2C,QAAQ,iBAAwB,CACrC,IAAIg3C,EAAQD,EAAK/2C,QAAQ,IAAK,GAAK,EACnC42C,GAASG,EAAKh6C,MAAMi6C,EAAOD,EAAK/2C,QAAQ,IAAKg3C,GACjD,MAGI,IAAK,IAAI56C,KADTw6C,GAASG,EACKx9C,EAAEoW,UACZinC,GAAS,IAAMl3C,EAAI,cAAgBtD,EAAI,IAAM7C,EAAEoW,UAAUvT,GAAGiX,gBAIpEujC,GAASG,CACjB,MAEIF,EAAGn3C,GAAKnG,CAChB,CACA,OAAOq9C,CACX,EACIK,EAAK,GAYLC,EAAO,SAAUC,EAAKC,EAAM1rC,EAAI2rC,GAChC,IAAKJ,EAAGvrC,GAAK,CAET,IADA,IAAIkrC,EAAQ,GAAIU,EAAO,CAAC,EAAGt7C,EAAIm7C,EAAI57C,OAAS,EACnC/B,EAAI,EAAGA,EAAIwC,IAAKxC,EACrBo9C,EAAQD,EAAKQ,EAAI39C,GAAIo9C,EAAOU,GAChCL,EAAGvrC,GAAM,CAAEjO,EAAGk5C,EAAKQ,EAAIn7C,GAAI46C,EAAOU,GAAO7zC,EAAG6zC,EAChD,CACA,IAAIT,EAAKH,EAAI,CAAC,EAAGO,EAAGvrC,GAAIjI,GACxB,OAp2BK,SAAWhG,EAAGiO,EAAIyiC,EAAKoJ,EAAUF,GACtC,IAAInF,EAAI,IAAIsF,OAAO5L,EAAIlgC,KAAQkgC,EAAIlgC,GAAMo+B,IAAIM,gBAAgB,IAAIxB,KAAK,CAClEnrC,EAAI,mGACL,CAAE03B,KAAM,uBAaX,OAZA+c,EAAEuF,UAAY,SAAUh0C,GACpB,IAAIsP,EAAItP,EAAE8E,KAAMmvC,EAAK3kC,EAAE4kC,IACvB,GAAID,EAAI,CACJ,IAAIzJ,EAAM,IAAIv5B,MAAMgjC,EAAG,IACvBzJ,EAAU,KAAIyJ,EAAG,GACjBzJ,EAAI7jB,MAAQstB,EAAG,GACfL,EAAGpJ,EAAK,KACZ,MAEIoJ,EAAG,KAAMtkC,EACjB,EACAm/B,EAAE0F,YAAYzJ,EAAKoJ,GACZrF,CACV,CAm1BU2F,CAAGZ,EAAGvrC,GAAIjO,EAAI,0EAA4E25C,EAAK/jC,WAAa,IAAK3H,EAAImrC,EAlBrH,SAAUt9C,GACjB,IAAIs2C,EAAK,GACT,IAAK,IAAInwC,KAAKnG,EACNA,EAAEmG,GAAGo4C,QACLjI,EAAG5zC,MAAM1C,EAAEmG,GAAK,IAAInG,EAAEmG,GAAGgQ,YAAYnW,EAAEmG,KAAKo4C,QAGpD,OAAOjI,CACX,CAUoIkI,CAAKlB,GAAKQ,EAC9I,EAEIW,EAAS,WAAc,MAAO,CAACnM,EAAIE,EAAKE,EAAKE,EAAMC,EAAMC,EAAMG,EAAIE,EAAIc,EAAME,EAAMd,EAAKoB,EAAInB,EAAMz6B,EAAKu7B,EAAMC,EAAQC,EAAMC,EAAKG,EAAKM,EAAO0J,GAAaC,GAAKC,GAAO,EACrKC,EAAQ,WAAc,MAAO,CAACvM,EAAIE,EAAKE,EAAKE,EAAMC,EAAMC,EAAMI,EAAOE,EAAOY,EAAKF,EAAKI,EAAKH,EAAKV,EAAKuH,EAAKnD,EAAInE,EAAM+D,EAAOC,EAASC,EAAOS,EAAIM,EAAIM,EAAME,EAAOC,EAAMzE,EAAMC,EAAKsG,EAAMiC,EAAMgC,GAAaH,GAAM,EAEhNI,EAAM,WAAc,MAAO,CAACC,GAAKC,GAAMC,GAAQvC,EAAKD,EAAO,EAE3DyC,GAAO,WAAc,MAAO,CAACC,GAAKC,GAAM,EAExCC,GAAM,WAAc,MAAO,CAACC,GAAKL,GAAQrC,EAAQ,EAEjD2C,GAAO,WAAc,MAAO,CAACC,GAAM,EAEnCd,GAAM,SAAU/J,GAAO,OAAOyJ,YAAYzJ,EAAK,CAACA,EAAI2J,QAAU,EAE9DK,GAAO,SAAUv0C,GAAK,OAAOA,GAAK,CAClC7F,IAAK6F,EAAEpB,MAAQ,IAAIqpC,EAAGjoC,EAAEpB,MACxB8zC,WAAY1yC,EAAE0yC,WACf,EAWC2C,GAAQ,SAAUC,GAElB,OADAA,EAAKC,OAAS,SAAU3K,EAAKY,GAAS,OAAOwI,YAAY,CAACpJ,EAAKY,GAAQ,CAACZ,EAAIsJ,QAAU,EAC/E,SAAUsB,GACTA,EAAG7wC,KAAKhN,QACR29C,EAAKj9C,KAAKm9C,EAAG7wC,KAAK,GAAI6wC,EAAG7wC,KAAK,IAC9BqvC,YAAY,CAACwB,EAAG7wC,KAAK,GAAGhN,UAGxB29C,EAAKG,OACb,CACJ,EAEIC,GAAW,SAAUnC,EAAK+B,EAAMn/C,EAAMq9C,EAAM1rC,EAAI2tC,EAAOE,GACvD,IAAIn9C,EACA81C,EAAIgF,EAAKC,EAAKC,EAAM1rC,GAAI,SAAUuiC,EAAKO,GACnCP,GACAiE,EAAEsH,YAAaN,EAAKC,OAAO1hC,KAAKyhC,EAAMjL,IAChCrxC,MAAM68C,QAAQjL,GAED,GAAdA,EAAIjzC,QACT29C,EAAKQ,YAAclL,EAAI,GACnB0K,EAAKS,SACLT,EAAKS,QAAQnL,EAAI,MAGjBA,EAAI,IACJ0D,EAAEsH,YACNN,EAAKC,OAAO1hC,KAAKyhC,EAAMjL,EAAKO,EAAI,GAAIA,EAAI,KATxC+K,EAAI/K,EAWZ,IACA0D,EAAE0F,YAAY79C,GACdm/C,EAAKQ,WAAa,EAClBR,EAAKj9C,KAAO,SAAU8W,EAAGnB,GAChBsnC,EAAKC,QACNlL,EAAI,GACJ7xC,GACA88C,EAAKC,OAAOlL,EAAI,EAAG,EAAG,GAAI,OAAQr8B,GACtCsnC,EAAKQ,YAAc3mC,EAAExX,OACrB22C,EAAE0F,YAAY,CAAC7kC,EAAG3W,EAAIwV,GAAI,CAACmB,EAAE+kC,QACjC,EACAoB,EAAKM,UAAY,WAActH,EAAEsH,WAAa,EAC1CH,IACAH,EAAKG,MAAQ,WAAcnH,EAAE0F,YAAY,GAAK,EAEtD,EAOIa,GAAS,SAAU1lC,EAAG9c,EAAGsD,GACzB,KAAOA,IAAKtD,EACR8c,EAAE9c,GAAKsD,EAAGA,KAAO,CACzB,EAEIg/C,GAAM,SAAU96C,EAAGmG,GACnB,IAAI2T,EAAK3T,EAAEg2C,SAIX,GAHAn8C,EAAE,GAAK,GAAIA,EAAE,GAAK,IAAKA,EAAE,GAAK,EAAGA,EAAE,GAAKmG,EAAE4V,MAAQ,EAAI,EAAe,GAAX5V,EAAE4V,MAAa,EAAI,EAAG/b,EAAE,GAAK,EACxE,GAAXmG,EAAEi2C,OACFpB,GAAOh7C,EAAG,EAAGjC,KAAKmW,MAAM,IAAImoC,KAAKl2C,EAAEi2C,OAASC,KAAKC,OAAS,MAC1DxiC,EAAI,CACJ9Z,EAAE,GAAK,EACP,IAAK,IAAIjE,EAAI,EAAGA,GAAK+d,EAAGhc,SAAU/B,EAC9BiE,EAAEjE,EAAI,IAAM+d,EAAGmO,WAAWlsB,EAClC,CACJ,EAGIm/C,GAAM,SAAU5lC,GACJ,IAARA,EAAE,IAAoB,KAARA,EAAE,IAAqB,GAARA,EAAE,IAC/Bk7B,EAAI,EAAG,qBACX,IAAI+L,EAAMjnC,EAAE,GACR07B,EAAK,GACC,EAANuL,IACAvL,GAA6B,GAAtB17B,EAAE,IAAMA,EAAE,KAAO,IAC5B,IAAK,IAAIknC,GAAMD,GAAO,EAAI,IAAMA,GAAO,EAAI,GAAIC,EAAK,EAAGA,IAAOlnC,EAAE07B,MAEhE,OAAOA,GAAY,EAANuL,EACjB,EAEIpB,GAAM,SAAU7lC,GAChB,IAAI9O,EAAI8O,EAAExX,OACV,OAAQwX,EAAE9O,EAAI,GAAK8O,EAAE9O,EAAI,IAAM,EAAI8O,EAAE9O,EAAI,IAAM,GAAK8O,EAAE9O,EAAI,IAAM,MAAQ,CAC5E,EAEIu0C,GAAO,SAAU50C,GAAK,OAAO,IAAMA,EAAEg2C,SAAWh2C,EAAEg2C,SAASr+C,OAAS,EAAI,EAAI,EAE5Eu9C,GAAM,SAAUr7C,EAAGmG,GACnB,IAAIs2C,EAAKt2C,EAAE4V,MAAOgzB,EAAW,GAAN0N,EAAU,EAAIA,EAAK,EAAI,EAAU,GAANA,EAAU,EAAI,EAGhE,GAFAz8C,EAAE,GAAK,IAAKA,EAAE,GAAM+uC,GAAM,GAAM5oC,EAAE0yC,YAAc,IAChD74C,EAAE,IAAM,IAAOA,EAAE,IAAM,EAAKA,EAAE,IAAM,GAChCmG,EAAE0yC,WAAY,CACd,IAAI3lC,EAAIylC,IACRzlC,EAAE5V,EAAE6I,EAAE0yC,YACNmC,GAAOh7C,EAAG,EAAGkT,EAAEoC,IACnB,CACJ,EAEIimC,GAAM,SAAUjmC,EAAG47B,GAKnB,OAJmB,IAAP,GAAP57B,EAAE,KAAkBA,EAAE,IAAM,EAAK,IAAOA,EAAE,IAAM,EAAIA,EAAE,IAAM,KAC7Dk7B,EAAI,EAAG,sBACNl7B,EAAE,IAAM,EAAI,MAAQ47B,GACrBV,EAAI,EAAG,uBAAgC,GAAPl7B,EAAE,GAAU,OAAS,cAAgB,eAChD,GAAjBA,EAAE,IAAM,EAAI,EACxB,EACA,SAASonC,GAAQpgD,EAAMs9C,GAInB,MAHmB,mBAARt9C,IACPs9C,EAAKt9C,EAAMA,EAAO,CAAC,GACvBiW,KAAKmpC,OAAS9B,EACPt9C,CACX,CAIA,IAAIqgD,GAAyB,WACzB,SAASA,EAAQrgD,EAAMs9C,GASnB,GARmB,mBAARt9C,IACPs9C,EAAKt9C,EAAMA,EAAO,CAAC,GACvBiW,KAAKmpC,OAAS9B,EACdrnC,KAAKpM,EAAI7J,GAAQ,CAAC,EAClBiW,KAAK3L,EAAI,CAAEJ,EAAG,EAAGzK,EAAG,MAAO04C,EAAG,MAAO3tC,EAAG,OAGxCyL,KAAK/Z,EAAI,IAAI41C,EAAG,OACZ77B,KAAKpM,EAAE0yC,WAAY,CACnB,IAAI3H,EAAO3+B,KAAKpM,EAAE0yC,WAAWvI,UAAU,OACvC/9B,KAAK/Z,EAAE4O,IAAI8pC,EAAM,MAAQA,EAAKpzC,QAC9ByU,KAAK3L,EAAE7K,EAAI,MAAQm1C,EAAKpzC,MAC5B,CACJ,CAoDA,OAnDA6+C,EAAQzqC,UAAU5U,EAAI,SAAU0C,EAAGmU,GAC/B5B,KAAKmpC,OAAO9C,EAAK54C,EAAGuS,KAAKpM,EAAG,EAAG,EAAGoM,KAAK3L,GAAIuN,EAC/C,EAMAwoC,EAAQzqC,UAAU1T,KAAO,SAAUo+C,EAAOjL,GACjCp/B,KAAKmpC,QACNlL,EAAI,GACJj+B,KAAK3L,EAAEJ,GACPgqC,EAAI,GACR,IAAIqM,EAASD,EAAM9+C,OAASyU,KAAK3L,EAAEE,EACnC,GAAI+1C,EAAStqC,KAAK/Z,EAAEsF,OAAQ,CACxB,GAAI++C,EAAS,EAAItqC,KAAK/Z,EAAEsF,OAAS,MAAO,CACpC,IAAIg/C,EAAS,IAAI1O,GAAa,MAAVyO,GACpBC,EAAO11C,IAAImL,KAAK/Z,EAAE83C,SAAS,EAAG/9B,KAAK3L,EAAEE,IACrCyL,KAAK/Z,EAAIskD,CACb,CACA,IAAI1+C,EAAQmU,KAAK/Z,EAAEsF,OAASyU,KAAK3L,EAAEE,EACnCyL,KAAK/Z,EAAE4O,IAAIw1C,EAAMtM,SAAS,EAAGlyC,GAAQmU,KAAK3L,EAAEE,GAC5CyL,KAAK3L,EAAEE,EAAIyL,KAAK/Z,EAAEsF,OAClByU,KAAKjV,EAAEiV,KAAK/Z,GAAG,GACf+Z,KAAK/Z,EAAE4O,IAAImL,KAAK/Z,EAAE83C,UAAU,QAC5B/9B,KAAK/Z,EAAE4O,IAAIw1C,EAAMtM,SAASlyC,GAAQ,OAClCmU,KAAK3L,EAAEE,EAAI81C,EAAM9+C,OAASM,EAAQ,MAClCmU,KAAK3L,EAAE7K,EAAI,MAAOwW,KAAK3L,EAAE6tC,EAAI,KACjC,MAEIliC,KAAK/Z,EAAE4O,IAAIw1C,EAAOrqC,KAAK3L,EAAEE,GACzByL,KAAK3L,EAAEE,GAAK81C,EAAM9+C,OAEtByU,KAAK3L,EAAEJ,EAAY,EAARmrC,GACPp/B,KAAK3L,EAAEE,EAAIyL,KAAK3L,EAAE6tC,EAAI,MAAQ9C,KAC9Bp/B,KAAKjV,EAAEiV,KAAK/Z,EAAGm5C,IAAS,GACxBp/B,KAAK3L,EAAE6tC,EAAIliC,KAAK3L,EAAE7K,EAAGwW,KAAK3L,EAAE7K,GAAK,EAEzC,EAKA4gD,EAAQzqC,UAAU0pC,MAAQ,WACjBrpC,KAAKmpC,QACNlL,EAAI,GACJj+B,KAAK3L,EAAEJ,GACPgqC,EAAI,GACRj+B,KAAKjV,EAAEiV,KAAK/Z,GAAG,GACf+Z,KAAK3L,EAAE6tC,EAAIliC,KAAK3L,EAAE7K,EAAGwW,KAAK3L,EAAE7K,GAAK,CACrC,EACO4gD,CACX,CApE4B,GAyExBI,GAA8B,WAU9B,OATA,SAAsBzgD,EAAMs9C,GACxBiC,GAAS,CACLlB,EACA,WAAc,MAAO,CAACa,GAAOmB,GAAU,GACxCpqC,KAAMmqC,GAAQ1iC,KAAKzH,KAAMjW,EAAMs9C,IAAK,SAAU+B,GAC7C,IAAIF,EAAO,IAAIkB,GAAQhB,EAAG7wC,MAC1BkvC,UAAYwB,GAAMC,EACtB,GAAG,EAAG,EACV,CAEJ,CAXiC,GA4B1B,SAASb,GAAY9vC,EAAMxO,GAC9B,OAAOs8C,EAAK9tC,EAAMxO,GAAQ,CAAC,EAAG,EAAG,EACrC,CAIA,IAAI0gD,GAAyB,WACzB,SAASA,EAAQ1gD,EAAMs9C,GAEA,mBAARt9C,IACPs9C,EAAKt9C,EAAMA,EAAO,CAAC,GACvBiW,KAAKmpC,OAAS9B,EACd,IAAI1I,EAAO50C,GAAQA,EAAKu8C,YAAcv8C,EAAKu8C,WAAWvI,UAAU,OAChE/9B,KAAK3L,EAAI,CAAE7K,EAAG,EAAGvD,EAAG04C,EAAOA,EAAKpzC,OAAS,GACzCyU,KAAKpM,EAAI,IAAIioC,EAAG,OAChB77B,KAAKjV,EAAI,IAAI8wC,EAAG,GACZ8C,GACA3+B,KAAKpM,EAAEiB,IAAI8pC,EACnB,CA6BA,OA5BA8L,EAAQ9qC,UAAUlM,EAAI,SAAUhG,GAK5B,GAJKuS,KAAKmpC,QACNlL,EAAI,GACJj+B,KAAK+C,GACLk7B,EAAI,GACHj+B,KAAKjV,EAAEQ,QAEP,GAAIkC,EAAElC,OAAQ,CACf,IAAI4I,EAAI,IAAI0nC,EAAG77B,KAAKjV,EAAEQ,OAASkC,EAAElC,QACjC4I,EAAEU,IAAImL,KAAKjV,GAAIoJ,EAAEU,IAAIpH,EAAGuS,KAAKjV,EAAEQ,QAASyU,KAAKjV,EAAIoJ,CACrD,OAJI6L,KAAKjV,EAAI0C,CAKjB,EACAg9C,EAAQ9qC,UAAUlS,EAAI,SAAU2xC,GAC5Bp/B,KAAK3L,EAAE7K,IAAMwW,KAAK+C,EAAIq8B,IAAS,GAC/B,IAAIsL,EAAM1qC,KAAK3L,EAAEpO,EACbm6C,EAAK7B,EAAMv+B,KAAKjV,EAAGiV,KAAK3L,EAAG2L,KAAKpM,GACpCoM,KAAKmpC,OAAOrL,EAAIsC,EAAIsK,EAAK1qC,KAAK3L,EAAEpO,GAAI+Z,KAAK+C,GACzC/C,KAAKpM,EAAIkqC,EAAIsC,EAAIpgC,KAAK3L,EAAEpO,EAAI,OAAQ+Z,KAAK3L,EAAEpO,EAAI+Z,KAAKpM,EAAErI,OACtDyU,KAAKjV,EAAI+yC,EAAI99B,KAAKjV,EAAIiV,KAAK3L,EAAEtJ,EAAI,EAAK,GAAIiV,KAAK3L,EAAEtJ,GAAK,CAC1D,EAMA0/C,EAAQ9qC,UAAU1T,KAAO,SAAUo+C,EAAOjL,GACtCp/B,KAAKvM,EAAE42C,GAAQrqC,KAAKvS,EAAE2xC,EAC1B,EACOqL,CACX,CA1C4B,GA+CxBE,GAA8B,WAU9B,OATA,SAAsB5gD,EAAMs9C,GACxBiC,GAAS,CACLtB,EACA,WAAc,MAAO,CAACiB,GAAOwB,GAAU,GACxCzqC,KAAMmqC,GAAQ1iC,KAAKzH,KAAMjW,EAAMs9C,IAAK,SAAU+B,GAC7C,IAAIF,EAAO,IAAIuB,GAAQrB,EAAG7wC,MAC1BkvC,UAAYwB,GAAMC,EACtB,GAAG,EAAG,EACV,CAEJ,CAXiC,GA4B1B,SAASjB,GAAY1vC,EAAMxO,GAC9B,OAAOw0C,EAAMhmC,EAAM,CAAE/O,EAAG,GAAKO,GAAQA,EAAKgE,IAAKhE,GAAQA,EAAKu8C,WAChE,CAKA,IAAIsE,GAAsB,WACtB,SAASA,EAAK7gD,EAAMs9C,GAChBrnC,KAAKvS,EAAIy4C,IACTlmC,KAAK/L,EAAI,EACT+L,KAAKzW,EAAI,EACT6gD,GAAQ3iC,KAAKzH,KAAMjW,EAAMs9C,EAC7B,CA0BA,OApBAuD,EAAKjrC,UAAU1T,KAAO,SAAUo+C,EAAOjL,GACnCp/B,KAAKvS,EAAE1C,EAAEs/C,GACTrqC,KAAK/L,GAAKo2C,EAAM9+C,OAChB6+C,GAAQzqC,UAAU1T,KAAKwb,KAAKzH,KAAMqqC,EAAOjL,EAC7C,EACAwL,EAAKjrC,UAAU5U,EAAI,SAAU0C,EAAGmU,GAC5B,IAAIipC,EAAMxE,EAAK54C,EAAGuS,KAAKpM,EAAGoM,KAAKzW,GAAKi/C,GAAKxoC,KAAKpM,GAAIgO,GAAK,EAAG5B,KAAK3L,GAC3D2L,KAAKzW,IACLg/C,GAAIsC,EAAK7qC,KAAKpM,GAAIoM,KAAKzW,EAAI,GAC3BqY,IACA6mC,GAAOoC,EAAKA,EAAIt/C,OAAS,EAAGyU,KAAKvS,EAAEsV,KAAM0lC,GAAOoC,EAAKA,EAAIt/C,OAAS,EAAGyU,KAAK/L,IAC9E+L,KAAKmpC,OAAO0B,EAAKjpC,EACrB,EAKAgpC,EAAKjrC,UAAU0pC,MAAQ,WACnBe,GAAQzqC,UAAU0pC,MAAM5hC,KAAKzH,KACjC,EACO4qC,CACX,CAjCyB,GAsCrBE,GAA2B,WAW3B,OAVA,SAAmB/gD,EAAMs9C,GACrBiC,GAAS,CACLlB,EACAE,EACA,WAAc,MAAO,CAACW,GAAOmB,GAASQ,GAAO,GAC9C5qC,KAAMmqC,GAAQ1iC,KAAKzH,KAAMjW,EAAMs9C,IAAK,SAAU+B,GAC7C,IAAIF,EAAO,IAAI0B,GAAKxB,EAAG7wC,MACvBkvC,UAAYwB,GAAMC,EACtB,GAAG,EAAG,EACV,CAEJ,CAZ8B,GA0C9B,IAAI6B,GAAwB,WACxB,SAASA,EAAOhhD,EAAMs9C,GAClBrnC,KAAKzW,EAAI,EACTyW,KAAKrL,EAAI,EACT81C,GAAQhjC,KAAKzH,KAAMjW,EAAMs9C,EAC7B,CAgCA,OA1BA0D,EAAOprC,UAAU1T,KAAO,SAAUo+C,EAAOjL,GAGrC,GAFAqL,GAAQ9qC,UAAUlM,EAAEgU,KAAKzH,KAAMqqC,GAC/BrqC,KAAKrL,GAAK01C,EAAM9+C,OACZyU,KAAKzW,EAAG,CACR,IAAIwB,EAAIiV,KAAKjV,EAAEgzC,SAAS/9B,KAAKzW,EAAI,GAC7B8K,EAAItJ,EAAEQ,OAAS,EAAIo9C,GAAI59C,GAAK,EAChC,GAAIsJ,EAAItJ,EAAEQ,QACN,IAAK6zC,EACD,YAECp/B,KAAKzW,EAAI,GAAKyW,KAAKgrC,UACxBhrC,KAAKgrC,SAAShrC,KAAKrL,EAAI5J,EAAEQ,QAE7ByU,KAAKjV,EAAIA,EAAEgzC,SAAS1pC,GAAI2L,KAAKzW,EAAI,CACrC,CAGAkhD,GAAQ9qC,UAAUlS,EAAEga,KAAKzH,KAAMo/B,IAE3Bp/B,KAAK3L,EAAEuN,GAAM5B,KAAK3L,EAAEJ,GAAMmrC,IAC1Bp/B,KAAKzW,EAAIs0C,EAAK79B,KAAK3L,EAAEtJ,GAAK,EAC1BiV,KAAK3L,EAAI,CAAE7K,EAAG,GACdwW,KAAKpM,EAAI,IAAIioC,EAAG,GAChB77B,KAAK/T,KAAK,IAAI4vC,EAAG,GAAIuD,GAE7B,EACO2L,CACX,CAtC2B,GA2CvBE,GAA6B,WAa7B,OAZA,SAAqBlhD,EAAMs9C,GACvB,IAAI6D,EAAQlrC,KACZspC,GAAS,CACLtB,EACAU,GACA,WAAc,MAAO,CAACO,GAAOwB,GAASM,GAAS,GAChD/qC,KAAMmqC,GAAQ1iC,KAAKzH,KAAMjW,EAAMs9C,IAAK,SAAU+B,GAC7C,IAAIF,EAAO,IAAI6B,GAAO3B,EAAG7wC,MACzB2wC,EAAK8B,SAAW,SAAUruC,GAAU,OAAOirC,YAAYjrC,EAAS,EAChE8qC,UAAYwB,GAAMC,EACtB,GAAG,EAAG,GAAG,SAAUvsC,GAAU,OAAOuuC,EAAMF,UAAYE,EAAMF,SAASruC,EAAS,GAClF,CAEJ,CAdgC,GA0ChC,IAAIwuC,GAAsB,WACtB,SAASA,EAAKphD,EAAMs9C,GAChBrnC,KAAKvS,EAAI24C,IACTpmC,KAAKzW,EAAI,EACT6gD,GAAQ3iC,KAAKzH,KAAMjW,EAAMs9C,EAC7B,CAyBA,OAnBA8D,EAAKxrC,UAAU1T,KAAO,SAAUo+C,EAAOjL,GACnCp/B,KAAKvS,EAAE1C,EAAEs/C,GACTD,GAAQzqC,UAAU1T,KAAKwb,KAAKzH,KAAMqqC,EAAOjL,EAC7C,EACA+L,EAAKxrC,UAAU5U,EAAI,SAAU0C,EAAGmU,GAC5B,IAAIipC,EAAMxE,EAAK54C,EAAGuS,KAAKpM,EAAGoM,KAAKzW,IAAMyW,KAAKpM,EAAE0yC,WAAa,EAAI,GAAI1kC,GAAK,EAAG5B,KAAK3L,GAC1E2L,KAAKzW,IACLu/C,GAAI+B,EAAK7qC,KAAKpM,GAAIoM,KAAKzW,EAAI,GAC3BqY,GACA6mC,GAAOoC,EAAKA,EAAIt/C,OAAS,EAAGyU,KAAKvS,EAAEsV,KACvC/C,KAAKmpC,OAAO0B,EAAKjpC,EACrB,EAKAupC,EAAKxrC,UAAU0pC,MAAQ,WACnBe,GAAQzqC,UAAU0pC,MAAM5hC,KAAKzH,KACjC,EACOmrC,CACX,CA/ByB,GAoCrBC,GAA2B,WAW3B,OAVA,SAAmBrhD,EAAMs9C,GACrBiC,GAAS,CACLlB,EACAS,GACA,WAAc,MAAO,CAACI,GAAOmB,GAASe,GAAO,GAC9CnrC,KAAMmqC,GAAQ1iC,KAAKzH,KAAMjW,EAAMs9C,IAAK,SAAU+B,GAC7C,IAAIF,EAAO,IAAIiC,GAAK/B,EAAG7wC,MACvBkvC,UAAYwB,GAAMC,EACtB,GAAG,GAAI,EACX,CAEJ,CAZ8B,GA0C9B,IAAImC,GAAwB,WACxB,SAASA,EAAOthD,EAAMs9C,GAClBoD,GAAQhjC,KAAKzH,KAAMjW,EAAMs9C,GACzBrnC,KAAKzW,EAAIQ,GAAQA,EAAKu8C,WAAa,EAAI,CAC3C,CAsBA,OAhBA+E,EAAO1rC,UAAU1T,KAAO,SAAUo+C,EAAOjL,GAErC,GADAqL,GAAQ9qC,UAAUlM,EAAEgU,KAAKzH,KAAMqqC,GAC3BrqC,KAAKzW,EAAG,CACR,GAAIyW,KAAKjV,EAAEQ,OAAS,IAAM6zC,EACtB,OACJp/B,KAAKjV,EAAIiV,KAAKjV,EAAEgzC,SAASiL,GAAIhpC,KAAKjV,EAAGiV,KAAKzW,EAAI,IAAKyW,KAAKzW,EAAI,CAChE,CACI61C,IACIp/B,KAAKjV,EAAEQ,OAAS,GAChB0yC,EAAI,EAAG,qBACXj+B,KAAKjV,EAAIiV,KAAKjV,EAAEgzC,SAAS,GAAI,IAIjC0M,GAAQ9qC,UAAUlS,EAAEga,KAAKzH,KAAMo/B,EACnC,EACOiM,CACX,CA3B2B,GAgCvBC,GAA6B,WAW7B,OAVA,SAAqBvhD,EAAMs9C,GACvBiC,GAAS,CACLtB,EACAe,GACA,WAAc,MAAO,CAACE,GAAOwB,GAASY,GAAS,GAChDrrC,KAAMmqC,GAAQ1iC,KAAKzH,KAAMjW,EAAMs9C,IAAK,SAAU+B,GAC7C,IAAIF,EAAO,IAAImC,GAAOjC,EAAG7wC,MACzBkvC,UAAYwB,GAAMC,EACtB,GAAG,GAAI,EACX,CAEJ,CAZgC,GAkJhC,IAgBIrC,GAA2B,oBAAf0E,aAA4C,IAAIA,YAGhE,IACI1E,GAAG2E,OAAOxK,EAAI,CAAEyK,QAAQ,IAClB,CACV,CACA,MAAOh4C,GAAK,CAw2BsB,mBAAlBi4C,eAA+BA,eAAsC,mBAAd1f,YAA2BA,WC/9ElG,MAAM2f,GAAYC,GACd,MACI,WAAAlsC,GACIM,KAAKxW,EAAI,IAAIoiD,EACb5rC,KAAKxW,EAAE2/C,OAAS,CAAC5wC,EAAM6mC,KACnBp/B,KAAKmpC,OAAO,KAAM5wC,EAAM6mC,EAAM,CAEtC,CACA,IAAAnzC,CAAKsM,EAAM6mC,GACP,IACIp/B,KAAK0pC,YAAcnxC,EAAKhN,OACxByU,KAAKxW,EAAEyC,KAAKsM,EAAM6mC,GAClBp/B,KAAK0pC,YAAcnxC,EAAKhN,OACpByU,KAAK2pC,SACL3pC,KAAK2pC,QAAQpxC,EAAKhN,OAC1B,CACA,MAAO0yC,GACHj+B,KAAKmpC,OAAOlL,EAAK,KAAMmB,IAAS,EACpC,CACJ,GAKR,IAAIyM,GAAY,EAChB,KACiB,IAAIrB,IACZhB,WACT,CACA,MAAOvL,GACH4N,GAAY,CAChB,CACA,MAAMC,GAAcD,GAAY,CAC5B,KAAQf,GACR,QAAWM,GACX,cAAeZ,IACf,CACA,KAAQmB,GAASf,IACjB,QAAWe,GAASR,IACpB,cAAeQ,GAASvB,KAEtB2B,GAAgBF,GAAY,CAC9B,KAAQZ,GACR,QAAWK,GACX,cAAeX,IACf,CACA,KAAQgB,GAASZ,IACjB,QAAWY,GAASN,IACpB,cAAeM,GAASlB,KAEtBuB,GAAY,CAACC,EAAqBC,EAAY9sC,IAChD,cAAoC6sC,EAChC,WAAAvsC,CAAYU,GACR,IAAKuH,UAAUpc,OACX,MAAM,IAAI4gD,UAAU,wBAAwB/sC,gDAEhD,MAAMgtC,EAAYF,EAAW9rC,GAC7B,IAAKgsC,EACD,MAAM,IAAID,UAAU,wBAAwB/sC,wCAA2CgB,MAE3F,IACIisC,EADAC,EAAa,IAAIF,EAErB/b,MAAM,CACFjnC,MAAOmjD,IACHD,EAAWnD,OAAS,CAAClL,EAAKO,EAAKY,KACvBnB,EACAsO,EAAWpT,MAAM8E,GACZO,IACL+N,EAAWC,QAAQhO,GACfY,IACIiN,EACAA,IAEAE,EAAW/C,aAEvB,CACH,EAEL3qB,UAAWwrB,IACP,GAAIA,aAAiBoC,YACjBpC,EAAQ,IAAIvO,WAAWuO,OACtB,KAAIoC,YAAYC,OAAOrC,GAIxB,MAAM,IAAI8B,UAAU,wEAHpB9B,EAAQ,IAAIvO,WAAWuO,EAAMvC,OAAQuC,EAAMsC,WAAYtC,EAAMuC,WAIjE,CAGA,GAFAN,EAAWrgD,KAAKo+C,GAEZiC,EAAW5C,YAAc,MACzB,OAAO,IAAImD,SAAQC,IACfR,EAAW3C,QAAU,KACb2C,EAAW5C,WAAa,OACxBoD,GAAS,CAChB,GAET,EAEJzD,MAAO,IAAM,IAAIwD,SAAQC,IACrBT,EAAQS,EACRR,EAAWrgD,KAAK,IAAI6vC,WAAW,IAAI,EAAK,KAE7C,CACCtpC,KAAM63C,GAA4B,EAAnBA,EAAMuC,WACrBG,cAAe,OAChB,CACCv6C,KAAM63C,GAA4B,EAAnBA,EAAMuC,WACrBG,cAAe,OAEvB,GC7GR,MAAMC,GAA+B,oBAAdC,WACF,oBAARvhB,KACc,oBAAVgO,OACH,CAAC,EACDA,OACJhO,KACJuhB,WD2GC,IAA+BhB,QC1GE,IAA7Be,GAAQE,oBACfF,GAAQE,mBDyG0BjB,GCzGgBkB,gBD0G3CnB,GAAUC,GAAqBH,GAAa,4BCxGb,IAA/BkB,GAAQI,sBACfJ,GAAQI,oBDyGL,SAAiCnB,GACpC,OAAOD,GAAUC,EAAqBF,GAAe,sBACzD,CC3GkCsB,CAAwBF,iB,2qBCI1D,IAAIG,EAAgB,SAASvqC,EAAG9c,GAI9B,OAHAqnD,EAAgBtjD,OAAOujD,gBAClB,CAAEC,UAAW,cAAgB5gD,OAAS,SAAUmW,EAAG9c,GAAK8c,EAAEyqC,UAAYvnD,CAAG,GAC1E,SAAU8c,EAAG9c,GAAK,IAAK,IAAI8E,KAAK9E,EAAO+D,OAAO2V,UAAUuC,eAAeuF,KAAKxhB,EAAG8E,KAAIgY,EAAEhY,GAAK9E,EAAE8E,GAAI,EAC7FuiD,EAAcvqC,EAAG9c,EAC1B,EAEO,SAASwnD,EAAU1qC,EAAG9c,GAC3B,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIkmD,UAAU,uBAAyB3qC,OAAOvb,GAAK,iCAE7D,SAASynD,IAAO1tC,KAAKN,YAAcqD,CAAG,CADtCuqC,EAAcvqC,EAAG9c,GAEjB8c,EAAEpD,UAAkB,OAAN1Z,EAAa+D,OAAO2jD,OAAO1nD,IAAMynD,EAAG/tC,UAAY1Z,EAAE0Z,UAAW,IAAI+tC,EACjF,CAEO,IAAIE,EAAW,WAQpB,OAPAA,EAAW5jD,OAAOC,QAAU,SAAkBmC,GAC1C,IAAK,IAAIiI,EAAG7K,EAAI,EAAG2K,EAAIwT,UAAUpc,OAAQ/B,EAAI2K,EAAG3K,IAE5C,IAAK,IAAIuB,KADTsJ,EAAIsT,UAAUne,GACOQ,OAAO2V,UAAUuC,eAAeuF,KAAKpT,EAAGtJ,KAAIqB,EAAErB,GAAKsJ,EAAEtJ,IAE9E,OAAOqB,CACX,EACOwhD,EAASpmC,MAAMxH,KAAM2H,UAC9B,EAEO,SAASkmC,EAAOx5C,EAAGZ,GACxB,IAAIrH,EAAI,CAAC,EACT,IAAK,IAAIrB,KAAKsJ,EAAOrK,OAAO2V,UAAUuC,eAAeuF,KAAKpT,EAAGtJ,IAAM0I,EAAEzD,QAAQjF,GAAK,IAC9EqB,EAAErB,GAAKsJ,EAAEtJ,IACb,GAAS,MAALsJ,GAAqD,mBAAjCrK,OAAO8jD,sBACtB,KAAItkD,EAAI,EAAb,IAAgBuB,EAAIf,OAAO8jD,sBAAsBz5C,GAAI7K,EAAIuB,EAAEQ,OAAQ/B,IAC3DiK,EAAEzD,QAAQjF,EAAEvB,IAAM,GAAKQ,OAAO2V,UAAUouC,qBAAqBtmC,KAAKpT,EAAGtJ,EAAEvB,MACvE4C,EAAErB,EAAEvB,IAAM6K,EAAEtJ,EAAEvB,IAF4B,CAItD,OAAO4C,CACT,CAEO,SAAS4hD,EAAWC,EAAYzwB,EAAQrI,EAAK8d,GAClD,IAA2HlwB,EAAvHtV,EAAIka,UAAUpc,OAAQoJ,EAAIlH,EAAI,EAAI+vB,EAAkB,OAATyV,EAAgBA,EAAOjpC,OAAOkkD,yBAAyB1wB,EAAQrI,GAAO8d,EACrH,GAAuB,iBAAZkb,SAAoD,mBAArBA,QAAQC,SAAyBz5C,EAAIw5C,QAAQC,SAASH,EAAYzwB,EAAQrI,EAAK8d,QACpH,IAAK,IAAIzpC,EAAIykD,EAAW1iD,OAAS,EAAG/B,GAAK,EAAGA,KAASuZ,EAAIkrC,EAAWzkD,MAAImL,GAAKlH,EAAI,EAAIsV,EAAEpO,GAAKlH,EAAI,EAAIsV,EAAEya,EAAQrI,EAAKxgB,GAAKoO,EAAEya,EAAQrI,KAASxgB,GAChJ,OAAOlH,EAAI,GAAKkH,GAAK3K,OAAOwxC,eAAehe,EAAQrI,EAAKxgB,GAAIA,CAC9D,CAEO,SAAS05C,EAAQC,EAAYC,GAClC,OAAO,SAAU/wB,EAAQrI,GAAOo5B,EAAU/wB,EAAQrI,EAAKm5B,EAAa,CACtE,CAEO,SAASE,EAAaC,EAAMC,EAAcT,EAAYU,EAAWC,EAAcC,GACpF,SAASC,EAAOltC,GAAK,QAAU,IAANA,GAA6B,mBAANA,EAAkB,MAAM,IAAIuqC,UAAU,qBAAsB,OAAOvqC,CAAG,CAKtH,IAJA,IAGIgU,EAHAm5B,EAAOJ,EAAUI,KAAM55B,EAAe,WAAT45B,EAAoB,MAAiB,WAATA,EAAoB,MAAQ,QACrFvxB,GAAUkxB,GAAgBD,EAAOE,EAAkB,OAAIF,EAAOA,EAAK9uC,UAAY,KAC/EqvC,EAAaN,IAAiBlxB,EAASxzB,OAAOkkD,yBAAyB1wB,EAAQmxB,EAAUvvC,MAAQ,CAAC,GAC/F6vC,GAAO,EACLzlD,EAAIykD,EAAW1iD,OAAS,EAAG/B,GAAK,EAAGA,IAAK,CAC7C,IAAI0lD,EAAU,CAAC,EACf,IAAK,IAAInkD,KAAK4jD,EAAWO,EAAQnkD,GAAW,WAANA,EAAiB,CAAC,EAAI4jD,EAAU5jD,GACtE,IAAK,IAAIA,KAAK4jD,EAAUQ,OAAQD,EAAQC,OAAOpkD,GAAK4jD,EAAUQ,OAAOpkD,GACrEmkD,EAAQE,eAAiB,SAAUxtC,GAAK,GAAIqtC,EAAM,MAAM,IAAI9C,UAAU,0DAA2D0C,EAAkB5iD,KAAK6iD,EAAOltC,GAAK,MAAQ,EAC5K,IAAIzM,GAAS,EAAI84C,EAAWzkD,IAAa,aAATulD,EAAsB,CAAE/5C,IAAKg6C,EAAWh6C,IAAKH,IAAKm6C,EAAWn6C,KAAQm6C,EAAW75B,GAAM+5B,GACtH,GAAa,aAATH,EAAqB,CACrB,QAAe,IAAX55C,EAAmB,SACvB,GAAe,OAAXA,GAAqC,iBAAXA,EAAqB,MAAM,IAAIg3C,UAAU,oBACnEv2B,EAAIk5B,EAAO35C,EAAOH,QAAMg6C,EAAWh6C,IAAM4gB,IACzCA,EAAIk5B,EAAO35C,EAAON,QAAMm6C,EAAWn6C,IAAM+gB,IACzCA,EAAIk5B,EAAO35C,EAAOiyC,QAAOwH,EAAa5zB,QAAQpF,EACtD,MACSA,EAAIk5B,EAAO35C,MACH,UAAT45C,EAAkBH,EAAa5zB,QAAQpF,GACtCo5B,EAAW75B,GAAOS,EAE/B,CACI4H,GAAQxzB,OAAOwxC,eAAehe,EAAQmxB,EAAUvvC,KAAM4vC,GAC1DC,GAAO,CACT,CAEO,SAASI,EAAkBC,EAASV,EAAczxC,GAEvD,IADA,IAAIoyC,EAAW5nC,UAAUpc,OAAS,EACzB/B,EAAI,EAAGA,EAAIolD,EAAarjD,OAAQ/B,IACrC2T,EAAQoyC,EAAWX,EAAaplD,GAAGie,KAAK6nC,EAASnyC,GAASyxC,EAAaplD,GAAGie,KAAK6nC,GAEnF,OAAOC,EAAWpyC,OAAQ,CAC5B,CAEO,SAASqyC,EAAUr5C,GACxB,MAAoB,iBAANA,EAAiBA,EAAI,GAAGhD,OAAOgD,EAC/C,CAEO,SAASs5C,EAAkB7tC,EAAGxC,EAAMswC,GAEzC,MADoB,iBAATtwC,IAAmBA,EAAOA,EAAKyrB,YAAc,IAAI13B,OAAOiM,EAAKyrB,YAAa,KAAO,IACrF7gC,OAAOwxC,eAAe55B,EAAG,OAAQ,CAAE+tC,cAAc,EAAMxyC,MAAOuyC,EAAS,GAAGv8C,OAAOu8C,EAAQ,IAAKtwC,GAAQA,GAC/G,CAEO,SAASwwC,EAAWC,EAAaC,GACtC,GAAuB,iBAAZ3B,SAAoD,mBAArBA,QAAQxtB,SAAyB,OAAOwtB,QAAQxtB,SAASkvB,EAAaC,EAClH,CAEO,SAASC,EAAUT,EAASU,EAAYC,EAAGC,GAEhD,OAAO,IAAKD,IAAMA,EAAIpD,WAAU,SAAUC,EAASqD,GAC/C,SAASC,EAAUjzC,GAAS,IAAMwH,EAAKurC,EAAUhxB,KAAK/hB,GAAS,CAAE,MAAO1J,GAAK08C,EAAO18C,EAAI,CAAE,CAC1F,SAAS48C,EAASlzC,GAAS,IAAMwH,EAAKurC,EAAiB,MAAE/yC,GAAS,CAAE,MAAO1J,GAAK08C,EAAO18C,EAAI,CAAE,CAC7F,SAASkR,EAAKxP,GAJlB,IAAegI,EAIahI,EAAO85C,KAAOnC,EAAQ33C,EAAOgI,QAJ1CA,EAIyDhI,EAAOgI,MAJhDA,aAAiB8yC,EAAI9yC,EAAQ,IAAI8yC,GAAE,SAAUnD,GAAWA,EAAQ3vC,EAAQ,KAIjB7C,KAAK81C,EAAWC,EAAW,CAC7G1rC,GAAMurC,EAAYA,EAAU1oC,MAAM8nC,EAASU,GAAc,KAAK9wB,OAClE,GACF,CAEO,SAASoxB,EAAYhB,EAAS/X,GACnC,IAAsG31B,EAAGxL,EAAGhK,EAAG8T,EAA3G0V,EAAI,CAAE5b,MAAO,EAAGu2C,KAAM,WAAa,GAAW,EAAPnkD,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAGokD,KAAM,GAAIC,IAAK,IAChG,OAAOvwC,EAAI,CAAEgf,KAAMwxB,EAAK,GAAI,MAASA,EAAK,GAAI,OAAUA,EAAK,IAAwB,mBAAXlxC,SAA0BU,EAAEV,OAAOC,UAAY,WAAa,OAAOO,IAAM,GAAIE,EACvJ,SAASwwC,EAAKv8C,GAAK,OAAO,SAAU5K,GAAK,OACzC,SAAconD,GACV,GAAI/uC,EAAG,MAAM,IAAIuqC,UAAU,mCAC3B,KAAOjsC,IAAMA,EAAI,EAAGywC,EAAG,KAAO/6B,EAAI,IAAKA,OACnC,GAAIhU,EAAI,EAAGxL,IAAMhK,EAAY,EAARukD,EAAG,GAASv6C,EAAU,OAAIu6C,EAAG,GAAKv6C,EAAS,SAAOhK,EAAIgK,EAAU,SAAMhK,EAAEqb,KAAKrR,GAAI,GAAKA,EAAE8oB,SAAW9yB,EAAIA,EAAEqb,KAAKrR,EAAGu6C,EAAG,KAAK1B,KAAM,OAAO7iD,EAE3J,OADIgK,EAAI,EAAGhK,IAAGukD,EAAK,CAAS,EAARA,EAAG,GAAQvkD,EAAE+Q,QACzBwzC,EAAG,IACP,KAAK,EAAG,KAAK,EAAGvkD,EAAIukD,EAAI,MACxB,KAAK,EAAc,OAAX/6B,EAAE5b,QAAgB,CAAEmD,MAAOwzC,EAAG,GAAI1B,MAAM,GAChD,KAAK,EAAGr5B,EAAE5b,QAAS5D,EAAIu6C,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAK/6B,EAAE66B,IAAIG,MAAOh7B,EAAE46B,KAAKI,MAAO,SACxC,QACI,KAAMxkD,EAAIwpB,EAAE46B,MAAMpkD,EAAIA,EAAEb,OAAS,GAAKa,EAAEA,EAAEb,OAAS,KAAkB,IAAVolD,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAE/6B,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAV+6B,EAAG,MAAcvkD,GAAMukD,EAAG,GAAKvkD,EAAE,IAAMukD,EAAG,GAAKvkD,EAAE,IAAM,CAAEwpB,EAAE5b,MAAQ22C,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAY/6B,EAAE5b,MAAQ5N,EAAE,GAAI,CAAEwpB,EAAE5b,MAAQ5N,EAAE,GAAIA,EAAIukD,EAAI,KAAO,CACpE,GAAIvkD,GAAKwpB,EAAE5b,MAAQ5N,EAAE,GAAI,CAAEwpB,EAAE5b,MAAQ5N,EAAE,GAAIwpB,EAAE66B,IAAIxkD,KAAK0kD,GAAK,KAAO,CAC9DvkD,EAAE,IAAIwpB,EAAE66B,IAAIG,MAChBh7B,EAAE46B,KAAKI,MAAO,SAEtBD,EAAKpZ,EAAK9vB,KAAK6nC,EAAS15B,EAC5B,CAAE,MAAOniB,GAAKk9C,EAAK,CAAC,EAAGl9C,GAAI2C,EAAI,CAAG,CAAE,QAAUwL,EAAIxV,EAAI,CAAG,CACzD,GAAY,EAARukD,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAExzC,MAAOwzC,EAAG,GAAKA,EAAG,QAAK,EAAQ1B,MAAM,EAC9E,CAtBgDtqC,CAAK,CAACxQ,EAAG5K,GAAK,CAAG,CAuBnE,CAEO,IAAIsnD,EAAkB7mD,OAAO2jD,OAAS,SAAU/5C,EAAG5H,EAAG0D,EAAGohD,QACnDniB,IAAPmiB,IAAkBA,EAAKphD,GAC3B,IAAIujC,EAAOjpC,OAAOkkD,yBAAyBliD,EAAG0D,GACzCujC,KAAS,QAASA,GAAQjnC,EAAE+kD,WAAa9d,EAAK+d,UAAY/d,EAAK0c,gBAChE1c,EAAO,CAAEge,YAAY,EAAMj8C,IAAK,WAAa,OAAOhJ,EAAE0D,EAAI,IAE9D1F,OAAOwxC,eAAe5nC,EAAGk9C,EAAI7d,EAC9B,EAAI,SAAUr/B,EAAG5H,EAAG0D,EAAGohD,QACXniB,IAAPmiB,IAAkBA,EAAKphD,GAC3BkE,EAAEk9C,GAAM9kD,EAAE0D,EACX,EAEM,SAASwhD,EAAallD,EAAG4H,GAC9B,IAAK,IAAI7I,KAAKiB,EAAa,YAANjB,GAAoBf,OAAO2V,UAAUuC,eAAeuF,KAAK7T,EAAG7I,IAAI8lD,EAAgBj9C,EAAG5H,EAAGjB,EAC7G,CAEO,SAASomD,EAASv9C,GACvB,IAAIS,EAAsB,mBAAXmL,QAAyBA,OAAOC,SAAUzT,EAAIqI,GAAKT,EAAES,GAAI7K,EAAI,EAC5E,GAAIwC,EAAG,OAAOA,EAAEyb,KAAK7T,GACrB,GAAIA,GAAyB,iBAAbA,EAAErI,OAAqB,MAAO,CAC1C2zB,KAAM,WAEF,OADItrB,GAAKpK,GAAKoK,EAAErI,SAAQqI,OAAI,GACrB,CAAEuJ,MAAOvJ,GAAKA,EAAEpK,KAAMylD,MAAOr7C,EACxC,GAEJ,MAAM,IAAIu4C,UAAU93C,EAAI,0BAA4B,kCACtD,CAEO,SAAS+8C,EAAOx9C,EAAGO,GACxB,IAAInI,EAAsB,mBAAXwT,QAAyB5L,EAAE4L,OAAOC,UACjD,IAAKzT,EAAG,OAAO4H,EACf,IAAmBe,EAAYlB,EAA3BjK,EAAIwC,EAAEyb,KAAK7T,GAAOy9C,EAAK,GAC3B,IACI,WAAc,IAANl9C,GAAgBA,KAAM,MAAQQ,EAAInL,EAAE01B,QAAQ+vB,MAAMoC,EAAGplD,KAAK0I,EAAEwI,MACxE,CACA,MAAOg8B,GAAS1lC,EAAI,CAAE0lC,MAAOA,EAAS,CACtC,QACI,IACQxkC,IAAMA,EAAEs6C,OAASjjD,EAAIxC,EAAU,SAAIwC,EAAEyb,KAAKje,EAClD,CACA,QAAU,GAAIiK,EAAG,MAAMA,EAAE0lC,KAAO,CACpC,CACA,OAAOkY,CACT,CAGO,SAASC,IACd,IAAK,IAAID,EAAK,GAAI7nD,EAAI,EAAGA,EAAIme,UAAUpc,OAAQ/B,IAC3C6nD,EAAKA,EAAGl+C,OAAOi+C,EAAOzpC,UAAUne,KACpC,OAAO6nD,CACT,CAGO,SAASE,IACd,IAAK,IAAIl9C,EAAI,EAAG7K,EAAI,EAAGgoD,EAAK7pC,UAAUpc,OAAQ/B,EAAIgoD,EAAIhoD,IAAK6K,GAAKsT,UAAUne,GAAG+B,OACxE,IAAIoJ,EAAI/H,MAAMyH,GAAI3E,EAAI,EAA3B,IAA8BlG,EAAI,EAAGA,EAAIgoD,EAAIhoD,IACzC,IAAK,IAAIxD,EAAI2hB,UAAUne,GAAI0C,EAAI,EAAGulD,EAAKzrD,EAAEuF,OAAQW,EAAIulD,EAAIvlD,IAAKwD,IAC1DiF,EAAEjF,GAAK1J,EAAEkG,GACjB,OAAOyI,CACT,CAEO,SAAS+8C,EAAcvgD,EAAID,EAAMygD,GACtC,GAAIA,GAA6B,IAArBhqC,UAAUpc,OAAc,IAAK,IAA4B8lD,EAAxB7nD,EAAI,EAAGyK,EAAI/C,EAAK3F,OAAY/B,EAAIyK,EAAGzK,KACxE6nD,GAAQ7nD,KAAK0H,IACRmgD,IAAIA,EAAKzkD,MAAM+S,UAAU5S,MAAM0a,KAAKvW,EAAM,EAAG1H,IAClD6nD,EAAG7nD,GAAK0H,EAAK1H,IAGrB,OAAO2H,EAAGgC,OAAOk+C,GAAMzkD,MAAM+S,UAAU5S,MAAM0a,KAAKvW,GACpD,CAEO,SAAS0gD,EAAQroD,GACtB,OAAOyW,gBAAgB4xC,GAAW5xC,KAAKzW,EAAIA,EAAGyW,MAAQ,IAAI4xC,EAAQroD,EACpE,CAEO,SAASsoD,EAAiBvC,EAASU,EAAYE,GACpD,IAAK1wC,OAAOsyC,cAAe,MAAM,IAAI3F,UAAU,wCAC/C,IAAoD3iD,EAAhD0W,EAAIgwC,EAAU1oC,MAAM8nC,EAASU,GAAc,IAAQnuC,EAAI,GAC3D,OAAOrY,EAAI,CAAC,EAAGknD,EAAK,QAASA,EAAK,SAAUA,EAAK,UACjD,SAAqB9uC,GAAK,OAAO,SAAUrY,GAAK,OAAOsjD,QAAQC,QAAQvjD,GAAG+Q,KAAKsH,EAAGuuC,EAAS,CAAG,IADrB3mD,EAAEgW,OAAOsyC,eAAiB,WAAc,OAAO9xC,IAAM,EAAGxW,EAEjI,SAASknD,EAAKv8C,EAAGyN,GAAS1B,EAAE/L,KAAM3K,EAAE2K,GAAK,SAAU5K,GAAK,OAAO,IAAIsjD,SAAQ,SAAU7mD,EAAGC,GAAK4b,EAAE5V,KAAK,CAACkI,EAAG5K,EAAGvD,EAAGC,IAAM,GAAK8rD,EAAO59C,EAAG5K,EAAI,GAAI,EAAOqY,IAAGpY,EAAE2K,GAAKyN,EAAEpY,EAAE2K,KAAO,CACvK,SAAS49C,EAAO59C,EAAG5K,GAAK,KACVoL,EADqBuL,EAAE/L,GAAG5K,IACnB4T,iBAAiBy0C,EAAU/E,QAAQC,QAAQn4C,EAAEwI,MAAM5T,GAAG+Q,KAAK03C,EAAS7B,GAAU8B,EAAOpwC,EAAE,GAAG,GAAIlN,EADtE,CAAE,MAAOlB,GAAKw+C,EAAOpwC,EAAE,GAAG,GAAIpO,EAAI,CAC/E,IAAckB,CADmE,CAEjF,SAASq9C,EAAQ70C,GAAS40C,EAAO,OAAQ50C,EAAQ,CACjD,SAASgzC,EAAOhzC,GAAS40C,EAAO,QAAS50C,EAAQ,CACjD,SAAS80C,EAAOrwC,EAAGrY,GAASqY,EAAErY,GAAIsY,EAAE2Y,QAAS3Y,EAAEtW,QAAQwmD,EAAOlwC,EAAE,GAAG,GAAIA,EAAE,GAAG,GAAK,CACnF,CAEO,SAASqwC,EAAiBt+C,GAC/B,IAAIpK,EAAGuB,EACP,OAAOvB,EAAI,CAAC,EAAGknD,EAAK,QAASA,EAAK,SAAS,SAAUj9C,GAAK,MAAMA,CAAG,IAAIi9C,EAAK,UAAWlnD,EAAEgW,OAAOC,UAAY,WAAc,OAAOO,IAAM,EAAGxW,EAC1I,SAASknD,EAAKv8C,EAAGyN,GAAKpY,EAAE2K,GAAKP,EAAEO,GAAK,SAAU5K,GAAK,OAAQwB,GAAKA,GAAK,CAAEoS,MAAOy0C,EAAQh+C,EAAEO,GAAG5K,IAAK0lD,MAAM,GAAUrtC,EAAIA,EAAErY,GAAKA,CAAG,EAAIqY,CAAG,CACvI,CAEO,SAASuwC,EAAcv+C,GAC5B,IAAK4L,OAAOsyC,cAAe,MAAM,IAAI3F,UAAU,wCAC/C,IAAiC3iD,EAA7BwC,EAAI4H,EAAE4L,OAAOsyC,eACjB,OAAO9lD,EAAIA,EAAEyb,KAAK7T,IAAMA,EAAqCu9C,EAASv9C,GAA2BpK,EAAI,CAAC,EAAGknD,EAAK,QAASA,EAAK,SAAUA,EAAK,UAAWlnD,EAAEgW,OAAOsyC,eAAiB,WAAc,OAAO9xC,IAAM,EAAGxW,GAC9M,SAASknD,EAAKv8C,GAAK3K,EAAE2K,GAAKP,EAAEO,IAAM,SAAU5K,GAAK,OAAO,IAAIsjD,SAAQ,SAAUC,EAASqD,IACvF,SAAgBrD,EAASqD,EAAQptC,EAAGxZ,GAAKsjD,QAAQC,QAAQvjD,GAAG+Q,MAAK,SAAS/Q,GAAKujD,EAAQ,CAAE3vC,MAAO5T,EAAG0lD,KAAMlsC,GAAM,GAAGotC,EAAS,EADb8B,CAAOnF,EAASqD,GAA7B5mD,EAAIqK,EAAEO,GAAG5K,IAA8B0lD,KAAM1lD,EAAE4T,MAAQ,GAAI,CAAG,CAEjK,CAEO,SAASi1C,EAAqBC,EAAQxH,GAE3C,OADI7gD,OAAOwxC,eAAkBxxC,OAAOwxC,eAAe6W,EAAQ,MAAO,CAAEl1C,MAAO0tC,IAAiBwH,EAAOxH,IAAMA,EAClGwH,CACT,CAEA,IAAIC,EAAqBtoD,OAAO2jD,OAAS,SAAU/5C,EAAGrK,GACpDS,OAAOwxC,eAAe5nC,EAAG,UAAW,CAAEq9C,YAAY,EAAM9zC,MAAO5T,GAChE,EAAI,SAASqK,EAAGrK,GACfqK,EAAW,QAAIrK,CACjB,EAEO,SAASgpD,EAAazwC,GAC3B,GAAIA,GAAOA,EAAIivC,WAAY,OAAOjvC,EAClC,IAAI3M,EAAS,CAAC,EACd,GAAW,MAAP2M,EAAa,IAAK,IAAIpS,KAAKoS,EAAe,YAANpS,GAAmB1F,OAAO2V,UAAUuC,eAAeuF,KAAK3F,EAAKpS,IAAImhD,EAAgB17C,EAAQ2M,EAAKpS,GAEtI,OADA4iD,EAAmBn9C,EAAQ2M,GACpB3M,CACT,CAEO,SAAS8lC,EAAgBn5B,GAC9B,OAAQA,GAAOA,EAAIivC,WAAcjvC,EAAM,CAAEs5B,QAASt5B,EACpD,CAEO,SAAS0wC,EAAuBC,EAAUniB,EAAOye,EAAMntC,GAC5D,GAAa,MAATmtC,IAAiBntC,EAAG,MAAM,IAAIuqC,UAAU,iDAC5C,GAAqB,mBAAV7b,EAAuBmiB,IAAaniB,IAAU1uB,GAAK0uB,EAAM59B,IAAI+/C,GAAW,MAAM,IAAItG,UAAU,4EACvG,MAAgB,MAAT4C,EAAentC,EAAa,MAATmtC,EAAentC,EAAE6F,KAAKgrC,GAAY7wC,EAAIA,EAAEzE,MAAQmzB,EAAMt7B,IAAIy9C,EACtF,CAEO,SAASC,EAAuBD,EAAUniB,EAAOnzB,EAAO4xC,EAAMntC,GACnE,GAAa,MAATmtC,EAAc,MAAM,IAAI5C,UAAU,kCACtC,GAAa,MAAT4C,IAAiBntC,EAAG,MAAM,IAAIuqC,UAAU,iDAC5C,GAAqB,mBAAV7b,EAAuBmiB,IAAaniB,IAAU1uB,GAAK0uB,EAAM59B,IAAI+/C,GAAW,MAAM,IAAItG,UAAU,2EACvG,MAAiB,MAAT4C,EAAentC,EAAE6F,KAAKgrC,EAAUt1C,GAASyE,EAAIA,EAAEzE,MAAQA,EAAQmzB,EAAMz7B,IAAI49C,EAAUt1C,GAASA,CACtG,CAEO,SAASw1C,EAAsBriB,EAAOmiB,GAC3C,GAAiB,OAAbA,GAA0C,iBAAbA,GAA6C,mBAAbA,EAA0B,MAAM,IAAItG,UAAU,0CAC/G,MAAwB,mBAAV7b,EAAuBmiB,IAAaniB,EAAQA,EAAM59B,IAAI+/C,EACtE,CAEO,SAASG,EAAwBC,EAAK11C,EAAO21C,GAClD,GAAI31C,QAAoC,CACtC,GAAqB,iBAAVA,GAAuC,mBAAVA,EAAsB,MAAM,IAAIgvC,UAAU,oBAClF,IAAI4G,EAASC,EACb,GAAIF,EAAO,CACT,IAAKtzC,OAAOyzC,aAAc,MAAM,IAAI9G,UAAU,uCAC9C4G,EAAU51C,EAAMqC,OAAOyzC,aACzB,CACA,QAAgB,IAAZF,EAAoB,CACtB,IAAKvzC,OAAOuzC,QAAS,MAAM,IAAI5G,UAAU,kCACzC4G,EAAU51C,EAAMqC,OAAOuzC,SACnBD,IAAOE,EAAQD,EACrB,CACA,GAAuB,mBAAZA,EAAwB,MAAM,IAAI5G,UAAU,0BACnD6G,IAAOD,EAAU,WAAa,IAAMC,EAAMvrC,KAAKzH,KAAO,CAAE,MAAOvM,GAAK,OAAOo5C,QAAQsD,OAAO18C,EAAI,CAAE,GACpGo/C,EAAIz4B,MAAMnuB,KAAK,CAAEkR,MAAOA,EAAO41C,QAASA,EAASD,MAAOA,GAC1D,MACSA,GACPD,EAAIz4B,MAAMnuB,KAAK,CAAE6mD,OAAO,IAE1B,OAAO31C,CACT,CAEA,IAAI+1C,EAA8C,mBAApBC,gBAAiCA,gBAAkB,SAAUha,EAAOia,EAAYC,GAC5G,IAAI5/C,EAAI,IAAIiR,MAAM2uC,GAClB,OAAO5/C,EAAE2L,KAAO,kBAAmB3L,EAAE0lC,MAAQA,EAAO1lC,EAAE2/C,WAAaA,EAAY3/C,CACjF,EAEO,SAAS6/C,EAAmBT,GACjC,SAASU,EAAK9/C,GACZo/C,EAAI1Z,MAAQ0Z,EAAIW,SAAW,IAAIN,EAAiBz/C,EAAGo/C,EAAI1Z,MAAO,4CAA8C1lC,EAC5Go/C,EAAIW,UAAW,CACjB,CAcA,OAbA,SAASt0B,IACP,KAAO2zB,EAAIz4B,MAAM7uB,QAAQ,CACvB,IAAIkoD,EAAMZ,EAAIz4B,MAAMw2B,MACpB,IACE,IAAIz7C,EAASs+C,EAAIV,SAAWU,EAAIV,QAAQtrC,KAAKgsC,EAAIt2C,OACjD,GAAIs2C,EAAIX,MAAO,OAAOjG,QAAQC,QAAQ33C,GAAQmF,KAAK4kB,GAAM,SAASzrB,GAAc,OAAT8/C,EAAK9/C,GAAWyrB,GAAQ,GACjG,CACA,MAAOzrB,GACH8/C,EAAK9/C,EACT,CACF,CACA,GAAIo/C,EAAIW,SAAU,MAAMX,EAAI1Z,KAC9B,CACOja,EACT,CAEA,SACEuuB,YACAG,WACAC,SACAG,aACAK,UACAuB,aACAG,YACAO,cACAO,kBACAK,eACAC,WACAC,SACAE,WACAC,iBACAG,gBACAE,UACAC,mBACAK,mBACAC,gBACAC,uBACAG,eACAtX,kBACAuX,yBACAE,yBACAC,wBACAC,0BACAU,qB", "sources": ["webpack://grafana-pyroscope-app/../node_modules/@leeoniya/ufuzzy/dist/uFuzzy.mjs", "webpack://grafana-pyroscope-app/../node_modules/react-use/esm/misc/util.js", "webpack://grafana-pyroscope-app/../node_modules/react-use/esm/useIsomorphicLayoutEffect.js", "webpack://grafana-pyroscope-app/../node_modules/react-use/esm/useMeasure.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/constants.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/FlameGraphContextMenu.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/FlameGraphTooltip.js", "webpack://grafana-pyroscope-app/../node_modules/tinycolor2/esm/tinycolor.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/types.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/colors.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/murmur3.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/rendering.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/FlameGraphCanvas.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/FlameGraphMetadata.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/FlameGraph.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/treeTransforms.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraph/dataTransform.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraphHeader.js", "webpack://grafana-pyroscope-app/../node_modules/react-virtualized-auto-sizer/dist/react-virtualized-auto-sizer.esm.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/TopTable/FlameGraphTopTableContainer.js", "webpack://grafana-pyroscope-app/../node_modules/@grafana/flamegraph/dist/esm/FlameGraphContainer.js", "webpack://grafana-pyroscope-app/../node_modules/file-saver/dist/FileSaver.min.js", "webpack://grafana-pyroscope-app/../node_modules/react-use/lib/useDebounce.js", "webpack://grafana-pyroscope-app/../node_modules/react-use/lib/usePrevious.js", "webpack://grafana-pyroscope-app/../node_modules/react-use/lib/useTimeoutFn.js", "webpack://grafana-pyroscope-app/../node_modules/fflate/esm/browser.js", "webpack://grafana-pyroscope-app/../node_modules/compression-streams-polyfill/esm/ponyfill.mjs", "webpack://grafana-pyroscope-app/../node_modules/compression-streams-polyfill/esm/index.mjs", "webpack://grafana-pyroscope-app/../node_modules/react-use/node_modules/tslib/tslib.es6.mjs"], "sourcesContent": ["/**\n* Copyright (c) 2025, <PERSON>\n* All rights reserved. (MIT Licensed)\n*\n* uFuzzy.js (μFuzzy)\n* A tiny, efficient fuzzy matcher that doesn't suck\n* https://github.com/leeoniya/uFuzzy (v1.0.18)\n*/\n\nconst cmp = (a, b) => a > b ? 1 : a < b ? -1 : 0;\n\nconst inf = Infinity;\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_expressions#escaping\nconst escapeRegExp = str => str.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n\n// meh, magic tmp placeholder, must be tolerant to toLocaleLowerCase(), interSplit, and intraSplit\nconst EXACT_HERE = 'eexxaacctt';\n\nconst PUNCT_RE = /\\p{P}/gu;\n\nconst LATIN_UPPER = 'A-Z';\nconst LATIN_LOWER = 'a-z';\n\nconst COLLATE_ARGS = ['en', { numeric: true, sensitivity: 'base' }];\n\nconst swapAlpha = (str, upper, lower) => str.replace(LATIN_UPPER, upper).replace(LATIN_LOWER, lower);\n\nconst OPTS = {\n\t// whether regexps use a /u unicode flag\n\tunicode: false,\n\n\talpha: null,\n\n\t// term segmentation & punct/whitespace merging\n\tinterSplit: \"[^A-Za-z\\\\d']+\",\n\tintraSplit: \"[a-z][A-Z]\",\n\n\t// inter bounds that will be used to increase lft2/rgt2 info counters\n\tinterBound: \"[^A-Za-z\\\\d]\",\n\t// intra bounds that will be used to increase lft1/rgt1 info counters\n\tintraBound: \"[A-Za-z]\\\\d|\\\\d[A-Za-z]|[a-z][A-Z]\",\n\n\t// inter-bounds mode\n\t// 2 = strict (will only match 'man' on whitepace and punct boundaries: Mega Man, Mega_Man, mega.man)\n\t// 1 = loose  (plus allowance for alpha-num and case-change boundaries: MegaMan, 0007man)\n\t// 0 = any    (will match 'man' as any substring: megamaniac)\n\tinterLft: 0,\n\tinterRgt: 0,\n\n\t// allowance between terms\n\tinterChars: '.',\n\tinterIns: inf,\n\n\t// allowance between chars in terms\n\tintraChars: \"[a-z\\\\d']\", // internally case-insensitive\n\tintraIns: null,\n\n\tintraContr: \"'[a-z]{1,2}\\\\b\",\n\n\t// multi-insert or single-error mode\n\tintraMode: 0,\n\n\t// single-error bounds for errors within terms, default requires exact first char\n\tintraSlice: [1, inf],\n\n\t// single-error tolerance toggles\n\tintraSub: null,\n\tintraTrn: null,\n\tintraDel: null,\n\n\t// can post-filter matches that are too far apart in distance or length\n\t// (since intraIns is between each char, it can accum to nonsense matches)\n\tintraFilt: (term, match, index) => true, // should this also accept WIP info?\n\n\ttoUpper: str => str.toLocaleUpperCase(),\n\ttoLower: str => str.toLocaleLowerCase(),\n\tcompare: null,\n\n\t// final sorting fn\n\tsort: (info, haystack, needle, compare = cmp) => {\n\t\tlet {\n\t\t\tidx,\n\t\t\tchars,\n\t\t\tterms,\n\t\t\tinterLft2,\n\t\t\tinterLft1,\n\t\t//\tinterRgt2,\n\t\t//\tinterRgt1,\n\t\t\tstart,\n\t\t\tintraIns,\n\t\t\tinterIns,\n\t\t\tcases,\n\t\t} = info;\n\n\t\treturn idx.map((v, i) => i).sort((ia, ib) => (\n\t\t\t// most contig chars matched\n\t\t\tchars[ib] - chars[ia] ||\n\t\t\t// least char intra-fuzz (most contiguous)\n\t\t\tintraIns[ia] - intraIns[ib] ||\n\t\t\t// most prefix bounds, boosted by full term matches\n\t\t\t(\n\t\t\t\t(terms[ib] + interLft2[ib] + 0.5 * interLft1[ib]) -\n\t\t\t\t(terms[ia] + interLft2[ia] + 0.5 * interLft1[ia])\n\t\t\t) ||\n\t\t\t// highest density of match (least span)\n\t\t//\tspan[ia] - span[ib] ||\n\t\t\t// highest density of match (least term inter-fuzz)\n\t\t\tinterIns[ia] - interIns[ib] ||\n\t\t\t// earliest start of match\n\t\t\tstart[ia] - start[ib] ||\n\t\t\t// case match\n\t\t\tcases[ib] - cases[ia] ||\n\t\t\t// alphabetic\n\t\t\tcompare(haystack[idx[ia]], haystack[idx[ib]])\n\t\t));\n\t},\n};\n\nconst lazyRepeat = (chars, limit) => (\n\tlimit == 0   ? ''           :\n\tlimit == 1   ? chars + '??' :\n\tlimit == inf ? chars + '*?' :\n\t               chars + `{0,${limit}}?`\n);\n\nconst mode2Tpl = '(?:\\\\b|_)';\n\nfunction uFuzzy(opts) {\n\topts = Object.assign({}, OPTS, opts);\n\n\tlet {\n\t\tunicode,\n\t\tinterLft,\n\t\tinterRgt,\n\t\tintraMode,\n\t\tintraSlice,\n\t\tintraIns,\n\t\tintraSub,\n\t\tintraTrn,\n\t\tintraDel,\n\t\tintraContr,\n\t\tintraSplit: _intraSplit,\n\t\tinterSplit: _interSplit,\n\t\tintraBound: _intraBound,\n\t\tinterBound: _interBound,\n\t\tintraChars,\n\t\ttoUpper,\n\t\ttoLower,\n\t\tcompare,\n\t} = opts;\n\n\tintraIns ??= intraMode;\n\tintraSub ??= intraMode;\n\tintraTrn ??= intraMode;\n\tintraDel ??= intraMode;\n\n\tcompare ??= typeof Intl == \"undefined\" ? cmp : new Intl.Collator(...COLLATE_ARGS).compare;\n\n\tlet alpha = opts.letters ?? opts.alpha;\n\n\tif (alpha != null) {\n\t\tlet upper = toUpper(alpha);\n\t\tlet lower = toLower(alpha);\n\n\t\t_interSplit = swapAlpha(_interSplit, upper, lower);\n\t\t_intraSplit = swapAlpha(_intraSplit, upper, lower);\n\t\t_interBound = swapAlpha(_interBound, upper, lower);\n\t\t_intraBound = swapAlpha(_intraBound, upper, lower);\n\t\tintraChars = swapAlpha(intraChars, upper, lower);\n\t\tintraContr = swapAlpha(intraContr, upper, lower);\n\t}\n\n\tlet uFlag = unicode ? 'u' : '';\n\n\tconst quotedAny = '\".+?\"';\n\tconst EXACTS_RE = new RegExp(quotedAny, 'gi' + uFlag);\n\tconst NEGS_RE = new RegExp(`(?:\\\\s+|^)-(?:${intraChars}+|${quotedAny})`, 'gi' + uFlag);\n\n\tlet { intraRules } = opts;\n\n\tif (intraRules == null) {\n\t\tintraRules = p => {\n\t\t\t// default is exact term matches only\n\t\t\tlet _intraSlice = OPTS.intraSlice, // requires first char\n\t\t\t\t_intraIns = 0,\n\t\t\t\t_intraSub = 0,\n\t\t\t\t_intraTrn = 0,\n\t\t\t\t_intraDel = 0;\n\n\t\t\t// only-digits strings should match exactly, else special rules for short strings\n\t\t\tif (/[^\\d]/.test(p)) {\n\t\t\t\tlet plen = p.length;\n\n\t\t\t\t// prevent junk matches by requiring stricter rules for short terms\n\t\t\t\tif (plen <= 4) {\n\t\t\t\t\tif (plen >= 3) {\n\t\t\t\t\t\t// one swap in non-first char when 3-4 chars\n\t\t\t\t\t\t_intraTrn = Math.min(intraTrn, 1);\n\n\t\t\t\t\t\t// or one insertion when 4 chars\n\t\t\t\t\t\tif (plen == 4)\n\t\t\t\t\t\t\t_intraIns = Math.min(intraIns, 1);\n\t\t\t\t\t}\n\t\t\t\t\t// else exact match when 1-2 chars\n\t\t\t\t}\n\t\t\t\t// use supplied opts\n\t\t\t\telse {\n\t\t\t\t\t_intraSlice = intraSlice;\n\t\t\t\t\t_intraIns = intraIns,\n\t\t\t\t\t_intraSub = intraSub,\n\t\t\t\t\t_intraTrn = intraTrn,\n\t\t\t\t\t_intraDel = intraDel;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tintraSlice: _intraSlice,\n\t\t\t\tintraIns: _intraIns,\n\t\t\t\tintraSub: _intraSub,\n\t\t\t\tintraTrn: _intraTrn,\n\t\t\t\tintraDel: _intraDel,\n\t\t\t};\n\t\t};\n\t}\n\n\tlet withIntraSplit = !!_intraSplit;\n\n\tlet intraSplit = new RegExp(_intraSplit, 'g' + uFlag);\n\tlet interSplit = new RegExp(_interSplit, 'g' + uFlag);\n\n\tlet trimRe = new RegExp('^' + _interSplit + '|' + _interSplit + '$', 'g' + uFlag);\n\tlet contrsRe = new RegExp(intraContr, 'gi' + uFlag);\n\n\tconst split = (needle, keepCase = false) => {\n\t\tlet exacts = [];\n\n\t\tneedle = needle.replace(EXACTS_RE, m => {\n\t\t\texacts.push(m);\n\t\t\treturn EXACT_HERE;\n\t\t});\n\n\t\tneedle = needle.replace(trimRe, '');\n\n\t\tif (!keepCase)\n\t\t\tneedle = toLower(needle);\n\n\t\tif (withIntraSplit)\n\t\t\tneedle = needle.replace(intraSplit, m => m[0] + ' ' + m[1]);\n\n\t\tlet j = 0;\n\t\treturn needle.split(interSplit).filter(t => t != '').map(v => v === EXACT_HERE ? exacts[j++] : v);\n\t};\n\n\tconst NUM_OR_ALPHA_RE = /[^\\d]+|\\d+/g;\n\n\tconst prepQuery = (needle, capt = 0, interOR = false) => {\n\t\t// split on punct, whitespace, num-alpha, and upper-lower boundaries\n\t\tlet parts = split(needle);\n\n\t\tif (parts.length == 0)\n\t\t\treturn [];\n\n\t\t// split out any detected contractions for each term that become required suffixes\n\t\tlet contrs = Array(parts.length).fill('');\n\t\tparts = parts.map((p, pi) => p.replace(contrsRe, m => {\n\t\t\tcontrs[pi] = m;\n\t\t\treturn '';\n\t\t}));\n\n\t\t// array of regexp tpls for each term\n\t\tlet reTpl;\n\n\t\t// allows single mutations within each term\n\t\tif (intraMode == 1) {\n\t\t\treTpl = parts.map((p, pi) => {\n\t\t\t\tif (p[0] === '\"')\n\t\t\t\t\treturn escapeRegExp(p.slice(1, -1));\n\n\t\t\t\tlet reTpl = '';\n\n\t\t\t\t// split into numeric and alpha parts, so numbers are only matched as following punct or alpha boundaries, without swaps or insertions\n\t\t\t\tfor (let m of p.matchAll(NUM_OR_ALPHA_RE)) {\n\t\t\t\t\tlet p = m[0];\n\n\t\t\t\t\tlet {\n\t\t\t\t\t\tintraSlice,\n\t\t\t\t\t\tintraIns,\n\t\t\t\t\t\tintraSub,\n\t\t\t\t\t\tintraTrn,\n\t\t\t\t\t\tintraDel,\n\t\t\t\t\t} = intraRules(p);\n\n\t\t\t\t\tif (intraIns + intraSub + intraTrn + intraDel == 0)\n\t\t\t\t\t\treTpl += p + contrs[pi];\n\t\t\t\t\telse {\n\t\t\t\t\t\tlet [lftIdx, rgtIdx] = intraSlice;\n\t\t\t\t\t\tlet lftChar = p.slice(0, lftIdx); // prefix\n\t\t\t\t\t\tlet rgtChar = p.slice(rgtIdx);    // suffix\n\n\t\t\t\t\t\tlet chars = p.slice(lftIdx, rgtIdx);\n\n\t\t\t\t\t\t// neg lookahead to prefer matching 'Test' instead of 'tTest' in ManifestTest or fittest\n\t\t\t\t\t\t// but skip when search term contains leading repetition (aardvark, aaa)\n\t\t\t\t\t\tif (intraIns == 1 && lftChar.length == 1 && lftChar != chars[0])\n\t\t\t\t\t\t\tlftChar += '(?!' + lftChar + ')';\n\n\t\t\t\t\t\tlet numChars = chars.length;\n\n\t\t\t\t\t\tlet variants = [p];\n\n\t\t\t\t\t\t// variants with single char substitutions\n\t\t\t\t\t\tif (intraSub) {\n\t\t\t\t\t\t\tfor (let i = 0; i < numChars; i++)\n\t\t\t\t\t\t\t\tvariants.push(lftChar + chars.slice(0, i) + intraChars + chars.slice(i + 1) + rgtChar);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// variants with single transpositions\n\t\t\t\t\t\tif (intraTrn) {\n\t\t\t\t\t\t\tfor (let i = 0; i < numChars - 1; i++) {\n\t\t\t\t\t\t\t\tif (chars[i] != chars[i+1])\n\t\t\t\t\t\t\t\t\tvariants.push(lftChar + chars.slice(0, i) + chars[i+1] + chars[i] + chars.slice(i + 2) + rgtChar);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// variants with single char omissions\n\t\t\t\t\t\tif (intraDel) {\n\t\t\t\t\t\t\tfor (let i = 0; i < numChars; i++)\n\t\t\t\t\t\t\t\tvariants.push(lftChar + chars.slice(0, i + 1) + '?' + chars.slice(i + 1) + rgtChar);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// variants with single char insertions\n\t\t\t\t\t\tif (intraIns) {\n\t\t\t\t\t\t\tlet intraInsTpl = lazyRepeat(intraChars, 1);\n\n\t\t\t\t\t\t\tfor (let i = 0; i < numChars; i++)\n\t\t\t\t\t\t\t\tvariants.push(lftChar + chars.slice(0, i) + intraInsTpl + chars.slice(i) + rgtChar);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treTpl += '(?:' + variants.join('|') + ')' + contrs[pi];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t//\tconsole.log(reTpl);\n\n\t\t\t\treturn reTpl;\n\t\t\t});\n\t\t}\n\t\telse {\n\t\t\tlet intraInsTpl = lazyRepeat(intraChars, intraIns);\n\n\t\t\t// capture at char level\n\t\t\tif (capt == 2 && intraIns > 0) {\n\t\t\t\t// sadly, we also have to capture the inter-term junk via parenth-wrapping .*?\n\t\t\t\t// to accum other capture groups' indices for \\b boosting during scoring\n\t\t\t\tintraInsTpl = ')(' + intraInsTpl + ')(';\n\t\t\t}\n\n\t\t\treTpl = parts.map((p, pi) => p[0] === '\"' ? escapeRegExp(p.slice(1, -1)) :  p.split('').map((c, i, chars) => {\n\t\t\t\t// neg lookahead to prefer matching 'Test' instead of 'tTest' in ManifestTest or fittest\n\t\t\t\t// but skip when search term contains leading repetition (aardvark, aaa)\n\t\t\t\tif (intraIns == 1 && i == 0 && chars.length > 1 && c != chars[i+1])\n\t\t\t\t\tc += '(?!' + c + ')';\n\n\t\t\t\treturn c;\n\t\t\t}).join(intraInsTpl) + contrs[pi]);\n\t\t}\n\n\t//\tconsole.log(reTpl);\n\n\t\t// this only helps to reduce initial matches early when they can be detected\n\t\t// TODO: might want a mode 3 that excludes _\n\t\tlet preTpl = interLft == 2 ? mode2Tpl : '';\n\t\tlet sufTpl = interRgt == 2 ? mode2Tpl : '';\n\n\t\tlet interCharsTpl = sufTpl + lazyRepeat(opts.interChars, opts.interIns) + preTpl;\n\n\t\t// capture at word level\n\t\tif (capt > 0) {\n\t\t\tif (interOR) {\n\t\t\t\t// this is basically for doing .matchAll() occurence counting and highlighting without needing permuted ooo needles\n\t\t\t\treTpl = preTpl + '(' + reTpl.join(')' + sufTpl + '|' + preTpl + '(') + ')' + sufTpl;\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// sadly, we also have to capture the inter-term junk via parenth-wrapping .*?\n\t\t\t\t// to accum other capture groups' indices for \\b boosting during scoring\n\t\t\t\treTpl = '(' + reTpl.join(')(' + interCharsTpl + ')(') + ')';\n\t\t\t\treTpl = '(.??' + preTpl + ')' + reTpl + '(' + sufTpl + '.*)'; // nit: trailing capture here assumes interIns = Inf\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\treTpl = reTpl.join(interCharsTpl);\n\t\t\treTpl = preTpl + reTpl + sufTpl;\n\t\t}\n\n\t//\tconsole.log(reTpl);\n\n\t\treturn [new RegExp(reTpl, 'i' + uFlag), parts, contrs];\n\t};\n\n\tconst filter = (haystack, needle, idxs) => {\n\n\t\tlet [query] = prepQuery(needle);\n\n\t\tif (query == null)\n\t\t\treturn null;\n\n\t\tlet out = [];\n\n\t\tif (idxs != null) {\n\t\t\tfor (let i = 0; i < idxs.length; i++) {\n\t\t\t\tlet idx = idxs[i];\n\t\t\t\tquery.test(haystack[idx]) && out.push(idx);\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tfor (let i = 0; i < haystack.length; i++)\n\t\t\t\tquery.test(haystack[i]) && out.push(i);\n\t\t}\n\n\t\treturn out;\n\t};\n\n\tlet withIntraBound = !!_intraBound;\n\n\tlet interBound = new RegExp(_interBound, uFlag);\n\tlet intraBound = new RegExp(_intraBound, uFlag);\n\n\tconst info = (idxs, haystack, needle) => {\n\n\t\tlet [query, parts, contrs] = prepQuery(needle, 1);\n\t\tlet partsCased = split(needle, true);\n\t\tlet [queryR] = prepQuery(needle, 2);\n\t\tlet partsLen = parts.length;\n\n\t\tlet _terms      = Array(partsLen);\n\t\tlet _termsCased = Array(partsLen);\n\n\t\tfor (let j = 0; j < partsLen; j++) {\n\t\t\tlet part      = parts[j];\n\t\t\tlet partCased = partsCased[j];\n\n\t\t\tlet term      = part[0]      == '\"' ? part.slice(1, -1)      : part      + contrs[j];\n\t\t\tlet termCased = partCased[0] == '\"' ? partCased.slice(1, -1) : partCased + contrs[j];\n\n\t\t\t_terms[j]      = term;\n\t\t\t_termsCased[j] = termCased;\n\t\t}\n\n\t\tlet len = idxs.length;\n\n\t\tlet field = Array(len).fill(0);\n\n\t\tlet info = {\n\t\t\t// idx in haystack\n\t\t\tidx: Array(len),\n\n\t\t\t// start of match\n\t\t\tstart: field.slice(),\n\t\t\t// length of match\n\t\t//\tspan: field.slice(),\n\n\t\t\t// contiguous chars matched\n\t\t\tchars: field.slice(),\n\n\t\t\t// case matched in term (via term.includes(match))\n\t\t\tcases: field.slice(),\n\n\t\t\t// contiguous (no fuzz) and bounded terms (intra=0, lft2/1, rgt2/1)\n\t\t\t// excludes terms that are contiguous but have < 2 bounds (substrings)\n\t\t\tterms: field.slice(),\n\n\t\t\t// cumulative length of unmatched chars (fuzz) within span\n\t\t\tinterIns: field.slice(), // between terms\n\t\t\tintraIns: field.slice(), // within terms\n\n\t\t\t// interLft/interRgt counters\n\t\t\tinterLft2: field.slice(),\n\t\t\tinterRgt2: field.slice(),\n\t\t\tinterLft1: field.slice(),\n\t\t\tinterRgt1: field.slice(),\n\n\t\t\tranges: Array(len),\n\t\t};\n\n\t\t// might discard idxs based on bounds checks\n\t\tlet mayDiscard = interLft == 1 || interRgt == 1;\n\n\t\tlet ii = 0;\n\n\t\tfor (let i = 0; i < idxs.length; i++) {\n\t\t\tlet mhstr = haystack[idxs[i]];\n\n\t\t\t// the matched parts are [full, junk, term, junk, term, junk]\n\t\t\tlet m = mhstr.match(query);\n\n\t\t\t// leading junk\n\t\t\tlet start = m.index + m[1].length;\n\n\t\t\tlet idxAcc = start;\n\t\t//\tlet span = m[0].length;\n\n\t\t\tlet disc = false;\n\t\t\tlet lft2 = 0;\n\t\t\tlet lft1 = 0;\n\t\t\tlet rgt2 = 0;\n\t\t\tlet rgt1 = 0;\n\t\t\tlet chars = 0;\n\t\t\tlet terms = 0;\n\t\t\tlet cases = 0;\n\t\t\tlet inter = 0;\n\t\t\tlet intra = 0;\n\n\t\t\tlet refine = [];\n\n\t\t\tfor (let j = 0, k = 2; j < partsLen; j++, k+=2) {\n\t\t\t\tlet group     = toLower(m[k]);\n\t\t\t\tlet term      = _terms[j];\n\t\t\t\tlet termCased = _termsCased[j];\n\t\t\t\tlet termLen   = term.length;\n\t\t\t\tlet groupLen  = group.length;\n\t\t\t\tlet fullMatch = group == term;\n\n\t\t\t\tif (m[k] == termCased)\n\t\t\t\t\tcases++;\n\n\t\t\t\t// this won't handle the case when an exact match exists across the boundary of the current group and the next junk\n\t\t\t\t// e.g. blob,ob when searching for 'bob' but finding the earlier `blob` (with extra insertion)\n\t\t\t\tif (!fullMatch && m[k+1].length >= termLen) {\n\t\t\t\t\t// probe for exact match in inter junk (TODO: maybe even in this matched part?)\n\t\t\t\t\tlet idxOf = toLower(m[k+1]).indexOf(term);\n\n\t\t\t\t\tif (idxOf > -1) {\n\t\t\t\t\t\trefine.push(idxAcc, groupLen, idxOf, termLen);\n\t\t\t\t\t\tidxAcc += refineMatch(m, k, idxOf, termLen);\n\t\t\t\t\t\tgroup = term;\n\t\t\t\t\t\tgroupLen = termLen;\n\t\t\t\t\t\tfullMatch = true;\n\n\t\t\t\t\t\tif (j == 0)\n\t\t\t\t\t\t\tstart = idxAcc;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (mayDiscard || fullMatch) {\n\t\t\t\t\t// does group's left and/or right land on \\b\n\t\t\t\t\tlet lftCharIdx = idxAcc - 1;\n\t\t\t\t\tlet rgtCharIdx = idxAcc + groupLen;\n\n\t\t\t\t\tlet isPre = false;\n\t\t\t\t\tlet isSuf = false;\n\n\t\t\t\t\t// prefix info\n\t\t\t\t\tif (lftCharIdx == -1           || interBound.test(mhstr[lftCharIdx])) {\n\t\t\t\t\t\tfullMatch && lft2++;\n\t\t\t\t\t\tisPre = true;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tif (interLft == 2) {\n\t\t\t\t\t\t\tdisc = true;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (withIntraBound && intraBound.test(mhstr[lftCharIdx] + mhstr[lftCharIdx + 1])) {\n\t\t\t\t\t\t\tfullMatch && lft1++;\n\t\t\t\t\t\t\tisPre = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tif (interLft == 1) {\n\t\t\t\t\t\t\t\t// regexps are eager, so try to improve the match by probing forward inter junk for exact match at a boundary\n\t\t\t\t\t\t\t\tlet junk = m[k+1];\n\t\t\t\t\t\t\t\tlet junkIdx = idxAcc + groupLen;\n\n\t\t\t\t\t\t\t\tif (junk.length >= termLen) {\n\t\t\t\t\t\t\t\t\tlet idxOf = 0;\n\t\t\t\t\t\t\t\t\tlet found = false;\n\t\t\t\t\t\t\t\t\tlet re = new RegExp(term, 'ig' + uFlag);\n\n\t\t\t\t\t\t\t\t\tlet m2;\n\t\t\t\t\t\t\t\t\twhile (m2 = re.exec(junk)) {\n\t\t\t\t\t\t\t\t\t\tidxOf = m2.index;\n\n\t\t\t\t\t\t\t\t\t\tlet charIdx = junkIdx + idxOf;\n\t\t\t\t\t\t\t\t\t\tlet lftCharIdx = charIdx - 1;\n\n\t\t\t\t\t\t\t\t\t\tif (lftCharIdx == -1 || interBound.test(mhstr[lftCharIdx])) {\n\t\t\t\t\t\t\t\t\t\t\tlft2++;\n\t\t\t\t\t\t\t\t\t\t\tfound = true;\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\telse if (intraBound.test(mhstr[lftCharIdx] + mhstr[charIdx])) {\n\t\t\t\t\t\t\t\t\t\t\tlft1++;\n\t\t\t\t\t\t\t\t\t\t\tfound = true;\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tif (found) {\n\t\t\t\t\t\t\t\t\t\tisPre = true;\n\n\t\t\t\t\t\t\t\t\t\t// identical to exact term refinement pass above\n\t\t\t\t\t\t\t\t\t\trefine.push(idxAcc, groupLen, idxOf, termLen);\n\t\t\t\t\t\t\t\t\t\tidxAcc += refineMatch(m, k, idxOf, termLen);\n\t\t\t\t\t\t\t\t\t\tgroup = term;\n\t\t\t\t\t\t\t\t\t\tgroupLen = termLen;\n\t\t\t\t\t\t\t\t\t\tfullMatch = true;\n\n\t\t\t\t\t\t\t\t\t\tif (j == 0)\n\t\t\t\t\t\t\t\t\t\t\tstart = idxAcc;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tif (!isPre) {\n\t\t\t\t\t\t\t\t\tdisc = true;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// suffix info\n\t\t\t\t\tif (rgtCharIdx == mhstr.length || interBound.test(mhstr[rgtCharIdx])) {\n\t\t\t\t\t\tfullMatch && rgt2++;\n\t\t\t\t\t\tisSuf = true;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tif (interRgt == 2) {\n\t\t\t\t\t\t\tdisc = true;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (withIntraBound && intraBound.test(mhstr[rgtCharIdx - 1] + mhstr[rgtCharIdx])) {\n\t\t\t\t\t\t\tfullMatch && rgt1++;\n\t\t\t\t\t\t\tisSuf = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tif (interRgt == 1) {\n\t\t\t\t\t\t\t\tdisc = true;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tif (fullMatch) {\n\t\t\t\t\t\tchars += termLen;\n\n\t\t\t\t\t\tif (isPre && isSuf)\n\t\t\t\t\t\t\tterms++;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (groupLen > termLen)\n\t\t\t\t\tintra += groupLen - termLen; // intraFuzz\n\n\t\t\t\tif (j > 0)\n\t\t\t\t\tinter += m[k-1].length; // interFuzz\n\n\t\t\t\t// TODO: group here is lowercased, which is okay for length cmp, but not more case-sensitive filts\n\t\t\t\tif (!opts.intraFilt(term, group, idxAcc)) {\n\t\t\t\t\tdisc = true;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tif (j < partsLen - 1)\n\t\t\t\t\tidxAcc += groupLen + m[k+1].length;\n\t\t\t}\n\n\t\t\tif (!disc) {\n\t\t\t\tinfo.idx[ii]       = idxs[i];\n\t\t\t\tinfo.interLft2[ii] = lft2;\n\t\t\t\tinfo.interLft1[ii] = lft1;\n\t\t\t\tinfo.interRgt2[ii] = rgt2;\n\t\t\t\tinfo.interRgt1[ii] = rgt1;\n\t\t\t\tinfo.chars[ii]     = chars;\n\t\t\t\tinfo.terms[ii]     = terms;\n\t\t\t\tinfo.cases[ii]     = cases;\n\t\t\t\tinfo.interIns[ii]  = inter;\n\t\t\t\tinfo.intraIns[ii]  = intra;\n\n\t\t\t\tinfo.start[ii] = start;\n\t\t\t//\tinfo.span[ii] = span;\n\n\t\t\t\t// ranges\n\t\t\t\tlet m = mhstr.match(queryR);\n\n\t\t\t\tlet idxAcc = m.index + m[1].length;\n\n\t\t\t\tlet refLen = refine.length;\n\t\t\t\tlet ri = refLen > 0 ? 0 : Infinity;\n\t\t\t\tlet lastRi = refLen - 4;\n\n\t\t\t\tfor (let i = 2; i < m.length;) {\n\t\t\t\t\tlet len = m[i].length;\n\n\t\t\t\t\tif (ri <= lastRi && refine[ri] == idxAcc) {\n\t\t\t\t\t\tlet groupLen = refine[ri+1];\n\t\t\t\t\t\tlet idxOf    = refine[ri+2];\n\t\t\t\t\t\tlet termLen  = refine[ri+3];\n\n\t\t\t\t\t\t// advance to end of original (full) group match that includes intra-junk\n\t\t\t\t\t\tlet j = i;\n\t\t\t\t\t\tlet v = '';\n\t\t\t\t\t\tfor (let _len = 0; _len < groupLen; j++) {\n\t\t\t\t\t\t\tv += m[j];\n\t\t\t\t\t\t\t_len += m[j].length;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tm.splice(i, j - i, v);\n\n\t\t\t\t\t\tidxAcc += refineMatch(m, i, idxOf, termLen);\n\n\t\t\t\t\t\tri += 4;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tidxAcc += len;\n\t\t\t\t\t\ti++;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tidxAcc = m.index + m[1].length;\n\n\t\t\t\tlet ranges = info.ranges[ii] = [];\n\t\t\t\tlet from = idxAcc;\n\t\t\t\tlet to = idxAcc;\n\n\t\t\t\tfor (let i = 2; i < m.length; i++) {\n\t\t\t\t\tlet len = m[i].length;\n\n\t\t\t\t\tidxAcc += len;\n\n\t\t\t\t\tif (i % 2 == 0)\n\t\t\t\t\t\tto = idxAcc;\n\t\t\t\t\telse if (len > 0) {\n\t\t\t\t\t\tranges.push(from, to);\n\t\t\t\t\t\tfrom = to = idxAcc;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (to > from)\n\t\t\t\t\tranges.push(from, to);\n\n\t\t\t\tii++;\n\t\t\t}\n\t\t}\n\n\t\t// trim arrays\n\t\tif (ii < idxs.length) {\n\t\t\tfor (let k in info)\n\t\t\t\tinfo[k] = info[k].slice(0, ii);\n\t\t}\n\n\t\treturn info;\n\t};\n\n\tconst refineMatch = (m, k, idxInNext, termLen) => {\n\t\t// shift the current group into the prior junk\n\t\tlet prepend = m[k] + m[k+1].slice(0, idxInNext);\n\t\tm[k-1] += prepend;\n\t\tm[k]    = m[k+1].slice(idxInNext, idxInNext + termLen);\n\t\tm[k+1]  = m[k+1].slice(idxInNext + termLen);\n\t\treturn prepend.length;\n\t};\n\n\tconst OOO_TERMS_LIMIT = 5;\n\n\t// returns [idxs, info, order]\n\tconst _search = (haystack, needle, outOfOrder, infoThresh = 1e3, preFiltered) => {\n\t\toutOfOrder = !outOfOrder ? 0 : outOfOrder === true ? OOO_TERMS_LIMIT : outOfOrder;\n\n\t\tlet needles = null;\n\t\tlet matches = null;\n\n\t\tlet negs = [];\n\n\t\tneedle = needle.replace(NEGS_RE, m => {\n\t\t\tlet neg = m.trim().slice(1);\n\n\t\t\tneg = neg[0] === '\"' ? escapeRegExp(neg.slice(1,-1)) :  neg.replace(PUNCT_RE, '');\n\n\t\t\tif (neg != '')\n\t\t\t\tnegs.push(neg);\n\n\t\t\treturn '';\n\t\t});\n\n\t\tlet terms = split(needle);\n\n\t\tlet negsRe;\n\n\t\tif (negs.length > 0) {\n\t\t\tnegsRe = new RegExp(negs.join('|'), 'i' + uFlag);\n\n\t\t\tif (terms.length == 0) {\n\t\t\t\tlet idxs = [];\n\n\t\t\t\tfor (let i = 0; i < haystack.length; i++) {\n\t\t\t\t\tif (!negsRe.test(haystack[i]))\n\t\t\t\t\t\tidxs.push(i);\n\t\t\t\t}\n\n\t\t\t\treturn [idxs, null, null];\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\t// abort search (needle is empty after pre-processing, e.g. no alpha-numeric chars)\n\t\t\tif (terms.length == 0)\n\t\t\t\treturn [null, null, null];\n\t\t}\n\n\t//\tconsole.log(negs);\n\t//\tconsole.log(needle);\n\n\t\tif (outOfOrder > 0) {\n\t\t\t// since uFuzzy is an AND-based search, we can iteratively pre-reduce the haystack by searching\n\t\t\t// for each term in isolation before running permutations on what's left.\n\t\t\t// this is a major perf win. e.g. searching \"test man ger pp a\" goes from 570ms -> 14ms\n\t\t\tlet terms = split(needle);\n\n\t\t\tif (terms.length > 1) {\n\t\t\t\t// longest -> shortest\n\t\t\t\tlet terms2 = terms.slice().sort((a, b) => b.length - a.length);\n\n\t\t\t\tfor (let ti = 0; ti < terms2.length; ti++) {\n\t\t\t\t\t// no haystack item contained all terms\n\t\t\t\t\tif (preFiltered?.length == 0)\n\t\t\t\t\t\treturn [[], null, null];\n\n\t\t\t\t\tpreFiltered = filter(haystack, terms2[ti], preFiltered);\n\t\t\t\t}\n\n\t\t\t\t// avoid combinatorial explosion by limiting outOfOrder to 5 terms (120 max searches)\n\t\t\t\t// fall back to just filter() otherwise\n\t\t\t\tif (terms.length > outOfOrder)\n\t\t\t\t\treturn [preFiltered, null, null];\n\n\t\t\t\tneedles = permute(terms).map(perm => perm.join(' '));\n\n\t\t\t\t// filtered matches for each needle excluding same matches for prior needles\n\t\t\t\tmatches = [];\n\n\t\t\t\t// keeps track of already-matched idxs to skip in follow-up permutations\n\t\t\t\tlet matchedIdxs = new Set();\n\n\t\t\t\tfor (let ni = 0; ni < needles.length; ni++) {\n\t\t\t\t\tif (matchedIdxs.size < preFiltered.length) {\n\t\t\t\t\t\t// filter further for this needle, exclude already-matched\n\t\t\t\t\t\tlet preFiltered2 = preFiltered.filter(idx => !matchedIdxs.has(idx));\n\n\t\t\t\t\t\tlet matched = filter(haystack, needles[ni], preFiltered2);\n\n\t\t\t\t\t\tfor (let j = 0; j < matched.length; j++)\n\t\t\t\t\t\t\tmatchedIdxs.add(matched[j]);\n\n\t\t\t\t\t\tmatches.push(matched);\n\t\t\t\t\t}\n\t\t\t\t\telse\n\t\t\t\t\t\tmatches.push([]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// interOR\n\t//\tconsole.log(prepQuery(needle, 1, null, true));\n\n\t\t// non-ooo or ooo w/single term\n\t\tif (needles == null) {\n\t\t\tneedles = [needle];\n\t\t\tmatches = [preFiltered?.length > 0 ? preFiltered : filter(haystack, needle)];\n\t\t}\n\n\t\tlet retInfo = null;\n\t\tlet retOrder = null;\n\n\t\tif (negs.length > 0)\n\t\t\tmatches = matches.map(idxs => idxs.filter(idx => !negsRe.test(haystack[idx])));\n\n\t\tlet matchCount = matches.reduce((acc, idxs) => acc + idxs.length, 0);\n\n\t\t// rank, sort, concat\n\t\tif (matchCount <= infoThresh) {\n\t\t\tretInfo = {};\n\t\t\tretOrder = [];\n\n\t\t\tfor (let ni = 0; ni < matches.length; ni++) {\n\t\t\t\tlet idxs = matches[ni];\n\n\t\t\t\tif (idxs == null || idxs.length == 0)\n\t\t\t\t\tcontinue;\n\n\t\t\t\tlet needle = needles[ni];\n\t\t\t\tlet _info = info(idxs, haystack, needle);\n\t\t\t\tlet order = opts.sort(_info, haystack, needle, compare);\n\n\t\t\t\t// offset idxs for concat'ing infos\n\t\t\t\tif (ni > 0) {\n\t\t\t\t\tfor (let i = 0; i < order.length; i++)\n\t\t\t\t\t\torder[i] += retOrder.length;\n\t\t\t\t}\n\n\t\t\t\tfor (let k in _info)\n\t\t\t\t\tretInfo[k] = (retInfo[k] ?? []).concat(_info[k]);\n\n\t\t\t\tretOrder = retOrder.concat(order);\n\t\t\t}\n\t\t}\n\n\t\treturn [\n\t\t\t[].concat(...matches),\n\t\t\tretInfo,\n\t\t\tretOrder,\n\t\t];\n\t};\n\n\treturn {\n\t\tsearch: (...args) => {\n\t\t\tlet out = _search(...args);\n\t\t\treturn out;\n\t\t},\n\t\tsplit,\n\t\tfilter,\n\t\tinfo,\n\t\tsort: opts.sort,\n\t};\n}\n\nconst latinize = (() => {\n\tlet accents = {\n\t\tA: 'ÁÀÃÂÄĄ',\n\t\ta: 'áàãâäą',\n\t\tE: 'ÉÈÊËĖ',\n\t\te: 'éèêëę',\n\t\tI: 'ÍÌÎÏĮ',\n\t\ti: 'íìîïį',\n\t\tO: 'ÓÒÔÕÖ',\n\t\to: 'óòôõö',\n\t\tU: 'ÚÙÛÜŪŲ',\n\t\tu: 'úùûüūų',\n\t\tC: 'ÇČĆ',\n\t\tc: 'çčć',\n\t\tL: 'Ł',\n\t\tl: 'ł',\n\t\tN: 'ÑŃ',\n\t\tn: 'ñń',\n\t\tS: 'ŠŚ',\n\t\ts: 'šś',\n\t\tZ: 'ŻŹ',\n\t\tz: 'żź'\n\t};\n\n\tlet accentsMap = new Map();\n\tlet accentsTpl = '';\n\n\tfor (let r in accents) {\n\t\taccents[r].split('').forEach(a => {\n\t\t\taccentsTpl += a;\n\t\t\taccentsMap.set(a, r);\n\t\t});\n\t}\n\n\tlet accentsRe = new RegExp(`[${accentsTpl}]`, 'g');\n\tlet replacer = m => accentsMap.get(m);\n\n\treturn strings => {\n\t\tif (typeof strings == 'string')\n\t\t\treturn strings.replace(accentsRe, replacer);\n\n\t\tlet out = Array(strings.length);\n\t\tfor (let i = 0; i < strings.length; i++)\n\t\t\tout[i] = strings[i].replace(accentsRe, replacer);\n\t\treturn out;\n\t};\n})();\n\n// https://stackoverflow.com/questions/9960908/permutations-in-javascript/37580979#37580979\nfunction permute(arr) {\n\tarr = arr.slice();\n\n\tlet length = arr.length,\n\t\tresult = [arr.slice()],\n\t\tc = new Array(length).fill(0),\n\t\ti = 1, k, p;\n\n\twhile (i < length) {\n\t\tif (c[i] < i) {\n\t\t\tk = i % 2 && c[i];\n\t\t\tp = arr[i];\n\t\t\tarr[i] = arr[k];\n\t\t\tarr[k] = p;\n\t\t\t++c[i];\n\t\t\ti = 1;\n\t\t\tresult.push(arr.slice());\n\t\t} else {\n\t\t\tc[i] = 0;\n\t\t\t++i;\n\t\t}\n\t}\n\n\treturn result;\n}\n\nconst _mark = (part, matched) => matched ? `<mark>${part}</mark>` : part;\nconst _append = (acc, part) => acc + part;\n\nfunction highlight(str, ranges, mark = _mark, accum = '', append = _append) {\n\taccum = append(accum, mark(str.substring(0, ranges[0]), false)) ?? accum;\n\n\tfor (let i = 0; i < ranges.length; i+=2) {\n\t\tlet fr = ranges[i];\n\t\tlet to = ranges[i+1];\n\n\t\taccum = append(accum, mark(str.substring(fr, to), true)) ?? accum;\n\n\t\tif (i < ranges.length - 3)\n\t\t\taccum = append(accum, mark(str.substring(ranges[i+1], ranges[i+2]), false)) ?? accum;\n\t}\n\n\taccum = append(accum, mark(str.substring(ranges[ranges.length - 1]), false)) ?? accum;\n\n\treturn accum;\n}\n\nuFuzzy.latinize = latinize;\nuFuzzy.permute = arr => {\n\tlet idxs = permute([...Array(arr.length).keys()]).sort((a,b) => {\n\t\tfor (let i = 0; i < a.length; i++) {\n\t\t\tif (a[i] != b[i])\n\t\t\t\treturn a[i] - b[i];\n\t\t}\n\t\treturn 0;\n\t});\n\n\treturn idxs.map(pi => pi.map(i => arr[i]));\n};\nuFuzzy.highlight = highlight;\n\nexport { uFuzzy as default };\n", "export var noop = function () { };\nexport function on(obj) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    if (obj && obj.addEventListener) {\n        obj.addEventListener.apply(obj, args);\n    }\n}\nexport function off(obj) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    if (obj && obj.removeEventListener) {\n        obj.removeEventListener.apply(obj, args);\n    }\n}\nexport var isBrowser = typeof window !== 'undefined';\nexport var isNavigator = typeof navigator !== 'undefined';\n", "import { useEffect, useLayoutEffect } from 'react';\nimport { isBrowser } from './misc/util';\nvar useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;\n", "import { useMemo, useState } from 'react';\nimport useIsomorphicLayoutEffect from './useIsomorphicLayoutEffect';\nimport { isBrowser, noop } from './misc/util';\nvar defaultState = {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n};\nfunction useMeasure() {\n    var _a = useState(null), element = _a[0], ref = _a[1];\n    var _b = useState(defaultState), rect = _b[0], setRect = _b[1];\n    var observer = useMemo(function () {\n        return new window.ResizeObserver(function (entries) {\n            if (entries[0]) {\n                var _a = entries[0].contentRect, x = _a.x, y = _a.y, width = _a.width, height = _a.height, top_1 = _a.top, left = _a.left, bottom = _a.bottom, right = _a.right;\n                setRect({ x: x, y: y, width: width, height: height, top: top_1, left: left, bottom: bottom, right: right });\n            }\n        });\n    }, []);\n    useIsomorphicLayoutEffect(function () {\n        if (!element)\n            return;\n        observer.observe(element);\n        return function () {\n            observer.disconnect();\n        };\n    }, [element]);\n    return [ref, rect];\n}\nexport default isBrowser && typeof window.ResizeObserver !== 'undefined'\n    ? useMeasure\n    : (function () { return [noop, defaultState]; });\n", "const PIXELS_PER_LEVEL = 22 * window.devicePixelRatio;\nconst MUTE_THRESHOLD = 10 * window.devicePixelRatio;\nconst HIDE_THRESHOLD = 0.5 * window.devicePixelRatio;\nconst LABEL_THRESHOLD = 20 * window.devicePixelRatio;\nconst BAR_BORDER_WIDTH = 0.5 * window.devicePixelRatio;\nconst BAR_TEXT_PADDING_LEFT = 4 * window.devicePixelRatio;\nconst GROUP_STRIP_WIDTH = 3 * window.devicePixelRatio;\nconst GROUP_STRIP_PADDING = 3 * window.devicePixelRatio;\nconst GROUP_STRIP_MARGIN_LEFT = 4 * window.devicePixelRatio;\nconst GROUP_TEXT_OFFSET = 2 * window.devicePixelRatio;\nconst MIN_WIDTH_TO_SHOW_BOTH_TOPTABLE_AND_FLAMEGRAPH = 800;\nconst TOP_TABLE_COLUMN_WIDTH = 120;\n\nexport { BAR_BORDER_WIDTH, BAR_TEXT_PADDING_LEFT, GROUP_STRIP_MARGIN_LEFT, GROUP_STRIP_PADDING, GROUP_STRIP_WIDTH, GROUP_TEXT_OFFSET, HIDE_THRESHOLD, LABEL_THRESHOLD, MIN_WIDTH_TO_SHOW_BOTH_TOPTABLE_AND_FLAMEGRAPH, MUTE_THRESHOLD, PIXELS_PER_LEVEL, TOP_TABLE_COLUMN_WIDTH };\n//# sourceMappingURL=constants.js.map\n", "import { jsx, jsxs, Fragment } from 'react/jsx-runtime';\nimport { ContextMenu, MenuItem, MenuGroup } from '@grafana/ui';\n\nconst FlameGraphContextMenu = ({\n  data,\n  itemData,\n  onMenuItemClick,\n  onItemFocus,\n  onSandwich,\n  collapseConfig,\n  onExpandGroup,\n  onCollapseGroup,\n  onExpandAllGroups,\n  onCollapseAllGroups,\n  getExtraContextMenuButtons,\n  collapsing,\n  allGroupsExpanded,\n  allGroupsCollapsed,\n  selectedView,\n  search\n}) => {\n  function renderItems() {\n    const extraButtons = (getExtraContextMenuButtons == null ? void 0 : getExtraContextMenuButtons(itemData, data.data, {\n      selectedView,\n      isDiff: data.isDiffFlamegraph(),\n      search,\n      collapseConfig\n    })) || [];\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        MenuItem,\n        {\n          label: \"Focus block\",\n          icon: \"eye\",\n          onClick: () => {\n            onItemFocus();\n            onMenuItemClick();\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        MenuItem,\n        {\n          label: \"Copy function name\",\n          icon: \"copy\",\n          onClick: () => {\n            navigator.clipboard.writeText(itemData.label).then(() => {\n              onMenuItemClick();\n            });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        MenuItem,\n        {\n          label: \"Sandwich view\",\n          icon: \"gf-show-context\",\n          onClick: () => {\n            onSandwich();\n            onMenuItemClick();\n          }\n        }\n      ),\n      extraButtons.map(({ label, icon, onClick }) => {\n        return /* @__PURE__ */ jsx(MenuItem, { label, icon, onClick: () => onClick() }, label);\n      }),\n      collapsing && /* @__PURE__ */ jsxs(MenuGroup, { label: \"Grouping\", children: [\n        collapseConfig ? collapseConfig.collapsed ? /* @__PURE__ */ jsx(\n          MenuItem,\n          {\n            label: \"Expand group\",\n            icon: \"angle-double-down\",\n            onClick: () => {\n              onExpandGroup();\n              onMenuItemClick();\n            }\n          }\n        ) : /* @__PURE__ */ jsx(\n          MenuItem,\n          {\n            label: \"Collapse group\",\n            icon: \"angle-double-up\",\n            onClick: () => {\n              onCollapseGroup();\n              onMenuItemClick();\n            }\n          }\n        ) : null,\n        !allGroupsExpanded && /* @__PURE__ */ jsx(\n          MenuItem,\n          {\n            label: \"Expand all groups\",\n            icon: \"angle-double-down\",\n            onClick: () => {\n              onExpandAllGroups();\n              onMenuItemClick();\n            }\n          }\n        ),\n        !allGroupsCollapsed && /* @__PURE__ */ jsx(\n          MenuItem,\n          {\n            label: \"Collapse all groups\",\n            icon: \"angle-double-up\",\n            onClick: () => {\n              onCollapseAllGroups();\n              onMenuItemClick();\n            }\n          }\n        )\n      ] })\n    ] });\n  }\n  return /* @__PURE__ */ jsx(\"div\", { \"data-testid\": \"contextMenu\", children: /* @__PURE__ */ jsx(\n    ContextMenu,\n    {\n      renderMenuItems: renderItems,\n      x: itemData.posX + 10,\n      y: itemData.posY,\n      focusOnOpen: false\n    }\n  ) });\n};\n\nexport { FlameGraphContextMenu as default };\n//# sourceMappingURL=FlameGraphContextMenu.js.map\n", "import { jsx, jsxs } from 'react/jsx-runtime';\nimport { css } from '@emotion/css';\nimport { getValueFormat } from '@grafana/data';\nimport { useStyles2, InteractiveTable, Portal, VizTooltipContainer } from '@grafana/ui';\n\nconst FlameGraphTooltip = ({ data, item, totalTicks, position, collapseConfig }) => {\n  const styles = useStyles2(getStyles);\n  if (!(item && position)) {\n    return null;\n  }\n  let content;\n  if (data.isDiffFlamegraph()) {\n    const tableData = getDiffTooltipData(data, item, totalTicks);\n    content = /* @__PURE__ */ jsx(\n      InteractiveTable,\n      {\n        className: styles.tooltipTable,\n        columns: [\n          { id: \"label\", header: \"\" },\n          { id: \"baseline\", header: \"Baseline\" },\n          { id: \"comparison\", header: \"Comparison\" },\n          { id: \"diff\", header: \"Diff\" }\n        ],\n        data: tableData,\n        getRowId: (originalRow) => originalRow.rowId\n      }\n    );\n  } else {\n    const tooltipData = getTooltipData(data, item, totalTicks);\n    content = /* @__PURE__ */ jsxs(\"p\", { className: styles.lastParagraph, children: [\n      tooltipData.unitTitle,\n      /* @__PURE__ */ jsx(\"br\", {}),\n      \"Total: \",\n      /* @__PURE__ */ jsx(\"b\", { children: tooltipData.unitValue }),\n      \" (\",\n      tooltipData.percentValue,\n      \"%)\",\n      /* @__PURE__ */ jsx(\"br\", {}),\n      \"Self: \",\n      /* @__PURE__ */ jsx(\"b\", { children: tooltipData.unitSelf }),\n      \" (\",\n      tooltipData.percentSelf,\n      \"%)\",\n      /* @__PURE__ */ jsx(\"br\", {}),\n      \"Samples: \",\n      /* @__PURE__ */ jsx(\"b\", { children: tooltipData.samples })\n    ] });\n  }\n  return /* @__PURE__ */ jsx(Portal, { children: /* @__PURE__ */ jsx(VizTooltipContainer, { className: styles.tooltipContainer, position, offset: { x: 15, y: 0 }, children: /* @__PURE__ */ jsxs(\"div\", { className: styles.tooltipContent, children: [\n    /* @__PURE__ */ jsxs(\"p\", { className: styles.tooltipName, children: [\n      data.getLabel(item.itemIndexes[0]),\n      collapseConfig && collapseConfig.collapsed ? /* @__PURE__ */ jsxs(\"span\", { children: [\n        /* @__PURE__ */ jsx(\"br\", {}),\n        \"and \",\n        collapseConfig.items.length,\n        \" similar items\"\n      ] }) : \"\"\n    ] }),\n    content\n  ] }) }) });\n};\nconst getTooltipData = (data, item, totalTicks) => {\n  const displayValue = data.valueDisplayProcessor(item.value);\n  const displaySelf = data.getSelfDisplay(item.itemIndexes);\n  const percentValue = Math.round(1e4 * (displayValue.numeric / totalTicks)) / 100;\n  const percentSelf = Math.round(1e4 * (displaySelf.numeric / totalTicks)) / 100;\n  let unitValue = displayValue.text + displayValue.suffix;\n  let unitSelf = displaySelf.text + displaySelf.suffix;\n  const unitTitle = data.getUnitTitle();\n  if (unitTitle === \"Count\") {\n    if (!displayValue.suffix) {\n      unitValue = displayValue.text;\n    }\n    if (!displaySelf.suffix) {\n      unitSelf = displaySelf.text;\n    }\n  }\n  return {\n    percentValue,\n    percentSelf,\n    unitTitle,\n    unitValue,\n    unitSelf,\n    samples: displayValue.numeric.toLocaleString()\n  };\n};\nconst getDiffTooltipData = (data, item, totalTicks) => {\n  const levels = data.getLevels();\n  const totalTicksRight = levels[0][0].valueRight;\n  const totalTicksLeft = totalTicks - totalTicksRight;\n  const valueLeft = item.value - item.valueRight;\n  const percentageLeft = Math.round(1e4 * valueLeft / totalTicksLeft) / 100;\n  const percentageRight = Math.round(1e4 * item.valueRight / totalTicksRight) / 100;\n  const diff = (percentageRight - percentageLeft) / percentageLeft * 100;\n  const displayValueLeft = getValueWithUnit(data, data.valueDisplayProcessor(valueLeft));\n  const displayValueRight = getValueWithUnit(data, data.valueDisplayProcessor(item.valueRight));\n  const shortValFormat = getValueFormat(\"short\");\n  return [\n    {\n      rowId: \"1\",\n      label: \"% of total\",\n      baseline: percentageLeft + \"%\",\n      comparison: percentageRight + \"%\",\n      diff: shortValFormat(diff).text + \"%\"\n    },\n    {\n      rowId: \"2\",\n      label: \"Value\",\n      baseline: displayValueLeft,\n      comparison: displayValueRight,\n      diff: getValueWithUnit(data, data.valueDisplayProcessor(item.valueRight - valueLeft))\n    },\n    {\n      rowId: \"3\",\n      label: \"Samples\",\n      baseline: shortValFormat(valueLeft).text,\n      comparison: shortValFormat(item.valueRight).text,\n      diff: shortValFormat(item.valueRight - valueLeft).text\n    }\n  ];\n};\nfunction getValueWithUnit(data, displayValue) {\n  let unitValue = displayValue.text + displayValue.suffix;\n  const unitTitle = data.getUnitTitle();\n  if (unitTitle === \"Count\") {\n    if (!displayValue.suffix) {\n      unitValue = displayValue.text;\n    }\n  }\n  return unitValue;\n}\nconst getStyles = (theme) => ({\n  tooltipContainer: css({\n    title: \"tooltipContainer\",\n    overflow: \"hidden\"\n  }),\n  tooltipContent: css({\n    title: \"tooltipContent\",\n    fontSize: theme.typography.bodySmall.fontSize,\n    width: \"100%\"\n  }),\n  tooltipName: css({\n    title: \"tooltipName\",\n    marginTop: 0,\n    wordBreak: \"break-all\"\n  }),\n  lastParagraph: css({\n    title: \"lastParagraph\",\n    marginBottom: 0\n  }),\n  name: css({\n    title: \"name\",\n    marginBottom: \"10px\"\n  }),\n  tooltipTable: css({\n    title: \"tooltipTable\",\n    maxWidth: \"400px\"\n  })\n});\n\nexport { FlameGraphTooltip as default, getDiffTooltipData, getTooltipData };\n//# sourceMappingURL=FlameGraphTooltip.js.map\n", "// This file is autogenerated. It's used to publish ESM to npm.\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\n// https://github.com/bgrins/TinyColor\n// <PERSON>, MIT License\n\nvar trimLeft = /^\\s+/;\nvar trimRight = /\\s+$/;\nfunction tinycolor(color, opts) {\n  color = color ? color : \"\";\n  opts = opts || {};\n\n  // If input is already a tinycolor, return itself\n  if (color instanceof tinycolor) {\n    return color;\n  }\n  // If we are called as a function, call using new instead\n  if (!(this instanceof tinycolor)) {\n    return new tinycolor(color, opts);\n  }\n  var rgb = inputToRGB(color);\n  this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;\n  this._gradientType = opts.gradientType;\n\n  // Don't let the range of [0,255] come back in [0,1].\n  // Potentially lose a little bit of precision here, but will fix issues where\n  // .5 gets interpreted as half of the total, instead of half of 1\n  // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n  if (this._r < 1) this._r = Math.round(this._r);\n  if (this._g < 1) this._g = Math.round(this._g);\n  if (this._b < 1) this._b = Math.round(this._b);\n  this._ok = rgb.ok;\n}\ntinycolor.prototype = {\n  isDark: function isDark() {\n    return this.getBrightness() < 128;\n  },\n  isLight: function isLight() {\n    return !this.isDark();\n  },\n  isValid: function isValid() {\n    return this._ok;\n  },\n  getOriginalInput: function getOriginalInput() {\n    return this._originalInput;\n  },\n  getFormat: function getFormat() {\n    return this._format;\n  },\n  getAlpha: function getAlpha() {\n    return this._a;\n  },\n  getBrightness: function getBrightness() {\n    //http://www.w3.org/TR/AERT#color-contrast\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  },\n  getLuminance: function getLuminance() {\n    //http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    var rgb = this.toRgb();\n    var RsRGB, GsRGB, BsRGB, R, G, B;\n    RsRGB = rgb.r / 255;\n    GsRGB = rgb.g / 255;\n    BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) R = RsRGB / 12.92;else R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    if (GsRGB <= 0.03928) G = GsRGB / 12.92;else G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    if (BsRGB <= 0.03928) B = BsRGB / 12.92;else B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  },\n  setAlpha: function setAlpha(value) {\n    this._a = boundAlpha(value);\n    this._roundA = Math.round(100 * this._a) / 100;\n    return this;\n  },\n  toHsv: function toHsv() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this._a\n    };\n  },\n  toHsvString: function toHsvString() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    var h = Math.round(hsv.h * 360),\n      s = Math.round(hsv.s * 100),\n      v = Math.round(hsv.v * 100);\n    return this._a == 1 ? \"hsv(\" + h + \", \" + s + \"%, \" + v + \"%)\" : \"hsva(\" + h + \", \" + s + \"%, \" + v + \"%, \" + this._roundA + \")\";\n  },\n  toHsl: function toHsl() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this._a\n    };\n  },\n  toHslString: function toHslString() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    var h = Math.round(hsl.h * 360),\n      s = Math.round(hsl.s * 100),\n      l = Math.round(hsl.l * 100);\n    return this._a == 1 ? \"hsl(\" + h + \", \" + s + \"%, \" + l + \"%)\" : \"hsla(\" + h + \", \" + s + \"%, \" + l + \"%, \" + this._roundA + \")\";\n  },\n  toHex: function toHex(allow3Char) {\n    return rgbToHex(this._r, this._g, this._b, allow3Char);\n  },\n  toHexString: function toHexString(allow3Char) {\n    return \"#\" + this.toHex(allow3Char);\n  },\n  toHex8: function toHex8(allow4Char) {\n    return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);\n  },\n  toHex8String: function toHex8String(allow4Char) {\n    return \"#\" + this.toHex8(allow4Char);\n  },\n  toRgb: function toRgb() {\n    return {\n      r: Math.round(this._r),\n      g: Math.round(this._g),\n      b: Math.round(this._b),\n      a: this._a\n    };\n  },\n  toRgbString: function toRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \")\" : \"rgba(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \", \" + this._roundA + \")\";\n  },\n  toPercentageRgb: function toPercentageRgb() {\n    return {\n      r: Math.round(bound01(this._r, 255) * 100) + \"%\",\n      g: Math.round(bound01(this._g, 255) * 100) + \"%\",\n      b: Math.round(bound01(this._b, 255) * 100) + \"%\",\n      a: this._a\n    };\n  },\n  toPercentageRgbString: function toPercentageRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%)\" : \"rgba(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%, \" + this._roundA + \")\";\n  },\n  toName: function toName() {\n    if (this._a === 0) {\n      return \"transparent\";\n    }\n    if (this._a < 1) {\n      return false;\n    }\n    return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;\n  },\n  toFilter: function toFilter(secondColor) {\n    var hex8String = \"#\" + rgbaToArgbHex(this._r, this._g, this._b, this._a);\n    var secondHex8String = hex8String;\n    var gradientType = this._gradientType ? \"GradientType = 1, \" : \"\";\n    if (secondColor) {\n      var s = tinycolor(secondColor);\n      secondHex8String = \"#\" + rgbaToArgbHex(s._r, s._g, s._b, s._a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\" + gradientType + \"startColorstr=\" + hex8String + \",endColorstr=\" + secondHex8String + \")\";\n  },\n  toString: function toString(format) {\n    var formatSet = !!format;\n    format = format || this._format;\n    var formattedString = false;\n    var hasAlpha = this._a < 1 && this._a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format === \"hex\" || format === \"hex6\" || format === \"hex3\" || format === \"hex4\" || format === \"hex8\" || format === \"name\");\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === \"name\" && this._a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === \"rgb\") {\n      formattedString = this.toRgbString();\n    }\n    if (format === \"prgb\") {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === \"hex\" || format === \"hex6\") {\n      formattedString = this.toHexString();\n    }\n    if (format === \"hex3\") {\n      formattedString = this.toHexString(true);\n    }\n    if (format === \"hex4\") {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === \"hex8\") {\n      formattedString = this.toHex8String();\n    }\n    if (format === \"name\") {\n      formattedString = this.toName();\n    }\n    if (format === \"hsl\") {\n      formattedString = this.toHslString();\n    }\n    if (format === \"hsv\") {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  },\n  clone: function clone() {\n    return tinycolor(this.toString());\n  },\n  _applyModification: function _applyModification(fn, args) {\n    var color = fn.apply(null, [this].concat([].slice.call(args)));\n    this._r = color._r;\n    this._g = color._g;\n    this._b = color._b;\n    this.setAlpha(color._a);\n    return this;\n  },\n  lighten: function lighten() {\n    return this._applyModification(_lighten, arguments);\n  },\n  brighten: function brighten() {\n    return this._applyModification(_brighten, arguments);\n  },\n  darken: function darken() {\n    return this._applyModification(_darken, arguments);\n  },\n  desaturate: function desaturate() {\n    return this._applyModification(_desaturate, arguments);\n  },\n  saturate: function saturate() {\n    return this._applyModification(_saturate, arguments);\n  },\n  greyscale: function greyscale() {\n    return this._applyModification(_greyscale, arguments);\n  },\n  spin: function spin() {\n    return this._applyModification(_spin, arguments);\n  },\n  _applyCombination: function _applyCombination(fn, args) {\n    return fn.apply(null, [this].concat([].slice.call(args)));\n  },\n  analogous: function analogous() {\n    return this._applyCombination(_analogous, arguments);\n  },\n  complement: function complement() {\n    return this._applyCombination(_complement, arguments);\n  },\n  monochromatic: function monochromatic() {\n    return this._applyCombination(_monochromatic, arguments);\n  },\n  splitcomplement: function splitcomplement() {\n    return this._applyCombination(_splitcomplement, arguments);\n  },\n  // Disabled until https://github.com/bgrins/TinyColor/issues/254\n  // polyad: function (number) {\n  //   return this._applyCombination(polyad, [number]);\n  // },\n  triad: function triad() {\n    return this._applyCombination(polyad, [3]);\n  },\n  tetrad: function tetrad() {\n    return this._applyCombination(polyad, [4]);\n  }\n};\n\n// If input is an object, force 1 into \"1.0\" to handle ratios properly\n// String input requires \"1.0\" as input, so 1 will be treated as 1\ntinycolor.fromRatio = function (color, opts) {\n  if (_typeof(color) == \"object\") {\n    var newColor = {};\n    for (var i in color) {\n      if (color.hasOwnProperty(i)) {\n        if (i === \"a\") {\n          newColor[i] = color[i];\n        } else {\n          newColor[i] = convertToPercentage(color[i]);\n        }\n      }\n    }\n    color = newColor;\n  }\n  return tinycolor(color, opts);\n};\n\n// Given a string or object, convert that input to RGB\n// Possible string inputs:\n//\n//     \"red\"\n//     \"#f00\" or \"f00\"\n//     \"#ff0000\" or \"ff0000\"\n//     \"#ff000000\" or \"ff000000\"\n//     \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n//     \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n//     \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n//     \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n//     \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n//     \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n//     \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n//\nfunction inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color == \"string\") {\n    color = stringInputToObject(color);\n  }\n  if (_typeof(color) == \"object\") {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = \"hsv\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = \"hsl\";\n    }\n    if (color.hasOwnProperty(\"a\")) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n\n// Conversion Functions\n// --------------------\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n\n// `rgbToRgb`\n// Handle bounds / percentage checking to conform to CSS color spec\n// <http://www.w3.org/TR/css3-color/>\n// *Assumes:* r, g, b in [0, 255] or [0, 1]\n// *Returns:* { r, g, b } in [0, 255]\nfunction rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\n\n// `rgbToHsl`\n// Converts an RGB color value to HSL.\n// *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n// *Returns:* { h, s, l } in [0,1]\nfunction rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    l = (max + min) / 2;\n  if (max == min) {\n    h = s = 0; // achromatic\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    l: l\n  };\n}\n\n// `hslToRgb`\n// Converts an HSL color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hslToRgb(h, s, l) {\n  var r, g, b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  function hue2rgb(p, q, t) {\n    if (t < 0) t += 1;\n    if (t > 1) t -= 1;\n    if (t < 1 / 6) return p + (q - p) * 6 * t;\n    if (t < 1 / 2) return q;\n    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n  }\n  if (s === 0) {\n    r = g = b = l; // achromatic\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHsv`\n// Converts an RGB color value to HSV\n// *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n// *Returns:* { h, s, v } in [0,1]\nfunction rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    v = max;\n  var d = max - min;\n  s = max === 0 ? 0 : d / max;\n  if (max == min) {\n    h = 0; // achromatic\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    v: v\n  };\n}\n\n// `hsvToRgb`\n// Converts an HSV color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  var i = Math.floor(h),\n    f = h - i,\n    p = v * (1 - s),\n    q = v * (1 - f * s),\n    t = v * (1 - (1 - f) * s),\n    mod = i % 6,\n    r = [v, q, p, p, t, v][mod],\n    g = [t, v, v, q, p, p][mod],\n    b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHex`\n// Converts an RGB color to hex\n// Assumes r, g, and b are contained in the set [0, 255]\n// Returns a 3 or 6 character hex\nfunction rgbToHex(r, g, b, allow3Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n\n  // Return a 3 character hex if possible\n  if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToHex`\n// Converts an RGBA color plus alpha transparency to hex\n// Assumes r, g, b are contained in the set [0, 255] and\n// a in [0, 1]. Returns a 4 or 8 character rgba hex\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n\n  // Return a 4 character hex if possible\n  if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToArgbHex`\n// Converts an RGBA color to an ARGB Hex8 string\n// Rarely used, but required for \"toFilter()\"\nfunction rgbaToArgbHex(r, g, b, a) {\n  var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join(\"\");\n}\n\n// `equals`\n// Can be called with any tinycolor input\ntinycolor.equals = function (color1, color2) {\n  if (!color1 || !color2) return false;\n  return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();\n};\ntinycolor.random = function () {\n  return tinycolor.fromRatio({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n};\n\n// Modification Functions\n// ----------------------\n// Thanks to less.js for some of the basics here\n// <https://github.com/cloudhead/less.js/blob/master/lib/less/functions.js>\n\nfunction _desaturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s -= amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _saturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s += amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _greyscale(color) {\n  return tinycolor(color).desaturate(100);\n}\nfunction _lighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l += amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\nfunction _brighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var rgb = tinycolor(color).toRgb();\n  rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n  rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n  rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n  return tinycolor(rgb);\n}\nfunction _darken(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l -= amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\n\n// Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n// Values outside of this range will be wrapped into this range.\nfunction _spin(color, amount) {\n  var hsl = tinycolor(color).toHsl();\n  var hue = (hsl.h + amount) % 360;\n  hsl.h = hue < 0 ? 360 + hue : hue;\n  return tinycolor(hsl);\n}\n\n// Combination Functions\n// ---------------------\n// Thanks to jQuery xColor for some of the ideas behind these\n// <https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js>\n\nfunction _complement(color) {\n  var hsl = tinycolor(color).toHsl();\n  hsl.h = (hsl.h + 180) % 360;\n  return tinycolor(hsl);\n}\nfunction polyad(color, number) {\n  if (isNaN(number) || number <= 0) {\n    throw new Error(\"Argument to polyad must be a positive number\");\n  }\n  var hsl = tinycolor(color).toHsl();\n  var result = [tinycolor(color)];\n  var step = 360 / number;\n  for (var i = 1; i < number; i++) {\n    result.push(tinycolor({\n      h: (hsl.h + i * step) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }));\n  }\n  return result;\n}\nfunction _splitcomplement(color) {\n  var hsl = tinycolor(color).toHsl();\n  var h = hsl.h;\n  return [tinycolor(color), tinycolor({\n    h: (h + 72) % 360,\n    s: hsl.s,\n    l: hsl.l\n  }), tinycolor({\n    h: (h + 216) % 360,\n    s: hsl.s,\n    l: hsl.l\n  })];\n}\nfunction _analogous(color, results, slices) {\n  results = results || 6;\n  slices = slices || 30;\n  var hsl = tinycolor(color).toHsl();\n  var part = 360 / slices;\n  var ret = [tinycolor(color)];\n  for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n    hsl.h = (hsl.h + part) % 360;\n    ret.push(tinycolor(hsl));\n  }\n  return ret;\n}\nfunction _monochromatic(color, results) {\n  results = results || 6;\n  var hsv = tinycolor(color).toHsv();\n  var h = hsv.h,\n    s = hsv.s,\n    v = hsv.v;\n  var ret = [];\n  var modification = 1 / results;\n  while (results--) {\n    ret.push(tinycolor({\n      h: h,\n      s: s,\n      v: v\n    }));\n    v = (v + modification) % 1;\n  }\n  return ret;\n}\n\n// Utility Functions\n// ---------------------\n\ntinycolor.mix = function (color1, color2, amount) {\n  amount = amount === 0 ? 0 : amount || 50;\n  var rgb1 = tinycolor(color1).toRgb();\n  var rgb2 = tinycolor(color2).toRgb();\n  var p = amount / 100;\n  var rgba = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b,\n    a: (rgb2.a - rgb1.a) * p + rgb1.a\n  };\n  return tinycolor(rgba);\n};\n\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n\n// `contrast`\n// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\ntinycolor.readability = function (color1, color2) {\n  var c1 = tinycolor(color1);\n  var c2 = tinycolor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n};\n\n// `isReadable`\n// Ensure that foreground and background color combinations meet WCAG2 guidelines.\n// The third argument is an optional Object.\n//      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n//      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n// If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n\n// *Example*\n//    tinycolor.isReadable(\"#000\", \"#111\") => false\n//    tinycolor.isReadable(\"#000\", \"#111\",{level:\"AA\",size:\"large\"}) => false\ntinycolor.isReadable = function (color1, color2, wcag2) {\n  var readability = tinycolor.readability(color1, color2);\n  var wcag2Parms, out;\n  out = false;\n  wcag2Parms = validateWCAG2Parms(wcag2);\n  switch (wcag2Parms.level + wcag2Parms.size) {\n    case \"AAsmall\":\n    case \"AAAlarge\":\n      out = readability >= 4.5;\n      break;\n    case \"AAlarge\":\n      out = readability >= 3;\n      break;\n    case \"AAAsmall\":\n      out = readability >= 7;\n      break;\n  }\n  return out;\n};\n\n// `mostReadable`\n// Given a base color and a list of possible foreground or background\n// colors for that base, returns the most readable color.\n// Optionally returns Black or White if the most readable color is unreadable.\n// *Example*\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:false}).toHexString(); // \"#112255\"\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:true}).toHexString();  // \"#ffffff\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"large\"}).toHexString(); // \"#faf3f3\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"small\"}).toHexString(); // \"#ffffff\"\ntinycolor.mostReadable = function (baseColor, colorList, args) {\n  var bestColor = null;\n  var bestScore = 0;\n  var readability;\n  var includeFallbackColors, level, size;\n  args = args || {};\n  includeFallbackColors = args.includeFallbackColors;\n  level = args.level;\n  size = args.size;\n  for (var i = 0; i < colorList.length; i++) {\n    readability = tinycolor.readability(baseColor, colorList[i]);\n    if (readability > bestScore) {\n      bestScore = readability;\n      bestColor = tinycolor(colorList[i]);\n    }\n  }\n  if (tinycolor.isReadable(baseColor, bestColor, {\n    level: level,\n    size: size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  } else {\n    args.includeFallbackColors = false;\n    return tinycolor.mostReadable(baseColor, [\"#fff\", \"#000\"], args);\n  }\n};\n\n// Big List of Colors\n// ------------------\n// <https://www.w3.org/TR/css-color-4/#named-colors>\nvar names = tinycolor.names = {\n  aliceblue: \"f0f8ff\",\n  antiquewhite: \"faebd7\",\n  aqua: \"0ff\",\n  aquamarine: \"7fffd4\",\n  azure: \"f0ffff\",\n  beige: \"f5f5dc\",\n  bisque: \"ffe4c4\",\n  black: \"000\",\n  blanchedalmond: \"ffebcd\",\n  blue: \"00f\",\n  blueviolet: \"8a2be2\",\n  brown: \"a52a2a\",\n  burlywood: \"deb887\",\n  burntsienna: \"ea7e5d\",\n  cadetblue: \"5f9ea0\",\n  chartreuse: \"7fff00\",\n  chocolate: \"d2691e\",\n  coral: \"ff7f50\",\n  cornflowerblue: \"6495ed\",\n  cornsilk: \"fff8dc\",\n  crimson: \"dc143c\",\n  cyan: \"0ff\",\n  darkblue: \"00008b\",\n  darkcyan: \"008b8b\",\n  darkgoldenrod: \"b8860b\",\n  darkgray: \"a9a9a9\",\n  darkgreen: \"006400\",\n  darkgrey: \"a9a9a9\",\n  darkkhaki: \"bdb76b\",\n  darkmagenta: \"8b008b\",\n  darkolivegreen: \"556b2f\",\n  darkorange: \"ff8c00\",\n  darkorchid: \"9932cc\",\n  darkred: \"8b0000\",\n  darksalmon: \"e9967a\",\n  darkseagreen: \"8fbc8f\",\n  darkslateblue: \"483d8b\",\n  darkslategray: \"2f4f4f\",\n  darkslategrey: \"2f4f4f\",\n  darkturquoise: \"00ced1\",\n  darkviolet: \"9400d3\",\n  deeppink: \"ff1493\",\n  deepskyblue: \"00bfff\",\n  dimgray: \"696969\",\n  dimgrey: \"696969\",\n  dodgerblue: \"1e90ff\",\n  firebrick: \"b22222\",\n  floralwhite: \"fffaf0\",\n  forestgreen: \"228b22\",\n  fuchsia: \"f0f\",\n  gainsboro: \"dcdcdc\",\n  ghostwhite: \"f8f8ff\",\n  gold: \"ffd700\",\n  goldenrod: \"daa520\",\n  gray: \"808080\",\n  green: \"008000\",\n  greenyellow: \"adff2f\",\n  grey: \"808080\",\n  honeydew: \"f0fff0\",\n  hotpink: \"ff69b4\",\n  indianred: \"cd5c5c\",\n  indigo: \"4b0082\",\n  ivory: \"fffff0\",\n  khaki: \"f0e68c\",\n  lavender: \"e6e6fa\",\n  lavenderblush: \"fff0f5\",\n  lawngreen: \"7cfc00\",\n  lemonchiffon: \"fffacd\",\n  lightblue: \"add8e6\",\n  lightcoral: \"f08080\",\n  lightcyan: \"e0ffff\",\n  lightgoldenrodyellow: \"fafad2\",\n  lightgray: \"d3d3d3\",\n  lightgreen: \"90ee90\",\n  lightgrey: \"d3d3d3\",\n  lightpink: \"ffb6c1\",\n  lightsalmon: \"ffa07a\",\n  lightseagreen: \"20b2aa\",\n  lightskyblue: \"87cefa\",\n  lightslategray: \"789\",\n  lightslategrey: \"789\",\n  lightsteelblue: \"b0c4de\",\n  lightyellow: \"ffffe0\",\n  lime: \"0f0\",\n  limegreen: \"32cd32\",\n  linen: \"faf0e6\",\n  magenta: \"f0f\",\n  maroon: \"800000\",\n  mediumaquamarine: \"66cdaa\",\n  mediumblue: \"0000cd\",\n  mediumorchid: \"ba55d3\",\n  mediumpurple: \"9370db\",\n  mediumseagreen: \"3cb371\",\n  mediumslateblue: \"7b68ee\",\n  mediumspringgreen: \"00fa9a\",\n  mediumturquoise: \"48d1cc\",\n  mediumvioletred: \"c71585\",\n  midnightblue: \"191970\",\n  mintcream: \"f5fffa\",\n  mistyrose: \"ffe4e1\",\n  moccasin: \"ffe4b5\",\n  navajowhite: \"ffdead\",\n  navy: \"000080\",\n  oldlace: \"fdf5e6\",\n  olive: \"808000\",\n  olivedrab: \"6b8e23\",\n  orange: \"ffa500\",\n  orangered: \"ff4500\",\n  orchid: \"da70d6\",\n  palegoldenrod: \"eee8aa\",\n  palegreen: \"98fb98\",\n  paleturquoise: \"afeeee\",\n  palevioletred: \"db7093\",\n  papayawhip: \"ffefd5\",\n  peachpuff: \"ffdab9\",\n  peru: \"cd853f\",\n  pink: \"ffc0cb\",\n  plum: \"dda0dd\",\n  powderblue: \"b0e0e6\",\n  purple: \"800080\",\n  rebeccapurple: \"663399\",\n  red: \"f00\",\n  rosybrown: \"bc8f8f\",\n  royalblue: \"4169e1\",\n  saddlebrown: \"8b4513\",\n  salmon: \"fa8072\",\n  sandybrown: \"f4a460\",\n  seagreen: \"2e8b57\",\n  seashell: \"fff5ee\",\n  sienna: \"a0522d\",\n  silver: \"c0c0c0\",\n  skyblue: \"87ceeb\",\n  slateblue: \"6a5acd\",\n  slategray: \"708090\",\n  slategrey: \"708090\",\n  snow: \"fffafa\",\n  springgreen: \"00ff7f\",\n  steelblue: \"4682b4\",\n  tan: \"d2b48c\",\n  teal: \"008080\",\n  thistle: \"d8bfd8\",\n  tomato: \"ff6347\",\n  turquoise: \"40e0d0\",\n  violet: \"ee82ee\",\n  wheat: \"f5deb3\",\n  white: \"fff\",\n  whitesmoke: \"f5f5f5\",\n  yellow: \"ff0\",\n  yellowgreen: \"9acd32\"\n};\n\n// Make it easy to access colors via `hexNames[hex]`\nvar hexNames = tinycolor.hexNames = flip(names);\n\n// Utilities\n// ---------\n\n// `{ 'name1': 'val1' }` becomes `{ 'val1': 'name1' }`\nfunction flip(o) {\n  var flipped = {};\n  for (var i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\n\n// Return a valid alpha value [0,1] with all invalid values being set to 1\nfunction boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n\n// Take input from [0, n] and return it as [0, 1]\nfunction bound01(n, max) {\n  if (isOnePointZero(n)) n = \"100%\";\n  var processPercent = isPercentage(n);\n  n = Math.min(max, Math.max(0, parseFloat(n)));\n\n  // Automatically convert percentage into number\n  if (processPercent) {\n    n = parseInt(n * max, 10) / 100;\n  }\n\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n\n  // Convert into [0, 1] range if it isn't already\n  return n % max / parseFloat(max);\n}\n\n// Force a number between 0 and 1\nfunction clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n\n// Parse a base-16 hex value into a base-10 integer\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\n\n// Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n// <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\nfunction isOnePointZero(n) {\n  return typeof n == \"string\" && n.indexOf(\".\") != -1 && parseFloat(n) === 1;\n}\n\n// Check to see if string passed in is a percentage\nfunction isPercentage(n) {\n  return typeof n === \"string\" && n.indexOf(\"%\") != -1;\n}\n\n// Force a hex value to have 2 characters\nfunction pad2(c) {\n  return c.length == 1 ? \"0\" + c : \"\" + c;\n}\n\n// Replace a decimal with it's percentage value\nfunction convertToPercentage(n) {\n  if (n <= 1) {\n    n = n * 100 + \"%\";\n  }\n  return n;\n}\n\n// Converts a decimal to a hex value\nfunction convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\n// Converts a hex value to a decimal\nfunction convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\nvar matchers = function () {\n  // <http://www.w3.org/TR/css3-values/#integers>\n  var CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n\n  // <http://www.w3.org/TR/css3-values/#number-value>\n  var CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n\n  // Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\n  var CSS_UNIT = \"(?:\" + CSS_NUMBER + \")|(?:\" + CSS_INTEGER + \")\";\n\n  // Actual matching.\n  // Parentheses and commas are optional, but not required.\n  // Whitespace can take the place of commas or opening paren\n  var PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  var PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  return {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n    rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n    hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n    hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n    hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n    hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n  };\n}();\n\n// `isValidCSSUnit`\n// Take in a single string / number and check to see if it looks like a CSS unit\n// (see `matchers` above for definition).\nfunction isValidCSSUnit(color) {\n  return !!matchers.CSS_UNIT.exec(color);\n}\n\n// `stringInputToObject`\n// Permissive string parsing.  Take in a number of formats, and output an object\n// based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\nfunction stringInputToObject(color) {\n  color = color.replace(trimLeft, \"\").replace(trimRight, \"\").toLowerCase();\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color == \"transparent\") {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: \"name\"\n    };\n  }\n\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match;\n  if (match = matchers.rgb.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  if (match = matchers.rgba.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsl.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  if (match = matchers.hsla.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsv.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  if (match = matchers.hsva.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hex8.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex6.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  if (match = matchers.hex4.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      a: convertHexToDecimal(match[4] + \"\" + match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex3.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  return false;\n}\nfunction validateWCAG2Parms(parms) {\n  // return valid WCAG2 parms for isReadable.\n  // If input parms are invalid, return {\"level\":\"AA\", \"size\":\"small\"}\n  var level, size;\n  parms = parms || {\n    level: \"AA\",\n    size: \"small\"\n  };\n  level = (parms.level || \"AA\").toUpperCase();\n  size = (parms.size || \"small\").toLowerCase();\n  if (level !== \"AA\" && level !== \"AAA\") {\n    level = \"AA\";\n  }\n  if (size !== \"small\" && size !== \"large\") {\n    size = \"small\";\n  }\n  return {\n    level: level,\n    size: size\n  };\n}\n\nexport { tinycolor as default };\n", "var SampleUnit = /* @__PURE__ */ ((SampleUnit2) => {\n  SampleUnit2[\"Bytes\"] = \"bytes\";\n  SampleUnit2[\"Short\"] = \"short\";\n  SampleUnit2[\"Nanoseconds\"] = \"ns\";\n  return SampleUnit2;\n})(SampleUnit || {});\nvar SelectedView = /* @__PURE__ */ ((SelectedView2) => {\n  SelectedView2[\"TopTable\"] = \"topTable\";\n  SelectedView2[\"FlameGraph\"] = \"flameGraph\";\n  SelectedView2[\"Both\"] = \"both\";\n  return SelectedView2;\n})(SelectedView || {});\nvar ColorScheme = /* @__PURE__ */ ((ColorScheme2) => {\n  ColorScheme2[\"ValueBased\"] = \"valueBased\";\n  ColorScheme2[\"PackageBased\"] = \"packageBased\";\n  return ColorScheme2;\n})(ColorScheme || {});\nvar ColorSchemeDiff = /* @__PURE__ */ ((ColorSchemeDiff2) => {\n  ColorSchemeDiff2[\"Default\"] = \"default\";\n  ColorSchemeDiff2[\"DiffColorBlind\"] = \"diffColorBlind\";\n  return ColorSchemeDiff2;\n})(ColorSchemeDiff || {});\n\nexport { ColorScheme, ColorSchemeDiff, SampleUnit, SelectedView };\n//# sourceMappingURL=types.js.map\n", "import { scaleLinear } from 'd3';\nimport color from 'tinycolor2';\nimport { ColorSchemeDiff } from '../types.js';\nimport murmurhash3_32_gc from './murmur3.js';\n\nconst packageColors = [\n  color({ h: 24, s: 69, l: 60 }),\n  color({ h: 34, s: 65, l: 65 }),\n  color({ h: 194, s: 52, l: 61 }),\n  color({ h: 163, s: 45, l: 55 }),\n  color({ h: 211, s: 48, l: 60 }),\n  color({ h: 246, s: 40, l: 65 }),\n  color({ h: 305, s: 63, l: 79 }),\n  color({ h: 47, s: 100, l: 73 }),\n  color({ r: 183, g: 219, b: 171 }),\n  color({ r: 244, g: 213, b: 152 }),\n  color({ r: 78, g: 146, b: 249 }),\n  color({ r: 249, g: 186, b: 143 }),\n  color({ r: 242, g: 145, b: 145 }),\n  color({ r: 130, g: 181, b: 216 }),\n  color({ r: 229, g: 168, b: 226 }),\n  color({ r: 174, g: 162, b: 224 }),\n  color({ r: 154, g: 196, b: 138 }),\n  color({ r: 242, g: 201, b: 109 }),\n  color({ r: 101, g: 197, b: 219 }),\n  color({ r: 249, g: 147, b: 78 }),\n  color({ r: 234, g: 100, b: 96 }),\n  color({ r: 81, g: 149, b: 206 }),\n  color({ r: 214, g: 131, b: 206 }),\n  color({ r: 128, g: 110, b: 183 })\n];\nconst byValueMinColor = getBarColorByValue(1, 100, 0, 1);\nconst byValueMaxColor = getBarColorByValue(100, 100, 0, 1);\nconst byValueGradient = `linear-gradient(90deg, ${byValueMinColor} 0%, ${byValueMaxColor} 100%)`;\nconst byPackageGradient = `linear-gradient(90deg, ${packageColors[0]} 0%, ${packageColors[2]} 30%, ${packageColors[6]} 50%, ${packageColors[7]} 70%, ${packageColors[8]} 100%)`;\nfunction getBarColorByValue(value, totalTicks, rangeMin, rangeMax) {\n  const intensity = Math.min(1, value / totalTicks / (rangeMax - rangeMin));\n  const h = 50 - 50 * intensity;\n  const l = 65 + 7 * intensity;\n  return color({ h, s: 100, l });\n}\nfunction getBarColorByPackage(label, theme) {\n  const packageName = getPackageName(label);\n  const hash = murmurhash3_32_gc(packageName || \"\", 0);\n  const colorIndex = hash % packageColors.length;\n  let packageColor = packageColors[colorIndex].clone();\n  if (theme.isLight) {\n    packageColor = packageColor.brighten(15);\n  }\n  return packageColor;\n}\nconst diffDefaultColors = [\"rgb(0, 170, 0)\", \"rgb(148, 142, 142)\", \"rgb(200, 0, 0)\"];\nconst diffDefaultGradient = `linear-gradient(90deg, ${diffDefaultColors[0]} 0%, ${diffDefaultColors[1]} 50%, ${diffDefaultColors[2]} 100%)`;\nconst diffColorBlindColors = [\"rgb(26, 133, 255)\", \"rgb(148, 142, 142)\", \"rgb(220, 50, 32)\"];\nconst diffColorBlindGradient = `linear-gradient(90deg, ${diffColorBlindColors[0]} 0%, ${diffColorBlindColors[1]} 50%, ${diffColorBlindColors[2]} 100%)`;\nfunction getBarColorByDiff(ticks, ticksRight, totalTicks, totalTicksRight, colorScheme) {\n  const range = colorScheme === ColorSchemeDiff.Default ? diffDefaultColors : diffColorBlindColors;\n  const colorScale = scaleLinear().domain([-100, 0, 100]).range(range);\n  const ticksLeft = ticks - ticksRight;\n  const totalTicksLeft = totalTicks - totalTicksRight;\n  if (totalTicksRight === 0 || totalTicksLeft === 0) {\n    const rgbString2 = colorScale(0);\n    return color(rgbString2);\n  }\n  const percentageLeft = Math.round(1e4 * ticksLeft / totalTicksLeft) / 100;\n  const percentageRight = Math.round(1e4 * ticksRight / totalTicksRight) / 100;\n  const diff = (percentageRight - percentageLeft) / percentageLeft * 100;\n  const rgbString = colorScale(diff);\n  return color(rgbString);\n}\nconst matchers = [\n  [\"phpspy\", /^(?<packageName>([^\\/]*\\/)*)(?<filename>.*\\.php+)(?<line_info>.*)$/],\n  [\"pyspy\", /^(?<packageName>([^\\/]*\\/)*)(?<filename>.*\\.py+)(?<line_info>.*)$/],\n  [\"rbspy\", /^(?<packageName>([^\\/]*\\/)*)(?<filename>.*\\.rb+)(?<line_info>.*)$/],\n  [\n    \"nodespy\",\n    /^(\\.\\/node_modules\\/)?(?<packageName>[^/]*)(?<filename>.*\\.?(jsx?|tsx?)?):(?<functionName>.*):(?<line_info>.*)$/\n  ],\n  [\"gospy\", /^(?<packageName>.*?\\/.*?\\.|.*?\\.|.+)(?<functionName>.*)$/],\n  // also 'scrape'\n  [\"javaspy\", /^(?<packageName>.+\\/)(?<filename>.+\\.)(?<functionName>.+)$/],\n  [\"dotnetspy\", /^(?<packageName>.+)\\.(.+)\\.(.+)\\(.*\\)$/],\n  [\"tracing\", /^(?<packageName>.+?):.*$/],\n  [\"pyroscope-rs\", /^(?<packageName>[^::]+)/],\n  [\"ebpfspy\", /^(?<packageName>.+)$/],\n  [\"unknown\", /^(?<packageName>.+)$/]\n];\nfunction getPackageName(name) {\n  var _a;\n  for (const [_, matcher] of matchers) {\n    const match = name.match(matcher);\n    if (match) {\n      return ((_a = match.groups) == null ? void 0 : _a.packageName) || \"\";\n    }\n  }\n  return void 0;\n}\n\nexport { byPackageGradient, byValueGradient, diffColorBlindColors, diffColorBlindGradient, diffDefaultColors, diffDefaultGradient, getBarColorByDiff, getBarColorByPackage, getBarColorByValue };\n//# sourceMappingURL=colors.js.map\n", "function murmurhash3_32_gc(key, seed = 0) {\n  let remainder;\n  let bytes;\n  let h1;\n  let h1b;\n  let c1;\n  let c2;\n  let k1;\n  let i;\n  remainder = key.length & 3;\n  bytes = key.length - remainder;\n  h1 = seed;\n  c1 = 3432918353;\n  c2 = 461845907;\n  i = 0;\n  while (i < bytes) {\n    k1 = key.charCodeAt(i) & 255 | (key.charCodeAt(++i) & 255) << 8 | (key.charCodeAt(++i) & 255) << 16 | (key.charCodeAt(++i) & 255) << 24;\n    ++i;\n    k1 = (k1 & 65535) * c1 + (((k1 >>> 16) * c1 & 65535) << 16) & 4294967295;\n    k1 = k1 << 15 | k1 >>> 17;\n    k1 = (k1 & 65535) * c2 + (((k1 >>> 16) * c2 & 65535) << 16) & 4294967295;\n    h1 ^= k1;\n    h1 = h1 << 13 | h1 >>> 19;\n    h1b = (h1 & 65535) * 5 + (((h1 >>> 16) * 5 & 65535) << 16) & 4294967295;\n    h1 = (h1b & 65535) + 27492 + (((h1b >>> 16) + 58964 & 65535) << 16);\n  }\n  k1 = 0;\n  switch (remainder) {\n    case 3:\n      k1 ^= (key.charCodeAt(i + 2) & 255) << 16;\n    // fall through\n    case 2:\n      k1 ^= (key.charCodeAt(i + 1) & 255) << 8;\n    // fall through\n    case 1:\n      k1 ^= key.charCodeAt(i) & 255;\n    // fall through\n    default:\n      k1 = (k1 & 65535) * c1 + (((k1 >>> 16) * c1 & 65535) << 16) & 4294967295;\n      k1 = k1 << 15 | k1 >>> 17;\n      k1 = (k1 & 65535) * c2 + (((k1 >>> 16) * c2 & 65535) << 16) & 4294967295;\n      h1 ^= k1;\n  }\n  h1 ^= key.length;\n  h1 ^= h1 >>> 16;\n  h1 = (h1 & 65535) * 2246822507 + (((h1 >>> 16) * 2246822507 & 65535) << 16) & 4294967295;\n  h1 ^= h1 >>> 13;\n  h1 = (h1 & 65535) * 3266489909 + (((h1 >>> 16) * 3266489909 & 65535) << 16) & 4294967295;\n  h1 ^= h1 >>> 16;\n  return h1 >>> 0;\n}\n\nexport { murmurhash3_32_gc as default };\n//# sourceMappingURL=murmur3.js.map\n", "import { useMemo, useEffect, useCallback, useState } from 'react';\nimport color from 'tinycolor2';\nimport { useTheme2 } from '@grafana/ui';\nimport { HIDE_THRESHOLD, PIXELS_PER_LEVEL, BAR_BORDER_WIDTH, LABEL_THRESHOLD, GROUP_STRIP_WIDTH, GROUP_STRIP_PADDING, MUTE_THRESHOLD, GROUP_STRIP_MARGIN_LEFT, GROUP_TEXT_OFFSET, BAR_TEXT_PADDING_LEFT } from '../constants.js';\nimport { ColorSchemeDiff, ColorScheme } from '../types.js';\nimport { getBarColorByDiff, getBarColorByValue, getBarColorByPackage } from './colors.js';\n\nfunction useFlameRender(options) {\n  const {\n    canvasRef,\n    data,\n    root,\n    depth,\n    direction,\n    wrapperWidth,\n    rangeMin,\n    rangeMax,\n    matchedLabels,\n    textAlign,\n    totalViewTicks,\n    totalColorTicks,\n    totalTicksRight,\n    colorScheme,\n    focusedItemData,\n    collapsedMap\n  } = options;\n  const ctx = useSetupCanvas(canvasRef, wrapperWidth, depth);\n  const theme = useTheme2();\n  const mutedColor = useMemo(() => {\n    const barMutedColor = color(theme.colors.background.secondary);\n    return theme.isLight ? barMutedColor.darken(10).toHexString() : barMutedColor.lighten(10).toHexString();\n  }, [theme]);\n  const getBarColor = useColorFunction(\n    totalColorTicks,\n    totalTicksRight,\n    colorScheme,\n    theme,\n    mutedColor,\n    rangeMin,\n    rangeMax,\n    matchedLabels,\n    focusedItemData ? focusedItemData.item.level : 0\n  );\n  const renderFunc = useRenderFunc(ctx, data, getBarColor, textAlign, collapsedMap);\n  useEffect(() => {\n    if (!ctx) {\n      return;\n    }\n    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n    const mutedPath2D = new Path2D();\n    walkTree(\n      root,\n      direction,\n      data,\n      totalViewTicks,\n      rangeMin,\n      rangeMax,\n      wrapperWidth,\n      collapsedMap,\n      (item, x, y, width, height, label, muted) => {\n        if (muted) {\n          mutedPath2D.rect(x, y, width, height);\n        } else {\n          renderFunc(item, x, y, width, height, label);\n        }\n      }\n    );\n    ctx.fillStyle = mutedColor;\n    ctx.fill(mutedPath2D);\n  }, [\n    ctx,\n    data,\n    root,\n    wrapperWidth,\n    rangeMin,\n    rangeMax,\n    totalViewTicks,\n    direction,\n    renderFunc,\n    collapsedMap,\n    mutedColor\n  ]);\n}\nfunction useRenderFunc(ctx, data, getBarColor, textAlign, collapsedMap) {\n  return useMemo(() => {\n    if (!ctx) {\n      return () => {\n      };\n    }\n    const renderFunc = (item, x, y, width, height, label) => {\n      ctx.beginPath();\n      ctx.rect(x + BAR_BORDER_WIDTH, y, width, height);\n      ctx.fillStyle = getBarColor(item, label, false);\n      ctx.stroke();\n      ctx.fill();\n      const collapsedItemConfig = collapsedMap.get(item);\n      let finalLabel = label;\n      if (collapsedItemConfig && collapsedItemConfig.collapsed) {\n        const numberOfCollapsedItems = collapsedItemConfig.items.length;\n        finalLabel = `(${numberOfCollapsedItems}) ` + label;\n      }\n      if (width >= LABEL_THRESHOLD) {\n        if (collapsedItemConfig) {\n          renderLabel(\n            ctx,\n            data,\n            finalLabel,\n            item,\n            width,\n            textAlign === \"left\" ? x + GROUP_STRIP_MARGIN_LEFT + GROUP_TEXT_OFFSET : x,\n            y,\n            textAlign\n          );\n          renderGroupingStrip(ctx, x, y, height, item, collapsedItemConfig);\n        } else {\n          renderLabel(ctx, data, finalLabel, item, width, x, y, textAlign);\n        }\n      }\n    };\n    return renderFunc;\n  }, [ctx, getBarColor, textAlign, data, collapsedMap]);\n}\nfunction renderGroupingStrip(ctx, x, y, height, item, collapsedItemConfig) {\n  const groupStripX = x + GROUP_STRIP_MARGIN_LEFT;\n  ctx.beginPath();\n  ctx.rect(x, y, groupStripX - x + GROUP_STRIP_WIDTH + GROUP_STRIP_PADDING, height);\n  ctx.fill();\n  ctx.beginPath();\n  if (collapsedItemConfig.collapsed) {\n    ctx.rect(groupStripX, y + height / 4, GROUP_STRIP_WIDTH, height / 2);\n  } else {\n    if (collapsedItemConfig.items[0] === item) {\n      ctx.rect(groupStripX, y + height / 2, GROUP_STRIP_WIDTH, height / 2);\n    } else if (collapsedItemConfig.items[collapsedItemConfig.items.length - 1] === item) {\n      ctx.rect(groupStripX, y, GROUP_STRIP_WIDTH, height / 2);\n    } else {\n      ctx.rect(groupStripX, y, GROUP_STRIP_WIDTH, height);\n    }\n  }\n  ctx.fillStyle = \"#666\";\n  ctx.fill();\n}\nfunction walkTree(root, direction, data, totalViewTicks, rangeMin, rangeMax, wrapperWidth, collapsedMap, renderFunc) {\n  const stack = [];\n  stack.push({ item: root, levelOffset: 0 });\n  const pixelsPerTick = wrapperWidth * window.devicePixelRatio / totalViewTicks / (rangeMax - rangeMin);\n  let collapsedItemRendered = void 0;\n  while (stack.length > 0) {\n    const { item, levelOffset } = stack.shift();\n    let curBarTicks = item.value;\n    const muted = curBarTicks * pixelsPerTick <= MUTE_THRESHOLD;\n    const width = curBarTicks * pixelsPerTick - (muted ? 0 : BAR_BORDER_WIDTH * 2);\n    const height = PIXELS_PER_LEVEL;\n    if (width < HIDE_THRESHOLD) {\n      continue;\n    }\n    let offsetModifier = 0;\n    let skipRender = false;\n    const collapsedItemConfig = collapsedMap.get(item);\n    const isCollapsedItem = collapsedItemConfig && collapsedItemConfig.collapsed;\n    if (isCollapsedItem) {\n      if (collapsedItemRendered === collapsedItemConfig.items[0]) {\n        offsetModifier = direction === \"children\" ? -1 : 1;\n        skipRender = true;\n      } else {\n        collapsedItemRendered = void 0;\n      }\n    } else {\n      collapsedItemRendered = void 0;\n    }\n    if (!skipRender) {\n      const barX = getBarX(item.start, totalViewTicks, rangeMin, pixelsPerTick);\n      const barY = (item.level + levelOffset) * PIXELS_PER_LEVEL;\n      let label = data.getLabel(item.itemIndexes[0]);\n      if (isCollapsedItem) {\n        collapsedItemRendered = item;\n      }\n      renderFunc(item, barX, barY, width, height, label, muted);\n    }\n    const nextList = direction === \"children\" ? item.children : item.parents;\n    if (nextList) {\n      stack.unshift(...nextList.map((c) => ({ item: c, levelOffset: levelOffset + offsetModifier })));\n    }\n  }\n}\nfunction useColorFunction(totalTicks, totalTicksRight, colorScheme, theme, mutedColor, rangeMin, rangeMax, matchedLabels, topLevel) {\n  return useCallback(\n    function getColor(item, label, muted) {\n      if (muted && !matchedLabels) {\n        return mutedColor;\n      }\n      const barColor = item.valueRight !== void 0 && (colorScheme === ColorSchemeDiff.Default || colorScheme === ColorSchemeDiff.DiffColorBlind) ? getBarColorByDiff(item.value, item.valueRight, totalTicks, totalTicksRight, colorScheme) : colorScheme === ColorScheme.ValueBased ? getBarColorByValue(item.value, totalTicks, rangeMin, rangeMax) : getBarColorByPackage(label, theme);\n      if (matchedLabels) {\n        return matchedLabels.has(label) ? barColor.toHslString() : mutedColor;\n      }\n      return item.level > topLevel - 1 ? barColor.toHslString() : barColor.lighten(15).toHslString();\n    },\n    [totalTicks, totalTicksRight, colorScheme, theme, rangeMin, rangeMax, matchedLabels, topLevel, mutedColor]\n  );\n}\nfunction useSetupCanvas(canvasRef, wrapperWidth, numberOfLevels) {\n  const [ctx, setCtx] = useState();\n  useEffect(() => {\n    if (!(numberOfLevels && canvasRef.current)) {\n      return;\n    }\n    const ctx2 = canvasRef.current.getContext(\"2d\");\n    const height = PIXELS_PER_LEVEL * numberOfLevels;\n    canvasRef.current.width = Math.round(wrapperWidth * window.devicePixelRatio);\n    canvasRef.current.height = Math.round(height);\n    canvasRef.current.style.width = `${wrapperWidth}px`;\n    canvasRef.current.style.height = `${height / window.devicePixelRatio}px`;\n    ctx2.textBaseline = \"middle\";\n    ctx2.font = 12 * window.devicePixelRatio + \"px monospace\";\n    ctx2.strokeStyle = \"white\";\n    setCtx(ctx2);\n  }, [canvasRef, setCtx, wrapperWidth, numberOfLevels]);\n  return ctx;\n}\nfunction renderLabel(ctx, data, label, item, width, x, y, textAlign) {\n  ctx.save();\n  ctx.clip();\n  ctx.fillStyle = \"#222\";\n  const displayValue = data.valueDisplayProcessor(item.value);\n  const unit = displayValue.suffix ? displayValue.text + displayValue.suffix : displayValue.text;\n  const measure = ctx.measureText(label);\n  const spaceForTextInRect = width - BAR_TEXT_PADDING_LEFT;\n  let fullLabel = `${label} (${unit})`;\n  let labelX = Math.max(x, 0) + BAR_TEXT_PADDING_LEFT;\n  if (measure.width > spaceForTextInRect) {\n    ctx.textAlign = textAlign;\n    if (textAlign === \"right\") {\n      fullLabel = label;\n      labelX = x + width - BAR_TEXT_PADDING_LEFT;\n    }\n  }\n  ctx.fillText(fullLabel, labelX, y + PIXELS_PER_LEVEL / 2 + 2);\n  ctx.restore();\n}\nfunction getBarX(offset, totalTicks, rangeMin, pixelsPerTick) {\n  return (offset - totalTicks * rangeMin) * pixelsPerTick;\n}\n\nexport { getBarX, useFlameRender, walkTree };\n//# sourceMappingURL=rendering.js.map\n", "import { jsxs, jsx } from 'react/jsx-runtime';\nimport { css } from '@emotion/css';\nimport { useRef, useState, useCallback, useEffect } from 'react';\nimport { useMeasure } from 'react-use';\nimport { PIXELS_PER_LEVEL } from '../constants.js';\nimport FlameGraphContextMenu from './FlameGraphContextMenu.js';\nimport FlameGraphTooltip from './FlameGraphTooltip.js';\nimport { useFlameRender, getBarX } from './rendering.js';\n\nconst FlameGraphCanvas = ({\n  data,\n  rangeMin,\n  rangeMax,\n  matchedLabels,\n  setRangeMin,\n  setRangeMax,\n  onItemFocused,\n  focusedItemData,\n  textAlign,\n  onSandwich,\n  colorScheme,\n  totalProfileTicks,\n  totalProfileTicksRight,\n  totalViewTicks,\n  root,\n  direction,\n  depth,\n  showFlameGraphOnly,\n  collapsedMap,\n  setCollapsedMap,\n  collapsing,\n  getExtraContextMenuButtons,\n  selectedView,\n  search\n}) => {\n  const styles = getStyles();\n  const [sizeRef, { width: wrapperWidth }] = useMeasure();\n  const graphRef = useRef(null);\n  const [tooltipItem, setTooltipItem] = useState();\n  const [clickedItemData, setClickedItemData] = useState();\n  useFlameRender({\n    canvasRef: graphRef,\n    colorScheme,\n    data,\n    focusedItemData,\n    root,\n    direction,\n    depth,\n    rangeMax,\n    rangeMin,\n    matchedLabels,\n    textAlign,\n    totalViewTicks,\n    // We need this so that if we have a diff profile and are in sandwich view we still show the same diff colors.\n    totalColorTicks: data.isDiffFlamegraph() ? totalProfileTicks : totalViewTicks,\n    totalTicksRight: totalProfileTicksRight,\n    wrapperWidth,\n    collapsedMap\n  });\n  const onGraphClick = useCallback(\n    (e) => {\n      setTooltipItem(void 0);\n      const pixelsPerTick = graphRef.current.clientWidth / totalViewTicks / (rangeMax - rangeMin);\n      const item = convertPixelCoordinatesToBarCoordinates(\n        { x: e.nativeEvent.offsetX, y: e.nativeEvent.offsetY },\n        root,\n        direction,\n        depth,\n        pixelsPerTick,\n        totalViewTicks,\n        rangeMin,\n        collapsedMap\n      );\n      if (item) {\n        setClickedItemData({\n          posY: e.clientY,\n          posX: e.clientX,\n          item,\n          label: data.getLabel(item.itemIndexes[0])\n        });\n      } else {\n        setClickedItemData(void 0);\n      }\n    },\n    [data, rangeMin, rangeMax, totalViewTicks, root, direction, depth, collapsedMap]\n  );\n  const [mousePosition, setMousePosition] = useState();\n  const onGraphMouseMove = useCallback(\n    (e) => {\n      if (clickedItemData === void 0) {\n        setTooltipItem(void 0);\n        setMousePosition(void 0);\n        const pixelsPerTick = graphRef.current.clientWidth / totalViewTicks / (rangeMax - rangeMin);\n        const item = convertPixelCoordinatesToBarCoordinates(\n          { x: e.nativeEvent.offsetX, y: e.nativeEvent.offsetY },\n          root,\n          direction,\n          depth,\n          pixelsPerTick,\n          totalViewTicks,\n          rangeMin,\n          collapsedMap\n        );\n        if (item) {\n          setMousePosition({ x: e.clientX, y: e.clientY });\n          setTooltipItem(item);\n        }\n      }\n    },\n    [rangeMin, rangeMax, totalViewTicks, clickedItemData, setMousePosition, root, direction, depth, collapsedMap]\n  );\n  const onGraphMouseLeave = useCallback(() => {\n    setTooltipItem(void 0);\n  }, []);\n  useEffect(() => {\n    const handleOnClick = (e) => {\n      var _a;\n      if (e.target instanceof HTMLElement && ((_a = e.target.parentElement) == null ? void 0 : _a.id) !== \"flameGraphCanvasContainer_clickOutsideCheck\") {\n        setClickedItemData(void 0);\n      }\n    };\n    window.addEventListener(\"click\", handleOnClick);\n    return () => window.removeEventListener(\"click\", handleOnClick);\n  }, [setClickedItemData]);\n  return /* @__PURE__ */ jsxs(\"div\", { className: styles.graph, children: [\n    /* @__PURE__ */ jsx(\"div\", { className: styles.canvasWrapper, id: \"flameGraphCanvasContainer_clickOutsideCheck\", ref: sizeRef, children: /* @__PURE__ */ jsx(\n      \"canvas\",\n      {\n        ref: graphRef,\n        \"data-testid\": \"flameGraph\",\n        onClick: onGraphClick,\n        onMouseMove: onGraphMouseMove,\n        onMouseLeave: onGraphMouseLeave\n      }\n    ) }),\n    /* @__PURE__ */ jsx(\n      FlameGraphTooltip,\n      {\n        position: mousePosition,\n        item: tooltipItem,\n        data,\n        totalTicks: totalViewTicks,\n        collapseConfig: tooltipItem ? collapsedMap.get(tooltipItem) : void 0\n      }\n    ),\n    !showFlameGraphOnly && clickedItemData && /* @__PURE__ */ jsx(\n      FlameGraphContextMenu,\n      {\n        data,\n        itemData: clickedItemData,\n        collapsing,\n        collapseConfig: collapsedMap.get(clickedItemData.item),\n        onMenuItemClick: () => {\n          setClickedItemData(void 0);\n        },\n        onItemFocus: () => {\n          setRangeMin(clickedItemData.item.start / totalViewTicks);\n          setRangeMax((clickedItemData.item.start + clickedItemData.item.value) / totalViewTicks);\n          onItemFocused(clickedItemData);\n        },\n        onSandwich: () => {\n          onSandwich(data.getLabel(clickedItemData.item.itemIndexes[0]));\n        },\n        onExpandGroup: () => {\n          setCollapsedMap(collapsedMap.setCollapsedStatus(clickedItemData.item, false));\n        },\n        onCollapseGroup: () => {\n          setCollapsedMap(collapsedMap.setCollapsedStatus(clickedItemData.item, true));\n        },\n        onExpandAllGroups: () => {\n          setCollapsedMap(collapsedMap.setAllCollapsedStatus(false));\n        },\n        onCollapseAllGroups: () => {\n          setCollapsedMap(collapsedMap.setAllCollapsedStatus(true));\n        },\n        allGroupsCollapsed: Array.from(collapsedMap.values()).every((i) => i.collapsed),\n        allGroupsExpanded: Array.from(collapsedMap.values()).every((i) => !i.collapsed),\n        getExtraContextMenuButtons,\n        selectedView,\n        search\n      }\n    )\n  ] });\n};\nconst getStyles = () => ({\n  graph: css({\n    label: \"graph\",\n    overflow: \"auto\",\n    flexGrow: 1,\n    flexBasis: \"50%\"\n  }),\n  canvasContainer: css({\n    label: \"canvasContainer\",\n    display: \"flex\"\n  }),\n  canvasWrapper: css({\n    label: \"canvasWrapper\",\n    cursor: \"pointer\",\n    flex: 1,\n    overflow: \"hidden\"\n  }),\n  sandwichMarker: css({\n    label: \"sandwichMarker\",\n    writingMode: \"vertical-lr\",\n    transform: \"rotate(180deg)\",\n    overflow: \"hidden\",\n    whiteSpace: \"nowrap\"\n  }),\n  sandwichMarkerIcon: css({\n    label: \"sandwichMarkerIcon\",\n    verticalAlign: \"baseline\"\n  })\n});\nconst convertPixelCoordinatesToBarCoordinates = (pos, root, direction, depth, pixelsPerTick, totalTicks, rangeMin, collapsedMap) => {\n  let next = root;\n  let currentLevel = direction === \"children\" ? 0 : depth - 1;\n  const levelIndex = Math.floor(pos.y / (PIXELS_PER_LEVEL / window.devicePixelRatio));\n  let found = void 0;\n  while (next) {\n    const node = next;\n    next = void 0;\n    if (currentLevel === levelIndex) {\n      found = node;\n      break;\n    }\n    const nextList = direction === \"children\" ? node.children : node.parents || [];\n    for (const child of nextList) {\n      const xStart = getBarX(child.start, totalTicks, rangeMin, pixelsPerTick);\n      const xEnd = getBarX(child.start + child.value, totalTicks, rangeMin, pixelsPerTick);\n      if (xStart <= pos.x && pos.x < xEnd) {\n        next = child;\n        const collapsedConfig = collapsedMap.get(child);\n        if (!collapsedConfig || !collapsedConfig.collapsed || collapsedConfig.items[0] === child) {\n          currentLevel = currentLevel + (direction === \"children\" ? 1 : -1);\n        }\n        break;\n      }\n    }\n  }\n  return found;\n};\n\nexport { convertPixelCoordinatesToBarCoordinates, FlameGraphCanvas as default };\n//# sourceMappingURL=FlameGraphCanvas.js.map\n", "import { jsxs, jsx } from 'react/jsx-runtime';\nimport { css } from '@emotion/css';\nimport { memo } from 'react';\nimport { getValueFormat } from '@grafana/data';\nimport { useStyles2, Tooltip, Icon, IconButton } from '@grafana/ui';\n\nconst FlameGraphMetadata = memo(\n  ({ data, focusedItem, totalTicks, sandwichedLabel, onFocusPillClick, onSandwichPillClick }) => {\n    const styles = useStyles2(getStyles);\n    const parts = [];\n    const ticksVal = getValueFormat(\"short\")(totalTicks);\n    const displayValue = data.valueDisplayProcessor(totalTicks);\n    let unitValue = displayValue.text + displayValue.suffix;\n    const unitTitle = data.getUnitTitle();\n    if (unitTitle === \"Count\") {\n      if (!displayValue.suffix) {\n        unitValue = displayValue.text;\n      }\n    }\n    parts.push(\n      /* @__PURE__ */ jsxs(\"div\", { className: styles.metadataPill, children: [\n        unitValue,\n        \" | \",\n        ticksVal.text,\n        ticksVal.suffix,\n        \" samples (\",\n        unitTitle,\n        \")\"\n      ] }, \"default\")\n    );\n    if (sandwichedLabel) {\n      parts.push(\n        /* @__PURE__ */ jsx(Tooltip, { content: sandwichedLabel, placement: \"top\", children: /* @__PURE__ */ jsxs(\"div\", { children: [\n          /* @__PURE__ */ jsx(Icon, { size: \"sm\", name: \"angle-right\" }),\n          /* @__PURE__ */ jsxs(\"div\", { className: styles.metadataPill, children: [\n            /* @__PURE__ */ jsx(Icon, { size: \"sm\", name: \"gf-show-context\" }),\n            \" \",\n            /* @__PURE__ */ jsx(\"span\", { className: styles.metadataPillName, children: sandwichedLabel.substring(sandwichedLabel.lastIndexOf(\"/\") + 1) }),\n            /* @__PURE__ */ jsx(\n              IconButton,\n              {\n                className: styles.pillCloseButton,\n                name: \"times\",\n                size: \"sm\",\n                onClick: onSandwichPillClick,\n                tooltip: \"Remove sandwich view\",\n                \"aria-label\": \"Remove sandwich view\"\n              }\n            )\n          ] })\n        ] }) }, \"sandwich\")\n      );\n    }\n    if (focusedItem) {\n      const percentValue = totalTicks > 0 ? Math.round(1e4 * (focusedItem.item.value / totalTicks)) / 100 : 0;\n      const iconName = percentValue > 0 ? \"eye\" : \"exclamation-circle\";\n      parts.push(\n        /* @__PURE__ */ jsx(Tooltip, { content: focusedItem.label, placement: \"top\", children: /* @__PURE__ */ jsxs(\"div\", { children: [\n          /* @__PURE__ */ jsx(Icon, { size: \"sm\", name: \"angle-right\" }),\n          /* @__PURE__ */ jsxs(\"div\", { className: styles.metadataPill, children: [\n            /* @__PURE__ */ jsx(Icon, { size: \"sm\", name: iconName }),\n            \"\\xA0\",\n            percentValue,\n            \"% of total\",\n            /* @__PURE__ */ jsx(\n              IconButton,\n              {\n                className: styles.pillCloseButton,\n                name: \"times\",\n                size: \"sm\",\n                onClick: onFocusPillClick,\n                tooltip: \"Remove focus\",\n                \"aria-label\": \"Remove focus\"\n              }\n            )\n          ] })\n        ] }) }, \"focus\")\n      );\n    }\n    return /* @__PURE__ */ jsx(\"div\", { className: styles.metadata, children: parts });\n  }\n);\nFlameGraphMetadata.displayName = \"FlameGraphMetadata\";\nconst getStyles = (theme) => ({\n  metadataPill: css({\n    label: \"metadataPill\",\n    display: \"inline-flex\",\n    alignItems: \"center\",\n    background: theme.colors.background.secondary,\n    borderRadius: theme.shape.borderRadius(8),\n    padding: theme.spacing(0.5, 1),\n    fontSize: theme.typography.bodySmall.fontSize,\n    fontWeight: theme.typography.fontWeightMedium,\n    lineHeight: theme.typography.bodySmall.lineHeight,\n    color: theme.colors.text.secondary\n  }),\n  pillCloseButton: css({\n    label: \"pillCloseButton\",\n    verticalAlign: \"text-bottom\",\n    margin: theme.spacing(0, 0.5)\n  }),\n  metadata: css({\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    margin: \"8px 0\"\n  }),\n  metadataPillName: css({\n    label: \"metadataPillName\",\n    maxWidth: \"200px\",\n    overflow: \"hidden\",\n    textOverflow: \"ellipsis\",\n    whiteSpace: \"nowrap\",\n    marginLeft: theme.spacing(0.5)\n  })\n});\n\nexport { FlameGraphMetadata as default };\n//# sourceMappingURL=FlameGraphMetadata.js.map\n", "import { jsxs, Fragment, jsx } from 'react/jsx-runtime';\nimport { cx, css } from '@emotion/css';\nimport { useState, useEffect } from 'react';\nimport { Icon } from '@grafana/ui';\nimport { PIXELS_PER_LEVEL } from '../constants.js';\nimport FlameGraphCanvas from './FlameGraphCanvas.js';\nimport FlameGraphMetadata from './FlameGraphMetadata.js';\n\nconst FlameGraph = ({\n  data,\n  rangeMin,\n  rangeMax,\n  matchedLabels,\n  setRangeMin,\n  setRangeMax,\n  onItemFocused,\n  focusedItemData,\n  textAlign,\n  onSandwich,\n  sandwichItem,\n  onFocusPillClick,\n  onSandwichPillClick,\n  colorScheme,\n  showFlameGraphOnly,\n  getExtraContextMenuButtons,\n  collapsing,\n  selectedView,\n  search,\n  collapsedMap,\n  setCollapsedMap\n}) => {\n  const styles = getStyles();\n  const [levels, setLevels] = useState();\n  const [levelsCallers, setLevelsCallers] = useState();\n  const [totalProfileTicks, setTotalProfileTicks] = useState(0);\n  const [totalProfileTicksRight, setTotalProfileTicksRight] = useState();\n  const [totalViewTicks, setTotalViewTicks] = useState(0);\n  useEffect(() => {\n    var _a, _b, _c;\n    if (data) {\n      let levels2 = data.getLevels();\n      let totalProfileTicks2 = levels2.length ? levels2[0][0].value : 0;\n      let totalProfileTicksRight2 = levels2.length ? levels2[0][0].valueRight : void 0;\n      let totalViewTicks2 = totalProfileTicks2;\n      let levelsCallers2 = void 0;\n      if (sandwichItem) {\n        const [callers, callees] = data.getSandwichLevels(sandwichItem);\n        levels2 = callees;\n        levelsCallers2 = callers;\n        totalViewTicks2 = (_c = (_b = (_a = callees[0]) == null ? void 0 : _a[0]) == null ? void 0 : _b.value) != null ? _c : 0;\n      }\n      setLevels(levels2);\n      setLevelsCallers(levelsCallers2);\n      setTotalProfileTicks(totalProfileTicks2);\n      setTotalProfileTicksRight(totalProfileTicksRight2);\n      setTotalViewTicks(totalViewTicks2);\n    }\n  }, [data, sandwichItem]);\n  if (!levels) {\n    return null;\n  }\n  const commonCanvasProps = {\n    data,\n    rangeMin,\n    rangeMax,\n    matchedLabels,\n    setRangeMin,\n    setRangeMax,\n    onItemFocused,\n    focusedItemData,\n    textAlign,\n    onSandwich,\n    colorScheme,\n    totalProfileTicks,\n    totalProfileTicksRight,\n    totalViewTicks,\n    showFlameGraphOnly,\n    collapsedMap,\n    setCollapsedMap,\n    getExtraContextMenuButtons,\n    collapsing,\n    search,\n    selectedView\n  };\n  let canvas = null;\n  if (levelsCallers == null ? void 0 : levelsCallers.length) {\n    canvas = /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsxs(\"div\", { className: styles.sandwichCanvasWrapper, children: [\n        /* @__PURE__ */ jsxs(\"div\", { className: styles.sandwichMarker, children: [\n          \"Callers\",\n          /* @__PURE__ */ jsx(Icon, { className: styles.sandwichMarkerIcon, name: \"arrow-down\" })\n        ] }),\n        /* @__PURE__ */ jsx(\n          FlameGraphCanvas,\n          {\n            ...commonCanvasProps,\n            root: levelsCallers[levelsCallers.length - 1][0],\n            depth: levelsCallers.length,\n            direction: \"parents\",\n            collapsing: false\n          }\n        )\n      ] }),\n      /* @__PURE__ */ jsxs(\"div\", { className: styles.sandwichCanvasWrapper, children: [\n        /* @__PURE__ */ jsxs(\"div\", { className: cx(styles.sandwichMarker, styles.sandwichMarkerCalees), children: [\n          /* @__PURE__ */ jsx(Icon, { className: styles.sandwichMarkerIcon, name: \"arrow-up\" }),\n          \"Callees\"\n        ] }),\n        /* @__PURE__ */ jsx(\n          FlameGraphCanvas,\n          {\n            ...commonCanvasProps,\n            root: levels[0][0],\n            depth: levels.length,\n            direction: \"children\",\n            collapsing: false\n          }\n        )\n      ] })\n    ] });\n  } else if (levels == null ? void 0 : levels.length) {\n    canvas = /* @__PURE__ */ jsx(FlameGraphCanvas, { ...commonCanvasProps, root: levels[0][0], depth: levels.length, direction: \"children\" });\n  }\n  return /* @__PURE__ */ jsxs(\"div\", { className: styles.graph, children: [\n    /* @__PURE__ */ jsx(\n      FlameGraphMetadata,\n      {\n        data,\n        focusedItem: focusedItemData,\n        sandwichedLabel: sandwichItem,\n        totalTicks: totalViewTicks,\n        onFocusPillClick,\n        onSandwichPillClick\n      }\n    ),\n    canvas\n  ] });\n};\nconst getStyles = () => ({\n  graph: css({\n    label: \"graph\",\n    overflow: \"auto\",\n    flexGrow: 1,\n    flexBasis: \"50%\"\n  }),\n  sandwichCanvasWrapper: css({\n    label: \"sandwichCanvasWrapper\",\n    display: \"flex\",\n    marginBottom: `${PIXELS_PER_LEVEL / window.devicePixelRatio}px`\n  }),\n  sandwichMarker: css({\n    label: \"sandwichMarker\",\n    writingMode: \"vertical-lr\",\n    transform: \"rotate(180deg)\",\n    overflow: \"hidden\",\n    whiteSpace: \"nowrap\"\n  }),\n  sandwichMarkerCalees: css({\n    label: \"sandwichMarkerCalees\",\n    textAlign: \"right\"\n  }),\n  sandwichMarkerIcon: css({\n    label: \"sandwichMarkerIcon\",\n    verticalAlign: \"baseline\"\n  })\n});\n\nexport { FlameGraph as default };\n//# sourceMappingURL=FlameGraph.js.map\n", "import { groupBy } from 'lodash';\n\nfunction mergeParentSubtrees(roots, data) {\n  const newRoots = getParentSubtrees(roots);\n  return mergeSubtrees(newRoots, data, \"parents\");\n}\nfunction getParentSubtrees(roots) {\n  return roots.map((r) => {\n    var _a, _b;\n    if (!((_a = r.parents) == null ? void 0 : _a.length)) {\n      return r;\n    }\n    const newRoot = {\n      ...r,\n      children: []\n    };\n    const stack = [\n      { child: newRoot, parent: r.parents[0] }\n    ];\n    while (stack.length) {\n      const args = stack.shift();\n      const newNode = {\n        ...args.parent,\n        children: args.child ? [args.child] : [],\n        parents: []\n      };\n      if (args.child) {\n        newNode.value = args.child.value;\n        args.child.parents = [newNode];\n      }\n      if ((_b = args.parent.parents) == null ? void 0 : _b.length) {\n        stack.push({ child: newNode, parent: args.parent.parents[0] });\n      }\n    }\n    return newRoot;\n  });\n}\nfunction mergeSubtrees(roots, data, direction = \"children\") {\n  var _a;\n  const oppositeDirection = direction === \"parents\" ? \"children\" : \"parents\";\n  const levels = [];\n  const stack = [\n    { previous: void 0, items: roots, level: 0 }\n  ];\n  while (stack.length) {\n    const args = stack.shift();\n    const indexes = args.items.flatMap((i) => i.itemIndexes);\n    const newItem = {\n      // We use the items value instead of value from the data frame, cause we could have changed it in the process\n      value: args.items.reduce((acc, i) => acc + i.value, 0),\n      itemIndexes: indexes,\n      // these will change later\n      children: [],\n      parents: [],\n      start: 0,\n      level: args.level\n    };\n    levels[args.level] = levels[args.level] || [];\n    levels[args.level].push(newItem);\n    if (args.previous) {\n      newItem[oppositeDirection] = [args.previous];\n      const prevSiblingsVal = ((_a = args.previous[direction]) == null ? void 0 : _a.reduce((acc, node) => {\n        return acc + node.value;\n      }, 0)) || 0;\n      newItem.start = args.previous.start + prevSiblingsVal;\n      args.previous[direction].push(newItem);\n    }\n    const nextItems = args.items.flatMap((i) => i[direction] || []);\n    const nextGroups = groupBy(nextItems, (c) => data.getLabel(c.itemIndexes[0]));\n    for (const g of Object.values(nextGroups)) {\n      stack.push({ previous: newItem, items: g, level: args.level + 1 });\n    }\n  }\n  if (direction === \"parents\") {\n    levels.reverse();\n    levels.forEach((level, index) => {\n      level.forEach((item) => {\n        item.level = index;\n      });\n    });\n  }\n  return levels;\n}\n\nexport { mergeParentSubtrees, mergeSubtrees };\n//# sourceMappingURL=treeTransforms.js.map\n", "import { FieldType, getDisplayProcessor, createTheme } from '@grafana/data';\nimport { SampleUnit } from '../types.js';\nimport { mergeParentSubtrees, mergeSubtrees } from './treeTransforms.js';\n\nfunction nestedSetToLevels(container, options) {\n  const levels = [];\n  let offset = 0;\n  let parent = void 0;\n  const uniqueLabels = {};\n  for (let i = 0; i < container.data.length; i++) {\n    const currentLevel = container.getLevel(i);\n    const prevLevel = i > 0 ? container.getLevel(i - 1) : void 0;\n    levels[currentLevel] = levels[currentLevel] || [];\n    if (prevLevel && prevLevel >= currentLevel) {\n      const lastSibling = levels[currentLevel][levels[currentLevel].length - 1];\n      offset = lastSibling.start + container.getValue(lastSibling.itemIndexes[0]) + container.getValueRight(lastSibling.itemIndexes[0]);\n      parent = lastSibling.parents[0];\n    }\n    const newItem = {\n      itemIndexes: [i],\n      value: container.getValue(i) + container.getValueRight(i),\n      valueRight: container.isDiffFlamegraph() ? container.getValueRight(i) : void 0,\n      start: offset,\n      parents: parent && [parent],\n      children: [],\n      level: currentLevel\n    };\n    if (uniqueLabels[container.getLabel(i)]) {\n      uniqueLabels[container.getLabel(i)].push(newItem);\n    } else {\n      uniqueLabels[container.getLabel(i)] = [newItem];\n    }\n    if (parent) {\n      parent.children.push(newItem);\n    }\n    parent = newItem;\n    levels[currentLevel].push(newItem);\n  }\n  const collapsedMapContainer = new CollapsedMapBuilder(options == null ? void 0 : options.collapsingThreshold);\n  if (options == null ? void 0 : options.collapsing) {\n    collapsedMapContainer.addTree(levels[0][0]);\n  }\n  return [levels, uniqueLabels, collapsedMapContainer.getCollapsedMap()];\n}\nclass CollapsedMap {\n  constructor(map) {\n    // The levelItem used as a key is the item that will always be rendered in the flame graph. The config.items are all\n    // the items that are in the group and if the config.collapsed is true they will be hidden.\n    this.map = /* @__PURE__ */ new Map();\n    this.map = map || /* @__PURE__ */ new Map();\n  }\n  get(item) {\n    return this.map.get(item);\n  }\n  keys() {\n    return this.map.keys();\n  }\n  values() {\n    return this.map.values();\n  }\n  size() {\n    return this.map.size;\n  }\n  setCollapsedStatus(item, collapsed) {\n    const newMap = new Map(this.map);\n    const collapsedConfig = this.map.get(item);\n    const newConfig = { ...collapsedConfig, collapsed };\n    for (const item2 of collapsedConfig.items) {\n      newMap.set(item2, newConfig);\n    }\n    return new CollapsedMap(newMap);\n  }\n  setAllCollapsedStatus(collapsed) {\n    const newMap = new Map(this.map);\n    for (const item of this.map.keys()) {\n      const collapsedConfig = this.map.get(item);\n      const newConfig = { ...collapsedConfig, collapsed };\n      newMap.set(item, newConfig);\n    }\n    return new CollapsedMap(newMap);\n  }\n}\nclass CollapsedMapBuilder {\n  constructor(threshold) {\n    this.map = /* @__PURE__ */ new Map();\n    this.threshold = 0.99;\n    if (threshold !== void 0) {\n      this.threshold = threshold;\n    }\n  }\n  addTree(root) {\n    var _a;\n    const stack = [root];\n    while (stack.length) {\n      const current = stack.shift();\n      if ((_a = current.parents) == null ? void 0 : _a.length) {\n        this.addItem(current, current.parents[0]);\n      }\n      if (current.children.length) {\n        stack.unshift(...current.children);\n      }\n    }\n  }\n  // The heuristics here is pretty simple right now. Just check if it's single child and if we are within threshold.\n  // We assume items with small self just aren't too important while we cannot really collapse items with siblings\n  // as it's not clear what to do with said sibling.\n  addItem(item, parent) {\n    if (parent && item.value > parent.value * this.threshold && parent.children.length === 1) {\n      if (this.map.has(parent)) {\n        const config = this.map.get(parent);\n        this.map.set(item, config);\n        config.items.push(item);\n      } else {\n        const config = { items: [parent, item], collapsed: true };\n        this.map.set(parent, config);\n        this.map.set(item, config);\n      }\n    }\n  }\n  getCollapsedMap() {\n    return new CollapsedMap(this.map);\n  }\n}\nfunction getMessageCheckFieldsResult(wrongFields) {\n  if (wrongFields.missingFields.length) {\n    return `Data is missing fields: ${wrongFields.missingFields.join(\", \")}`;\n  }\n  if (wrongFields.wrongTypeFields.length) {\n    return `Data has fields of wrong type: ${wrongFields.wrongTypeFields.map((f) => `${f.name} has type ${f.type} but should be ${f.expectedTypes.join(\" or \")}`).join(\", \")}`;\n  }\n  return \"\";\n}\nfunction checkFields(data) {\n  const fields = [\n    [\"label\", [FieldType.string, FieldType.enum]],\n    [\"level\", [FieldType.number]],\n    [\"value\", [FieldType.number]],\n    [\"self\", [FieldType.number]]\n  ];\n  const missingFields = [];\n  const wrongTypeFields = [];\n  for (const field of fields) {\n    const [name, types] = field;\n    const frameField = data == null ? void 0 : data.fields.find((f) => f.name === name);\n    if (!frameField) {\n      missingFields.push(name);\n      continue;\n    }\n    if (!types.includes(frameField.type)) {\n      wrongTypeFields.push({ name, expectedTypes: types, type: frameField.type });\n    }\n  }\n  if (missingFields.length > 0 || wrongTypeFields.length > 0) {\n    return {\n      wrongTypeFields,\n      missingFields\n    };\n  }\n  return void 0;\n}\nclass FlameGraphDataContainer {\n  constructor(data, options, theme = createTheme()) {\n    var _a, _b, _c;\n    this.data = data;\n    this.options = options;\n    const wrongFields = checkFields(data);\n    if (wrongFields) {\n      throw new Error(getMessageCheckFieldsResult(wrongFields));\n    }\n    this.labelField = data.fields.find((f) => f.name === \"label\");\n    this.levelField = data.fields.find((f) => f.name === \"level\");\n    this.valueField = data.fields.find((f) => f.name === \"value\");\n    this.selfField = data.fields.find((f) => f.name === \"self\");\n    this.valueRightField = data.fields.find((f) => f.name === \"valueRight\");\n    this.selfRightField = data.fields.find((f) => f.name === \"selfRight\");\n    if ((this.valueField || this.selfField) && !(this.valueField && this.selfField)) {\n      throw new Error(\n        \"Malformed dataFrame: both valueRight and selfRight has to be present if one of them is present.\"\n      );\n    }\n    const enumConfig = (_c = (_b = (_a = this.labelField) == null ? void 0 : _a.config) == null ? void 0 : _b.type) == null ? void 0 : _c.enum;\n    if (enumConfig) {\n      this.labelDisplayProcessor = getDisplayProcessor({ field: this.labelField, theme });\n      this.uniqueLabels = enumConfig.text || [];\n    } else {\n      this.labelDisplayProcessor = (value) => ({\n        text: value + \"\",\n        numeric: 0\n      });\n      this.uniqueLabels = [...new Set(this.labelField.values)];\n    }\n    this.valueDisplayProcessor = getDisplayProcessor({\n      field: this.valueField,\n      theme\n    });\n  }\n  isDiffFlamegraph() {\n    return Boolean(this.valueRightField && this.selfRightField);\n  }\n  getLabel(index) {\n    return this.labelDisplayProcessor(this.labelField.values[index]).text;\n  }\n  getLevel(index) {\n    return this.levelField.values[index];\n  }\n  getValue(index) {\n    return fieldAccessor(this.valueField, index);\n  }\n  getValueRight(index) {\n    return fieldAccessor(this.valueRightField, index);\n  }\n  getSelf(index) {\n    return fieldAccessor(this.selfField, index);\n  }\n  getSelfRight(index) {\n    return fieldAccessor(this.selfRightField, index);\n  }\n  getSelfDisplay(index) {\n    return this.valueDisplayProcessor(this.getSelf(index));\n  }\n  getUniqueLabels() {\n    return this.uniqueLabels;\n  }\n  getUnitTitle() {\n    switch (this.valueField.config.unit) {\n      case SampleUnit.Bytes:\n        return \"RAM\";\n      case SampleUnit.Nanoseconds:\n        return \"Time\";\n    }\n    return \"Count\";\n  }\n  getLevels() {\n    this.initLevels();\n    return this.levels;\n  }\n  getSandwichLevels(label) {\n    const nodes = this.getNodesWithLabel(label);\n    if (!(nodes == null ? void 0 : nodes.length)) {\n      return [[], []];\n    }\n    const callers = mergeParentSubtrees(nodes, this);\n    const callees = mergeSubtrees(nodes, this);\n    return [callers, callees];\n  }\n  getNodesWithLabel(label) {\n    this.initLevels();\n    return this.uniqueLabelsMap[label];\n  }\n  getCollapsedMap() {\n    this.initLevels();\n    return this.collapsedMap;\n  }\n  initLevels() {\n    if (!this.levels) {\n      const [levels, uniqueLabelsMap, collapsedMap] = nestedSetToLevels(this, this.options);\n      this.levels = levels;\n      this.uniqueLabelsMap = uniqueLabelsMap;\n      this.collapsedMap = collapsedMap;\n    }\n  }\n}\nfunction fieldAccessor(field, index) {\n  if (!field) {\n    return 0;\n  }\n  let indexArray = typeof index === \"number\" ? [index] : index;\n  return indexArray.reduce((acc, index2) => {\n    return acc + field.values[index2];\n  }, 0);\n}\n\nexport { CollapsedMap, CollapsedMapBuilder, FlameGraphDataContainer, checkFields, getMessageCheckFieldsResult, nestedSetToLevels };\n//# sourceMappingURL=dataTransform.js.map\n", "import { jsxs, jsx } from 'react/jsx-runtime';\nimport { cx, css } from '@emotion/css';\nimport { useState, useEffect } from 'react';\nimport useDebounce from 'react-use/lib/useDebounce';\nimport usePrevious from 'react-use/lib/usePrevious';\nimport { useStyles2, Input, Button, ButtonGroup, RadioButtonGroup, Menu, Dropdown } from '@grafana/ui';\nimport { byValueGradient, byPackageGradient, diffDefaultGradient, diffColorBlindGradient } from './FlameGraph/colors.js';\nimport { MIN_WIDTH_TO_SHOW_BOTH_TOPTABLE_AND_FLAMEGRAPH } from './constants.js';\nimport { SelectedView, ColorScheme, ColorSchemeDiff } from './types.js';\n\nconst FlameGraphHeader = ({\n  search,\n  setSearch,\n  selectedView,\n  setSelectedView,\n  containerWidth,\n  onReset,\n  textAlign,\n  onTextAlignChange,\n  showResetButton,\n  colorScheme,\n  onColorSchemeChange,\n  stickyHeader,\n  extraHeaderElements,\n  vertical,\n  isDiffMode,\n  setCollapsedMap,\n  collapsedMap\n}) => {\n  const styles = useStyles2(getStyles);\n  const [localSearch, setLocalSearch] = useSearchInput(search, setSearch);\n  const suffix = localSearch !== \"\" ? /* @__PURE__ */ jsx(\n    Button,\n    {\n      icon: \"times\",\n      fill: \"text\",\n      size: \"sm\",\n      onClick: () => {\n        setSearch(\"\");\n        setLocalSearch(\"\");\n      },\n      children: \"Clear\"\n    }\n  ) : null;\n  return /* @__PURE__ */ jsxs(\"div\", { className: cx(styles.header, { [styles.stickyHeader]: stickyHeader }), children: [\n    /* @__PURE__ */ jsx(\"div\", { className: styles.inputContainer, children: /* @__PURE__ */ jsx(\n      Input,\n      {\n        value: localSearch || \"\",\n        onChange: (v) => {\n          setLocalSearch(v.currentTarget.value);\n        },\n        placeholder: \"Search...\",\n        suffix\n      }\n    ) }),\n    /* @__PURE__ */ jsxs(\"div\", { className: styles.rightContainer, children: [\n      showResetButton && /* @__PURE__ */ jsx(\n        Button,\n        {\n          variant: \"secondary\",\n          fill: \"outline\",\n          size: \"sm\",\n          icon: \"history-alt\",\n          tooltip: \"Reset focus and sandwich state\",\n          onClick: () => {\n            onReset();\n          },\n          className: styles.buttonSpacing,\n          \"aria-label\": \"Reset focus and sandwich state\"\n        }\n      ),\n      /* @__PURE__ */ jsx(ColorSchemeButton, { value: colorScheme, onChange: onColorSchemeChange, isDiffMode }),\n      /* @__PURE__ */ jsxs(ButtonGroup, { className: styles.buttonSpacing, children: [\n        /* @__PURE__ */ jsx(\n          Button,\n          {\n            variant: \"secondary\",\n            fill: \"outline\",\n            size: \"sm\",\n            tooltip: \"Expand all groups\",\n            onClick: () => {\n              setCollapsedMap(collapsedMap.setAllCollapsedStatus(false));\n            },\n            \"aria-label\": \"Expand all groups\",\n            icon: \"angle-double-down\",\n            disabled: selectedView === SelectedView.TopTable\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          Button,\n          {\n            variant: \"secondary\",\n            fill: \"outline\",\n            size: \"sm\",\n            tooltip: \"Collapse all groups\",\n            onClick: () => {\n              setCollapsedMap(collapsedMap.setAllCollapsedStatus(true));\n            },\n            \"aria-label\": \"Collapse all groups\",\n            icon: \"angle-double-up\",\n            disabled: selectedView === SelectedView.TopTable\n          }\n        )\n      ] }),\n      /* @__PURE__ */ jsx(\n        RadioButtonGroup,\n        {\n          size: \"sm\",\n          disabled: selectedView === SelectedView.TopTable,\n          options: alignOptions,\n          value: textAlign,\n          onChange: onTextAlignChange,\n          className: styles.buttonSpacing\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        RadioButtonGroup,\n        {\n          size: \"sm\",\n          options: getViewOptions(containerWidth, vertical),\n          value: selectedView,\n          onChange: setSelectedView\n        }\n      ),\n      extraHeaderElements && /* @__PURE__ */ jsx(\"div\", { className: styles.extraElements, children: extraHeaderElements })\n    ] })\n  ] });\n};\nfunction ColorSchemeButton(props) {\n  const styles = useStyles2(getStyles);\n  let menu = /* @__PURE__ */ jsxs(Menu, { children: [\n    /* @__PURE__ */ jsx(Menu.Item, { label: \"By package name\", onClick: () => props.onChange(ColorScheme.PackageBased) }),\n    /* @__PURE__ */ jsx(Menu.Item, { label: \"By value\", onClick: () => props.onChange(ColorScheme.ValueBased) })\n  ] });\n  const colorDotStyle = {\n    [ColorScheme.ValueBased]: styles.colorDotByValue,\n    [ColorScheme.PackageBased]: styles.colorDotByPackage,\n    [ColorSchemeDiff.DiffColorBlind]: styles.colorDotDiffColorBlind,\n    [ColorSchemeDiff.Default]: styles.colorDotDiffDefault\n  }[props.value] || styles.colorDotByValue;\n  let contents = /* @__PURE__ */ jsx(\"span\", { className: cx(styles.colorDot, colorDotStyle) });\n  if (props.isDiffMode) {\n    menu = /* @__PURE__ */ jsxs(Menu, { children: [\n      /* @__PURE__ */ jsx(Menu.Item, { label: \"Default (green to red)\", onClick: () => props.onChange(ColorSchemeDiff.Default) }),\n      /* @__PURE__ */ jsx(Menu.Item, { label: \"Color blind (blue to red)\", onClick: () => props.onChange(ColorSchemeDiff.DiffColorBlind) })\n    ] });\n    contents = /* @__PURE__ */ jsxs(\"div\", { className: cx(styles.colorDotDiff, colorDotStyle), children: [\n      /* @__PURE__ */ jsx(\"div\", { children: \"-100% (removed)\" }),\n      /* @__PURE__ */ jsx(\"div\", { children: \"0%\" }),\n      /* @__PURE__ */ jsx(\"div\", { children: \"+100% (added)\" })\n    ] });\n  }\n  return /* @__PURE__ */ jsx(Dropdown, { overlay: menu, children: /* @__PURE__ */ jsx(\n    Button,\n    {\n      variant: \"secondary\",\n      fill: \"outline\",\n      size: \"sm\",\n      tooltip: \"Change color scheme\",\n      onClick: () => {\n      },\n      className: styles.buttonSpacing,\n      \"aria-label\": \"Change color scheme\",\n      children: contents\n    }\n  ) });\n}\nconst alignOptions = [\n  { value: \"left\", description: \"Align text left\", icon: \"align-left\" },\n  { value: \"right\", description: \"Align text right\", icon: \"align-right\" }\n];\nfunction getViewOptions(width, vertical) {\n  let viewOptions = [\n    { value: SelectedView.TopTable, label: \"Top Table\", description: \"Only show top table\" },\n    { value: SelectedView.FlameGraph, label: \"Flame Graph\", description: \"Only show flame graph\" }\n  ];\n  if (width >= MIN_WIDTH_TO_SHOW_BOTH_TOPTABLE_AND_FLAMEGRAPH || vertical) {\n    viewOptions.push({\n      value: SelectedView.Both,\n      label: \"Both\",\n      description: \"Show both the top table and flame graph\"\n    });\n  }\n  return viewOptions;\n}\nfunction useSearchInput(search, setSearch) {\n  const [localSearchState, setLocalSearchState] = useState(search);\n  const prevSearch = usePrevious(search);\n  useDebounce(\n    () => {\n      setSearch(localSearchState);\n    },\n    250,\n    [localSearchState]\n  );\n  useEffect(() => {\n    if (prevSearch !== search && search !== localSearchState) {\n      setLocalSearchState(search);\n    }\n  }, [search, prevSearch, localSearchState]);\n  return [localSearchState, setLocalSearchState];\n}\nconst getStyles = (theme) => ({\n  header: css({\n    label: \"header\",\n    display: \"flex\",\n    flexWrap: \"wrap\",\n    justifyContent: \"space-between\",\n    width: \"100%\",\n    top: 0,\n    gap: theme.spacing(1),\n    marginTop: theme.spacing(1)\n  }),\n  stickyHeader: css({\n    zIndex: theme.zIndex.navbarFixed,\n    position: \"sticky\",\n    background: theme.colors.background.primary\n  }),\n  inputContainer: css({\n    label: \"inputContainer\",\n    flexGrow: 1,\n    minWidth: \"150px\",\n    maxWidth: \"350px\"\n  }),\n  rightContainer: css({\n    label: \"rightContainer\",\n    display: \"flex\",\n    alignItems: \"flex-start\",\n    flexWrap: \"wrap\"\n  }),\n  buttonSpacing: css({\n    label: \"buttonSpacing\",\n    marginRight: theme.spacing(1)\n  }),\n  resetButton: css({\n    label: \"resetButton\",\n    display: \"flex\",\n    marginRight: theme.spacing(2)\n  }),\n  resetButtonIconWrapper: css({\n    label: \"resetButtonIcon\",\n    padding: \"0 5px\",\n    color: theme.colors.text.disabled\n  }),\n  colorDot: css({\n    label: \"colorDot\",\n    display: \"inline-block\",\n    width: \"10px\",\n    height: \"10px\",\n    // eslint-disable-next-line @grafana/no-border-radius-literal\n    borderRadius: \"50%\"\n  }),\n  colorDotDiff: css({\n    label: \"colorDotDiff\",\n    display: \"flex\",\n    width: \"200px\",\n    height: \"12px\",\n    color: \"white\",\n    fontSize: 9,\n    lineHeight: 1.3,\n    fontWeight: 300,\n    justifyContent: \"space-between\",\n    padding: \"0 2px\",\n    // We have a specific sizing for this so probably makes sense to use hardcoded value here\n    // eslint-disable-next-line @grafana/no-border-radius-literal\n    borderRadius: \"2px\"\n  }),\n  colorDotByValue: css({\n    label: \"colorDotByValue\",\n    background: byValueGradient\n  }),\n  colorDotByPackage: css({\n    label: \"colorDotByPackage\",\n    background: byPackageGradient\n  }),\n  colorDotDiffDefault: css({\n    label: \"colorDotDiffDefault\",\n    background: diffDefaultGradient\n  }),\n  colorDotDiffColorBlind: css({\n    label: \"colorDotDiffColorBlind\",\n    background: diffColorBlindGradient\n  }),\n  extraElements: css({\n    label: \"extraElements\",\n    marginLeft: theme.spacing(1)\n  })\n});\n\nexport { FlameGraphHeader as default };\n//# sourceMappingURL=FlameGraphHeader.js.map\n", "import { Component, createElement } from 'react';\n\n/**\n * Detect Element Resize.\n * https://github.com/sdecima/javascript-detect-element-resize\n * Sebastian Decima\n *\n * Forked from version 0.5.3; includes the following modifications:\n * 1) Guard against unsafe 'window' and 'document' references (to support SSR).\n * 2) Defer initialization code via a top-level function wrapper (to support SSR).\n * 3) Avoid unnecessary reflows by not measuring size for scroll events bubbling from children.\n * 4) Add nonce for style element.\n * 5) Use 'export' statement over 'module.exports' assignment\n **/\n\n// Check `document` and `window` in case of server-side rendering\nlet windowObject;\nif (typeof window !== \"undefined\") {\n  windowObject = window;\n\n  // eslint-disable-next-line no-restricted-globals\n} else if (typeof self !== \"undefined\") {\n  // eslint-disable-next-line no-restricted-globals\n  windowObject = self;\n} else {\n  windowObject = global;\n}\nlet cancelFrame = null;\nlet requestFrame = null;\nconst TIMEOUT_DURATION = 20;\nconst clearTimeoutFn = windowObject.clearTimeout;\nconst setTimeoutFn = windowObject.setTimeout;\nconst cancelAnimationFrameFn = windowObject.cancelAnimationFrame || windowObject.mozCancelAnimationFrame || windowObject.webkitCancelAnimationFrame;\nconst requestAnimationFrameFn = windowObject.requestAnimationFrame || windowObject.mozRequestAnimationFrame || windowObject.webkitRequestAnimationFrame;\nif (cancelAnimationFrameFn == null || requestAnimationFrameFn == null) {\n  // For environments that don't support animation frame,\n  // fallback to a setTimeout based approach.\n  cancelFrame = clearTimeoutFn;\n  requestFrame = function requestAnimationFrameViaSetTimeout(callback) {\n    return setTimeoutFn(callback, TIMEOUT_DURATION);\n  };\n} else {\n  // Counter intuitively, environments that support animation frames can be trickier.\n  // Chrome's \"Throttle non-visible cross-origin iframes\" flag can prevent rAFs from being called.\n  // In this case, we should fallback to a setTimeout() implementation.\n  cancelFrame = function cancelFrame([animationFrameID, timeoutID]) {\n    cancelAnimationFrameFn(animationFrameID);\n    clearTimeoutFn(timeoutID);\n  };\n  requestFrame = function requestAnimationFrameWithSetTimeoutFallback(callback) {\n    const animationFrameID = requestAnimationFrameFn(function animationFrameCallback() {\n      clearTimeoutFn(timeoutID);\n      callback();\n    });\n    const timeoutID = setTimeoutFn(function timeoutCallback() {\n      cancelAnimationFrameFn(animationFrameID);\n      callback();\n    }, TIMEOUT_DURATION);\n    return [animationFrameID, timeoutID];\n  };\n}\nfunction createDetectElementResize(nonce) {\n  let animationKeyframes;\n  let animationName;\n  let animationStartEvent;\n  let animationStyle;\n  let checkTriggers;\n  let resetTriggers;\n  let scrollListener;\n  const attachEvent = typeof document !== \"undefined\" && document.attachEvent;\n  if (!attachEvent) {\n    resetTriggers = function (element) {\n      const triggers = element.__resizeTriggers__,\n        expand = triggers.firstElementChild,\n        contract = triggers.lastElementChild,\n        expandChild = expand.firstElementChild;\n      contract.scrollLeft = contract.scrollWidth;\n      contract.scrollTop = contract.scrollHeight;\n      expandChild.style.width = expand.offsetWidth + 1 + \"px\";\n      expandChild.style.height = expand.offsetHeight + 1 + \"px\";\n      expand.scrollLeft = expand.scrollWidth;\n      expand.scrollTop = expand.scrollHeight;\n    };\n    checkTriggers = function (element) {\n      return element.offsetWidth !== element.__resizeLast__.width || element.offsetHeight !== element.__resizeLast__.height;\n    };\n    scrollListener = function (e) {\n      // Don't measure (which forces) reflow for scrolls that happen inside of children!\n      if (e.target.className && typeof e.target.className.indexOf === \"function\" && e.target.className.indexOf(\"contract-trigger\") < 0 && e.target.className.indexOf(\"expand-trigger\") < 0) {\n        return;\n      }\n      const element = this;\n      resetTriggers(this);\n      if (this.__resizeRAF__) {\n        cancelFrame(this.__resizeRAF__);\n      }\n      this.__resizeRAF__ = requestFrame(function animationFrame() {\n        if (checkTriggers(element)) {\n          element.__resizeLast__.width = element.offsetWidth;\n          element.__resizeLast__.height = element.offsetHeight;\n          element.__resizeListeners__.forEach(function forEachResizeListener(fn) {\n            fn.call(element, e);\n          });\n        }\n      });\n    };\n\n    /* Detect CSS Animations support to detect element display/re-attach */\n    let animation = false;\n    let keyframeprefix = \"\";\n    animationStartEvent = \"animationstart\";\n    const domPrefixes = \"Webkit Moz O ms\".split(\" \");\n    let startEvents = \"webkitAnimationStart animationstart oAnimationStart MSAnimationStart\".split(\" \");\n    let pfx = \"\";\n    {\n      const elm = document.createElement(\"fakeelement\");\n      if (elm.style.animationName !== undefined) {\n        animation = true;\n      }\n      if (animation === false) {\n        for (let i = 0; i < domPrefixes.length; i++) {\n          if (elm.style[domPrefixes[i] + \"AnimationName\"] !== undefined) {\n            pfx = domPrefixes[i];\n            keyframeprefix = \"-\" + pfx.toLowerCase() + \"-\";\n            animationStartEvent = startEvents[i];\n            animation = true;\n            break;\n          }\n        }\n      }\n    }\n    animationName = \"resizeanim\";\n    animationKeyframes = \"@\" + keyframeprefix + \"keyframes \" + animationName + \" { from { opacity: 0; } to { opacity: 0; } } \";\n    animationStyle = keyframeprefix + \"animation: 1ms \" + animationName + \"; \";\n  }\n  const createStyles = function (doc) {\n    if (!doc.getElementById(\"detectElementResize\")) {\n      //opacity:0 works around a chrome bug https://code.google.com/p/chromium/issues/detail?id=286360\n      const css = (animationKeyframes ? animationKeyframes : \"\") + \".resize-triggers { \" + (animationStyle ? animationStyle : \"\") + \"visibility: hidden; opacity: 0; } \" + '.resize-triggers, .resize-triggers > div, .contract-trigger:before { content: \" \"; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',\n        head = doc.head || doc.getElementsByTagName(\"head\")[0],\n        style = doc.createElement(\"style\");\n      style.id = \"detectElementResize\";\n      style.type = \"text/css\";\n      if (nonce != null) {\n        style.setAttribute(\"nonce\", nonce);\n      }\n      if (style.styleSheet) {\n        style.styleSheet.cssText = css;\n      } else {\n        style.appendChild(doc.createTextNode(css));\n      }\n      head.appendChild(style);\n    }\n  };\n  const addResizeListener = function (element, fn) {\n    if (attachEvent) {\n      element.attachEvent(\"onresize\", fn);\n    } else {\n      if (!element.__resizeTriggers__) {\n        const doc = element.ownerDocument;\n        const elementStyle = windowObject.getComputedStyle(element);\n        if (elementStyle && elementStyle.position === \"static\") {\n          element.style.position = \"relative\";\n        }\n        createStyles(doc);\n        element.__resizeLast__ = {};\n        element.__resizeListeners__ = [];\n        (element.__resizeTriggers__ = doc.createElement(\"div\")).className = \"resize-triggers\";\n        const expandTrigger = doc.createElement(\"div\");\n        expandTrigger.className = \"expand-trigger\";\n        expandTrigger.appendChild(doc.createElement(\"div\"));\n        const contractTrigger = doc.createElement(\"div\");\n        contractTrigger.className = \"contract-trigger\";\n        element.__resizeTriggers__.appendChild(expandTrigger);\n        element.__resizeTriggers__.appendChild(contractTrigger);\n        element.appendChild(element.__resizeTriggers__);\n        resetTriggers(element);\n        element.addEventListener(\"scroll\", scrollListener, true);\n\n        /* Listen for a css animation to detect element display/re-attach */\n        if (animationStartEvent) {\n          element.__resizeTriggers__.__animationListener__ = function animationListener(e) {\n            if (e.animationName === animationName) {\n              resetTriggers(element);\n            }\n          };\n          element.__resizeTriggers__.addEventListener(animationStartEvent, element.__resizeTriggers__.__animationListener__);\n        }\n      }\n      element.__resizeListeners__.push(fn);\n    }\n  };\n  const removeResizeListener = function (element, fn) {\n    if (attachEvent) {\n      element.detachEvent(\"onresize\", fn);\n    } else {\n      element.__resizeListeners__.splice(element.__resizeListeners__.indexOf(fn), 1);\n      if (!element.__resizeListeners__.length) {\n        element.removeEventListener(\"scroll\", scrollListener, true);\n        if (element.__resizeTriggers__.__animationListener__) {\n          element.__resizeTriggers__.removeEventListener(animationStartEvent, element.__resizeTriggers__.__animationListener__);\n          element.__resizeTriggers__.__animationListener__ = null;\n        }\n        try {\n          element.__resizeTriggers__ = !element.removeChild(element.__resizeTriggers__);\n        } catch (e) {\n          // Preact compat; see developit/preact-compat/issues/228\n        }\n      }\n    }\n  };\n  return {\n    addResizeListener,\n    removeResizeListener\n  };\n}\n\nclass AutoSizer extends Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      height: this.props.defaultHeight || 0,\n      scaledHeight: this.props.defaultHeight || 0,\n      scaledWidth: this.props.defaultWidth || 0,\n      width: this.props.defaultWidth || 0\n    };\n    this._autoSizer = null;\n    this._detectElementResize = null;\n    this._parentNode = null;\n    this._resizeObserver = null;\n    this._timeoutId = null;\n    this._onResize = () => {\n      this._timeoutId = null;\n      const {\n        disableHeight,\n        disableWidth,\n        onResize\n      } = this.props;\n      if (this._parentNode) {\n        // Guard against AutoSizer component being removed from the DOM immediately after being added.\n        // This can result in invalid style values which can result in NaN values if we don't handle them.\n        // See issue #150 for more context.\n\n        const style = window.getComputedStyle(this._parentNode) || {};\n        const paddingLeft = parseFloat(style.paddingLeft || \"0\");\n        const paddingRight = parseFloat(style.paddingRight || \"0\");\n        const paddingTop = parseFloat(style.paddingTop || \"0\");\n        const paddingBottom = parseFloat(style.paddingBottom || \"0\");\n        const rect = this._parentNode.getBoundingClientRect();\n        const scaledHeight = rect.height - paddingTop - paddingBottom;\n        const scaledWidth = rect.width - paddingLeft - paddingRight;\n        const height = this._parentNode.offsetHeight - paddingTop - paddingBottom;\n        const width = this._parentNode.offsetWidth - paddingLeft - paddingRight;\n        if (!disableHeight && (this.state.height !== height || this.state.scaledHeight !== scaledHeight) || !disableWidth && (this.state.width !== width || this.state.scaledWidth !== scaledWidth)) {\n          this.setState({\n            height,\n            width,\n            scaledHeight,\n            scaledWidth\n          });\n          if (typeof onResize === \"function\") {\n            onResize({\n              height,\n              scaledHeight,\n              scaledWidth,\n              width\n            });\n          }\n        }\n      }\n    };\n    this._setRef = autoSizer => {\n      this._autoSizer = autoSizer;\n    };\n  }\n  componentDidMount() {\n    const {\n      nonce\n    } = this.props;\n    const parentNode = this._autoSizer ? this._autoSizer.parentNode : null;\n    if (parentNode != null && parentNode.ownerDocument && parentNode.ownerDocument.defaultView && parentNode instanceof parentNode.ownerDocument.defaultView.HTMLElement) {\n      // Delay access of parentNode until mount.\n      // This handles edge-cases where the component has already been unmounted before its ref has been set,\n      // As well as libraries like react-lite which have a slightly different lifecycle.\n      this._parentNode = parentNode;\n\n      // Use ResizeObserver from the same context where parentNode (which we will observe) was defined\n      // Using just global can result into onResize events not being emitted in cases with multiple realms\n      const ResizeObserverInstance = parentNode.ownerDocument.defaultView.ResizeObserver;\n      if (ResizeObserverInstance != null) {\n        this._resizeObserver = new ResizeObserverInstance(() => {\n          // Guard against \"ResizeObserver loop limit exceeded\" error;\n          // could be triggered if the state update causes the ResizeObserver handler to run long.\n          // See https://github.com/bvaughn/react-virtualized-auto-sizer/issues/55\n          this._timeoutId = setTimeout(this._onResize, 0);\n        });\n        this._resizeObserver.observe(parentNode);\n      } else {\n        // Defer requiring resize handler in order to support server-side rendering.\n        // See issue #41\n        this._detectElementResize = createDetectElementResize(nonce);\n        this._detectElementResize.addResizeListener(parentNode, this._onResize);\n      }\n      this._onResize();\n    }\n  }\n  componentWillUnmount() {\n    if (this._parentNode) {\n      if (this._detectElementResize) {\n        this._detectElementResize.removeResizeListener(this._parentNode, this._onResize);\n      }\n      if (this._timeoutId !== null) {\n        clearTimeout(this._timeoutId);\n      }\n      if (this._resizeObserver) {\n        this._resizeObserver.disconnect();\n      }\n    }\n  }\n  render() {\n    const {\n      children,\n      defaultHeight,\n      defaultWidth,\n      disableHeight = false,\n      disableWidth = false,\n      doNotBailOutOnEmptyChildren = false,\n      nonce,\n      onResize,\n      style = {},\n      tagName = \"div\",\n      ...rest\n    } = this.props;\n    const {\n      height,\n      scaledHeight,\n      scaledWidth,\n      width\n    } = this.state;\n\n    // Outer div should not force width/height since that may prevent containers from shrinking.\n    // Inner component should overflow and use calculated width/height.\n    // See issue #68 for more information.\n    const outerStyle = {\n      overflow: \"visible\"\n    };\n    const childParams = {};\n\n    // Avoid rendering children before the initial measurements have been collected.\n    // At best this would just be wasting cycles.\n    let bailoutOnChildren = false;\n    if (!disableHeight) {\n      if (height === 0) {\n        bailoutOnChildren = true;\n      }\n      outerStyle.height = 0;\n      childParams.height = height;\n      childParams.scaledHeight = scaledHeight;\n    }\n    if (!disableWidth) {\n      if (width === 0) {\n        bailoutOnChildren = true;\n      }\n      outerStyle.width = 0;\n      childParams.width = width;\n      childParams.scaledWidth = scaledWidth;\n    }\n    if (doNotBailOutOnEmptyChildren) {\n      bailoutOnChildren = false;\n    }\n    return createElement(tagName, {\n      ref: this._setRef,\n      style: {\n        ...outerStyle,\n        ...style\n      },\n      ...rest\n    }, !bailoutOnChildren && children(childParams));\n  }\n}\n\nfunction isHeightAndWidthProps(props) {\n  return props && props.disableHeight !== true && props.disableWidth !== true;\n}\nfunction isHeightOnlyProps(props) {\n  return props && props.disableHeight !== true && props.disableWidth === true;\n}\nfunction isWidthOnlyProps(props) {\n  return props && props.disableHeight === true && props.disableWidth !== true;\n}\n\nexport { AutoSizer as default, isHeightAndWidthProps, isHeightOnlyProps, isWidthOnlyProps };\n", "import { jsx, jsxs } from 'react/jsx-runtime';\nimport { css } from '@emotion/css';\nimport { memo, useMemo, useState } from 'react';\nimport AutoSizer from 'react-virtualized-auto-sizer';\nimport { FieldType, MappingType, applyFieldOverrides } from '@grafana/data';\nimport { useStyles2, useTheme2, Table, TableCellDisplayMode, IconButton } from '@grafana/ui';\nimport { diffColorBlindColors, diffDefaultColors } from '../FlameGraph/colors.js';\nimport { TOP_TABLE_COLUMN_WIDTH } from '../constants.js';\nimport { ColorSchemeDiff } from '../types.js';\n\nconst FlameGraphTopTableContainer = memo(\n  ({\n    data,\n    onSymbolClick,\n    search,\n    matchedLabels,\n    onSearch,\n    sandwichItem,\n    onSandwich,\n    onTableSort,\n    colorScheme\n  }) => {\n    const table = useMemo(() => {\n      let filteredTable = {};\n      for (let i = 0; i < data.data.length; i++) {\n        const value = data.getValue(i);\n        const valueRight = data.getValueRight(i);\n        const self = data.getSelf(i);\n        const label = data.getLabel(i);\n        if (!matchedLabels || matchedLabels.has(label)) {\n          filteredTable[label] = filteredTable[label] || {};\n          filteredTable[label].self = filteredTable[label].self ? filteredTable[label].self + self : self;\n          filteredTable[label].total = filteredTable[label].total ? filteredTable[label].total + value : value;\n          filteredTable[label].totalRight = filteredTable[label].totalRight ? filteredTable[label].totalRight + valueRight : valueRight;\n        }\n      }\n      return filteredTable;\n    }, [data, matchedLabels]);\n    const styles = useStyles2(getStyles);\n    const theme = useTheme2();\n    const [sort, setSort] = useState([{ displayName: \"Self\", desc: true }]);\n    return /* @__PURE__ */ jsx(\"div\", { className: styles.topTableContainer, \"data-testid\": \"topTable\", children: /* @__PURE__ */ jsx(AutoSizer, { style: { width: \"100%\" }, children: ({ width, height }) => {\n      if (width < 3 || height < 3) {\n        return null;\n      }\n      const frame = buildTableDataFrame(\n        data,\n        table,\n        width,\n        onSymbolClick,\n        onSearch,\n        onSandwich,\n        theme,\n        colorScheme,\n        search,\n        sandwichItem\n      );\n      return /* @__PURE__ */ jsx(\n        Table,\n        {\n          initialSortBy: sort,\n          onSortByChange: (s) => {\n            if (s && s.length) {\n              onTableSort == null ? void 0 : onTableSort(s[0].displayName + \"_\" + (s[0].desc ? \"desc\" : \"asc\"));\n            }\n            setSort(s);\n          },\n          data: frame,\n          width,\n          height\n        }\n      );\n    } }) });\n  }\n);\nFlameGraphTopTableContainer.displayName = \"FlameGraphTopTableContainer\";\nfunction buildTableDataFrame(data, table, width, onSymbolClick, onSearch, onSandwich, theme, colorScheme, search, sandwichItem) {\n  const actionField = createActionField(onSandwich, onSearch, search, sandwichItem);\n  const symbolField = {\n    type: FieldType.string,\n    name: \"Symbol\",\n    values: [],\n    config: {\n      custom: { width: width - actionColumnWidth - TOP_TABLE_COLUMN_WIDTH * 2 },\n      links: [\n        {\n          title: \"Highlight symbol\",\n          url: \"\",\n          onClick: (e) => {\n            const field = e.origin.field;\n            const value = field.values[e.origin.rowIndex];\n            onSymbolClick(value);\n          }\n        }\n      ]\n    }\n  };\n  let frame;\n  if (data.isDiffFlamegraph()) {\n    symbolField.config.custom.width = width - actionColumnWidth - TOP_TABLE_COLUMN_WIDTH * 3;\n    const baselineField = createNumberField(\"Baseline\", \"percent\");\n    const comparisonField = createNumberField(\"Comparison\", \"percent\");\n    const diffField = createNumberField(\"Diff\", \"percent\");\n    diffField.config.custom.cellOptions.type = TableCellDisplayMode.ColorText;\n    const [removeColor, addColor] = colorScheme === ColorSchemeDiff.DiffColorBlind ? [diffColorBlindColors[0], diffColorBlindColors[2]] : [diffDefaultColors[0], diffDefaultColors[2]];\n    diffField.config.mappings = [\n      { type: MappingType.ValueToText, options: { [Infinity]: { text: \"new\", color: addColor } } },\n      { type: MappingType.ValueToText, options: { [-100]: { text: \"removed\", color: removeColor } } },\n      { type: MappingType.RangeToText, options: { from: 0, to: Infinity, result: { color: addColor } } },\n      { type: MappingType.RangeToText, options: { from: -Infinity, to: 0, result: { color: removeColor } } }\n    ];\n    const levels = data.getLevels();\n    const totalTicks = levels.length ? levels[0][0].value : 0;\n    const totalTicksRight = levels.length ? levels[0][0].valueRight : void 0;\n    for (let key in table) {\n      actionField.values.push(null);\n      symbolField.values.push(key);\n      const ticksLeft = table[key].total;\n      const ticksRight = table[key].totalRight;\n      const totalTicksLeft = totalTicks - totalTicksRight;\n      const percentageLeft = Math.round(1e4 * ticksLeft / totalTicksLeft) / 100;\n      const percentageRight = Math.round(1e4 * ticksRight / totalTicksRight) / 100;\n      const diff = (percentageRight - percentageLeft) / percentageLeft * 100;\n      diffField.values.push(diff);\n      baselineField.values.push(percentageLeft);\n      comparisonField.values.push(percentageRight);\n    }\n    frame = {\n      fields: [actionField, symbolField, baselineField, comparisonField, diffField],\n      length: symbolField.values.length\n    };\n  } else {\n    const selfField = createNumberField(\"Self\", data.selfField.config.unit);\n    const totalField = createNumberField(\"Total\", data.valueField.config.unit);\n    for (let key in table) {\n      actionField.values.push(null);\n      symbolField.values.push(key);\n      selfField.values.push(table[key].self);\n      totalField.values.push(table[key].total);\n    }\n    frame = { fields: [actionField, symbolField, selfField, totalField], length: symbolField.values.length };\n  }\n  const dataFrames = applyFieldOverrides({\n    data: [frame],\n    fieldConfig: {\n      defaults: {},\n      overrides: []\n    },\n    replaceVariables: (value) => value,\n    theme\n  });\n  return dataFrames[0];\n}\nfunction createNumberField(name, unit) {\n  const tableFieldOptions = {\n    width: TOP_TABLE_COLUMN_WIDTH,\n    align: \"auto\",\n    inspect: false,\n    cellOptions: { type: TableCellDisplayMode.Auto }\n  };\n  return {\n    type: FieldType.number,\n    name,\n    values: [],\n    config: {\n      unit,\n      custom: tableFieldOptions\n    }\n  };\n}\nconst actionColumnWidth = 61;\nfunction createActionField(onSandwich, onSearch, search, sandwichItem) {\n  const options = {\n    type: TableCellDisplayMode.Custom,\n    cellComponent: (props) => {\n      return /* @__PURE__ */ jsx(\n        ActionCell,\n        {\n          frame: props.frame,\n          onSandwich,\n          onSearch,\n          search,\n          sandwichItem,\n          rowIndex: props.rowIndex\n        }\n      );\n    }\n  };\n  const actionFieldTableConfig = {\n    filterable: false,\n    width: actionColumnWidth,\n    hideHeader: true,\n    inspect: false,\n    align: \"auto\",\n    cellOptions: options\n  };\n  return {\n    type: FieldType.number,\n    name: \"actions\",\n    values: [],\n    config: {\n      custom: actionFieldTableConfig\n    }\n  };\n}\nfunction ActionCell(props) {\n  var _a;\n  const styles = getStylesActionCell();\n  const symbol = (_a = props.frame.fields.find((f) => f.name === \"Symbol\")) == null ? void 0 : _a.values[props.rowIndex];\n  const isSearched = props.search === symbol;\n  const isSandwiched = props.sandwichItem === symbol;\n  return /* @__PURE__ */ jsxs(\"div\", { className: styles.actionCellWrapper, children: [\n    /* @__PURE__ */ jsx(\n      IconButton,\n      {\n        className: styles.actionCellButton,\n        name: \"search\",\n        variant: isSearched ? \"primary\" : \"secondary\",\n        tooltip: isSearched ? \"Clear from search\" : \"Search for symbol\",\n        \"aria-label\": isSearched ? \"Clear from search\" : \"Search for symbol\",\n        onClick: () => {\n          props.onSearch(isSearched ? \"\" : symbol);\n        }\n      }\n    ),\n    /* @__PURE__ */ jsx(\n      IconButton,\n      {\n        className: styles.actionCellButton,\n        name: \"gf-show-context\",\n        tooltip: isSandwiched ? \"Remove from sandwich view\" : \"Show in sandwich view\",\n        variant: isSandwiched ? \"primary\" : \"secondary\",\n        \"aria-label\": isSandwiched ? \"Remove from sandwich view\" : \"Show in sandwich view\",\n        onClick: () => {\n          props.onSandwich(isSandwiched ? void 0 : symbol);\n        }\n      }\n    )\n  ] });\n}\nconst getStyles = (theme) => {\n  return {\n    topTableContainer: css({\n      label: \"topTableContainer\",\n      padding: theme.spacing(1),\n      backgroundColor: theme.colors.background.secondary,\n      height: \"100%\"\n    })\n  };\n};\nconst getStylesActionCell = () => {\n  return {\n    actionCellWrapper: css({\n      label: \"actionCellWrapper\",\n      display: \"flex\",\n      height: \"24px\"\n    }),\n    actionCellButton: css({\n      label: \"actionCellButton\",\n      marginRight: 0,\n      width: \"24px\"\n    })\n  };\n};\n\nexport { FlameGraphTopTableContainer as default };\n//# sourceMappingURL=FlameGraphTopTableContainer.js.map\n", "import { jsx, jsxs } from 'react/jsx-runtime';\nimport { css } from '@emotion/css';\nimport uFuzzy from '@leeoniya/ufuzzy';\nimport { useState, useMemo, useEffect, useCallback } from 'react';\nimport { useMeasure } from 'react-use';\nimport { ThemeContext } from '@grafana/ui';\nimport FlameGraph from './FlameGraph/FlameGraph.js';\nimport { CollapsedMap, FlameGraphDataContainer } from './FlameGraph/dataTransform.js';\nimport FlameGraphHeader from './FlameGraphHeader.js';\nimport FlameGraphTopTableContainer from './TopTable/FlameGraphTopTableContainer.js';\nimport { MIN_WIDTH_TO_SHOW_BOTH_TOPTABLE_AND_FLAMEGRAPH } from './constants.js';\nimport { SelectedView, ColorSchemeDiff, ColorScheme } from './types.js';\n\nconst ufuzzy = new uFuzzy();\nconst FlameGraphContainer = ({\n  data,\n  onTableSymbolClick,\n  onViewSelected,\n  onTextAlignSelected,\n  onTableSort,\n  getTheme,\n  stickyHeader,\n  extraHeaderElements,\n  vertical,\n  showFlameGraphOnly,\n  disableCollapsing,\n  keepFocusOnDataChange,\n  getExtraContextMenuButtons\n}) => {\n  const [focusedItemData, setFocusedItemData] = useState();\n  const [rangeMin, setRangeMin] = useState(0);\n  const [rangeMax, setRangeMax] = useState(1);\n  const [search, setSearch] = useState(\"\");\n  const [selectedView, setSelectedView] = useState(SelectedView.Both);\n  const [sizeRef, { width: containerWidth }] = useMeasure();\n  const [textAlign, setTextAlign] = useState(\"left\");\n  const [sandwichItem, setSandwichItem] = useState();\n  const [collapsedMap, setCollapsedMap] = useState(new CollapsedMap());\n  const theme = useMemo(() => getTheme(), [getTheme]);\n  const dataContainer = useMemo(() => {\n    if (!data) {\n      return;\n    }\n    const container = new FlameGraphDataContainer(data, { collapsing: !disableCollapsing }, theme);\n    setCollapsedMap(container.getCollapsedMap());\n    return container;\n  }, [data, theme, disableCollapsing]);\n  const [colorScheme, setColorScheme] = useColorScheme(dataContainer);\n  const styles = getStyles(theme);\n  const matchedLabels = useLabelSearch(search, dataContainer);\n  useEffect(() => {\n    if (containerWidth > 0 && containerWidth < MIN_WIDTH_TO_SHOW_BOTH_TOPTABLE_AND_FLAMEGRAPH && selectedView === SelectedView.Both && !vertical) {\n      setSelectedView(SelectedView.FlameGraph);\n    }\n  }, [selectedView, setSelectedView, containerWidth, vertical]);\n  const resetFocus = useCallback(() => {\n    setFocusedItemData(void 0);\n    setRangeMin(0);\n    setRangeMax(1);\n  }, [setFocusedItemData, setRangeMax, setRangeMin]);\n  const resetSandwich = useCallback(() => {\n    setSandwichItem(void 0);\n  }, [setSandwichItem]);\n  useEffect(() => {\n    var _a;\n    if (!keepFocusOnDataChange) {\n      resetFocus();\n      resetSandwich();\n      return;\n    }\n    if (dataContainer && focusedItemData) {\n      const item = (_a = dataContainer.getNodesWithLabel(focusedItemData.label)) == null ? void 0 : _a[0];\n      if (item) {\n        setFocusedItemData({ ...focusedItemData, item });\n        const levels = dataContainer.getLevels();\n        const totalViewTicks = levels.length ? levels[0][0].value : 0;\n        setRangeMin(item.start / totalViewTicks);\n        setRangeMax((item.start + item.value) / totalViewTicks);\n      } else {\n        setFocusedItemData({\n          ...focusedItemData,\n          item: {\n            start: 0,\n            value: 0,\n            itemIndexes: [],\n            children: [],\n            level: 0\n          }\n        });\n        setRangeMin(0);\n        setRangeMax(1);\n      }\n    }\n  }, [dataContainer, keepFocusOnDataChange]);\n  const onSymbolClick = useCallback(\n    (symbol) => {\n      if (search === symbol) {\n        setSearch(\"\");\n      } else {\n        onTableSymbolClick == null ? void 0 : onTableSymbolClick(symbol);\n        setSearch(symbol);\n        resetFocus();\n      }\n    },\n    [setSearch, resetFocus, onTableSymbolClick, search]\n  );\n  if (!dataContainer) {\n    return null;\n  }\n  const flameGraph = /* @__PURE__ */ jsx(\n    FlameGraph,\n    {\n      data: dataContainer,\n      rangeMin,\n      rangeMax,\n      matchedLabels,\n      setRangeMin,\n      setRangeMax,\n      onItemFocused: (data2) => setFocusedItemData(data2),\n      focusedItemData,\n      textAlign,\n      sandwichItem,\n      onSandwich: (label) => {\n        resetFocus();\n        setSandwichItem(label);\n      },\n      onFocusPillClick: resetFocus,\n      onSandwichPillClick: resetSandwich,\n      colorScheme,\n      showFlameGraphOnly,\n      collapsing: !disableCollapsing,\n      getExtraContextMenuButtons,\n      selectedView,\n      search,\n      collapsedMap,\n      setCollapsedMap\n    }\n  );\n  const table = /* @__PURE__ */ jsx(\n    FlameGraphTopTableContainer,\n    {\n      data: dataContainer,\n      onSymbolClick,\n      search,\n      matchedLabels,\n      sandwichItem,\n      onSandwich: setSandwichItem,\n      onSearch: setSearch,\n      onTableSort,\n      colorScheme\n    }\n  );\n  let body;\n  if (showFlameGraphOnly || selectedView === SelectedView.FlameGraph) {\n    body = flameGraph;\n  } else if (selectedView === SelectedView.TopTable) {\n    body = /* @__PURE__ */ jsx(\"div\", { className: styles.tableContainer, children: table });\n  } else if (selectedView === SelectedView.Both) {\n    if (vertical) {\n      body = /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx(\"div\", { className: styles.verticalGraphContainer, children: flameGraph }),\n        /* @__PURE__ */ jsx(\"div\", { className: styles.verticalTableContainer, children: table })\n      ] });\n    } else {\n      body = /* @__PURE__ */ jsxs(\"div\", { className: styles.horizontalContainer, children: [\n        /* @__PURE__ */ jsx(\"div\", { className: styles.horizontalTableContainer, children: table }),\n        /* @__PURE__ */ jsx(\"div\", { className: styles.horizontalGraphContainer, children: flameGraph })\n      ] });\n    }\n  }\n  return (\n    // We add the theme context to bridge the gap if this is rendered in non grafana environment where the context\n    // isn't already provided.\n    /* @__PURE__ */ jsx(ThemeContext.Provider, { value: theme, children: /* @__PURE__ */ jsxs(\"div\", { ref: sizeRef, className: styles.container, children: [\n      !showFlameGraphOnly && /* @__PURE__ */ jsx(\n        FlameGraphHeader,\n        {\n          search,\n          setSearch,\n          selectedView,\n          setSelectedView: (view) => {\n            setSelectedView(view);\n            onViewSelected == null ? void 0 : onViewSelected(view);\n          },\n          containerWidth,\n          onReset: () => {\n            resetFocus();\n            resetSandwich();\n          },\n          textAlign,\n          onTextAlignChange: (align) => {\n            setTextAlign(align);\n            onTextAlignSelected == null ? void 0 : onTextAlignSelected(align);\n          },\n          showResetButton: Boolean(focusedItemData || sandwichItem),\n          colorScheme,\n          onColorSchemeChange: setColorScheme,\n          stickyHeader: Boolean(stickyHeader),\n          extraHeaderElements,\n          vertical,\n          isDiffMode: dataContainer.isDiffFlamegraph(),\n          setCollapsedMap,\n          collapsedMap\n        }\n      ),\n      /* @__PURE__ */ jsx(\"div\", { className: styles.body, children: body })\n    ] }) })\n  );\n};\nfunction useColorScheme(dataContainer) {\n  const defaultColorScheme = (dataContainer == null ? void 0 : dataContainer.isDiffFlamegraph()) ? ColorSchemeDiff.Default : ColorScheme.PackageBased;\n  const [colorScheme, setColorScheme] = useState(defaultColorScheme);\n  useEffect(() => {\n    setColorScheme(defaultColorScheme);\n  }, [defaultColorScheme]);\n  return [colorScheme, setColorScheme];\n}\nfunction useLabelSearch(search, data) {\n  return useMemo(() => {\n    if (search && data) {\n      const foundLabels = /* @__PURE__ */ new Set();\n      let idxs = ufuzzy.filter(data.getUniqueLabels(), search);\n      if (idxs) {\n        for (let idx of idxs) {\n          foundLabels.add(data.getUniqueLabels()[idx]);\n        }\n      }\n      return foundLabels;\n    }\n    return void 0;\n  }, [search, data]);\n}\nfunction getStyles(theme) {\n  return {\n    container: css({\n      label: \"container\",\n      overflow: \"auto\",\n      height: \"100%\",\n      display: \"flex\",\n      flex: \"1 1 0\",\n      flexDirection: \"column\",\n      minHeight: 0,\n      gap: theme.spacing(1)\n    }),\n    body: css({\n      label: \"body\",\n      flexGrow: 1\n    }),\n    tableContainer: css({\n      // This is not ideal for dashboard panel where it creates a double scroll. In a panel it should be 100% but then\n      // in explore we need a specific height.\n      height: 800\n    }),\n    horizontalContainer: css({\n      label: \"horizontalContainer\",\n      display: \"flex\",\n      minHeight: 0,\n      flexDirection: \"row\",\n      columnGap: theme.spacing(1),\n      width: \"100%\"\n    }),\n    horizontalGraphContainer: css({\n      flexBasis: \"50%\"\n    }),\n    horizontalTableContainer: css({\n      flexBasis: \"50%\",\n      maxHeight: 800\n    }),\n    verticalGraphContainer: css({\n      marginBottom: theme.spacing(1)\n    }),\n    verticalTableContainer: css({\n      height: 800\n    })\n  };\n}\n\nexport { FlameGraphContainer as default };\n//# sourceMappingURL=FlameGraphContainer.js.map\n", "(function(a,b){if(\"function\"==typeof define&&define.amd)define([],b);else if(\"undefined\"!=typeof exports)b();else{b(),a.FileSaver={exports:{}}.exports}})(this,function(){\"use strict\";function b(a,b){return\"undefined\"==typeof b?b={autoBom:!1}:\"object\"!=typeof b&&(console.warn(\"Deprecated: Expected third argument to be a object\"),b={autoBom:!b}),b.autoBom&&/^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(a.type)?new Blob([\"\\uFEFF\",a],{type:a.type}):a}function c(a,b,c){var d=new XMLHttpRequest;d.open(\"GET\",a),d.responseType=\"blob\",d.onload=function(){g(d.response,b,c)},d.onerror=function(){console.error(\"could not download file\")},d.send()}function d(a){var b=new XMLHttpRequest;b.open(\"HEAD\",a,!1);try{b.send()}catch(a){}return 200<=b.status&&299>=b.status}function e(a){try{a.dispatchEvent(new MouseEvent(\"click\"))}catch(c){var b=document.createEvent(\"MouseEvents\");b.initMouseEvent(\"click\",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),a.dispatchEvent(b)}}var f=\"object\"==typeof window&&window.window===window?window:\"object\"==typeof self&&self.self===self?self:\"object\"==typeof global&&global.global===global?global:void 0,a=f.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),g=f.saveAs||(\"object\"!=typeof window||window!==f?function(){}:\"download\"in HTMLAnchorElement.prototype&&!a?function(b,g,h){var i=f.URL||f.webkitURL,j=document.createElement(\"a\");g=g||b.name||\"download\",j.download=g,j.rel=\"noopener\",\"string\"==typeof b?(j.href=b,j.origin===location.origin?e(j):d(j.href)?c(b,g,h):e(j,j.target=\"_blank\")):(j.href=i.createObjectURL(b),setTimeout(function(){i.revokeObjectURL(j.href)},4E4),setTimeout(function(){e(j)},0))}:\"msSaveOrOpenBlob\"in navigator?function(f,g,h){if(g=g||f.name||\"download\",\"string\"!=typeof f)navigator.msSaveOrOpenBlob(b(f,h),g);else if(d(f))c(f,g,h);else{var i=document.createElement(\"a\");i.href=f,i.target=\"_blank\",setTimeout(function(){e(i)})}}:function(b,d,e,g){if(g=g||open(\"\",\"_blank\"),g&&(g.document.title=g.document.body.innerText=\"downloading...\"),\"string\"==typeof b)return c(b,d,e);var h=\"application/octet-stream\"===b.type,i=/constructor/i.test(f.HTMLElement)||f.safari,j=/CriOS\\/[\\d]+/.test(navigator.userAgent);if((j||h&&i||a)&&\"undefined\"!=typeof FileReader){var k=new FileReader;k.onloadend=function(){var a=k.result;a=j?a:a.replace(/^data:[^;]*;/,\"data:attachment/file;\"),g?g.location.href=a:location=a,g=null},k.readAsDataURL(b)}else{var l=f.URL||f.webkitURL,m=l.createObjectURL(b);g?g.location=m:location.href=m,g=null,setTimeout(function(){l.revokeObjectURL(m)},4E4)}});f.saveAs=g.saveAs=g,\"undefined\"!=typeof module&&(module.exports=g)});\n\n//# sourceMappingURL=FileSaver.min.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar tslib_1 = require(\"tslib\");\nvar react_1 = require(\"react\");\nvar useTimeoutFn_1 = tslib_1.__importDefault(require(\"./useTimeoutFn\"));\nfunction useDebounce(fn, ms, deps) {\n    if (ms === void 0) { ms = 0; }\n    if (deps === void 0) { deps = []; }\n    var _a = useTimeoutFn_1.default(fn, ms), isReady = _a[0], cancel = _a[1], reset = _a[2];\n    react_1.useEffect(reset, deps);\n    return [isReady, cancel];\n}\nexports.default = useDebounce;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar react_1 = require(\"react\");\nfunction usePrevious(state) {\n    var ref = react_1.useRef();\n    react_1.useEffect(function () {\n        ref.current = state;\n    });\n    return ref.current;\n}\nexports.default = usePrevious;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar react_1 = require(\"react\");\nfunction useTimeoutFn(fn, ms) {\n    if (ms === void 0) { ms = 0; }\n    var ready = react_1.useRef(false);\n    var timeout = react_1.useRef();\n    var callback = react_1.useRef(fn);\n    var isReady = react_1.useCallback(function () { return ready.current; }, []);\n    var set = react_1.useCallback(function () {\n        ready.current = false;\n        timeout.current && clearTimeout(timeout.current);\n        timeout.current = setTimeout(function () {\n            ready.current = true;\n            callback.current();\n        }, ms);\n    }, [ms]);\n    var clear = react_1.useCallback(function () {\n        ready.current = null;\n        timeout.current && clearTimeout(timeout.current);\n    }, []);\n    // update ref when function changes\n    react_1.useEffect(function () {\n        callback.current = fn;\n    }, [fn]);\n    // set on mount, clear on unmount\n    react_1.useEffect(function () {\n        set();\n        return clear;\n    }, [ms]);\n    return [isReady, clear, set];\n}\nexports.default = useTimeoutFn;\n", "// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\nvar ch2 = {};\nvar wk = (function (c, id, msg, transfer, cb) {\n    var w = new Worker(ch2[id] || (ch2[id] = URL.createObjectURL(new Blob([\n        c + ';addEventListener(\"error\",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'\n    ], { type: 'text/javascript' }))));\n    w.onmessage = function (e) {\n        var d = e.data, ed = d.$e$;\n        if (ed) {\n            var err = new Error(ed[0]);\n            err['code'] = ed[1];\n            err.stack = ed[2];\n            cb(err, null);\n        }\n        else\n            cb(null, d);\n    };\n    w.postMessage(msg, transfer);\n    return w;\n});\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, i32 = Int32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */ 0, 0, /* impossible */ 0]);\n// fixed distance extra bits\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */ 0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n    var b = new u16(31);\n    for (var i = 0; i < 31; ++i) {\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new i32(b[30]);\n    for (var i = 1; i < 30; ++i) {\n        for (var j = b[i]; j < b[i + 1]; ++j) {\n            r[j] = ((j - b[i]) << 5) | i;\n        }\n    }\n    return { b: b, r: r };\n};\nvar _a = freb(fleb, 2), fl = _a.b, revfl = _a.r;\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b.b, revfd = _b.r;\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n    // reverse table algorithm from SO\n    var x = ((i & 0xAAAA) >> 1) | ((i & 0x5555) << 1);\n    x = ((x & 0xCCCC) >> 2) | ((x & 0x3333) << 2);\n    x = ((x & 0xF0F0) >> 4) | ((x & 0x0F0F) << 4);\n    rev[i] = (((x & 0xFF00) >> 8) | ((x & 0x00FF) << 8)) >> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = (function (cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for (; i < s; ++i) {\n        if (cd[i])\n            ++l[cd[i] - 1];\n    }\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for (i = 1; i < mb; ++i) {\n        le[i] = (le[i - 1] + l[i - 1]) << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for (i = 0; i < s; ++i) {\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = (i << 4) | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for (var m = v | ((1 << r_1) - 1); v <= m; ++v) {\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >> rvb] = sv;\n                }\n            }\n        }\n    }\n    else {\n        co = new u16(s);\n        for (i = 0; i < s; ++i) {\n            if (cd[i]) {\n                co[i] = rev[le[cd[i] - 1]++] >> (15 - cd[i]);\n            }\n        }\n    }\n    return co;\n});\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i)\n    flt[i] = 8;\nfor (var i = 144; i < 256; ++i)\n    flt[i] = 9;\nfor (var i = 256; i < 280; ++i)\n    flt[i] = 7;\nfor (var i = 280; i < 288; ++i)\n    flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i)\n    fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n    var m = a[0];\n    for (var i = 1; i < a.length; ++i) {\n        if (a[i] > m)\n            m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8)) >> (p & 7)) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8) | (d[o + 2] << 16)) >> (p & 7));\n};\n// get end of byte\nvar shft = function (p) { return ((p + 7) / 8) | 0; };\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    // can't use .constructor in case user-supplied\n    return new u8(v.subarray(s, e));\n};\n/**\n * Codes for errors generated within this library\n */\nexport var FlateErrorCode = {\n    UnexpectedEOF: 0,\n    InvalidBlockType: 1,\n    InvalidLengthLiteral: 2,\n    InvalidDistance: 3,\n    StreamFinished: 4,\n    NoStreamHandler: 5,\n    InvalidHeader: 6,\n    NoCallback: 7,\n    InvalidUTF8: 8,\n    ExtraFieldTooLong: 9,\n    InvalidDate: 10,\n    FilenameTooLong: 11,\n    StreamFinishing: 12,\n    InvalidZipData: 13,\n    UnknownCompressionMethod: 14\n};\n// error codes\nvar ec = [\n    'unexpected EOF',\n    'invalid block type',\n    'invalid length/literal',\n    'invalid distance',\n    'stream finished',\n    'no stream handler',\n    ,\n    'no callback',\n    'invalid UTF-8 data',\n    'extra field too long',\n    'date not in range 1980-2099',\n    'filename too long',\n    'stream finishing',\n    'invalid zip data'\n    // determined by unknown compression method\n];\n;\nvar err = function (ind, msg, nt) {\n    var e = new Error(msg || ec[ind]);\n    e.code = ind;\n    if (Error.captureStackTrace)\n        Error.captureStackTrace(e, err);\n    if (!nt)\n        throw e;\n    return e;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, st, buf, dict) {\n    // source length       dict length\n    var sl = dat.length, dl = dict ? dict.length : 0;\n    if (!sl || st.f && !st.l)\n        return buf || new u8(0);\n    var noBuf = !buf;\n    // have to estimate size\n    var resize = noBuf || st.i != 2;\n    // no state\n    var noSt = st.i;\n    // Assumes roughly 33% compression ratio average\n    if (noBuf)\n        buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function (l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | (dat[s - 3] << 8), t = s + l;\n                if (t > sl) {\n                    if (noSt)\n                        err(0);\n                    break;\n                }\n                // ensure size\n                if (resize)\n                    cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8, st.f = final;\n                continue;\n            }\n            else if (type == 1)\n                lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for (var i = 0; i < hcLen; ++i) {\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for (var i = 0; i < tl;) {\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    }\n                    else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16)\n                            n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17)\n                            n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18)\n                            n = 11 + bits(dat, pos, 127), pos += 7;\n                        while (n--)\n                            ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            }\n            else\n                err(1);\n            if (pos > tbts) {\n                if (noSt)\n                    err(0);\n                break;\n            }\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17\n        if (resize)\n            cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var lpos = pos;\n        for (;; lpos = pos) {\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >> 4;\n            pos += c & 15;\n            if (pos > tbts) {\n                if (noSt)\n                    err(0);\n                break;\n            }\n            if (!c)\n                err(2);\n            if (sym < 256)\n                buf[bt++] = sym;\n            else if (sym == 256) {\n                lpos = pos, lm = null;\n                break;\n            }\n            else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >> 4;\n                if (!d)\n                    err(3);\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & (1 << b) - 1, pos += b;\n                }\n                if (pos > tbts) {\n                    if (noSt)\n                        err(0);\n                    break;\n                }\n                if (resize)\n                    cbuf(bt + 131072);\n                var end = bt + add;\n                if (bt < dt) {\n                    var shift = dl - dt, dend = Math.min(dt, end);\n                    if (shift + bt < 0)\n                        err(3);\n                    for (; bt < dend; ++bt)\n                        buf[bt] = dict[shift + bt];\n                }\n                for (; bt < end; ++bt)\n                    buf[bt] = buf[bt - dt];\n            }\n        }\n        st.l = lm, st.p = lpos, st.b = bt, st.f = final;\n        if (lm)\n            final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    } while (!final);\n    // don't reallocate for streams or user buffers\n    return bt != buf.length && noBuf ? slc(buf, 0, bt) : buf.subarray(0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >> 8;\n    d[o + 2] |= v >> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for (var i = 0; i < d.length; ++i) {\n        if (d[i])\n            t.push({ s: i, f: d[i] });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s)\n        return { t: et, l: 0 };\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return { t: v, l: 1 };\n    }\n    t.sort(function (a, b) { return a.f - b.f; });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({ s: -1, f: 25001 });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = { s: -1, f: l.f + r.f, l: l, r: r };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while (i1 != s - 1) {\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = { s: -1, f: l.f + r.f, l: l, r: r };\n    }\n    var maxSym = t2[0].s;\n    for (var i = 1; i < s; ++i) {\n        if (t2[i].s > maxSym)\n            maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function (a, b) { return tr[b.s] - tr[a.s] || a.f - b.f; });\n        for (; i < s; ++i) {\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << (mbt - tr[i2_1]));\n                tr[i2_1] = mb;\n            }\n            else\n                break;\n        }\n        dt >>= lft;\n        while (dt > 0) {\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb)\n                dt -= 1 << (mb - tr[i2_2]++ - 1);\n            else\n                ++i;\n        }\n        for (; i >= 0 && dt; --i) {\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return { t: new u8(tr), l: mbt };\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n    return n.s == -1\n        ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1))\n        : (l[n.s] = d);\n};\n// length codes generation\nvar lc = function (c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while (s && !c[--s])\n        ;\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function (v) { cl[cli++] = v; };\n    for (var i = 1; i <= s; ++i) {\n        if (c[i] == cln && i != s)\n            ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for (; cls > 138; cls -= 138)\n                    w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? ((cls - 11) << 5) | 28690 : ((cls - 3) << 5) | 12305);\n                    cls = 0;\n                }\n            }\n            else if (cls > 3) {\n                w(cln), --cls;\n                for (; cls > 6; cls -= 6)\n                    w(8304);\n                if (cls > 2)\n                    w(((cls - 3) << 5) | 8208), cls = 0;\n            }\n            while (cls--)\n                w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return { c: cl.subarray(0, cli), n: s };\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n    var l = 0;\n    for (var i = 0; i < cl.length; ++i)\n        l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for (var i = 0; i < s; ++i)\n        out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a.t, mlb = _a.l;\n    var _b = hTree(df, 15), ddt = _b.t, mdb = _b.l;\n    var _c = lc(dlt), lclt = _c.c, nlc = _c.n;\n    var _d = lc(ddt), lcdt = _d.c, ndc = _d.n;\n    var lcfreq = new u16(19);\n    for (var i = 0; i < lclt.length; ++i)\n        ++lcfreq[lclt[i] & 31];\n    for (var i = 0; i < lcdt.length; ++i)\n        ++lcfreq[lcdt[i] & 31];\n    var _e = hTree(lcfreq, 7), lct = _e.t, mlcb = _e.l;\n    var nlcc = 19;\n    for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc)\n        ;\n    var flen = (bl + 5) << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + 2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18];\n    if (bs >= 0 && flen <= ftlen && flen <= dtlen)\n        return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for (var i = 0; i < nlcc; ++i)\n            wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [lclt, lcdt];\n        for (var it = 0; it < 2; ++it) {\n            var clct = lcts[it];\n            for (var i = 0; i < clct.length; ++i) {\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15)\n                    wbits(out, p, (clct[i] >> 5) & 127), p += clct[i] >> 12;\n            }\n        }\n    }\n    else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for (var i = 0; i < li; ++i) {\n        var sym = syms[i];\n        if (sym > 255) {\n            var len = (sym >> 18) & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7)\n                wbits(out, p, (sym >> 23) & 31), p += fleb[len];\n            var dst = sym & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3)\n                wbits16(out, p, (sym >> 5) & 8191), p += fdeb[dst];\n        }\n        else {\n            wbits16(out, p, lm[sym]), p += ll[sym];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new i32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, st) {\n    var s = st.z || dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var lst = st.l;\n    var pos = (st.r || 0) & 7;\n    if (lvl) {\n        if (pos)\n            w[0] = st.r >> 3;\n        var opt = deo[lvl - 1];\n        var n = opt >> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = st.p || new u16(32768), head = st.h || new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function (i) { return (dat[i] ^ (dat[i + 1] << bs1_1) ^ (dat[i + 2] << bs2_1)) & msk_1; };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new i32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index          l/lind  waitdx          blkpos\n        var lc_1 = 0, eb = 0, i = st.i || 0, li = 0, wi = st.w || 0, bs = 0;\n        for (; i + 2 < s; ++i) {\n            // hash value\n            var hv = hsh(i);\n            // index mod 32768    previous index mod\n            var imod = i & 32767, pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && (rem > 423 || !lst)) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for (var j = 0; j < 286; ++j)\n                        lf[j] = 0;\n                    for (var j = 0; j < 30; ++j)\n                        df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = imod - pimod & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while (dif <= maxd && --ch_1 && imod != pimod) {\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl)\n                                ;\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn)\n                                    break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for (var j = 0; j < mmd; ++j) {\n                                    var ti = i - dif + j & 32767;\n                                    var pti = prev[ti];\n                                    var cd = ti - pti & 32767;\n                                    if (cd > md)\n                                        md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += imod - pimod & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one int32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | (revfl[l] << 18) | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                }\n                else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        for (i = Math.max(i, wi); i < s; ++i) {\n            syms[li++] = dat[i];\n            ++lf[dat[i]];\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        if (!lst) {\n            st.r = (pos & 7) | w[(pos / 8) | 0] << 3;\n            // shft(pos) now 1 less if pos & 7 != 0\n            pos -= 7;\n            st.h = head, st.p = prev, st.i = i, st.w = wi;\n        }\n    }\n    else {\n        for (var i = st.w || 0; i < s + lst; i += 65535) {\n            // end\n            var e = i + 65535;\n            if (e >= s) {\n                // write final block\n                w[(pos / 8) | 0] = lst;\n                e = s;\n            }\n            pos = wfblk(w, pos + 1, dat.subarray(i, e));\n        }\n        st.i = s;\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ (function () {\n    var t = new Int32Array(256);\n    for (var i = 0; i < 256; ++i) {\n        var c = i, k = 9;\n        while (--k)\n            c = ((c & 1) && -306674912) ^ (c >>> 1);\n        t[i] = c;\n    }\n    return t;\n})();\n// CRC32\nvar crc = function () {\n    var c = -1;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var cr = c;\n            for (var i = 0; i < d.length; ++i)\n                cr = crct[(cr & 255) ^ d[i]] ^ (cr >>> 8);\n            c = cr;\n        },\n        d: function () { return ~c; }\n    };\n};\n// Adler32\nvar adler = function () {\n    var a = 1, b = 0;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length | 0;\n            for (var i = 0; i != l;) {\n                var e = Math.min(i + 2655, l);\n                for (; i < e; ++i)\n                    m += n += d[i];\n                n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n            }\n            a = n, b = m;\n        },\n        d: function () {\n            a %= 65521, b %= 65521;\n            return (a & 255) << 24 | (a & 0xFF00) << 8 | (b & 255) << 8 | (b >> 8);\n        }\n    };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n    if (!st) {\n        st = { l: 1 };\n        if (opt.dictionary) {\n            var dict = opt.dictionary.subarray(-32768);\n            var newDat = new u8(dict.length + dat.length);\n            newDat.set(dict);\n            newDat.set(dat, dict.length);\n            dat = newDat;\n            st.w = dict.length;\n        }\n    }\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? (st.l ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : 20) : (12 + opt.mem), pre, post, st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n    var o = {};\n    for (var k in a)\n        o[k] = a[k];\n    for (var k in b)\n        o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/\\s+/g, '').split(',');\n    for (var i = 0; i < dt.length; ++i) {\n        var v = dt[i], k = ks[i];\n        if (typeof v == 'function') {\n            fnStr += ';' + k + '=';\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf('[native code]') != -1) {\n                    var spInd = st_1.indexOf(' ', 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n                }\n                else {\n                    fnStr += st_1;\n                    for (var t in v.prototype)\n                        fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n                }\n            }\n            else\n                fnStr += st_1;\n        }\n        else\n            td[k] = v;\n    }\n    return fnStr;\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n    var tl = [];\n    for (var k in v) {\n        if (v[k].buffer) {\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n        }\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n    if (!ch[id]) {\n        var fnStr = '', td_1 = {}, m = fns.length - 1;\n        for (var i = 0; i < m; ++i)\n            fnStr = wcln(fns[i], fnStr, td_1);\n        ch[id] = { c: wcln(fns[m], fnStr, td_1), e: td_1 };\n    }\n    var td = mrg({}, ch[id].e);\n    return wk(ch[id].c + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () { return [u8, u16, i32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, ec, hMap, max, bits, bits16, shft, slc, err, inflt, inflateSync, pbf, gopt]; };\nvar bDflt = function () { return [u8, u16, i32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf]; };\n// gzip extra\nvar gze = function () { return [gzh, gzhl, wbytes, crc, crct]; };\n// gunzip extra\nvar guze = function () { return [gzs, gzl]; };\n// zlib extra\nvar zle = function () { return [zlh, wbytes, adler]; };\n// unzlib extra\nvar zule = function () { return [zls]; };\n// post buf\nvar pbf = function (msg) { return postMessage(msg, [msg.buffer]); };\n// get opts\nvar gopt = function (o) { return o && {\n    out: o.size && new u8(o.size),\n    dictionary: o.dictionary\n}; };\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function (err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    w.postMessage([dat, opts], opts.consume ? [dat.buffer] : []);\n    return function () { w.terminate(); };\n};\n// auto stream\nvar astrm = function (strm) {\n    strm.ondata = function (dat, final) { return postMessage([dat, final], [dat.buffer]); };\n    return function (ev) {\n        if (ev.data.length) {\n            strm.push(ev.data[0], ev.data[1]);\n            postMessage([ev.data[0].length]);\n        }\n        else\n            strm.flush();\n    };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id, flush, ext) {\n    var t;\n    var w = wrkr(fns, init, id, function (err, dat) {\n        if (err)\n            w.terminate(), strm.ondata.call(strm, err);\n        else if (!Array.isArray(dat))\n            ext(dat);\n        else if (dat.length == 1) {\n            strm.queuedSize -= dat[0];\n            if (strm.ondrain)\n                strm.ondrain(dat[0]);\n        }\n        else {\n            if (dat[1])\n                w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.queuedSize = 0;\n    strm.push = function (d, f) {\n        if (!strm.ondata)\n            err(5);\n        if (t)\n            strm.ondata(err(4, 0, 1), null, !!f);\n        strm.queuedSize += d.length;\n        w.postMessage([d, t = f], [d.buffer]);\n    };\n    strm.terminate = function () { w.terminate(); };\n    if (flush) {\n        strm.flush = function () { w.postMessage([]); };\n    }\n};\n// read 2 bytes\nvar b2 = function (d, b) { return d[b] | (d[b + 1] << 8); };\n// read 4 bytes\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16) | (d[b + 3] << 24)) >>> 0; };\nvar b8 = function (d, b) { return b4(d, b) + (b4(d, b + 4) * 4294967296); };\n// write bytes\nvar wbytes = function (d, b, v) {\n    for (; v; ++b)\n        d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0)\n        wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for (var i = 0; i <= fn.length; ++i)\n            c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8)\n        err(6, 'invalid gzip data');\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4)\n        st += (d[10] | d[11] << 8) + 2;\n    for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++])\n        ;\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n    var l = d.length;\n    return (d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16 | d[l - 1] << 24) >>> 0;\n};\n// gzip header length\nvar gzhl = function (o) { return 10 + (o.filename ? o.filename.length + 1 : 0); };\n// zlib header\nvar zlh = function (c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = (fl << 6) | (o.dictionary && 32);\n    c[1] |= 31 - ((c[0] << 8) | c[1]) % 31;\n    if (o.dictionary) {\n        var h = adler();\n        h.p(o.dictionary);\n        wbytes(c, 2, h.d());\n    }\n};\n// zlib start\nvar zls = function (d, dict) {\n    if ((d[0] & 15) != 8 || (d[0] >> 4) > 7 || ((d[0] << 8 | d[1]) % 31))\n        err(6, 'invalid zlib data');\n    if ((d[1] >> 5 & 1) == +!dict)\n        err(6, 'invalid zlib data: ' + (d[1] & 32 ? 'need' : 'unexpected') + ' dictionary');\n    return (d[1] >> 3 & 4) + 2;\n};\nfunction StrmOpt(opts, cb) {\n    if (typeof opts == 'function')\n        cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/ (function () {\n    function Deflate(opts, cb) {\n        if (typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n        this.s = { l: 0, i: 32768, w: 32768, z: 32768 };\n        // Buffer length must always be 0 mod 32768 for index calculations to be correct when modifying head and prev\n        // 98304 = 32768 (lookback) + 65536 (common chunk size)\n        this.b = new u8(98304);\n        if (this.o.dictionary) {\n            var dict = this.o.dictionary.subarray(-32768);\n            this.b.set(dict, 32768 - dict.length);\n            this.s.i = 32768 - dict.length;\n        }\n    }\n    Deflate.prototype.p = function (c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, this.s), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Deflate.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            err(5);\n        if (this.s.l)\n            err(4);\n        var endLen = chunk.length + this.s.z;\n        if (endLen > this.b.length) {\n            if (endLen > 2 * this.b.length - 32768) {\n                var newBuf = new u8(endLen & -32768);\n                newBuf.set(this.b.subarray(0, this.s.z));\n                this.b = newBuf;\n            }\n            var split = this.b.length - this.s.z;\n            this.b.set(chunk.subarray(0, split), this.s.z);\n            this.s.z = this.b.length;\n            this.p(this.b, false);\n            this.b.set(this.b.subarray(-32768));\n            this.b.set(chunk.subarray(split), 32768);\n            this.s.z = chunk.length - split + 32768;\n            this.s.i = 32766, this.s.w = 32768;\n        }\n        else {\n            this.b.set(chunk, this.s.z);\n            this.s.z += chunk.length;\n        }\n        this.s.l = final & 1;\n        if (this.s.z > this.s.w + 8191 || final) {\n            this.p(this.b, final || false);\n            this.s.w = this.s.i, this.s.i -= 2;\n        }\n    };\n    /**\n     * Flushes buffered uncompressed data. Useful to immediately retrieve the\n     * deflated output for small inputs.\n     */\n    Deflate.prototype.flush = function () {\n        if (!this.ondata)\n            err(5);\n        if (this.s.l)\n            err(4);\n        this.p(this.b, false);\n        this.s.w = this.s.i, this.s.i -= 2;\n    };\n    return Deflate;\n}());\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/ (function () {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function () { return [astrm, Deflate]; }\n        ], this, StrmOpt.call(this, opts, cb), function (ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6, 1);\n    }\n    return AsyncDeflate;\n}());\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        err(7);\n    return cbify(data, opts, [\n        bDflt,\n    ], function (ev) { return pbf(deflateSync(ev.data[0], ev.data[1])); }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n    return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/ (function () {\n    function Inflate(opts, cb) {\n        // no StrmOpt here to avoid adding to workerizer\n        if (typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        var dict = opts && opts.dictionary && opts.dictionary.subarray(-32768);\n        this.s = { i: 0, b: dict ? dict.length : 0 };\n        this.o = new u8(32768);\n        this.p = new u8(0);\n        if (dict)\n            this.o.set(dict);\n    }\n    Inflate.prototype.e = function (c) {\n        if (!this.ondata)\n            err(5);\n        if (this.d)\n            err(4);\n        if (!this.p.length)\n            this.p = c;\n        else if (c.length) {\n            var n = new u8(this.p.length + c.length);\n            n.set(this.p), n.set(c, this.p.length), this.p = n;\n        }\n    };\n    Inflate.prototype.c = function (final) {\n        this.s.i = +(this.d = final || false);\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.s, this.o);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, (this.s.p / 8) | 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */\n    Inflate.prototype.push = function (chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}());\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/ (function () {\n    function AsyncInflate(opts, cb) {\n        astrmify([\n            bInflt,\n            function () { return [astrm, Inflate]; }\n        ], this, StrmOpt.call(this, opts, cb), function (ev) {\n            var strm = new Inflate(ev.data);\n            onmessage = astrm(strm);\n        }, 7, 0);\n    }\n    return AsyncInflate;\n}());\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        err(7);\n    return cbify(data, opts, [\n        bInflt\n    ], function (ev) { return pbf(inflateSync(ev.data[0], gopt(ev.data[1]))); }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, opts) {\n    return inflt(data, { i: 2 }, opts && opts.out, opts && opts.dictionary);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/ (function () {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gzip.prototype.push = function (chunk, final) {\n        this.c.p(chunk);\n        this.l += chunk.length;\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function (c, f) {\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, this.s);\n        if (this.v)\n            gzh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    /**\n     * Flushes buffered uncompressed data. Useful to immediately retrieve the\n     * GZIPped output for small inputs.\n     */\n    Gzip.prototype.flush = function () {\n        Deflate.prototype.flush.call(this);\n    };\n    return Gzip;\n}());\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/ (function () {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function () { return [astrm, Deflate, Gzip]; }\n        ], this, StrmOpt.call(this, opts, cb), function (ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8, 1);\n    }\n    return AsyncGzip;\n}());\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        err(7);\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function () { return [gzipSync]; }\n    ], function (ev) { return pbf(gzipSync(ev.data[0], ev.data[1])); }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming single or multi-member GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/ (function () {\n    function Gunzip(opts, cb) {\n        this.v = 1;\n        this.r = 0;\n        Inflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gunzip.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        this.r += chunk.length;\n        if (this.v) {\n            var p = this.p.subarray(this.v - 1);\n            var s = p.length > 3 ? gzs(p) : 4;\n            if (s > p.length) {\n                if (!final)\n                    return;\n            }\n            else if (this.v > 1 && this.onmember) {\n                this.onmember(this.r - p.length);\n            }\n            this.p = p.subarray(s), this.v = 0;\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n        // process concatenated GZIP\n        if (this.s.f && !this.s.l && !final) {\n            this.v = shft(this.s.p) + 9;\n            this.s = { i: 0 };\n            this.o = new u8(0);\n            this.push(new u8(0), final);\n        }\n    };\n    return Gunzip;\n}());\nexport { Gunzip };\n/**\n * Asynchronous streaming single or multi-member GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/ (function () {\n    function AsyncGunzip(opts, cb) {\n        var _this = this;\n        astrmify([\n            bInflt,\n            guze,\n            function () { return [astrm, Inflate, Gunzip]; }\n        ], this, StrmOpt.call(this, opts, cb), function (ev) {\n            var strm = new Gunzip(ev.data);\n            strm.onmember = function (offset) { return postMessage(offset); };\n            onmessage = astrm(strm);\n        }, 9, 0, function (offset) { return _this.onmember && _this.onmember(offset); });\n    }\n    return AsyncGunzip;\n}());\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        err(7);\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function () { return [gunzipSync]; }\n    ], function (ev) { return pbf(gunzipSync(ev.data[0], ev.data[1])); }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, opts) {\n    var st = gzs(data);\n    if (st + 8 > data.length)\n        err(6, 'invalid gzip data');\n    return inflt(data.subarray(st, -8), { i: 2 }, opts && opts.out || new u8(gzl(data)), opts && opts.dictionary);\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/ (function () {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Zlib.prototype.push = function (chunk, final) {\n        this.c.p(chunk);\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function (c, f) {\n        var raw = dopt(c, this.o, this.v && (this.o.dictionary ? 6 : 2), f && 4, this.s);\n        if (this.v)\n            zlh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    /**\n     * Flushes buffered uncompressed data. Useful to immediately retrieve the\n     * zlibbed output for small inputs.\n     */\n    Zlib.prototype.flush = function () {\n        Deflate.prototype.flush.call(this);\n    };\n    return Zlib;\n}());\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/ (function () {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function () { return [astrm, Deflate, Zlib]; }\n        ], this, StrmOpt.call(this, opts, cb), function (ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10, 1);\n    }\n    return AsyncZlib;\n}());\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        err(7);\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function () { return [zlibSync]; }\n    ], function (ev) { return pbf(zlibSync(ev.data[0], ev.data[1])); }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, opts.dictionary ? 6 : 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/ (function () {\n    function Unzlib(opts, cb) {\n        Inflate.call(this, opts, cb);\n        this.v = opts && opts.dictionary ? 2 : 1;\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzlib.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 6 && !final)\n                return;\n            this.p = this.p.subarray(zls(this.p, this.v - 1)), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4)\n                err(6, 'invalid zlib data');\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}());\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/ (function () {\n    function AsyncUnzlib(opts, cb) {\n        astrmify([\n            bInflt,\n            zule,\n            function () { return [astrm, Inflate, Unzlib]; }\n        ], this, StrmOpt.call(this, opts, cb), function (ev) {\n            var strm = new Unzlib(ev.data);\n            onmessage = astrm(strm);\n        }, 11, 0);\n    }\n    return AsyncUnzlib;\n}());\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        err(7);\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function () { return [unzlibSync]; }\n    ], function (ev) { return pbf(unzlibSync(ev.data[0], gopt(ev.data[1]))); }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, opts) {\n    return inflt(data.subarray(zls(data, opts && opts.dictionary), -4), { i: 2 }, opts && opts.out, opts && opts.dictionary);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    function Decompress(opts, cb) {\n        this.o = StrmOpt.call(this, opts, cb) || {};\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n    }\n    // init substream\n    // overriden by AsyncDecompress\n    Decompress.prototype.i = function () {\n        var _this = this;\n        this.s.ondata = function (dat, final) {\n            _this.ondata(dat, final);\n        };\n    };\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            err(5);\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            }\n            else\n                this.p = chunk;\n            if (this.p.length > 2) {\n                this.s = (this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8)\n                    ? new this.G(this.o)\n                    : ((this.p[0] & 15) != 8 || (this.p[0] >> 4) > 7 || ((this.p[0] << 8 | this.p[1]) % 31))\n                        ? new this.I(this.o)\n                        : new this.Z(this.o);\n                this.i();\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        }\n        else\n            this.s.push(chunk, final);\n    };\n    return Decompress;\n}());\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/ (function () {\n    function AsyncDecompress(opts, cb) {\n        Decompress.call(this, opts, cb);\n        this.queuedSize = 0;\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n    }\n    AsyncDecompress.prototype.i = function () {\n        var _this = this;\n        this.s.ondata = function (err, dat, final) {\n            _this.ondata(err, dat, final);\n        };\n        this.s.ondrain = function (size) {\n            _this.queuedSize -= size;\n            if (_this.ondrain)\n                _this.ondrain(size);\n        };\n    };\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncDecompress.prototype.push = function (chunk, final) {\n        this.queuedSize += chunk.length;\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}());\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        err(7);\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzip(data, opts, cb)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflate(data, opts, cb)\n            : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, opts) {\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzipSync(data, opts)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflateSync(data, opts)\n            : unzlibSync(data, opts);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n    for (var k in d) {\n        var val = d[k], n = p + k, op = o;\n        if (Array.isArray(val))\n            op = mrg(o, val[1]), val = val[0];\n        if (val instanceof u8)\n            t[n] = [val, op];\n        else {\n            t[n += '/'] = [new u8(0), op];\n            fltn(val, n, t, o);\n        }\n    }\n};\n// text encoder\nvar te = typeof TextEncoder != 'undefined' && /*#__PURE__*/ new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != 'undefined' && /*#__PURE__*/ new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n    td.decode(et, { stream: true });\n    tds = 1;\n}\ncatch (e) { }\n// decode UTF8\nvar dutf8 = function (d) {\n    for (var r = '', i = 0;;) {\n        var c = d[i++];\n        var eb = (c > 127) + (c > 223) + (c > 239);\n        if (i + eb > d.length)\n            return { s: r, r: slc(d, i - 1) };\n        if (!eb)\n            r += String.fromCharCode(c);\n        else if (eb == 3) {\n            c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63)) - 65536,\n                r += String.fromCharCode(55296 | (c >> 10), 56320 | (c & 1023));\n        }\n        else if (eb & 1)\n            r += String.fromCharCode((c & 31) << 6 | (d[i++] & 63));\n        else\n            r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63));\n    }\n};\n/**\n * Streaming UTF-8 decoding\n */\nvar DecodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is decoded\n     */\n    function DecodeUTF8(cb) {\n        this.ondata = cb;\n        if (tds)\n            this.t = new TextDecoder();\n        else\n            this.p = et;\n    }\n    /**\n     * Pushes a chunk to be decoded from UTF-8 binary\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    DecodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            err(5);\n        final = !!final;\n        if (this.t) {\n            this.ondata(this.t.decode(chunk, { stream: true }), final);\n            if (final) {\n                if (this.t.decode().length)\n                    err(8);\n                this.t = null;\n            }\n            return;\n        }\n        if (!this.p)\n            err(4);\n        var dat = new u8(this.p.length + chunk.length);\n        dat.set(this.p);\n        dat.set(chunk, this.p.length);\n        var _a = dutf8(dat), s = _a.s, r = _a.r;\n        if (final) {\n            if (r.length)\n                err(8);\n            this.p = null;\n        }\n        else\n            this.p = r;\n        this.ondata(s, final);\n    };\n    return DecodeUTF8;\n}());\nexport { DecodeUTF8 };\n/**\n * Streaming UTF-8 encoding\n */\nvar EncodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is encoded\n     */\n    function EncodeUTF8(cb) {\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be encoded to UTF-8\n     * @param chunk The string data to push\n     * @param final Whether this is the last chunk\n     */\n    EncodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            err(5);\n        if (this.d)\n            err(4);\n        this.ondata(strToU8(chunk), this.d = final || false);\n    };\n    return EncodeUTF8;\n}());\nexport { EncodeUTF8 };\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n    if (latin1) {\n        var ar_1 = new u8(str.length);\n        for (var i = 0; i < str.length; ++i)\n            ar_1[i] = str.charCodeAt(i);\n        return ar_1;\n    }\n    if (te)\n        return te.encode(str);\n    var l = str.length;\n    var ar = new u8(str.length + (str.length >> 1));\n    var ai = 0;\n    var w = function (v) { ar[ai++] = v; };\n    for (var i = 0; i < l; ++i) {\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + ((l - i) << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1)\n            w(c);\n        else if (c < 2048)\n            w(192 | (c >> 6)), w(128 | (c & 63));\n        else if (c > 55295 && c < 57344)\n            c = 65536 + (c & 1023 << 10) | (str.charCodeAt(++i) & 1023),\n                w(240 | (c >> 18)), w(128 | ((c >> 12) & 63)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n        else\n            w(224 | (c >> 12)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n    if (latin1) {\n        var r = '';\n        for (var i = 0; i < dat.length; i += 16384)\n            r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n        return r;\n    }\n    else if (td) {\n        return td.decode(dat);\n    }\n    else {\n        var _a = dutf8(dat), s = _a.s, r = _a.r;\n        if (r.length)\n            err(8);\n        return s;\n    }\n}\n;\n// deflate bit flag\nvar dbf = function (l) { return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0; };\n// skip local zip header\nvar slzh = function (d, b) { return b + 30 + b2(d, b + 26) + b2(d, b + 28); };\n// read zip header\nvar zh = function (d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl, bs = b4(d, b + 20);\n    var _a = z && bs == 4294967295 ? z64e(d, es) : [bs, b4(d, b + 24), b4(d, b + 42)], sc = _a[0], su = _a[1], off = _a[2];\n    return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n    for (; b2(d, b) != 1; b += 4 + b2(d, b + 2))\n        ;\n    return [b8(d, b + 12), b8(d, b + 4), b8(d, b + 20)];\n};\n// extra field length\nvar exfl = function (ex) {\n    var le = 0;\n    if (ex) {\n        for (var k in ex) {\n            var l = ex[k].length;\n            if (l > 65535)\n                err(9);\n            le += l + 4;\n        }\n    }\n    return le;\n};\n// write zip header\nvar wzh = function (d, b, f, fn, u, c, ce, co) {\n    var fl = fn.length, ex = f.extra, col = co && co.length;\n    var exl = exfl(ex);\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null)\n        d[b++] = 20, d[b++] = f.os;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = (f.flag << 1) | (c < 0 && 8), d[b++] = u && 8;\n    d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n    var dt = new Date(f.mtime == null ? Date.now() : f.mtime), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119)\n        err(10);\n    wbytes(d, b, (y << 25) | ((dt.getMonth() + 1) << 21) | (dt.getDate() << 16) | (dt.getHours() << 11) | (dt.getMinutes() << 5) | (dt.getSeconds() >> 1)), b += 4;\n    if (c != -1) {\n        wbytes(d, b, f.crc);\n        wbytes(d, b + 4, c < 0 ? -c - 2 : c);\n        wbytes(d, b + 8, f.size);\n    }\n    wbytes(d, b + 12, fl);\n    wbytes(d, b + 14, exl), b += 16;\n    if (ce != null) {\n        wbytes(d, b, col);\n        wbytes(d, b + 6, f.attrs);\n        wbytes(d, b + 10, ce), b += 14;\n    }\n    d.set(fn, b);\n    b += fl;\n    if (exl) {\n        for (var k in ex) {\n            var exf = ex[k], l = exf.length;\n            wbytes(d, b, +k);\n            wbytes(d, b + 2, l);\n            d.set(exf, b + 4), b += 4 + l;\n        }\n    }\n    if (col)\n        d.set(co, b), b += col;\n    return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */\nvar ZipPassThrough = /*#__PURE__*/ (function () {\n    /**\n     * Creates a pass-through stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     */\n    function ZipPassThrough(filename) {\n        this.filename = filename;\n        this.c = crc();\n        this.size = 0;\n        this.compression = 0;\n    }\n    /**\n     * Processes a chunk and pushes to the output stream. You can override this\n     * method in a subclass for custom behavior, but by default this passes\n     * the data through. You must call this.ondata(err, chunk, final) at some\n     * point in this method.\n     * @param chunk The chunk to process\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.process = function (chunk, final) {\n        this.ondata(null, chunk, final);\n    };\n    /**\n     * Pushes a chunk to be added. If you are subclassing this with a custom\n     * compression algorithm, note that you must push data from the source\n     * file only, pre-compression.\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            err(5);\n        this.c.p(chunk);\n        this.size += chunk.length;\n        if (final)\n            this.crc = this.c.d();\n        this.process(chunk, final || false);\n    };\n    return ZipPassThrough;\n}());\nexport { ZipPassThrough };\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */\nvar ZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function ZipDeflate(filename, opts) {\n        var _this = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new Deflate(opts, function (dat, final) {\n            _this.ondata(null, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n    }\n    ZipDeflate.prototype.process = function (chunk, final) {\n        try {\n            this.d.push(chunk, final);\n        }\n        catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return ZipDeflate;\n}());\nexport { ZipDeflate };\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */\nvar AsyncZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function AsyncZipDeflate(filename, opts) {\n        var _this = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new AsyncDeflate(opts, function (err, dat, final) {\n            _this.ondata(err, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n        this.terminate = this.d.terminate;\n    }\n    AsyncZipDeflate.prototype.process = function (chunk, final) {\n        this.d.push(chunk, final);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return AsyncZipDeflate;\n}());\nexport { AsyncZipDeflate };\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */\nvar Zip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an empty ZIP archive to which files can be added\n     * @param cb The callback to call whenever data for the generated ZIP archive\n     *           is available\n     */\n    function Zip(cb) {\n        this.ondata = cb;\n        this.u = [];\n        this.d = 1;\n    }\n    /**\n     * Adds a file to the ZIP archive\n     * @param file The file stream to add\n     */\n    Zip.prototype.add = function (file) {\n        var _this = this;\n        if (!this.ondata)\n            err(5);\n        // finishing or finished\n        if (this.d & 2)\n            this.ondata(err(4 + (this.d & 1) * 8, 0, 1), null, false);\n        else {\n            var f = strToU8(file.filename), fl_1 = f.length;\n            var com = file.comment, o = com && strToU8(com);\n            var u = fl_1 != file.filename.length || (o && (com.length != o.length));\n            var hl_1 = fl_1 + exfl(file.extra) + 30;\n            if (fl_1 > 65535)\n                this.ondata(err(11, 0, 1), null, false);\n            var header = new u8(hl_1);\n            wzh(header, 0, file, f, u, -1);\n            var chks_1 = [header];\n            var pAll_1 = function () {\n                for (var _i = 0, chks_2 = chks_1; _i < chks_2.length; _i++) {\n                    var chk = chks_2[_i];\n                    _this.ondata(null, chk, false);\n                }\n                chks_1 = [];\n            };\n            var tr_1 = this.d;\n            this.d = 0;\n            var ind_1 = this.u.length;\n            var uf_1 = mrg(file, {\n                f: f,\n                u: u,\n                o: o,\n                t: function () {\n                    if (file.terminate)\n                        file.terminate();\n                },\n                r: function () {\n                    pAll_1();\n                    if (tr_1) {\n                        var nxt = _this.u[ind_1 + 1];\n                        if (nxt)\n                            nxt.r();\n                        else\n                            _this.d = 1;\n                    }\n                    tr_1 = 1;\n                }\n            });\n            var cl_1 = 0;\n            file.ondata = function (err, dat, final) {\n                if (err) {\n                    _this.ondata(err, dat, final);\n                    _this.terminate();\n                }\n                else {\n                    cl_1 += dat.length;\n                    chks_1.push(dat);\n                    if (final) {\n                        var dd = new u8(16);\n                        wbytes(dd, 0, 0x8074B50);\n                        wbytes(dd, 4, file.crc);\n                        wbytes(dd, 8, cl_1);\n                        wbytes(dd, 12, file.size);\n                        chks_1.push(dd);\n                        uf_1.c = cl_1, uf_1.b = hl_1 + cl_1 + 16, uf_1.crc = file.crc, uf_1.size = file.size;\n                        if (tr_1)\n                            uf_1.r();\n                        tr_1 = 1;\n                    }\n                    else if (tr_1)\n                        pAll_1();\n                }\n            };\n            this.u.push(uf_1);\n        }\n    };\n    /**\n     * Ends the process of adding files and prepares to emit the final chunks.\n     * This *must* be called after adding all desired files for the resulting\n     * ZIP file to work properly.\n     */\n    Zip.prototype.end = function () {\n        var _this = this;\n        if (this.d & 2) {\n            this.ondata(err(4 + (this.d & 1) * 8, 0, 1), null, true);\n            return;\n        }\n        if (this.d)\n            this.e();\n        else\n            this.u.push({\n                r: function () {\n                    if (!(_this.d & 1))\n                        return;\n                    _this.u.splice(-1, 1);\n                    _this.e();\n                },\n                t: function () { }\n            });\n        this.d = 3;\n    };\n    Zip.prototype.e = function () {\n        var bt = 0, l = 0, tl = 0;\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n        }\n        var out = new u8(tl + 22);\n        for (var _b = 0, _c = this.u; _b < _c.length; _b++) {\n            var f = _c[_b];\n            wzh(out, bt, f, f.f, f.u, -f.c - 2, l, f.o);\n            bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n        }\n        wzf(out, bt, this.u.length, tl, l);\n        this.ondata(null, out, true);\n        this.d = 2;\n    };\n    /**\n     * A method to terminate any internal workers used by the stream. Subsequent\n     * calls to add() will fail.\n     */\n    Zip.prototype.terminate = function () {\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            f.t();\n        }\n        this.d = 2;\n    };\n    return Zip;\n}());\nexport { Zip };\nexport function zip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        err(7);\n    var r = {};\n    fltn(data, '', r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var cbd = function (a, b) {\n        mt(function () { cb(a, b); });\n    };\n    mt(function () { cbd = cb; });\n    var cbf = function () {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for (var i = 0; i < slft; ++i) {\n            var f = files[i];\n            try {\n                var l = f.c.length;\n                wzh(out, tot, f, f.f, f.u, l);\n                var badd = 30 + f.f.length + exfl(f.extra);\n                var loc = tot + badd;\n                out.set(f.c, loc);\n                wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n            }\n            catch (e) {\n                return cbd(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cbd(null, out);\n    };\n    if (!lft)\n        cbf();\n    var _loop_1 = function (i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), size = file.length;\n        c.p(file);\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        var compression = p.level == 0 ? 0 : 8;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cbd(e, null);\n            }\n            else {\n                var l = d.length;\n                files[i] = mrg(p, {\n                    size: size,\n                    crc: c.d(),\n                    c: d,\n                    f: f,\n                    m: m,\n                    u: s != fn.length || (m && (com.length != ms)),\n                    compression: compression\n                });\n                o += 30 + s + exl + l;\n                tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n                if (!--lft)\n                    cbf();\n            }\n        };\n        if (s > 65535)\n            cbl(err(11, 0, 1), null);\n        if (!compression)\n            cbl(null, file);\n        else if (size < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            }\n            catch (e) {\n                cbl(e, null);\n            }\n        }\n        else\n            term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for (var i = 0; i < slft; ++i) {\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var r = {};\n    var files = [];\n    fltn(data, '', r, opts);\n    var o = 0;\n    var tot = 0;\n    for (var fn in r) {\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var compression = p.level == 0 ? 0 : 8;\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        if (s > 65535)\n            err(11);\n        var d = compression ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push(mrg(p, {\n            size: file.length,\n            crc: c.d(),\n            c: d,\n            f: f,\n            m: m,\n            u: s != fn.length || (m && (com.length != ms)),\n            o: o,\n            compression: compression\n        }));\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for (var i = 0; i < files.length; ++i) {\n        var f = files[i];\n        wzh(out, f.o, f, f.f, f.u, f.c.length);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        out.set(f.c, f.o + badd);\n        wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */\nvar UnzipPassThrough = /*#__PURE__*/ (function () {\n    function UnzipPassThrough() {\n    }\n    UnzipPassThrough.prototype.push = function (data, final) {\n        this.ondata(null, data, final);\n    };\n    UnzipPassThrough.compression = 0;\n    return UnzipPassThrough;\n}());\nexport { UnzipPassThrough };\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */\nvar UnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function UnzipInflate() {\n        var _this = this;\n        this.i = new Inflate(function (dat, final) {\n            _this.ondata(null, dat, final);\n        });\n    }\n    UnzipInflate.prototype.push = function (data, final) {\n        try {\n            this.i.push(data, final);\n        }\n        catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    UnzipInflate.compression = 8;\n    return UnzipInflate;\n}());\nexport { UnzipInflate };\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */\nvar AsyncUnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function AsyncUnzipInflate(_, sz) {\n        var _this = this;\n        if (sz < 320000) {\n            this.i = new Inflate(function (dat, final) {\n                _this.ondata(null, dat, final);\n            });\n        }\n        else {\n            this.i = new AsyncInflate(function (err, dat, final) {\n                _this.ondata(err, dat, final);\n            });\n            this.terminate = this.i.terminate;\n        }\n    }\n    AsyncUnzipInflate.prototype.push = function (data, final) {\n        if (this.i.terminate)\n            data = slc(data, 0);\n        this.i.push(data, final);\n    };\n    AsyncUnzipInflate.compression = 8;\n    return AsyncUnzipInflate;\n}());\nexport { AsyncUnzipInflate };\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */\nvar Unzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a ZIP decompression stream\n     * @param cb The callback to call whenever a file in the ZIP archive is found\n     */\n    function Unzip(cb) {\n        this.onfile = cb;\n        this.k = [];\n        this.o = {\n            0: UnzipPassThrough\n        };\n        this.p = et;\n    }\n    /**\n     * Pushes a chunk to be unzipped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzip.prototype.push = function (chunk, final) {\n        var _this = this;\n        if (!this.onfile)\n            err(5);\n        if (!this.p)\n            err(4);\n        if (this.c > 0) {\n            var len = Math.min(this.c, chunk.length);\n            var toAdd = chunk.subarray(0, len);\n            this.c -= len;\n            if (this.d)\n                this.d.push(toAdd, !this.c);\n            else\n                this.k[0].push(toAdd);\n            chunk = chunk.subarray(len);\n            if (chunk.length)\n                return this.push(chunk, final);\n        }\n        else {\n            var f = 0, i = 0, is = void 0, buf = void 0;\n            if (!this.p.length)\n                buf = chunk;\n            else if (!chunk.length)\n                buf = this.p;\n            else {\n                buf = new u8(this.p.length + chunk.length);\n                buf.set(this.p), buf.set(chunk, this.p.length);\n            }\n            var l = buf.length, oc = this.c, add = oc && this.d;\n            var _loop_2 = function () {\n                var _a;\n                var sig = b4(buf, i);\n                if (sig == 0x4034B50) {\n                    f = 1, is = i;\n                    this_1.d = null;\n                    this_1.c = 0;\n                    var bf = b2(buf, i + 6), cmp_1 = b2(buf, i + 8), u = bf & 2048, dd = bf & 8, fnl = b2(buf, i + 26), es = b2(buf, i + 28);\n                    if (l > i + 30 + fnl + es) {\n                        var chks_3 = [];\n                        this_1.k.unshift(chks_3);\n                        f = 2;\n                        var sc_1 = b4(buf, i + 18), su_1 = b4(buf, i + 22);\n                        var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n                        if (sc_1 == 4294967295) {\n                            _a = dd ? [-2] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n                        }\n                        else if (dd)\n                            sc_1 = -1;\n                        i += es;\n                        this_1.c = sc_1;\n                        var d_1;\n                        var file_1 = {\n                            name: fn_1,\n                            compression: cmp_1,\n                            start: function () {\n                                if (!file_1.ondata)\n                                    err(5);\n                                if (!sc_1)\n                                    file_1.ondata(null, et, true);\n                                else {\n                                    var ctr = _this.o[cmp_1];\n                                    if (!ctr)\n                                        file_1.ondata(err(14, 'unknown compression type ' + cmp_1, 1), null, false);\n                                    d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                                    d_1.ondata = function (err, dat, final) { file_1.ondata(err, dat, final); };\n                                    for (var _i = 0, chks_4 = chks_3; _i < chks_4.length; _i++) {\n                                        var dat = chks_4[_i];\n                                        d_1.push(dat, false);\n                                    }\n                                    if (_this.k[0] == chks_3 && _this.c)\n                                        _this.d = d_1;\n                                    else\n                                        d_1.push(et, true);\n                                }\n                            },\n                            terminate: function () {\n                                if (d_1 && d_1.terminate)\n                                    d_1.terminate();\n                            }\n                        };\n                        if (sc_1 >= 0)\n                            file_1.size = sc_1, file_1.originalSize = su_1;\n                        this_1.onfile(file_1);\n                    }\n                    return \"break\";\n                }\n                else if (oc) {\n                    if (sig == 0x8074B50) {\n                        is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                    else if (sig == 0x2014B50) {\n                        is = i -= 4, f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                }\n            };\n            var this_1 = this;\n            for (; i < l - 4; ++i) {\n                var state_1 = _loop_2();\n                if (state_1 === \"break\")\n                    break;\n            }\n            this.p = et;\n            if (oc < 0) {\n                var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n                if (add)\n                    add.push(dat, !!f);\n                else\n                    this.k[+(f == 2)].push(dat);\n            }\n            if (f & 2)\n                return this.push(buf.subarray(i), final);\n            this.p = buf.subarray(i);\n        }\n        if (final) {\n            if (this.c)\n                err(13);\n            this.p = null;\n        }\n    };\n    /**\n     * Registers a decoder with the stream, allowing for files compressed with\n     * the compression type provided to be expanded correctly\n     * @param decoder The decoder constructor\n     */\n    Unzip.prototype.register = function (decoder) {\n        this.o[decoder.compression] = decoder;\n    };\n    return Unzip;\n}());\nexport { Unzip };\nvar mt = typeof queueMicrotask == 'function' ? queueMicrotask : typeof setTimeout == 'function' ? setTimeout : function (fn) { fn(); };\nexport function unzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        err(7);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var files = {};\n    var cbd = function (a, b) {\n        mt(function () { cb(a, b); });\n    };\n    mt(function () { cbd = cb; });\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558) {\n            cbd(err(13, 0, 1), null);\n            return tAll;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (lft) {\n        var c = lft;\n        var o = b4(data, e + 16);\n        var z = o == 4294967295 || c == 65535;\n        if (z) {\n            var ze = b4(data, e - 12);\n            z = b4(data, ze) == 0x6064B50;\n            if (z) {\n                c = lft = b4(data, ze + 32);\n                o = b4(data, ze + 48);\n            }\n        }\n        var fltr = opts && opts.filter;\n        var _loop_3 = function (i) {\n            var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n            o = no;\n            var cbl = function (e, d) {\n                if (e) {\n                    tAll();\n                    cbd(e, null);\n                }\n                else {\n                    if (d)\n                        files[fn] = d;\n                    if (!--lft)\n                        cbd(null, files);\n                }\n            };\n            if (!fltr || fltr({\n                name: fn,\n                size: sc,\n                originalSize: su,\n                compression: c_1\n            })) {\n                if (!c_1)\n                    cbl(null, slc(data, b, b + sc));\n                else if (c_1 == 8) {\n                    var infl = data.subarray(b, b + sc);\n                    // Synchronously decompress under 512KB, or barely-compressed data\n                    if (su < 524288 || sc > 0.8 * su) {\n                        try {\n                            cbl(null, inflateSync(infl, { out: new u8(su) }));\n                        }\n                        catch (e) {\n                            cbl(e, null);\n                        }\n                    }\n                    else\n                        term.push(inflate(infl, { size: su }, cbl));\n                }\n                else\n                    cbl(err(14, 'unknown compression type ' + c_1, 1), null);\n            }\n            else\n                cbl(null, null);\n        };\n        for (var i = 0; i < c; ++i) {\n            _loop_3(i);\n        }\n    }\n    else\n        cbd(null, {});\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @param opts The ZIP extraction options\n * @returns The decompressed files\n */\nexport function unzipSync(data, opts) {\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558)\n            err(13);\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c)\n        return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295 || c == 65535;\n    if (z) {\n        var ze = b4(data, e - 12);\n        z = b4(data, ze) == 0x6064B50;\n        if (z) {\n            c = b4(data, ze + 32);\n            o = b4(data, ze + 48);\n        }\n    }\n    var fltr = opts && opts.filter;\n    for (var i = 0; i < c; ++i) {\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!fltr || fltr({\n            name: fn,\n            size: sc,\n            originalSize: su,\n            compression: c_2\n        })) {\n            if (!c_2)\n                files[fn] = slc(data, b, b + sc);\n            else if (c_2 == 8)\n                files[fn] = inflateSync(data.subarray(b, b + sc), { out: new u8(su) });\n            else\n                err(14, 'unknown compression type ' + c_2);\n        }\n    }\n    return files;\n}\n", "import { AsyncDeflate, Deflate, Async<PERSON><PERSON>p, Async<PERSON>lib, AsyncInflate, AsyncGunzip, AsyncUnzlib, Gzip, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>z<PERSON><PERSON>, Inflate } from 'fflate';\nconst wrapSync = (Stream) => {\n    class AsyncWrappedStream {\n        constructor() {\n            this.i = new Stream();\n            this.i.ondata = (data, final) => {\n                this.ondata(null, data, final);\n            };\n        }\n        push(data, final) {\n            try {\n                this.queuedSize += data.length;\n                this.i.push(data, final);\n                this.queuedSize -= data.length;\n                if (this.ondrain)\n                    this.ondrain(data.length);\n            }\n            catch (err) {\n                this.ondata(err, null, final || false);\n            }\n        }\n    }\n    return AsyncWrappedStream;\n};\n// Safari fix\nlet hasWorker = 1;\ntry {\n    const test = new AsyncDeflate();\n    test.terminate();\n}\ncatch (err) {\n    hasWorker = 0;\n}\nconst compressors = hasWorker ? {\n    'gzip': AsyncGzip,\n    'deflate': AsyncZlib,\n    'deflate-raw': AsyncDeflate\n} : {\n    'gzip': wrapSync(Gzip),\n    'deflate': wrapSync(Zlib),\n    'deflate-raw': wrapSync(Deflate)\n};\nconst decompressors = hasWorker ? {\n    'gzip': AsyncGunzip,\n    'deflate': AsyncUnzlib,\n    'deflate-raw': AsyncInflate\n} : {\n    'gzip': wrapSync(Gunzip),\n    'deflate': wrapSync(Unzlib),\n    'deflate-raw': wrapSync(Inflate)\n};\nconst makeMulti = (TransformStreamBase, processors, name) => {\n    class BaseCompressionStream extends TransformStreamBase {\n        constructor(format) {\n            if (!arguments.length) {\n                throw new TypeError(`Failed to construct '${name}': 1 argument required, but only 0 present.`);\n            }\n            const Processor = processors[format];\n            if (!Processor) {\n                throw new TypeError(`Failed to construct '${name}': Unsupported compression format: '${format}'`);\n            }\n            let compressor = new Processor();\n            let endCb;\n            super({\n                start: controller => {\n                    compressor.ondata = (err, dat, final) => {\n                        if (err)\n                            controller.error(err);\n                        else if (dat) {\n                            controller.enqueue(dat);\n                            if (final) {\n                                if (endCb)\n                                    endCb();\n                                else\n                                    controller.terminate();\n                            }\n                        }\n                    };\n                },\n                transform: chunk => {\n                    if (chunk instanceof ArrayBuffer)\n                        chunk = new Uint8Array(chunk);\n                    else if (ArrayBuffer.isView(chunk)) {\n                        chunk = new Uint8Array(chunk.buffer, chunk.byteOffset, chunk.byteLength);\n                    }\n                    else {\n                        throw new TypeError(\"The provided value is not of type '(ArrayBuffer or ArrayBufferView)'\");\n                    }\n                    compressor.push(chunk);\n                    // use fflate internal buffering to keep worker message channel fed\n                    if (compressor.queuedSize >= 32768) {\n                        return new Promise(resolve => {\n                            compressor.ondrain = () => {\n                                if (compressor.queuedSize < 32768)\n                                    resolve();\n                            };\n                        });\n                    }\n                },\n                flush: () => new Promise(resolve => {\n                    endCb = resolve;\n                    compressor.push(new Uint8Array(0), true);\n                })\n            }, {\n                size: chunk => chunk.byteLength | 0,\n                highWaterMark: 65536\n            }, {\n                size: chunk => chunk.byteLength | 0,\n                highWaterMark: 65536\n            });\n        }\n    }\n    return BaseCompressionStream;\n};\nexport function makeCompressionStream(TransformStreamBase) {\n    return makeMulti(TransformStreamBase, compressors, 'CompressionStream');\n}\nexport function makeDecompressionStream(TransformStreamBase) {\n    return makeMulti(TransformStreamBase, decompressors, 'DecompressionStream');\n}\n", "import { makeCompressionStream, makeDecompressionStream } from './ponyfill.mjs';\nconst globals = typeof globalThis == 'undefined'\n    ? typeof self == 'undefined'\n        ? typeof global == 'undefined'\n            ? {}\n            : global\n        : self\n    : globalThis;\nif (typeof globals.CompressionStream == 'undefined') {\n    globals.CompressionStream = makeCompressionStream(TransformStream);\n}\nif (typeof globals.DecompressionStream == 'undefined') {\n    globals.DecompressionStream = makeDecompressionStream(TransformStream);\n}\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n  return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  function next() {\n    while (env.stack.length) {\n      var rec = env.stack.pop();\n      try {\n        var result = rec.dispose && rec.dispose.call(rec.value);\n        if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n      }\n      catch (e) {\n          fail(e);\n      }\n    }\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n};\n"], "names": ["cmp", "a", "b", "inf", "Infinity", "escapeRegExp", "str", "replace", "EXACT_HERE", "PUNCT_RE", "COLLATE_ARGS", "numeric", "sensitivity", "swapAlpha", "upper", "lower", "OPTS", "unicode", "alpha", "interSplit", "intraSplit", "interBound", "intraBound", "interLft", "interRgt", "interChars", "interIns", "intraChars", "intraIns", "intraContr", "intraMode", "intraSlice", "intraSub", "intraTrn", "intraDel", "intraFilt", "term", "match", "index", "toUpper", "toLocaleUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "toLocaleLowerCase", "compare", "sort", "info", "haystack", "needle", "idx", "chars", "terms", "interLft2", "interLft1", "start", "cases", "map", "v", "i", "ia", "ib", "lazyRepeat", "limit", "mode2Tpl", "uFuzzy", "opts", "Object", "assign", "_intraSplit", "_interSplit", "_intraBound", "_interBound", "Intl", "Collator", "letters", "uFlag", "quotedAny", "EXACTS_RE", "RegExp", "NEGS_RE", "intraRules", "p", "_intraSlice", "_intraIns", "_intraSub", "_intraTrn", "_intraDel", "test", "plen", "length", "Math", "min", "withIntraSplit", "trimRe", "contrsRe", "split", "keepCase", "exacts", "m", "push", "j", "filter", "t", "NUM_OR_ALPHA_RE", "prepQuery", "capt", "interOR", "parts", "reTpl", "contrs", "Array", "fill", "pi", "slice", "matchAll", "lftIdx", "rgtIdx", "lftChar", "rgtChar", "numChars", "variants", "intraInsTpl", "join", "c", "preTpl", "sufTpl", "interCharsTpl", "idxs", "query", "out", "withIntraBound", "partsCased", "queryR", "partsLen", "_terms", "_termsCased", "part", "partCased", "termCased", "len", "field", "interRgt2", "interRgt1", "ranges", "mayDiscard", "ii", "mhstr", "idxAcc", "disc", "lft2", "lft1", "rgt2", "rgt1", "inter", "intra", "refine", "k", "group", "termLen", "groupLen", "fullMatch", "idxOf", "indexOf", "refineMatch", "lftCharIdx", "rgtCharIdx", "isPre", "is<PERSON><PERSON>", "junk", "junkIdx", "m2", "found", "re", "exec", "charIdx", "refLen", "ri", "lastRi", "_len", "splice", "from", "to", "idxInNext", "prepend", "search", "args", "outOfOrder", "infoThresh", "preFiltered", "needles", "matches", "negs", "neg", "trim", "negsRe", "terms2", "ti", "permute", "perm", "matchedIdxs", "Set", "ni", "size", "preFiltered2", "has", "matched", "add", "retInfo", "retOrder", "reduce", "acc", "_info", "order", "concat", "_search", "latinize", "accents", "A", "E", "e", "I", "O", "o", "U", "u", "C", "L", "l", "N", "n", "S", "s", "Z", "z", "accentsMap", "Map", "accentsTpl", "r", "for<PERSON>ach", "set", "accentsRe", "replacer", "get", "strings", "arr", "result", "_mark", "_append", "keys", "highlight", "mark", "accum", "append", "substring", "fr", "noop", "<PERSON><PERSON><PERSON><PERSON>", "window", "useLayoutEffect", "useEffect", "defaultState", "x", "y", "width", "height", "top", "left", "bottom", "right", "ResizeObserver", "_a", "useState", "element", "ref", "_b", "rect", "setRect", "observer", "useMemo", "entries", "contentRect", "top_1", "observe", "disconnect", "PIXELS_PER_LEVEL", "devicePixelRatio", "MUTE_THRESHOLD", "HIDE_THRESHOLD", "LABEL_THRESHOLD", "BAR_BORDER_WIDTH", "BAR_TEXT_PADDING_LEFT", "GROUP_STRIP_WIDTH", "GROUP_STRIP_PADDING", "GROUP_STRIP_MARGIN_LEFT", "GROUP_TEXT_OFFSET", "TOP_TABLE_COLUMN_WIDTH", "FlameGraphContextMenu", "data", "itemData", "onMenuItemClick", "onItemFocus", "onSandwich", "collapseConfig", "onExpandGroup", "onCollapseGroup", "onExpandAllGroups", "onCollapseAllGroups", "getExtraContextMenuButtons", "collapsing", "allGroupsExpanded", "allGroupsCollapsed", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "children", "ContextMenu", "renderMenuItems", "extraButtons", "isDiff", "isDiffFlamegraph", "jsxs", "Fragment", "MenuItem", "label", "icon", "onClick", "navigator", "clipboard", "writeText", "then", "MenuGroup", "collapsed", "posX", "posY", "focusOnOpen", "FlameGraphTooltip", "item", "totalTicks", "position", "styles", "useStyles2", "getStyles", "content", "tableData", "getDiffTooltipData", "InteractiveTable", "className", "tooltipTable", "columns", "id", "header", "getRowId", "originalRow", "rowId", "tooltipData", "getTooltipData", "lastParagraph", "unitTitle", "unitValue", "percentValue", "unitSelf", "percentSelf", "samples", "Portal", "VizTooltipContainer", "tooltipContainer", "offset", "tooltipContent", "tooltipName", "get<PERSON><PERSON><PERSON>", "itemIndexes", "items", "displayValue", "valueDisplayProcessor", "value", "displaySelf", "getSelfDisplay", "round", "text", "suffix", "getUnitTitle", "toLocaleString", "totalTicksRight", "getLevels", "valueRight", "totalTicksLeft", "valueLeft", "percentageLeft", "percentageRight", "diff", "displayValueLeft", "getValueWithUnit", "displayValueRight", "shortValFormat", "getValueFormat", "baseline", "comparison", "theme", "css", "title", "overflow", "fontSize", "typography", "bodySmall", "marginTop", "wordBreak", "marginBottom", "name", "max<PERSON><PERSON><PERSON>", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "trimLeft", "trimRight", "tinycolor", "color", "this", "rgb", "g", "ok", "format", "toLowerCase", "named", "names", "matchers", "rgba", "hsl", "h", "hsla", "hsv", "hsva", "hex8", "parseIntFromHex", "convertHexToDecimal", "hex6", "hex4", "hex3", "stringInputToObject", "isValidCSSUnit", "bound01", "String", "substr", "convertToPercentage", "floor", "f", "q", "mod", "hsvToRgb", "hue2rgb", "hslToRgb", "hasOwnProperty", "boundAlpha", "max", "inputToRGB", "_originalInput", "_r", "_g", "_roundA", "_format", "_gradientType", "gradientType", "_ok", "rgbToHsl", "d", "rgbToHsv", "rgbToHex", "allow3Char", "hex", "pad2", "toString", "char<PERSON>t", "rgbaToArgbHex", "convertDecimalToHex", "_desaturate", "amount", "toHsl", "clamp01", "_saturate", "_greyscale", "desaturate", "_lighten", "_brighten", "toRgb", "_darken", "_spin", "hue", "_complement", "polyad", "number", "isNaN", "Error", "step", "_splitcomplement", "_analogous", "results", "slices", "ret", "_monochromatic", "toHsv", "modification", "isDark", "getBrightness", "isLight", "<PERSON><PERSON><PERSON><PERSON>", "getOriginalInput", "getFormat", "get<PERSON><PERSON><PERSON>", "getLuminance", "RsRGB", "GsRGB", "BsRGB", "pow", "<PERSON><PERSON><PERSON><PERSON>", "toHsvString", "toHslString", "toHex", "toHexString", "toHex8", "allow4Char", "rgbaToHex", "toHex8String", "toRgbString", "toPercentageRgb", "toPercentageRgbString", "to<PERSON>ame", "hexNames", "to<PERSON><PERSON>er", "secondColor", "hex8String", "secondHex8String", "formatSet", "formattedString", "has<PERSON><PERSON><PERSON>", "clone", "_applyModification", "fn", "apply", "call", "lighten", "arguments", "brighten", "darken", "saturate", "greyscale", "spin", "_applyCombination", "analogous", "complement", "monochromatic", "splitcomplement", "triad", "tetrad", "fromRatio", "newColor", "equals", "color1", "color2", "random", "mix", "rgb1", "rgb2", "readability", "c1", "c2", "isReadable", "wcag2", "wcag2Parms", "parms", "level", "toUpperCase", "validateWCAG2Parms", "mostReadable", "baseColor", "colorList", "includeFallbackColors", "bestColor", "bestScore", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "<PERSON><PERSON><PERSON>", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "flipped", "flip", "parseFloat", "isOnePointZero", "processPercent", "isPercentage", "parseInt", "abs", "val", "CSS_UNIT", "PERMISSIVE_MATCH3", "PERMISSIVE_MATCH4", "SampleUnit", "SampleUnit2", "<PERSON><PERSON><PERSON><PERSON>", "SelectedView2", "ColorScheme", "ColorScheme2", "ColorSchemeDiff", "ColorSchemeDiff2", "packageColors", "by<PERSON><PERSON>ueGradient", "getBarColorByValue", "byPackageGradient", "rangeMin", "rangeMax", "intensity", "getBarColorByPackage", "colorIndex", "key", "seed", "remainder", "bytes", "h1", "h1b", "k1", "charCodeAt", "murmurhash3_32_gc", "_", "matcher", "groups", "packageName", "getPackageName", "packageColor", "diffDefaultColors", "diffDefaultGradient", "diffColorBlindColors", "diffColorBlindGradient", "useFlameRender", "options", "canvasRef", "root", "depth", "direction", "wrapperWidth", "<PERSON><PERSON><PERSON><PERSON>", "textAlign", "totalViewTicks", "totalColorTicks", "colorScheme", "focusedItemData", "collapsedMap", "ctx", "numberOfLevels", "setCtx", "current", "ctx2", "getContext", "style", "textBaseline", "font", "strokeStyle", "useSetupCanvas", "useTheme2", "mutedColor", "barMutedColor", "colors", "background", "secondary", "getBarColor", "topLevel", "useCallback", "muted", "barColor", "<PERSON><PERSON><PERSON>", "DiffColorBlind", "ValueBased", "ticks", "ticksRight", "range", "colorScale", "scaleLinear", "domain", "ticksLeft", "getBarColorByDiff", "useColorFunction", "renderFunc", "beginPath", "fillStyle", "stroke", "collapsedItemConfig", "finalLabel", "renderLabel", "groupStripX", "renderGroupingStrip", "useRenderFunc", "clearRect", "canvas", "mutedPath2D", "Path2D", "stack", "levelOffset", "pixelsPerTick", "collapsedItemRendered", "shift", "curBarTicks", "offsetModifier", "skipRender", "isCollapsedItem", "getBarX", "nextList", "parents", "unshift", "walkTree", "save", "clip", "unit", "measure", "measureText", "spaceForTextInRect", "fullLabel", "labelX", "fillText", "restore", "FlameGraphCanvas", "setRangeMin", "setRangeMax", "onItemFocused", "totalProfileTicks", "totalProfileTicksRight", "showFlameGraphOnly", "setCollapsedMap", "sizeRef", "graphRef", "useRef", "tooltipItem", "setTooltipItem", "clickedItemData", "setClickedItemData", "onGraphClick", "clientWidth", "convertPixelCoordinatesToBarCoordinates", "nativeEvent", "offsetX", "offsetY", "clientY", "clientX", "mousePosition", "setMousePosition", "onGraphMouseMove", "onGraphMouseLeave", "handleOnClick", "target", "HTMLElement", "parentElement", "addEventListener", "removeEventListener", "graph", "canvasWrapper", "onMouseMove", "onMouseLeave", "setCollapsedStatus", "setAllCollapsedStatus", "values", "every", "flexGrow", "flexBasis", "canvasContainer", "display", "cursor", "flex", "sandwich<PERSON><PERSON><PERSON>", "writingMode", "transform", "whiteSpace", "sandwichMarkerIcon", "verticalAlign", "pos", "next", "currentLevel", "levelIndex", "node", "child", "xStart", "xEnd", "collapsedConfig", "FlameGraphMetadata", "memo", "focusedItem", "sandwiched<PERSON>abel", "onFocusPillClick", "onSandwichPillClick", "ticksVal", "metadataPill", "<PERSON><PERSON><PERSON>", "placement", "Icon", "metadataPillName", "lastIndexOf", "IconButton", "pillCloseButton", "tooltip", "iconName", "metadata", "displayName", "alignItems", "borderRadius", "shape", "padding", "spacing", "fontWeight", "fontWeightMedium", "lineHeight", "margin", "justifyContent", "textOverflow", "marginLeft", "FlameGraph", "sandwichItem", "levels", "setLevels", "levelsCallers", "setLevelsCallers", "setTotalProfileTicks", "setTotalProfileTicksRight", "setTotalViewTicks", "_c", "levelsCallers2", "levels2", "totalProfileTicks2", "totalProfileTicksRight2", "totalViewTicks2", "callers", "callees", "getSandwichLevels", "commonCanvasProps", "sandwichCanvasWrapper", "cx", "sandwichMarkerCalees", "mergeSubtrees", "roots", "oppositeDirection", "previous", "indexes", "flatMap", "newItem", "prevSiblingsVal", "nextItems", "nextGroups", "groupBy", "reverse", "CollapsedMap", "newMap", "newConfig", "item2", "CollapsedMapBuilder", "threshold", "addTree", "addItem", "parent", "config", "getCollapsedMap", "FlameGraphDataContainer", "createTheme", "wrongFields", "fields", "FieldType", "string", "enum", "missingFields", "wrongTypeFields", "types", "frameField", "find", "includes", "type", "expectedTypes", "checkFields", "getMessageCheckFieldsResult", "labelField", "levelField", "valueField", "selfField", "valueRightField", "selfRightField", "enumConfig", "labelDisplayProcessor", "getDisplayProcessor", "uniqueLabels", "Boolean", "getLevel", "getValue", "fieldAccessor", "getValueRight", "getSelf", "getSelfRight", "getUniqueLabels", "Bytes", "Nanoseconds", "initLevels", "nodes", "getNodesWithLabel", "newRoot", "newNode", "getParentSubtrees", "uniqueLabelsMap", "container", "prevLevel", "lastSibling", "collapsedMapContainer", "collapsingThreshold", "nestedSetToLevels", "index2", "FlameGraphHeader", "setSearch", "setSelectedView", "containerWidth", "onReset", "onTextAlignChange", "showResetButton", "onColorSchemeChange", "<PERSON><PERSON><PERSON><PERSON>", "extraHeaderElements", "vertical", "isDiffMode", "localSearch", "setLocalSearch", "localSearchState", "setLocalSearchState", "prevSearch", "usePrevious", "useDebounce", "useSearchInput", "<PERSON><PERSON>", "inputContainer", "Input", "onChange", "currentTarget", "placeholder", "right<PERSON><PERSON><PERSON>", "variant", "buttonSpacing", "ColorSchemeButton", "ButtonGroup", "disabled", "TopTable", "RadioButtonGroup", "alignOptions", "getViewOptions", "extraElements", "props", "menu", "<PERSON><PERSON>", "<PERSON><PERSON>", "PackageBased", "colorDotStyle", "colorDotByValue", "colorDotByPackage", "colorDotDiffColorBlind", "colorDotDiffDefault", "contents", "colorDot", "colorDotDiff", "Dropdown", "overlay", "description", "viewOptions", "Both", "flexWrap", "gap", "zIndex", "navbarFixed", "primary", "min<PERSON><PERSON><PERSON>", "marginRight", "resetButton", "resetButtonIconWrapper", "windowObject", "self", "cancelFrame", "requestFrame", "clearTimeoutFn", "clearTimeout", "setTimeoutFn", "setTimeout", "cancelAnimationFrameFn", "cancelAnimationFrame", "mozCancelAnimationFrame", "webkitCancelAnimationFrame", "requestAnimationFrameFn", "requestAnimationFrame", "mozRequestAnimationFrame", "webkitRequestAnimationFrame", "createDetectElementResize", "nonce", "animationKeyframes", "animationName", "animationStartEvent", "animationStyle", "checkTriggers", "resetTriggers", "scrollListener", "attachEvent", "document", "triggers", "__resizeTriggers__", "expand", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "contract", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expand<PERSON><PERSON>d", "scrollLeft", "scrollWidth", "scrollTop", "scrollHeight", "offsetWidth", "offsetHeight", "__resizeLast__", "__resizeRAF__", "__resizeListeners__", "animation", "keyframeprefix", "domPrefixes", "startEvents", "pfx", "elm", "createElement", "undefined", "addResizeListener", "doc", "ownerDocument", "elementStyle", "getComputedStyle", "getElementById", "head", "getElementsByTagName", "setAttribute", "styleSheet", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "createStyles", "expandTrigger", "contractTrigger", "__animationListener__", "removeResizeListener", "detachEvent", "<PERSON><PERSON><PERSON><PERSON>", "callback", "animationFrameID", "timeoutID", "AutoSizer", "Component", "super", "state", "defaultHeight", "scaledHeight", "scaledWidth", "defaultWidth", "_autoSizer", "_detectElementResize", "_parentNode", "_resizeObserver", "_timeoutId", "_onResize", "disableHeight", "disable<PERSON><PERSON><PERSON>", "onResize", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "getBoundingClientRect", "setState", "_setRef", "autoSizer", "componentDidMount", "parentNode", "defaultView", "ResizeObserverInstance", "componentWillUnmount", "render", "doNotBailOutOnEmptyChildren", "tagName", "rest", "outerStyle", "childP<PERSON>ms", "bailoutOnChildren", "FlameGraphTopTableContainer", "onSymbolClick", "onSearch", "onTableSort", "table", "filteredTable", "total", "totalRight", "setSort", "desc", "topTableContainer", "frame", "actionField", "TableCellDisplayMode", "Custom", "cellComponent", "ActionCell", "rowIndex", "actionFieldTableConfig", "filterable", "actionColumnWidth", "<PERSON><PERSON>ead<PERSON>", "inspect", "align", "cellOptions", "custom", "createActionField", "symbolField", "links", "url", "origin", "baselineField", "createNumberField", "comparisonField", "diffField", "ColorText", "removeColor", "addColor", "mappings", "MappingType", "ValueToText", "RangeToText", "totalField", "dataFrames", "applyFieldOverrides", "fieldConfig", "defaults", "overrides", "replaceVariables", "buildTableDataFrame", "Table", "initialSortBy", "onSortByChange", "tableFieldOptions", "Auto", "getStylesActionCell", "symbol", "isSearched", "isSandwiched", "actionCellWrapper", "actionCellButton", "backgroundColor", "ufuzzy", "FlameGraphContainer", "onTableSymbolClick", "onViewSelected", "onTextAlignSelected", "getTheme", "disableCollapsing", "keepFocusOnDataChange", "setFocusedItemData", "setTextAlign", "setSandwichItem", "dataContainer", "setColorScheme", "defaultColorScheme", "useColorScheme", "flexDirection", "minHeight", "body", "tableContainer", "horizontalContainer", "columnGap", "horizontalGraphContainer", "horizontalTableContainer", "maxHeight", "verticalGraphContainer", "verticalTableContainer", "<PERSON><PERSON><PERSON><PERSON>", "useLabelSearch", "resetFocus", "resetSandwich", "flameGraph", "data2", "ThemeContext", "Provider", "view", "autoBom", "console", "warn", "Blob", "XMLHttpRequest", "open", "responseType", "onload", "response", "onerror", "error", "send", "status", "dispatchEvent", "MouseEvent", "createEvent", "initMouseEvent", "global", "userAgent", "saveAs", "HTMLAnchorElement", "URL", "webkitURL", "download", "rel", "href", "location", "createObjectURL", "revokeObjectURL", "msSaveOrOpenBlob", "innerText", "safari", "FileReader", "onloadend", "readAsDataURL", "module", "exports", "tslib_1", "react_1", "useTimeoutFn_1", "__importDefault", "ms", "deps", "default", "isReady", "cancel", "reset", "defineProperty", "ready", "timeout", "clear", "ch2", "u8", "Uint8Array", "u16", "Uint16Array", "i32", "Int32Array", "fleb", "fdeb", "clim", "freb", "eb", "fl", "revfl", "fd", "revfd", "rev", "hMap", "cd", "mb", "co", "le", "rvb", "sv", "r_1", "flt", "fdt", "flm", "flrm", "fdm", "fdrm", "bits", "bits16", "shft", "slc", "subarray", "ec", "err", "ind", "msg", "nt", "code", "captureStackTrace", "inflt", "dat", "st", "buf", "dict", "sl", "dl", "noBuf", "resize", "noSt", "cbuf", "bl", "nbuf", "final", "bt", "lm", "dm", "lbt", "dbt", "tbts", "hLit", "hcLen", "tl", "ldt", "clt", "clb", "clbmsk", "clm", "lt", "dt", "lms", "dms", "lpos", "sym", "dsym", "end", "dend", "wbits", "wbits16", "hTree", "t2", "et", "i0", "i1", "i2", "maxSym", "tr", "mbt", "ln", "lft", "cst", "i2_1", "i2_2", "i2_3", "lc", "cl", "cli", "cln", "cls", "w", "clen", "cf", "wfblk", "wblk", "syms", "lf", "df", "li", "bs", "dlt", "mlb", "ddt", "mdb", "lclt", "nlc", "_d", "lcdt", "ndc", "lcfreq", "_e", "lct", "mlcb", "nlcc", "ll", "flen", "ftlen", "dtlen", "llm", "lcts", "it", "clct", "dst", "deo", "dflt", "lvl", "plvl", "pre", "post", "ceil", "lst", "opt", "msk_1", "prev", "bs1_1", "bs2_1", "hsh", "lc_1", "wi", "hv", "imod", "pimod", "rem", "ch_1", "dif", "maxn", "maxd", "ml", "nl", "mmd", "md", "lin", "din", "crct", "crc", "cr", "<PERSON><PERSON>", "dopt", "dictionary", "newDat", "mem", "log", "mrg", "wcln", "fnStr", "td", "ks", "st_1", "spInd", "ch", "wrkr", "fns", "init", "cb", "td_1", "transfer", "Worker", "onmessage", "ed", "$e$", "postMessage", "wk", "buffer", "cbfs", "bInflt", "inflateSync", "pbf", "gopt", "bDflt", "deflateSync", "gze", "gzh", "gzhl", "wbytes", "guze", "gzs", "gzl", "zle", "zlh", "zule", "zls", "astrm", "strm", "ondata", "ev", "flush", "astrmify", "ext", "terminate", "isArray", "queuedSize", "ondrain", "filename", "mtime", "Date", "now", "flg", "zs", "lv", "StrmOpt", "Deflate", "chunk", "endLen", "newBuf", "AsyncDeflate", "Inflate", "bts", "AsyncInflate", "Gzip", "raw", "AsyncGzip", "<PERSON><PERSON><PERSON>", "onmember", "AsyncGunzip", "_this", "<PERSON><PERSON><PERSON>", "AsyncZlib", "<PERSON><PERSON><PERSON><PERSON>", "AsyncUnzlib", "TextDecoder", "decode", "stream", "queueMicrotask", "wrapSync", "Stream", "hasWorker", "compressors", "decompressors", "make<PERSON><PERSON><PERSON>", "TransformStreamBase", "processors", "TypeError", "Processor", "endCb", "compressor", "controller", "enqueue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "byteOffset", "byteLength", "Promise", "resolve", "highWaterMark", "globals", "globalThis", "CompressionStream", "TransformStream", "DecompressionStream", "makeDecompressionStream", "extendStatics", "setPrototypeOf", "__proto__", "__extends", "__", "create", "__assign", "__rest", "getOwnPropertySymbols", "propertyIsEnumerable", "__decorate", "decorators", "getOwnPropertyDescriptor", "Reflect", "decorate", "__param", "paramIndex", "decorator", "__esDecorate", "ctor", "descriptorIn", "contextIn", "initializers", "extraInitializers", "accept", "kind", "descriptor", "done", "context", "access", "addInitializer", "__runInitializers", "thisArg", "useValue", "__prop<PERSON>ey", "__setFunctionName", "prefix", "configurable", "__metadata", "metadataKey", "metadataValue", "__awaiter", "_arguments", "P", "generator", "reject", "fulfilled", "rejected", "__generator", "sent", "trys", "ops", "verb", "op", "pop", "__createBinding", "k2", "__esModule", "writable", "enumerable", "__exportStar", "__values", "__read", "ar", "__spread", "__spreadA<PERSON>ys", "il", "jl", "__spread<PERSON><PERSON>y", "pack", "__await", "__asyncGenerator", "asyncIterator", "resume", "fulfill", "settle", "__asyncDelegator", "__asyncValues", "__makeTemplateObject", "cooked", "__setModuleDefault", "__importStar", "__classPrivateFieldGet", "receiver", "__classPrivateFieldSet", "__classPrivateFieldIn", "__addDisposableResource", "env", "async", "dispose", "inner", "asyncDispose", "_SuppressedError", "SuppressedError", "suppressed", "message", "__disposeResources", "fail", "<PERSON><PERSON><PERSON><PERSON>", "rec"], "sourceRoot": ""}