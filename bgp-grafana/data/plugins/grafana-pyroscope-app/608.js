(self.webpackChunkgrafana_pyroscope_app=self.webpackChunkgrafana_pyroscope_app||[]).push([[608],{3062:(e,t,n)=>{"use strict";n.d(t,{A:()=>kt});var r=n(2540),i=n(6089);const a=(e,t)=>e>t?1:e<t?-1:0,o=1/0,s=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),l="eexxaacctt",c=/\p{P}/gu,u=["en",{numeric:!0,sensitivity:"base"}],h=(e,t,n)=>e.replace("A-Z",t).replace("a-z",n),f={unicode:!1,alpha:null,interSplit:"[^A-Za-z\\d']+",intraSplit:"[a-z][A-Z]",interBound:"[^A-Za-z\\d]",intraBound:"[A-Za-z]\\d|\\d[A-Za-z]|[a-z][A-Z]",interLft:0,interRgt:0,interChars:".",interIns:o,intraChars:"[a-z\\d']",intraIns:null,intraContr:"'[a-z]{1,2}\\b",intraMode:0,intraSlice:[1,o],intraSub:null,intraTrn:null,intraDel:null,intraFilt:(e,t,n)=>!0,toUpper:e=>e.toLocaleUpperCase(),toLower:e=>e.toLocaleLowerCase(),compare:null,sort:(e,t,n,r=a)=>{let{idx:i,chars:o,terms:s,interLft2:l,interLft1:c,start:u,intraIns:h,interIns:f,cases:d}=e;return i.map(((e,t)=>t)).sort(((e,n)=>o[n]-o[e]||h[e]-h[n]||s[n]+l[n]+.5*c[n]-(s[e]+l[e]+.5*c[e])||f[e]-f[n]||u[e]-u[n]||d[n]-d[e]||r(t[i[e]],t[i[n]])))}},d=(e,t)=>0==t?"":1==t?e+"??":t==o?e+"*?":e+`{0,${t}}?`,p="(?:\\b|_)";function g(e){e=Object.assign({},f,e);let{unicode:t,interLft:n,interRgt:r,intraMode:i,intraSlice:o,intraIns:g,intraSub:m,intraTrn:b,intraDel:y,intraContr:w,intraSplit:x,interSplit:_,intraBound:S,interBound:M,intraChars:C,toUpper:k,toLower:R,compare:T}=e;g??=i,m??=i,b??=i,y??=i,T??="undefined"==typeof Intl?a:new Intl.Collator(...u).compare;let j=e.letters??e.alpha;if(null!=j){let e=k(j),t=R(j);_=h(_,e,t),x=h(x,e,t),M=h(M,e,t),S=h(S,e,t),C=h(C,e,t),w=h(w,e,t)}let A=t?"u":"";const z='".+?"',E=new RegExp(z,"gi"+A),F=new RegExp(`(?:\\s+|^)-(?:${C}+|${z})`,"gi"+A);let{intraRules:D}=e;null==D&&(D=e=>{let t=f.intraSlice,n=0,r=0,i=0,a=0;if(/[^\d]/.test(e)){let s=e.length;s<=4?s>=3&&(i=Math.min(b,1),4==s&&(n=Math.min(g,1))):(t=o,n=g,r=m,i=b,a=y)}return{intraSlice:t,intraIns:n,intraSub:r,intraTrn:i,intraDel:a}});let I=!!x,L=new RegExp(x,"g"+A),B=new RegExp(_,"g"+A),P=new RegExp("^"+_+"|"+_+"$","g"+A),N=new RegExp(w,"gi"+A);const O=(e,t=!1)=>{let n=[];e=(e=e.replace(E,(e=>(n.push(e),l)))).replace(P,""),t||(e=R(e)),I&&(e=e.replace(L,(e=>e[0]+" "+e[1])));let r=0;return e.split(B).filter((e=>""!=e)).map((e=>e===l?n[r++]:e))},H=/[^\d]+|\d+/g,V=(t,a=0,o=!1)=>{let l=O(t);if(0==l.length)return[];let c,u=Array(l.length).fill("");if(l=l.map(((e,t)=>e.replace(N,(e=>(u[t]=e,""))))),1==i)c=l.map(((e,t)=>{if('"'===e[0])return s(e.slice(1,-1));let n="";for(let r of e.matchAll(H)){let e=r[0],{intraSlice:i,intraIns:a,intraSub:o,intraTrn:s,intraDel:l}=D(e);if(a+o+s+l==0)n+=e+u[t];else{let[r,c]=i,h=e.slice(0,r),f=e.slice(c),p=e.slice(r,c);1==a&&1==h.length&&h!=p[0]&&(h+="(?!"+h+")");let g=p.length,m=[e];if(o)for(let e=0;e<g;e++)m.push(h+p.slice(0,e)+C+p.slice(e+1)+f);if(s)for(let e=0;e<g-1;e++)p[e]!=p[e+1]&&m.push(h+p.slice(0,e)+p[e+1]+p[e]+p.slice(e+2)+f);if(l)for(let e=0;e<g;e++)m.push(h+p.slice(0,e+1)+"?"+p.slice(e+1)+f);if(a){let e=d(C,1);for(let t=0;t<g;t++)m.push(h+p.slice(0,t)+e+p.slice(t)+f)}n+="(?:"+m.join("|")+")"+u[t]}}return n}));else{let e=d(C,g);2==a&&g>0&&(e=")("+e+")("),c=l.map(((t,n)=>'"'===t[0]?s(t.slice(1,-1)):t.split("").map(((e,t,n)=>(1==g&&0==t&&n.length>1&&e!=n[t+1]&&(e+="(?!"+e+")"),e))).join(e)+u[n]))}let h=2==n?p:"",f=2==r?p:"",m=f+d(e.interChars,e.interIns)+h;return a>0?o?c=h+"("+c.join(")"+f+"|"+h+"(")+")"+f:(c="("+c.join(")("+m+")(")+")",c="(.??"+h+")"+c+"("+f+".*)"):(c=c.join(m),c=h+c+f),[new RegExp(c,"i"+A),l,u]},W=(e,t,n)=>{let[r]=V(t);if(null==r)return null;let i=[];if(null!=n)for(let t=0;t<n.length;t++){let a=n[t];r.test(e[a])&&i.push(a)}else for(let t=0;t<e.length;t++)r.test(e[t])&&i.push(t);return i};let $=!!S,G=new RegExp(M,A),q=new RegExp(S,A);const U=(t,i,a)=>{let[o,s,l]=V(a,1),c=O(a,!0),[u]=V(a,2),h=s.length,f=Array(h),d=Array(h);for(let e=0;e<h;e++){let t=s[e],n=c[e],r='"'==t[0]?t.slice(1,-1):t+l[e],i='"'==n[0]?n.slice(1,-1):n+l[e];f[e]=r,d[e]=i}let p=t.length,g=Array(p).fill(0),m={idx:Array(p),start:g.slice(),chars:g.slice(),cases:g.slice(),terms:g.slice(),interIns:g.slice(),intraIns:g.slice(),interLft2:g.slice(),interRgt2:g.slice(),interLft1:g.slice(),interRgt1:g.slice(),ranges:Array(p)},v=1==n||1==r,b=0;for(let a=0;a<t.length;a++){let s=i[t[a]],l=s.match(o),c=l.index+l[1].length,p=c,g=!1,y=0,w=0,x=0,_=0,S=0,M=0,C=0,k=0,T=0,j=[];for(let t=0,i=2;t<h;t++,i+=2){let a=R(l[i]),o=f[t],u=d[t],m=o.length,b=a.length,z=a==o;if(l[i]==u&&C++,!z&&l[i+1].length>=m){let e=R(l[i+1]).indexOf(o);e>-1&&(j.push(p,b,e,m),p+=X(l,i,e,m),a=o,b=m,z=!0,0==t&&(c=p))}if(v||z){let e=p-1,u=p+b,h=!1,f=!1;if(-1==e||G.test(s[e]))z&&y++,h=!0;else{if(2==n){g=!0;break}if($&&q.test(s[e]+s[e+1]))z&&w++,h=!0;else if(1==n){let e=l[i+1],n=p+b;if(e.length>=m){let r,u=0,f=!1,d=new RegExp(o,"ig"+A);for(;r=d.exec(e);){u=r.index;let e=n+u,t=e-1;if(-1==t||G.test(s[t])){y++,f=!0;break}if(q.test(s[t]+s[e])){w++,f=!0;break}}f&&(h=!0,j.push(p,b,u,m),p+=X(l,i,u,m),a=o,b=m,z=!0,0==t&&(c=p))}if(!h){g=!0;break}}}if(u==s.length||G.test(s[u]))z&&x++,f=!0;else{if(2==r){g=!0;break}if($&&q.test(s[u-1]+s[u]))z&&_++,f=!0;else if(1==r){g=!0;break}}z&&(S+=m,h&&f&&M++)}if(b>m&&(T+=b-m),t>0&&(k+=l[i-1].length),!e.intraFilt(o,a,p)){g=!0;break}t<h-1&&(p+=b+l[i+1].length)}if(!g){m.idx[b]=t[a],m.interLft2[b]=y,m.interLft1[b]=w,m.interRgt2[b]=x,m.interRgt1[b]=_,m.chars[b]=S,m.terms[b]=M,m.cases[b]=C,m.interIns[b]=k,m.intraIns[b]=T,m.start[b]=c;let e=s.match(u),n=e.index+e[1].length,r=j.length,i=r>0?0:1/0,o=r-4;for(let t=2;t<e.length;){let r=e[t].length;if(i<=o&&j[i]==n){let r=j[i+1],a=j[i+2],o=j[i+3],s=t,l="";for(let t=0;t<r;s++)l+=e[s],t+=e[s].length;e.splice(t,s-t,l),n+=X(e,t,a,o),i+=4}else n+=r,t++}n=e.index+e[1].length;let l=m.ranges[b]=[],h=n,f=n;for(let t=2;t<e.length;t++){let r=e[t].length;n+=r,t%2==0?f=n:r>0&&(l.push(h,f),h=f=n)}f>h&&l.push(h,f),b++}}if(b<t.length)for(let e in m)m[e]=m[e].slice(0,b);return m},X=(e,t,n,r)=>{let i=e[t]+e[t+1].slice(0,n);return e[t-1]+=i,e[t]=e[t+1].slice(n,n+r),e[t+1]=e[t+1].slice(n+r),i.length};return{search:(...t)=>((t,n,r,i=1e3,a)=>{r=r?!0===r?5:r:0;let o=null,l=null,u=[];n=n.replace(F,(e=>{let t=e.trim().slice(1);return t='"'===t[0]?s(t.slice(1,-1)):t.replace(c,""),""!=t&&u.push(t),""}));let h,f=O(n);if(u.length>0){if(h=new RegExp(u.join("|"),"i"+A),0==f.length){let e=[];for(let n=0;n<t.length;n++)h.test(t[n])||e.push(n);return[e,null,null]}}else if(0==f.length)return[null,null,null];if(r>0){let e=O(n);if(e.length>1){let n=e.slice().sort(((e,t)=>t.length-e.length));for(let e=0;e<n.length;e++){if(0==a?.length)return[[],null,null];a=W(t,n[e],a)}if(e.length>r)return[a,null,null];o=v(e).map((e=>e.join(" "))),l=[];let i=new Set;for(let e=0;e<o.length;e++)if(i.size<a.length){let n=a.filter((e=>!i.has(e))),r=W(t,o[e],n);for(let e=0;e<r.length;e++)i.add(r[e]);l.push(r)}else l.push([])}}null==o&&(o=[n],l=[a?.length>0?a:W(t,n)]);let d=null,p=null;if(u.length>0&&(l=l.map((e=>e.filter((e=>!h.test(t[e])))))),l.reduce(((e,t)=>e+t.length),0)<=i){d={},p=[];for(let n=0;n<l.length;n++){let r=l[n];if(null==r||0==r.length)continue;let i=o[n],a=U(r,t,i),s=e.sort(a,t,i,T);if(n>0)for(let e=0;e<s.length;e++)s[e]+=p.length;for(let e in a)d[e]=(d[e]??[]).concat(a[e]);p=p.concat(s)}}return[[].concat(...l),d,p]})(...t),split:O,filter:W,info:U,sort:e.sort}}const m=(()=>{let e={A:"ÁÀÃÂÄĄ",a:"áàãâäą",E:"ÉÈÊËĖ",e:"éèêëę",I:"ÍÌÎÏĮ",i:"íìîïį",O:"ÓÒÔÕÖ",o:"óòôõö",U:"ÚÙÛÜŪŲ",u:"úùûüūų",C:"ÇČĆ",c:"çčć",L:"Ł",l:"ł",N:"ÑŃ",n:"ñń",S:"ŠŚ",s:"šś",Z:"ŻŹ",z:"żź"},t=new Map,n="";for(let r in e)e[r].split("").forEach((e=>{n+=e,t.set(e,r)}));let r=new RegExp(`[${n}]`,"g"),i=e=>t.get(e);return e=>{if("string"==typeof e)return e.replace(r,i);let t=Array(e.length);for(let n=0;n<e.length;n++)t[n]=e[n].replace(r,i);return t}})();function v(e){let t,n,r=(e=e.slice()).length,i=[e.slice()],a=new Array(r).fill(0),o=1;for(;o<r;)a[o]<o?(t=o%2&&a[o],n=e[o],e[o]=e[t],e[t]=n,++a[o],o=1,i.push(e.slice())):(a[o]=0,++o);return i}const b=(e,t)=>t?`<mark>${e}</mark>`:e,y=(e,t)=>e+t;g.latinize=m,g.permute=e=>v([...Array(e.length).keys()]).sort(((e,t)=>{for(let n=0;n<e.length;n++)if(e[n]!=t[n])return e[n]-t[n];return 0})).map((t=>t.map((t=>e[t])))),g.highlight=function(e,t,n=b,r="",i=y){r=i(r,n(e.substring(0,t[0]),!1))??r;for(let a=0;a<t.length;a+=2){let o=t[a],s=t[a+1];r=i(r,n(e.substring(o,s),!0))??r,a<t.length-3&&(r=i(r,n(e.substring(t[a+1],t[a+2]),!1))??r)}return r=i(r,n(e.substring(t[t.length-1]),!1))??r};var w=n(5959),x=function(){};var _="undefined"!=typeof window;const S=_?w.useLayoutEffect:w.useEffect;var M={x:0,y:0,width:0,height:0,top:0,left:0,bottom:0,right:0};const C=_&&void 0!==window.ResizeObserver?function(){var e=(0,w.useState)(null),t=e[0],n=e[1],r=(0,w.useState)(M),i=r[0],a=r[1],o=(0,w.useMemo)((function(){return new window.ResizeObserver((function(e){if(e[0]){var t=e[0].contentRect,n=t.x,r=t.y,i=t.width,o=t.height,s=t.top,l=t.left,c=t.bottom,u=t.right;a({x:n,y:r,width:i,height:o,top:s,left:l,bottom:c,right:u})}}))}),[]);return S((function(){if(t)return o.observe(t),function(){o.disconnect()}}),[t]),[n,i]}:function(){return[x,M]};var k=n(2007);const R=22*window.devicePixelRatio,T=10*window.devicePixelRatio,j=.5*window.devicePixelRatio,A=20*window.devicePixelRatio,z=.5*window.devicePixelRatio,E=4*window.devicePixelRatio,F=3*window.devicePixelRatio,D=3*window.devicePixelRatio,I=4*window.devicePixelRatio,L=2*window.devicePixelRatio,B=120,P=({data:e,itemData:t,onMenuItemClick:n,onItemFocus:i,onSandwich:a,collapseConfig:o,onExpandGroup:s,onCollapseGroup:l,onExpandAllGroups:c,onCollapseAllGroups:u,getExtraContextMenuButtons:h,collapsing:f,allGroupsExpanded:d,allGroupsCollapsed:p,selectedView:g,search:m})=>(0,r.jsx)("div",{"data-testid":"contextMenu",children:(0,r.jsx)(k.ContextMenu,{renderMenuItems:function(){const v=(null==h?void 0:h(t,e.data,{selectedView:g,isDiff:e.isDiffFlamegraph(),search:m,collapseConfig:o}))||[];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(k.MenuItem,{label:"Focus block",icon:"eye",onClick:()=>{i(),n()}}),(0,r.jsx)(k.MenuItem,{label:"Copy function name",icon:"copy",onClick:()=>{navigator.clipboard.writeText(t.label).then((()=>{n()}))}}),(0,r.jsx)(k.MenuItem,{label:"Sandwich view",icon:"gf-show-context",onClick:()=>{a(),n()}}),v.map((({label:e,icon:t,onClick:n})=>(0,r.jsx)(k.MenuItem,{label:e,icon:t,onClick:()=>n()},e))),f&&(0,r.jsxs)(k.MenuGroup,{label:"Grouping",children:[o?o.collapsed?(0,r.jsx)(k.MenuItem,{label:"Expand group",icon:"angle-double-down",onClick:()=>{s(),n()}}):(0,r.jsx)(k.MenuItem,{label:"Collapse group",icon:"angle-double-up",onClick:()=>{l(),n()}}):null,!d&&(0,r.jsx)(k.MenuItem,{label:"Expand all groups",icon:"angle-double-down",onClick:()=>{c(),n()}}),!p&&(0,r.jsx)(k.MenuItem,{label:"Collapse all groups",icon:"angle-double-up",onClick:()=>{u(),n()}})]})]})},x:t.posX+10,y:t.posY,focusOnOpen:!1})});var N=n(7781);const O=({data:e,item:t,totalTicks:n,position:i,collapseConfig:a})=>{const o=(0,k.useStyles2)($);if(!t||!i)return null;let s;if(e.isDiffFlamegraph()){const i=V(e,t,n);s=(0,r.jsx)(k.InteractiveTable,{className:o.tooltipTable,columns:[{id:"label",header:""},{id:"baseline",header:"Baseline"},{id:"comparison",header:"Comparison"},{id:"diff",header:"Diff"}],data:i,getRowId:e=>e.rowId})}else{const i=H(e,t,n);s=(0,r.jsxs)("p",{className:o.lastParagraph,children:[i.unitTitle,(0,r.jsx)("br",{}),"Total: ",(0,r.jsx)("b",{children:i.unitValue})," (",i.percentValue,"%)",(0,r.jsx)("br",{}),"Self: ",(0,r.jsx)("b",{children:i.unitSelf})," (",i.percentSelf,"%)",(0,r.jsx)("br",{}),"Samples: ",(0,r.jsx)("b",{children:i.samples})]})}return(0,r.jsx)(k.Portal,{children:(0,r.jsx)(k.VizTooltipContainer,{className:o.tooltipContainer,position:i,offset:{x:15,y:0},children:(0,r.jsxs)("div",{className:o.tooltipContent,children:[(0,r.jsxs)("p",{className:o.tooltipName,children:[e.getLabel(t.itemIndexes[0]),a&&a.collapsed?(0,r.jsxs)("span",{children:[(0,r.jsx)("br",{}),"and ",a.items.length," similar items"]}):""]}),s]})})})},H=(e,t,n)=>{const r=e.valueDisplayProcessor(t.value),i=e.getSelfDisplay(t.itemIndexes),a=Math.round(r.numeric/n*1e4)/100,o=Math.round(i.numeric/n*1e4)/100;let s=r.text+r.suffix,l=i.text+i.suffix;const c=e.getUnitTitle();return"Count"===c&&(r.suffix||(s=r.text),i.suffix||(l=i.text)),{percentValue:a,percentSelf:o,unitTitle:c,unitValue:s,unitSelf:l,samples:r.numeric.toLocaleString()}},V=(e,t,n)=>{const r=e.getLevels()[0][0].valueRight,i=n-r,a=t.value-t.valueRight,o=Math.round(1e4*a/i)/100,s=Math.round(1e4*t.valueRight/r)/100,l=(s-o)/o*100,c=W(e,e.valueDisplayProcessor(a)),u=W(e,e.valueDisplayProcessor(t.valueRight)),h=(0,N.getValueFormat)("short");return[{rowId:"1",label:"% of total",baseline:o+"%",comparison:s+"%",diff:h(l).text+"%"},{rowId:"2",label:"Value",baseline:c,comparison:u,diff:W(e,e.valueDisplayProcessor(t.valueRight-a))},{rowId:"3",label:"Samples",baseline:h(a).text,comparison:h(t.valueRight).text,diff:h(t.valueRight-a).text}]};function W(e,t){let n=t.text+t.suffix;return"Count"===e.getUnitTitle()&&(t.suffix||(n=t.text)),n}const $=e=>({tooltipContainer:(0,i.css)({title:"tooltipContainer",overflow:"hidden"}),tooltipContent:(0,i.css)({title:"tooltipContent",fontSize:e.typography.bodySmall.fontSize,width:"100%"}),tooltipName:(0,i.css)({title:"tooltipName",marginTop:0,wordBreak:"break-all"}),lastParagraph:(0,i.css)({title:"lastParagraph",marginBottom:0}),name:(0,i.css)({title:"name",marginBottom:"10px"}),tooltipTable:(0,i.css)({title:"tooltipTable",maxWidth:"400px"})});function G(e){return G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},G(e)}var q=/^\s+/,U=/\s+$/;function X(e,t){if(t=t||{},(e=e||"")instanceof X)return e;if(!(this instanceof X))return new X(e,t);var n=function(e){var t={r:0,g:0,b:0},n=1,r=null,i=null,a=null,o=!1,s=!1;"string"==typeof e&&(e=function(e){e=e.replace(q,"").replace(U,"").toLowerCase();var t,n=!1;if(he[e])e=he[e],n=!0;else if("transparent"==e)return{r:0,g:0,b:0,a:0,format:"name"};if(t=Me.rgb.exec(e))return{r:t[1],g:t[2],b:t[3]};if(t=Me.rgba.exec(e))return{r:t[1],g:t[2],b:t[3],a:t[4]};if(t=Me.hsl.exec(e))return{h:t[1],s:t[2],l:t[3]};if(t=Me.hsla.exec(e))return{h:t[1],s:t[2],l:t[3],a:t[4]};if(t=Me.hsv.exec(e))return{h:t[1],s:t[2],v:t[3]};if(t=Me.hsva.exec(e))return{h:t[1],s:t[2],v:t[3],a:t[4]};if(t=Me.hex8.exec(e))return{r:me(t[1]),g:me(t[2]),b:me(t[3]),a:we(t[4]),format:n?"name":"hex8"};if(t=Me.hex6.exec(e))return{r:me(t[1]),g:me(t[2]),b:me(t[3]),format:n?"name":"hex"};if(t=Me.hex4.exec(e))return{r:me(t[1]+""+t[1]),g:me(t[2]+""+t[2]),b:me(t[3]+""+t[3]),a:we(t[4]+""+t[4]),format:n?"name":"hex8"};if(t=Me.hex3.exec(e))return{r:me(t[1]+""+t[1]),g:me(t[2]+""+t[2]),b:me(t[3]+""+t[3]),format:n?"name":"hex"};return!1}(e));"object"==G(e)&&(Ce(e.r)&&Ce(e.g)&&Ce(e.b)?(l=e.r,c=e.g,u=e.b,t={r:255*pe(l,255),g:255*pe(c,255),b:255*pe(u,255)},o=!0,s="%"===String(e.r).substr(-1)?"prgb":"rgb"):Ce(e.h)&&Ce(e.s)&&Ce(e.v)?(r=be(e.s),i=be(e.v),t=function(e,t,n){e=6*pe(e,360),t=pe(t,100),n=pe(n,100);var r=Math.floor(e),i=e-r,a=n*(1-t),o=n*(1-i*t),s=n*(1-(1-i)*t),l=r%6,c=[n,o,a,a,s,n][l],u=[s,n,n,o,a,a][l],h=[a,a,s,n,n,o][l];return{r:255*c,g:255*u,b:255*h}}(e.h,r,i),o=!0,s="hsv"):Ce(e.h)&&Ce(e.s)&&Ce(e.l)&&(r=be(e.s),a=be(e.l),t=function(e,t,n){var r,i,a;function o(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}if(e=pe(e,360),t=pe(t,100),n=pe(n,100),0===t)r=i=a=n;else{var s=n<.5?n*(1+t):n+t-n*t,l=2*n-s;r=o(l,s,e+1/3),i=o(l,s,e),a=o(l,s,e-1/3)}return{r:255*r,g:255*i,b:255*a}}(e.h,r,a),o=!0,s="hsl"),e.hasOwnProperty("a")&&(n=e.a));var l,c,u;return n=de(n),{ok:o,format:e.format||s,r:Math.min(255,Math.max(t.r,0)),g:Math.min(255,Math.max(t.g,0)),b:Math.min(255,Math.max(t.b,0)),a:n}}(e);this._originalInput=e,this._r=n.r,this._g=n.g,this._b=n.b,this._a=n.a,this._roundA=Math.round(100*this._a)/100,this._format=t.format||n.format,this._gradientType=t.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=n.ok}function Z(e,t,n){e=pe(e,255),t=pe(t,255),n=pe(n,255);var r,i,a=Math.max(e,t,n),o=Math.min(e,t,n),s=(a+o)/2;if(a==o)r=i=0;else{var l=a-o;switch(i=s>.5?l/(2-a-o):l/(a+o),a){case e:r=(t-n)/l+(t<n?6:0);break;case t:r=(n-e)/l+2;break;case n:r=(e-t)/l+4}r/=6}return{h:r,s:i,l:s}}function Y(e,t,n){e=pe(e,255),t=pe(t,255),n=pe(n,255);var r,i,a=Math.max(e,t,n),o=Math.min(e,t,n),s=a,l=a-o;if(i=0===a?0:l/a,a==o)r=0;else{switch(a){case e:r=(t-n)/l+(t<n?6:0);break;case t:r=(n-e)/l+2;break;case n:r=(e-t)/l+4}r/=6}return{h:r,s:i,v:s}}function K(e,t,n,r){var i=[ve(Math.round(e).toString(16)),ve(Math.round(t).toString(16)),ve(Math.round(n).toString(16))];return r&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function J(e,t,n,r){return[ve(ye(r)),ve(Math.round(e).toString(16)),ve(Math.round(t).toString(16)),ve(Math.round(n).toString(16))].join("")}function Q(e,t){t=0===t?0:t||10;var n=X(e).toHsl();return n.s-=t/100,n.s=ge(n.s),X(n)}function ee(e,t){t=0===t?0:t||10;var n=X(e).toHsl();return n.s+=t/100,n.s=ge(n.s),X(n)}function te(e){return X(e).desaturate(100)}function ne(e,t){t=0===t?0:t||10;var n=X(e).toHsl();return n.l+=t/100,n.l=ge(n.l),X(n)}function re(e,t){t=0===t?0:t||10;var n=X(e).toRgb();return n.r=Math.max(0,Math.min(255,n.r-Math.round(-t/100*255))),n.g=Math.max(0,Math.min(255,n.g-Math.round(-t/100*255))),n.b=Math.max(0,Math.min(255,n.b-Math.round(-t/100*255))),X(n)}function ie(e,t){t=0===t?0:t||10;var n=X(e).toHsl();return n.l-=t/100,n.l=ge(n.l),X(n)}function ae(e,t){var n=X(e).toHsl(),r=(n.h+t)%360;return n.h=r<0?360+r:r,X(n)}function oe(e){var t=X(e).toHsl();return t.h=(t.h+180)%360,X(t)}function se(e,t){if(isNaN(t)||t<=0)throw new Error("Argument to polyad must be a positive number");for(var n=X(e).toHsl(),r=[X(e)],i=360/t,a=1;a<t;a++)r.push(X({h:(n.h+a*i)%360,s:n.s,l:n.l}));return r}function le(e){var t=X(e).toHsl(),n=t.h;return[X(e),X({h:(n+72)%360,s:t.s,l:t.l}),X({h:(n+216)%360,s:t.s,l:t.l})]}function ce(e,t,n){t=t||6,n=n||30;var r=X(e).toHsl(),i=360/n,a=[X(e)];for(r.h=(r.h-(i*t>>1)+720)%360;--t;)r.h=(r.h+i)%360,a.push(X(r));return a}function ue(e,t){t=t||6;for(var n=X(e).toHsv(),r=n.h,i=n.s,a=n.v,o=[],s=1/t;t--;)o.push(X({h:r,s:i,v:a})),a=(a+s)%1;return o}X.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},getLuminance:function(){var e,t,n,r=this.toRgb();return e=r.r/255,t=r.g/255,n=r.b/255,.2126*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))+.7152*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.0722*(n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))},setAlpha:function(e){return this._a=de(e),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var e=Y(this._r,this._g,this._b);return{h:360*e.h,s:e.s,v:e.v,a:this._a}},toHsvString:function(){var e=Y(this._r,this._g,this._b),t=Math.round(360*e.h),n=Math.round(100*e.s),r=Math.round(100*e.v);return 1==this._a?"hsv("+t+", "+n+"%, "+r+"%)":"hsva("+t+", "+n+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var e=Z(this._r,this._g,this._b);return{h:360*e.h,s:e.s,l:e.l,a:this._a}},toHslString:function(){var e=Z(this._r,this._g,this._b),t=Math.round(360*e.h),n=Math.round(100*e.s),r=Math.round(100*e.l);return 1==this._a?"hsl("+t+", "+n+"%, "+r+"%)":"hsla("+t+", "+n+"%, "+r+"%, "+this._roundA+")"},toHex:function(e){return K(this._r,this._g,this._b,e)},toHexString:function(e){return"#"+this.toHex(e)},toHex8:function(e){return function(e,t,n,r,i){var a=[ve(Math.round(e).toString(16)),ve(Math.round(t).toString(16)),ve(Math.round(n).toString(16)),ve(ye(r))];if(i&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)&&a[3].charAt(0)==a[3].charAt(1))return a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0);return a.join("")}(this._r,this._g,this._b,this._a,e)},toHex8String:function(e){return"#"+this.toHex8(e)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(100*pe(this._r,255))+"%",g:Math.round(100*pe(this._g,255))+"%",b:Math.round(100*pe(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+Math.round(100*pe(this._r,255))+"%, "+Math.round(100*pe(this._g,255))+"%, "+Math.round(100*pe(this._b,255))+"%)":"rgba("+Math.round(100*pe(this._r,255))+"%, "+Math.round(100*pe(this._g,255))+"%, "+Math.round(100*pe(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(fe[K(this._r,this._g,this._b,!0)]||!1)},toFilter:function(e){var t="#"+J(this._r,this._g,this._b,this._a),n=t,r=this._gradientType?"GradientType = 1, ":"";if(e){var i=X(e);n="#"+J(i._r,i._g,i._b,i._a)}return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+t+",endColorstr="+n+")"},toString:function(e){var t=!!e;e=e||this._format;var n=!1,r=this._a<1&&this._a>=0;return t||!r||"hex"!==e&&"hex6"!==e&&"hex3"!==e&&"hex4"!==e&&"hex8"!==e&&"name"!==e?("rgb"===e&&(n=this.toRgbString()),"prgb"===e&&(n=this.toPercentageRgbString()),"hex"!==e&&"hex6"!==e||(n=this.toHexString()),"hex3"===e&&(n=this.toHexString(!0)),"hex4"===e&&(n=this.toHex8String(!0)),"hex8"===e&&(n=this.toHex8String()),"name"===e&&(n=this.toName()),"hsl"===e&&(n=this.toHslString()),"hsv"===e&&(n=this.toHsvString()),n||this.toHexString()):"name"===e&&0===this._a?this.toName():this.toRgbString()},clone:function(){return X(this.toString())},_applyModification:function(e,t){var n=e.apply(null,[this].concat([].slice.call(t)));return this._r=n._r,this._g=n._g,this._b=n._b,this.setAlpha(n._a),this},lighten:function(){return this._applyModification(ne,arguments)},brighten:function(){return this._applyModification(re,arguments)},darken:function(){return this._applyModification(ie,arguments)},desaturate:function(){return this._applyModification(Q,arguments)},saturate:function(){return this._applyModification(ee,arguments)},greyscale:function(){return this._applyModification(te,arguments)},spin:function(){return this._applyModification(ae,arguments)},_applyCombination:function(e,t){return e.apply(null,[this].concat([].slice.call(t)))},analogous:function(){return this._applyCombination(ce,arguments)},complement:function(){return this._applyCombination(oe,arguments)},monochromatic:function(){return this._applyCombination(ue,arguments)},splitcomplement:function(){return this._applyCombination(le,arguments)},triad:function(){return this._applyCombination(se,[3])},tetrad:function(){return this._applyCombination(se,[4])}},X.fromRatio=function(e,t){if("object"==G(e)){var n={};for(var r in e)e.hasOwnProperty(r)&&(n[r]="a"===r?e[r]:be(e[r]));e=n}return X(e,t)},X.equals=function(e,t){return!(!e||!t)&&X(e).toRgbString()==X(t).toRgbString()},X.random=function(){return X.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})},X.mix=function(e,t,n){n=0===n?0:n||50;var r=X(e).toRgb(),i=X(t).toRgb(),a=n/100;return X({r:(i.r-r.r)*a+r.r,g:(i.g-r.g)*a+r.g,b:(i.b-r.b)*a+r.b,a:(i.a-r.a)*a+r.a})},X.readability=function(e,t){var n=X(e),r=X(t);return(Math.max(n.getLuminance(),r.getLuminance())+.05)/(Math.min(n.getLuminance(),r.getLuminance())+.05)},X.isReadable=function(e,t,n){var r,i,a=X.readability(e,t);switch(i=!1,(r=function(e){var t,n;t=((e=e||{level:"AA",size:"small"}).level||"AA").toUpperCase(),n=(e.size||"small").toLowerCase(),"AA"!==t&&"AAA"!==t&&(t="AA");"small"!==n&&"large"!==n&&(n="small");return{level:t,size:n}}(n)).level+r.size){case"AAsmall":case"AAAlarge":i=a>=4.5;break;case"AAlarge":i=a>=3;break;case"AAAsmall":i=a>=7}return i},X.mostReadable=function(e,t,n){var r,i,a,o,s=null,l=0;i=(n=n||{}).includeFallbackColors,a=n.level,o=n.size;for(var c=0;c<t.length;c++)(r=X.readability(e,t[c]))>l&&(l=r,s=X(t[c]));return X.isReadable(e,s,{level:a,size:o})||!i?s:(n.includeFallbackColors=!1,X.mostReadable(e,["#fff","#000"],n))};var he=X.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},fe=X.hexNames=function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}(he);function de(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function pe(e,t){(function(e){return"string"==typeof e&&-1!=e.indexOf(".")&&1===parseFloat(e)})(e)&&(e="100%");var n=function(e){return"string"==typeof e&&-1!=e.indexOf("%")}(e);return e=Math.min(t,Math.max(0,parseFloat(e))),n&&(e=parseInt(e*t,10)/100),Math.abs(e-t)<1e-6?1:e%t/parseFloat(t)}function ge(e){return Math.min(1,Math.max(0,e))}function me(e){return parseInt(e,16)}function ve(e){return 1==e.length?"0"+e:""+e}function be(e){return e<=1&&(e=100*e+"%"),e}function ye(e){return Math.round(255*parseFloat(e)).toString(16)}function we(e){return me(e)/255}var xe,_e,Se,Me=(_e="[\\s|\\(]+("+(xe="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+xe+")[,|\\s]+("+xe+")\\s*\\)?",Se="[\\s|\\(]+("+xe+")[,|\\s]+("+xe+")[,|\\s]+("+xe+")[,|\\s]+("+xe+")\\s*\\)?",{CSS_UNIT:new RegExp(xe),rgb:new RegExp("rgb"+_e),rgba:new RegExp("rgba"+Se),hsl:new RegExp("hsl"+_e),hsla:new RegExp("hsla"+Se),hsv:new RegExp("hsv"+_e),hsva:new RegExp("hsva"+Se),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function Ce(e){return!!Me.CSS_UNIT.exec(e)}var ke=(e=>(e.Bytes="bytes",e.Short="short",e.Nanoseconds="ns",e))(ke||{}),Re=(e=>(e.TopTable="topTable",e.FlameGraph="flameGraph",e.Both="both",e))(Re||{}),Te=(e=>(e.ValueBased="valueBased",e.PackageBased="packageBased",e))(Te||{}),je=(e=>(e.Default="default",e.DiffColorBlind="diffColorBlind",e))(je||{}),Ae=n(4201);const ze=[X({h:24,s:69,l:60}),X({h:34,s:65,l:65}),X({h:194,s:52,l:61}),X({h:163,s:45,l:55}),X({h:211,s:48,l:60}),X({h:246,s:40,l:65}),X({h:305,s:63,l:79}),X({h:47,s:100,l:73}),X({r:183,g:219,b:171}),X({r:244,g:213,b:152}),X({r:78,g:146,b:249}),X({r:249,g:186,b:143}),X({r:242,g:145,b:145}),X({r:130,g:181,b:216}),X({r:229,g:168,b:226}),X({r:174,g:162,b:224}),X({r:154,g:196,b:138}),X({r:242,g:201,b:109}),X({r:101,g:197,b:219}),X({r:249,g:147,b:78}),X({r:234,g:100,b:96}),X({r:81,g:149,b:206}),X({r:214,g:131,b:206}),X({r:128,g:110,b:183})],Ee=`linear-gradient(90deg, ${De(1,100,0,1)} 0%, ${De(100,100,0,1)} 100%)`,Fe=`linear-gradient(90deg, ${ze[0]} 0%, ${ze[2]} 30%, ${ze[6]} 50%, ${ze[7]} 70%, ${ze[8]} 100%)`;function De(e,t,n,r){const i=Math.min(1,e/t/(r-n));return X({h:50-50*i,s:100,l:65+7*i})}function Ie(e,t){const n=function(e,t=0){let n,r,i,a,o,s,l,c;for(n=3&e.length,r=e.length-n,i=t,o=3432918353,s=461845907,c=0;c<r;)l=255&e.charCodeAt(c)|(255&e.charCodeAt(++c))<<8|(255&e.charCodeAt(++c))<<16|(255&e.charCodeAt(++c))<<24,++c,l=(65535&l)*o+(((l>>>16)*o&65535)<<16)&4294967295,l=l<<15|l>>>17,l=(65535&l)*s+(((l>>>16)*s&65535)<<16)&4294967295,i^=l,i=i<<13|i>>>19,a=5*(65535&i)+((5*(i>>>16)&65535)<<16)&4294967295,i=27492+(65535&a)+((58964+(a>>>16)&65535)<<16);switch(l=0,n){case 3:l^=(255&e.charCodeAt(c+2))<<16;case 2:l^=(255&e.charCodeAt(c+1))<<8;case 1:l^=255&e.charCodeAt(c);default:l=(65535&l)*o+(((l>>>16)*o&65535)<<16)&4294967295,l=l<<15|l>>>17,l=(65535&l)*s+(((l>>>16)*s&65535)<<16)&4294967295,i^=l}return i^=e.length,i^=i>>>16,i=2246822507*(65535&i)+((2246822507*(i>>>16)&65535)<<16)&4294967295,i^=i>>>13,i=3266489909*(65535&i)+((3266489909*(i>>>16)&65535)<<16)&4294967295,i^=i>>>16,i>>>0}(function(e){var t;for(const[n,r]of Oe){const n=e.match(r);if(n)return(null==(t=n.groups)?void 0:t.packageName)||""}return}(e)||"",0)%ze.length;let r=ze[n].clone();return t.isLight&&(r=r.brighten(15)),r}const Le=["rgb(0, 170, 0)","rgb(148, 142, 142)","rgb(200, 0, 0)"],Be=`linear-gradient(90deg, ${Le[0]} 0%, ${Le[1]} 50%, ${Le[2]} 100%)`,Pe=["rgb(26, 133, 255)","rgb(148, 142, 142)","rgb(220, 50, 32)"],Ne=`linear-gradient(90deg, ${Pe[0]} 0%, ${Pe[1]} 50%, ${Pe[2]} 100%)`;const Oe=[["phpspy",/^(?<packageName>([^\/]*\/)*)(?<filename>.*\.php+)(?<line_info>.*)$/],["pyspy",/^(?<packageName>([^\/]*\/)*)(?<filename>.*\.py+)(?<line_info>.*)$/],["rbspy",/^(?<packageName>([^\/]*\/)*)(?<filename>.*\.rb+)(?<line_info>.*)$/],["nodespy",/^(\.\/node_modules\/)?(?<packageName>[^/]*)(?<filename>.*\.?(jsx?|tsx?)?):(?<functionName>.*):(?<line_info>.*)$/],["gospy",/^(?<packageName>.*?\/.*?\.|.*?\.|.+)(?<functionName>.*)$/],["javaspy",/^(?<packageName>.+\/)(?<filename>.+\.)(?<functionName>.+)$/],["dotnetspy",/^(?<packageName>.+)\.(.+)\.(.+)\(.*\)$/],["tracing",/^(?<packageName>.+?):.*$/],["pyroscope-rs",/^(?<packageName>[^::]+)/],["ebpfspy",/^(?<packageName>.+)$/],["unknown",/^(?<packageName>.+)$/]];function He(e){const{canvasRef:t,data:n,root:r,depth:i,direction:a,wrapperWidth:o,rangeMin:s,rangeMax:l,matchedLabels:c,textAlign:u,totalViewTicks:h,totalColorTicks:f,totalTicksRight:d,colorScheme:p,focusedItemData:g,collapsedMap:m}=e,v=function(e,t,n){const[r,i]=(0,w.useState)();return(0,w.useEffect)((()=>{if(!n||!e.current)return;const r=e.current.getContext("2d"),a=R*n;e.current.width=Math.round(t*window.devicePixelRatio),e.current.height=Math.round(a),e.current.style.width=`${t}px`,e.current.style.height=a/window.devicePixelRatio+"px",r.textBaseline="middle",r.font=12*window.devicePixelRatio+"px monospace",r.strokeStyle="white",i(r)}),[e,i,t,n]),r}(t,o,i),b=(0,k.useTheme2)(),y=(0,w.useMemo)((()=>{const e=X(b.colors.background.secondary);return b.isLight?e.darken(10).toHexString():e.lighten(10).toHexString()}),[b]),x=function(e,t,n,r,i,a,o,s,l){return(0,w.useCallback)((function(c,u,h){if(h&&!s)return i;const f=void 0===c.valueRight||n!==je.Default&&n!==je.DiffColorBlind?n===Te.ValueBased?De(c.value,e,a,o):Ie(u,r):function(e,t,n,r,i){const a=i===je.Default?Le:Pe,o=(0,Ae.scaleLinear)().domain([-100,0,100]).range(a),s=e-t,l=n-r;if(0===r||0===l)return X(o(0));const c=Math.round(1e4*s/l)/100;return X(o((Math.round(1e4*t/r)/100-c)/c*100))}(c.value,c.valueRight,e,t,n);return s?s.has(u)?f.toHslString():i:c.level>l-1?f.toHslString():f.lighten(15).toHslString()}),[e,t,n,r,a,o,s,l,i])}(f,d,p,b,y,s,l,c,g?g.item.level:0),_=function(e,t,n,r,i){return(0,w.useMemo)((()=>{if(!e)return()=>{};return(a,o,s,l,c,u)=>{e.beginPath(),e.rect(o+z,s,l,c),e.fillStyle=n(a,u,!1),e.stroke(),e.fill();const h=i.get(a);let f=u;if(h&&h.collapsed){f=`(${h.items.length}) `+u}l>=A&&(h?(Ve(e,t,f,a,l,"left"===r?o+I+L:o,s,r),function(e,t,n,r,i,a){const o=t+I;e.beginPath(),e.rect(t,n,o-t+F+D,r),e.fill(),e.beginPath(),a.collapsed?e.rect(o,n+r/4,F,r/2):a.items[0]===i?e.rect(o,n+r/2,F,r/2):a.items[a.items.length-1]===i?e.rect(o,n,F,r/2):e.rect(o,n,F,r);e.fillStyle="#666",e.fill()}(e,o,s,c,a,h)):Ve(e,t,f,a,l,o,s,r))}}),[e,n,r,t,i])}(v,n,x,u,m);(0,w.useEffect)((()=>{if(!v)return;v.clearRect(0,0,v.canvas.width,v.canvas.height);const e=new Path2D;!function(e,t,n,r,i,a,o,s,l){const c=[];c.push({item:e,levelOffset:0});const u=o*window.devicePixelRatio/r/(a-i);let h;for(;c.length>0;){const{item:e,levelOffset:a}=c.shift();let o=e.value;const f=o*u<=T,d=o*u-(f?0:2*z),p=R;if(d<j)continue;let g=0,m=!1;const v=s.get(e),b=v&&v.collapsed;if(b&&h===v.items[0]?(g="children"===t?-1:1,m=!0):h=void 0,!m){b&&(h=e),l(e,We(e.start,r,i,u),(e.level+a)*R,d,p,n.getLabel(e.itemIndexes[0]),f)}const y="children"===t?e.children:e.parents;y&&c.unshift(...y.map((e=>({item:e,levelOffset:a+g}))))}}(r,a,n,h,s,l,o,m,((t,n,r,i,a,o,s)=>{s?e.rect(n,r,i,a):_(t,n,r,i,a,o)})),v.fillStyle=y,v.fill(e)}),[v,n,r,o,s,l,h,a,_,m,y])}function Ve(e,t,n,r,i,a,o,s){e.save(),e.clip(),e.fillStyle="#222";const l=t.valueDisplayProcessor(r.value),c=l.suffix?l.text+l.suffix:l.text,u=e.measureText(n),h=i-E;let f=`${n} (${c})`,d=Math.max(a,0)+E;u.width>h&&(e.textAlign=s,"right"===s&&(f=n,d=a+i-E)),e.fillText(f,d,o+R/2+2),e.restore()}function We(e,t,n,r){return(e-t*n)*r}const $e=({data:e,rangeMin:t,rangeMax:n,matchedLabels:i,setRangeMin:a,setRangeMax:o,onItemFocused:s,focusedItemData:l,textAlign:c,onSandwich:u,colorScheme:h,totalProfileTicks:f,totalProfileTicksRight:d,totalViewTicks:p,root:g,direction:m,depth:v,showFlameGraphOnly:b,collapsedMap:y,setCollapsedMap:x,collapsing:_,getExtraContextMenuButtons:S,selectedView:M,search:k})=>{const R=Ge(),[T,{width:j}]=C(),A=(0,w.useRef)(null),[z,E]=(0,w.useState)(),[F,D]=(0,w.useState)();He({canvasRef:A,colorScheme:h,data:e,focusedItemData:l,root:g,direction:m,depth:v,rangeMax:n,rangeMin:t,matchedLabels:i,textAlign:c,totalViewTicks:p,totalColorTicks:e.isDiffFlamegraph()?f:p,totalTicksRight:d,wrapperWidth:j,collapsedMap:y});const I=(0,w.useCallback)((r=>{E(void 0);const i=A.current.clientWidth/p/(n-t),a=qe({x:r.nativeEvent.offsetX,y:r.nativeEvent.offsetY},g,m,v,i,p,t,y);D(a?{posY:r.clientY,posX:r.clientX,item:a,label:e.getLabel(a.itemIndexes[0])}:void 0)}),[e,t,n,p,g,m,v,y]),[L,B]=(0,w.useState)(),N=(0,w.useCallback)((e=>{if(void 0===F){E(void 0),B(void 0);const r=A.current.clientWidth/p/(n-t),i=qe({x:e.nativeEvent.offsetX,y:e.nativeEvent.offsetY},g,m,v,r,p,t,y);i&&(B({x:e.clientX,y:e.clientY}),E(i))}}),[t,n,p,F,B,g,m,v,y]),H=(0,w.useCallback)((()=>{E(void 0)}),[]);return(0,w.useEffect)((()=>{const e=e=>{var t;e.target instanceof HTMLElement&&"flameGraphCanvasContainer_clickOutsideCheck"!==(null==(t=e.target.parentElement)?void 0:t.id)&&D(void 0)};return window.addEventListener("click",e),()=>window.removeEventListener("click",e)}),[D]),(0,r.jsxs)("div",{className:R.graph,children:[(0,r.jsx)("div",{className:R.canvasWrapper,id:"flameGraphCanvasContainer_clickOutsideCheck",ref:T,children:(0,r.jsx)("canvas",{ref:A,"data-testid":"flameGraph",onClick:I,onMouseMove:N,onMouseLeave:H})}),(0,r.jsx)(O,{position:L,item:z,data:e,totalTicks:p,collapseConfig:z?y.get(z):void 0}),!b&&F&&(0,r.jsx)(P,{data:e,itemData:F,collapsing:_,collapseConfig:y.get(F.item),onMenuItemClick:()=>{D(void 0)},onItemFocus:()=>{a(F.item.start/p),o((F.item.start+F.item.value)/p),s(F)},onSandwich:()=>{u(e.getLabel(F.item.itemIndexes[0]))},onExpandGroup:()=>{x(y.setCollapsedStatus(F.item,!1))},onCollapseGroup:()=>{x(y.setCollapsedStatus(F.item,!0))},onExpandAllGroups:()=>{x(y.setAllCollapsedStatus(!1))},onCollapseAllGroups:()=>{x(y.setAllCollapsedStatus(!0))},allGroupsCollapsed:Array.from(y.values()).every((e=>e.collapsed)),allGroupsExpanded:Array.from(y.values()).every((e=>!e.collapsed)),getExtraContextMenuButtons:S,selectedView:M,search:k})]})},Ge=()=>({graph:(0,i.css)({label:"graph",overflow:"auto",flexGrow:1,flexBasis:"50%"}),canvasContainer:(0,i.css)({label:"canvasContainer",display:"flex"}),canvasWrapper:(0,i.css)({label:"canvasWrapper",cursor:"pointer",flex:1,overflow:"hidden"}),sandwichMarker:(0,i.css)({label:"sandwichMarker",writingMode:"vertical-lr",transform:"rotate(180deg)",overflow:"hidden",whiteSpace:"nowrap"}),sandwichMarkerIcon:(0,i.css)({label:"sandwichMarkerIcon",verticalAlign:"baseline"})}),qe=(e,t,n,r,i,a,o,s)=>{let l=t,c="children"===n?0:r-1;const u=Math.floor(e.y/(R/window.devicePixelRatio));let h;for(;l;){const t=l;if(l=void 0,c===u){h=t;break}const r="children"===n?t.children:t.parents||[];for(const t of r){const r=We(t.start,a,o,i),u=We(t.start+t.value,a,o,i);if(r<=e.x&&e.x<u){l=t;const e=s.get(t);e&&e.collapsed&&e.items[0]!==t||(c+="children"===n?1:-1);break}}}return h},Ue=(0,w.memo)((({data:e,focusedItem:t,totalTicks:n,sandwichedLabel:i,onFocusPillClick:a,onSandwichPillClick:o})=>{const s=(0,k.useStyles2)(Xe),l=[],c=(0,N.getValueFormat)("short")(n),u=e.valueDisplayProcessor(n);let h=u.text+u.suffix;const f=e.getUnitTitle();if("Count"===f&&(u.suffix||(h=u.text)),l.push((0,r.jsxs)("div",{className:s.metadataPill,children:[h," | ",c.text,c.suffix," samples (",f,")"]},"default")),i&&l.push((0,r.jsx)(k.Tooltip,{content:i,placement:"top",children:(0,r.jsxs)("div",{children:[(0,r.jsx)(k.Icon,{size:"sm",name:"angle-right"}),(0,r.jsxs)("div",{className:s.metadataPill,children:[(0,r.jsx)(k.Icon,{size:"sm",name:"gf-show-context"})," ",(0,r.jsx)("span",{className:s.metadataPillName,children:i.substring(i.lastIndexOf("/")+1)}),(0,r.jsx)(k.IconButton,{className:s.pillCloseButton,name:"times",size:"sm",onClick:o,tooltip:"Remove sandwich view","aria-label":"Remove sandwich view"})]})]})},"sandwich")),t){const e=n>0?Math.round(t.item.value/n*1e4)/100:0,i=e>0?"eye":"exclamation-circle";l.push((0,r.jsx)(k.Tooltip,{content:t.label,placement:"top",children:(0,r.jsxs)("div",{children:[(0,r.jsx)(k.Icon,{size:"sm",name:"angle-right"}),(0,r.jsxs)("div",{className:s.metadataPill,children:[(0,r.jsx)(k.Icon,{size:"sm",name:i})," ",e,"% of total",(0,r.jsx)(k.IconButton,{className:s.pillCloseButton,name:"times",size:"sm",onClick:a,tooltip:"Remove focus","aria-label":"Remove focus"})]})]})},"focus"))}return(0,r.jsx)("div",{className:s.metadata,children:l})}));Ue.displayName="FlameGraphMetadata";const Xe=e=>({metadataPill:(0,i.css)({label:"metadataPill",display:"inline-flex",alignItems:"center",background:e.colors.background.secondary,borderRadius:e.shape.borderRadius(8),padding:e.spacing(.5,1),fontSize:e.typography.bodySmall.fontSize,fontWeight:e.typography.fontWeightMedium,lineHeight:e.typography.bodySmall.lineHeight,color:e.colors.text.secondary}),pillCloseButton:(0,i.css)({label:"pillCloseButton",verticalAlign:"text-bottom",margin:e.spacing(0,.5)}),metadata:(0,i.css)({display:"flex",alignItems:"center",justifyContent:"center",margin:"8px 0"}),metadataPillName:(0,i.css)({label:"metadataPillName",maxWidth:"200px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",marginLeft:e.spacing(.5)})}),Ze=({data:e,rangeMin:t,rangeMax:n,matchedLabels:a,setRangeMin:o,setRangeMax:s,onItemFocused:l,focusedItemData:c,textAlign:u,onSandwich:h,sandwichItem:f,onFocusPillClick:d,onSandwichPillClick:p,colorScheme:g,showFlameGraphOnly:m,getExtraContextMenuButtons:v,collapsing:b,selectedView:y,search:x,collapsedMap:_,setCollapsedMap:S})=>{const M=Ye(),[C,R]=(0,w.useState)(),[T,j]=(0,w.useState)(),[A,z]=(0,w.useState)(0),[E,F]=(0,w.useState)(),[D,I]=(0,w.useState)(0);if((0,w.useEffect)((()=>{var t,n,r;if(e){let i,a=e.getLevels(),o=a.length?a[0][0].value:0,s=a.length?a[0][0].valueRight:void 0,l=o;if(f){const[o,s]=e.getSandwichLevels(f);a=s,i=o,l=null!=(r=null==(n=null==(t=s[0])?void 0:t[0])?void 0:n.value)?r:0}R(a),j(i),z(o),F(s),I(l)}}),[e,f]),!C)return null;const L={data:e,rangeMin:t,rangeMax:n,matchedLabels:a,setRangeMin:o,setRangeMax:s,onItemFocused:l,focusedItemData:c,textAlign:u,onSandwich:h,colorScheme:g,totalProfileTicks:A,totalProfileTicksRight:E,totalViewTicks:D,showFlameGraphOnly:m,collapsedMap:_,setCollapsedMap:S,getExtraContextMenuButtons:v,collapsing:b,search:x,selectedView:y};let B=null;return(null==T?void 0:T.length)?B=(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:M.sandwichCanvasWrapper,children:[(0,r.jsxs)("div",{className:M.sandwichMarker,children:["Callers",(0,r.jsx)(k.Icon,{className:M.sandwichMarkerIcon,name:"arrow-down"})]}),(0,r.jsx)($e,{...L,root:T[T.length-1][0],depth:T.length,direction:"parents",collapsing:!1})]}),(0,r.jsxs)("div",{className:M.sandwichCanvasWrapper,children:[(0,r.jsxs)("div",{className:(0,i.cx)(M.sandwichMarker,M.sandwichMarkerCalees),children:[(0,r.jsx)(k.Icon,{className:M.sandwichMarkerIcon,name:"arrow-up"}),"Callees"]}),(0,r.jsx)($e,{...L,root:C[0][0],depth:C.length,direction:"children",collapsing:!1})]})]}):(null==C?void 0:C.length)&&(B=(0,r.jsx)($e,{...L,root:C[0][0],depth:C.length,direction:"children"})),(0,r.jsxs)("div",{className:M.graph,children:[(0,r.jsx)(Ue,{data:e,focusedItem:c,sandwichedLabel:f,totalTicks:D,onFocusPillClick:d,onSandwichPillClick:p}),B]})},Ye=()=>({graph:(0,i.css)({label:"graph",overflow:"auto",flexGrow:1,flexBasis:"50%"}),sandwichCanvasWrapper:(0,i.css)({label:"sandwichCanvasWrapper",display:"flex",marginBottom:R/window.devicePixelRatio+"px"}),sandwichMarker:(0,i.css)({label:"sandwichMarker",writingMode:"vertical-lr",transform:"rotate(180deg)",overflow:"hidden",whiteSpace:"nowrap"}),sandwichMarkerCalees:(0,i.css)({label:"sandwichMarkerCalees",textAlign:"right"}),sandwichMarkerIcon:(0,i.css)({label:"sandwichMarkerIcon",verticalAlign:"baseline"})});var Ke=n(3241);function Je(e,t,n="children"){var r;const i="parents"===n?"children":"parents",a=[],o=[{previous:void 0,items:e,level:0}];for(;o.length;){const e=o.shift(),s=e.items.flatMap((e=>e.itemIndexes)),l={value:e.items.reduce(((e,t)=>e+t.value),0),itemIndexes:s,children:[],parents:[],start:0,level:e.level};if(a[e.level]=a[e.level]||[],a[e.level].push(l),e.previous){l[i]=[e.previous];const t=(null==(r=e.previous[n])?void 0:r.reduce(((e,t)=>e+t.value),0))||0;l.start=e.previous.start+t,e.previous[n].push(l)}const c=e.items.flatMap((e=>e[n]||[])),u=(0,Ke.groupBy)(c,(e=>t.getLabel(e.itemIndexes[0])));for(const t of Object.values(u))o.push({previous:l,items:t,level:e.level+1})}return"parents"===n&&(a.reverse(),a.forEach(((e,t)=>{e.forEach((e=>{e.level=t}))}))),a}class Qe{constructor(e){this.map=new Map,this.map=e||new Map}get(e){return this.map.get(e)}keys(){return this.map.keys()}values(){return this.map.values()}size(){return this.map.size}setCollapsedStatus(e,t){const n=new Map(this.map),r=this.map.get(e),i={...r,collapsed:t};for(const e of r.items)n.set(e,i);return new Qe(n)}setAllCollapsedStatus(e){const t=new Map(this.map);for(const n of this.map.keys()){const r={...this.map.get(n),collapsed:e};t.set(n,r)}return new Qe(t)}}class et{constructor(e){this.map=new Map,this.threshold=.99,void 0!==e&&(this.threshold=e)}addTree(e){var t;const n=[e];for(;n.length;){const e=n.shift();(null==(t=e.parents)?void 0:t.length)&&this.addItem(e,e.parents[0]),e.children.length&&n.unshift(...e.children)}}addItem(e,t){if(t&&e.value>t.value*this.threshold&&1===t.children.length)if(this.map.has(t)){const n=this.map.get(t);this.map.set(e,n),n.items.push(e)}else{const n={items:[t,e],collapsed:!0};this.map.set(t,n),this.map.set(e,n)}}getCollapsedMap(){return new Qe(this.map)}}class tt{constructor(e,t,n=(0,N.createTheme)()){var r,i,a;this.data=e,this.options=t;const o=function(e){const t=[["label",[N.FieldType.string,N.FieldType.enum]],["level",[N.FieldType.number]],["value",[N.FieldType.number]],["self",[N.FieldType.number]]],n=[],r=[];for(const i of t){const[t,a]=i,o=null==e?void 0:e.fields.find((e=>e.name===t));o?a.includes(o.type)||r.push({name:t,expectedTypes:a,type:o.type}):n.push(t)}if(n.length>0||r.length>0)return{wrongTypeFields:r,missingFields:n}}(e);if(o)throw new Error(function(e){return e.missingFields.length?`Data is missing fields: ${e.missingFields.join(", ")}`:e.wrongTypeFields.length?`Data has fields of wrong type: ${e.wrongTypeFields.map((e=>`${e.name} has type ${e.type} but should be ${e.expectedTypes.join(" or ")}`)).join(", ")}`:""}(o));if(this.labelField=e.fields.find((e=>"label"===e.name)),this.levelField=e.fields.find((e=>"level"===e.name)),this.valueField=e.fields.find((e=>"value"===e.name)),this.selfField=e.fields.find((e=>"self"===e.name)),this.valueRightField=e.fields.find((e=>"valueRight"===e.name)),this.selfRightField=e.fields.find((e=>"selfRight"===e.name)),(this.valueField||this.selfField)&&(!this.valueField||!this.selfField))throw new Error("Malformed dataFrame: both valueRight and selfRight has to be present if one of them is present.");const s=null==(a=null==(i=null==(r=this.labelField)?void 0:r.config)?void 0:i.type)?void 0:a.enum;s?(this.labelDisplayProcessor=(0,N.getDisplayProcessor)({field:this.labelField,theme:n}),this.uniqueLabels=s.text||[]):(this.labelDisplayProcessor=e=>({text:e+"",numeric:0}),this.uniqueLabels=[...new Set(this.labelField.values)]),this.valueDisplayProcessor=(0,N.getDisplayProcessor)({field:this.valueField,theme:n})}isDiffFlamegraph(){return Boolean(this.valueRightField&&this.selfRightField)}getLabel(e){return this.labelDisplayProcessor(this.labelField.values[e]).text}getLevel(e){return this.levelField.values[e]}getValue(e){return nt(this.valueField,e)}getValueRight(e){return nt(this.valueRightField,e)}getSelf(e){return nt(this.selfField,e)}getSelfRight(e){return nt(this.selfRightField,e)}getSelfDisplay(e){return this.valueDisplayProcessor(this.getSelf(e))}getUniqueLabels(){return this.uniqueLabels}getUnitTitle(){switch(this.valueField.config.unit){case ke.Bytes:return"RAM";case ke.Nanoseconds:return"Time"}return"Count"}getLevels(){return this.initLevels(),this.levels}getSandwichLevels(e){const t=this.getNodesWithLabel(e);if(!(null==t?void 0:t.length))return[[],[]];var n;return[(n=this,Je(function(e){return e.map((e=>{var t,n;if(!(null==(t=e.parents)?void 0:t.length))return e;const r={...e,children:[]},i=[{child:r,parent:e.parents[0]}];for(;i.length;){const e=i.shift(),t={...e.parent,children:e.child?[e.child]:[],parents:[]};e.child&&(t.value=e.child.value,e.child.parents=[t]),(null==(n=e.parent.parents)?void 0:n.length)&&i.push({child:t,parent:e.parent.parents[0]})}return r}))}(t),n,"parents")),Je(t,this)]}getNodesWithLabel(e){return this.initLevels(),this.uniqueLabelsMap[e]}getCollapsedMap(){return this.initLevels(),this.collapsedMap}initLevels(){if(!this.levels){const[e,t,n]=function(e,t){const n=[];let r,i=0;const a={};for(let t=0;t<e.data.length;t++){const o=e.getLevel(t),s=t>0?e.getLevel(t-1):void 0;if(n[o]=n[o]||[],s&&s>=o){const t=n[o][n[o].length-1];i=t.start+e.getValue(t.itemIndexes[0])+e.getValueRight(t.itemIndexes[0]),r=t.parents[0]}const l={itemIndexes:[t],value:e.getValue(t)+e.getValueRight(t),valueRight:e.isDiffFlamegraph()?e.getValueRight(t):void 0,start:i,parents:r&&[r],children:[],level:o};a[e.getLabel(t)]?a[e.getLabel(t)].push(l):a[e.getLabel(t)]=[l],r&&r.children.push(l),r=l,n[o].push(l)}const o=new et(null==t?void 0:t.collapsingThreshold);return(null==t?void 0:t.collapsing)&&o.addTree(n[0][0]),[n,a,o.getCollapsedMap()]}(this,this.options);this.levels=e,this.uniqueLabelsMap=t,this.collapsedMap=n}}}function nt(e,t){if(!e)return 0;return("number"==typeof t?[t]:t).reduce(((t,n)=>t+e.values[n]),0)}var rt=n(4386),it=n(1336);const at=({search:e,setSearch:t,selectedView:n,setSelectedView:a,containerWidth:o,onReset:s,textAlign:l,onTextAlignChange:c,showResetButton:u,colorScheme:h,onColorSchemeChange:f,stickyHeader:d,extraHeaderElements:p,vertical:g,isDiffMode:m,setCollapsedMap:v,collapsedMap:b})=>{const y=(0,k.useStyles2)(ct),[x,_]=function(e,t){const[n,r]=(0,w.useState)(e),i=(0,it.A)(e);return(0,rt.A)((()=>{t(n)}),250,[n]),(0,w.useEffect)((()=>{i!==e&&e!==n&&r(e)}),[e,i,n]),[n,r]}(e,t),S=""!==x?(0,r.jsx)(k.Button,{icon:"times",fill:"text",size:"sm",onClick:()=>{t(""),_("")},children:"Clear"}):null;return(0,r.jsxs)("div",{className:(0,i.cx)(y.header,{[y.stickyHeader]:d}),children:[(0,r.jsx)("div",{className:y.inputContainer,children:(0,r.jsx)(k.Input,{value:x||"",onChange:e=>{_(e.currentTarget.value)},placeholder:"Search...",suffix:S})}),(0,r.jsxs)("div",{className:y.rightContainer,children:[u&&(0,r.jsx)(k.Button,{variant:"secondary",fill:"outline",size:"sm",icon:"history-alt",tooltip:"Reset focus and sandwich state",onClick:()=>{s()},className:y.buttonSpacing,"aria-label":"Reset focus and sandwich state"}),(0,r.jsx)(ot,{value:h,onChange:f,isDiffMode:m}),(0,r.jsxs)(k.ButtonGroup,{className:y.buttonSpacing,children:[(0,r.jsx)(k.Button,{variant:"secondary",fill:"outline",size:"sm",tooltip:"Expand all groups",onClick:()=>{v(b.setAllCollapsedStatus(!1))},"aria-label":"Expand all groups",icon:"angle-double-down",disabled:n===Re.TopTable}),(0,r.jsx)(k.Button,{variant:"secondary",fill:"outline",size:"sm",tooltip:"Collapse all groups",onClick:()=>{v(b.setAllCollapsedStatus(!0))},"aria-label":"Collapse all groups",icon:"angle-double-up",disabled:n===Re.TopTable})]}),(0,r.jsx)(k.RadioButtonGroup,{size:"sm",disabled:n===Re.TopTable,options:st,value:l,onChange:c,className:y.buttonSpacing}),(0,r.jsx)(k.RadioButtonGroup,{size:"sm",options:lt(o,g),value:n,onChange:a}),p&&(0,r.jsx)("div",{className:y.extraElements,children:p})]})]})};function ot(e){const t=(0,k.useStyles2)(ct);let n=(0,r.jsxs)(k.Menu,{children:[(0,r.jsx)(k.Menu.Item,{label:"By package name",onClick:()=>e.onChange(Te.PackageBased)}),(0,r.jsx)(k.Menu.Item,{label:"By value",onClick:()=>e.onChange(Te.ValueBased)})]});const a={[Te.ValueBased]:t.colorDotByValue,[Te.PackageBased]:t.colorDotByPackage,[je.DiffColorBlind]:t.colorDotDiffColorBlind,[je.Default]:t.colorDotDiffDefault}[e.value]||t.colorDotByValue;let o=(0,r.jsx)("span",{className:(0,i.cx)(t.colorDot,a)});return e.isDiffMode&&(n=(0,r.jsxs)(k.Menu,{children:[(0,r.jsx)(k.Menu.Item,{label:"Default (green to red)",onClick:()=>e.onChange(je.Default)}),(0,r.jsx)(k.Menu.Item,{label:"Color blind (blue to red)",onClick:()=>e.onChange(je.DiffColorBlind)})]}),o=(0,r.jsxs)("div",{className:(0,i.cx)(t.colorDotDiff,a),children:[(0,r.jsx)("div",{children:"-100% (removed)"}),(0,r.jsx)("div",{children:"0%"}),(0,r.jsx)("div",{children:"+100% (added)"})]})),(0,r.jsx)(k.Dropdown,{overlay:n,children:(0,r.jsx)(k.Button,{variant:"secondary",fill:"outline",size:"sm",tooltip:"Change color scheme",onClick:()=>{},className:t.buttonSpacing,"aria-label":"Change color scheme",children:o})})}const st=[{value:"left",description:"Align text left",icon:"align-left"},{value:"right",description:"Align text right",icon:"align-right"}];function lt(e,t){let n=[{value:Re.TopTable,label:"Top Table",description:"Only show top table"},{value:Re.FlameGraph,label:"Flame Graph",description:"Only show flame graph"}];return(e>=800||t)&&n.push({value:Re.Both,label:"Both",description:"Show both the top table and flame graph"}),n}const ct=e=>({header:(0,i.css)({label:"header",display:"flex",flexWrap:"wrap",justifyContent:"space-between",width:"100%",top:0,gap:e.spacing(1),marginTop:e.spacing(1)}),stickyHeader:(0,i.css)({zIndex:e.zIndex.navbarFixed,position:"sticky",background:e.colors.background.primary}),inputContainer:(0,i.css)({label:"inputContainer",flexGrow:1,minWidth:"150px",maxWidth:"350px"}),rightContainer:(0,i.css)({label:"rightContainer",display:"flex",alignItems:"flex-start",flexWrap:"wrap"}),buttonSpacing:(0,i.css)({label:"buttonSpacing",marginRight:e.spacing(1)}),resetButton:(0,i.css)({label:"resetButton",display:"flex",marginRight:e.spacing(2)}),resetButtonIconWrapper:(0,i.css)({label:"resetButtonIcon",padding:"0 5px",color:e.colors.text.disabled}),colorDot:(0,i.css)({label:"colorDot",display:"inline-block",width:"10px",height:"10px",borderRadius:"50%"}),colorDotDiff:(0,i.css)({label:"colorDotDiff",display:"flex",width:"200px",height:"12px",color:"white",fontSize:9,lineHeight:1.3,fontWeight:300,justifyContent:"space-between",padding:"0 2px",borderRadius:"2px"}),colorDotByValue:(0,i.css)({label:"colorDotByValue",background:Ee}),colorDotByPackage:(0,i.css)({label:"colorDotByPackage",background:Fe}),colorDotDiffDefault:(0,i.css)({label:"colorDotDiffDefault",background:Be}),colorDotDiffColorBlind:(0,i.css)({label:"colorDotDiffColorBlind",background:Ne}),extraElements:(0,i.css)({label:"extraElements",marginLeft:e.spacing(1)})});let ut;ut="undefined"!=typeof window?window:"undefined"!=typeof self?self:n.g;let ht=null,ft=null;const dt=ut.clearTimeout,pt=ut.setTimeout,gt=ut.cancelAnimationFrame||ut.mozCancelAnimationFrame||ut.webkitCancelAnimationFrame,mt=ut.requestAnimationFrame||ut.mozRequestAnimationFrame||ut.webkitRequestAnimationFrame;function vt(e){let t,n,r,i,a,o,s;const l="undefined"!=typeof document&&document.attachEvent;if(!l){o=function(e){const t=e.__resizeTriggers__,n=t.firstElementChild,r=t.lastElementChild,i=n.firstElementChild;r.scrollLeft=r.scrollWidth,r.scrollTop=r.scrollHeight,i.style.width=n.offsetWidth+1+"px",i.style.height=n.offsetHeight+1+"px",n.scrollLeft=n.scrollWidth,n.scrollTop=n.scrollHeight},a=function(e){return e.offsetWidth!==e.__resizeLast__.width||e.offsetHeight!==e.__resizeLast__.height},s=function(e){if(e.target.className&&"function"==typeof e.target.className.indexOf&&e.target.className.indexOf("contract-trigger")<0&&e.target.className.indexOf("expand-trigger")<0)return;const t=this;o(this),this.__resizeRAF__&&ht(this.__resizeRAF__),this.__resizeRAF__=ft((function(){a(t)&&(t.__resizeLast__.width=t.offsetWidth,t.__resizeLast__.height=t.offsetHeight,t.__resizeListeners__.forEach((function(n){n.call(t,e)})))}))};let e=!1,l="";r="animationstart";const c="Webkit Moz O ms".split(" ");let u="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),h="";{const t=document.createElement("fakeelement");if(void 0!==t.style.animationName&&(e=!0),!1===e)for(let n=0;n<c.length;n++)if(void 0!==t.style[c[n]+"AnimationName"]){h=c[n],l="-"+h.toLowerCase()+"-",r=u[n],e=!0;break}}n="resizeanim",t="@"+l+"keyframes "+n+" { from { opacity: 0; } to { opacity: 0; } } ",i=l+"animation: 1ms "+n+"; "}return{addResizeListener:function(a,c){if(l)a.attachEvent("onresize",c);else{if(!a.__resizeTriggers__){const l=a.ownerDocument,c=ut.getComputedStyle(a);c&&"static"===c.position&&(a.style.position="relative"),function(n){if(!n.getElementById("detectElementResize")){const r=(t||"")+".resize-triggers { "+(i||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',a=n.head||n.getElementsByTagName("head")[0],o=n.createElement("style");o.id="detectElementResize",o.type="text/css",null!=e&&o.setAttribute("nonce",e),o.styleSheet?o.styleSheet.cssText=r:o.appendChild(n.createTextNode(r)),a.appendChild(o)}}(l),a.__resizeLast__={},a.__resizeListeners__=[],(a.__resizeTriggers__=l.createElement("div")).className="resize-triggers";const u=l.createElement("div");u.className="expand-trigger",u.appendChild(l.createElement("div"));const h=l.createElement("div");h.className="contract-trigger",a.__resizeTriggers__.appendChild(u),a.__resizeTriggers__.appendChild(h),a.appendChild(a.__resizeTriggers__),o(a),a.addEventListener("scroll",s,!0),r&&(a.__resizeTriggers__.__animationListener__=function(e){e.animationName===n&&o(a)},a.__resizeTriggers__.addEventListener(r,a.__resizeTriggers__.__animationListener__))}a.__resizeListeners__.push(c)}},removeResizeListener:function(e,t){if(l)e.detachEvent("onresize",t);else if(e.__resizeListeners__.splice(e.__resizeListeners__.indexOf(t),1),!e.__resizeListeners__.length){e.removeEventListener("scroll",s,!0),e.__resizeTriggers__.__animationListener__&&(e.__resizeTriggers__.removeEventListener(r,e.__resizeTriggers__.__animationListener__),e.__resizeTriggers__.__animationListener__=null);try{e.__resizeTriggers__=!e.removeChild(e.__resizeTriggers__)}catch(e){}}}}}null==gt||null==mt?(ht=dt,ft=function(e){return pt(e,20)}):(ht=function([e,t]){gt(e),dt(t)},ft=function(e){const t=mt((function(){dt(n),e()})),n=pt((function(){gt(t),e()}),20);return[t,n]});class bt extends w.Component{constructor(...e){super(...e),this.state={height:this.props.defaultHeight||0,scaledHeight:this.props.defaultHeight||0,scaledWidth:this.props.defaultWidth||0,width:this.props.defaultWidth||0},this._autoSizer=null,this._detectElementResize=null,this._parentNode=null,this._resizeObserver=null,this._timeoutId=null,this._onResize=()=>{this._timeoutId=null;const{disableHeight:e,disableWidth:t,onResize:n}=this.props;if(this._parentNode){const r=window.getComputedStyle(this._parentNode)||{},i=parseFloat(r.paddingLeft||"0"),a=parseFloat(r.paddingRight||"0"),o=parseFloat(r.paddingTop||"0"),s=parseFloat(r.paddingBottom||"0"),l=this._parentNode.getBoundingClientRect(),c=l.height-o-s,u=l.width-i-a,h=this._parentNode.offsetHeight-o-s,f=this._parentNode.offsetWidth-i-a;(e||this.state.height===h&&this.state.scaledHeight===c)&&(t||this.state.width===f&&this.state.scaledWidth===u)||(this.setState({height:h,width:f,scaledHeight:c,scaledWidth:u}),"function"==typeof n&&n({height:h,scaledHeight:c,scaledWidth:u,width:f}))}},this._setRef=e=>{this._autoSizer=e}}componentDidMount(){const{nonce:e}=this.props,t=this._autoSizer?this._autoSizer.parentNode:null;if(null!=t&&t.ownerDocument&&t.ownerDocument.defaultView&&t instanceof t.ownerDocument.defaultView.HTMLElement){this._parentNode=t;const n=t.ownerDocument.defaultView.ResizeObserver;null!=n?(this._resizeObserver=new n((()=>{this._timeoutId=setTimeout(this._onResize,0)})),this._resizeObserver.observe(t)):(this._detectElementResize=vt(e),this._detectElementResize.addResizeListener(t,this._onResize)),this._onResize()}}componentWillUnmount(){this._parentNode&&(this._detectElementResize&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize),null!==this._timeoutId&&clearTimeout(this._timeoutId),this._resizeObserver&&this._resizeObserver.disconnect())}render(){const{children:e,defaultHeight:t,defaultWidth:n,disableHeight:r=!1,disableWidth:i=!1,doNotBailOutOnEmptyChildren:a=!1,nonce:o,onResize:s,style:l={},tagName:c="div",...u}=this.props,{height:h,scaledHeight:f,scaledWidth:d,width:p}=this.state,g={overflow:"visible"},m={};let v=!1;return r||(0===h&&(v=!0),g.height=0,m.height=h,m.scaledHeight=f),i||(0===p&&(v=!0),g.width=0,m.width=p,m.scaledWidth=d),a&&(v=!1),(0,w.createElement)(c,{ref:this._setRef,style:{...g,...l},...u},!v&&e(m))}}const yt=(0,w.memo)((({data:e,onSymbolClick:t,search:n,matchedLabels:i,onSearch:a,sandwichItem:o,onSandwich:s,onTableSort:l,colorScheme:c})=>{const u=(0,w.useMemo)((()=>{let t={};for(let n=0;n<e.data.length;n++){const r=e.getValue(n),a=e.getValueRight(n),o=e.getSelf(n),s=e.getLabel(n);i&&!i.has(s)||(t[s]=t[s]||{},t[s].self=t[s].self?t[s].self+o:o,t[s].total=t[s].total?t[s].total+r:r,t[s].totalRight=t[s].totalRight?t[s].totalRight+a:a)}return t}),[e,i]),h=(0,k.useStyles2)(St),f=(0,k.useTheme2)(),[d,p]=(0,w.useState)([{displayName:"Self",desc:!0}]);return(0,r.jsx)("div",{className:h.topTableContainer,"data-testid":"topTable",children:(0,r.jsx)(bt,{style:{width:"100%"},children:({width:i,height:h})=>{if(i<3||h<3)return null;const g=function(e,t,n,i,a,o,s,l,c,u){const h=function(e,t,n,i){const a={type:k.TableCellDisplayMode.Custom,cellComponent:a=>(0,r.jsx)(_t,{frame:a.frame,onSandwich:e,onSearch:t,search:n,sandwichItem:i,rowIndex:a.rowIndex})},o={filterable:!1,width:xt,hideHeader:!0,inspect:!1,align:"auto",cellOptions:a};return{type:N.FieldType.number,name:"actions",values:[],config:{custom:o}}}(o,a,c,u),f={type:N.FieldType.string,name:"Symbol",values:[],config:{custom:{width:n-xt-2*B},links:[{title:"Highlight symbol",url:"",onClick:e=>{const t=e.origin.field.values[e.origin.rowIndex];i(t)}}]}};let d;if(e.isDiffFlamegraph()){f.config.custom.width=n-xt-3*B;const r=wt("Baseline","percent"),i=wt("Comparison","percent"),a=wt("Diff","percent");a.config.custom.cellOptions.type=k.TableCellDisplayMode.ColorText;const[o,s]=l===je.DiffColorBlind?[Pe[0],Pe[2]]:[Le[0],Le[2]];a.config.mappings=[{type:N.MappingType.ValueToText,options:{[1/0]:{text:"new",color:s}}},{type:N.MappingType.ValueToText,options:{[-100]:{text:"removed",color:o}}},{type:N.MappingType.RangeToText,options:{from:0,to:1/0,result:{color:s}}},{type:N.MappingType.RangeToText,options:{from:-1/0,to:0,result:{color:o}}}];const c=e.getLevels(),u=c.length?c[0][0].value:0,p=c.length?c[0][0].valueRight:void 0;for(let e in t){h.values.push(null),f.values.push(e);const n=t[e].total,o=t[e].totalRight,s=u-p,l=Math.round(1e4*n/s)/100,c=Math.round(1e4*o/p)/100,d=(c-l)/l*100;a.values.push(d),r.values.push(l),i.values.push(c)}d={fields:[h,f,r,i,a],length:f.values.length}}else{const n=wt("Self",e.selfField.config.unit),r=wt("Total",e.valueField.config.unit);for(let e in t)h.values.push(null),f.values.push(e),n.values.push(t[e].self),r.values.push(t[e].total);d={fields:[h,f,n,r],length:f.values.length}}const p=(0,N.applyFieldOverrides)({data:[d],fieldConfig:{defaults:{},overrides:[]},replaceVariables:e=>e,theme:s});return p[0]}(e,u,i,t,a,s,f,c,n,o);return(0,r.jsx)(k.Table,{initialSortBy:d,onSortByChange:e=>{e&&e.length&&(null==l||l(e[0].displayName+"_"+(e[0].desc?"desc":"asc"))),p(e)},data:g,width:i,height:h})}})})}));function wt(e,t){const n={width:B,align:"auto",inspect:!1,cellOptions:{type:k.TableCellDisplayMode.Auto}};return{type:N.FieldType.number,name:e,values:[],config:{unit:t,custom:n}}}yt.displayName="FlameGraphTopTableContainer";const xt=61;function _t(e){var t;const n=Mt(),i=null==(t=e.frame.fields.find((e=>"Symbol"===e.name)))?void 0:t.values[e.rowIndex],a=e.search===i,o=e.sandwichItem===i;return(0,r.jsxs)("div",{className:n.actionCellWrapper,children:[(0,r.jsx)(k.IconButton,{className:n.actionCellButton,name:"search",variant:a?"primary":"secondary",tooltip:a?"Clear from search":"Search for symbol","aria-label":a?"Clear from search":"Search for symbol",onClick:()=>{e.onSearch(a?"":i)}}),(0,r.jsx)(k.IconButton,{className:n.actionCellButton,name:"gf-show-context",tooltip:o?"Remove from sandwich view":"Show in sandwich view",variant:o?"primary":"secondary","aria-label":o?"Remove from sandwich view":"Show in sandwich view",onClick:()=>{e.onSandwich(o?void 0:i)}})]})}const St=e=>({topTableContainer:(0,i.css)({label:"topTableContainer",padding:e.spacing(1),backgroundColor:e.colors.background.secondary,height:"100%"})}),Mt=()=>({actionCellWrapper:(0,i.css)({label:"actionCellWrapper",display:"flex",height:"24px"}),actionCellButton:(0,i.css)({label:"actionCellButton",marginRight:0,width:"24px"})}),Ct=new g,kt=({data:e,onTableSymbolClick:t,onViewSelected:n,onTextAlignSelected:a,onTableSort:o,getTheme:s,stickyHeader:l,extraHeaderElements:c,vertical:u,showFlameGraphOnly:h,disableCollapsing:f,keepFocusOnDataChange:d,getExtraContextMenuButtons:p})=>{const[g,m]=(0,w.useState)(),[v,b]=(0,w.useState)(0),[y,x]=(0,w.useState)(1),[_,S]=(0,w.useState)(""),[M,R]=(0,w.useState)(Re.Both),[T,{width:j}]=C(),[A,z]=(0,w.useState)("left"),[E,F]=(0,w.useState)(),[D,I]=(0,w.useState)(new Qe),L=(0,w.useMemo)((()=>s()),[s]),B=(0,w.useMemo)((()=>{if(!e)return;const t=new tt(e,{collapsing:!f},L);return I(t.getCollapsedMap()),t}),[e,L,f]),[P,N]=function(e){const t=(null==e?void 0:e.isDiffFlamegraph())?je.Default:Te.PackageBased,[n,r]=(0,w.useState)(t);return(0,w.useEffect)((()=>{r(t)}),[t]),[n,r]}(B),O=function(e){return{container:(0,i.css)({label:"container",overflow:"auto",height:"100%",display:"flex",flex:"1 1 0",flexDirection:"column",minHeight:0,gap:e.spacing(1)}),body:(0,i.css)({label:"body",flexGrow:1}),tableContainer:(0,i.css)({height:800}),horizontalContainer:(0,i.css)({label:"horizontalContainer",display:"flex",minHeight:0,flexDirection:"row",columnGap:e.spacing(1),width:"100%"}),horizontalGraphContainer:(0,i.css)({flexBasis:"50%"}),horizontalTableContainer:(0,i.css)({flexBasis:"50%",maxHeight:800}),verticalGraphContainer:(0,i.css)({marginBottom:e.spacing(1)}),verticalTableContainer:(0,i.css)({height:800})}}(L),H=function(e,t){return(0,w.useMemo)((()=>{if(e&&t){const n=new Set;let r=Ct.filter(t.getUniqueLabels(),e);if(r)for(let e of r)n.add(t.getUniqueLabels()[e]);return n}}),[e,t])}(_,B);(0,w.useEffect)((()=>{j>0&&j<800&&M===Re.Both&&!u&&R(Re.FlameGraph)}),[M,R,j,u]);const V=(0,w.useCallback)((()=>{m(void 0),b(0),x(1)}),[m,x,b]),W=(0,w.useCallback)((()=>{F(void 0)}),[F]);(0,w.useEffect)((()=>{var e;if(!d)return V(),void W();if(B&&g){const t=null==(e=B.getNodesWithLabel(g.label))?void 0:e[0];if(t){m({...g,item:t});const e=B.getLevels(),n=e.length?e[0][0].value:0;b(t.start/n),x((t.start+t.value)/n)}else m({...g,item:{start:0,value:0,itemIndexes:[],children:[],level:0}}),b(0),x(1)}}),[B,d]);const $=(0,w.useCallback)((e=>{_===e?S(""):(null==t||t(e),S(e),V())}),[S,V,t,_]);if(!B)return null;const G=(0,r.jsx)(Ze,{data:B,rangeMin:v,rangeMax:y,matchedLabels:H,setRangeMin:b,setRangeMax:x,onItemFocused:e=>m(e),focusedItemData:g,textAlign:A,sandwichItem:E,onSandwich:e=>{V(),F(e)},onFocusPillClick:V,onSandwichPillClick:W,colorScheme:P,showFlameGraphOnly:h,collapsing:!f,getExtraContextMenuButtons:p,selectedView:M,search:_,collapsedMap:D,setCollapsedMap:I}),q=(0,r.jsx)(yt,{data:B,onSymbolClick:$,search:_,matchedLabels:H,sandwichItem:E,onSandwich:F,onSearch:S,onTableSort:o,colorScheme:P});let U;return h||M===Re.FlameGraph?U=G:M===Re.TopTable?U=(0,r.jsx)("div",{className:O.tableContainer,children:q}):M===Re.Both&&(U=u?(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:O.verticalGraphContainer,children:G}),(0,r.jsx)("div",{className:O.verticalTableContainer,children:q})]}):(0,r.jsxs)("div",{className:O.horizontalContainer,children:[(0,r.jsx)("div",{className:O.horizontalTableContainer,children:q}),(0,r.jsx)("div",{className:O.horizontalGraphContainer,children:G})]})),(0,r.jsx)(k.ThemeContext.Provider,{value:L,children:(0,r.jsxs)("div",{ref:T,className:O.container,children:[!h&&(0,r.jsx)(at,{search:_,setSearch:S,selectedView:M,setSelectedView:e=>{R(e),null==n||n(e)},containerWidth:j,onReset:()=>{V(),W()},textAlign:A,onTextAlignChange:e=>{z(e),null==a||a(e)},showResetButton:Boolean(g||E),colorScheme:P,onColorSchemeChange:N,stickyHeader:Boolean(l),extraHeaderElements:c,vertical:u,isDiffMode:B.isDiffFlamegraph(),setCollapsedMap:I,collapsedMap:D}),(0,r.jsx)("div",{className:O.body,children:U})]})})}},2249:function(e,t,n){var r,i,a;i=[],void 0===(a="function"==typeof(r=function(){"use strict";function t(e,t){return void 0===t?t={autoBom:!1}:"object"!=typeof t&&(console.warn("Deprecated: Expected third argument to be a object"),t={autoBom:!t}),t.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\ufeff",e],{type:e.type}):e}function r(e,t,n){var r=new XMLHttpRequest;r.open("GET",e),r.responseType="blob",r.onload=function(){l(r.response,t,n)},r.onerror=function(){console.error("could not download file")},r.send()}function i(e){var t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(e){}return 200<=t.status&&299>=t.status}function a(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(n){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}var o="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof n.g&&n.g.global===n.g?n.g:void 0,s=o.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),l=o.saveAs||("object"!=typeof window||window!==o?function(){}:"download"in HTMLAnchorElement.prototype&&!s?function(e,t,n){var s=o.URL||o.webkitURL,l=document.createElement("a");t=t||e.name||"download",l.download=t,l.rel="noopener","string"==typeof e?(l.href=e,l.origin===location.origin?a(l):i(l.href)?r(e,t,n):a(l,l.target="_blank")):(l.href=s.createObjectURL(e),setTimeout((function(){s.revokeObjectURL(l.href)}),4e4),setTimeout((function(){a(l)}),0))}:"msSaveOrOpenBlob"in navigator?function(e,n,o){if(n=n||e.name||"download","string"!=typeof e)navigator.msSaveOrOpenBlob(t(e,o),n);else if(i(e))r(e,n,o);else{var s=document.createElement("a");s.href=e,s.target="_blank",setTimeout((function(){a(s)}))}}:function(e,t,n,i){if((i=i||open("","_blank"))&&(i.document.title=i.document.body.innerText="downloading..."),"string"==typeof e)return r(e,t,n);var a="application/octet-stream"===e.type,l=/constructor/i.test(o.HTMLElement)||o.safari,c=/CriOS\/[\d]+/.test(navigator.userAgent);if((c||a&&l||s)&&"undefined"!=typeof FileReader){var u=new FileReader;u.onloadend=function(){var e=u.result;e=c?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=e:location=e,i=null},u.readAsDataURL(e)}else{var h=o.URL||o.webkitURL,f=h.createObjectURL(e);i?i.location=f:location.href=f,i=null,setTimeout((function(){h.revokeObjectURL(f)}),4e4)}});o.saveAs=l.saveAs=l,e.exports=l})?r.apply(t,i):r)||(e.exports=a)},4386:(e,t,n)=>{"use strict";var r=n(6212),i=n(5959),a=r.__importDefault(n(5690));t.A=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=[]);var r=a.default(e,t),o=r[0],s=r[1],l=r[2];return i.useEffect(l,n),[o,s]}},1336:(e,t,n)=>{"use strict";var r=n(5959);t.A=function(e){var t=r.useRef();return r.useEffect((function(){t.current=e})),t.current}},5690:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(5959);t.default=function(e,t){void 0===t&&(t=0);var n=r.useRef(!1),i=r.useRef(),a=r.useRef(e),o=r.useCallback((function(){return n.current}),[]),s=r.useCallback((function(){n.current=!1,i.current&&clearTimeout(i.current),i.current=setTimeout((function(){n.current=!0,a.current()}),t)}),[t]),l=r.useCallback((function(){n.current=null,i.current&&clearTimeout(i.current)}),[]);return r.useEffect((function(){a.current=e}),[e]),r.useEffect((function(){return s(),l}),[t]),[o,l,s]}},8727:()=>{"use strict";var e={},t=Uint8Array,n=Uint16Array,r=Int32Array,i=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),a=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),o=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(e,t){for(var i=new n(31),a=0;a<31;++a)i[a]=t+=1<<e[a-1];var o=new r(i[30]);for(a=1;a<30;++a)for(var s=i[a];s<i[a+1];++s)o[s]=s-i[a]<<5|a;return{b:i,r:o}},l=s(i,2),c=l.b,u=l.r;c[28]=258,u[258]=28;for(var h=s(a,0),f=h.b,d=h.r,p=new n(32768),g=0;g<32768;++g){var m=(43690&g)>>1|(21845&g)<<1;m=(61680&(m=(52428&m)>>2|(13107&m)<<2))>>4|(3855&m)<<4,p[g]=((65280&m)>>8|(255&m)<<8)>>1}var v=function(e,t,r){for(var i=e.length,a=0,o=new n(t);a<i;++a)e[a]&&++o[e[a]-1];var s,l=new n(t);for(a=1;a<t;++a)l[a]=l[a-1]+o[a-1]<<1;if(r){s=new n(1<<t);var c=15-t;for(a=0;a<i;++a)if(e[a])for(var u=a<<4|e[a],h=t-e[a],f=l[e[a]-1]++<<h,d=f|(1<<h)-1;f<=d;++f)s[p[f]>>c]=u}else for(s=new n(i),a=0;a<i;++a)e[a]&&(s[a]=p[l[e[a]-1]++]>>15-e[a]);return s},b=new t(288);for(g=0;g<144;++g)b[g]=8;for(g=144;g<256;++g)b[g]=9;for(g=256;g<280;++g)b[g]=7;for(g=280;g<288;++g)b[g]=8;var y=new t(32);for(g=0;g<32;++g)y[g]=5;var w=v(b,9,0),x=v(b,9,1),_=v(y,5,0),S=v(y,5,1),M=function(e){for(var t=e[0],n=1;n<e.length;++n)e[n]>t&&(t=e[n]);return t},C=function(e,t,n){var r=t/8|0;return(e[r]|e[r+1]<<8)>>(7&t)&n},k=function(e,t){var n=t/8|0;return(e[n]|e[n+1]<<8|e[n+2]<<16)>>(7&t)},R=function(e){return(e+7)/8|0},T=function(e,n,r){return(null==n||n<0)&&(n=0),(null==r||r>e.length)&&(r=e.length),new t(e.subarray(n,r))},j=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],A=function(e,t,n){var r=new Error(t||j[e]);if(r.code=e,Error.captureStackTrace&&Error.captureStackTrace(r,A),!n)throw r;return r},z=function(e,n,r,s){var l=e.length,u=s?s.length:0;if(!l||n.f&&!n.l)return r||new t(0);var h=!r,d=h||2!=n.i,p=n.i;h&&(r=new t(3*l));var g=function(e){var n=r.length;if(e>n){var i=new t(Math.max(2*n,e));i.set(r),r=i}},m=n.f||0,b=n.p||0,y=n.b||0,w=n.l,_=n.d,j=n.m,z=n.n,E=8*l;do{if(!w){m=C(e,b,1);var F=C(e,b+1,3);if(b+=3,!F){var D=e[(G=R(b)+4)-4]|e[G-3]<<8,I=G+D;if(I>l){p&&A(0);break}d&&g(y+D),r.set(e.subarray(G,I),y),n.b=y+=D,n.p=b=8*I,n.f=m;continue}if(1==F)w=x,_=S,j=9,z=5;else if(2==F){var L=C(e,b,31)+257,B=C(e,b+10,15)+4,P=L+C(e,b+5,31)+1;b+=14;for(var N=new t(P),O=new t(19),H=0;H<B;++H)O[o[H]]=C(e,b+3*H,7);b+=3*B;var V=M(O),W=(1<<V)-1,$=v(O,V,1);for(H=0;H<P;){var G,q=$[C(e,b,W)];if(b+=15&q,(G=q>>4)<16)N[H++]=G;else{var U=0,X=0;for(16==G?(X=3+C(e,b,3),b+=2,U=N[H-1]):17==G?(X=3+C(e,b,7),b+=3):18==G&&(X=11+C(e,b,127),b+=7);X--;)N[H++]=U}}var Z=N.subarray(0,L),Y=N.subarray(L);j=M(Z),z=M(Y),w=v(Z,j,1),_=v(Y,z,1)}else A(1);if(b>E){p&&A(0);break}}d&&g(y+131072);for(var K=(1<<j)-1,J=(1<<z)-1,Q=b;;Q=b){var ee=(U=w[k(e,b)&K])>>4;if((b+=15&U)>E){p&&A(0);break}if(U||A(2),ee<256)r[y++]=ee;else{if(256==ee){Q=b,w=null;break}var te=ee-254;if(ee>264){var ne=i[H=ee-257];te=C(e,b,(1<<ne)-1)+c[H],b+=ne}var re=_[k(e,b)&J],ie=re>>4;re||A(3),b+=15&re;Y=f[ie];if(ie>3){ne=a[ie];Y+=k(e,b)&(1<<ne)-1,b+=ne}if(b>E){p&&A(0);break}d&&g(y+131072);var ae=y+te;if(y<Y){var oe=u-Y,se=Math.min(Y,ae);for(oe+y<0&&A(3);y<se;++y)r[y]=s[oe+y]}for(;y<ae;++y)r[y]=r[y-Y]}}n.l=w,n.p=Q,n.b=y,n.f=m,w&&(m=1,n.m=j,n.d=_,n.n=z)}while(!m);return y!=r.length&&h?T(r,0,y):r.subarray(0,y)},E=function(e,t,n){n<<=7&t;var r=t/8|0;e[r]|=n,e[r+1]|=n>>8},F=function(e,t,n){n<<=7&t;var r=t/8|0;e[r]|=n,e[r+1]|=n>>8,e[r+2]|=n>>16},D=function(e,r){for(var i=[],a=0;a<e.length;++a)e[a]&&i.push({s:a,f:e[a]});var o=i.length,s=i.slice();if(!o)return{t:H,l:0};if(1==o){var l=new t(i[0].s+1);return l[i[0].s]=1,{t:l,l:1}}i.sort((function(e,t){return e.f-t.f})),i.push({s:-1,f:25001});var c=i[0],u=i[1],h=0,f=1,d=2;for(i[0]={s:-1,f:c.f+u.f,l:c,r:u};f!=o-1;)c=i[i[h].f<i[d].f?h++:d++],u=i[h!=f&&i[h].f<i[d].f?h++:d++],i[f++]={s:-1,f:c.f+u.f,l:c,r:u};var p=s[0].s;for(a=1;a<o;++a)s[a].s>p&&(p=s[a].s);var g=new n(p+1),m=I(i[f-1],g,0);if(m>r){a=0;var v=0,b=m-r,y=1<<b;for(s.sort((function(e,t){return g[t.s]-g[e.s]||e.f-t.f}));a<o;++a){var w=s[a].s;if(!(g[w]>r))break;v+=y-(1<<m-g[w]),g[w]=r}for(v>>=b;v>0;){var x=s[a].s;g[x]<r?v-=1<<r-g[x]++-1:++a}for(;a>=0&&v;--a){var _=s[a].s;g[_]==r&&(--g[_],++v)}m=r}return{t:new t(g),l:m}},I=function(e,t,n){return-1==e.s?Math.max(I(e.l,t,n+1),I(e.r,t,n+1)):t[e.s]=n},L=function(e){for(var t=e.length;t&&!e[--t];);for(var r=new n(++t),i=0,a=e[0],o=1,s=function(e){r[i++]=e},l=1;l<=t;++l)if(e[l]==a&&l!=t)++o;else{if(!a&&o>2){for(;o>138;o-=138)s(32754);o>2&&(s(o>10?o-11<<5|28690:o-3<<5|12305),o=0)}else if(o>3){for(s(a),--o;o>6;o-=6)s(8304);o>2&&(s(o-3<<5|8208),o=0)}for(;o--;)s(a);o=1,a=e[l]}return{c:r.subarray(0,i),n:t}},B=function(e,t){for(var n=0,r=0;r<t.length;++r)n+=e[r]*t[r];return n},P=function(e,t,n){var r=n.length,i=R(t+2);e[i]=255&r,e[i+1]=r>>8,e[i+2]=255^e[i],e[i+3]=255^e[i+1];for(var a=0;a<r;++a)e[i+a+4]=n[a];return 8*(i+4+r)},N=function(e,t,r,s,l,c,u,h,f,d,p){E(t,p++,r),++l[256];for(var g=D(l,15),m=g.t,x=g.l,S=D(c,15),M=S.t,C=S.l,k=L(m),R=k.c,T=k.n,j=L(M),A=j.c,z=j.n,I=new n(19),N=0;N<R.length;++N)++I[31&R[N]];for(N=0;N<A.length;++N)++I[31&A[N]];for(var O=D(I,7),H=O.t,V=O.l,W=19;W>4&&!H[o[W-1]];--W);var $,G,q,U,X=d+5<<3,Z=B(l,b)+B(c,y)+u,Y=B(l,m)+B(c,M)+u+14+3*W+B(I,H)+2*I[16]+3*I[17]+7*I[18];if(f>=0&&X<=Z&&X<=Y)return P(t,p,e.subarray(f,f+d));if(E(t,p,1+(Y<Z)),p+=2,Y<Z){$=v(m,x,0),G=m,q=v(M,C,0),U=M;var K=v(H,V,0);E(t,p,T-257),E(t,p+5,z-1),E(t,p+10,W-4),p+=14;for(N=0;N<W;++N)E(t,p+3*N,H[o[N]]);p+=3*W;for(var J=[R,A],Q=0;Q<2;++Q){var ee=J[Q];for(N=0;N<ee.length;++N){var te=31&ee[N];E(t,p,K[te]),p+=H[te],te>15&&(E(t,p,ee[N]>>5&127),p+=ee[N]>>12)}}}else $=w,G=b,q=_,U=y;for(N=0;N<h;++N){var ne=s[N];if(ne>255){F(t,p,$[(te=ne>>18&31)+257]),p+=G[te+257],te>7&&(E(t,p,ne>>23&31),p+=i[te]);var re=31&ne;F(t,p,q[re]),p+=U[re],re>3&&(F(t,p,ne>>5&8191),p+=a[re])}else F(t,p,$[ne]),p+=G[ne]}return F(t,p,$[256]),p+G[256]},O=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),H=new t(0),V=function(e,o,s,l,c,h){var f=h.z||e.length,p=new t(l+f+5*(1+Math.ceil(f/7e3))+c),g=p.subarray(l,p.length-c),m=h.l,v=7&(h.r||0);if(o){v&&(g[0]=h.r>>3);for(var b=O[o-1],y=b>>13,w=8191&b,x=(1<<s)-1,_=h.p||new n(32768),S=h.h||new n(x+1),M=Math.ceil(s/3),C=2*M,k=function(t){return(e[t]^e[t+1]<<M^e[t+2]<<C)&x},j=new r(25e3),A=new n(288),z=new n(32),E=0,F=0,D=h.i||0,I=0,L=h.w||0,B=0;D+2<f;++D){var H=k(D),V=32767&D,W=S[H];if(_[V]=W,S[H]=V,L<=D){var $=f-D;if((E>7e3||I>24576)&&($>423||!m)){v=N(e,g,0,j,A,z,F,I,B,D-B,v),I=E=F=0,B=D;for(var G=0;G<286;++G)A[G]=0;for(G=0;G<30;++G)z[G]=0}var q=2,U=0,X=w,Z=V-W&32767;if($>2&&H==k(D-Z))for(var Y=Math.min(y,$)-1,K=Math.min(32767,D),J=Math.min(258,$);Z<=K&&--X&&V!=W;){if(e[D+q]==e[D+q-Z]){for(var Q=0;Q<J&&e[D+Q]==e[D+Q-Z];++Q);if(Q>q){if(q=Q,U=Z,Q>Y)break;var ee=Math.min(Z,Q-2),te=0;for(G=0;G<ee;++G){var ne=D-Z+G&32767,re=ne-_[ne]&32767;re>te&&(te=re,W=ne)}}}Z+=(V=W)-(W=_[V])&32767}if(U){j[I++]=268435456|u[q]<<18|d[U];var ie=31&u[q],ae=31&d[U];F+=i[ie]+a[ae],++A[257+ie],++z[ae],L=D+q,++E}else j[I++]=e[D],++A[e[D]]}}for(D=Math.max(D,L);D<f;++D)j[I++]=e[D],++A[e[D]];v=N(e,g,m,j,A,z,F,I,B,D-B,v),m||(h.r=7&v|g[v/8|0]<<3,v-=7,h.h=S,h.p=_,h.i=D,h.w=L)}else{for(D=h.w||0;D<f+m;D+=65535){var oe=D+65535;oe>=f&&(g[v/8|0]=m,oe=f),v=P(g,v+1,e.subarray(D,oe))}h.i=f}return T(p,0,l+R(v)+c)},W=function(){for(var e=new Int32Array(256),t=0;t<256;++t){for(var n=t,r=9;--r;)n=(1&n&&-306674912)^n>>>1;e[t]=n}return e}(),$=function(){var e=-1;return{p:function(t){for(var n=e,r=0;r<t.length;++r)n=W[255&n^t[r]]^n>>>8;e=n},d:function(){return~e}}},G=function(){var e=1,t=0;return{p:function(n){for(var r=e,i=t,a=0|n.length,o=0;o!=a;){for(var s=Math.min(o+2655,a);o<s;++o)i+=r+=n[o];r=(65535&r)+15*(r>>16),i=(65535&i)+15*(i>>16)}e=r,t=i},d:function(){return(255&(e%=65521))<<24|(65280&e)<<8|(255&(t%=65521))<<8|t>>8}}},q=function(e,n,r,i,a){if(!a&&(a={l:1},n.dictionary)){var o=n.dictionary.subarray(-32768),s=new t(o.length+e.length);s.set(o),s.set(e,o.length),e=s,a.w=o.length}return V(e,null==n.level?6:n.level,null==n.mem?a.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):20:12+n.mem,r,i,a)},U=function(e,t){var n={};for(var r in e)n[r]=e[r];for(var r in t)n[r]=t[r];return n},X=function(e,t,n){for(var r=e(),i=e.toString(),a=i.slice(i.indexOf("[")+1,i.lastIndexOf("]")).replace(/\s+/g,"").split(","),o=0;o<r.length;++o){var s=r[o],l=a[o];if("function"==typeof s){t+=";"+l+"=";var c=s.toString();if(s.prototype)if(-1!=c.indexOf("[native code]")){var u=c.indexOf(" ",8)+1;t+=c.slice(u,c.indexOf("(",u))}else for(var h in t+=c,s.prototype)t+=";"+l+".prototype."+h+"="+s.prototype[h].toString();else t+=c}else n[l]=s}return t},Z=[],Y=function(t,n,r,i){if(!Z[r]){for(var a="",o={},s=t.length-1,l=0;l<s;++l)a=X(t[l],a,o);Z[r]={c:X(t[s],a,o),e:o}}var c=U({},Z[r].e);return function(t,n,r,i,a){var o=new Worker(e[n]||(e[n]=URL.createObjectURL(new Blob([t+';addEventListener("error",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'],{type:"text/javascript"}))));return o.onmessage=function(e){var t=e.data,n=t.$e$;if(n){var r=new Error(n[0]);r.code=n[1],r.stack=n[2],a(r,null)}else a(null,t)},o.postMessage(r,i),o}(Z[r].c+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+n.toString()+"}",r,c,function(e){var t=[];for(var n in e)e[n].buffer&&t.push((e[n]=new e[n].constructor(e[n])).buffer);return t}(c),i)},K=function(){return[t,n,r,i,a,o,c,f,x,S,p,j,v,M,C,k,R,T,A,z,we,re,ie]},J=function(){return[t,n,r,i,a,o,u,d,w,b,_,y,p,O,H,v,E,F,D,I,L,B,P,N,R,T,V,q,ve,re]},Q=function(){return[le,he,se,$,W]},ee=function(){return[ce,ue]},te=function(){return[fe,se,G]},ne=function(){return[de]},re=function(e){return postMessage(e,[e.buffer])},ie=function(e){return e&&{out:e.size&&new t(e.size),dictionary:e.dictionary}},ae=function(e){return e.ondata=function(e,t){return postMessage([e,t],[e.buffer])},function(t){t.data.length?(e.push(t.data[0],t.data[1]),postMessage([t.data[0].length])):e.flush()}},oe=function(e,t,n,r,i,a,o){var s,l=Y(e,r,i,(function(e,n){e?(l.terminate(),t.ondata.call(t,e)):Array.isArray(n)?1==n.length?(t.queuedSize-=n[0],t.ondrain&&t.ondrain(n[0])):(n[1]&&l.terminate(),t.ondata.call(t,e,n[0],n[1])):o(n)}));l.postMessage(n),t.queuedSize=0,t.push=function(e,n){t.ondata||A(5),s&&t.ondata(A(4,0,1),null,!!n),t.queuedSize+=e.length,l.postMessage([e,s=n],[e.buffer])},t.terminate=function(){l.terminate()},a&&(t.flush=function(){l.postMessage([])})},se=function(e,t,n){for(;n;++t)e[t]=n,n>>>=8},le=function(e,t){var n=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:9==t.level?2:0,e[9]=3,0!=t.mtime&&se(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),n){e[3]=8;for(var r=0;r<=n.length;++r)e[r+10]=n.charCodeAt(r)}},ce=function(e){31==e[0]&&139==e[1]&&8==e[2]||A(6,"invalid gzip data");var t=e[3],n=10;4&t&&(n+=2+(e[10]|e[11]<<8));for(var r=(t>>3&1)+(t>>4&1);r>0;r-=!e[n++]);return n+(2&t)},ue=function(e){var t=e.length;return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0},he=function(e){return 10+(e.filename?e.filename.length+1:0)},fe=function(e,t){var n=t.level,r=0==n?0:n<6?1:9==n?3:2;if(e[0]=120,e[1]=r<<6|(t.dictionary&&32),e[1]|=31-(e[0]<<8|e[1])%31,t.dictionary){var i=G();i.p(t.dictionary),se(e,2,i.d())}},de=function(e,t){return(8!=(15&e[0])||e[0]>>4>7||(e[0]<<8|e[1])%31)&&A(6,"invalid zlib data"),(e[1]>>5&1)==+!t&&A(6,"invalid zlib data: "+(32&e[1]?"need":"unexpected")+" dictionary"),2+(e[1]>>3&4)};function pe(e,t){return"function"==typeof e&&(t=e,e={}),this.ondata=t,e}var ge=function(){function e(e,n){if("function"==typeof e&&(n=e,e={}),this.ondata=n,this.o=e||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var r=this.o.dictionary.subarray(-32768);this.b.set(r,32768-r.length),this.s.i=32768-r.length}}return e.prototype.p=function(e,t){this.ondata(q(e,this.o,0,0,this.s),t)},e.prototype.push=function(e,n){this.ondata||A(5),this.s.l&&A(4);var r=e.length+this.s.z;if(r>this.b.length){if(r>2*this.b.length-32768){var i=new t(-32768&r);i.set(this.b.subarray(0,this.s.z)),this.b=i}var a=this.b.length-this.s.z;this.b.set(e.subarray(0,a),this.s.z),this.s.z=this.b.length,this.p(this.b,!1),this.b.set(this.b.subarray(-32768)),this.b.set(e.subarray(a),32768),this.s.z=e.length-a+32768,this.s.i=32766,this.s.w=32768}else this.b.set(e,this.s.z),this.s.z+=e.length;this.s.l=1&n,(this.s.z>this.s.w+8191||n)&&(this.p(this.b,n||!1),this.s.w=this.s.i,this.s.i-=2)},e.prototype.flush=function(){this.ondata||A(5),this.s.l&&A(4),this.p(this.b,!1),this.s.w=this.s.i,this.s.i-=2},e}(),me=function(){return function(e,t){oe([J,function(){return[ae,ge]}],this,pe.call(this,e,t),(function(e){var t=new ge(e.data);onmessage=ae(t)}),6,1)}}();function ve(e,t){return q(e,t||{},0,0)}var be=function(){function e(e,n){"function"==typeof e&&(n=e,e={}),this.ondata=n;var r=e&&e.dictionary&&e.dictionary.subarray(-32768);this.s={i:0,b:r?r.length:0},this.o=new t(32768),this.p=new t(0),r&&this.o.set(r)}return e.prototype.e=function(e){if(this.ondata||A(5),this.d&&A(4),this.p.length){if(e.length){var n=new t(this.p.length+e.length);n.set(this.p),n.set(e,this.p.length),this.p=n}}else this.p=e},e.prototype.c=function(e){this.s.i=+(this.d=e||!1);var t=this.s.b,n=z(this.p,this.s,this.o);this.ondata(T(n,t,this.s.b),this.d),this.o=T(n,this.s.b-32768),this.s.b=this.o.length,this.p=T(this.p,this.s.p/8|0),this.s.p&=7},e.prototype.push=function(e,t){this.e(e),this.c(t)},e}(),ye=function(){return function(e,t){oe([K,function(){return[ae,be]}],this,pe.call(this,e,t),(function(e){var t=new be(e.data);onmessage=ae(t)}),7,0)}}();function we(e,t){return z(e,{i:2},t&&t.out,t&&t.dictionary)}var xe=function(){function e(e,t){this.c=$(),this.l=0,this.v=1,ge.call(this,e,t)}return e.prototype.push=function(e,t){this.c.p(e),this.l+=e.length,ge.prototype.push.call(this,e,t)},e.prototype.p=function(e,t){var n=q(e,this.o,this.v&&he(this.o),t&&8,this.s);this.v&&(le(n,this.o),this.v=0),t&&(se(n,n.length-8,this.c.d()),se(n,n.length-4,this.l)),this.ondata(n,t)},e.prototype.flush=function(){ge.prototype.flush.call(this)},e}(),_e=function(){return function(e,t){oe([J,Q,function(){return[ae,ge,xe]}],this,pe.call(this,e,t),(function(e){var t=new xe(e.data);onmessage=ae(t)}),8,1)}}();var Se=function(){function e(e,t){this.v=1,this.r=0,be.call(this,e,t)}return e.prototype.push=function(e,n){if(be.prototype.e.call(this,e),this.r+=e.length,this.v){var r=this.p.subarray(this.v-1),i=r.length>3?ce(r):4;if(i>r.length){if(!n)return}else this.v>1&&this.onmember&&this.onmember(this.r-r.length);this.p=r.subarray(i),this.v=0}be.prototype.c.call(this,n),!this.s.f||this.s.l||n||(this.v=R(this.s.p)+9,this.s={i:0},this.o=new t(0),this.push(new t(0),n))},e}(),Me=function(){return function(e,t){var n=this;oe([K,ee,function(){return[ae,be,Se]}],this,pe.call(this,e,t),(function(e){var t=new Se(e.data);t.onmember=function(e){return postMessage(e)},onmessage=ae(t)}),9,0,(function(e){return n.onmember&&n.onmember(e)}))}}();var Ce=function(){function e(e,t){this.c=G(),this.v=1,ge.call(this,e,t)}return e.prototype.push=function(e,t){this.c.p(e),ge.prototype.push.call(this,e,t)},e.prototype.p=function(e,t){var n=q(e,this.o,this.v&&(this.o.dictionary?6:2),t&&4,this.s);this.v&&(fe(n,this.o),this.v=0),t&&se(n,n.length-4,this.c.d()),this.ondata(n,t)},e.prototype.flush=function(){ge.prototype.flush.call(this)},e}(),ke=function(){return function(e,t){oe([J,te,function(){return[ae,ge,Ce]}],this,pe.call(this,e,t),(function(e){var t=new Ce(e.data);onmessage=ae(t)}),10,1)}}();var Re=function(){function e(e,t){be.call(this,e,t),this.v=e&&e.dictionary?2:1}return e.prototype.push=function(e,t){if(be.prototype.e.call(this,e),this.v){if(this.p.length<6&&!t)return;this.p=this.p.subarray(de(this.p,this.v-1)),this.v=0}t&&(this.p.length<4&&A(6,"invalid zlib data"),this.p=this.p.subarray(0,-4)),be.prototype.c.call(this,t)},e}(),Te=function(){return function(e,t){oe([K,ne,function(){return[ae,be,Re]}],this,pe.call(this,e,t),(function(e){var t=new Re(e.data);onmessage=ae(t)}),11,0)}}();var je="undefined"!=typeof TextDecoder&&new TextDecoder;try{je.decode(H,{stream:!0}),1}catch(e){}"function"==typeof queueMicrotask?queueMicrotask:"function"==typeof setTimeout&&setTimeout;const Ae=e=>class{constructor(){this.i=new e,this.i.ondata=(e,t)=>{this.ondata(null,e,t)}}push(e,t){try{this.queuedSize+=e.length,this.i.push(e,t),this.queuedSize-=e.length,this.ondrain&&this.ondrain(e.length)}catch(e){this.ondata(e,null,t||!1)}}};let ze=1;try{(new me).terminate()}catch(A){ze=0}const Ee=ze?{gzip:_e,deflate:ke,"deflate-raw":me}:{gzip:Ae(xe),deflate:Ae(Ce),"deflate-raw":Ae(ge)},Fe=ze?{gzip:Me,deflate:Te,"deflate-raw":ye}:{gzip:Ae(Se),deflate:Ae(Re),"deflate-raw":Ae(be)},De=(e,t,n)=>class extends e{constructor(e){if(!arguments.length)throw new TypeError(`Failed to construct '${n}': 1 argument required, but only 0 present.`);const r=t[e];if(!r)throw new TypeError(`Failed to construct '${n}': Unsupported compression format: '${e}'`);let i,a=new r;super({start:e=>{a.ondata=(t,n,r)=>{t?e.error(t):n&&(e.enqueue(n),r&&(i?i():e.terminate()))}},transform:e=>{if(e instanceof ArrayBuffer)e=new Uint8Array(e);else{if(!ArrayBuffer.isView(e))throw new TypeError("The provided value is not of type '(ArrayBuffer or ArrayBufferView)'");e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}if(a.push(e),a.queuedSize>=32768)return new Promise((e=>{a.ondrain=()=>{a.queuedSize<32768&&e()}}))},flush:()=>new Promise((e=>{i=e,a.push(new Uint8Array(0),!0)}))},{size:e=>0|e.byteLength,highWaterMark:65536},{size:e=>0|e.byteLength,highWaterMark:65536})}};const Ie="undefined"==typeof globalThis?"undefined"==typeof self?"undefined"==typeof global?{}:global:self:globalThis;var Le;void 0===Ie.CompressionStream&&(Ie.CompressionStream=(Le=TransformStream,De(Le,Ee,"CompressionStream"))),void 0===Ie.DecompressionStream&&(Ie.DecompressionStream=function(e){return De(e,Fe,"DecompressionStream")}(TransformStream))},6212:(e,t,n)=>{"use strict";n.r(t),n.d(t,{__addDisposableResource:()=>D,__assign:()=>a,__asyncDelegator:()=>C,__asyncGenerator:()=>M,__asyncValues:()=>k,__await:()=>S,__awaiter:()=>p,__classPrivateFieldGet:()=>z,__classPrivateFieldIn:()=>F,__classPrivateFieldSet:()=>E,__createBinding:()=>m,__decorate:()=>s,__disposeResources:()=>L,__esDecorate:()=>c,__exportStar:()=>v,__extends:()=>i,__generator:()=>g,__importDefault:()=>A,__importStar:()=>j,__makeTemplateObject:()=>R,__metadata:()=>d,__param:()=>l,__propKey:()=>h,__read:()=>y,__rest:()=>o,__runInitializers:()=>u,__setFunctionName:()=>f,__spread:()=>w,__spreadArray:()=>_,__spreadArrays:()=>x,__values:()=>b,default:()=>B});var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)};function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var a=function(){return a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},a.apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}function s(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o}function l(e,t){return function(n,r){t(n,r,e)}}function c(e,t,n,r,i,a){function o(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var s,l=r.kind,c="getter"===l?"get":"setter"===l?"set":"value",u=!t&&e?r.static?e:e.prototype:null,h=t||(u?Object.getOwnPropertyDescriptor(u,r.name):{}),f=!1,d=n.length-1;d>=0;d--){var p={};for(var g in r)p[g]="access"===g?{}:r[g];for(var g in r.access)p.access[g]=r.access[g];p.addInitializer=function(e){if(f)throw new TypeError("Cannot add initializers after decoration has completed");a.push(o(e||null))};var m=(0,n[d])("accessor"===l?{get:h.get,set:h.set}:h[c],p);if("accessor"===l){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw new TypeError("Object expected");(s=o(m.get))&&(h.get=s),(s=o(m.set))&&(h.set=s),(s=o(m.init))&&i.unshift(s)}else(s=o(m))&&("field"===l?i.unshift(s):h[c]=s)}u&&Object.defineProperty(u,r.name,h),f=!0}function u(e,t,n){for(var r=arguments.length>2,i=0;i<t.length;i++)n=r?t[i].call(e,n):t[i].call(e);return r?n:void 0}function h(e){return"symbol"==typeof e?e:"".concat(e)}function f(e,t,n){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:n?"".concat(n," ",t):t})}function d(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function p(e,t,n,r){return new(n||(n=Promise))((function(i,a){function o(e){try{l(r.next(e))}catch(e){a(e)}}function s(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}l((r=r.apply(e,t||[])).next())}))}function g(e,t){var n,r,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(l){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(n=1,r&&(i=2&s[0]?r.return:s[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,s[1])).done)return i;switch(r=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,r=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],r=0}finally{n=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var m=Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]};function v(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||m(t,e,n)}function b(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function y(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o}function w(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(y(arguments[t]));return e}function x(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var a=arguments[t],o=0,s=a.length;o<s;o++,i++)r[i]=a[o];return r}function _(e,t,n){if(n||2===arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}function S(e){return this instanceof S?(this.v=e,this):new S(e)}function M(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(e,t||[]),a=[];return r={},o("next"),o("throw"),o("return",(function(e){return function(t){return Promise.resolve(t).then(e,c)}})),r[Symbol.asyncIterator]=function(){return this},r;function o(e,t){i[e]&&(r[e]=function(t){return new Promise((function(n,r){a.push([e,t,n,r])>1||s(e,t)}))},t&&(r[e]=t(r[e])))}function s(e,t){try{(n=i[e](t)).value instanceof S?Promise.resolve(n.value.v).then(l,c):u(a[0][2],n)}catch(e){u(a[0][3],e)}var n}function l(e){s("next",e)}function c(e){s("throw",e)}function u(e,t){e(t),a.shift(),a.length&&s(a[0][0],a[0][1])}}function C(e){var t,n;return t={},r("next"),r("throw",(function(e){throw e})),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,i){t[r]=e[r]?function(t){return(n=!n)?{value:S(e[r](t)),done:!1}:i?i(t):t}:i}}function k(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=b(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,i,(t=e[n](t)).done,t.value)}))}}}function R(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var T=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};function j(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&m(t,e,n);return T(t,e),t}function A(e){return e&&e.__esModule?e:{default:e}}function z(e,t,n,r){if("a"===n&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}function E(e,t,n,r,i){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!i)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(e,n):i?i.value=n:t.set(e,n),n}function F(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw new TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function D(e,t,n){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var r,i;if(n){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(void 0===r){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],n&&(i=r)}if("function"!=typeof r)throw new TypeError("Object not disposable.");i&&(r=function(){try{i.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:r,async:n})}else n&&e.stack.push({async:!0});return t}var I="function"==typeof SuppressedError?SuppressedError:function(e,t,n){var r=new Error(n);return r.name="SuppressedError",r.error=e,r.suppressed=t,r};function L(e){function t(t){e.error=e.hasError?new I(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}return function n(){for(;e.stack.length;){var r=e.stack.pop();try{var i=r.dispose&&r.dispose.call(r.value);if(r.async)return Promise.resolve(i).then(n,(function(e){return t(e),n()}))}catch(e){t(e)}}if(e.hasError)throw e.error}()}const B={__extends:i,__assign:a,__rest:o,__decorate:s,__param:l,__metadata:d,__awaiter:p,__generator:g,__createBinding:m,__exportStar:v,__values:b,__read:y,__spread:w,__spreadArrays:x,__spreadArray:_,__await:S,__asyncGenerator:M,__asyncDelegator:C,__asyncValues:k,__makeTemplateObject:R,__importStar:j,__importDefault:A,__classPrivateFieldGet:z,__classPrivateFieldSet:E,__classPrivateFieldIn:F,__addDisposableResource:D,__disposeResources:L}}}]);
//# sourceMappingURL=608.js.map