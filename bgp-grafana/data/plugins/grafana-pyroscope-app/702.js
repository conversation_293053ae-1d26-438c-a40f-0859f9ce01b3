"use strict";(self.webpackChunkgrafana_pyroscope_app=self.webpackChunkgrafana_pyroscope_app||[]).push([[702],{702:(e,t,n)=>{n.r(t),n.d(t,{default:()=>J});var r=n(1049),o=n(5959),a=n.n(o),i=n(6089),l=n(2007);const c=e=>({container:i.css`
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: ${e.spacing(1)};
    width: 100%;
  `,column:i.css`
    width: 50%;
  `});function s({left:e,right:t}){const n=(0,l.useStyles2)(c);return a().createElement("div",{className:n.container},a().createElement("div",{className:n.column},e),a().createElement("div",{className:n.column},t))}var p=n(7907),u=n(2673),f=n(5656);function m(e,t,n,r,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function d(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){m(a,r,o,i,l,"next",e)}function l(e){m(a,r,o,i,l,"throw",e)}i(void 0)}))}}class y extends f.O{get(e,t){var n=this;return d((function*(){const r=yield n.fetch("/adhocprofiles.v1.AdHocProfileService/Get",{method:"POST",body:JSON.stringify({id:e,profile_type:t})}),o=yield r.json();return{id:o.id,name:o.name,profileTypes:o.profileTypes,profile:JSON.parse(o.flamebearerProfile)}}))()}uploadSingle(e){var t=this;return d((function*(){const n=yield t._readProfileFile(e),r=yield t.fetch("/adhocprofiles.v1.AdHocProfileService/Upload",{method:"POST",body:JSON.stringify({name:e.name,profile:n})}),o=yield r.json();return{id:o.id,name:e.name,profileTypes:o.profileTypes,profile:JSON.parse(o.flamebearerProfile)}}))()}uploadDiff(){return d((function*(){return{id:"?",name:"??",profileTypes:[],profile:null}}))()}_readProfileFile(e){return d((function*(){return new Promise(((t,n)=>{const r=new FileReader;r.addEventListener("load",(()=>{try{t(function(e){const[,t]=e.split(";base64,");if(!t)throw new Error("No content after stripping the base64 prefix.");if(e===t)throw new Error("No base64 prefix?!");return t}(r.result))}catch(e){n(e)}})),r.addEventListener("error",(()=>{n(new Error(`Error while reading file "${e.name}"!`))})),r.readAsDataURL(e)}))}))()}}const g=new y;function h(e,t,n,r,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function b(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){h(a,r,o,i,l,"next",e)}function l(e){h(a,r,o,i,l,"throw",e)}i(void 0)}))}}function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){v(e,t,n[t])}))}return e}function w(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const E={id:"",name:"",profileTypes:[],profile:null};function j(){const[e,t]=(0,o.useState)(!1),[n,r]=(0,o.useState)(E);(0,o.useEffect)((()=>()=>{g.abort()}),[]);const a=(0,o.useCallback)((()=>{g.abort(),t(!1),r(E)}),[]),i=(0,o.useCallback)(function(){var e=b((function*(e){a();try{t(!0);const n=yield g.uploadSingle(e);r(n)}catch(e){r(E),g.isAbortError(e)||(0,u.jx)(e,["Error while uploading profile!",e.message])}t(!1)}));return function(t){return e.apply(this,arguments)}}(),[a]),l=(0,o.useCallback)(function(){var e=b((function*(e){const o=e.value;if(o&&n.id&&n.profileTypes.includes(o)){g.abort(),t(!1),r((e=>w(O({},e),{profile:null}))),t(!0);try{const e=yield g.get(n.id,o);r((t=>w(O({},t),{profile:e.profile})))}catch(e){g.isAbortError(e)||(0,u.jx)(e,["Error while fetching profile!",e.message])}t(!1)}}));return function(t){return e.apply(this,arguments)}}(),[n.id,n.profileTypes]);return{processFile:i,profileTypes:n.profileTypes,selectProfileType:l,profile:n.profile,removeFile:a,isLoading:e}}function P(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){P(e,t,n[t])}))}return e}function T(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const _={accept:{"application/gzip":[".gz"],"application/json":[".json"],"application/proto":[".pb",".pprof"]},multiple:!1,onError(e){(0,u.jx)(e,["Error while uploading file!",e.toString()])}};function x({onFileDropped:e,onFileRemove:t}){const n=(0,o.useCallback)((function(t){e(t[0])}),[e]);return a().createElement(l.FileDropzone,{options:T(S({},_),{onDropAccepted:n}),onFileRemove:t})}var C=n(8629);const D=e=>({flamegraph:i.css`
    margin-top: ${e.spacing(2)};
  `});function F({profile:e,diff:t}){const n=(0,l.useStyles2)(D);return a().createElement("div",{className:n.flamegraph,"data-testid":"flamegraph"},a().createElement(C.C,{profile:e,diff:t}))}const N=e=>({selectorContainer:i.css`
    display: flex;
    justify-content: center;
    margin-bottom: ${e.spacing(2)};
  `});function k({profileTypes:e,onChange:t}){const n=(0,l.useStyles2)(N),r=(0,o.useMemo)((()=>e.map((e=>({value:e,label:e})))),[e]),[i,c]=(0,o.useState)(),s=(0,o.useCallback)((e=>{c(e),t(e)}),[t]);return(0,o.useEffect)((()=>{c(r[0])}),[r]),a().createElement("div",{className:n.selectorContainer},a().createElement(l.InlineFieldRow,null,a().createElement(l.InlineField,{label:"Profile",disabled:!r.length,"data-testid":"profile-types-dropdown"},a().createElement(l.Select,{key:null==i?void 0:i.value,value:i,options:r,onChange:s,width:16}))))}const R=e=>({spinner:i.css`
    text-align: center;
    margin-top: ${e.spacing(2)};
  `});function A(){const e=(0,l.useStyles2)(R);return a().createElement("div",{className:e.spinner},a().createElement(l.Spinner,{size:36}))}function $(){const{processFile:e,profileTypes:t,selectProfileType:n,profile:r,removeFile:o,isLoading:i}=j();return a().createElement("div",null,a().createElement(k,{profileTypes:t,onChange:e=>{(0,p.r)("g_pyroscope_app_ad_hoc_profile_metric_selected"),n(e)}}),a().createElement(x,{onFileDropped:t=>{(0,p.r)("g_pyroscope_app_ad_hoc_file_dropped",{fileType:t.type}),e(t)},onFileRemove:()=>{(0,p.r)("g_pyroscope_app_ad_hoc_file_removed"),o()}}),i&&!r?a().createElement(A,null):null,r&&a().createElement(F,{profile:r}))}const L=(0,o.memo)($);function B(){return a().createElement(s,{left:a().createElement(L,null),right:a().createElement(L,null)})}const I=e=>({tabContent:i.css`
    padding: ${e.spacing(2)};
    margin: ${e.spacing(2)};
  `});function M(){const e=(0,l.useStyles2)(I),[t,n]=(0,o.useState)(0);return a().createElement("div",null,a().createElement(l.TabsBar,null,a().createElement(l.Tab,{label:" Single view",active:0===t,onChangeTab:()=>n(0)}),a().createElement(l.Tab,{label:" Comparison view",active:1===t,onChangeTab:()=>n(1)})),a().createElement(l.TabContent,{className:e.tabContent},0===t&&a().createElement(L,null),1===t&&a().createElement(B,null)))}function J(){return a().createElement(a().Fragment,null,a().createElement(r.s,{title:"Ad hoc view"}),a().createElement(M,null))}},9897:(e,t,n)=>{n.d(t,{N:()=>r});var r=function(e){return e.BASELINE="baseline",e.COMPARISON="comparison",e}({})},8629:(e,t,n)=>{n.d(t,{C:()=>C});var r=n(7781),o=n(3062),a=n(2007),i=n(5959),l=n.n(i),c=n(2673),s=n(7907),p=(n(8727),n(2249)),u=n.n(p),f=n(9090);function m(e,t,n,r,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}class d extends f.Q{upload(e,t){var n,r=this;return(n=function*(){const n=yield r.fetch("/upload/v1",{method:"POST",body:JSON.stringify({name:e,profile:btoa(JSON.stringify(t)),fileTypeData:{units:t.metadata.units,spyName:t.metadata.spyName},type:"json"})});return yield n.json()},function(){var e=this,t=arguments;return new Promise((function(r,o){var a=n.apply(e,t);function i(e){m(a,r,o,i,l,"next",e)}function l(e){m(a,r,o,i,l,"throw",e)}i(void 0)}))})()}constructor(){super("https://flamegraph.com/api",{"content-type":"application/json"})}}const y=new d;var g=n(9897);const h=new Intl.DateTimeFormat("fr-CA",{year:"numeric",month:"2-digit",day:"2-digit",hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"});function b(e){const t=h.formatToParts(e).reduce(((e,{type:t,value:n})=>(e[t]=n,e)),{});return`${t.year}-${t.month}-${t.day}_${t.hour}${t.minute}`}function v(e){const t=new Date(Math.round(1e3*e.from.unix())),n=new Date(Math.round(1e3*e.to.unix()));return`${b(t)}-to-${b(n)}`}function O(e){const[t,n]=e===g.N.BASELINE?["diffFrom","diffTo"]:["diffFrom-2","diffTo-2"],o=new URLSearchParams(window.location.search),a=o.get(t),i=o.get(n);return{raw:{from:a,to:i},from:(0,r.dateTimeParse)(a),to:(0,r.dateTimeParse)(i)}}function w(e){const t=["baseline",v(O(g.N.BASELINE)),"comparison",v(O(g.N.COMPARISON))];return e?[e,...t].join("_"):["flamegraph",...t].join("_")}function E(e,t,n,r,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function j({profile:e,enableFlameGraphDotComExport:t}){const n=function(){var t,n=(t=function*(){(0,s.r)("g_pyroscope_app_export_profile",{format:"flamegraph.com"});const t=w(e.metadata.appName);let n;try{n=yield y.upload(t,e)}catch(e){return void(0,c.jx)(e,["Failed to export to flamegraph.com!",e.message])}const r=document.createElement("a");r.target="_blank",r.href=n.url,document.body.appendChild(r),r.click(),document.body.removeChild(r)},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){E(a,r,o,i,l,"next",e)}function l(e){E(a,r,o,i,l,"throw",e)}i(void 0)}))});return function(){return n.apply(this,arguments)}}();return{data:{shouldDisplayFlamegraphDotCom:Boolean(t)},actions:{downloadPng:()=>{(0,s.r)("g_pyroscope_app_export_profile",{format:"png"});const t=`${w(e.metadata.appName)}.png`;document.querySelector('canvas[data-testid="flameGraph"]').toBlob((e=>{if(e)u()(e,t);else{const e=new Error("No Blob, the image cannot be created.");(0,c.jx)(e,["Failed to export to png!",e.message])}}),"image/png")},downloadJson:()=>{(0,s.r)("g_pyroscope_app_export_profile",{format:"json"});const t=`${w(e.metadata.appName)}.json`,n=`data:text/json;charset=utf-8,${encodeURIComponent(JSON.stringify(e))}`;try{u()(n,t)}catch(e){return void(0,c.jx)(e,["Failed to export to JSON!",e.message])}},uploadToFlamegraphDotCom:n}}}function P(e){const{actions:t}=j(e);return l().createElement(a.Menu,null,l().createElement(a.Menu.Item,{label:"png",onClick:t.downloadPng}),l().createElement(a.Menu.Item,{label:"json",onClick:t.downloadJson}))}function S(e){const{profile:t,enableFlameGraphDotComExport:n}=e;return l().createElement(a.Dropdown,{overlay:l().createElement(P,{profile:t,enableFlameGraphDotComExport:n})},l().createElement(a.Button,{icon:"download-alt",size:"sm",variant:"secondary",fill:"outline","aria-label":"Export profile data",tooltip:"Export profile data"}))}const T=(0,i.memo)(S);function _(e,t,n){const r=[],o=n?7:4;for(let a=0;a<e.length;a+=o)r.push({level:0,label:n?t[e[a+6]]:t[e[a+3]],offset:e[a],val:e[a+1],self:e[a+2],selfRight:n?e[a+5]:0,valRight:n?e[a+4]:0,valTotal:n?e[a+1]+e[a+4]:e[a+1],offsetRight:n?e[a+3]:0,offsetTotal:n?e[a]+e[a+3]:e[a],children:[]});return r}function x({profile:e,diff:t,vertical:n,enableFlameGraphDotComExport:c,collapsedFlamegraphs:s,getExtraContextMenuButtons:p}){const{isLight:u}=(0,a.useTheme2)(),f=(0,i.useMemo)((()=>function(e,t,n,o){if(!e.length)return;const a=[];for(let n=0;n<e.length;n++){a[n]=[];for(const r of _(e[n],t,o))if(r.level=n,a[n].push(r),n>0){const e=a[n].slice(0,-1).reduce(((e,t)=>t.offsetTotal+t.valTotal+e),0)+r.offsetTotal,t=a[n-1];let o=0;for(const n of t){const t=o+n.offsetTotal,a=t+n.valTotal;if(t<=e&&a>e){n.children.push(r);break}o+=n.offsetTotal+n.valTotal}}}const i=[a[0][0]],l=[],c=[],s=[],p=[],u=[],f=[];for(;i.length;){const e=i.shift();l.push(e.label),c.push(e.level),s.push(e.self),p.push(e.val),u.push(e.selfRight),f.push(e.valRight),i.unshift(...e.children)}let m="short";switch(n){case"samples":case"trace_samples":case"lock_nanoseconds":case"nanoseconds":m="ns";break;case"bytes":m="bytes"}const d=[{name:"level",values:c},{name:"label",values:l,type:r.FieldType.string},{name:"self",values:s,config:{unit:m}},{name:"value",values:p,config:{unit:m}}];o&&d.push({name:"selfRight",values:u,config:{unit:m}},{name:"valueRight",values:f,config:{unit:m}});const y={name:"response",meta:{preferredVisualisationType:"flamegraph"},fields:d};return(0,r.createDataFrame)(y)}(e.flamebearer.levels,e.flamebearer.names,e.metadata.units,Boolean(t))),[e,t]);return l().createElement(o.A,{data:f,disableCollapsing:!s,extraHeaderElements:l().createElement(T,{profile:e,enableFlameGraphDotComExport:c}),vertical:n,getTheme:()=>(0,r.createTheme)({colors:{mode:u?"light":"dark"}}),getExtraContextMenuButtons:p,keepFocusOnDataChange:!0})}const C=(0,i.memo)(x)},2673:(e,t,n)=>{n.d(t,{HA:()=>s,jx:()=>c,qq:()=>p});var r=n(7781),o=n(8531),a=n(2096);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function c(e,t){const n=t.reduce(((e,t,n)=>l(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){i(e,t,n[t])}))}return e}({},e),{[`info${n+1}`]:t})),{handheldBy:"displayError"});a.v.error(e,n),(0,o.getAppEvents)().publish({type:r.AppEvents.alertError.name,payload:t})}function s(e){a.v.warn(e),(0,o.getAppEvents)().publish({type:r.AppEvents.alertWarning.name,payload:e})}function p(e){(0,o.getAppEvents)().publish({type:r.AppEvents.alertSuccess.name,payload:e})}},7907:(e,t,n)=>{n.d(t,{r:()=>s});var r=n(8531),o=n(4137),a=n(5176);const i=o.bw.EXPLORE.slice(1);function l(){const{pathname:e}=new URL(window.location.toString());return e.split("/").pop()||""}function c(){const e={appRelease:r.config.apps[o.R2].version,appVersion:a.t,page:l()};return e.page===i&&(e.view=new URLSearchParams(window.location.search).get("explorationType")||""),e}function s(e,t){(0,r.reportInteraction)(e,{props:t,meta:c()})}}}]);
//# sourceMappingURL=702.js.map