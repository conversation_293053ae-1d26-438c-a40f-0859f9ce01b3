"use strict";(self.webpackChunkgrafana_pyroscope_app=self.webpackChunkgrafana_pyroscope_app||[]).push([[75],{7746:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Zl});var r=n(5959),o=n.n(r),a=n(6089),i=n(6356),s=n(2007),l=n(2673);function c(){history.pushState(null,"")}var u,d,p,m=n(7907),f=n(7781),h=n(897),g=n(2096),y=n(3241);class b extends f.BusEventWithPayload{}p="timeseries-data-received",(d="type")in(u=b)?Object.defineProperty(u,d,{value:p,enumerable:!0,configurable:!0,writable:!0}):u[d]=p;var v=function(e){return e.partial="partial",e["attribute-operator-value"]="attribute-operator-value",e["attribute-operator"]="attribute-operator",e}({}),E=function(e){return e["="]="=",e["!="]="!=",e.in="in",e["not-in"]="not-in",e["is-empty"]="is-empty",e["=~"]="=~",e["!~"]="!~",e}({}),S=function(e){return e.attribute="attribute",e.operator="operator",e.value="value",e}({}),w=function(e){return e.attribute="attribute",e.operator="operator",e.value="value",e}({});var O=n(1015),x=n(7268),T=n(8987),P=n(9221),C=n(8531),k=n(9090);class A extends k.Q{constructor(e){var t;const{dataSourceUid:n}=e;let{appSubUrl:r="",bootData:o}=C.config;"/"!==(null==r?void 0:r.at(-1))&&(r+="/"),super(`${r}api/datasources/proxy/uid/${n}`,{"content-type":"application/json","X-Grafana-Org-Id":String((null==o||null===(t=o.user)||void 0===t?void 0:t.orgId)||"")}),function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(this,"dataSourceUid",void 0),this.dataSourceUid=e.dataSourceUid}}function j(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function N(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){j(a,r,o,i,s,"next",e)}function s(e){j(a,r,o,i,s,"throw",e)}i(void 0)}))}}class I extends A{static queryToMatchers(e){const t=e.indexOf("{");if(t>0){return[`{__profile_type__="${e.substring(0,t)}", ${e.substring(t+1,e.length)}`]}return 0===t?[e]:[`{__profile_type__="${e}"}`]}fetchLabels(e,t,n){var r=this;return N((function*(){return r._post("/querier.v1.QuerierService/LabelNames",{matchers:I.queryToMatchers(e),start:t,end:n}).then((e=>e.json()))}))()}fetchLabelValues(e,t,n,r){var o=this;return N((function*(){return o._post("/querier.v1.QuerierService/LabelValues",{name:e,matchers:I.queryToMatchers(t),start:n,end:r}).then((e=>e.json()))}))()}_post(e,t){return super.fetch(e,{method:"POST",body:JSON.stringify(t)})}constructor(e){super(e)}}class _{static buildCacheKey(e){let t="";for(const n of e)t+=String(n);return t}get(e){return this.store.get(_.buildCacheKey(e))}set(e,t){this.store.set(_.buildCacheKey(e),t)}delete(e){this.store.delete(_.buildCacheKey(e))}constructor(){!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(this,"store",new Map)}}function R(e,t){if(!e)throw new Error(t)}function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class D{setApiClient(e){this.apiClient=e}setCacheClient(e){this.cacheClient=e}cancel(e){this.apiClient.abort(e)}constructor(e){L(this,"apiClient",void 0),L(this,"cacheClient",void 0),this.apiClient=e.apiClient,this.cacheClient=null==e?void 0:e.cacheClient}}var F=n(5656);function $(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function B(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){$(a,r,o,i,s,"next",e)}function s(e){$(a,r,o,i,s,"throw",e)}i(void 0)}))}}class M extends F.O{static queryToMatchers(e){const t=e.indexOf("{");if(t>0){return[`{__profile_type__="${e.substring(0,t)}", ${e.substring(t+1,e.length)}`]}return 0===t?[e]:[`{__profile_type__="${e}"}`]}fetchLabels(e,t,n){var r=this;return B((function*(){return r._post("/querier.v1.QuerierService/LabelNames",{matchers:M.queryToMatchers(e),start:t,end:n}).then((e=>e.json()))}))()}fetchLabelValues(e,t,n,r){var o=this;return B((function*(){return o._post("/querier.v1.QuerierService/LabelValues",{name:e,matchers:M.queryToMatchers(t),start:n,end:r}).then((e=>e.json()))}))()}_post(e,t){return super.fetch(e,{method:"POST",body:JSON.stringify(t)})}}function U(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function V(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){U(a,r,o,i,s,"next",e)}function s(e){U(a,r,o,i,s,"throw",e)}i(void 0)}))}}function q(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class G extends D{static parseLabelsResponse(e){if(!Array.isArray(e.names))return[];return Array.from(new Set(e.names.filter(G.isNotMetaLabelOrServiceName))).map((e=>({value:e,label:e})))}static parseLabelValuesResponse(e){if(!Array.isArray(e.names))return[];return e.names.map((e=>({value:e,label:e})))}static assertParams(e,t,n){R(Boolean(e),'Missing "query" parameter!'),R(t>0&&n>0&&n>t,"Invalid timerange!")}listLabels({query:e,from:t,to:n}){var r=this;return V((function*(){G.assertParams(e,t,n);const o=[r.apiClient.baseUrl,e,t,n],a=r.cacheClient.get(o);if(a){const e=yield a,t=G.parseLabelsResponse(e);return t.length||r.cacheClient.delete(o),t}const i=r.apiClient.fetchLabels(e,t,n);r.cacheClient.set(o,i);try{const e=yield i;return G.parseLabelsResponse(e)}catch(e){throw r.cacheClient.delete(o),e}}))()}listLabelValues({label:e,query:t,from:n,to:r}){var o=this;return V((function*(){G.assertParams(t,n,r),R(Boolean(e),"Missing label value!");const a=[o.apiClient.baseUrl,e,t,n,r],i=o.cacheClient.get(a);if(i){const e=yield i,t=G.parseLabelsResponse(e);return t.length||o.cacheClient.delete(a),t}const s=o.apiClient.fetchLabelValues(e,t,n,r);o.cacheClient.set(a,s);try{const e=yield s;return G.parseLabelValuesResponse(e)}catch(e){throw o.cacheClient.delete(a),e}}))()}constructor(e){super({apiClient:e.apiClient}),q(this,"cacheClient",void 0),this.cacheClient=e.cacheClient}}q(G,"isNotMetaLabelOrServiceName",(e=>!/^(__.+__|service_name)$/.test(e)));const K=new G({apiClient:new M,cacheClient:new _});function H(e,t){const n=e.filter((({type:e})=>e!==v.partial)),r=t.filter((({type:e})=>e!==v.partial));return n.length===r.length&&n.every((e=>r.find((({type:t,attribute:n,operator:r,value:o})=>{var a,i;return t===e.type&&n.value===e.attribute.value&&(null==r?void 0:r.value)===(null===(a=e.operator)||void 0===a?void 0:a.value)&&(null==o?void 0:o.value)===(null===(i=e.value)||void 0===i?void 0:i.value)}))))}function z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Y={type:v["attribute-operator"],operator:{value:E["is-empty"],label:"is empty"},value:{value:E["is-empty"],label:""}},Q=e=>function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){z(e,t,n[t])}))}return e}({},e,Y);function W(e,t){const n=t.filter((({type:e})=>e!==v.partial)).map((e=>{const{attribute:t,operator:n,value:r}=e;switch(n.value){case E.in:return`${t.value}=~"${r.value}"`;case E["not-in"]:return`${t.value}!~"${r.value}"`;case E["is-empty"]:return`${t.value}=""`;default:return`${t.value}${n.value}"${r.value}"`}}));var r;const[,o]=null!==(r=e.match(/{.*(service_name="[^"]*").*}/))&&void 0!==r?r:[];return o&&n.unshift(o),e.replace(/{(.*)}$/,`{${n.join(",")}}`)}const J=e=>e.at(-1)||null,X=e=>e===E.in||e===E["not-in"],Z=e=>(R(Boolean(e),"The filter is falsy!"),e.type===v.partial);function ee(e,t){return e!==t&&(t!==E["is-empty"]&&([E["=~"],E["!~"],E.in,E["not-in"],E["is-empty"]].includes(e)||[E["=~"],E["!~"],E.in,E["not-in"]].includes(t)))}function te(e,t){R(void 0!==e.operator,"No operator for the filter under edition!");return ee(e.operator.value,t)}function ne(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function re(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const oe=e=>{const t=e.matchAll(/(\w+)(=|!=|=~|!~)"([^"]*)"/g);return Array.from(t).map((([,e,t,n])=>[e,t,n]))},ae=/.+:[^{]+\{(.+)\}$/,ie=/.*(\^|\$|\*|\+|\{|\}|\?).*/;function se(e){if(!e)return[];const t=e.match(ae);if(!t)return[];return oe(t[1]).filter((([e])=>"service_name"!==e)).map((([e,t,n])=>{const r={id:(0,T.Ak)(10),type:v["attribute-operator-value"],active:!0,attribute:{value:e,label:e},operator:{value:t,label:t},value:{value:n,label:n}};if(t===E["="]&&""===n)return Q(r);return[E["=~"],E["!~"]].includes(t)&&!ie.test(n)?re(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ne(e,t,n[t])}))}return e}({},r),{operator:t===E["=~"]?{value:E.in,label:"in"}:{value:E["not-in"],label:"not in"},value:{value:n,label:n.split("|").map((e=>e.trim())).join(", ")}}):r}))}function le(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ce(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const ue=(e,t)=>e.map((e=>e.type!==v.partial?ce(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){le(e,t,n[t])}))}return e}({},e),{active:t}):e));function de(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function pe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){de(e,t,n[t])}))}return e}function me(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function fe(e,t){const n=H(e,se(t.inputParams.query));return{filters:n?ue(e,!0):e,query:W(t.query,e),isQueryUpToDate:n}}const he={cancelAllLoad:()=>{K.cancel("Discarded by user")},setFilterAttribute:(0,P.kp)(((e,t)=>{const n=[...e.filters,{id:(0,T.Ak)(10),type:v.partial,active:!1,attribute:t.data}];return me(pe({},e),{filters:n,isQueryUpToDate:H(n,se(e.inputParams.query))})})),editFilterAttribute:(0,P.kp)(((e,t)=>{if(null===e.edition)throw new Error("Cannot edit filter attribute without edition data!");const{filterId:n}=e.edition,r=e.filters.map((e=>e.id===n?me(pe({},e),{attribute:t.data,operator:void 0,value:void 0}):e));return me(pe({},e),{filters:r,isQueryUpToDate:H(r,se(e.inputParams.query)),edition:null})})),setFilterOperator:(0,P.kp)(((e,t)=>{const n=e.filters.map((e=>{if(!Z(e))return e;const n=t.data;return n.value===E["is-empty"]?Q(e):me(pe({},e),{operator:n,value:void 0})}));return pe({},e,fe(n,e))})),editFilterOperator:(0,P.kp)(((e,t)=>{if(null===e.edition)throw new Error("Cannot edit filter operator without edition data!");const{filterId:n}=e.edition,r=t.data;let o=null;const a=e.filters.map((t=>{const a=t.operator.value;return t.id!==n||a===r.value?t:r.value===E["is-empty"]?Q(me(pe({},t),{active:!1})):(a===E["is-empty"]&&(t.value={value:"(no value)",label:"(no value)"}),!Z(t)&&ee(a,r.value)&&(o=me(pe({},e.edition),{part:S.value})),me(pe({},t),{operator:r,value:X(a)&&!X(r.value)&&t.value?{value:t.value.value.split("|").shift(),label:t.value.label.split(", ").shift()}:t.value,active:!1}))}));return me(pe({},e,fe(a,e)),{edition:o})})),setFilterValue:(0,P.kp)(((e,t)=>{const n=e.filters.map((e=>Z(e)?me(pe({},e),{type:v["attribute-operator-value"],active:!1,value:t.data}):e));return pe({},e,fe(n,e))})),editFilterValue:(0,P.kp)(((e,t)=>{if(null===e.edition)throw new Error("Cannot edit filter value without edition data!");const{filterId:n}=e.edition,r=e.filters.map((e=>e.id===n?me(pe({},e),{type:v["attribute-operator-value"],active:!1,value:t.data}):e));return me(pe({},e,fe(r,e)),{edition:null})})),removeFilter:(0,P.kp)(((e,t)=>{const n=t.data,r=ue(e.filters.filter((({id:e})=>e!==n)),!1);return pe({},e,fe(r,e))})),removeLastFilter:(0,P.kp)((e=>{const{filters:t}=e,n=J(t);if(!n)return e;if(Z(n)&&n.operator){const r=t.slice(0,t.length-1).concat(me(pe({},n),{operator:void 0}));return me(pe({},e),{filters:r,isQueryUpToDate:!0})}const r=t.slice(0,t.length-1).map((e=>me(pe({},e),{active:!1})));return pe({},e,fe(r,e))})),setEdition:(0,P.kp)({edition:(e,t)=>t.data}),changeInputParams:(0,P.kp)(((e,t)=>(t.data.dataSourceUid&&K.setApiClient(new I({dataSourceUid:t.data.dataSourceUid})),{inputParams:t.data,query:t.data.query,filters:se(t.data.query),isQueryUpToDate:!0}))),activateFilters:(0,P.kp)((e=>pe({},e,fe(e.filters,e))))};function ge(e){const{edition:t,filters:n}=e;R(null!==t,'"edition" is null!');const r=n.find((({id:e})=>e===t.filterId));return R(void 0!==r,"Cannot find the filter under edition!"),r}const ye={shouldSuggestAttributes:e=>{const t=J(e.filters);return!t||!Z(t)},shouldSuggestOperators:e=>{var t;return!(null===(t=J(e.filters))||void 0===t?void 0:t.operator)},shouldSuggestValues:e=>{const t=J(e.filters);return Boolean((null==t?void 0:t.operator)&&!(null==t?void 0:t.value))},isEditing:e=>null!==e.edition,shouldSuggestValuesAfterOperatorEdition:(e,t)=>!!e.edition&&te(ge(e),t.data.value),shouldNotSuggestValuesAfterOperatorEdition:(e,t)=>!!e.edition&&!te(ge(e),t.data.value),hasPartialFilter:e=>{const t=J(e.filters);return Boolean(t&&Z(t))},shouldEditAttribute:(e,t)=>t.data.part===S.attribute,shouldEditOperator:(e,t)=>t.data.part===S.operator,shouldEditValue:(e,t)=>t.data.part===S.value};function be(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}const ve=new class{list(){return(e=function*(){return[{value:"=",label:"="},{value:"!=",label:"!="},{value:"is-empty",label:"is empty"},{value:"in",label:"in",description:"Is one of"},{value:"not-in",label:"not in",description:"Is not one of"},{value:"=~",label:"=~",description:"Matches regex"},{value:"!~",label:"!~",description:"Does not match regex"}]},function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){be(a,r,o,i,s,"next",e)}function s(e){be(a,r,o,i,s,"throw",e)}i(void 0)}))})();var e}},Ee=e=>e.startsWith("__");function Se(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function we(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Se(a,r,o,i,s,"next",e)}function s(e){Se(a,r,o,i,s,"throw",e)}i(void 0)}))}}function Oe(e,t){if(e instanceof DOMException&&"AbortError"===e.name)return[];throw g.v.error(e,{info:t}),e}const xe={fetchLabels:function(){var e=we((function*(e){const{from:t,to:n}=e.inputParams;try{const r=yield K.listLabels({query:e.query,from:t,to:n}),o=[],a=[];return r.forEach((e=>{Ee(e.value)?a.push(e):o.push(e)})),[...o,...a]}catch(e){return Oe(e,"Error while fetching labels!")}}));return function(t){return e.apply(this,arguments)}}(),fetchOperators:we((function*(){try{return yield ve.list()}catch(e){return Oe(e,"Error while fetching operators!")}})),fetchLabelValues:function(){var e=we((function*(e){let t,{query:n,edition:r,suggestions:o}=e;try{if(r){const o=e.filters.filter((e=>e.id!==r.filterId||(t=e,!1)));if(!t)throw new Error(`Impossible to edit filter id="${r.filterId}": no filter found!`);n=W(n,o)}else if(t=J(e.filters),(null==t?void 0:t.type)!==v.partial)throw new Error("Impossible to load label values: no partial filter found!");if(o.disabled)return[];const a=t.attribute.value,{from:i,to:s}=e.inputParams;return yield K.listLabelValues({label:a,query:n,from:i,to:s})}catch(e){return Oe(e,"Error while fetching label values!")}}));return function(t){return e.apply(this,arguments)}}()},Te={always:[{cond:"shouldSuggestOperators",target:"loadOperators"},{cond:"shouldSuggestValues",target:"loadLabelValues"},{target:"idle"}]},Pe={FILTER_ADD:"Filter by label values...",SELECT_LABEL:"Select a label...",SELECT_OPERATOR:"Select an operator...",SELECT_VALUE:"Select a value...",SELECT_VALUES:"Select values...",TYPE_VALUE:"Type a regex...",LOADING:"Loading...",ERROR_LOAD:"An unexpected error occurred while loading! Please try again.",SUGGESTIONS_NONE:"No suggestions available.",SUGGESTIONS_DISABLED:"Suggestions are disabled for this label."},Ce=e=>e===E["=~"]||e===E["!~"];function ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ae(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const je={entry:["cancelAllLoad",(0,P.kp)({suggestions:e=>{let t=Pe.FILTER_ADD,n=!1;const r=J(e.filters);return r&&Z(r)&&(r.operator?(n=Ce(r.operator.value),t=X(r.operator.value)?Pe.SELECT_VALUES:n?Pe.TYPE_VALUE:Pe.SELECT_VALUE):t=Pe.SELECT_OPERATOR),Ae(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ke(e,t,n[t])}))}return e}({},Ye.suggestions),{placeholder:t,allowCustomValue:n})},edition:null})],on:{START_INPUT:[{cond:"shouldSuggestAttributes",target:"loadLabels"},{cond:"shouldSuggestOperators",target:"loadOperators"},{cond:"shouldSuggestValues",target:"loadLabelValues"}],EDIT_FILTER:[{cond:"shouldEditAttribute",target:"loadLabels",actions:["setEdition"]},{cond:"shouldEditOperator",target:"loadOperators",actions:["setEdition"]},{cond:"shouldEditValue",target:"loadLabelValues",actions:["setEdition"]}],REMOVE_FILTER:[{cond:"hasPartialFilter",target:"autoSuggestProxy",actions:["removeFilter"]},{target:"idle",actions:["removeFilter"]}],REMOVE_LAST_FILTER:{target:"idle",actions:["removeLastFilter"]},CHANGE_INPUT_PARAMS:{target:"idle",actions:["changeInputParams"]},EXECUTE_QUERY:{target:"idle",actions:["activateFilters"]}}};function Ne(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ne(e,t,n[t])}))}return e}function _e(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Re={entry:(0,P.kp)({suggestions:()=>_e(Ie({},Ye.suggestions),{isVisible:!0,isLoading:!0})}),invoke:{id:"fetchLabels",src:"fetchLabels",onDone:{target:"displayLabels",actions:(0,P.kp)({suggestions:(e,t)=>_e(Ie({},e.suggestions),{items:t.data.filter((({value:t})=>!e.filters.some((e=>{var n;return(null===(n=e.attribute)||void 0===n?void 0:n.value)===t})))),isLoading:!1})})},onError:{target:"displayLabels",actions:(0,P.kp)({suggestions:(e,t)=>_e(Ie({},e.suggestions),{isLoading:!1,error:t.data})})}},on:{DISCARD_SUGGESTIONS:"idle"}},Le={entry:(0,P.kp)({suggestions:e=>_e(Ie({},e.suggestions),{type:w.attribute,isVisible:!0,placeholder:Pe.SELECT_LABEL})}),on:{DISCARD_SUGGESTIONS:"idle",SELECT_SUGGESTION:[{cond:"isEditing",target:"loadOperators",actions:["editFilterAttribute"]},{target:"loadOperators",actions:["setFilterAttribute"]}],REMOVE_LAST_FILTER:{target:"idle",actions:["removeLastFilter"]}}};function De(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Fe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){De(e,t,n[t])}))}return e}function $e(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Be={entry:(0,P.kp)({suggestions:e=>{const t=e.edition?ge(e):J(e.filters);return R(void 0!==typeof(null==t?void 0:t.operator),"No operator for the target filter!"),$e(Fe({},Ye.suggestions),{disabled:["=~","!~"].includes(t.operator.value)||Ee(t.attribute.value),isVisible:!0,isLoading:!0})}}),invoke:{id:"fetchLabelValues",src:"fetchLabelValues",onDone:{target:"displayLabelValues",actions:(0,P.kp)({suggestions:(e,t)=>$e(Fe({},e.suggestions),{items:t.data,isLoading:!1})})},onError:{target:"displayLabelValues",actions:(0,P.kp)({suggestions:(e,t)=>$e(Fe({},e.suggestions),{items:[],isLoading:!1,error:t.data})})}},on:{DISCARD_SUGGESTIONS:"idle"}},Me={entry:(0,P.kp)({suggestions:e=>{const t=e.edition?ge(e):J(e.filters);R(void 0!==typeof(null==t?void 0:t.operator),"No operator for the target filter!");const n=t.operator.value,r=Ce(n)||e.suggestions.disabled,o=X(n);let a,i;return a=r?Pe.TYPE_VALUE:o?Pe.SELECT_VALUES:Pe.SELECT_VALUE,i=e.suggestions.error?Pe.ERROR_LOAD:e.suggestions.disabled?Pe.SUGGESTIONS_DISABLED:Pe.SUGGESTIONS_NONE,$e(Fe({},e.suggestions),{type:w.value,isVisible:!0,placeholder:a,noOptionsMessage:i,allowCustomValue:r,multiple:o})}}),on:{DISCARD_SUGGESTIONS:"idle",SELECT_SUGGESTION:[{cond:"isEditing",target:"autoSuggestProxy",actions:["editFilterValue"]},{target:"idle",actions:["setFilterValue"]}],REMOVE_LAST_FILTER:{target:"loadOperators",actions:["removeLastFilter"]}}};function Ue(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ve(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ue(e,t,n[t])}))}return e}function qe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Ge={entry:(0,P.kp)({suggestions:()=>qe(Ve({},Ye.suggestions),{isVisible:!0,isLoading:!0})}),invoke:{id:"fetchOperators",src:"fetchOperators",onDone:{target:"displayOperators",actions:(0,P.kp)({suggestions:(e,t)=>qe(Ve({},e.suggestions),{items:t.data,isLoading:!1})})},onError:{target:"displayOperators",actions:(0,P.kp)({suggestions:(e,t)=>qe(Ve({},e.suggestions),{items:[],isLoading:!1,error:t.data})})}},on:{DISCARD_SUGGESTIONS:"idle"}},Ke={entry:(0,P.kp)({suggestions:e=>qe(Ve({},e.suggestions),{type:w.operator,isVisible:!0,placeholder:Pe.SELECT_OPERATOR,allowCustomValue:!1,multiple:!1})}),on:{DISCARD_SUGGESTIONS:"idle",SELECT_SUGGESTION:[{cond:"shouldSuggestValuesAfterOperatorEdition",target:"loadLabelValues",actions:["editFilterOperator"]},{cond:"shouldNotSuggestValuesAfterOperatorEdition",target:"autoSuggestProxy",actions:["editFilterOperator"]},{cond:"hasPartialFilter",target:"autoSuggestProxy",actions:["setFilterOperator"]},{target:"loadLabelValues",actions:["setFilterOperator"]}],REMOVE_LAST_FILTER:{target:"loadLabels",actions:["removeLastFilter"]}}};function He(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ze(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Ye=Object.freeze({inputParams:{query:"",from:0,to:0},query:"",filters:[],isQueryUpToDate:!0,edition:null,suggestions:{type:null,items:[],isVisible:!1,isLoading:!1,error:null,placeholder:"",noOptionsMessage:"",allowCustomValue:!1,multiple:!1,disabled:!1}}),Qe=e=>({id:"query-builder",initial:"idle",context:e,predictableActionArguments:!0,states:{idle:je,loadLabels:Re,displayLabels:Le,loadOperators:Ge,displayOperators:Ke,loadLabelValues:Be,displayLabelValues:Me,autoSuggestProxy:Te}}),We={guards:ye,services:xe,actions:he};function Je(e){const{query:t}=e,n=ze(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){He(e,t,n[t])}))}return e}({},Ye),{inputParams:e,query:t,filters:se(t)}),r=(0,O.O)(Qe(n),We);return{actor:(0,x.U4)(r),initialContext:n}}const Xe=new Intl.Collator("en",{sensitivity:"case"}).compare,Ze=e=>(t,n)=>{const r=e.some((e=>e.value===t.value)),o=e.some((e=>e.value===n.value));return r&&o?Xe(t.value,n.value):o?1:r?-1:0};function et({selection:e,suggestions:t,onCloseMenu:n}){const a=(0,s.useStyles2)(tt),i=(0,r.useMemo)((()=>{const t=e.value.split("|"),n=e.label.split(", ");return t.map(((e,t)=>({value:e,label:n[t]})))}),[e]),[l,c]=(0,r.useState)(i),u=(0,r.useMemo)((()=>t.items.sort(Ze(l))),[t.items]),d=(0,r.useCallback)((e=>{c(e.map((({value:e="",label:t=""})=>({value:e,label:t}))))}),[]),p=(0,r.useCallback)((()=>{n(l)}),[n,l]);return o().createElement(s.MultiSelect,{className:a.editionSelect,placeholder:t.placeholder,loadingMessage:Pe.LOADING,closeMenuOnSelect:!1,hideSelectedOptions:!1,backspaceRemovesValue:!0,autoFocus:!0,value:l,onChange:d,onCloseMenu:p,options:u,isOpen:!0,isLoading:t.isLoading,invalid:Boolean(t.error),noOptionsMessage:t.noOptionsMessage})}const tt=()=>({editionSelect:a.css`
    position: absolute;
    z-index: 1;

    [aria-label='Remove'] svg {
      display: none;
    }
  `});function nt({placeholder:e,defaultValue:t,onFocus:n,onChange:i,onBlur:l}){const c=(0,s.useStyles2)(rt),u=(0,r.useRef)(null),[d,p]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{u.current&&u.current.focus()}),[]),o().createElement(s.Input,{ref:u,className:(0,a.cx)(t&&c.edition),invalid:d,placeholder:e,defaultValue:t,onFocus:n,onKeyUp:e=>{const t=e.target.value.trim();"Enter"===e.code&&(t?i({value:t,label:t}):p(!0))},onBlur:e=>{const t=e.target.value.trim();t?i({value:t,label:t}):l()}})}const rt=()=>({edition:a.css`
    position: absolute;
    z-index: 1;
  `}),ot=()=>({editionSelect:a.css`
    position: absolute;
    z-index: 1;
    min-width: 160px;
    box-shadow: none;

    & input:focus {
      outline: none !important;
    }
  `});function at({selection:e,suggestions:t,onChange:n,onCloseMenu:r}){const a=(0,s.useStyles2)(ot);return t.allowCustomValue?o().createElement(nt,{defaultValue:e.value,placeholder:t.placeholder,onChange:n,onBlur:r}):o().createElement(s.Select,{className:a.editionSelect,placeholder:t.placeholder,loadingMessage:Pe.LOADING,closeMenuOnSelect:!1,autoFocus:!0,value:e.value,onChange:n,onCloseMenu:r,options:t.items,isOpen:!0,isLoading:t.isLoading,invalid:Boolean(t.error),noOptionsMessage:t.noOptionsMessage})}const it=()=>{},st=({filter:e,onClick:t,onRemove:n})=>{const r=(0,s.useStyles2)(mt),{attribute:i,operator:l,active:c}=e,u=c?r.chiclet:(0,a.cx)(r.chiclet,r.inactiveChiclet);return o().createElement("div",{className:u,"aria-label":"Filter"},o().createElement(s.Tag,{"aria-label":"Filter label",className:r.chicletAttribute,name:i.label,onClick:it}),o().createElement(s.Tag,{"aria-label":"Filter operator",className:r.chicletOperator,name:l.label,onClick:(n,r)=>t(r,e,S.operator),tabIndex:0}),o().createElement(s.Tag,{"aria-label":"Remove filter",className:r.chicletRemoveButton,icon:"times",name:"",onClick:(t,r)=>n(r,e),tabIndex:0}))},lt=()=>{},ct=({filter:e,onClick:t,onRemove:n})=>{const r=(0,s.useStyles2)(mt),{attribute:i,operator:l,value:c,active:u}=e,d=u?r.chiclet:(0,a.cx)(r.chiclet,r.inactiveChiclet);return o().createElement("div",{className:d,"aria-label":"Filter"},o().createElement(s.Tag,{"aria-label":"Filter label",className:r.chicletAttribute,name:i.label,onClick:lt}),o().createElement(s.Tag,{"aria-label":"Filter operator",className:r.chicletOperator,name:l.label,onClick:(n,r)=>t(r,e,S.operator),tabIndex:0}),o().createElement(s.Tooltip,{content:c.label},o().createElement(s.Tag,{"aria-label":"Filter value",name:c.label,className:r.chicletValue,onClick:(n,r)=>t(r,e,S.value),tabIndex:0})),o().createElement(s.Tag,{"aria-label":"Remove filter",className:r.chicletRemoveButton,icon:"times",name:"",onClick:(t,r)=>n(r,e),tabIndex:0}))},ut=({filter:e,onClick:t})=>{const n=(0,s.useStyles2)(mt),{attribute:r,operator:i}=e;return r||i?o().createElement("div",{className:(0,a.cx)(n.chiclet,n.partialChiclet),"aria-label":"Partial filter"},o().createElement(s.Tag,{colorIndex:9,name:r.label,title:`Edit "${r.label}"`,onClick:(n,r)=>t(r,e,S.attribute),tabIndex:0}),i&&o().createElement(s.Tag,{colorIndex:9,name:i.label,title:`Edit "${i.label}"`,className:n.chicletOperator,onClick:(n,r)=>t(r,e,S.operator),tabIndex:0})):null},dt="rgb(61, 113, 217)",pt="#4a4b52",mt=e=>({chiclet:a.css`
    display: flex;
    align-items: center;
    border: 1px solid ${dt};
    border-radius: 2px;

    & > button {
      height: 30px;
      background-color: ${e.colors.background.primary};
      color: ${e.colors.text.maxContrast};
    }

    & > :first-child {
      background-color: ${dt};
      color: ${"#fff"};
      border-radius: 0;

      &:hover {
        cursor: not-allowed !important;
      }
    }

    & > :last-child {
      border-left: 1px solid ${dt};
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  `,partialChiclet:a.css`
    border-color: ${pt};
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    & > :first-child {
      background-color: ${e.colors.background.secondary};
      color: ${e.colors.text.maxContrast};
      border-radius: 0;
      border-left: 0;

      &:hover {
        cursor: pointer !important;
      }
    }

    & > :last-child {
      border-color: ${pt};
      color: ${e.colors.text.maxContrast};
    }
  `,inactiveChiclet:a.css`
    border-color: ${pt};

    & > button {
      color: ${e.colors.text.maxContrast};
    }

    & > :first-child {
      background-color: ${e.colors.background.secondary};
      color: ${e.colors.text.maxContrast};
    }

    & > :last-child {
      border-color: ${pt};
    }
  `,chicletAttribute:a.css`
    &:hover {
      opacity: 1 !important;
    }
  `,chicletOperator:a.css`
    &:hover {
      background-color: ${e.colors.background.secondary};
    }
  `,chicletValue:a.css`
    flex-grow: 1;
    text-align: left;
    max-width: 420px;
    text-overflow: ellipsis;
    text-wrap: nowrap;
    overflow: hidden;

    &:hover {
      background-color: ${e.colors.background.secondary};
    }
  `,chicletRemoveButton:a.css`
    &:hover {
      background-color: ${e.colors.background.secondary};
    }

    & svg {
      width: 12px;
      height: 12px;
    }
  `}),ft=({filter:e,onClick:t,onRemove:n})=>{switch(e.type){case v.partial:return o().createElement(ut,{filter:e,onClick:t});case v["attribute-operator-value"]:return o().createElement(ct,{filter:e,onClick:t,onRemove:n});case v["attribute-operator"]:return o().createElement(st,{filter:e,onClick:t,onRemove:n});default:throw new TypeError(`Unsupported filter type "${e.type}" (${JSON.stringify(e)})!`)}},ht=(0,r.memo)(ft,((e,t)=>JSON.stringify(e.filter)===JSON.stringify(t.filter))),gt=()=>({chicletsList:a.css`
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
  `,editChicletContainer:a.css`
    position: relative;
  `});function yt({filters:e,onClickChiclet:t,onRemoveChiclet:n,edition:r,suggestions:a,onChangeSingleSuggestion:i,onCloseSingleSuggestionsMenu:l,onCloseMultipleSuggestionsMenu:c}){const u=(0,s.useStyles2)(gt);return o().createElement("div",{className:u.chicletsList,"data-testid":"filtersList"},e.map((e=>o().createElement("div",{key:e.id,className:u.editChicletContainer},o().createElement(ht,{filter:e,onClick:t,onRemove:n}),(null==r?void 0:r.filterId)===e.id?a.multiple?o().createElement(et,{selection:e[r.part],suggestions:a,onCloseMenu:c}):o().createElement(at,{key:r.part,selection:e[r.part],suggestions:a,onChange:i,onCloseMenu:l}):null))))}const bt=(0,r.memo)(yt),vt=()=>({select:a.css`
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  `});function Et({suggestions:e,onFocus:t,onChange:n,onKeyDown:a,onCloseMenu:i}){const l=(0,s.useStyles2)(vt),c=function(e){const[t,n]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{!e||t?e!==t&&n(e):setTimeout((()=>n(!0)),0)}),[t,e]),t}(e.isVisible);return e.allowCustomValue?o().createElement(nt,{placeholder:e.placeholder,onFocus:t,onChange:n,onBlur:i}):o().createElement(s.Select,{className:l.select,placeholder:e.placeholder,loadingMessage:Pe.LOADING,closeMenuOnSelect:!1,value:null,onFocus:t,onKeyDown:a,onChange:n,onCloseMenu:i,options:e.items,isOpen:c,isLoading:e.isLoading,invalid:Boolean(e.error),noOptionsMessage:e.noOptionsMessage})}const St=()=>{};function wt(){const e=(0,s.useStyles2)(vt);return o().createElement(s.Select,{disabled:!0,className:e.select,placeholder:Pe.FILTER_ADD,onChange:St})}function Ot({suggestions:e,onFocus:t,onKeyDown:n,onCloseMenu:a}){const i=(0,s.useStyles2)(xt),[l,c]=(0,r.useState)([]),u=(0,r.useCallback)((e=>{c(e.map((({value:e="",label:t=""})=>({value:e,label:t}))))}),[]),d=(0,r.useCallback)((e=>{n(e,l)}),[n,l]),p=(0,r.useCallback)((()=>{a(l)}),[a,l]);return o().createElement(s.MultiSelect,{className:i.select,placeholder:e.placeholder,loadingMessage:Pe.LOADING,closeMenuOnSelect:!1,hideSelectedOptions:!1,backspaceRemovesValue:!0,autoFocus:!0,value:l,onFocus:t,onKeyDown:d,onChange:u,onCloseMenu:p,options:e.items,isOpen:e.isVisible,isLoading:e.isLoading,invalid:Boolean(e.error),noOptionsMessage:e.noOptionsMessage})}const xt=()=>({select:a.css`
    [aria-label='Remove'] svg {
      display: none;
    }
  `}),Tt=()=>({queryBuilder:a.css`
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    flex-wrap: wrap;
    gap: 4px;
    width: 100%;
  `,controls:a.css`
    display: flex;
    align-self: flex-start;
    flex-grow: 1;
  `,executeButton:a.css`
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  `});function Pt(e){const t=(0,s.useStyles2)(Tt),{actor:n,internalProps:i}=function({dataSourceUid:e,query:t,from:n,to:o,onChangeQuery:a}){const{actor:i,initialContext:s}=(0,r.useMemo)((()=>Je({query:t,from:n,to:o})),[]);(0,r.useEffect)((()=>{i.send({type:"CHANGE_INPUT_PARAMS",data:{dataSourceUid:e,query:t,from:n,to:o}})}),[i,e,t,n,o]);const[l,c]=(0,r.useState)(s);return(0,r.useEffect)((()=>(i.start(),i.subscribe((({event:e,context:t})=>{"EXECUTE_QUERY"===e.type&&a(t.query,t.filters),c(t)})),()=>{i.stop()})),[i]),{actor:i,internalProps:l}}(e),{filters:l,edition:c,isQueryUpToDate:u,suggestions:d}=i,{onClickChiclet:p,onRemoveChiclet:m}=function(e){const t=(0,r.useCallback)(((t,n,r)=>{e.send({type:"EDIT_FILTER",data:{filterId:n.id,part:r}})}),[e]),n=(0,r.useCallback)(((t,n)=>{e.send({type:"REMOVE_FILTER",data:n.id})}),[e]);return{onClickChiclet:t,onRemoveChiclet:n}}(n),{onFocus:f,onChangeSingleSuggestion:h,onSingleSelectKeyDown:g,onCloseSingleMenu:y,onMultipleSelectKeyDown:b,onCloseMultipleMenu:v}=function(e,t,n){const o=(0,r.useCallback)((()=>{e.send({type:"START_INPUT"})}),[e]),a=(0,r.useCallback)((t=>{const{value:n="",label:r=""}=t;e.send({type:"SELECT_SUGGESTION",data:{value:n,label:r}})}),[e]),i=(0,r.useCallback)((t=>{"Backspace"!==t.code||t.target.value||e.send({type:"REMOVE_LAST_FILTER"})}),[e]),s=(0,r.useCallback)((()=>{e.send({type:"DISCARD_SUGGESTIONS"})}),[e]),l=(0,r.useCallback)(((t,n)=>{"Backspace"!==t.code||t.target.value||n.length||e.send({type:"REMOVE_LAST_FILTER"})}),[e]),c=(0,r.useCallback)((t=>{t.length?e.send({type:"SELECT_SUGGESTION",data:{value:t.map((e=>e.value)).join("|"),label:t.map((e=>e.label)).join(", ")}}):e.send({type:"DISCARD_SUGGESTIONS"})}),[e]),u=function(e){const t=(0,r.useRef)();return(0,r.useEffect)((()=>{t.current=e})),t.current}(t.isVisible);return(0,r.useEffect)((()=>{var e;!t.isVisible&&u&&(null===(e=document.querySelector(`#${n} input`))||void 0===e||e.blur())}),[t.isVisible,u,n]),{onFocus:o,onChangeSingleSuggestion:a,onSingleSelectKeyDown:i,onMultipleSelectKeyDown:l,onCloseSingleMenu:s,onCloseMultipleMenu:c}}(n,d,e.id),E=(0,r.useCallback)((()=>{n.send({type:"EXECUTE_QUERY"})}),[n]);return(0,r.useEffect)((()=>{if(!e.autoExecute)return;const t=({value:e,context:t,event:r})=>{"idle"!==e||t.isQueryUpToDate||"EXECUTE_QUERY"===r.type||n.send({type:"EXECUTE_QUERY"})};return n.onTransition(t),()=>{n.off(t)}}),[n,e.autoExecute]),o().createElement("div",{id:e.id,className:(0,a.cx)(t.queryBuilder,e.className)},l.length>0?o().createElement(bt,{filters:l,onClickChiclet:p,onRemoveChiclet:m,edition:c,suggestions:d,onChangeSingleSuggestion:h,onCloseSingleSuggestionsMenu:y,onCloseMultipleSuggestionsMenu:v}):null,o().createElement("div",{className:t.controls},c?o().createElement(wt,null):d.multiple?o().createElement(Ot,{suggestions:d,onFocus:f,onKeyDown:b,onCloseMenu:v}):o().createElement(Et,{suggestions:d,onFocus:f,onChange:h,onKeyDown:g,onCloseMenu:y}),!e.autoExecute&&o().createElement(s.Button,{onClick:E,tooltip:u?"Nothing to execute, all filters applied":"Execute new query",className:t.executeButton,disabled:u},"Execute")))}const Ct=(0,r.memo)(Pt),kt=JSON.parse('{"block:contentions:count:contentions:count":{"id":"block:contentions:count:contentions:count","description":"Number of blocking contentions","type":"contentions","group":"block","unit":"short"},"block:delay:nanoseconds:contentions:count":{"id":"block:delay:nanoseconds:contentions:count","description":"Time spent in blocking delays","type":"delay","group":"block","unit":"ns"},"goroutine:goroutine:count:goroutine:count":{"id":"goroutine:goroutine:count:goroutine:count","description":"Number of goroutines","type":"goroutine","group":"goroutine","unit":"short"},"goroutines:goroutine:count:goroutine:count":{"id":"goroutines:goroutine:count:goroutine:count","description":"Number of goroutines","type":"goroutine","group":"goroutine","unit":"short"},"memory:alloc_in_new_tlab_bytes:bytes::":{"id":"memory:alloc_in_new_tlab_bytes:bytes::","description":"Size of memory allocated inside Thread-Local Allocation Buffers (TLAB)","type":"alloc_in_new_tlab_bytes","group":"memory","unit":"bytes"},"memory:alloc_in_new_tlab_objects:count::":{"id":"memory:alloc_in_new_tlab_objects:count::","description":"Number of objects allocated inside Thread-Local Allocation Buffers (TLAB)","type":"alloc_in_new_tlab_objects","group":"memory","unit":"short"},"memory:alloc_objects:count:space:bytes":{"id":"memory:alloc_objects:count:space:bytes","description":"Number of objects allocated","type":"alloc_objects","group":"memory","unit":"short"},"memory:alloc_space:bytes:space:bytes":{"id":"memory:alloc_space:bytes:space:bytes","description":"Size of memory allocated in the heap","type":"alloc_space","group":"memory","unit":"bytes"},"memory:inuse_objects:count:space:bytes":{"id":"memory:inuse_objects:count:space:bytes","description":"Number of objects currently in use","type":"inuse_objects","group":"memory","unit":"short"},"memory:inuse_space:bytes:space:bytes":{"id":"memory:inuse_space:bytes:space:bytes","description":"Size of memory currently in use","type":"inuse_space","group":"memory","unit":"bytes"},"mutex:contentions:count:contentions:count":{"id":"mutex:contentions:count:contentions:count","description":"Number of observed mutex contentions","type":"contentions","group":"mutex","unit":"short"},"mutex:delay:nanoseconds:contentions:count":{"id":"mutex:delay:nanoseconds:contentions:count","description":"Time spent waiting due to mutex contentions","type":"delay","group":"mutex","unit":"ns"},"process_cpu:alloc_samples:count:cpu:nanoseconds":{"id":"process_cpu:alloc_samples:count:cpu:nanoseconds","description":"Number of memory allocation samples during CPU time","type":"alloc_samples","group":"memory","unit":"short"},"process_cpu:alloc_size:bytes:cpu:nanoseconds":{"id":"process_cpu:alloc_size:bytes:cpu:nanoseconds","description":"Size of memory allocated during CPU time","type":"alloc_size","group":"alloc_size","unit":"bytes"},"process_cpu:cpu:nanoseconds:cpu:nanoseconds":{"id":"process_cpu:cpu:nanoseconds:cpu:nanoseconds","description":"CPU time consumed","type":"cpu","group":"process_cpu","unit":"ns"},"process_cpu:exception:count:cpu:nanoseconds":{"id":"process_cpu:exception:count:cpu:nanoseconds","description":"Number of exceptions within the sampled CPU time","type":"exceptions","group":"exceptions","unit":"short"},"process_cpu:lock_count:count:cpu:nanoseconds":{"id":"process_cpu:lock_count:count:cpu:nanoseconds","description":"Number of lock acquisitions attempted during CPU time","type":"lock_count","group":"locks","unit":"short"},"process_cpu:lock_time:nanoseconds:cpu:nanoseconds":{"id":"process_cpu:lock_time:nanoseconds:cpu:nanoseconds","description":"Cumulative time spent acquiring locks","type":"lock_time","group":"locks","unit":"ns"},"process_cpu:samples:count::milliseconds":{"id":"process_cpu:samples:count::milliseconds","description":"Number of process samples collected","type":"samples","group":"process_cpu","unit":"short"},"process_cpu:samples:count:cpu:nanoseconds":{"id":"process_cpu:samples:count:cpu:nanoseconds","description":"Number of samples collected over CPU time","type":"samples","group":"process_cpu","unit":"short"}}');function At(e){if(kt[e])return kt[e];const[t="?",n="?"]=e?e.split(":"):[];return{id:e,description:"",type:n,group:t,unit:"short"}}var jt=n(1269);const Nt=Object.freeze({type:"grafana-pyroscope-datasource",uid:"$dataSource"}),It=Object.freeze({type:"grafana-pyroscope-series-datasource",uid:"grafana-pyroscope-series-datasource"}),_t=Object.freeze({type:"grafana-pyroscope-favorites-datasource",uid:"grafana-pyroscope-favorites-datasource"}),Rt=Object.freeze({type:"grafana-pyroscope-labels-datasource",uid:"grafana-pyroscope-labels-datasource"});function Lt(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Dt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ft extends i.fS{onActivate(){this.state.value||this.setState({value:Ft.DEFAULT_VALUE})}update(e=!1){var t,n=this;return(t=function*(){if(!e&&n.state.loading)return;let t=[],r=null;n.setState({loading:!0,options:[],error:null});try{t=yield(0,jt.lastValueFrom)(n.getValueOptions({}))}catch(e){r=e}finally{n.setState({loading:!1,options:t,error:r})}},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){Lt(a,r,o,i,s,"next",e)}function s(e){Lt(a,r,o,i,s,"throw",e)}i(void 0)}))})()}static buildCascaderOptions(e){const t=new Map;for(const{value:n}of e){const e=At(n),{group:r,type:o}=e,a=t.get(r)||{value:r,label:r,items:[]},i=a.items||[];i.push({value:n,label:o}),a.items=i,t.set(r,a)}return Array.from(t.values()).sort(((e,t)=>Xe(t.label,e.label)))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Dt(e,t,n[t])}))}return e}({key:"profileMetricId",name:"profileMetricId",label:"Profile type",datasource:It,query:Ft.QUERY_DEFAULT,loading:!0,refresh:f.VariableRefresh.onTimeRangeChanged},e)),Dt(this,"onSelect",(e=>{(0,m.r)("g_pyroscope_app_profile_metric_selected"),this.state.skipUrlSync||c(),this.changeValueTo(e)})),this.changeValueTo=this.changeValueTo.bind(this),this.addActivationHandler(this.onActivate.bind(this))}}Dt(Ft,"DEFAULT_VALUE","process_cpu:cpu:nanoseconds:cpu:nanoseconds"),Dt(Ft,"QUERY_DEFAULT","$dataSource and all profile metrics"),Dt(Ft,"QUERY_SERVICE_NAME_DEPENDENT","$dataSource and only $serviceName profile metrics"),Dt(Ft,"Component",(({model:e})=>{const t=(0,s.useStyles2)($t),{loading:n,value:a,options:i,error:l}=e.useState(),c=(0,r.useMemo)((()=>Ft.buildCascaderOptions(i)),[i]);return l?o().createElement(s.Tooltip,{theme:"error",content:l.toString()},o().createElement(s.Icon,{className:t.iconError,name:"exclamation-triangle",size:"xl"})):o().createElement(s.Cascader,{key:(0,T.Ak)(5),"aria-label":"Profile metrics list",width:24,separator:"/",displayAllSelectedLevels:!0,placeholder:n?"Loading...":`Select a profile metric (${i.length})`,options:c,initialValue:a,changeOnSelect:!1,onSelect:e.onSelect})}));const $t=e=>({iconError:a.css`
    height: 32px;
    align-self: center;
    color: ${e.colors.error.text};
  `});var Bt=n(550);function Mt(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Ut(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Vt extends i.fS{onActivate(){const{serviceName:e}=Bt.x.get(Bt.x.KEYS.PROFILES_EXPLORER)||{};e&&!this.state.value&&this.setState({value:e}),this.subscribeToState(((e,t)=>{if(e.value&&e.value!==t.value){const t=Bt.x.get(Bt.x.KEYS.PROFILES_EXPLORER)||{};t.serviceName=e.value,Bt.x.set(Bt.x.KEYS.PROFILES_EXPLORER,t)}}))}update(){var e,t=this;return(e=function*(){if(t.state.loading)return;let e=[],n=null;t.setState({loading:!0,options:[],error:null});try{e=yield(0,jt.lastValueFrom)(t.getValueOptions({}))}catch(e){n=e}finally{t.setState({loading:!1,options:e,error:n})}},function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Mt(a,r,o,i,s,"next",e)}function s(e){Mt(a,r,o,i,s,"throw",e)}i(void 0)}))})()}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ut(e,t,n[t])}))}return e}({key:"serviceName",name:"serviceName",label:"Service",datasource:It,query:Vt.QUERY_DEFAULT,loading:!0,refresh:f.VariableRefresh.onTimeRangeChanged},e)),Ut(this,"selectNewValue",(e=>{(0,m.r)("g_pyroscope_app_service_name_selected"),this.state.skipUrlSync||c(),this.changeValueTo(e)})),this.addActivationHandler(this.onActivate.bind(this))}}Ut(Vt,"QUERY_DEFAULT","$dataSource and all services"),Ut(Vt,"QUERY_PROFILE_METRIC_DEPENDENT","$dataSource and only $profileMetricId services"),Ut(Vt,"Component",(({model:e})=>{const t=(0,s.useStyles2)(qt),{loading:n,value:a,options:i,error:l}=e.useState(),c=(0,r.useMemo)((()=>function(e){const t=[];for(const n of e){const e=n.split("/");let r;const o=[];let a=t;for(let t=0;t<e.length;t+=1){r=e[t],o.push(r);const n=o.join("/"),i=a.find((e=>e.value===n));if(i)a=i.items;else{const o={value:n,label:r,items:t<e.length-1?[]:void 0};a.push(o),a=o.items||[]}}}return t}(i.map((({label:e})=>e)))),[i]);return l?o().createElement(s.Tooltip,{theme:"error",content:l.toString()},o().createElement(s.Icon,{className:t.iconError,name:"exclamation-triangle",size:"xl"})):o().createElement(s.Cascader,{key:(0,T.Ak)(5),"aria-label":"Services list",width:32,separator:"/",displayAllSelectedLevels:!0,placeholder:n?"Loading services...":`Select a service (${i.length})`,options:c,initialValue:a,changeOnSelect:!1,onSelect:e.selectNewValue})}));const qt=e=>({iconError:a.css`
    height: 32px;
    align-self: center;
    color: ${e.colors.error.text};
  `});function Gt(e,t){const{value:n}=i.jh.findByKeyAndType(e,"serviceName",Vt).useState(),{value:o}=i.jh.findByKeyAndType(e,"profileMetricId",Ft).useState(),{filterExpression:a}=i.jh.findByKeyAndType(e,t,rn).useState();return(0,r.useMemo)((()=>`${o}{service_name="${n}",${a}}`),[a,o,n])}class Kt extends i.mI{onActivate(){this.setState({skipUrlSync:!1}),this.subscribeToState(((e,t)=>{if(e.value&&e.value!==t.value){const t=Bt.x.get(Bt.x.KEYS.PROFILES_EXPLORER)||{};t.dataSource=e.value,Bt.x.set(Bt.x.KEYS.PROFILES_EXPLORER,t)}}))}constructor(){super({pluginId:"grafana-pyroscope-datasource",key:"dataSource",name:"dataSource",label:"Data source",skipUrlSync:!0,value:F.O.selectDefaultDataSource().uid}),this.addActivationHandler(this.onActivate.bind(this))}}function Ht(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function zt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ht(e,t,n[t])}))}return e}function Yt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Qt=e=>{let t=e.operator.value;return e.operator.value===E.in?t=E["=~"]:e.operator.value===E["not-in"]&&(t=E["!~"]),{key:e.attribute.value,operator:t,value:e.value.value}};function Wt(e,t){let n;const r=e.filter((e=>e.key!==t||(n=e,!1)));return{found:n,filtersWithoutFound:r}}const Jt=(e,t)=>[...e,t];function Xt(e,t){const{found:n,filtersWithoutFound:r}=Wt(e,t.key);if(!n)return Jt(e,Yt(zt({},t),{operator:"=~"}));if(["!~","!="].includes(n.operator))return Jt(r,Yt(zt({},t),{operator:"=~"}));const o=new Set(n.value.split("|"));return"=~"===n.operator?Jt(r,Yt(zt({},n),{value:Array.from(o.add(t.value)).join("|")})):n.value===t.value?e:Jt(r,Yt(zt({},t),{operator:"=~",value:Array.from(o.add(t.value)).join("|")}))}function Zt(e,t){const{found:n,filtersWithoutFound:r}=Wt(e,t.key);if(!n)return Jt(e,Yt(zt({},t),{operator:"!~"}));if(["=~","="].includes(n.operator))return Jt(r,Yt(zt({},t),{operator:"!~"}));const o=new Set(n.value.split("|"));return"!~"===n.operator?Jt(r,Yt(zt({},n),{value:Array.from(o.add(t.value)).join("|")})):n.value===t.value?e:Jt(r,Yt(zt({},t),{operator:"!~",value:Array.from(o.add(t.value)).join("|")}))}function en(e,t){const{found:n,filtersWithoutFound:r}=Wt(e,t.key);if(!n)return e;const o=n.value.split("|").filter((e=>e!==t.value));return o.length>0?Jt(r,Yt(zt({},n),{value:o.join("|")})):[...r]}const tn=e=>e.operator in E;function nn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class rn extends i.H9{reset(){this.setState({filters:rn.DEFAULT_VALUE})}static resetAll(e){["filters","filtersBaseline","filtersComparison"].forEach((t=>{i.jh.findByKeyAndType(e,t,rn).reset()}))}onActivate(){const e=i.jh.findByKeyAndType(this,"dataSource",Kt).subscribeToState((()=>{this.reset()}));return()=>{e.unsubscribe()}}constructor({key:e}){super({key:e,name:e,label:"Filters",filters:rn.DEFAULT_VALUE,expressionBuilder:e=>e.filter(tn).map((({key:e,operator:t,value:n})=>t===E["is-empty"]?`${e}=""`:`${e}${t}"${n}"`)).join(",")}),nn(this,"onChangeQuery",((e,t)=>{(0,m.r)("g_pyroscope_app_filters_changed",{name:this.state.name,count:t.length,operators:(0,y.uniq)(t.map((e=>e.operator.label)))}),this.setState({filters:t.map(Qt)})})),this.addActivationHandler(this.onActivate.bind(this))}}function on(e,t){var n;return null===(n=i.jh.lookupVariable(t,e))||void 0===n?void 0:n.getValue()}function an(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}nn(rn,"DEFAULT_VALUE",[]),nn(rn,"Component",(({model:e})=>{const{key:t}=e.useState(),n=Gt(e,t),{value:r}=i.jh.findByKeyAndType(e,"dataSource",Kt).useState(),{from:a,to:s}=i.jh.getTimeRange(e).state.value;return o().createElement(Ct,{id:`query-builder-${t}`,autoExecute:!0,dataSourceUid:r,query:n,from:1e3*a.unix(),to:1e3*s.unix(),onChangeQuery:e.onChangeQuery})}));var sn=function(e){return e.TIMESERIES="time-series",e.BARGAUGE="bar-gauge",e.TABLE="table",e.HISTOGRAM="histogram",e}({});class ln extends i.Bs{getUrlState(){return{panelType:this.state.panelType}}updateFromUrl(e){const t={};"string"==typeof e.panelType&&e.panelType!==this.state.panelType&&(t.panelType=Object.values(sn).includes(e.panelType)?e.panelType:ln.DEFAULT_PANEL_TYPE),this.setState(t)}reset(){this.setState({panelType:ln.DEFAULT_PANEL_TYPE})}constructor(){super({key:"panel-type-switcher",panelType:ln.DEFAULT_PANEL_TYPE}),an(this,"_urlSync",new i.So(this,{keys:["panelType"]})),an(this,"onChange",(e=>{(0,m.r)("g_pyroscope_app_panel_type_changed",{panelType:e}),this.setState({panelType:e})}))}}an(ln,"OPTIONS",[{label:"Time series",value:"time-series",icon:"heart-rate"},{label:"Totals",value:"bar-gauge",icon:"align-left"},{label:"Maxima",value:"table",icon:"angle-double-up"},{label:"Histograms",value:"histogram",icon:"graph-bar"}]),an(ln,"DEFAULT_PANEL_TYPE","time-series"),an(ln,"Component",(({model:e})=>{const{panelType:t}=e.useState();return o().createElement(s.RadioButtonGroup,{"aria-label":"Panel type switcher",options:ln.OPTIONS,value:t,onChange:e.onChange,fullWidth:!1})}));var cn=n(3321);function un(e){const t=C.config.theme2.visualization;return t.getColorByName(t.palette[e%8])}const dn=(e,t)=>{var n;return(null===(n=e.labels)||void 0===n?void 0:n[t])||"(no value)"},pn=(e,t)=>{var n,r,o;return null===(o=e.meta)||void 0===o||null===(r=o.stats)||void 0===r||null===(n=r.find((e=>e.displayName===t)))||void 0===n?void 0:n.value};function mn(e){const[,t=""]=e.match(/.+\{.*service_name="([^"]+)".*\}/)||[],[,n=""]=e.match(/([^{]+)\{.*}/)||[],r=e.substring(e.indexOf("{")),o=r.replace(/(\{|\})/,"").split(",").map((e=>{var t;return null===(t=e.match(/\W*([^=!~]+)(=|!=|=~|!~)"(.*)"/))||void 0===t?void 0:t[0]})).filter((e=>e&&!e.includes("service_name")));return{serviceId:t,profileMetricId:n,labelsSelector:r,labels:o}}function fn(e){return e.addActivationHandler((()=>{const{profileTypeId:t,labelSelector:n}=e.state.queries[0];if(!t)return void e.setState({queries:[{refId:"null"}],data:hn(e,"Missing profile type!")});if(!n)return void e.setState({queries:[{refId:"null"}],data:hn(e,"Missing label selector!")});if(!i.jh.interpolate(e,"$profileMetricId"))return void e.setState({queries:[{refId:"null"}],data:hn(e,"Missing profile type!")});mn(i.jh.interpolate(e,`$profileTypeId${n})`)).serviceId||e.setState({queries:[{refId:"null"}],data:hn(e,"Missing service name!")})})),e}function hn(e,t){const n=new Error(t);return g.v.error(n),{state:f.LoadingState.Error,errors:[n],series:[],timeRange:i.jh.getTimeRange(e).state.value}}function gn({serviceName:e,profileMetricId:t,groupBy:n,filters:r},o){const a=r?[...r]:[];a.unshift({key:"service_name",operator:"=",value:e||"$serviceName"});const s=a.map((({key:e,operator:t,value:n})=>`${e}${t}"${n}"`)).join(",");return fn(new i.dt({datasource:Nt,queries:[{refId:`${t||"$profileMetricId"}-${s}-${(null==n?void 0:n.label)||"no-group-by"}`,queryType:"metrics",profileTypeId:t||"$profileMetricId",labelSelector:`{${s},$filters}`,groupBy:(null==n?void 0:n.label)?[n.label]:[],limit:o}]}))}const yn=()=>e=>e.pipe((0,jt.map)((e=>null==e?void 0:e.map(((e,t)=>(0,y.merge)(e,{refId:`${e.refId}-${t}`})))))),bn=()=>e=>e.pipe((0,jt.map)((e=>{const t=null==e?void 0:e.length;return null==e?void 0:e.map((e=>{var n,r;let o=Number.NEGATIVE_INFINITY;const a=null===(r=e.fields)||void 0===r||null===(n=r.find((e=>"number"===e.type)))||void 0===n?void 0:n.values.reduce(((e,t)=>(t>o&&(o=t),e+t)),0);return(0,y.merge)(e,{meta:{stats:[{displayName:"totalSeriesCount",value:t},{displayName:"allValuesSum",value:a},{displayName:"maxValue",value:o}]}})}))})));class vn extends i.Bs{onActivate(e){const{body:t}=this.state,n=t.state.$data.subscribeToState((n=>{var r;if((null===(r=n.data)||void 0===r?void 0:r.state)!==f.LoadingState.Done)return;const{series:o}=n.data;(null==o?void 0:o.length)&&t.setState(this.getConfig(e,o)),this.publishEvent(new b({series:o}),!0)}));return()=>{n.unsubscribe()}}getConfig(e,t){var n;let r=Number.NEGATIVE_INFINITY;for(const e of t){const t=pn(e,"allValuesSum")||0;t>r&&(r=t)}const o=null===(n=e.queryRunnerParams.groupBy)||void 0===n?void 0:n.label,a=o?"This panel displays aggregate values over the current time period":void 0;return{title:t.length>1?`${e.label} (${t.length})`:e.label,description:a,options:{reduceOptions:{values:!1,calcs:["sum"]},orientation:f.VizOrientation.Horizontal,displayMode:cn.eX.Gradient,valueMode:cn.$l.Text,showUnfilled:!0,sizing:cn.T6.Manual,text:{titleSize:13,valueSize:13},namePlacement:cn.TZ.Top,minVizHeight:36,maxVizHeight:36,legend:{showLegend:!1}},fieldConfig:{defaults:{displayName:1===t.length?o:void 0,min:0,max:r,thresholds:{mode:f.ThresholdsMode.Percentage,steps:[]}},overrides:this.getOverrides(e,t)}}}getOverrides(e,t){var n;const{index:r,queryRunnerParams:o}=e,a=null===(n=o.groupBy)||void 0===n?void 0:n.label;return t.map(((e,t)=>({matcher:{id:f.FieldMatcherID.byFrameRefID,options:e.refId},properties:[{id:"displayName",value:dn(e.fields[1],a)},{id:"color",value:{mode:"fixed",fixedColor:un(r+t)}}]})))}static Component({model:e}){const{body:t}=e.useState();return o().createElement(t.Component,{model:t})}constructor({item:e,headerActions:t}){super({key:"bar-gauge-label-values",body:i.d0.bargauge().setTitle(e.label).setData(new i.Es({$data:gn(e.queryRunnerParams),transformations:[yn,bn]})).setHeaderActions(t(e)).build()}),this.addActivationHandler(this.onActivate.bind(this,e))}}function En(e,t){const n=t.fields[1].config.unit,r=pn(t,"allValuesSum")||0,o=(0,f.getValueFormat)(n)(r),a=pn(t,"maxValue")||0,i=(0,f.getValueFormat)(n)(a);return`total ${e} = ${o.text}${o.suffix} / max = ${i.text}${i.suffix}`}class Sn extends i.Bs{onActivate(e){const{body:t}=this.state,n=t.state.$data.subscribeToState((n=>{var r;if((null===(r=n.data)||void 0===r?void 0:r.state)!==f.LoadingState.Done)return;const{series:o}=n.data;(null==o?void 0:o.length)&&t.setState(this.getConfig(e,o)),this.publishEvent(new b({series:o}),!0)}));return()=>{n.unsubscribe()}}getConfig(e,t){var n;const{legendPlacement:r}=this.state,o=null===(n=e.queryRunnerParams.groupBy)||void 0===n?void 0:n.label;return{title:t.length>1?`${e.label} (${t.length})`:e.label,options:{tooltip:{mode:s.TooltipDisplayMode.Single,sort:cn.xB.None},legend:{showLegend:!0,displayMode:s.LegendDisplayMode.List,placement:r,calcs:[]}},fieldConfig:{defaults:{displayName:1===t.length?o:void 0,custom:{lineWidth:1}},overrides:this.getOverrides(e,t)}}}getOverrides(e,t){var n;const{index:r,queryRunnerParams:o}=e,a=null===(n=o.groupBy)||void 0===n?void 0:n.label;return t.map(((e,n)=>{const o=e.fields[1];let i=a?dn(o,a):o.name;return 1===t.length&&(i=En(i,e)),{matcher:{id:f.FieldMatcherID.byFrameRefID,options:e.refId},properties:[{id:"displayName",value:i},{id:"color",value:{mode:"fixed",fixedColor:un(r+n)}}]}}))}static Component({model:e}){const{body:t}=e.useState();return o().createElement(t.Component,{model:t})}constructor({item:e,headerActions:t,legendPlacement:n}){super({key:"histogram-label-values",legendPlacement:n||"bottom",body:i.d0.histogram().setTitle(e.label).setData(new i.Es({$data:gn(e.queryRunnerParams),transformations:[yn,bn]})).setHeaderActions(t(e)).build()}),this.addActivationHandler(this.onActivate.bind(this,e))}}class wn extends i.Bs{onActivate(e){const{body:t}=this.state,n=t.state.$data.subscribeToState((n=>{var r;if((null===(r=n.data)||void 0===r?void 0:r.state)!==f.LoadingState.Done)return;const{series:o}=n.data;(null==o?void 0:o.length)&&t.setState(this.getConfig(e,o)),this.publishEvent(new b({series:o}),!0)}));return()=>{n.unsubscribe()}}getConfig(e,t){const n=t[0].fields[0].values.length,r=At(i.jh.findByKeyAndType(this,"profileMetricId",Ft).state.value).unit;return{title:n>1?`${e.label} (${n})`:e.label,fieldConfig:{defaults:{custom:{filterable:!0,cellOptions:{}}},overrides:[{matcher:{id:"byName",options:"max"},properties:[{id:"unit",value:r},{id:"custom.width",value:100}]}]}}}static Component({model:e}){const t=(0,s.useStyles2)(On),{body:n}=e.useState();return o().createElement("span",{className:t.container},o().createElement(n.Component,{model:n}))}constructor({item:e,headerActions:t}){super({key:"table-label-values",body:i.d0.table().setTitle(e.label).setData(new i.Es({$data:gn(e.queryRunnerParams),transformations:[{id:f.DataTransformerID.reduce,options:{reducers:["max"],labelsToFields:!0}},{id:f.DataTransformerID.filterFieldsByName,options:{exclude:{names:["Field"]}}},{id:f.DataTransformerID.renameByRegex,options:{regex:"Max",renamePattern:"max"}},{id:f.DataTransformerID.sortBy,options:{sort:[{field:"max",desc:!0}]}}]})).setHeaderActions(t(e)).build()}),this.addActivationHandler(this.onActivate.bind(this,e))}}const On=()=>({container:a.css`
    [data-testid='data-testid table body'] [role='row']:first-child {
      color: ${un(3)};
      font-weight: 500;
    }
  `});function xn(e){return{from:1e4*Math.floor((e.from.valueOf()||0)/1e4),to:1e4*Math.floor((e.to.valueOf()||0)/1e4)}}function Tn(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Pn(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Tn(a,r,o,i,s,"next",e)}function s(e){Tn(a,r,o,i,s,"throw",e)}i(void 0)}))}}function Cn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Cn(e,t,n[t])}))}return e}function An(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const jn=(0,n(8537).A)(20);class Nn extends i.UU{query(){return Pn((function*(){return{state:f.LoadingState.Done,data:[{name:"Labels",fields:[{name:"Label",type:f.FieldType.other,values:[],config:{}}],length:0}]}}))()}getParams(e){var t;const{scopedVars:n,range:r}=e,o=null==n||null===(t=n.__sceneObject)||void 0===t?void 0:t.value,a=i.jh.interpolate(o,"$dataSource"),s=i.jh.interpolate(o,"$serviceName"),l=i.jh.interpolate(o,"$profileMetricId"),c=`${l}{service_name="${s}"}`,{from:u,to:d}=xn(r);return{dataSourceUid:a,serviceName:s,profileMetricId:l,query:c,from:u,to:d}}fetchLabels(e,t,n,r,o){return Pn((function*(){K.setApiClient(new I({dataSourceUid:e}));try{return yield K.listLabels({query:t,from:n,to:r})}catch(e){throw g.v.error(e,{info:"Error while loading Pyroscope label names!",variableName:o||""}),e}}))()}fetchLabelValues(e,t,n,r,o,a){return Pn((function*(){let e;try{e=yield K.listLabelValues({query:t,from:n,to:r,label:o})}catch(e){g.v.error(e,{info:"Error while loading Pyroscope label values!",variableName:a||""})}const i=e?e.length:-1;return{value:{value:o,groupBy:{label:o,values:e||[]}},text:`${o} (${i>-1?i:"?"})`,count:i}}))()}metricFindQuery(e,t){var n=this;return Pn((function*(){var e,r,o;if(!(null===(r=t.scopedVars)||void 0===r||null===(e=r.__sceneObject)||void 0===e?void 0:e.value).isActive)return[];const{dataSourceUid:a,serviceName:i,profileMetricId:s,query:l,from:c,to:u}=n.getParams(t);if(!i||!s)return g.v.warn('LabelsDataSource: either serviceName="%s" and/or profileMetricId="%s" is empty! Discarding request.',i,s),[];const d=yield n.fetchLabels(a,l,c,u,null===(o=t.variable)||void 0===o?void 0:o.name),p=yield Promise.all(d.filter((({value:e})=>!Ee(e))).map((({value:e},r)=>jn((()=>{var o;return n.fetchLabelValues(r,l,c,u,e,null===(o=t.variable)||void 0===o?void 0:o.name)}))))),m=p.sort(((e,t)=>t.count-e.count)).map((({value:e,text:t},n)=>({value:JSON.stringify(An(kn({},e),{index:n})),text:t})));return[{value:"all",text:"All"},...m]}))()}testDatasource(){return Pn((function*(){return{status:"success",message:"OK"}}))()}constructor(){super(Rt.type,Rt.uid)}}Cn(Nn,"MAX_TIMESERIES_LABEL_VALUES",10);const In=n.p+"e6c722427cfa8715e19d.svg";function _n(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Rn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){_n(e,t,n[t])}))}return e}function Ln(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function Dn(e){const t=At(e);return`${t.type} (${t.group})`}function Fn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $n(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Fn(e,t,n[t])}))}return e}function Bn(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Mn=[{text:"Linear",scaleDistribution:{type:cn.L4.Linear}},{text:"Log2",scaleDistribution:{type:cn.L4.Log,log:2}}];class Un extends i.Bs{onActivate(){this.setState({items:this.buildMenuItems()})}buildMenuItems(e){const{items:t,scaleType:n}=this.state,r=[{text:"Scale type",type:"group",subMenu:Mn.map((e=>({text:`${n===e.scaleDistribution.type?"✔ ":""}${e.text}`,onClick:()=>this.onClickScaleOption(e)})))},{type:"divider",text:""},{iconClassName:"compass",text:"Open in Explore",onClick:()=>this.onClickExplore()}];if(e)r.push({iconClassName:"plus-square",text:"Add to investigation (beta)",onClick:()=>{e.onClick()}});else{const e=null==t?void 0:t.find((e=>e.text.includes("Add to investigation")));e&&r.push($n({},e))}return r}onClickScaleOption(e){const{scaleDistribution:t,text:n}=e;(0,C.reportInteraction)("g_pyroscope_app_timeseries_scale_changed",{scale:t.type});i.jh.getAncestor(this,Vn).changeScale(t,n),this.setState({scaleType:t.type,items:this.buildMenuItems()})}onClickExplore(){(0,C.reportInteraction)("g_pyroscope_app_open_in_explore_clicked");const e=function(e,t,n){const r=JSON.stringify({"pyroscope-explore":{range:(0,f.toURLRange)(e),queries:[Ln(Rn({},t),{datasource:n})],panelsState:{},datasource:n}});var o;const a=null!==(o=C.config.appSubUrl)&&void 0!==o?o:"";return f.urlUtil.renderUrl(`${a}/explore`,{panes:r,schemaVersion:1})}(i.jh.getTimeRange(this).state.value.raw,this.getInterpolatedQuery(),i.jh.interpolate(this,"${dataSource}"));window.open(e,"_blank")}getInterpolatedQuery(){var e;const t=null===(e=i.jh.getAncestor(this,Vn).state.body.state.$data)||void 0===e?void 0:e.state.$data,n=null==t?void 0:t.state.queries[0];return Object.entries(n).map((([e,t])=>[e,"string"==typeof t?i.jh.interpolate(this,t):t])).reduce(((e,[t,n])=>Bn($n({},e),{[t]:n})),{})}useGetInvestigationPluginLinkContext(){const{refId:e,queryType:t,profileTypeId:n,labelSelector:o,groupBy:a}=this.getInterpolatedQuery(),s=mn(`${n}${o}`),l=[s.serviceId,Dn(s.profileMetricId)];(null==a?void 0:a.length)&&l.push(a[0]),s.labels.length&&l.push(s.labels.join(", "));const c=l.join(" · "),u=i.jh.interpolate(this,"${dataSource}"),d=i.jh.getTimeRange(this).state.value;return(0,r.useMemo)((()=>({id:(0,T.Ak)(),origin:"Explore Profiles",url:window.location.href,logoPath:In,title:c,type:"timeseries",timeRange:$n({},d),queries:[{refId:e,queryType:t,profileTypeId:n,labelSelector:o,groupBy:a}],datasource:u})),[u,a,o,n,t,e,d,c])}useUpdateMenuItems(){const e=function({extensionPointId:e,context:t,pluginId:n}){const r=(0,C.usePluginLinks)({extensionPointId:e,context:t}),[o]=r.links.filter((e=>e.pluginId===n));return o}({extensionPointId:"grafana-pyroscope-app/exploration/v1",context:this.useGetInvestigationPluginLinkContext(),pluginId:"grafana-explorations-app"});(0,r.useEffect)((()=>{e&&this.setState({items:this.buildMenuItems(e)})}),[e])}static Component({model:e}){return e.useUpdateMenuItems(),o().createElement(i.Lw.Component,{model:e})}constructor(e){super($n({scaleType:cn.L4.Linear},e)),this.addActivationHandler(this.onActivate.bind(this))}}class Vn extends i.Bs{onActivate(){const{body:e}=this.state,t=e.state.$data.subscribeToState(((t,n)=>{var r,o,a,i;if((null===(r=t.data)||void 0===r?void 0:r.state)!==f.LoadingState.Done)return;!(null===(o=t.data.annotations)||void 0===o?void 0:o.length)&&(null===(i=n.data)||void 0===i||null===(a=i.annotations)||void 0===a?void 0:a.length)&&(t.data.annotations=n.data.annotations);const{series:s}=t.data;if(null==s?void 0:s.length){const t=this.state.displayAllValues?this.getAllValuesConfig(s):this.getConfig(s);e.setState((0,y.merge)({},e.state,t))}this.publishEvent(new b({series:s}),!0)}));return()=>{t.unsubscribe()}}getConfig(e){var t;const{body:n,item:r,legendPlacement:o}=this.state;let a,{title:i}=n.state;return(null===(t=r.queryRunnerParams.groupBy)||void 0===t?void 0:t.label)&&(i=e.length>1?`${r.label} (${e.length})`:r.label,a=this.buildDescription(r.queryRunnerParams.groupBy)),{title:i,description:a,options:{tooltip:{mode:"single",sort:"none"},legend:{showLegend:!0,displayMode:"list",placement:o}},fieldConfig:{defaults:{min:0,custom:{fillOpacity:e.length>=Nn.MAX_TIMESERIES_LABEL_VALUES?0:9,gradientMode:1===e.length?cn.on.None:cn.on.Opacity,pointSize:3}},overrides:this.getOverrides(e)}}}buildDescription(e){return e?e.values?e.values.length>Nn.MAX_TIMESERIES_LABEL_VALUES?`Showing only ${Nn.MAX_TIMESERIES_LABEL_VALUES} out of ~${e.values.length} series to preserve readability. To view all the series for the current filters, click on the expand icon on this panel.`:"":`Showing only ${Nn.MAX_TIMESERIES_LABEL_VALUES} series to preserve readability. To view all the series, click on the expand icon on this panel.`:""}getAllValuesConfig(e){const{legendPlacement:t}=this.state;return{options:{tooltip:{mode:s.TooltipDisplayMode.Single,sort:cn.xB.None},legend:{showLegend:!0,displayMode:s.LegendDisplayMode.List,placement:t,calcs:[]}},fieldConfig:{defaults:{min:0,custom:{fillOpacity:0,pointSize:5}},overrides:this.getOverrides(e)}}}getOverrides(e){var t;if(this.state.overrides)return this.state.overrides(e);const{item:n}=this.state,r=null===(t=n.queryRunnerParams.groupBy)||void 0===t?void 0:t.label;return e.map(((t,o)=>{const a=t.fields[1];let i=r?dn(a,r):a.name;return 1===e.length&&(i=En(i,t)),{matcher:{id:f.FieldMatcherID.byFrameRefID,options:t.refId},properties:[{id:"displayName",value:i},{id:"color",value:{mode:"fixed",fixedColor:un(n.index+o)}}]}}))}updateItem(e){var t,n;const{item:r,headerActions:o,body:a}=this.state,i=(0,y.merge)({},r,e);if((null===(t=e.queryRunnerParams)||void 0===t?void 0:t.hasOwnProperty("groupBy"))&&(void 0===e.queryRunnerParams.groupBy?delete i.queryRunnerParams.groupBy:i.queryRunnerParams.groupBy=e.queryRunnerParams.groupBy),(null===(n=e.queryRunnerParams)||void 0===n?void 0:n.hasOwnProperty("filters"))&&void 0===e.queryRunnerParams.filters&&delete i.queryRunnerParams.filters,this.setState({item:i}),a.setState({title:e.label,description:this.buildDescription(i.queryRunnerParams.groupBy),headerActions:o(i)}),!(0,y.isEqual)(r.queryRunnerParams,i.queryRunnerParams)){var s;const{queries:e}=gn(i.queryRunnerParams,Nn.MAX_TIMESERIES_LABEL_VALUES).state,t=null===(s=a.state.$data)||void 0===s?void 0:s.state.$data;null==t||t.setState({queries:e}),null==t||t.runQueries()}}changeScale(e,t){const{body:n}=this.state;n.clearFieldConfigCache(),n.setState({fieldConfig:(0,y.merge)({},n.state.fieldConfig,{defaults:{custom:{scaleDistribution:e,axisLabel:e.type!==cn.L4.Linear?t:""}}})})}static Component({model:e}){const{body:t}=e.useState();return o().createElement(t.Component,{model:t})}constructor({item:e,headerActions:t,displayAllValues:n,legendPlacement:r,data:o,overrides:a}){super({key:"timeseries-label-values",item:e,headerActions:t,displayAllValues:Boolean(n),legendPlacement:r||"bottom",overrides:a,body:i.d0.timeseries().setTitle(e.label).setData(o||new i.Es({$data:gn(e.queryRunnerParams,n?void 0:Nn.MAX_TIMESERIES_LABEL_VALUES),transformations:[yn,bn]})).setHeaderActions(t(e)).setMenu(new Un({})).build()}),this.addActivationHandler(this.onActivate.bind(this))}}function qn(e,t){switch(e){case sn.BARGAUGE:return new vn(t);case sn.TABLE:return new wn(t);case sn.HISTOGRAM:return new Sn(t);case sn.TIMESERIES:default:return new Vn(t)}}var Gn=n(9488);const Kn=n.p+"944c737f589d02ecf603.svg",Hn=n.p+"e79edcfbe2068fae2364.svg",zn=(e=50)=>{const[t,n]=(0,r.useState)({x:null,y:null});return(0,r.useEffect)((()=>{const t=(0,y.throttle)((e=>{n({x:e.clientX,y:e.clientY})}),e);return window.addEventListener("mousemove",t),()=>{window.removeEventListener("mousemove",t)}}),[e]),t},Yn=({width:e="auto",height:t,show404:n=!1})=>{const r=(0,s.useTheme2)(),{x:a,y:i}=zn(),l=(0,s.useStyles2)(Qn,a,i,n);return o().createElement(Gn.A,{src:r.isDark?Kn:Hn,className:l.svg,height:t,width:e})};Yn.displayName="GrotNotFound";const Qn=(e,t,n,r)=>{const{innerWidth:o,innerHeight:i}=window,s=n&&n/i,l=t&&t/o,c=null!==s?Wn(s,-20,5):0,u=null!==l?Wn(l,-5,5):0;return{svg:(0,a.css)({"#grot-404-arm, #grot-404-magnifier":{transform:`rotate(${c}deg) translateX(${u}%)`,transformOrigin:"center",transition:"transform 50ms linear"},"#grot-404-text":{display:r?"block":"none"}})}},Wn=(e,t,n)=>e*(n-t)+t,Jn=({message:e})=>{const t=(0,s.useStyles2)(Xn);return o().createElement("div",{className:t.container},o().createElement(s.Box,{paddingY:8},o().createElement(s.Stack,{direction:"column",alignItems:"center",gap:3},o().createElement(Yn,{width:300}),o().createElement(s.Text,{variant:"h5"},e))))};function Xn(){return{container:(0,a.css)({width:"100%",display:"flex",justifyContent:"space-evenly",flexDirection:"column"})}}Jn.displayName="EmptyState";class Zn extends i.Bs{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Zn,"Component",(({model:e})=>{const{message:t}=e.useState();return o().createElement(Jn,{message:t})}));class er extends i.Bs{}function tr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(er,"Component",(({model:e})=>{const{message:t}=e.useState();return o().createElement(s.Alert,{title:"Query error!",severity:"error"},t)}));var nr=function(e){return e.GRID="grid",e.ROWS="rows",e}({});class rr extends i.Bs{getUrlState(){return{layout:this.state.layout}}updateFromUrl(e){const t={};"string"==typeof e.layout&&e.layout!==this.state.layout&&(t.layout=Object.values(nr).includes(e.layout)?e.layout:rr.DEFAULT_LAYOUT),this.setState(t)}constructor(){super({key:"layout-switcher",layout:rr.DEFAULT_LAYOUT}),tr(this,"_urlSync",new i.So(this,{keys:["layout"]})),tr(this,"onChange",(e=>{(0,m.r)("g_pyroscope_app_layout_changed",{layout:e}),this.setState({layout:e})}))}}function or(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}tr(rr,"OPTIONS",[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]),tr(rr,"DEFAULT_LAYOUT","grid"),tr(rr,"Component",(({model:e})=>{const{layout:t}=e.useState();return o().createElement(s.RadioButtonGroup,{"aria-label":"Layout switcher",options:rr.OPTIONS,value:t,onChange:e.onChange,fullWidth:!1})}));class ar extends i.Bs{getUrlState(){return{hideNoData:this.state.hideNoData}}updateFromUrl(e){const t={};"string"==typeof e.hideNoData&&e.hideNoData!==this.state.hideNoData&&(t.hideNoData=["on","off"].includes(e.hideNoData)?e.hideNoData:ar.DEFAULT_VALUE),this.setState(t)}constructor(){super({key:"no-data-switcher",hideNoData:ar.DEFAULT_VALUE}),or(this,"_urlSync",new i.So(this,{keys:["hideNoData"]})),or(this,"onChange",(e=>{(0,m.r)("g_pyroscope_app_hide_no_data_changed",{hideNoData:e}),this.setState({hideNoData:e})}))}}function ir(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}or(ar,"DEFAULT_VALUE","off"),or(ar,"Component",(({model:e})=>{const{hideNoData:t}=e.useState();return o().createElement(s.InlineSwitch,{"data-testid":"noDataSwitcher",showLabel:!0,label:"Hide panels without data",value:"on"===t,onChange:t=>e.onChange(t.target.checked?"on":"off")})}));class sr extends i.Bs{setPlaceholder(e){this.setState({placeholder:e})}setResultsCount(e){this.setState({resultsCount:String(e)})}getUrlState(){return{searchText:this.state.searchText}}updateFromUrl(e){const t={};"string"==typeof e.searchText&&e.searchText!==this.state.searchText&&(t.searchText=e.searchText),this.setState(t)}reset(){this.setState({placeholder:"",searchText:"",resultsCount:""})}constructor({placeholder:e}){super({key:"quick-filter",placeholder:e,searchText:sr.DEFAULT_SEARCH_TEXT,resultsCount:""}),ir(this,"_urlSync",new i.So(this,{keys:["searchText"]})),ir(this,"onChange",(e=>{this.setState({searchText:e.target.value})})),ir(this,"clearSearchText",(()=>{this.setState({searchText:""})})),ir(this,"onFocus",(()=>{(0,m.r)("g_pyroscope_app_quick_filter_focused")}))}}ir(sr,"DEFAULT_SEARCH_TEXT",""),ir(sr,"DEBOUNCE_DELAY",250),ir(sr,"Component",(({model:e})=>{const t=(0,s.useStyles2)(lr),{placeholder:n,searchText:r,resultsCount:a}=e.useState();return o().createElement(s.Input,{type:"text",className:"quick-filter","aria-label":"Quick filter",placeholder:n,value:r,prefix:o().createElement(s.Icon,{name:"search"}),suffix:o().createElement(o().Fragment,null,""!==a&&o().createElement(s.Tag,{className:t.resultsCount,name:a,colorIndex:9,"data-testid":"quick-filter-results-count"}),o().createElement(s.IconButton,{name:"times","aria-label":"Clear search",onClick:e.clearSearchText})),onChange:e.onChange,onKeyDown:t=>{"Escape"===t.key&&e.clearSearchText()},onFocus:e.onFocus})}));const lr=e=>({resultsCount:a.css`
    margin-right: ${e.spacing(1)};
    border-radius: 11px;
    padding: 2px 8px;
    color: ${e.colors.text.primary};
    background-color: ${e.colors.background.secondary};
  `});function cr(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function ur(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){cr(a,r,o,i,s,"next",e)}function s(e){cr(a,r,o,i,s,"throw",e)}i(void 0)}))}}function dr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function pr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){dr(e,t,n[t])}))}return e}class mr extends i.UU{static getAllFavorites(){var e;return(null===(e=Bt.x.get(Bt.x.KEYS.PROFILES_EXPLORER))||void 0===e?void 0:e.favorites)||[]}static areFavoritesEqual(e,t){return e.panelType===t.panelType&&(0,y.isEqual)(e.queryRunnerParams,t.queryRunnerParams)}static exists(e){return mr.getAllFavorites().some((t=>mr.areFavoritesEqual(t,e)))}static addFavorite(e){const t=Bt.x.get(Bt.x.KEYS.PROFILES_EXPLORER);t.favorites.push(e),Bt.x.set(Bt.x.KEYS.PROFILES_EXPLORER,t)}static removeFavorite(e){const t=Bt.x.get(Bt.x.KEYS.PROFILES_EXPLORER);t.favorites=t.favorites.filter((t=>!mr.areFavoritesEqual(t,e))),Bt.x.set(Bt.x.KEYS.PROFILES_EXPLORER,t)}query(){return ur((function*(){return{state:f.LoadingState.Done,data:[{name:"Favories",fields:[{name:null,type:f.FieldType.other,values:[],config:{}}],length:0}]}}))()}metricFindQuery(){return ur((function*(){return mr.getAllFavorites().map((e=>{const{serviceName:t,profileMetricId:n,groupBy:r,filters:o}=e.queryRunnerParams||{},a=[t,Dn(n)];return(null==r?void 0:r.label)&&a.push(r.label),(null==o?void 0:o.length)&&a.push(o.map((({key:e,operator:t,value:n})=>`${e}${t}"${n}"`)).join(", ")),{value:JSON.stringify(pr({value:JSON.stringify(e)},e)),text:a.join(" · ")}}))}))()}testDatasource(){return ur((function*(){return{status:"success",message:"OK"}}))()}constructor(){var e;super(_t.type,_t.uid);const t=Bt.x.get(Bt.x.KEYS.PROFILES_EXPLORER)||{};(e=t).favorites||(e.favorites=[]),t.favorites=t.favorites.map((e=>pr({panelType:sn.TIMESERIES},e))),Bt.x.set(Bt.x.KEYS.PROFILES_EXPLORER,t)}}function fr(e,t){const{queryRunnerParams:n}=t,r=(0,y.defaults)((0,y.clone)(n),{serviceName:on(e,"serviceName"),profileMetricId:on(e,"profileMetricId")}),o=i.jh.lookupVariable("filters",e).state.filters.map((({key:e,operator:t,value:n})=>({key:e,operator:t,value:n})));return r.filters=(0,y.uniqBy)([...r.filters||[],...o],(({key:e,operator:t,value:n})=>`${e}${t}${n}`)),r}function hr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class gr extends i.Bs{update(){this.setState({isFav:this.isStored()})}isStored(){return mr.exists(this.buildFavorite())}static buildFavorite(e){var t;const{index:n,queryRunnerParams:r,panelType:o}=e,a={index:n,queryRunnerParams:{serviceName:r.serviceName,profileMetricId:r.profileMetricId},panelType:o};return r.groupBy&&(a.queryRunnerParams.groupBy={label:r.groupBy.label}),(null===(t=r.filters)||void 0===t?void 0:t.length)&&(a.queryRunnerParams.filters=r.filters),a}buildFavorite(){const{item:e,skipVariablesInterpolation:t}=this.state;return gr.buildFavorite({index:e.index,queryRunnerParams:t?e.queryRunnerParams:fr(this,e),panelType:e.panelType})}constructor(e){super(e),hr(this,"_variableDependency",new i.Sh(this,{variableNames:["serviceName","profileMetricId","filters"],onReferencedVariableValueChanged:()=>{this.update()}})),hr(this,"onClick",(()=>{(0,m.r)("g_pyroscope_app_fav_action_clicked",{favAfterClick:!this.state.isFav}),this.state.isFav?mr.removeFavorite(this.buildFavorite()):mr.addFavorite(this.buildFavorite()),this.setState({isFav:!this.state.isFav})})),this.addActivationHandler((()=>this.update()))}}hr(gr,"Component",(({model:e})=>{const t=(0,s.useStyles2)(yr),{isFav:n}=e.useState();return o().createElement(s.IconButton,{className:n?t.favedButton:t.notFavedbutton,name:n?"favorite":"star",variant:"secondary",size:"sm","aria-label":n?"Unfavorite":"Favorite",tooltip:n?"Unfavorite":"Favorite",tooltipPlacement:"top",onClick:e.onClick})}));const yr=()=>({favedButton:a.css`
    color: #f2cc0d;
    margin: 0;
  `,notFavedbutton:a.css`
    margin: 0;
  `}),br=function(e,t){const n=mr.exists(gr.buildFavorite(e)),r=mr.exists(gr.buildFavorite(t));return n&&r?Xe(e.label,t.label):r?1:n?-1:0},vr="240px";class Er extends i.Bs{static buildGridItemKey(e){return`grid-item-${e.index}-${e.value}`}static getGridColumnsTemplate(e){return e===nr.ROWS?"1fr":"repeat(auto-fit, minmax(400px, 1fr))"}onActivate(){const e=i.jh.lookupVariable(this.state.variableName,this),t=e.subscribeToState(((e,t)=>{!e.loading&&t.loading&&this.renderGridItems()}));e.update();const n=this.subscribeToRefreshClick(),r=this.subscribeToQuickFilterChange(),o=this.subscribeToLayoutChange(),a=this.subscribeToHideNoDataChange(),s=this.subscribeToFiltersChange();return()=>{s.unsubscribe(),a.unsubscribe(),o.unsubscribe(),r.unsubscribe(),n.unsubscribe(),t.unsubscribe()}}subscribeToRefreshClick(){const e=i.jh.lookupVariable(this.state.variableName,this),t=e.state.refresh;e.setState({refresh:f.VariableRefresh.never});const n=()=>{e.update()},r=document.querySelector('[data-testid="data-testid RefreshPicker run button"]');return r||g.v.error(new Error("SceneByVariableRepeaterGrid: Refresh button not found! The list of items will never be updated.")),null==r||r.addEventListener("click",n),null==r||r.setAttribute("title","Click to completely refresh all the panels present on the screen"),{unsubscribe(){null==r||r.removeAttribute("title"),null==r||r.removeEventListener("click",n),e.setState({refresh:t})}}}subscribeToQuickFilterChange(){const e=i.jh.findByKeyAndType(this,"quick-filter",sr);this.subscribeToState(((t,n)=>{t.items.length!==n.items.length&&e.setResultsCount(t.items.length)}));return e.subscribeToState((0,y.debounce)(((e,t)=>{e.searchText!==(null==t?void 0:t.searchText)&&this.renderGridItems()}),sr.DEBOUNCE_DELAY))}subscribeToLayoutChange(){const e=i.jh.findByKeyAndType(this,"layout-switcher",rr),t=this.state.body,n=(e,n)=>{e.layout!==(null==n?void 0:n.layout)&&t.setState({templateColumns:Er.getGridColumnsTemplate(e.layout)})};return n(e.state),e.subscribeToState(n)}subscribeToHideNoDataChange(){const e=i.jh.findByKeyAndType(this,"no-data-switcher",ar);if(!e.isActive)return this.setState({hideNoData:!1}),{unsubscribe:h.f};const t=(e,t)=>{e.hideNoData!==(null==t?void 0:t.hideNoData)&&(this.setState({hideNoData:"on"===e.hideNoData}),this.renderGridItems(!0))};return t(e.state),e.subscribeToState(t)}subscribeToFiltersChange(){const e=i.jh.findByKeyAndType(this,"filters",rn),t=i.jh.findByKeyAndType(this,"no-data-switcher",ar);return e.subscribeToState((()=>{"on"===t.state.hideNoData&&this.renderGridItems(!0)}))}buildItemsData(e){const{mapOptionToItem:t}=this.state,n={serviceName:on(this,"serviceName"),profileMetricId:on(this,"profileMetricId"),panelType:i.jh.findByKeyAndType(this,"panel-type-switcher",ln).state.panelType},r=e.state.options.map(((e,r)=>t(e,r,n))).filter(Boolean);return this.filterItems(r).sort(this.state.sortItemsFn)}shouldRenderItems(e){const{items:t}=this.state;return!e.length||t.length!==e.length||!(0,y.isEqual)(t,e)}renderGridItems(e=!1){const t=i.jh.lookupVariable(this.state.variableName,this);if(t.state.loading)return;if(t.state.error)return void this.renderErrorState(t.state.error);const n=this.buildItemsData(t);if(!e&&!this.shouldRenderItems(n))return;if(this.setState({items:n}),!this.state.items.length)return void this.renderEmptyState();const r=this.state.items.map((e=>{const t=qn(e.panelType,{item:e,headerActions:this.state.headerActions.bind(null,e,this.state.items)});return this.state.hideNoData&&this.setupHideNoData(t),new i.xK({key:Er.buildGridItemKey(e),body:t})}));this.state.body.setState({autoRows:vr,children:r})}setupHideNoData(e){const t=e.subscribeToEvent(b,(t=>{var n;if(null===(n=t.payload.series)||void 0===n?void 0:n.length)return;const r=i.jh.getAncestor(e,i.xK),{key:o}=r.state,a=i.jh.getAncestor(r,i.gF),s=a.state.children.filter((e=>e.state.key!==o));s.length?a.setState({children:s}):this.renderEmptyState()}));e.addActivationHandler((()=>()=>{t.unsubscribe()}))}filterItems(e){const t=i.jh.findByKeyAndType(this,"quick-filter",sr),{searchText:n}=t.state;if(!n)return e;const r=n.split(",").map((e=>e.trim())).filter(Boolean).map((e=>{try{return new RegExp(e)}catch(e){return null}})).filter(Boolean);return e.filter((({label:e})=>r.some((t=>t.test(e)))))}renderEmptyState(){this.state.body.setState({autoRows:"480px",children:[new i.xK({body:new Zn({message:"No results"})})]})}renderErrorState(e){this.state.body.setState({autoRows:"480px",children:[new i.xK({body:new er({message:e.message||e.toString()})})]})}static Component({model:e}){var t;const{body:n,variableName:r}=e.useState(),{loading:a}=null===(t=i.jh.lookupVariable(r,e))||void 0===t?void 0:t.useState();return a?o().createElement(s.Spinner,null):o().createElement(n.Component,{model:n})}constructor({key:e,variableName:t,headerActions:n,mapOptionToItem:r,sortItemsFn:o}){super({key:e,variableName:t,items:[],headerActions:n,mapOptionToItem:r,sortItemsFn:o||br,hideNoData:!1,body:new i.gF({templateColumns:Er.getGridColumnsTemplate(rr.DEFAULT_LAYOUT),autoRows:vr,isLazy:!0,$behaviors:[new i.Gg.K2({key:"metricCrosshairSync",sync:f.DashboardCursorSync.Crosshair})],children:[]})}),this.addActivationHandler(this.onActivate.bind(this))}}class Sr extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Sr,"type","expand-panel");class wr extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(wr,"type","select-label");class Or extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Or,"type","view-service-flame-graph");class xr extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(xr,"type","view-service-labels");class Tr extends f.BusEventWithPayload{}function Pr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Cr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Pr(e,t,n[t])}))}return e}function kr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Tr,"type","view-service-profiles");const Ar=new Map([["expand-panel",Object.freeze({ariaLabel:"Expand panel",icon:"expand-arrows",tooltip:()=>"Expand this panel to view all the data for the current filters",EventConstructor:Sr})],["select-label",Object.freeze({label:"Select",tooltip:({queryRunnerParams:e})=>{var t;return`View "${null===(t=e.groupBy)||void 0===t?void 0:t.label}" values breakdown`},EventConstructor:wr})],["view-flame-graph",Object.freeze({label:"Flame graph",tooltip:({queryRunnerParams:e},t)=>{const n=e.serviceName||on(t,"serviceName");return`View the "${At(e.profileMetricId||on(t,"profileMetricId")).type}" flame graph of ${n}`},EventConstructor:Or})],["view-labels",Object.freeze({label:"Labels",tooltip:({queryRunnerParams:e},t)=>`Explore the labels of ${e.serviceName||on(t,"serviceName")}`,EventConstructor:xr})],["view-profiles",Object.freeze({label:"Profile types",tooltip:({queryRunnerParams:e},t)=>`View the profile types of ${e.serviceName||on(t,"serviceName")}`,EventConstructor:Tr})]]);class jr extends i.Bs{buildEvent(){const{EventConstructor:e,item:t,skipVariablesInterpolation:n}=this.state;return new e({item:kr(Cr({},t),{queryRunnerParams:n?t.queryRunnerParams:fr(this,t)})})}constructor({type:e,item:t,tooltip:n,skipVariablesInterpolation:r}){const o=Ar.get(e);if(!o)throw new TypeError(`Unknown event type="${e}"!`);super(Cr({type:e,item:t},(0,y.merge)({},o,{tooltip:n,skipVariablesInterpolation:r}))),Pr(this,"onClick",(()=>{(0,m.r)("g_pyroscope_app_select_action_clicked",{type:this.state.type}),this.publishEvent(this.buildEvent(),!0)}))}}Pr(jr,"Component",(({model:e})=>{const t=(0,s.useStyles2)(Nr),{ariaLabel:n,label:r,icon:a,tooltip:i,item:l}=e.useState();return o().createElement(s.Button,{className:t.selectButton,"aria-label":n||r,variant:"primary",size:"sm",fill:"text",onClick:e.onClick,icon:a,tooltip:null==i?void 0:i(l,e),tooltipPlacement:"top"},r)}));const Nr=()=>({selectButton:a.css`
    margin: 0;
    padding: 0;
  `});class Ir extends i.Bs{onActivate(){i.jh.findByKeyAndType(this,"quick-filter",sr).setPlaceholder("Search services (comma-separated regexes are supported)")}getVariablesAndGridControls(){return{variables:[i.jh.findByKeyAndType(this,"profileMetricId",Ft)],gridControls:[i.jh.findByKeyAndType(this,"quick-filter",sr),i.jh.findByKeyAndType(this,"layout-switcher",rr)]}}static Component({model:e}){const{body:t}=e.useState();return o().createElement(t.Component,{model:t})}constructor(){super({key:"explore-all-services",$variables:new i.Pj({variables:[new Vt({query:Vt.QUERY_PROFILE_METRIC_DEPENDENT,skipUrlSync:!0})]}),body:new Er({key:"all-services-grid",variableName:"serviceName",mapOptionToItem:(e,t,{profileMetricId:n})=>({index:t,value:e.value,label:e.label,queryRunnerParams:{serviceName:e.value,profileMetricId:n},panelType:sn.TIMESERIES}),headerActions:e=>[new jr({type:"view-profiles",item:e}),new jr({type:"view-labels",item:e}),new jr({type:"view-flame-graph",item:e}),new gr({item:e})]})}),this.addActivationHandler(this.onActivate.bind(this))}}function _r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Rr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){_r(e,t,n[t])}))}return e}function Lr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class Dr extends i.Bs{constructor(e){super(Rr({key:"drawer",isOpen:!1},e)),_r(this,"open",(({title:e,subTitle:t,body:n})=>{this.setState(Lr(Rr({},this.state),{isOpen:!0,title:e,subTitle:t,body:n}))})),_r(this,"close",(()=>{this.setState({isOpen:!1})}))}}function Fr(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}_r(Dr,"Component",(({model:e})=>{const{isOpen:t,title:n,subTitle:r,body:a}=e.useState();return o().createElement(o().Fragment,null,a&&t&&o().createElement(s.Drawer,{size:"lg",title:n,subtitle:r,closeOnMaskClick:!0,onClose:e.close},o().createElement(a.Component,{model:a})))}));class $r extends i.fS{update(){var e,t=this;return(e=function*(){if(t.state.loading)return;let e=[],n=null;t.setState({loading:!0,options:[],error:null});try{e=yield(0,jt.lastValueFrom)(t.getValueOptions({}))}catch(e){n=e}finally{t.setState({loading:!1,options:e,error:n})}},function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Fr(a,r,o,i,s,"next",e)}function s(e){Fr(a,r,o,i,s,"throw",e)}i(void 0)}))})()}constructor(){super({name:"favorite",label:"🔖 Favorite",datasource:_t,query:"$dataSource",loading:!0,refresh:f.VariableRefresh.never,skipUrlSync:!0})}}function Br(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}class Mr extends i.Bs{onActivate(){i.jh.findByKeyAndType(this,"quick-filter",sr).setPlaceholder("Search favorites (comma-separated regexes are supported)");var e=this;const t=this.subscribeToEvent(Sr,function(){var t,n=(t=function*(t){e.openExpandedPanelDrawer(t.payload.item)},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){Br(a,r,o,i,s,"next",e)}function s(e){Br(a,r,o,i,s,"throw",e)}i(void 0)}))});return function(e){return n.apply(this,arguments)}}());return()=>{t.unsubscribe()}}getVariablesAndGridControls(){return{variables:[],gridControls:[i.jh.findByKeyAndType(this,"quick-filter",sr),i.jh.findByKeyAndType(this,"layout-switcher",rr),i.jh.findByKeyAndType(this,"no-data-switcher",ar)]}}openExpandedPanelDrawer(e){this.state.drawer.open({title:e.label,body:qn(e.panelType,{displayAllValues:!0,legendPlacement:"right",item:e,headerActions:()=>[new jr({type:"view-labels",item:e}),new jr({type:"view-flame-graph",item:e})]})})}static Component({model:e}){const{body:t,drawer:n}=e.useState();return o().createElement(o().Fragment,null,o().createElement(t.Component,{model:t}),o().createElement(n.Component,{model:n}))}constructor(){super({key:"explore-favorites",$variables:new i.Pj({variables:[new $r]}),body:new Er({key:"favorites-grid",variableName:"favorite",mapOptionToItem:e=>{const{index:t,value:n,panelType:r,queryRunnerParams:o}=JSON.parse(e.value);return{index:t,value:n,label:e.label,queryRunnerParams:o,panelType:r}},sortItemsFn:(e,t)=>Xe(e.label,t.label),headerActions:e=>{const t=[new jr({type:"view-labels",item:e,skipVariablesInterpolation:!0}),new jr({type:"view-flame-graph",item:e,skipVariablesInterpolation:!0})];return e.queryRunnerParams.groupBy&&t.push(new jr({type:"expand-panel",item:e,tooltip:()=>"Expand panel to view all the data",skipVariablesInterpolation:!0})),t.push(new gr({item:e,skipVariablesInterpolation:!0})),t}}),drawer:new Dr}),this.addActivationHandler(this.onActivate.bind(this))}}var Ur=n(5540);function Vr({options:e,mainLabels:t,value:n,onChange:a,onRefresh:i}){const l=(0,s.useStyles2)(qr),c=(0,s.useTheme2)(),[u,d]=(0,r.useState)(0),[p,m]=(0,r.useState)(0),f=p>u,g=(0,r.useRef)(null);(0,Ur.w)({ref:g,onResize:()=>{const e=g.current;e&&m(e.clientWidth)}});const y=e.filter((e=>t.includes(e.value))),b=e.filter((e=>!t.includes(e.value)));return(0,r.useEffect)((()=>{const{fontSize:e}=c.typography,t=y.map((e=>e.label||e.text||"")).join(" "),n=(0,s.measureText)(t,e).width;d(n+70*y.length)}),[y,c]),o().createElement(s.Field,{label:"Group by labels"},o().createElement("div",{ref:g,className:l.container},f?o().createElement(o().Fragment,null,o().createElement(s.RadioButtonGroup,{"aria-label":"Labels selector",options:y,value:n,onChange:a}),o().createElement(s.Select,{"aria-label":"Other labels selector",className:l.select,placeholder:"Other labels",options:b,value:n&&b.some((e=>e.value===n))?n:null,onChange:e=>{var t;return a(null!==(t=null==e?void 0:e.value)&&void 0!==t?t:"all")},isClearable:!0})):o().createElement(s.Select,{"aria-label":"Labels selector",className:l.select,value:n,placeholder:"Select label",options:e,onChange:e=>a((null==e?void 0:e.value)||zr.DEFAULT_VALUE),isClearable:!0}),o().createElement(s.RefreshPicker,{noIntervalPicker:!0,onRefresh:i,isOnCanvas:!1,onIntervalChanged:h.f,tooltip:"Click to refresh all labels"})))}const qr=e=>({container:a.css`
    display: flex;
    gap: ${e.spacing(1)};
  `,select:a.css`
    max-width: ${e.spacing(22)};
  `});function Gr(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Kr(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Gr(a,r,o,i,s,"next",e)}function s(e){Gr(a,r,o,i,s,"throw",e)}i(void 0)}))}}function Hr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class zr extends i.fS{onActivate(){this.state.value||this.setState({value:zr.DEFAULT_VALUE})}findCurrentOption(){const{value:e}=this.state,t=this.state.options.filter((e=>"all"!==e.value)).find((t=>JSON.parse(t.value).value===e));if(t){const e=JSON.parse(t.value);return{index:e.index,value:e.value,label:e.value,groupBy:e.groupBy}}return{index:0,value:e,label:e,groupBy:void 0}}constructor(){var e;super({key:"groupBy",name:"groupBy",label:"Group by labels",datasource:Rt,query:'$dataSource and $profileMetricId{service_name="$serviceName"}',loading:!0}),e=this,Hr(this,"update",Kr((function*(){if(e.state.loading)return;let t=[],n=null;e.setState({loading:!0,options:[],error:null});try{t=yield(0,jt.lastValueFrom)(e.getValueOptions({}))}catch(e){n=e}finally{e.setState({loading:!1,options:t,error:n})}}))),Hr(this,"onChange",(e=>{(0,m.r)("g_pyroscope_app_group_by_label_clicked"),c(),this.changeValueTo(e)})),this.changeValueTo=this.changeValueTo.bind(this),this.addActivationHandler(this.onActivate.bind(this))}}Hr(zr,"DEFAULT_VALUE","all"),Hr(zr,"MAX_MAIN_LABELS",8),Hr(zr,"Component",(({model:e})=>{const t=(0,s.useStyles2)(Yr),{loading:n,value:a,options:i,error:l}=e.useState(),c=(0,r.useMemo)((()=>i.map((({label:e,value:t})=>"all"===t?{label:e,value:t}:{label:e,value:JSON.parse(String(t)).value}))),[i]);if(n)return o().createElement(s.Field,{label:"Group by labels"},o().createElement(s.Spinner,{className:t.spinner}));if(l)return o().createElement(s.Field,{label:"Group by labels"},o().createElement("div",{className:t.groupByErrorContainer},o().createElement(s.Tooltip,{theme:"error",content:l.toString()},o().createElement(s.Icon,{className:t.iconError,name:"exclamation-triangle",size:"xl"})),o().createElement(s.RefreshPicker,{noIntervalPicker:!0,onRefresh:e.update,isOnCanvas:!1,onIntervalChanged:h.f})));return o().createElement(Vr,{options:c,value:a,mainLabels:(e=>e.slice(0,zr.MAX_MAIN_LABELS).map((({value:e})=>e)))(c),onChange:e.onChange,onRefresh:e.update})}));const Yr=e=>({spinner:a.css`
    height: 32px;
    line-height: 32px;
  `,groupByErrorContainer:a.css`
    display: flex;
  `,iconError:a.css`
    height: 32px;
    align-self: center;
    color: ${e.colors.error.text};
  `});function Qr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Wr extends i.Bs{onActivate(e,t){e&&this.initVariables(e),this.setState({body:this.buildTimeseries(e,t)}),t&&this.subscribeToGroupByStateChanges(e)}initVariables(e){const{serviceName:t,profileMetricId:n,filters:r}=e.queryRunnerParams;if(t){i.jh.findByKeyAndType(this,"serviceName",Vt).changeValueTo(t)}if(n){i.jh.findByKeyAndType(this,"profileMetricId",Ft).changeValueTo(n)}if(r){i.jh.findByKeyAndType(this,"filters",rn).setState({filters:r})}}buildTimeseries(e,t){const{headerActions:n}=this.state,r={index:0,value:"",queryRunnerParams:{},label:this.buildTitle(),panelType:sn.TIMESERIES};e&&t&&(r.queryRunnerParams.groupBy=e.queryRunnerParams.groupBy);const o=i.jh.findByKeyAndType(this,"groupBy",zr).state.value;return new Vn({item:r,headerActions:n,data:!e&&t&&o&&"all"!==o?new i.Es({$data:new i.dt({datasource:Nt,queries:[]}),transformations:[yn,bn]}):void 0})}subscribeToGroupByStateChanges(e){const t=i.jh.findByKeyAndType(this,"groupBy",zr);this._subs.add(t.subscribeToState(((n,r)=>{!n.loading&&n.options.length&&(e||!r.loading?n.value!==r.value&&this.onGroupByChanged(t):this.onGroupByChanged(t))})))}onGroupByChanged(e){var t;if(!e.state.value||"all"===e.state.value)return void this.resetTimeseries();const{index:n,value:r,groupBy:o}=e.findCurrentOption();null===(t=this.state.body)||void 0===t||t.updateItem({index:n,label:`${this.buildTitle()}, grouped by ${r}`,queryRunnerParams:{groupBy:o}})}resetTimeseries(e=!1){var t;e&&i.jh.findByKeyAndType(this,"filters",rn).reset(),null===(t=this.state.body)||void 0===t||t.updateItem({index:0,label:this.buildTitle(),queryRunnerParams:{groupBy:void 0}})}buildTitle(){const e=on(this,"profileMetricId"),{description:t}=At(e);return t||Dn(e)}static Component({model:e}){const{body:t}=e.useState();return t&&o().createElement(t.Component,{model:t})}constructor({item:e,headerActions:t,supportGroupBy:n}){super({headerActions:t,body:void 0}),Qr(this,"_variableDependency",new i.Sh(this,{variableNames:["serviceName","profileMetricId"],onReferencedVariableValueChanged:e=>{this.resetTimeseries("serviceName"===e.state.name)}})),this.addActivationHandler(this.onActivate.bind(this,e,n))}}Qr(Wr,"MIN_HEIGHT",240);class Jr extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Jr,"type","view-diff-flame-graph");var Xr=n(9897);class Zr extends f.BusEventWithPayload{}function eo({option:e,checked:t,onChange:n}){var i;const l=(0,s.useStyles2)(to),[c,u]=(0,r.useState)(!1),d=(0,r.useRef)(null),p=null===(i=d.current)||void 0===i?void 0:i.closest("label");return(0,r.useEffect)((()=>{if(!p||t)return void u(!1);const e=()=>{u(!0)},n=()=>{u(!1)};return p.addEventListener("mouseenter",e),p.addEventListener("mouseleave",n),()=>{p.removeEventListener("mouseleave",n),p.removeEventListener("mouseenter",e)}}),[t,p]),o().createElement(o().Fragment,null,o().createElement(s.Tooltip,{content:e.description,show:!t&&c,placement:"top"},o().createElement("span",{className:l.tooltipAnchor})),o().createElement(s.Checkbox,{ref:d,className:(0,a.cx)(l.checkbox,"checkbox",t&&"checked"),checked:t,label:e.label,onChange:()=>n(e.value)}))}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Zr,"type","select-for-compare");const to=e=>({tooltipAnchor:a.css`
    position: relative;
    left: 42px;
  `,checkbox:a.css`
    column-gap: 4px;

    &:last-child {
      & :nth-child(1) {
        grid-column-start: 2;
      }
      & :nth-child(2) {
        grid-column-start: 1;
      }
    }

    span {
      color: ${e.colors.text.secondary};
    }
    span:hover {
      color: ${e.colors.text.primary};
    }

    &.checked span {
      color: ${e.colors.text.primary};
    }
  `});function no({item:e,itemStats:t,statsDescription:n,compareActionChecks:a,onChangeCompareTarget:i}){const l=(0,s.useStyles2)(ro),{index:c,value:u}=e,d=un(c),p=(0,r.useMemo)((()=>{if(!t)return o().createElement(s.Spinner,{inline:!0});const{allValuesSum:e,unit:n}=t,{text:r,suffix:a}=(0,f.getValueFormat)(n)(e);return`${r}${a}`}),[t]),m=(0,r.useMemo)((()=>[{label:"Baseline",value:Xr.N.BASELINE,description:a[0]?"":`Click to select "${u}" as baseline for comparison`},{label:"Comparison",value:Xr.N.COMPARISON,description:a[1]?"":`Click to select "${u}" as target for comparison`}]),[a,u]);return o().createElement("div",{className:l.container,"data-testid":`stats-panel-${u}`},o().createElement("h1",{style:{color:d},className:l.title,title:`${n}: ${p}`},p),o().createElement("div",{className:l.compareActions},o().createElement(eo,{option:m[0],checked:a[0],onChange:i}),o().createElement(eo,{option:m[1],checked:a[1],onChange:i})))}const ro=e=>({container:a.css`
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    background-color: ${e.colors.background.canvas};
    padding: ${e.spacing(1)};
    border: 1px solid ${e.colors.border.weak};
    border-right: none;
    border-radius: 2px 0 0 2px;
  `,title:a.css`
    font-size: 24px;
    width: 100%;
    text-align: center;
    margin-top: ${e.spacing(5)};
  `,compareActions:a.css`
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    border-top: 1px solid ${e.colors.border.weak};
    padding: ${e.spacing(1)} 0 0 0;

    & .checkbox:nth-child(2) {
      padding-right: 4px;
      border-right: 1px solid ${e.colors.border.strong};
    }
    & .checkbox:nth-child(4) {
      padding-left: 4px;
    }
  `});function oo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ao extends i.Bs{onActivate(){const e=i.jh.findByKeyAndType(this,"group-by-labels",xo).getCompare();this.updateCompareActions(e.get(Xr.N.BASELINE),e.get(Xr.N.COMPARISON)),this.setState({statsDescription:this.getStatsDescription()})}updateCompareActions(e,t){const{item:n}=this.state;this.setState({compareActionChecks:[(null==e?void 0:e.value)===n.value,(null==t?void 0:t.value)===n.value]})}getStatsDescription(){const e=on(this,"profileMetricId"),{description:t}=At(e);return t||Dn(e)}getStats(){return this.state.itemStats}updateStats(e){this.setState({itemStats:e})}static Component({model:e}){const{item:t,itemStats:n,statsDescription:r,compareActionChecks:a}=e.useState();return o().createElement(no,{item:t,itemStats:n,statsDescription:r,compareActionChecks:a,onChangeCompareTarget:e.onChangeCompareTarget})}constructor({item:e}){super({item:e,itemStats:void 0,compareActionChecks:[!1,!1],statsDescription:""}),oo(this,"onChangeCompareTarget",(e=>{this.publishEvent(new Zr({compareTarget:e,item:this.state.item}),!0)})),this.addActivationHandler(this.onActivate.bind(this))}}oo(ao,"WIDTH_IN_PIXELS",186);class io extends i.Bs{static buildPanelKey(e){return`compare-panel-${e.value}`}onActivate(){const{statsPanel:e,timeseriesPanel:t}=this.state,n=t.subscribeToEvent(b,(t=>{var n,r;const o=null===(n=t.payload.series)||void 0===n?void 0:n[0];if(!o)return void e.updateStats({allValuesSum:0,unit:"short"});const a=pn(o,"allValuesSum")||0;(null===(r=e.getStats())||void 0===r?void 0:r.allValuesSum)!==a&&e.updateStats({allValuesSum:a,unit:o.fields[1].config.unit||"short"})}));return()=>{n.unsubscribe()}}static Component({model:e}){const t=(0,s.useStyles2)(so),{statsPanel:n,timeseriesPanel:r}=e.useState(),{compareActionChecks:i}=n.useState(),l=i[0]||i[1];return o().createElement("div",{className:(0,a.cx)(t.container,l&&"selected")},o().createElement("div",{className:t.statsPanel},o().createElement(n.Component,{model:n})),o().createElement("div",{className:t.timeseriesPanel},o().createElement(r.Component,{model:r})))}constructor({item:e,headerActions:t}){super({key:"label-value-panel",statsPanel:new ao({item:e}),timeseriesPanel:new Vn({item:e,headerActions:t})}),this.addActivationHandler(this.onActivate.bind(this))}}const so=e=>({container:a.css`
    display: flex;
    min-width: 0px;
    min-height: ${co};
    flex-flow: row;

    box-sizing: border-box;
    border: 1px solid transparent;
    &.selected {
      border: 1px solid ${e.colors.primary.main};
    }

    & > div {
      display: flex;
      position: relative;
      flex-direction: row;
      align-self: stretch;
      min-height: ${co};
    }
  `,statsPanel:a.css`
    width: ${ao.WIDTH_IN_PIXELS}px;
  `,timeseriesPanel:a.css`
    flex-grow: 1;

    & [data-viz-panel-key] > * {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  `});function lo({label:e}){const t='service_name="$serviceName"';return new i.dt({datasource:Nt,queries:[{refId:`$profileMetricId-${t}-${e}`,queryType:"metrics",profileTypeId:"$profileMetricId",labelSelector:`{${t}}`,groupBy:[e]}]})}const co="160px";class uo extends i.Bs{static buildGridItemKey(e){return`grid-item-${e.index}-${e.value}`}onActivate(){this.subscribeOnceToDataChange();const e=this.subscribeToGroupByChange(),t=this.subscribeToRefreshClick(),n=this.subscribeToQuickFilterChange(),r=this.subscribeToLayoutChange(),o=this.subscribeToHideNoDataChange(),a=this.subscribeToFiltersChange();return()=>{a.unsubscribe(),o.unsubscribe(),r.unsubscribe(),n.unsubscribe(),t.unsubscribe(),e.unsubscribe()}}subscribeOnceToDataChange(e=!1){const t=this.state.$data.subscribeToState((n=>{var r;(null===(r=n.data)||void 0===r?void 0:r.state)!==f.LoadingState.Loading&&(t.unsubscribe(),this.renderGridItems(e),this.setState({isLoading:!1}))}))}subscribeToGroupByChange(){return i.jh.findByKeyAndType(this,"groupBy",zr).subscribeToState(((e,t)=>{!e.loading&&t.loading&&this.refetchData()}))}subscribeToRefreshClick(){const e=()=>{this.refetchData()},t=document.querySelector('[data-testid="data-testid RefreshPicker run button"]');return t||g.v.error(new Error("SceneByVariableRepeaterGrid: Refresh button not found! The list of items will never be updated.")),null==t||t.addEventListener("click",e),null==t||t.setAttribute("title","Click to completely refresh all the panels present on the screen"),{unsubscribe(){null==t||t.removeAttribute("title"),null==t||t.removeEventListener("click",e)}}}subscribeToQuickFilterChange(){const e=i.jh.findByKeyAndType(this,"quick-filter",sr);this.subscribeToState(((t,n)=>{t.items.length!==n.items.length&&e.setResultsCount(t.items.length)}));return e.subscribeToState((0,y.debounce)(((e,t)=>{e.searchText!==(null==t?void 0:t.searchText)&&this.renderGridItems()}),sr.DEBOUNCE_DELAY))}subscribeToLayoutChange(){const e=i.jh.findByKeyAndType(this,"layout-switcher",rr),t=this.state.body,n=(e,n)=>{e.layout!==(null==n?void 0:n.layout)&&t.setState({templateColumns:e.layout===nr.ROWS?"1fr":"repeat(auto-fit, minmax(600px, 1fr))"})};return n(e.state),e.subscribeToState(n)}subscribeToHideNoDataChange(){const e=i.jh.findByKeyAndType(this,"no-data-switcher",ar);this.setState({hideNoData:"on"===e.state.hideNoData});return e.subscribeToState(((e,t)=>{e.hideNoData!==(null==t?void 0:t.hideNoData)&&(this.setState({hideNoData:"on"===e.hideNoData}),this.refetchData(!0))}))}subscribeToFiltersChange(){const e=i.jh.findByKeyAndType(this,"filters",rn),t=i.jh.findByKeyAndType(this,"no-data-switcher",ar);return e.subscribeToState((()=>{"on"===t.state.hideNoData&&this.refetchData()}))}refetchData(e=!1){this.setState({isLoading:!0,$data:new i.Es({$data:lo({label:this.state.label}),transformations:[yn,bn]})}),this.subscribeOnceToDataChange(e)}shouldRenderItems(e){const{items:t}=this.state;return!e.length||t.length!==e.length||!(0,y.isEqual)(t,e)}buildItemsData(e){const t=on(this,"serviceName"),n=on(this,"profileMetricId"),{label:r,startColorIndex:o,sortItemsFn:a}=this.state,i=e.map(((e,a)=>{var i;const s=e.fields[1],l=(null===(i=s.labels)||void 0===i?void 0:i[r])||"",c=dn(s,r);return{index:o+a,value:l,label:c,queryRunnerParams:{serviceName:t,profileMetricId:n,filters:[{key:r,operator:"=",value:l}]},panelType:sn.TIMESERIES}}));return this.filterItems(i).sort(a)}renderGridItems(e=!1){if(!this.state.$data.state.data)return;const{state:t,series:n,errors:r}=this.state.$data.state.data;if(t===f.LoadingState.Loading)return;if(t===f.LoadingState.Error)return void this.renderErrorState(null==r?void 0:r[0]);const o=this.buildItemsData(n);if(!e&&!this.shouldRenderItems(o))return;if(this.setState({items:o}),!this.state.items.length)return void this.renderEmptyState();const a=o.map((e=>new i.xK({key:uo.buildGridItemKey(e),body:this.buildVizPanel(e)})));this.state.body.setState({autoRows:co,children:a})}buildVizPanel(e){const t=new io({item:e,headerActions:this.state.headerActions.bind(null,e,this.state.items)}),n=t.subscribeToEvent(b,(e=>{var n;if(!this.state.hideNoData||(null===(n=e.payload.series)||void 0===n?void 0:n.length))return;const r=i.jh.getAncestor(t,i.xK),{key:o}=r.state,a=i.jh.getAncestor(r,i.gF),s=a.state.children.filter((e=>e.state.key!==o));s.length?a.setState({children:s}):this.renderEmptyState()}));return t.addActivationHandler((()=>()=>{n.unsubscribe()})),t}filterItems(e){const t=i.jh.findByKeyAndType(this,"quick-filter",sr),{searchText:n}=t.state;if(!n)return e;const r=n.split(",").map((e=>e.trim())).filter(Boolean).map((e=>{try{return new RegExp(e)}catch(e){return null}})).filter(Boolean);return e.filter((({label:e})=>r.some((t=>t.test(e)))))}renderEmptyState(){this.state.body.setState({autoRows:"480px",children:[new i.xK({body:new Zn({message:"No results"})})]})}renderErrorState(e){this.state.body.setState({autoRows:"480px",children:[new i.xK({body:new er({message:e.message||e.toString()})})]})}static Component({model:e}){const{body:t,isLoading:n}=e.useState();return n?o().createElement(s.Spinner,null):o().createElement("div",{style:{marginBottom:"2px"}},o().createElement(t.Component,{model:t}))}constructor({key:e,label:t,startColorIndex:n,headerActions:r}){super({key:e,label:t,startColorIndex:n,items:[],isLoading:!0,$data:new i.Es({$data:lo({label:t}),transformations:[yn,bn]}),hideNoData:!1,headerActions:r,sortItemsFn:br,body:new i.gF({templateColumns:"1fr",autoRows:co,isLazy:!0,$behaviors:[new i.Gg.K2({key:"metricCrosshairSync",sync:f.DashboardCursorSync.Crosshair})],children:[]})}),this.addActivationHandler(this.onActivate.bind(this))}}class po extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(po,"type","clear-label-from-filters");class mo extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(mo,"type","exclude-label-from-filters");class fo extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(fo,"type","include-label-in-filters");const ho=e=>{const t=(0,s.useStyles2)(yo),{include:n,exclude:r}=function({status:e,label:t,onInclude:n,onExclude:r,onClear:o}){const a="included"===e,i="excluded"===e;return{include:{isSelected:a,tooltip:a?`Clear "${t}" from the filters`:`Include "${t}" in the filters`,onClick:a?o:n},exclude:{isSelected:i,tooltip:i?`Clear "${t}" from the filters`:`Exclude "${t}" in the filters`,onClick:i?o:r}}}(e);return o().createElement("div",{className:t.container},o().createElement(s.Button,{size:"sm",fill:"outline",variant:n.isSelected?"primary":"secondary","aria-selected":n.isSelected,className:(0,a.cx)(t.includeButton,n.isSelected&&"selected"),onClick:n.onClick,tooltip:n.tooltip,tooltipPlacement:"top","data-testid":"filter-button-include"},"Include"),o().createElement(s.Button,{size:"sm",fill:"outline",variant:r.isSelected?"primary":"secondary","aria-selected":r.isSelected,className:(0,a.cx)(t.excludeButton,r.isSelected&&"selected"),onClick:r.onClick,tooltip:r.tooltip,tooltipPlacement:"top","data-testid":"filter-button-exclude"},"Exclude"))},go=(0,r.memo)(ho),yo=e=>({container:a.css`
      display: flex;
      justify-content: center;
    `,includeButton:a.css`
      border-radius: ${e.shape.radius.default} 0 0 ${e.shape.radius.default};

      &:not(.selected) {
        border-right: none;
      }
    `,excludeButton:a.css`
      border-radius: 0 ${e.shape.radius.default} ${e.shape.radius.default} 0;

      &:not(.selected) {
        border-left: none;
      }
    `});function bo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class vo extends i.Bs{getStatus(e){const{key:t,value:n}=this.state.item.queryRunnerParams.filters[0],r=e.find((e=>e.key===t));return r?Ce(r.operator)&&r.value.split("|").includes(n)?"=~"===r.operator?"included":"excluded":r.value===n?"="===r.operator?"included":"excluded":"clear":"clear"}constructor({item:e}){super({item:e}),bo(this,"onInclude",(()=>{(0,m.r)("g_pyroscope_app_include_action_clicked"),this.publishEvent(new fo({item:this.state.item}),!0)})),bo(this,"onExclude",(()=>{(0,m.r)("g_pyroscope_app_exclude_action_clicked"),this.publishEvent(new mo({item:this.state.item}),!0)})),bo(this,"onClear",(()=>{this.publishEvent(new po({item:this.state.item}),!0)}))}}function Eo({compare:e,onClickCompare:t,onClickClear:n}){const i=(0,s.useStyles2)(So),l=e.size<2,c=e.size>0,u=(0,r.useMemo)((()=>{var t,n,r,o;return 2===e.size?`Compare "${null===(r=e.get(Xr.N.BASELINE))||void 0===r?void 0:r.label}" vs "${null===(o=e.get(Xr.N.COMPARISON))||void 0===o?void 0:o.label}"`:0===e.size?"Select both a baseline and a comparison panel to compare their flame graphs":e.has(Xr.N.BASELINE)?`Select another panel to compare against "${null===(t=e.get(Xr.N.BASELINE))||void 0===t?void 0:t.label}"`:`Select another panel to compare against "${null===(n=e.get(Xr.N.COMPARISON))||void 0===n?void 0:n.label}"`}),[e]);return o().createElement("div",{className:i.container},o().createElement(s.Button,{"arial-label":"Compare",className:i.compareButton,variant:"primary",disabled:l,onClick:l?h.f:t,tooltip:u},"Compare (",e.size,"/2)"),o().createElement(s.Button,{"data-testid":"clearComparison",className:(0,a.cx)(i.clearButton,l?void 0:i.clearButtonActive),icon:"times",variant:"secondary",tooltip:c?"Clear comparison selection":"",disabled:!c,onClick:c?n:h.f}))}bo(vo,"Component",(({model:e})=>{const{item:t}=e.useState(),{filters:n}=i.jh.findByKeyAndType(e,"filters",rn).useState(),a=(0,r.useMemo)((()=>e.getStatus(n)),[n,e]);return o().createElement(go,{label:t.value,status:a,onInclude:e.onInclude,onExclude:e.onExclude,onClear:e.onClear})}));const So=e=>({container:a.css`
    display: flex;
    align-items: center;
    width: ${ao.WIDTH_IN_PIXELS}px;
  `,compareButton:a.css`
    width: ${ao.WIDTH_IN_PIXELS-32}px;
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  `,clearButton:a.css`
    box-sizing: border-box;
    width: 32px !important;
    height: 32px !important;
    color: ${e.colors.text.secondary};
    background-color: transparent;
    border-left: none !important;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;

    &:not([disabled]),
    &:not([disabled]):hover {
      background-color: transparent;
      box-shadow: none;
    }
  `,clearButtonActive:a.css`
    border-color: ${e.colors.border.medium};

    &:hover {
      border-color: ${e.colors.border.medium};
    }
  `});function wo(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Oo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class xo extends i.Bs{onActivate(e){var t,n=this;return(t=function*(){const t=i.jh.findByKeyAndType(n,"groupBy",zr);yield t.update(),e&&n.initVariablesAndControls(e),n.renderBody(t);const r=n.subscribeToGroupByChange(),o=n.subscribeToPanelEvents();return()=>{var e;o.unsubscribe(),r.unsubscribe(),null===(e=n.state.panelTypeChangeSub)||void 0===e||e.unsubscribe()}},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){wo(a,r,o,i,s,"next",e)}function s(e){wo(a,r,o,i,s,"throw",e)}i(void 0)}))})()}initVariablesAndControls(e){const{queryRunnerParams:t,panelType:n}=e,{groupBy:r}=t;if(null==r?void 0:r.label){i.jh.findByKeyAndType(this,"groupBy",zr).changeValueTo(r.label)}if(n){i.jh.findByKeyAndType(this,"panel-type-switcher",ln).setState({panelType:n})}}subscribeToGroupByChange(){const e=i.jh.findByKeyAndType(this,"groupBy",zr),t=i.jh.findByKeyAndType(this,"quick-filter",sr);return e.subscribeToState(((n,r)=>{n.value!==(null==r?void 0:r.value)&&(t.clearSearchText(),this.renderBody(e))}))}subscribeToPanelEvents(){const e=this.subscribeToEvent(wr,(e=>{this.selectLabel(e.payload.item)})),t=this.subscribeToEvent(Zr,(e=>{const{compareTarget:t,item:n}=e.payload;this.selectForCompare(t,n)})),n=this.subscribeToEvent(fo,(e=>{this.includeLabelValueInFilters(e.payload.item)})),r=this.subscribeToEvent(mo,(e=>{this.excludeLabelValueFromFilters(e.payload.item)})),o=this.subscribeToEvent(po,(e=>{this.clearLabelValueFromFilters(e.payload.item)}));return{unsubscribe(){o.unsubscribe(),r.unsubscribe(),n.unsubscribe(),t.unsubscribe(),e.unsubscribe()}}}subscribeToPanelTypeChange(){return i.jh.findByKeyAndType(this,"panel-type-switcher",ln).subscribeToState(((e,t)=>{var n;e.panelType!==(null==t?void 0:t.panelType)&&(null===(n=this.state.body)||void 0===n||n.renderGridItems())}))}renderBody(e){var t;null===(t=this.state.panelTypeChangeSub)||void 0===t||t.unsubscribe(),"all"===e.state.value?(this.setState({panelTypeChangeSub:this.subscribeToPanelTypeChange()}),this.switchToLabelNamesGrid()):this.switchToLabelValuesGrid(e)}switchToLabelNamesGrid(){i.jh.findByKeyAndType(this,"quick-filter",sr).setPlaceholder("Search labels (comma-separated regexes are supported)"),this.setState({body:this.buildSceneLabelNamesGrid()})}buildSceneLabelNamesGrid(){return new Er({key:"service-labels-grid",variableName:"groupBy",mapOptionToItem:(e,t,{serviceName:n,profileMetricId:r,panelType:o})=>{if("all"===e.value)return null;const{value:a,groupBy:i}=JSON.parse(e.value);return{index:t-1,value:a,label:a,queryRunnerParams:{serviceName:n,profileMetricId:r,groupBy:i,filters:[]},panelType:o}},headerActions:e=>[new jr({type:"select-label",item:e}),new jr({type:"expand-panel",item:e}),new gr({item:e})]})}switchToLabelValuesGrid(e){i.jh.findByKeyAndType(this,"quick-filter",sr).setPlaceholder("Search label values (comma-separated regexes are supported)"),this.clearCompare();const{index:t,value:n}=e.findCurrentOption();this.setState({body:this.buildSceneLabelValuesGrid(n,t)})}buildSceneLabelValuesGrid(e,t){return new uo({key:"service-label-values-grid",startColorIndex:t,label:e,headerActions:e=>[new jr({type:"view-flame-graph",item:e,tooltip:(e,t)=>{const{queryRunnerParams:n,label:r}=e,o=n.profileMetricId||on(t,"profileMetricId"),a=on(t,"groupBy");return`View the "${At(o).type}" flame graph for "${a}=${r}"`}}),new vo({item:e}),new gr({item:e})]})}selectLabel({queryRunnerParams:e}){const t=e.groupBy.label,n=i.jh.findByKeyAndType(this,"groupBy",zr);c(),n.changeValueTo(t)}includeLabelValueInFilters(e){const[t]=e.queryRunnerParams.filters,n=i.jh.findByKeyAndType(this,"filters",rn);n.setState({filters:Xt(n.state.filters,t)})}excludeLabelValueFromFilters(e){const t=i.jh.findByKeyAndType(this,"filters",rn),[n]=e.queryRunnerParams.filters;t.setState({filters:Zt(t.state.filters,n)})}clearLabelValueFromFilters(e){const t=i.jh.findByKeyAndType(this,"filters",rn),[n]=e.queryRunnerParams.filters;t.setState({filters:en(t.state.filters,n)})}selectForCompare(e,t){var n;const r=new Map(this.state.compare);(null===(n=r.get(e))||void 0===n?void 0:n.value)===t.value?r.delete(e):r.set(e,t),this.setState({compare:r}),this.updateStatsPanels()}updateStatsPanels(){const{compare:e}=this.state,t=e.get(Xr.N.BASELINE),n=e.get(Xr.N.COMPARISON),r=i.jh.findAllObjects(this,(e=>e instanceof ao));for(const e of r)e.updateCompareActions(t,n)}getCompare(){return this.state.compare}clearCompare(){this.setState({compare:new Map})}constructor({item:e}){super({key:"group-by-labels",body:void 0,compare:new Map,panelTypeChangeSub:void 0}),Oo(this,"onClickCompareButton",(()=>{(0,m.r)("g_pyroscope_app_compare_link_clicked");const{compare:e}=this.state,{filters:t}=fr(this,e.get(Xr.N.BASELINE)),{filters:n}=fr(this,e.get(Xr.N.COMPARISON));this.publishEvent(new Jr({useAncestorTimeRange:!0,clearDiffRange:!0,baselineFilters:t,comparisonFilters:n}),!0)})),Oo(this,"onClickClearCompareButton",(()=>{this.clearCompare(),this.updateStatsPanels()})),this.addActivationHandler((()=>{this.onActivate(e)}))}}Oo(xo,"Component",(({model:e})=>{const t=(0,s.useStyles2)(To),{body:n,compare:a}=e.useState(),l=i.jh.findByKeyAndType(e,"groupBy",zr),{value:c}=l.useState(),u=(0,r.useMemo)((()=>"all"===c?i.jh.findByKeyAndType(e,"profiles-explorer",Jl).state.gridControls:[i.jh.findByKeyAndType(e,"quick-filter",sr),i.jh.findByKeyAndType(e,"layout-switcher",rr),i.jh.findByKeyAndType(e,"no-data-switcher",ar)]),[c,e]);return o().createElement("div",{className:t.container,"data-testid":"groupByLabelsContainer"},o().createElement(l.Component,{model:l}),o().createElement("div",{className:t.sceneControls},o().createElement(s.Stack,{wrap:"wrap"},"all"!==c&&o().createElement(Eo,{compare:a,onClickCompare:e.onClickCompareButton,onClickClear:e.onClickClearCompareButton}),u.map((e=>o().createElement(e.Component,{key:e.state.key,model:e}))))),n&&o().createElement(n.Component,{model:n}))}));const To=e=>({container:a.css`
    margin-top: ${e.spacing(1)};
  `,sceneControls:a.css`
    margin-bottom: ${e.spacing(1)};

    & .quick-filter {
      flex: 1;
      min-width: 112px;
    }
  `});function Po(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Co(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ko(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Co(e,t,n[t])}))}return e}function Ao(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class jo extends i.Bs{onActivate(e){e&&this.initVariables(e);const t=i.jh.findByKeyAndType(this,"profileMetricId",Ft);t.setState({query:Ft.QUERY_SERVICE_NAME_DEPENDENT}),t.update(!0);const n=this.subscribeToPanelEvents();return()=>{n.unsubscribe(),t.setState({query:Ft.QUERY_DEFAULT}),t.update(!0)}}initVariables(e){const{queryRunnerParams:t}=e,{serviceName:n,profileMetricId:r,filters:o}=t;if(n){i.jh.findByKeyAndType(this,"serviceName",Vt).changeValueTo(n)}if(r){i.jh.findByKeyAndType(this,"profileMetricId",Ft).changeValueTo(r)}if(o){i.jh.findByKeyAndType(this,"filters",rn).setState({filters:o})}}subscribeToPanelEvents(){var e=this;const t=this.subscribeToEvent(Sr,function(){var t,n=(t=function*(t){e.openExpandedPanelDrawer(t.payload.item)},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){Po(a,r,o,i,s,"next",e)}function s(e){Po(a,r,o,i,s,"throw",e)}i(void 0)}))});return function(e){return n.apply(this,arguments)}}()),n=this.subscribeToEvent(wr,(()=>{this.state.drawer.close()}));return{unsubscribe(){n.unsubscribe(),t.unsubscribe()}}}getVariablesAndGridControls(){return{variables:[i.jh.findByKeyAndType(this,"serviceName",Vt),i.jh.findByKeyAndType(this,"profileMetricId",Ft),i.jh.findByKeyAndType(this,"filters",rn)],gridControls:[]}}openExpandedPanelDrawer(e){var t;const n=on(this,"serviceName"),r=on(this,"profileMetricId"),o=`${At(r).description||Dn(r)}, grouped by ${(null===(t=e.queryRunnerParams.groupBy)||void 0===t?void 0:t.label)||"?"}`;this.state.drawer.open({title:n,body:qn(e.panelType,{displayAllValues:!0,legendPlacement:"right",item:Ao(ko({},e),{label:o}),headerActions:()=>[new jr({type:"select-label",item:e}),new gr({item:e})]})})}static Component({model:e}){const{body:t,drawer:n}=e.useState();return o().createElement(o().Fragment,null,o().createElement(t.Component,{model:t}),o().createElement(n.Component,{model:n}))}constructor({item:e}){super({key:"explore-service-labels",body:new i.G1({direction:"column",$behaviors:[new i.Gg.K2({key:"metricCrosshairSync",sync:f.DashboardCursorSync.Crosshair})],children:[new i.vA({minHeight:Wr.MIN_HEIGHT,body:new Wr({item:e,headerActions:e=>e.queryRunnerParams.groupBy?[new jr({type:"view-flame-graph",item:e}),new jr({type:"expand-panel",item:e}),new gr({item:e})]:[new jr({type:"view-flame-graph",item:e}),new gr({item:e})],supportGroupBy:!0})}),new i.vA({body:new xo({item:e})})]}),drawer:new Dr}),this.addActivationHandler(this.onActivate.bind(this,e))}}class No extends i.Bs{onActivate(e){i.jh.findByKeyAndType(this,"quick-filter",sr).setPlaceholder("Search profile types (comma-separated regexes are supported)"),e&&this.initVariables(e)}initVariables(e){if(e.queryRunnerParams.serviceName){i.jh.findByKeyAndType(this,"serviceName",Vt).changeValueTo(e.queryRunnerParams.serviceName)}}getVariablesAndGridControls(){return{variables:[i.jh.findByKeyAndType(this,"serviceName",Vt)],gridControls:[i.jh.findByKeyAndType(this,"quick-filter",sr),i.jh.findByKeyAndType(this,"layout-switcher",rr)]}}static Component({model:e}){const{body:t}=e.useState();return o().createElement(t.Component,{model:t})}constructor({item:e}){super({key:"explore-service-profile-types",$variables:new i.Pj({variables:[new Ft({query:Ft.QUERY_SERVICE_NAME_DEPENDENT,skipUrlSync:!0})]}),body:new Er({key:"profile-metrics-grid",variableName:"profileMetricId",mapOptionToItem:(e,t,{serviceName:n})=>({index:t,value:e.value,label:e.label,queryRunnerParams:{serviceName:n,profileMetricId:e.value},panelType:sn.TIMESERIES}),headerActions:e=>[new jr({type:"view-labels",item:e}),new jr({type:"view-flame-graph",item:e}),new gr({item:e})]})}),this.addActivationHandler(this.onActivate.bind(this,e))}}function Io(e,t){return{from:e,to:t,value:{from:(0,f.dateTimeParse)(e),to:(0,f.dateTimeParse)(t),raw:{from:e,to:t}}}}const _o=()=>Io("now-30m","now");function Ro(e,t){if(t){const n=e.services.get(t)||new Map;return Array.from(n.values()).sort(((e,t)=>Xe(t.group,e.group))).map((({id:e,type:t,group:n})=>({value:e,text:`${t} (${n})`})))}return Array.from(e.profileMetrics.keys()).map((e=>At(e))).sort(((e,t)=>Xe(t.group,e.group))).map((({id:e,type:t,group:n})=>({value:e,text:`${t} (${n})`})))}function Lo(e,t){if(t){const n=e.profileMetrics.get(t)||new Set;return Array.from(n).sort(Xe).map((e=>({text:e,value:e})))}return Array.from(e.services.keys()).sort(Xe).map((e=>({text:e,value:e})))}class Do{static build(e,t){const n=`${e}-${t.name}`,r=Do.cache.get(n);if(r instanceof t)return r;const o=new t({dataSourceUid:e});return Do.cache.set(n,o),o}}function Fo(e){let t,n;for(const{name:r,value:o}of e)if("service_name"===r&&(t=o),"__profile_type__"===r&&(n=o),t&&n)return[t,n];return[]}function $o(e){const t=new Map,n=new Map;if(!e.labelsSet)return g.v.warn("Pyroscope SeriesApiClient: no data received!"),{services:t,profileMetrics:n};for(const{labels:r}of e.labelsSet){const[e,o]=Fo(r);if(!e||!o){g.v.warn('Pyroscope ServicesApiClient: "service_name" and/or "__profile_type__" are missing in the labels received!',r);continue}const a=t.get(e)||new Map;a.set(o,At(o)),t.set(e,a);const i=n.get(o)||new Set;i.add(e),n.set(o,i)}return{services:t,profileMetrics:n}}function Bo(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Do,"cache",new Map);class Mo extends A{list(e){var t,n=this;return(t=function*(){const{from:t,to:r}=e;return n.fetch("/querier.v1.QuerierService/Series",{method:"POST",body:JSON.stringify({start:t,end:r,labelNames:["service_name","__profile_type__"],matchers:[]})}).then((e=>e.json())).then($o)},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){Bo(a,r,o,i,s,"next",e)}function s(e){Bo(a,r,o,i,s,"throw",e)}i(void 0)}))})()}constructor(e){super(e)}}function Uo(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}const Vo=new class extends D{list(e){var t,n=this;return(t=function*(){const{from:t,to:r}=xn(e.timeRange),o=[n.apiClient.baseUrl,t,r],a=n.cacheClient.get(o);if(a){const{services:e,profileMetrics:t}=yield a;return e.size||t.size||n.cacheClient.delete(o),{services:e,profileMetrics:t}}const i=n.apiClient.list({from:t,to:r});n.cacheClient.set(o,i);try{const{services:e,profileMetrics:t}=yield i;return{services:e,profileMetrics:t}}catch(e){throw n.cacheClient.delete(o),e}},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){Uo(a,r,o,i,s,"next",e)}function s(e){Uo(a,r,o,i,s,"throw",e)}i(void 0)}))})()}constructor(e){super(e)}}({cacheClient:new _});function qo(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Go(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){qo(a,r,o,i,s,"next",e)}function s(e){qo(a,r,o,i,s,"throw",e)}i(void 0)}))}}class Ko extends i.UU{fetchSeries(e,t,n){return Go((function*(){Vo.setApiClient(Do.build(e,Mo));try{return yield Vo.list({timeRange:t})}catch(e){throw g.v.error(e,{info:"Error while loading Pyroscope series!",variableName:n||""}),e}}))()}query(){return Go((function*(){return{state:f.LoadingState.Done,data:[{name:"PyroscopeSeries",fields:[{name:"PyroscopeSeries",type:f.FieldType.other,values:[],config:{}}],length:0}]}}))()}metricFindQuery(e,t){var n=this;return Go((function*(){var r,o,a;const s=null===(o=t.scopedVars)||void 0===o||null===(r=o.__sceneObject)||void 0===r?void 0:r.value,l=i.jh.interpolate(s,"$dataSource"),c=i.jh.interpolate(s,"$serviceName"),u=i.jh.interpolate(s,"$profileMetricId"),d=yield n.fetchSeries(l,t.range,null===(a=t.variable)||void 0===a?void 0:a.name);switch(e){case"$dataSource and all services":return Lo(d);case"$dataSource and all profile metrics":return Ro(d);case"$dataSource and only $profileMetricId services":return Lo(d,u);case"$dataSource and only $serviceName profile metrics":return Ro(d,c);default:throw new TypeError(`Unsupported query "${e}"!`)}}))()}testDatasource(){return Go((function*(){return{status:"success",message:"OK"}}))()}constructor(){super(It.type,It.uid)}}class Ho extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Ho,"type","enable-sync-timeranges");class zo extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(zo,"type","sync-refresh");class Yo extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Yo,"type","sync-timeranges");class Qo extends i.KE{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Qo,"Component",(function({model:e}){const{hidePicker:t,isOnCanvas:n}=e.useState(),r=i.jh.getTimeRange(e),a=r.getTimeZone(),l=r.useState();return t?null:o().createElement(s.TimeRangePicker,{isOnCanvas:null==n||n,value:l.value,onChange:r.onTimeRangeChange,timeZone:a,fiscalYearStartMonth:l.fiscalYearStartMonth,onMoveBackward:e.onMoveBackward,onMoveForward:e.onMoveForward,onZoom:e.onZoom,onChangeTimeZone:r.onTimeZoneChange,onChangeFiscalYearStartMonth:e.onChangeFiscalYearStartMonth,isSynced:!1})}));const Wo=/^\d+[yYmMsSwWhHdD]$/;function Jo(e){if("string"!=typeof e)return null;if(-1!==e.indexOf("now"))return e;if(Wo.test(e))return e;if(8===e.length){const t=(0,f.toUtc)(e,"YYYYMMDD");if(t.isValid())return t.toISOString()}else if(15===e.length){const t=(0,f.toUtc)(e,"YYYYMMDDTHHmmss");if(t.isValid())return t.toISOString()}else if(24===e.length){return(0,f.toUtc)(e).toISOString()}const t=parseInt(e,10);return isNaN(t)?null:(0,f.toUtc)(t).toISOString()}function Xo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Zo(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class ea extends f.MutableDataFrame{addRange(e){this.add(Zo(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Xo(e,t,n[t])}))}return e}({},e),{isRegion:!0}))}constructor(){super(),[{name:"time",type:f.FieldType.time},{name:"timeEnd",type:f.FieldType.time},{name:"isRegion",type:f.FieldType.boolean},{name:"color",type:f.FieldType.other},{name:"text",type:f.FieldType.string}].forEach((e=>this.addField(e)))}}function ta(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function na(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ta(e,t,n[t])}))}return e}function ra(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}var oa=function(e){return e.ANNOTATIONS="annotations",e.DEFAULT="default",e}({});const aa={from:(0,f.dateTime)(0),to:(0,f.dateTime)(0),raw:{from:"",to:""}};class ia extends i.Bs{onActivate(){var e;this.setState((0,y.omit)(this.getAncestorTimeRange().state,"key")),this._subs.add(this.getAncestorTimeRange().subscribeToState((e=>{this.setState((0,y.omit)(e,"key"))}))),this._subs.add(null===(e=this.getTimeseries().state.$data)||void 0===e?void 0:e.subscribeToState(((e,t)=>{var n,r,o,a,i,s;e.data&&e.data.state===f.LoadingState.Done&&((null===(n=e.data.annotations)||void 0===n?void 0:n.length)||(null===(o=t.data)||void 0===o||null===(r=o.annotations)||void 0===r?void 0:r.length)?!(null===(a=e.data.annotations)||void 0===a?void 0:a.length)&&(null===(s=t.data)||void 0===s||null===(i=s.annotations)||void 0===i?void 0:i.length)&&(e.data.annotations=t.data.annotations):this.updateTimeseriesAnnotation())})))}getAncestorTimeRange(){if(!this.parent||!this.parent.parent)throw new Error(typeof this+" must be used within $timeRange scope");return i.jh.getTimeRange(this.parent.parent)}getTimeseries(){try{const e=i.jh.getAncestor(this,i.Eb);if("timeseries"!==e.state.pluginId)throw new TypeError("Incorrect VizPanel type!");return e}catch(e){throw new Error("Ancestor timeseries panel not found!")}}updateTimeseriesAnnotation(){const{annotationTimeRange:e,annotationColor:t,annotationTitle:n}=this.state,{$data:r}=this.getTimeseries().state,o=null==r?void 0:r.state.data;if(!o)return;const a=new ea;a.addRange({color:t,text:n,time:1e3*e.from.unix(),timeEnd:1e3*e.to.unix()}),null==r||r.setState({data:ra(na({},o),{annotations:[a]})})}setAnnotationTimeRange(e,t=!1){this.setState({annotationTimeRange:e}),t&&this.updateTimeseriesAnnotation()}nullifyAnnotationTimeRange(){this.setAnnotationTimeRange(aa)}getUrlState(){const{annotationTimeRange:e}=this.state;return{diffFrom:"string"==typeof e.raw.from?e.raw.from:e.raw.from.toISOString(),diffTo:"string"==typeof e.raw.to?e.raw.to:e.raw.to.toISOString()}}updateFromUrl(e){const{diffFrom:t,diffTo:n}=e;if(!n&&!t)return;const{annotationTimeRange:r}=this.state;var o,a;this.setAnnotationTimeRange(this.buildAnnotationTimeRange(null!==(o=Jo(t))&&void 0!==o?o:r.from,null!==(a=Jo(n))&&void 0!==a?a:r.to))}buildAnnotationTimeRange(e,t){return function(e,t,n,r,o){const a=o&&"now"===t;return{from:f.dateMath.parse(e,!1,n,r),to:f.dateMath.parse(a?"now-"+o:t,!0,n,r),raw:{from:e,to:t}}}(e,t,this.getTimeZone(),this.state.fiscalYearStartMonth,this.state.UNSAFE_nowDelay)}onTimeRangeChange(e){const{mode:t}=this.state;"default"!==t?this.setAnnotationTimeRange(e,!0):this.getAncestorTimeRange().onTimeRangeChange(e)}onTimeZoneChange(e){this.getAncestorTimeRange().onTimeZoneChange(e)}getTimeZone(){return this.getAncestorTimeRange().getTimeZone()}onRefresh(){this.getAncestorTimeRange().onRefresh()}constructor(e){super(na({from:aa.raw.from,to:aa.raw.to,value:aa,annotationTimeRange:aa},e)),ta(this,"_variableDependency",new i.Sh(this,{variableNames:["dataSource","serviceName"],onReferencedVariableValueChanged:()=>{this.nullifyAnnotationTimeRange(),this.updateTimeseriesAnnotation()}})),ta(this,"_urlSync",new i.So(this,{keys:["diffFrom","diffTo"]})),this.addActivationHandler(this.onActivate.bind(this))}}class sa extends f.BusEventWithPayload{}function la(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(sa,"type","switch-timerange-selection-mode");var ca=function(e){return e.TIMEPICKER="timepicker",e.FLAMEGRAPH="flame-graph",e}({});class ua extends i.Bs{constructor(){super({mode:"flame-graph"}),la(this,"onChange",(e=>{this.setState({mode:e}),this.publishEvent(new sa({mode:e}),!0)}))}}la(ua,"OPTIONS",[{label:"Time picker",value:"timepicker"},{label:"Flame graph",value:"flame-graph"}]),la(ua,"Component",(({model:e})=>{const t=(0,s.useStyles2)(da),{mode:n}=e.useState();return o().createElement("div",{className:t.container},o().createElement("label",{className:t.label},o().createElement("span",null,"Range selection mode "),o().createElement(s.Tooltip,{content:o().createElement("div",{className:t.tooltip},o().createElement("div",null,"Use these buttons to change the behaviour when selecting a range with the mouse on the time series:"),o().createElement("dl",null,o().createElement("dt",null,"Time picker"),o().createElement("dd",null,"Time range zoom in (default behaviour)"),o().createElement("dt",null,"Flame graph"),o().createElement("dd",null,"Time range for building the flame graph (the stack traces will be retrieved only for the selected range)"))),placement:"top"},o().createElement(s.Icon,{name:"question-circle"}))),o().createElement(s.RadioButtonGroup,{size:"sm",options:ua.OPTIONS,value:n,onChange:e.onChange,"aria-label":"Range selection mode"}))}));const da=e=>({container:a.css`
    display: flex;
    flex-direction: column;
  `,tooltip:a.css`
    padding: ${e.spacing(1)};
    & dl {
      margin-top: ${e.spacing(2)};
      display: grid;
      grid-gap: ${e.spacing(1)} ${e.spacing(2)};
      grid-template-columns: max-content;
    }
    & dt {
      font-weight: bold;
    }
    & dd {
      margin: 0;
      grid-column-start: 2;
    }
  `,label:a.css`
    font-size: 12px;
    text-align: right;
    margin-bottom: 2px;
    color: ${e.colors.text.secondary};
  `});function pa({filterKey:e}){return fn(new i.dt({datasource:Nt,queries:[{refId:`$profileMetricId-$serviceName-${e}}`,queryType:"metrics",profileTypeId:"$profileMetricId",labelSelector:`{service_name="$serviceName",$${e}}`}]}))}var ma=n(4308),fa=n.n(ma);const ha={COLOR:fa()("#d066d4"),OVERLAY:fa()("#d066d4").alpha(.3)},ga={COLOR:fa()("#1398f6"),OVERLAY:fa()("#1398f6").alpha(.3)};function ya(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ba(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ya(e,t,n[t])}))}return e}class va extends i.Bs{onActivate(e,t,n){const{$timeRange:r,timeseriesPanel:o,filterKey:a}=this.state;if(t&&this.setDiffRange(null),e&&r.setState((0,y.omit)(this.getAncestorTimeRange().state,"key")),n.length){i.jh.findByKey(this,a).setState({filters:n})}o.updateItem({label:this.buildTimeseriesTitle()});const s=this.subscribeToEvents();return()=>{s.unsubscribe()}}static buildTimeSeriesPanel({target:e,filterKey:t,title:n,color:r}){const o=new Vn({item:{index:0,value:e,label:"",queryRunnerParams:{},panelType:sn.TIMESERIES},data:new i.Es({$data:pa({filterKey:t}),transformations:[yn,bn]}),overrides:e=>e.map((e=>{const t=e.fields[1],n=pn(e,"allValuesSum")||0,a=(0,f.getValueFormat)(t.config.unit)(n),i=`${a.text}${a.suffix}`,[s,l,c]=va.getDiffRange(o),u=s&&l?`Total = ${i} / Flame graph range = ${(0,f.dateTimeFormat)(s,{format:f.systemDateFormats.fullDate,timeZone:c})} → ${(0,f.dateTimeFormat)(l,{format:f.systemDateFormats.fullDate,timeZone:c})}`:`Total = ${i}`;return{matcher:{id:f.FieldMatcherID.byFrameRefID,options:e.refId},properties:[{id:"displayName",value:u},{id:"color",value:{mode:"fixed",fixedColor:r}}]}})),headerActions:()=>[new ua]});return o.state.body.setState({$timeRange:new ia({key:`${e}-annotation-timerange`,mode:oa.ANNOTATIONS,annotationColor:e===Xr.N.BASELINE?ha.OVERLAY.toString():ga.OVERLAY.toString(),annotationTitle:`${n} flame graph range`})}),o}static getDiffRange(e){var t,n,r,o;let a,i;const s=null===(r=e.state.body.state.$data)||void 0===r||null===(n=r.state.data)||void 0===n||null===(t=n.annotations)||void 0===t?void 0:t[0];return null==s||s.fields.some((({name:e,values:t})=>(a="time"===e?t[0]:a,i="timeEnd"===e?t[0]:i,a&&i))),[a,i,null===(o=e.state.$timeRange)||void 0===o?void 0:o.state.timeZone]}getAncestorTimeRange(){if(!this.parent||!this.parent.parent)throw new Error(typeof this+" must be used within $timeRange scope");return i.jh.getTimeRange(this.parent.parent)}subscribeToEvents(){const{target:e,timeseriesPanel:t,$timeRange:n}=this.state,r=t.state.body.state.$timeRange,o=this.subscribeToEvent(sa,(e=>{r.setState({mode:e.payload.mode===ca.FLAMEGRAPH?oa.ANNOTATIONS:oa.DEFAULT})})),a=r.subscribeToState(((t,n)=>{this.state.timeRangeSyncEnabled&&t.annotationTimeRange!==n.annotationTimeRange&&this.publishEvent(new Yo({source:e,annotationTimeRange:t.annotationTimeRange}),!0)})),i=n.subscribeToState(((t,n)=>{t.from===n.from&&t.to===n.to||(this.updateTitle(""),this.state.timeRangeSyncEnabled&&this.publishEvent(new Yo({source:e,timeRange:t}),!0))}));return{unsubscribe(){i.unsubscribe(),a.unsubscribe(),o.unsubscribe()}}}buildTimeseriesTitle(){const e=on(this,"profileMetricId"),{description:t}=At(e);return t||Dn(e)}useDiffTimeRange(){return this.state.timeseriesPanel.state.body.state.$timeRange.useState()}applyPreset({from:e,to:t,diffFrom:n,diffTo:r,label:o}){this.setDiffRange({from:n,to:r}),this.setTimeRange(Io(e,t)),this.updateTitle(o)}setTimeRange(e){const{from:t,to:n}=this.state.$timeRange.state.value;t.isSame(e.value.from)&&n.isSame(e.value.to)||this.state.$timeRange.setState({from:e.from,to:e.to,value:e.value})}setDiffRange(e){const t=this.state.timeseriesPanel.state.body.state.$timeRange;if(null===e)return void t.nullifyAnnotationTimeRange();const{annotationTimeRange:n}=t.state,r=t.buildAnnotationTimeRange(e.from,e.to);n.from.isSame(r.from)&&n.to.isSame(r.to)||t.setAnnotationTimeRange(r,!0)}autoSelectDiffRange(e){const{$timeRange:t,target:n}=this.state,{from:r,to:o}=t.state.value;if(this.updateTitle(""),e)return void this.setDiffRange({from:r.toISOString(),to:o.toISOString()});const a=o.diff(r),i=Math.min(Math.round(.25*a),864e5);n===Xr.N.BASELINE?this.setDiffRange({from:r.toISOString(),to:(0,f.dateTime)(r).add(i).toISOString()}):this.setDiffRange({from:(0,f.dateTime)(o).subtract(i).toISOString(),to:o.toISOString()})}updateTitle(e=""){const t=this.state.target===Xr.N.BASELINE?"Baseline":"Comparison",n=e?`${t} (${e})`:t;this.setState({title:n})}toggleTimeRangeSync(e){this.setState({timeRangeSyncEnabled:e})}refreshTimeseries(){this.state.$timeRange.onRefresh()}constructor({target:e,useAncestorTimeRange:t,clearDiffRange:n,filters:r}){const o=e===Xr.N.BASELINE?"filtersBaseline":"filtersComparison",a=e===Xr.N.BASELINE?"Baseline":"Comparison",s=e===Xr.N.BASELINE?ha.COLOR.toString():ga.COLOR.toString();super({key:`${e}-panel`,target:e,filterKey:o,title:a,color:s,$timeRange:new i.JZ(ba({key:`${e}-panel-timerange`},Io("now-1h","now"))),timePicker:new Qo({isOnCanvas:!0}),refreshPicker:new i.WM({isOnCanvas:!0}),timeseriesPanel:va.buildTimeSeriesPanel({target:e,filterKey:o,title:a,color:s}),timeRangeSyncEnabled:!1}),ya(this,"_variableDependency",new i.Sh(this,{variableNames:["profileMetricId"],onReferencedVariableValueChanged:()=>{this.state.timeseriesPanel.updateItem({label:this.buildTimeseriesTitle()})}})),ya(this,"onClickTimeRangeSync",(()=>{const{target:e,timeRangeSyncEnabled:t,$timeRange:n,timeseriesPanel:r}=this.state,o=r.state.body.state.$timeRange;this.publishEvent(new Ho({source:e,enable:!t,timeRange:n.state,annotationTimeRange:o.state.annotationTimeRange}),!0)})),ya(this,"onClickRefresh",(()=>{this.publishEvent(new zo({source:this.state.target}),!0)})),this.addActivationHandler(this.onActivate.bind(this,t,n,r))}}ya(va,"Component",(({model:e})=>{const{target:t,color:n,title:r,timeseriesPanel:l,timePicker:c,refreshPicker:u,filterKey:d,timeRangeSyncEnabled:p}=e.useState(),m=(0,s.useStyles2)(Ea,n),f=i.jh.findByKey(e,d);return o().createElement("div",{className:m.panel,"data-testid":`panel-${t}`},o().createElement("div",{className:m.panelHeader},o().createElement("h6",null,o().createElement("div",{className:m.colorCircle}),r),o().createElement("div",{className:m.timeControls},o().createElement(c.Component,{model:c}),o().createElement("div",{onClick:e.onClickRefresh},o().createElement(u.Component,{model:u})),o().createElement(s.IconButton,{className:(0,a.cx)(m.syncButton,p&&"active"),name:"link","aria-label":p?"Unsync time ranges":"Sync time ranges",tooltip:p?"Unsync time ranges":"Sync time ranges",onClick:e.onClickTimeRangeSync}))),o().createElement("div",{className:m.filter},o().createElement(f.Component,{model:f})),o().createElement("div",{className:m.timeseries},l&&o().createElement(l.Component,{model:l})))}));const Ea=(e,t)=>({panel:a.css`
    background-color: ${e.colors.background.primary};
    padding: ${e.spacing(1)} ${e.spacing(1)} 0 ${e.spacing(1)};
    border: 1px solid ${e.colors.border.weak};
    border-radius: 2px;
  `,panelHeader:a.css`
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: ${e.spacing(2)};
    flex-wrap: wrap;

    & > h6 {
      font-size: 15px;
      height: 32px;
      line-height: 32px;
      margin: 0 ${e.spacing(1)} 0 0;
    }
  `,colorCircle:a.css`
    display: inline-block;
    background-color: ${t};
    border-radius: 50%;
    width: 9px;
    height: 9px;
    margin-right: 6px;
  `,timeControls:a.css`
    display: flex;
    justify-content: flex-end;
    gap: 4px;
  `,syncButton:a.css`
    z-index: unset;
    padding: ${e.spacing(0,1)};
    margin: 0;
    background: ${e.colors.secondary.main};
    border: 1px solid ${e.colors.secondary.border};
    border-radius: ${e.shape.radius.default};

    &:hover {
      background: ${e.colors.secondary.shade};
    }

    &.active {
      color: ${e.colors.primary.text};
      border: 1px solid ${e.colors.primary.text};
    }
  `,filter:a.css`
    display: flex;
    margin-bottom: ${e.spacing(3)};
  `,timeseries:a.css`
    height: 200px;

    & [data-viz-panel-key] > * {
      border: 0 none;
    }

    & [data-viz-panel-key] [data-testid='uplot-main-div'] {
      cursor: crosshair;
    }
  `});var Sa=n(8629);function wa(){const[e,t]=(0,r.useState)(null),[n,o]=(0,r.useState)();return{onOpen(e){o((()=>e))},isOpen:t=>t===e,open(e){t(e),null==n||n()},close(){t(null)}}}var Oa=n(8873),xa=n(8536),Ta=n(7945);function Pa({children:e,delay:t}){const[n,a]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{window.setTimeout((()=>{a(!0)}),t)}),[e,t]),o().createElement(o().Fragment,null,n?e:null)}function Ca({menu:e,title:t,placement:n="bottom",offset:i,dragClassCancel:l,menuButtonClass:c,onVisibleChange:u,onOpenMenu:d}){const p=t?Ta.Tp.components.Panels.Panel.menu(t):"panel-menu-button",m=(0,r.useCallback)((e=>(e&&d&&d(),u)),[d,u]),f=t?`Menu for panel with title ${t}`:"Menu for panel with no title";return o().createElement(s.Dropdown,{overlay:e,placement:n,offset:i,onVisibleChange:m},o().createElement(s.ToolbarButton,{"aria-label":f,title:"Menu",icon:"ellipsis-v",iconSize:"md",narrow:!0,"data-testid":p,className:(0,a.cx)(c,l)}))}function ka({menu:e,title:t,dragClass:n,children:i,offset:l=-32,onOpenMenu:c}){const u=(0,s.useStyles2)(Aa),d=(0,r.useRef)(null),p=Ta.Tp.components.Panels.Panel.HoverWidget,m=(0,r.useCallback)((e=>{var t;null===(t=d.current)||void 0===t||t.setPointerCapture(e.pointerId)}),[]),f=(0,r.useCallback)((e=>{var t;null===(t=d.current)||void 0===t||t.releasePointerCapture(e.pointerId)}),[]),[h,g]=(0,r.useState)(!1);return void 0===i||0===o().Children.count(i)?null:o().createElement("div",{className:(0,a.cx)(u.container,{"show-on-hover":!h}),style:{top:`${l}px`},"data-testid":p.container},n&&o().createElement("div",{className:(0,a.cx)(u.square,u.draggable,n),onPointerDown:m,onPointerUp:f,ref:d,"data-testid":p.dragIcon},o().createElement(s.Icon,{name:"expand-arrows",className:u.draggableIcon})),!t&&o().createElement("h6",{className:(0,a.cx)(u.untitled,{[u.draggable]:!!n},n)},"Untitled"),i,e&&o().createElement(Ca,{menu:e,title:t,placement:"bottom",menuButtonClass:u.menuButton,onVisibleChange:g,onOpenMenu:c}))}function Aa(e){return{hidden:(0,a.css)({visibility:"hidden",opacity:"0"}),container:(0,a.css)({label:"hover-container-widget",transition:"all .1s linear",display:"flex",position:"absolute",zIndex:1,right:0,boxSizing:"content-box",alignItems:"center",background:e.colors.background.secondary,color:e.colors.text.primary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,height:e.spacing(4),boxShadow:e.shadows.z1}),square:(0,a.css)({display:"flex",justifyContent:"center",alignItems:"center",width:e.spacing(4),height:"100%"}),draggable:(0,a.css)({cursor:"move",[e.breakpoints.down("md")]:{display:"none"}}),menuButton:(0,a.css)({background:"inherit",border:"none","&:hover":{background:e.colors.secondary.main}}),untitled:(0,a.css)({color:e.colors.text.disabled,fontStyle:"italic",padding:e.spacing(0,1),marginBottom:0}),draggableIcon:(0,a.css)({transform:"rotate(45deg)",color:e.colors.text.secondary,"&:hover":{color:e.colors.text.primary}})}}function ja(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Na(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ja(e,t,n[t])}))}return e}function Ia(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function _a(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}const Ra=(0,r.forwardRef)(((e,t)=>{var{className:n,children:r,href:i,onClick:l,target:c,title:u}=e,d=_a(e,["className","children","href","onClick","target","title"]);const p=(0,s.useStyles2)(La);return i?o().createElement("a",Na({ref:t,href:i,onClick:l,target:c,title:u,className:(0,a.cx)(p.linkItem,n)},d),r):l?o().createElement(s.Button,{ref:t,className:(0,a.cx)(p.item,n),variant:"secondary",fill:"text",onClick:l},r):o().createElement("span",Na({ref:t,className:(0,a.cx)(p.item,n)},d),r)}));Ra.displayName="TitleItem";const La=e=>{const t=(0,a.css)({color:`${e.colors.text.secondary}`,label:"panel-header-item",cursor:"auto",border:"none",borderRadius:`${e.shape.radius.default}`,padding:`${e.spacing(0,1)}`,height:`${e.spacing(e.components.panel.headerHeight)}`,display:"flex",alignItems:"center",justifyContent:"center","&:focus, &:focus-visible":Ia(Na({},Da(e)),{zIndex:1}),"&: focus:not(:focus-visible)":{outline:"none",boxShadow:"none"},"&:hover ":{boxShadow:`${e.shadows.z1}`,background:`${e.colors.background.secondary}`,color:`${e.colors.text.primary}`}});return{item:t,linkItem:(0,a.cx)(t,(0,a.css)({cursor:"pointer"}))}};function Da(e){return{outline:"2px dotted transparent",outlineOffset:"2px",boxShadow:`0 0 0 2px ${e.colors.background.canvas}, 0 0 0px 4px ${e.colors.primary.main}`,transitionTimingFunction:"cubic-bezier(0.19, 1, 0.22, 1)",transitionDuration:"0.2s",transitionProperty:"outline, outline-offset, box-shadow"}}function Fa({description:e,className:t}){const n=(0,s.useStyles2)($a);return""!==e?o().createElement(s.Tooltip,{interactive:!0,content:()=>{const t="function"==typeof e?e():e;return o().createElement("div",{className:"panel-info-content markdown-html"},o().createElement("div",{dangerouslySetInnerHTML:{__html:t}}))}},o().createElement(Ra,{className:(0,a.cx)(t,n.description)},o().createElement(s.Icon,{name:"info-circle",size:"md"}))):null}const $a=()=>({description:(0,a.css)({code:{whiteSpace:"normal",wordWrap:"break-word"},"pre > code":{display:"block"}})});function Ba({message:e,onClick:t,ariaLabel:n="status"}){const r=(0,s.useStyles2)(Ma);return o().createElement(s.ToolbarButton,{className:r.buttonStyles,onClick:t,variant:"destructive",icon:"exclamation-triangle",iconSize:"md",tooltip:e||"","aria-label":n})}const Ma=e=>{const{headerHeight:t,padding:n}=e.components.panel;return{buttonStyles:(0,a.css)({label:"panel-header-state-button",display:"flex",alignItems:"center",justifyContent:"center",padding:e.spacing(n),width:e.spacing(t),height:e.spacing(t),borderRadius:e.shape.radius.default})}};function Ua({children:e,padding:t="md",title:n="",description:i="",displayMode:l="default",titleItems:c,menu:u,dragClass:d,dragClassCancel:p,hoverHeader:m=!1,hoverHeaderOffset:h,loadingState:g,statusMessage:y,statusMessageOnClick:b,actions:v,onCancelQuery:E,onOpenMenu:S}){const w=(0,s.useTheme2)(),O=(0,s.useStyles2)(Ka),[x,T]=(0,r.useState)(0),P=(0,r.useRef)(null);(0,r.useEffect)((()=>{P.current&&T(P.current.offsetWidth)}),[P]);const C=!m,k=qa(w,C),{contentStyle:A}=Ga(t,w),j={height:k,cursor:d?"move":"auto"},N={};"transparent"===l&&(N.backgroundColor="transparent",N.border="none");const I=n?Ta.Tp.components.Panels.Panel.title(n):"Panel",_=o().createElement(o().Fragment,null,n&&o().createElement("h6",{title:n,className:O.title},n),o().createElement("div",{className:(0,a.cx)(O.titleItems,p),"data-testid":"title-items-container"},o().createElement(Fa,{description:i,className:p}),c),g===f.LoadingState.Streaming&&o().createElement(s.Tooltip,{content:E?"Stop streaming":"Streaming"},o().createElement(Ra,{className:p,"data-testid":"panel-streaming",onClick:E},o().createElement(s.Icon,{name:"circle-mono",size:"md",className:O.streaming}))),g===f.LoadingState.Loading&&E&&o().createElement(Pa,{delay:2e3},o().createElement(s.Tooltip,{content:"Cancel query"},o().createElement(Ra,{className:(0,a.cx)(p,O.pointer),"data-testid":"panel-cancel-query",onClick:E},o().createElement(s.Icon,{name:"sync-slash",size:"md"})))),o().createElement("div",{className:O.rightAligned},v&&o().createElement("div",{className:O.rightActions},Va(v,(e=>e)))));return o().createElement("div",{className:O.container,style:N,"data-testid":I},o().createElement("div",{className:O.loadingBarContainer},g===f.LoadingState.Loading?o().createElement(s.LoadingBar,{width:x,ariaLabel:"Panel loading bar"}):null),m&&o().createElement(o().Fragment,null,o().createElement(ka,{menu:u,title:n,offset:h,dragClass:d,onOpenMenu:S},_),y&&o().createElement("div",{className:O.errorContainerFloating},o().createElement(Ba,{message:y,onClick:b,ariaLabel:"Panel status"}))),C&&o().createElement("div",{className:(0,a.cx)(O.headerContainer,d),style:j,"data-testid":"header-container"},y&&o().createElement("div",{className:p},o().createElement(Ba,{message:y,onClick:b,ariaLabel:"Panel status"})),_,u&&o().createElement(Ca,{menu:u,title:n,placement:"bottom-end",menuButtonClass:(0,a.cx)(O.menuItem,p,"show-on-hover"),onOpenMenu:S})),o().createElement("div",{className:O.content,style:A,ref:P},e))}const Va=(e,t)=>{const n=o().Children.toArray(e).filter(Boolean);return n.length>0?t(n):null},qa=(e,t)=>t?e.spacing.gridSize*e.components.panel.headerHeight:0,Ga=(e,t)=>({contentStyle:{padding:("md"===e?t.components.panel.padding:0)*t.spacing.gridSize}}),Ka=e=>{const{background:t,borderColor:n,padding:r}=e.components.panel;return{container:(0,a.css)({label:"panel-container",backgroundColor:t,border:`1px solid ${n}`,position:"relative",borderRadius:e.shape.radius.default,height:"100%",display:"flex",flexDirection:"column",".show-on-hover":{visibility:"hidden",opacity:"0"},"&:focus-visible, &:hover":{".show-on-hover":{visibility:"visible",opacity:"1"}},"&:focus-visible":{outline:`1px solid ${e.colors.action.focus}`},"&:focus-within":{".show-on-hover":{visibility:"visible",opacity:"1"}}}),loadingBarContainer:(0,a.css)({label:"panel-loading-bar-container",position:"absolute",top:0,width:"100%",overflow:"hidden"}),content:(0,a.css)({label:"panel-content",flexGrow:1}),headerContainer:(0,a.css)({label:"panel-header",display:"flex",alignItems:"center"}),pointer:(0,a.css)({cursor:"pointer"}),streaming:(0,a.css)({label:"panel-streaming",marginRight:0,color:e.colors.success.text,"&:hover":{color:e.colors.success.text}}),title:(0,a.css)({label:"panel-title",marginBottom:0,padding:e.spacing(0,r),textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",fontSize:e.typography.h6.fontSize,fontWeight:e.typography.h6.fontWeight}),items:(0,a.css)({display:"flex"}),item:(0,a.css)({display:"flex",justifyContent:"center",alignItems:"center"}),hiddenMenu:(0,a.css)({visibility:"hidden"}),menuItem:(0,a.css)({label:"panel-menu",border:"none",background:e.colors.secondary.main,"&:hover":{background:e.colors.secondary.shade}}),errorContainerFloating:(0,a.css)({label:"error-container",position:"absolute",left:0,top:0,zIndex:e.zIndex.tooltip}),rightActions:(0,a.css)({display:"flex",padding:e.spacing(0,r),gap:e.spacing(1)}),rightAligned:(0,a.css)({label:"right-aligned-container",marginLeft:"auto",display:"flex",alignItems:"center"}),titleItems:(0,a.css)({display:"flex",height:"100%"})}},Ha=e=>({panelWrap:a.css`
    margin-bottom: ${e.spacing(1)};
  `});function za({isLoading:e,title:t,description:n,children:r,className:a="",headerActions:i,dataTestId:l}){const c=(0,s.useStyles2)(Ha),u=e?f.LoadingState.Loading:f.LoadingState.Done;return o().createElement("div",{className:`${a} ${c.panelWrap}`,"data-testid":l||"panel"},o().createElement(Ua,{loadingState:u,title:t,description:n,actions:i},r))}var Ya=n(9660),Qa=n(1630),Wa=n(7616);function Ja({children:e,onClick:t,disabled:n,interactionName:r}){const a=(0,s.useStyles2)(Xa),{isEnabled:i,error:l,isFetching:c}=function(){const{data:e,isFetching:t,error:n}=(0,Wa.I)({queryKey:["llm"],queryFn:()=>Qa.Sn()});return n&&g.v.error(n,{info:"Error while checking the status of the Grafana LLM plugin!"}),{isEnabled:Boolean(e),isFetching:t,error:n}}();let u="ai",d="";return c?(u="fa fa-spinner",d="Checking the status of the Grafana LLM plugin..."):l?(u="exclamation-triangle",d="Error while checking the status of the Grafana LLM plugin!"):i||(u="shield-exclamation",d="Grafana LLM plugin missing or not configured! Please check the plugins administration page."),o().createElement(s.Button,{className:a.aiButton,size:"md",fill:"text",icon:u,disabled:!i||n,tooltip:d,tooltipPlacement:"top",onClick:e=>{(0,m.r)(r),t(e)}},e)}const Xa=()=>({aiButton:a.css`
    padding: 0 4px;
  `});var Za=n(7879);const ei=e=>{const t=document.querySelector('[placeholder^="Search"]');if(null===t)return void g.v.error(new Error("Cannot find search input element!"));((e,t)=>{const n=Object.getOwnPropertyDescriptor(e,"value").set,r=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(e),"value").set;n&&n!==r?r.call(e,t):n.call(e,t)})(t,e.target.textContent.trim()),t.dispatchEvent(new Event("input",{bubbles:!0}))},ti={overrides:{code:{component:({children:e})=>{const t=(0,s.useStyles2)(ri);return"string"==typeof e&&e.includes("\n")?o().createElement("code",null,e):o().createElement("code",{className:t.searchLink,title:"Search for this node",onClick:ei},e)}}}};function ni({reply:e}){var t;const n=(0,s.useStyles2)(ri);return o().createElement("div",{className:n.container},null==e||null===(t=e.messages)||void 0===t?void 0:t.filter((e=>"system"!==e.role)).map((e=>o().createElement(o().Fragment,null,o().createElement("div",{className:n.reply},o().createElement(Za.Ay,{options:ti},e.content)),o().createElement("hr",null)))),o().createElement("div",{className:n.reply},o().createElement(Za.Ay,{options:ti},e.text)))}const ri=()=>({container:a.css`
    width: 100%;
    height: 100%;
  `,reply:a.css`
    font-size: 13px;

    & ol,
    & ul {
      margin: 0 0 16px 24px;
    }
  `,searchLink:a.css`
    color: rgb(255, 136, 51);
    border: 1px solid transparent;
    padding: 2px 4px;
    cursor: pointer;
    font-size: 13px;

    &:hover,
    &:focus,
    &:active {
      box-sizing: border-box;
      border: 1px solid rgb(255, 136, 51, 0.8);
      border-radius: 4px;
    }
  `}),oi=()=>({textarea:a.css`
    margin-bottom: 8px;
  `,sendButton:a.css`
    float: right;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  `});function ai({onSubmit:e}){const t=(0,s.useStyles2)(oi),{question:n,onChangeInput:a,onClickSend:i}=function(e){const[t,n]=(0,r.useState)(""),o=(0,r.useCallback)((e=>{n(e.target.value)}),[]),a=(0,r.useCallback)((()=>{const r=t.trim();r&&(e(r),n(""))}),[t,e]);return{question:t,onChangeInput:o,onClickSend:a}}(e);return o().createElement("div",null,o().createElement(s.TextArea,{className:t.textarea,placeholder:"Ask a follow-up question...",value:n,onChange:a,onKeyDown:e=>{"Enter"!==e.code||e.shiftKey||i()}}),o().createElement(s.Button,{className:t.sendButton,onClick:i},"Send"))}const ii={system:{empty:()=>"\n    You are a performance profiling expert and excel at analyzing profiles in the DOT format.\n    In the DOT format, a row like N47 -> N61 means the function from N47 called the function from N61.\n"},user:{single:(e,t)=>`\n    Analyze this flamegraph in DOT format and address these key aspects:\n    - **Performance Bottleneck**: Identify the primary factors slowing down the process, consuming excessive memory, or causing a bottleneck in the system.\n    - **Root Cause**: Explain clearly why these bottlenecks are occurring.\n    - **Recommended Fix**: Suggest practical solutions for these issues.\n\n    Guidelines:\n    - Always use full function names without splitting them from package names.\n    - Exclude numeric values, percentages, and node names (e.g., N1, N3, Node 1, Node 2).\n    - Focus on user code over low-level runtime optimizations.\n    - For standard library or runtime functions, explain their presence/function and link them to user code functions calling them. Avoid repetitive mentions from the same call chain.\n    - Do not mention that the flamegraph profile is in DOT format.\n    - Only use h5 and h6 markdown headers (e.g., ##### Performance Bottleneck, ###### Recommended Fix)\n    - Do not use h1,h2,h3,h4 headers (e.g., ## Bottleneck, ### Root Cause, #### Recommended Fix)\n\n    Format the response using markdown headers for each section corresponding to the key aspects.\n\n    The profile type is: ${e}\n    Profile in DOT format:\n    ${t[0]}\n`,anton:(e,t)=>`\nGive me actionable feedback and suggestions on how I improve the application performance.\n\nDo not break function names.\nDo not show any numeric values, absolute or percents.\nDo not show node names like N1, N3, or Node 1, Node 2.\nDo not suggest low-level runtime optimisations, focus on the user code.\n\nAlways use full function names.\nNever split function and package name.\n\nRemove any numeric values, absolute or percents, from the output.\nRemove node names like N1, N3, or Node 1, Node 2 from the output.\n\nIf the function is widely known (e.g., a runtime or stdlib function), provide me concise explanation why the function is present in the profile and what could be the cause.\nIf a function is defined in the runtime or in the standard library, tell me which function in the user code calls it.\nAvoid mentioning functions from the same call-chain.\n\n5 suggestions is enough.\nThe profile type is ${e}\nBelow is the performance profile in DOT format:\n${t[0]}\n`,diff:(e,t)=>`\nAnalyze the differences between these two performance profiles presented in DOT format. Provide a detailed comparison focusing on the following aspects:\n\n- Performance Change: Determine how the performance has changed from the first profile to the second. Identify if there are new bottlenecks, improved or worsened performance areas, or significant changes in resource consumption.\n- Function Impact: Highlight no more than 3 specific functions that have undergone notable changes in their performance impact. Discuss any new functions that have appeared in the second profile or any existing functions that have significantly increased or decreased in resource usage.\n- Potential Causes: Discuss the possible reasons for these changes in performance, linking them to the differences in function execution or resource usage between the two profiles.\n\nGuidelines for Analysis:\n- Use full function names without separating them from their package names\n- Focus on user code rather than low-level runtime optimizations or standard library functions unless they are directly relevant to the user code's performance changes\n- Exclude numeric values, percentages, and node names (e.g., N1, N3, Node 1, Node 2) from the analysis\n- Format the response using markdown headers for each section to structure the analysis clearly\n\nThe profile type is: ${e}\n\nFirst performance profile in DOT format:\n${t[0]}\n\nSecond performance profile in DOT format:\n${t[1]}\n`}},si=({system:e,user:t,profileType:n,profiles:r})=>{const o=ii.system[e];if("function"!=typeof o)throw new Error(`Cannot find system prompt "${e}"!`);const a=ii.user[t];if("function"!=typeof a)throw new Error(`Cannot find user prompt "${t}"!`);return{system:o(n,r),user:a(n,r)}};function li(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}class ci extends A{get(e){var t,n=this;return(t=function*(){const t=new URLSearchParams({query:e.query,from:String(1e3*e.timeRange.from.unix()),until:String(1e3*e.timeRange.to.unix()),format:e.format});e.maxNodes&&t.set("max-nodes",String(e.maxNodes));const r=yield n.fetch(`/pyroscope/render?${t.toString()}`);switch(e.format){case"dot":return r.text();case"json":return r.json();default:throw new TypeError(`Unknown format "${e.format}"!`)}},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){li(a,r,o,i,s,"next",e)}function s(e){li(a,r,o,i,s,"throw",e)}i(void 0)}))})()}constructor(e){super(e)}}function ui(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class di extends i.Bs{validateFetchParams(e,t){let n,r=t;return e&&2!==t.length?(n=new Error(`Invalid number of fetch parameters for analyzing the diff flame graph (${t.length})!`),r=[]):e||1===t.length||(n=new Error(`Invalid number of fetch parameters for analyzing the flame graph (${t.length})!`),r=[]),{params:r,error:n}}constructor(){super({key:"ai-panel"}),ui(this,"useSceneAiPanel",((e,t)=>{const n=i.jh.findByKeyAndType(this,"dataSource",Kt).useState().value,{params:o,error:a}=this.validateFetchParams(e,t),{error:s,isFetching:l,profiles:c}=function(e,t){const n=Do.build(e,ci),{isFetching:r,error:o,data:a}=(0,Wa.I)({queryKey:["dot-profiles",e,...t.flatMap((({query:e,timeRange:t})=>[e,t.from.unix(),t.to.unix()])),100],queryFn:()=>Promise.all(t.map((({query:e,timeRange:t})=>n.get({query:e,timeRange:t,format:"dot",maxNodes:100}).then((e=>e.replace(/fontsize=\d+ /g,"").replace(/id="node\d+" /g,"").replace(/labeltooltip=".*\)" /g,"").replace(/tooltip=".*\)" /g,"").replace(/(N\d+ -> N\d+).*/g,"$1").replace(/N\d+ \[label="other.*\n/,"").replace(/shape=box /g,"").replace(/fillcolor="#\w{6}"/g,"").replace(/color="#\w{6}" /g,""))))))});return{isFetching:r,error:o,profiles:a||[]}}(n,o),u=At(on(this,"profileMetricId")).type,{reply:d,error:p,retry:m}=function(e,t){const[n,o]=(0,r.useState)(""),[a,i]=(0,r.useState)(!1),[s,l]=(0,r.useState)(!1),[c,u]=(0,r.useState)([]),[d,p]=(0,r.useState)(null),[m,f]=(0,r.useState)(),h=(0,r.useCallback)((e=>{u(e),p(null),o(""),i(!0),l(!1);const t=Qa.qH({model:"gpt-4-1106-preview",messages:e}).pipe(Qa.qA()).subscribe({next:o,error(e){p(e),i(!1),l(!0),f(void 0)},complete(){i(!1),l(!0),f(void 0)}});f(t)}),[]),g=(0,r.useCallback)((e=>{const t=[{role:"assistant",content:n},{role:"user",content:e}];try{h([...c,...t])}catch(e){p(e)}}),[c,n,h]);return(0,r.useEffect)((()=>{if(!t.length||c.length>0)return;const n=si({system:"empty",user:2===t.length?"diff":"single",profileType:e,profiles:t});try{h([{role:"system",content:n.system},{role:"system",content:n.user}])}catch(e){p(e)}}),[c.length,e,t,t.length,h]),(0,r.useEffect)((()=>()=>{m&&(m.unsubscribe(),f(void 0))}),[m]),{reply:{text:n,hasStarted:a,hasFinished:s,messages:c,askFollowupQuestion:g},retry(){if(c.length>0)try{h(c)}catch(e){p(e)}},error:d}}(u,c);return{data:{validationError:a,isLoading:l||!l&&!s&&!p&&!d.text.trim(),fetchError:s,llmError:p,reply:d,shouldDisplayReply:Boolean((null==d?void 0:d.hasStarted)||(null==d?void 0:d.hasFinished)),shouldDisplayFollowUpForm:!s&&!p&&Boolean(null==d?void 0:d.hasFinished)},actions:{retry:m,submitFollowupQuestion(e){d.askFollowupQuestion(e)}}}}))}}ui(di,"Component",(({model:e,isDiff:t,fetchParams:n,onClose:r})=>{const a=(0,s.useStyles2)(pi),{data:i,actions:l}=e.useSceneAiPanel(t,n);return o().createElement(za,{className:a.sidePanel,title:"Flame graph analysis",isLoading:i.isLoading,headerActions:o().createElement(s.IconButton,{title:"Close panel",name:"times-circle",variant:"secondary","aria-label":"close",onClick:r}),dataTestId:"ai-panel"},o().createElement("div",{className:a.content},i.validationError&&o().createElement(xa._,{severity:"error",title:"Validation error!",error:i.validationError}),i.fetchError&&o().createElement(xa._,{severity:"error",title:"Error while loading profile data!",message:"Sorry for any inconvenience, please try again later.",error:i.fetchError}),i.shouldDisplayReply&&o().createElement(ni,{reply:i.reply}),i.isLoading&&o().createElement(o().Fragment,null,o().createElement(s.Spinner,{inline:!0})," Analyzing..."),i.llmError&&o().createElement(s.Alert,{title:"An error occured while generating content using OpenAI!",severity:"warning"},o().createElement("div",null,o().createElement("div",null,o().createElement("p",null,i.llmError.message),o().createElement("p",null,"Sorry for any inconvenience, please retry or if the problem persists, contact your organization admin."))),o().createElement(s.Button,{className:a.retryButton,variant:"secondary",fill:"outline",onClick:()=>l.retry()},"Retry")),i.shouldDisplayFollowUpForm&&o().createElement(ai,{onSubmit:l.submitFollowupQuestion})))}));const pi=e=>({sidePanel:a.css`
    flex: 1 0 50%;
    margin-left: 8px;
    max-width: calc(50% - 4px);
  `,title:a.css`
    margin: -4px 0 4px 0;
  `,content:a.css`
    padding: ${e.spacing(1)};
  `,retryButton:a.css`
    float: right;
  `});class mi extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(mi,"type","diff-auto-select");class fi extends f.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(fi,"type","diff-choose-preset");var hi=n(9993);function gi(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}class yi extends A{get(e){var t,n=this;return(t=function*(){const t=new URLSearchParams({leftQuery:e.leftQuery,leftFrom:String(1e3*e.leftTimeRange.from.unix()),leftUntil:String(1e3*e.leftTimeRange.to.unix()),rightQuery:e.rightQuery,rightFrom:String(1e3*e.rightTimeRange.from.unix()),rightUntil:String(1e3*e.rightTimeRange.to.unix())});e.maxNodes&&t.set("max-nodes",String(e.maxNodes));const r=yield n.fetch(`/pyroscope/render-diff?${t.toString()}`);return yield r.json()},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){gi(a,r,o,i,s,"next",e)}function s(e){gi(a,r,o,i,s,"throw",e)}i(void 0)}))})()}constructor(e){super(e)}}function bi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function vi(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function Ei({enabled:e,dataSourceUid:t,baselineTimeRange:n,baselineQuery:r,comparisonTimeRange:o,comparisonQuery:a}){const[i]=(0,hi.I)(),s=Do.build(t,yi),{isFetching:l,error:c,data:u,refetch:d}=(0,Wa.I)({placeholderData:e=>e,enabled:Boolean(e&&i),queryKey:["diff-profile",t,r,n.from.unix(),n.to.unix(),a,o.from.unix(),o.to.unix(),i],queryFn:()=>{s.abort();const e={leftQuery:r,leftTimeRange:n,rightQuery:a,rightTimeRange:o,maxNodes:i};return s.get(e).then((e=>({profile:{version:e.version,flamebearer:e.flamebearer,metadata:e.metadata}})))}});return vi(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){bi(e,t,n[t])}))}return e}({isFetching:l,error:s.isAbortError(c)?null:c},u),{refetch:d})}function Si({onClickAutoSelect:e,onClickChoosePreset:t,onOpenLearnHow:n}){const a=(0,s.useStyles2)(wi),[i,l]=(0,r.useState)(!1);return o().createElement(xa._,{severity:"info",title:"Select both the baseline and the comparison flame graph ranges to view the diff flame graph",message:o().createElement("div",{className:a.infoMsg},o().createElement("p",null,"How?"),o().createElement("p",null,o().createElement(s.Button,{variant:"primary",onClick:e},"Auto-select")," ","or"," ",o().createElement(s.Button,{variant:"primary",fill:"text",className:a.textButton,onClick:t},"choose a preset")),o().createElement("p",null,"Alternatively:"),o().createElement(s.Collapse,{label:"Click here to learn how to select the flame graph ranges with the mouse",collapsible:!0,className:a.collapse,isOpen:i,onToggle:()=>{i||n(),l(!i)}},o().createElement("div",{className:a.collapseContent},o().createElement("ol",null,o().createElement("li",null,"Ensure that the “Flame graph” range selection mode is selected"),o().createElement("li",null,"Use your mouse to select the desired time ranges on both the baseline and the comparison time series")),o().createElement("img",{src:"public/plugins/grafana-pyroscope-app/img/8cdf4d2e2df8326311ab.gif",alt:"How to view the diff flame graph"}))))})}const wi=e=>({infoMsg:a.css`
    padding: ${e.spacing(2)} 0 0 0;
  `,textButton:a.css`
    padding: 0;
  `,collapse:a.css`
    background: transparent;
    border: 0;
  `,collapseContent:a.css`
    padding: 0 ${e.spacing(5)};

    & img {
      max-width: 100%;
      width: auto;
      margin-top: ${e.spacing(2)};
    }
  `});function Oi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class xi extends i.Bs{buildTitle(){const e=on(this,"serviceName"),t=At(on(this,"profileMetricId")).type;return o().createElement(o().Fragment,null,o().createElement(Ya.S,{size:"small"}),"Diff flame graph for ",e," (",t,")")}constructor(){super({key:"diff-flame-graph",aiPanel:new di}),Oi(this,"useSceneDiffFlameGraph",(()=>{const{aiPanel:e}=this.useState(),{baselineTimeRange:t,comparisonTimeRange:n}=this.parent.useDiffTimeRanges(),r=Gt(this,"filtersBaseline"),o=Gt(this,"filtersComparison"),{settings:a,error:s}=(0,Oa._)(),l=i.jh.findByKeyAndType(this,"dataSource",Kt).useState().value,c=Boolean(r&&o&&t.from.unix()&&t.to.unix()&&n.from.unix()&&n.to.unix()),{isFetching:u,error:d,profile:p}=Ei({enabled:c,dataSourceUid:l,baselineTimeRange:t,baselineQuery:r,comparisonTimeRange:n,comparisonQuery:o}),m=c&&!u&&!d&&0===(null==p?void 0:p.flamebearer.numTicks),f=Boolean(c&&!d&&!m&&p),h=!c;return{data:{title:this.buildTitle(),isLoading:u,fetchProfileError:d,noProfileDataAvailable:m,shouldDisplayFlamegraph:f,hasMissingSelections:h,profile:p,settings:a,fetchSettingsError:s,ai:{panel:e,fetchParams:[{query:r,timeRange:t},{query:o,timeRange:n}]}},actions:{}}})),Oi(this,"onClickAutoSelect",(()=>{(0,m.r)("g_pyroscope_app_diff_auto_select_clicked"),this.publishEvent(new mi({wholeRange:!1}),!0)})),Oi(this,"onClickChoosePreset",(()=>{(0,m.r)("g_pyroscope_app_diff_choose_preset_clicked"),this.publishEvent(new fi({}),!0)})),Oi(this,"onOpenLearnHow",(()=>{(0,m.r)("g_pyroscope_app_diff_learn_how_clicked")}))}}Oi(xi,"Component",(({model:e})=>{var t,n;const a=(0,s.useStyles2)(Ti),{data:i}=e.useSceneDiffFlameGraph(),c=wa(),u=i.isLoading||i.hasMissingSelections||i.noProfileDataAvailable;(0,r.useEffect)((()=>{u&&c.close()}),[u,c]),i.fetchSettingsError&&(0,l.HA)(["Error while retrieving the plugin settings!","Some features might not work as expected (e.g. flamegraph export options). Please try to reload the page, sorry for the inconvenience."]);const d=(0,r.useMemo)((()=>o().createElement(o().Fragment,null,i.title,i.isLoading&&o().createElement(s.Spinner,{inline:!0,className:a.spinner}))),[i.isLoading,i.title,a.spinner]);return o().createElement("div",{className:a.flex},o().createElement(za,{dataTestId:"diff-flame-graph-panel",className:a.flamegraphPanel,title:d,isLoading:i.isLoading,headerActions:o().createElement(Ja,{disabled:u||c.isOpen("ai"),onClick:()=>c.open("ai"),interactionName:"g_pyroscope_app_explain_flamegraph_clicked"},"Explain Flame Graph")},i.hasMissingSelections&&o().createElement(Si,{onClickAutoSelect:e.onClickAutoSelect,onClickChoosePreset:e.onClickChoosePreset,onOpenLearnHow:e.onOpenLearnHow}),i.fetchProfileError&&o().createElement(xa._,{severity:"error",title:"Error while loading profile data!",error:i.fetchProfileError}),i.noProfileDataAvailable&&o().createElement(xa._,{severity:"warning",title:"No profile data available",message:"Please verify that you've selected adequate filters and time ranges."}),i.shouldDisplayFlamegraph&&o().createElement(Sa.C,{diff:!0,profile:i.profile,enableFlameGraphDotComExport:null===(t=i.settings)||void 0===t?void 0:t.enableFlameGraphDotComExport,collapsedFlamegraphs:null===(n=i.settings)||void 0===n?void 0:n.collapsedFlamegraphs})),c.isOpen("ai")&&o().createElement(i.ai.panel.Component,{model:i.ai.panel,isDiff:!0,fetchParams:i.ai.fetchParams,onClose:c.close}))}));const Ti=e=>({flex:a.css`
    display: flex;
  `,flamegraphPanel:a.css`
    min-width: 0;
    flex-grow: 1;
  `,sidePanel:a.css`
    flex: 1 0 50%;
    margin-left: 8px;
    max-width: calc(50% - 4px);
  `,spinner:a.css`
    margin-left: ${e.spacing(1)};
  `,aiButton:a.css`
    margin-top: ${e.spacing(1)};
  `}),Pi="https://grafana.qualtrics.com/jfe/form/SV_6Gav4IUU6jcYfd4",Ci=()=>{const e=(0,s.useStyles2)(ki);return o().createElement("div",{className:e.wrapper},o().createElement("a",{href:Pi,className:e.feedback,title:"Share your thoughts about Profiles in Grafana.",target:"_blank",rel:"noreferrer noopener"},o().createElement(s.Icon,{name:"comment-alt-message"})," Give feedback"),o().createElement("a",{href:Pi,className:e.feedback,title:"Share your thoughts about Profiles in Grafana.",target:"_blank",rel:"noreferrer noopener"},o().createElement(s.Badge,{text:"Preview",color:"blue",icon:"rocket"})))},ki=e=>({wrapper:(0,a.css)({display:"flex",gap:e.spacing(1),justifyContent:"flex-end",paddingTop:"4px"}),feedback:(0,a.css)({alignSelf:"center",color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize,"&:hover":{color:e.colors.text.link}})});function Ai(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ji extends i.Bs{onActivate(){[Xr.N.BASELINE,Xr.N.COMPARISON].forEach((e=>{this._subs.add(i.jh.findByKeyAndType(this,`${e}-panel`,va).state.$timeRange.subscribeToState(((e,t)=>{e.from===t.from&&e.to===t.to||this.setState({value:null})})))}))}openSelect(){this.setState({isSelectOpen:!0})}closeSelect(){this.setState({isSelectOpen:!1})}reset(){this.setState({value:null,isSelectOpen:!1,isModalOpen:!1})}static Component({model:e}){const t=(0,s.useStyles2)(Ni),{value:n,isSelectOpen:r,isModalOpen:a}=e.useState();return o().createElement(o().Fragment,null,o().createElement("div",{className:t.presetsContainer},o().createElement(s.Select,{className:t.select,placeholder:"Choose a preset",value:n,options:ji.PRESETS,onChange:e.onChangePreset,isOpen:r,onOpenMenu:e.onOpenSelect,onCloseMenu:e.onCloseSelect}),o().createElement(s.Button,{icon:"save",variant:"secondary",tooltip:"Save the current time ranges and filters as a custom preset",onClick:e.onClickSave})),o().createElement(s.Modal,{title:"Custom user presets",isOpen:a,closeOnEscape:!0,closeOnBackdropClick:!0,onDismiss:e.closeModal},o().createElement("p",null,"This feature, which would allow you to save the current time ranges and filters, is currently not implemented."),o().createElement("p",null,"Please let us know if you would be interested to use it by"," ",o().createElement("a",{href:Pi,target:"_blank",rel:"noreferrer noopener",className:t.link},"leaving us your feedback.")),o().createElement("p",null,"Thank you!"),o().createElement(s.Modal.ButtonRow,null,o().createElement(s.Button,{variant:"secondary",fill:"outline",onClick:e.closeModal},"Cancel"),o().createElement(s.Button,{onClick:e.closeModal,disabled:!0},"Save"))))}constructor(){super({name:"compare-presets",label:"Comparison presets",value:null,isModalOpen:!1,isSelectOpen:!1}),Ai(this,"_variableDependency",new i.Sh(this,{variableNames:["dataSource","serviceName"],onReferencedVariableValueChanged:()=>{this.reset()}})),Ai(this,"onChangePreset",(e=>{var t;if((0,m.r)("g_pyroscope_app_diff_preset_selected",{value:e.value}),this.closeSelect(),"dummy"!==e.value){if(null===(t=e.value)||void 0===t?void 0:t.startsWith("auto-select-"))return this.setState({value:null}),void this.publishEvent(new mi({wholeRange:"auto-select-whole"===e.value}),!0);[Xr.N.BASELINE,Xr.N.COMPARISON].forEach((t=>{const n=i.jh.findByKeyAndType(this,`${t}-panel`,va);n.toggleTimeRangeSync(!1),n.applyPreset(e[t])})),this.setState({value:e.value})}else this.setState({value:null,isModalOpen:!0})})),Ai(this,"onClickSave",(()=>{(0,m.r)("g_pyroscope_app_diff_preset_save_clicked"),this.setState({isModalOpen:!0})})),Ai(this,"closeModal",(()=>{this.setState({isModalOpen:!1})})),Ai(this,"onOpenSelect",(()=>{setTimeout((()=>this.openSelect()),0)})),Ai(this,"onCloseSelect",(()=>{this.closeSelect()})),this.addActivationHandler(this.onActivate.bind(this))}}Ai(ji,"PRESETS",[{label:"Built-in presets",value:"built-in",options:[{value:"last hour (30m-window)",label:"Last hour (30m-window)",baseline:{from:"now-1h",to:"now",diffFrom:"now-1h",diffTo:"now-30m",label:"last hour"},comparison:{from:"now-1h",to:"now",diffFrom:"now-30m",diffTo:"now",label:"last hour"}},{value:"last hour (1h-window)",label:"Last hour (1h-window)",baseline:{from:"now-1h",to:"now",diffFrom:"now-1h",diffTo:"now",label:"last hour"},comparison:{from:"now-1h",to:"now",diffFrom:"now-1h",diffTo:"now",label:"last hour"}},{value:"6h ago vs now",label:"6h ago vs now (30m-window)",baseline:{from:"now-375m",to:"now-315m",diffFrom:"now-375m",diffTo:"now-345m",label:"6h ago"},comparison:{from:"now-1h",to:"now",diffFrom:"now-30m",diffTo:"now",label:"last hour"}},{value:"24h ago vs now",label:"24h ago vs now (30m-window)",baseline:{from:"now-1455m",to:"now-1395m",diffFrom:"now-1455m",diffTo:"now-1425m",label:"24h ago"},comparison:{from:"now-1h",to:"now",diffFrom:"now-30m",diffTo:"now",label:"last hour"}},{value:"auto-select-25",label:"Auto-select (25% range)"},{value:"auto-select-whole",label:"Auto-select (whole range)"}]},{label:"My presets",value:"custom",options:[{label:"Dummy preset saved earlier",value:"dummy"}]}]);const Ni=e=>({presetsContainer:a.css`
    display: flex;
  `,select:a.css`
    min-width: ${e.spacing(24)};
    text-align: left;
  `,link:a.css`
    color: ${e.colors.text.link};
  `});function Ii(){return e=>{const t=new Map,n=e.subscribeToEvent(b,(n=>{var r;const o=null===(r=n.payload.series)||void 0===r?void 0:r[0];(null==o?void 0:o.refId)?(t.set(o.refId,Math.max(...o.fields[1].values)),function(e,t){const n=i.jh.findAllObjects(e,(e=>e instanceof i.Eb&&"timeseries"===e.state.pluginId));for(const e of n)e.clearFieldConfigCache(),e.setState({fieldConfig:(0,y.merge)((0,y.cloneDeep)(e.state.fieldConfig),{defaults:{max:t}})})}(e,Math.max(...t.values()))):g.v.warn("Missing refId! Cannot sync y-axis on the timeseries.",n.payload.series)}));return()=>{n.unsubscribe()}}}class _i extends i.Bs{onActivate(){C.locationService.partial({},!0);const e=i.jh.findByKeyAndType(this,"profileMetricId",Ft);return e.setState({query:Ft.QUERY_SERVICE_NAME_DEPENDENT}),e.update(!0),this.subscribeToEvents(),()=>{e.setState({query:Ft.QUERY_DEFAULT}),e.update(!0)}}subscribeToEvents(){this._subs.add(this.subscribeToEvent(mi,(e=>{const t=e.payload.wholeRange,{baselinePanel:n,comparisonPanel:r}=this.state;n.toggleTimeRangeSync(!1),r.toggleTimeRangeSync(!1),n.autoSelectDiffRange(t),r.autoSelectDiffRange(t)}))),this._subs.add(this.subscribeToEvent(fi,(()=>{this.state.presetsPicker.openSelect()}))),this._subs.add(this.subscribeToEvent(Ho,(e=>{const{source:t,enable:n,timeRange:r,annotationTimeRange:o}=e.payload,{baselinePanel:a,comparisonPanel:i}=this.state,s=t===Xr.N.BASELINE?i:a;n&&this.syncTimeRanges(s,r,o),i.toggleTimeRangeSync(n),a.toggleTimeRangeSync(n)}))),this._subs.add(this.subscribeToEvent(Yo,(e=>{const{source:t,timeRange:n,annotationTimeRange:r}=e.payload,{baselinePanel:o,comparisonPanel:a}=this.state,i=t===Xr.N.BASELINE?a:o;this.syncTimeRanges(i,n,r)}))),this._subs.add(this.subscribeToEvent(zo,(e=>{const{source:t}=e.payload,{baselinePanel:n,comparisonPanel:r}=this.state;(t===Xr.N.BASELINE?r:n).refreshTimeseries()})))}syncTimeRanges(e,t,n){t&&e.setTimeRange(t),n&&e.setDiffRange({from:n.from.toISOString(),to:n.to.toISOString()})}getVariablesAndGridControls(){return{variables:[i.jh.findByKeyAndType(this,"serviceName",Vt),i.jh.findByKeyAndType(this,"profileMetricId",Ft),this.state.presetsPicker],gridControls:[]}}static Component({model:e}){const t=(0,s.useStyles2)(Ri),{baselinePanel:n,comparisonPanel:r,body:a}=e.useState();return o().createElement("div",{className:t.container},o().createElement("div",{className:t.columns},o().createElement(n.Component,{model:n}),o().createElement(r.Component,{model:r})),o().createElement(a.Component,{model:a}))}constructor({useAncestorTimeRange:e,clearDiffRange:t,baselineFilters:n,comparisonFilters:r}){super({key:"explore-diff-flame-graph",baselinePanel:new va({target:Xr.N.BASELINE,useAncestorTimeRange:Boolean(e),clearDiffRange:Boolean(t),filters:n||[]}),comparisonPanel:new va({target:Xr.N.COMPARISON,useAncestorTimeRange:Boolean(e),clearDiffRange:Boolean(t),filters:r||[]}),$behaviors:[new i.Gg.K2({key:"metricCrosshairSync",sync:f.DashboardCursorSync.Crosshair}),Ii()],body:new xi,presetsPicker:new ji}),function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(this,"useDiffTimeRanges",(()=>{const{baselinePanel:e,comparisonPanel:t}=this.state,{annotationTimeRange:n}=e.useDiffTimeRange(),{annotationTimeRange:r}=t.useDiffTimeRange();return{baselineTimeRange:n,comparisonTimeRange:r}})),this.addActivationHandler(this.onActivate.bind(this))}}const Ri=e=>({container:a.css`
    width: 100%;
    display: flex;
    flex-direction: column;
  `,columns:a.css`
    display: flex;
    flex-direction: row;
    gap: ${e.spacing(1)};
    margin-bottom: ${e.spacing(1)};

    & > div {
      flex: 1 1 0;
    }
  `});function Li(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Di(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Li(a,r,o,i,s,"next",e)}function s(e){Li(a,r,o,i,s,"throw",e)}i(void 0)}))}}class Fi extends A{githubLogin(e){var t=this;return Di((function*(){const n=yield t.fetch("/vcs.v1.VCSService/GithubLogin",{method:"POST",body:JSON.stringify({authorizationCode:e})});return yield n.json()}))()}githubApp(){var e=this;return Di((function*(){const t=yield e.fetch("/vcs.v1.VCSService/GithubApp",{method:"POST",body:JSON.stringify({})});return(yield t.json()).clientID}))()}}function $i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Bi{isUserTokenExpired(e=0){return Date.now()>=this.expiry.getTime()-e}static decode(e){if(void 0===e||""===e)return;let t;try{t=atob(e)}catch(e){return void g.v.error(e,{info:"Failed to base64 decode GitSession value"})}const{payload:n,isLegacy:r}=Bi.tryDecode(t);return r?new Bi(e,864e13):new Bi(n.metadata,Number(n.expiry))}static tryDecode(e){try{return{payload:JSON.parse(e),isLegacy:!1}}catch(e){return{payload:void 0,isLegacy:!0}}}constructor(e,t){$i(this,"oauthTokenMetadata",void 0),$i(this,"expiry",void 0),this.oauthTokenMetadata=e,this.expiry=new Date(t)}}function Mi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Ui="pyroscope_git_session";class Vi{getCookie(){return this.syncCookieWithBrowser(),this.sessionCookie}setCookie(e){e.startsWith(`${Ui}=`)||(e=`${Ui}=${e}`);const t=Vi.getCookieFromJar(e,Ui);void 0!==t&&(this.deleteLegacyCookie(),this.rawCookie=t,this.sessionCookie=Bi.decode(t.value),document.cookie=`${e}; path=/`)}deleteCookie(){document.cookie=`${Ui}=; Path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC;`,this.deleteLegacyCookie(),this.rawCookie=void 0,this.sessionCookie=void 0}deleteLegacyCookie(){document.cookie="GitSession=; Path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC;"}syncCookieWithBrowser(){var e,t;const n=Vi.getCookieFromJar(document.cookie,Ui);(null==n?void 0:n.key)===(null===(e=this.rawCookie)||void 0===e?void 0:e.key)&&(null==n?void 0:n.value)===(null===(t=this.rawCookie)||void 0===t?void 0:t.value)||(void 0!==n?this.setCookie(`${n.key}=${n.value}`):this.deleteCookie())}static getCookieFromJar(e,t){return e.split(";").map((e=>{const[t,...n]=e.trim().split("="),r=n.join("=");return{key:t.trim(),value:null==r?void 0:r.trim()}})).find((({key:e})=>e===t))}constructor(){Mi(this,"rawCookie",void 0),Mi(this,"sessionCookie",void 0)}}const qi=new Vi;const Gi=800,Ki=950;function Hi(e,t){const n=function(e,t){const n=new URL("/login/oauth/authorize","https://github.com");return n.searchParams.set("client_id",e),n.searchParams.set("scope","repo"),n.searchParams.set("state",btoa(JSON.stringify({redirect_uri:window.location.origin,nonce:t}))),n.toString()}(e,t),{top:r}=window;var o,a;const i=(null!==(o=null==r?void 0:r.outerWidth)&&void 0!==o?o:0)/2+(null!==(a=null==r?void 0:r.screenX)&&void 0!==a?a:0)-Gi/2;var s,l;const c=(null!==(s=null==r?void 0:r.outerHeight)&&void 0!==s?s:0)/2+(null!==(l=null==r?void 0:r.screenY)&&void 0!==l?l:0)-Ki/2;return window.open(n,"GitHub Login",`toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=${Gi}, height=${Ki}, top=${c}, left=${i}`)}function zi(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Yi(){var e;return e=function*(e,t,n,r,o){if(r&&r.close(),null==n?void 0:n.isUserTokenExpired())try{return void(yield t.refresh())}catch(e){g.v.error(e,{info:"Failed to refresh GitHub user token"}),qi.deleteCookie()}try{o(Hi(yield e.githubApp(),us))}catch(e){(0,l.jx)(e,["Failed to start login flow.",e.message])}},Yi=function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){zi(a,r,o,i,s,"next",e)}function s(e){zi(a,r,o,i,s,"throw",e)}i(void 0)}))},Yi.apply(this,arguments)}function Qi(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Wi(){var e;return e=function*(e,t,n){const r=t.get("code");if(!r)return"";const o=t.get("state");if(!o)throw new Error("Invalid state parameter!");let a;try{a=JSON.parse(atob(o))}catch(e){throw new Error("Invalid state parameter!")}if(a.nonce!==n)throw new Error("Invalid nonce parameter!");return(yield e.githubLogin(r)).cookie},Wi=function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Qi(a,r,o,i,s,"next",e)}function s(e){Qi(a,r,o,i,s,"throw",e)}i(void 0)}))},Wi.apply(this,arguments)}function Ji(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Xi({vcsClient:e,externalWindow:t,setExternalWindow:n,setSessionCookie:o,nonce:a}){(0,r.useEffect)((()=>{const r=function(){var i,s=(i=function*(){if(t&&!t.closed){try{const r=function(e){try{return new URL(e.location.href).searchParams}catch(e){return null}}(t);if(null!==r){const i=yield function(e,t,n){return Wi.apply(this,arguments)}(e,r,a);if(i)return o(i),t.close(),void n(null)}}catch(e){return(0,l.jx)(e,["Error while login in with GitHub!",e.message]),t.close(),void n(null)}window.setTimeout(r,700)}else n(null)},function(){var e=this,t=arguments;return new Promise((function(n,r){var o=i.apply(e,t);function a(e){Ji(o,n,r,a,s,"next",e)}function s(e){Ji(o,n,r,a,s,"throw",e)}a(void 0)}))});return function(){return s.apply(this,arguments)}}();return t&&r(),()=>{t&&(t.close(),n(null))}}),[t,n,o,a,e])}function Zi(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function es(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Zi(a,r,o,i,s,"next",e)}function s(e){Zi(a,r,o,i,s,"throw",e)}i(void 0)}))}}const ts={isLoginInProgress:!1,isLoggedIn:!1,isSessionExpired:!1,login:es((function*(){}))},ns=(0,r.createContext)(ts);function rs(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function os(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){rs(a,r,o,i,s,"next",e)}function s(e){rs(a,r,o,i,s,"throw",e)}i(void 0)}))}}function as(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const is=Object.freeze({sha:"<unknown>",date:void 0,author:{login:"unknown author",avatarURL:""},message:"",URL:""});class ss extends A{getFile(e,t,n,r){var o=this;return os((function*(){return(yield o.postWithRefresh("/vcs.v1.VCSService/GetFile",JSON.stringify({repositoryURL:e,ref:t,localPath:n,rootPath:r}))).json()}))()}getCommits(e){var t=this;return os((function*(){return yield Promise.all(e.map((({repositoryUrl:e,gitRef:n})=>e&&n?t.getCommit(e,n).catch((t=>(g.v.error(t,{info:`Error while fetching commit from repo "${e}" (${n})!'`}),is))):is)))}))()}refresh(){var e=this;return os((function*(){return e.refreshSession()}))()}getCommit(e,t){var n=this;return os((function*(){var r;const o=yield n.postWithRefresh("/vcs.v1.VCSService/GetCommit",JSON.stringify({repositoryURL:e,ref:t})),a=yield o.json();return(r=a).date&&(r.date=new Date(a.date)),a}))()}postWithRefresh(e,t){var n=this;return os((function*(){var r;if(n.isRefreshing)return n.queueRequest(e,t);if(null===(r=n.sessionManager.getCookie())||void 0===r?void 0:r.isUserTokenExpired(ss.BIAS_MS)){n.isRefreshing=!0;try{yield n.refreshSession()}catch(e){n.sessionManager.deleteCookie(),n.flushQueue(e)}n.flushQueue(),n.isRefreshing=!1}return n.post(e,t)}))()}post(e,t){var n=this;return os((function*(){return n.fetch(e,{method:"POST",body:t})}))()}refreshSession(){var e=this;return os((function*(){const t=yield e.fetch("/vcs.v1.VCSService/GithubRefresh",{method:"POST",body:JSON.stringify({})}),n=yield t.json();e.sessionManager.setCookie(n.cookie)}))()}queueRequest(e,t){var n=this;return os((function*(){return new Promise(((r,o)=>{n.pendingQueue.push((a=>{a?o(a):r(n.post(e,t))}))}))}))()}flushQueue(e=void 0){this.pendingQueue.forEach((t=>t(e))),this.pendingQueue=[]}constructor(e){super(e),as(this,"sessionManager",void 0),as(this,"pendingQueue",void 0),as(this,"isRefreshing",void 0),this.sessionManager=qi,this.isRefreshing=!1,this.pendingQueue=[]}}function ls(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function cs(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){ls(a,r,o,i,s,"next",e)}function s(e){ls(a,r,o,i,s,"throw",e)}i(void 0)}))}}as(ss,"BIAS_MS",3e5);const us=btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(32))));function ds({dataSourceUid:e,children:t}){const n=Do.build(e,Fi),a=Do.build(e,ss),[i,s]=(0,r.useState)(ts.isLoginInProgress),[c,u]=function(){const[e,t]=(0,r.useState)(qi.getCookie());return[e,e=>{e?(qi.setCookie(e),t(qi.getCookie())):(qi.deleteCookie(),t(void 0))}]}(),[d,p]=(0,r.useState)();(0,r.useEffect)((()=>{u("")}),[e]),Xi({vcsClient:n,externalWindow:d,setExternalWindow:p,setSessionCookie:u,nonce:us});const m=!!d&&!d.closed;m!==i&&s(m);const f=(0,r.useCallback)(cs((function*(){try{yield function(e,t,n,r,o){return Yi.apply(this,arguments)}(n,a,c,d,p)}catch(e){(0,l.jx)(e,["Failed to login to GitHub",e.message])}})),[n,a,c,d]);return o().createElement(ns.Provider,{value:{isLoginInProgress:i,isLoggedIn:Boolean(c&&!c.isUserTokenExpired()),isSessionExpired:Boolean(null==c?void 0:c.isUserTokenExpired()),login:f}},t)}var ps=n(3062);function ms({filters:e,maxNodes:t}){const n=e?[...e]:[];n.unshift({key:"service_name",operator:"=",value:"$serviceName"});const r=n.map((({key:e,operator:t,value:n})=>`${e}${t}"${n}"`)).join(",");return fn(new i.dt({datasource:Nt,queries:[{refId:"profile",queryType:"profile",profileTypeId:"$profileMetricId",labelSelector:`{${r},$filters}`,maxNodes:t}]}))}var fs=n(9326),hs=(n(8727),n(2249)),gs=n.n(hs),ys=n(585);function bs(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function vs(e,t,n,r){var o,a=arguments.length,i=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(i=(a<3?o(i):a>3?o(t,n,i):o(t,n))||i);return a>3&&i&&Object.defineProperty(t,n,i),i}class Es extends ys.Message{constructor(e,t,n,r,o){super(),bs(this,"profile_typeID",void 0),bs(this,"label_selector",void 0),bs(this,"start",void 0),bs(this,"end",void 0),bs(this,"max_nodes",void 0),this.profile_typeID=e,this.label_selector=t,this.start=n,this.end=r,this.max_nodes=o}}function Ss(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function ws(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Ss(a,r,o,i,s,"next",e)}function s(e){Ss(a,r,o,i,s,"throw",e)}i(void 0)}))}}vs([ys.Field.d(1,"string")],Es.prototype,"profile_typeID",void 0),vs([ys.Field.d(2,"string")],Es.prototype,"label_selector",void 0),vs([ys.Field.d(3,"int64")],Es.prototype,"start",void 0),vs([ys.Field.d(4,"int64")],Es.prototype,"end",void 0),vs([ys.Field.d(5,"int64")],Es.prototype,"max_nodes",void 0);class Os extends A{static buildPprofRequest(e,t,n){const{profileMetricId:r,labelsSelector:o}=mn(e),a=1e3*t.from.unix(),i=1e3*t.to.unix(),s=new Es(r,o,a,i,n);return Es.encode(s).finish()}selectMergeProfile({query:e,timeRange:t,maxNodes:n}){var r=this;return ws((function*(){return(yield r.fetch("/querier.v1.QuerierService/SelectMergeProfile",{method:"POST",headers:{"content-type":"application/proto"},body:new Blob([Os.buildPprofRequest(e,t,n)])})).blob()}))()}selectMergeProfileJson({profileMetricId:e,labelsSelector:t,start:n,end:r,stackTrace:o,maxNodes:a}){var i=this;return ws((function*(){return(yield i.fetch("/querier.v1.QuerierService/SelectMergeProfile",{method:"POST",body:JSON.stringify({profile_typeID:e,label_selector:t,start:1e3*n,end:1e3*r,stackTraceSelector:{call_site:o.map((e=>({name:e})))},maxNodes:a})})).json()}))()}}function xs(e,t){const{serviceId:n,profileMetricId:r}=mn(e),o=`${t.from.format("YYYY-MM-DD_HHmm")}-to-${t.to.format("YYYY-MM-DD_HHmm")}`;return`${n.replace(/\//g,"-")}_${r}_${o}`}function Ts(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}class Ps extends k.Q{upload(e,t){var n,r=this;return(n=function*(){const n=yield r.fetch("/upload/v1",{method:"POST",body:JSON.stringify({name:e,profile:btoa(JSON.stringify(t)),fileTypeData:{units:t.metadata.units,spyName:t.metadata.spyName},type:"json"})});return yield n.json()},function(){var e=this,t=arguments;return new Promise((function(r,o){var a=n.apply(e,t);function i(e){Ts(a,r,o,i,s,"next",e)}function s(e){Ts(a,r,o,i,s,"throw",e)}i(void 0)}))})()}constructor(){super("https://flamegraph.com/api",{"content-type":"application/json"})}}const Cs=new Ps;function ks(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function As(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){ks(a,r,o,i,s,"next",e)}function s(e){ks(a,r,o,i,s,"throw",e)}i(void 0)}))}}function js(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ns extends i.Bs{fetchFlamebearerProfile({dataSourceUid:e,query:t,timeRange:n,maxNodes:r}){return As((function*(){const o=Do.build(e,ci);let a;try{a=yield o.get({query:t,timeRange:n,format:"json",maxNodes:r||fs.a.maxNodes})}catch(e){return(0,l.jx)(e,["Error while loading flamebearer profile data!",e.message]),null}return a}))()}fetchPprofProfile({dataSourceUid:e,query:t,timeRange:n,maxNodes:r}){return As((function*(){const o=Do.build(e,Os);let a;try{const e=yield o.selectMergeProfile({query:t,timeRange:n,maxNodes:r||fs.a.maxNodes});a=yield new Response(e.stream().pipeThrough(new CompressionStream("gzip"))).blob()}catch(e){return(0,l.jx)(e,["Failed to export to pprof!",e.message]),null}return a}))()}constructor(){super({key:"export-flame-graph-menu"}),js(this,"useSceneExportMenu",(({query:e,timeRange:t})=>{const n=i.jh.findByKeyAndType(this,"dataSource",Kt).useState().value,[r]=(0,hi.I)(),{settings:o}=(0,Oa._)();var a=this;const s=function(){var o=As((function*(){(0,m.r)("g_pyroscope_app_export_profile",{format:"json"});const o=yield a.fetchFlamebearerProfile({dataSourceUid:n,query:e,timeRange:t,maxNodes:r});if(!o)return;const i=`${xs(e,t)}.json`,s=`data:text/json;charset=utf-8,${encodeURIComponent(JSON.stringify(o))}`;gs()(s,i)}));return function(){return o.apply(this,arguments)}}();var c=this;const u=function(){var o=As((function*(){(0,m.r)("g_pyroscope_app_export_profile",{format:"pprof"});const o=yield c.fetchPprofProfile({dataSourceUid:n,query:e,timeRange:t,maxNodes:r});if(!o)return;const a=`${xs(e,t)}.pb.gz`;gs()(o,a)}));return function(){return o.apply(this,arguments)}}();var d=this;const p=function(){var o=As((function*(){(0,m.r)("g_pyroscope_app_export_profile",{format:"flamegraph.com"});const o=yield d.fetchFlamebearerProfile({dataSourceUid:n,query:e,timeRange:t,maxNodes:r});if(o)try{const n=yield Cs.upload(xs(e,t),o);if(!n.url)throw new Error("Empty URL received.");const r=document.createElement("a");r.target="_blank",r.href=n.url,document.body.appendChild(r),r.click(),document.body.removeChild(r)}catch(e){return void(0,l.jx)(e,["Failed to export to flamegraph.com!",e.message])}}));return function(){return o.apply(this,arguments)}}();return{data:{shouldDisplayFlamegraphDotCom:Boolean(null==o?void 0:o.enableFlameGraphDotComExport)},actions:{downloadPng:()=>{(0,m.r)("g_pyroscope_app_export_profile",{format:"png"});const n=`${xs(e,t)}.png`;document.querySelector('canvas[data-testid="flameGraph"]').toBlob((e=>{if(e)gs()(e,n);else{const e=new Error("Error while creating the image, no blob.");(0,l.jx)(e,["Failed to export to png!",e.message])}}),"image/png")},downloadJson:s,downloadPprof:u,uploadToFlamegraphDotCom:p}}}))}}function Is(){return(0,r.useContext)(ns)}function _s(e){const{login:t,isSessionExpired:n}=Is(),{settings:o}=(0,Oa._)(),a=null==o?void 0:o.enableFunctionDetails,[i,s]=(0,r.useState)([]),l=(0,r.useCallback)((({item:r},o)=>a&&0!==r.level?[{label:"Function details",icon:"info-circle",onClick:()=>{(0,m.r)("g_pyroscope_app_function_details_clicked"),s(function(e,t){let n=[];const r=t.fields.find((({name:e})=>"label"===e));if(!r)return n;const o=(0,f.getDisplayProcessor)({field:r,theme:(0,f.createTheme)()});let a=e;for(;a&&a.level>0;){var i;for(const e of a.itemIndexes)n.unshift(o(r.values[e]).text);a=null===(i=a.parents)||void 0===i?void 0:i[0]}return n}(r,o)),e.open("function-details"),n&&t()}}]:[]),[a,n,t,e]);return{data:{stacktrace:i},actions:{getExtraFlameGraphMenuItems:l}}}js(Ns,"Component",(({model:e,query:t,timeRange:n})=>{const{actions:r}=e.useSceneExportMenu({query:t,timeRange:n});return o().createElement(s.Dropdown,{overlay:o().createElement(s.Menu,null,o().createElement(s.Menu.Item,{label:"png",onClick:r.downloadPng}),o().createElement(s.Menu.Item,{label:"json",onClick:r.downloadJson}),o().createElement(s.Menu.Item,{label:"pprof",onClick:r.downloadPprof}))},o().createElement(s.Button,{icon:"download-alt",size:"sm",variant:"secondary",fill:"outline","aria-label":"Export profile data",tooltip:"Export profile data"}))}));const Rs="gpt-4-1106-preview",Ls=({functionDetails:e,lines:t})=>{const n=`\nYou are a code optimization expert. I will give you code, each line annotated with amount of time spent on a particular line (it's in the beginning of each line), and a function name.\n\nI want you to write back a new improved code for this function and explain why you made changes.\n\nMake sure to take annotations into strong consideration. If a suggested performance improvement isn't backed up by information from the annotations, do not include it.\n\nDo not mention the actual numbers from the annotations, users can already see how much time was spent on each line. Do not list various lines and their time spent. When you mention functions or lines, do not mention the time spent on them.\n\nIf you can't find any meaningful performance optimizations, say so. Ask for context if you think other context might help make decisions. If you think the problem is with user input and not the actual code itself, say so.\n\nWhen you output code in markdown, please don't specify language after 3 backticks (e.g instead of saying "\`\`\`go" say "\`\`\`"), and always add a new line after 3 backticks.\n\nFunction name is \`${e.name}\`. Do not mention the function name, users can already see it.\n\nWhen posting a response, follow the outline below:\n* give a brief explanation of things that could be improve\n* print new code if it's possible\n* explain each change in more details\n\n\nAnnotated code is below:\n\`\`\`\n${function(e,t){let n=t.map((t=>`(${t.cum} ${e.unit}) ${t.line}`)).join("\n");return n}(e,t)}\n\`\`\`\n`;return{system:"",user:n}};function Ds(e){const{reply:t,error:n}=function(e){const[t,n]=(0,r.useState)(""),[o,a]=(0,r.useState)(!1),[i,s]=(0,r.useState)(!1),[l,c]=(0,r.useState)([]),[u,d]=(0,r.useState)(null),p=(0,r.useCallback)((e=>{c(e),d(null),n(""),a(!0),s(!1),Qa.qH({model:Rs,messages:e}).pipe(Qa.qA()).subscribe({next:n,error(e){d(e),a(!1),s(!0)},complete(){a(!1),s(!0)}})}),[]),m=(0,r.useCallback)((e=>{const n=[{role:"assistant",content:t},{role:"user",content:e}];try{p([...l,...n])}catch(e){d(e)}}),[l,t,p]);return(0,r.useEffect)((()=>{if(l.length>0)return;const t=Ls(e);try{p([{role:"system",content:t.system},{role:"system",content:t.user}])}catch(e){d(e)}}),[l.length,e,p]),{reply:{text:t,hasStarted:o,hasFinished:i,messages:l,askFollowupQuestion:m},error:u}}(e);return{data:{isLoading:!n&&!t.text.trim(),llmError:n,reply:t,shouldDisplayReply:Boolean((null==t?void 0:t.hasStarted)||(null==t?void 0:t.hasFinished)),shouldDisplayFollowUpForm:!n&&Boolean(null==t?void 0:t.hasFinished)},actions:{submitFollowupQuestion(e){t.askFollowupQuestion(e)}}}}const Fs=()=>({title:a.css`
    margin: -4px 0 4px 0;
  `,content:a.css``});function $s({suggestionPromptInputs:e}){const t=(0,s.useStyles2)(Fs),{data:n,actions:r}=Ds(e);return o().createElement(o().Fragment,null,o().createElement("h6",{className:t.title},"Code Optimization Suggestions"),o().createElement("div",{className:t.content},n.isLoading&&o().createElement(o().Fragment,null,o().createElement(s.Spinner,{inline:!0})," Analyzing..."),n.fetchError&&o().createElement(xa._,{severity:"error",title:"Error while fetching profiles!",message:"Sorry for any inconvenience, please try again later."}),n.llmError&&o().createElement(xa._,{severity:"error",title:"Failed to generate content using OpenAI!",error:n.llmError,message:"Sorry for any inconvenience, please try again later or if the problem persists, contact your organization admin."}),n.shouldDisplayReply&&o().createElement(ni,{reply:n.reply}),n.shouldDisplayFollowUpForm&&o().createElement(ai,{onSubmit:r.submitFollowupQuestion})))}function Bs(e,t){let n=e;const r=e.match(/raw\.githubusercontent\.com\/([^/]+)\/([^/]+)\/(.+)/);if(r){const[,e,t,o]=r;n=`https://github.com/${e}/${t}/blob/${o}`}return void 0===t||e.includes("#")||(n+=`#L${t}`),n}const Ms=5;function Us(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Vs(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function qs(e,t){const{isLoggedIn:n}=Is(),{version:o}=t,[a,i]=(0,r.useState)(!1);var s,l,c;const{fileInfo:u,error:d,isFetching:p}=function({enabled:e,dataSourceUid:t,repository:n,gitRef:r,localPath:o,rootPath:a}){const i=Do.build(t,ss),{isFetching:s,error:l,data:c}=(0,Wa.I)({enabled:Boolean(e&&o),queryKey:["vcs-file",n,r,o,a],queryFn:()=>i.getFile(n,r,o,a).then((e=>({content:e.content,URL:e.URL}))).then((e=>({URL:e.URL,content:atob(e.content)})))});return{isFetching:s,error:i.isAbortError(l)?null:l,fileInfo:c}}({enabled:n,dataSourceUid:e,localPath:t.fileName,repository:null!==(s=null==o?void 0:o.repository)&&void 0!==s?s:"",gitRef:null!==(l=null==o?void 0:o.git_ref)&&void 0!==l?l:"",rootPath:null!==(c=null==o?void 0:o.root_path)&&void 0!==c?c:""}),m=(0,r.useMemo)((()=>(null==u?void 0:u.content)?function(e,t){if(!t.size)return[];const n=Array.from(t.values()).sort(((e,t)=>e.line-t.line)),r=e.split("\n"),o=Math.max(0,n[0].line-Ms-1),a=Math.min(r.length,n[n.length-1].line+Ms);return r.slice(o,a).map(((e,n)=>{const r=n+o+1,a=t.get(r);var i,s;return{line:e,number:r,cum:null!==(i=null==a?void 0:a.cum)&&void 0!==i?i:0,flat:null!==(s=null==a?void 0:a.flat)&&void 0!==s?s:0}}))}(u.content,t.callSites):function(e){if(!e.size)return[];const t=Array.from(e.values()).sort(((e,t)=>e.line-t.line)),n=Math.max(0,t[0].line-Ms-1),r=t[t.length-1].line+Ms+1,o=[];for(let t=n+1;t<r;t++){const n=e.get(t);var a,i;o.push({line:void 0,number:t,cum:null!==(a=null==n?void 0:n.cum)&&void 0!==a?a:0,flat:null!==(i=null==n?void 0:n.flat)&&void 0!==i?i:0})}return o}(t.callSites)),[null==u?void 0:u.content,t.callSites]);return{data:{fetchError:d,openAiSuggestions:a,isLoadingCode:p,unit:t.unit,githubUrl:(null==u?void 0:u.URL)?Bs(u.URL,t.startLine):void 0,lines:m.map((e=>{var t;return Vs(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Us(e,t,n[t])}))}return e}({},e),{line:null!==(t=e.line)&&void 0!==t?t:"???"})})),noCodeAvailable:Boolean(d)||!m.some((e=>e.line))},actions:{setOpenAiSuggestions:i}}}function Gs(e){switch(e){case"nanoseconds":return(0,f.getValueFormat)("ns");case"microseconds":return(0,f.getValueFormat)("µs");case"milliseconds":return(0,f.getValueFormat)("ms");case"seconds":return(0,f.getValueFormat)("s");case"count":return(0,f.getValueFormat)("short");default:return(0,f.getValueFormat)(e)}}const Ks=({lines:e,unit:t,githubUrl:n,isLoadingCode:r,noCodeAvailable:i,onOptimizeCodeClick:l})=>{const c=(0,s.useStyles2)(Ws),u=Gs(t),d=e=>{if(e<=0)return".";const t=u(e);return t.suffix?t.text+t.suffix:t.text};zs(e);const[p,m]=e.reduce((([e,t],{flat:n,cum:r})=>[e+n,t+r]),[0,0]);return o().createElement("div",{"data-testid":"function-details-code-container"},o().createElement("div",{className:c.container},o().createElement("div",{className:c.header},o().createElement("div",{className:c.breakdownLabel},o().createElement("h6",null,"Breakdown per line"),o().createElement("span",null,r&&o().createElement(s.Spinner,{inline:!0}),!r&&i&&"(file information unavailable)")),o().createElement("div",{className:c.buttons},o().createElement(s.LinkButton,{disabled:Boolean(r||!n),href:n,target:"_blank",icon:"github",fill:"text"},"View on GitHub"),o().createElement(Ja,{onClick:l,disabled:r||i,interactionName:"g_pyroscope_app_optimize_code_clicked"},"Optimize Code")))),o().createElement("pre",{className:c.codeBlock,"data-testid":"function-details-code"},o().createElement("div",{className:(0,a.cx)(c.highlighted,c.codeBlockHeader)},Hs("Total:",d(p),d(m)," (self, total)")),e.map((({line:e,number:t,cum:n,flat:r})=>o().createElement("div",{key:e+t+n+r,className:r+n>0?c.highlighted:""},Hs(`${t} `,d(r),d(n),e))))))},Hs=(e,t,n,r)=>{const o=e.padStart(7," ")+t.padStart(12," ")+n.padStart(12," ");return r?`${o} ${r}`:o},zs=e=>{if(0===e.length)return;let t=Ys(e[0].line);for(let n=1;n<e.length;n++){const{line:r}=e[n];if(""===r.trim())continue;const o=Ys(r);t=Qs(t,o)}if(t)for(let n=0;n<e.length;n++)e[n].line=e[n].line.substring(t.length)},Ys=e=>{const t=e.match(/^[ \t]*/);var n;return null!==(n=null==t?void 0:t[0])&&void 0!==n?n:""},Qs=(e,t)=>{let n=0;for(let r=0;r<Math.min(e.length,t.length)&&e[r]===t[r];r++)n++;return e.substring(0,n)},Ws=e=>({container:a.css`
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
  `,header:a.css`
    display: flex;
    justify-content: space-between;
    align-items: end;
    width: 100%;
  `,breakdownLabel:a.css`
    & > h6 {
      display: inline-block;
      margin-top: ${e.spacing(1)};
    }

    & > span {
      margin-left: ${e.spacing(1)};
      font-size: ${e.typography.bodySmall.fontSize};
    }

    & > svg {
      margin-left: ${e.spacing(1)};
    }
  `,buttons:a.css`
    display: flex;
    flex-wrap: no-wrap;
  `,codeBlock:a.css`
    position: relative;
    min-height: 240px;
    font-size: 12px;
    overflow-x: auto;
    white-space: pre;
    color: ${e.colors.text.secondary};
  `,highlighted:a.css`
    color: ${e.colors.text.maxContrast};
  `,codeBlockHeader:a.css`
    margin-bottom: 8px;
  `});function Js({dataSourceUid:e,functionDetails:t}){var n,r;const{data:a,actions:i}=qs(e,t);return a.fetchError&&404!==(null===(r=a.fetchError)||void 0===r||null===(n=r.response)||void 0===n?void 0:n.status)&&(0,l.jx)(a.fetchError,["Failed to fetch file information!",a.fetchError.message]),o().createElement(o().Fragment,null,o().createElement(Ks,{lines:a.lines,unit:a.unit,githubUrl:a.githubUrl,isLoadingCode:a.isLoadingCode,noCodeAvailable:a.noCodeAvailable,onOptimizeCodeClick:()=>{var e;i.setOpenAiSuggestions(!0),null===(e=document.getElementById("ai-suggestions-panel"))||void 0===e||e.scrollIntoView({behavior:"smooth"})}}),o().createElement("h6",{id:"ai-suggestions-panel",style:{height:0,marginBottom:0}}),a.openAiSuggestions?o().createElement($s,{suggestionPromptInputs:{functionDetails:t,lines:a.lines}}):null)}const Xs=e=>({ellipsis:a.css`
    color: ${e.colors.primary.text};
    text-overflow: ellipsis;
    overflow: hidden;
    direction: rtl;
    white-space: nowrap;
  `}),Zs=({enableIntegration:e,repository:t})=>{const n=(0,s.useStyles2)(Xs),{isLoginInProgress:r,isLoggedIn:a,login:i}=Is();return e?r?o().createElement(o().Fragment,null,o().createElement(s.Spinner,null),o().createElement("span",null,"Connecting to GitHub...")):a?o().createElement(o().Fragment,null,o().createElement(s.Icon,{name:"github",size:"lg"}),o().createElement("a",{className:n.ellipsis,href:t.commitUrl,target:"_blank",rel:"noreferrer",title:"View commit"},o().createElement(s.Icon,{name:"external-link-alt"})," ",t.commitName)):o().createElement(s.Button,{icon:"github",variant:"primary",onClick:i,tooltip:"Once connected, the GitHub code will be accessible only from this browser session.",tooltipPlacement:"top"},"Connect to ",t.name):o().createElement(o().Fragment,null,"-")};function el(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function tl(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function nl(e,t){const n=e.map((e=>{var n;return tl(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){el(e,t,n[t])}))}return e}({},e.commit),{samples:{unit:null!==(n=e.unit)&&void 0!==n?n:"count",current:Array.from(e.callSites.values()).reduce(((e,{cum:t})=>e+t),0),total:t}})}));return n}const rl="https://github.com/";function ol(e,t){if(!(null==t?void 0:t.repository))return null;const n=t.repository,r=n.replace(rl,""),o=t.git_ref;return{isGitHub:e,url:n,name:r,commitUrl:o?`${n}/commit/${o}`:n,commitName:o?`${r}@${o.substring(0,7)}`:r}}const al=(e,t,n)=>{let r;try{r=n?JSON.parse(e.stringTable[Number(n.buildId)]):void 0}catch(e){}return{name:e.stringTable[Number(t.name)],version:r,startLine:Number.isNaN(Number(t.startLine))?void 0:Number(t.startLine),fileName:e.stringTable[Number(t.filename)],callSites:new Map,unit:e.stringTable[Number(e.sampleType[0].unit)],commit:is}};function il(e,t,n,r,o,a,i){const s=new Set;a.locationId.forEach(((l,c)=>{const u=n.get(l);u&&u.line.forEach((n=>{const d=r.get(n.functionId);if(!d)return;if(t.stringTable[Number(d.name)]!==e)return;if(s.has(l))return;s.add(l);const p=i.get(u.mappingId)||al(t,d,o.get(u.mappingId));i.set(u.mappingId,function(e,t,n,r){const o=Number(t.line),a=e.callSites.get(o)||{line:Number(t.line),flat:0,cum:0},i=0===r?n:0,s=n;return a.flat+=i,a.cum+=s,e.callSites.set(o,a),e}(p,n,Number(a.value[0]),c))}))}))}function sl(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function ll(){var e;return e=function*(e,t){const n=Do.build(e,ss),r=t.map((e=>{var t,n,r;return{repositoryUrl:(null==e||null===(t=e.version)||void 0===t?void 0:t.repository)||"",gitRef:(null==e||null===(n=e.version)||void 0===n?void 0:n.git_ref)||"HEAD",rootPath:(null==e||null===(r=e.version)||void 0===r?void 0:r.root_path)||""}}));return(yield n.getCommits(r)).forEach(((e,n)=>{t[n].commit=e})),t},ll=function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){sl(a,r,o,i,s,"next",e)}function s(e){sl(a,r,o,i,s,"throw",e)}i(void 0)}))},ll.apply(this,arguments)}const cl=e=>Array.from(e.callSites.values()).reduce(((e,{cum:t})=>e+t),0),ul=(e,t)=>cl(t)-cl(e);function dl(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function pl(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){dl(a,r,o,i,s,"next",e)}function s(e){dl(a,r,o,i,s,"throw",e)}i(void 0)}))}}function ml({dataSourceUid:e,query:t,timeRange:n,stackTrace:o}){const{profileMetricId:a,labelsSelector:i}=mn(t),[s,l]=[n.from.unix(),n.to.unix()],{isLoggedIn:c}=Is(),u=Do.build(e,Os),{isFetching:d,error:p,data:m}=(0,Wa.I)({enabled:Boolean(a&&i&&o.length>0&&s>0&&l>0),queryKey:["function-details",a,i,s,l,o,c],queryFn:pl((function*(){const t=yield u.selectMergeProfileJson({profileMetricId:a,labelsSelector:i,start:s,end:l,stackTrace:o,maxNodes:500}),n=function(e,t){var n,r,o,a;const i=new Map,s=new Map(null===(n=t.location)||void 0===n?void 0:n.map((e=>[e.id,e]))),l=new Map(null===(r=t.function)||void 0===r?void 0:r.map((e=>[e.id,e]))),c=new Map(null===(o=t.mapping)||void 0===o?void 0:o.map((e=>[e.id,e])));return null===(a=t.sample)||void 0===a||a.filter((e=>void 0!==e.locationId)).forEach((n=>il(e,t,s,l,c,n,i))),Array.from(i.values())}(o[o.length-1],t).sort(ul);return c?function(e,t){return ll.apply(this,arguments)}(e,n):n}))}),f=(0,r.useMemo)((()=>(null==m?void 0:m.length)?m:[{name:o.at(-1),startLine:void 0,fileName:"",callSites:new Map,unit:"",commit:is}]),[m,o]);return{isFetching:d,error:u.isAbortError(p)?null:p,functionsDetails:f}}const fl=[60,3600,86400,604800,2592e3,31536e3,1/0],hl=["second","minute","hour","day","week","month","year"],gl=new Intl.RelativeTimeFormat("en-US",{numeric:"auto"});const yl=new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"2-digit"});function bl(e){return e?`${yl.format(e)} (${function(e){const t=e.getTime(),n=Math.round((t-Date.now())/1e3),r=fl.findIndex((e=>e>Math.abs(n))),o=r?fl[r-1]:1;return gl.format(Math.floor(n/o),hl[r])}(e)})`:"?"}const vl=e=>({container:a.css`
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  `,firstLine:a.css`
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1em;
  `,sha:a.css`
    font-family: monospace;
  `,sample:a.css`
    font-size: 12px;
  `,secondLine:a.css`
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;
    color: ${e.colors.text.secondary};
  `,avatar:a.css`
    display: inline-block;
    margin-right: 4px;
    border-radius: 50%;
    background: grey;
    width: 16px;
    height: 16px;
  `,message:a.css`
    font-size: 12px;
    color: ${e.colors.text.secondary};
  `});function El({commit:e}){const t=(0,s.useStyles2)(vl),{author:n,samples:r}=e,a=n.login,i=n.avatarURL,l=Gs(r.unit)(r.current),c=Math.round(r.current/r.total*100);return o().createElement("div",{className:t.container},o().createElement("div",{className:t.firstLine},o().createElement("span",{className:t.sha},Tl(e.sha)),o().createElement("span",{className:t.sample},l.text,l.suffix," (",c,"%)")),o().createElement("div",{className:t.secondLine},i&&o().createElement("img",{className:t.avatar,src:i,alt:a}),o().createElement("span",null,a," on ",bl(e.date))),o().createElement("span",{className:t.message},Pl(e.message)))}const Sl=e=>({container:a.css`
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 1em;
  `,sha:a.css`
    font-family: monospace;
  `,message:a.css`
    color: ${e.colors.text.secondary};
  `});function wl({commit:e}){const t=(0,s.useStyles2)(Sl);return o().createElement("div",{className:t.container},o().createElement("span",{className:t.sha},Tl(e.sha)),o().createElement("div",{className:t.message},o().createElement("span",null,Pl(e.message))))}function Ol({commits:e,selectedCommit:t,onChange:n}){return o().createElement(s.Select,{options:e.map((e=>({label:e.sha,value:e}))),value:{label:t.sha,value:t},hideSelectedOptions:!0,isSearchable:!1,noOptionsMessage:"No commits found",formatOptionLabel:xl,onChange:e=>{e.value&&n(e.value)}})}function xl(e,t){var n;const{value:r}=e;if(!r)return null;return(null===(n=t.selectValue[0])||void 0===n?void 0:n.value)===r?o().createElement(wl,{commit:r}):o().createElement(El,{commit:r})}const Tl=e=>e===is.sha?e:e.substring(0,7),Pl=e=>e.split("\n")[0],Cl=({onDismiss:e})=>o().createElement(s.Alert,{severity:"info",title:"Integrate with Github",buttonContent:"Dismiss",onRemove:e},o().createElement("p",null,"This language supports integration with ",o().createElement(s.Icon,{name:"github"})," GitHub."),o().createElement("p",null,"To activate this feature, you will need to add two new labels when sending profiles"," ",o().createElement("code",null,"service_repository")," and ",o().createElement("code",null,"service_ref"),"."," "),o().createElement("p",null,"They should respectively be set to the full repository GitHub URL and the current"," ",o().createElement(s.TextLink,{href:"https://docs.github.com/en/rest/git/refs?apiVersion=2022-11-28#about-git-references",external:!0},"git ref")," ","of the running service."),o().createElement(s.Icon,{name:"document-info"})," ",o().createElement(s.TextLink,{href:"https://grafana.com/docs/grafana-cloud/monitor-applications/profiles/pyroscope-github-integration/",external:!0},"Learn more"));function kl({isLoading:e,children:t}){return e?o().createElement(s.Spinner,{inline:!0}):o().createElement(o().Fragment,null,t)}const Al=(0,r.memo)(kl);function jl(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Nl(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Il extends i.Bs{constructor(){super({key:"function-details-panel"}),Nl(this,"useSceneFunctionDetailsPanel",((e,t)=>{var n,o;const a=i.jh.findByKeyAndType(this,"dataSource",Kt).useState().value,s=Gt(this,"filters"),{functionsDetails:c,error:u,isFetching:d}=ml({dataSourceUid:a,query:s,timeRange:t,stackTrace:e}),[p,m]=(0,r.useState)(),[f,h]=(0,r.useState)(c[0]),[g,y]=(0,r.useState)(Bt.x.has(Bt.x.KEYS.GITHUB_INTEGRATION));c&&p!==c&&(m(c),f!==c[0]&&h(c[0]));const b=((null==f||null===(n=f.version)||void 0===n?void 0:n.repository)||"").startsWith(rl);const v=null==f||null===(o=f.fileName)||void 0===o?void 0:o.endsWith(".go"),E=!g&&!b&&v,S=(0,r.useMemo)((()=>c.map((e=>Array.from(e.callSites.values()).reduce(((e,{cum:t})=>e+t),0))).reduce(((e,t)=>e+t),0)),[c]),w=nl(c,S),O=w.find((({sha:e})=>{var t;return e===(null==f||null===(t=f.commit)||void 0===t?void 0:t.sha)}));return{data:{isLoading:d,fetchFunctionDetailsError:u,functionDetails:f,repository:ol(b,null==f?void 0:f.version),commits:w,selectedCommit:O,isGitHubSupported:v,shouldDisplayGitHubBanner:E,dataSourceUid:a},actions:{selectCommit(e){const t=c.find((({commit:t})=>t.sha===e.sha));h(t)},copyFilePathToClipboard(){return(e=function*(){try{(null==f?void 0:f.fileName)&&(yield navigator.clipboard.writeText(f.fileName),(0,l.qq)(["File path copied to clipboard!"]))}catch(e){}},function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){jl(a,r,o,i,s,"next",e)}function s(e){jl(a,r,o,i,s,"throw",e)}i(void 0)}))})();var e},dismissGitHubBanner(){Bt.x.set(Bt.x.KEYS.GITHUB_INTEGRATION,{}),y(!0)}}}}))}}Nl(Il,"LABEL_WIDTH",16),Nl(Il,"Component",(({model:e,timeRange:t,stackTrace:n,onClose:r})=>{const a=(0,s.useStyles2)(_l),{data:i,actions:l}=e.useSceneFunctionDetailsPanel(n,t);return o().createElement(za,{className:a.sidePanel,title:"Function Details",isLoading:!1,headerActions:o().createElement(s.IconButton,{name:"times-circle",variant:"secondary","aria-label":"close",onClick:r}),dataTestId:"function-details-panel"},o().createElement("div",{className:a.content},i.fetchFunctionDetailsError&&o().createElement(xa._,{severity:"error",title:"Error while fetching function details!",error:i.fetchFunctionDetailsError}),o().createElement("div",{className:a.container},o().createElement("div",{className:a.row,"data-testid":"row-function-name"},o().createElement(s.InlineLabel,{width:Il.LABEL_WIDTH},"Function name"),o().createElement(s.Tooltip,{content:i.functionDetails.name,placement:"top"},o().createElement("span",{className:a.textValue},i.functionDetails.name))),o().createElement("div",{className:a.row,"data-testid":"row-start-line"},o().createElement(s.InlineLabel,{tooltip:"The line where this function definition starts",width:Il.LABEL_WIDTH},"Start line"),o().createElement("span",{className:a.textValue},o().createElement(Al,{isLoading:i.isLoading},void 0!==i.functionDetails.startLine?i.functionDetails.startLine:"-"))),o().createElement("div",{className:a.row,"data-testid":"row-file-path"},o().createElement(s.InlineLabel,{tooltip:"File path where that function is defined",width:Il.LABEL_WIDTH},"File"),o().createElement(Al,{isLoading:i.isLoading},i.functionDetails.fileName?o().createElement(o().Fragment,null,o().createElement(s.Tooltip,{content:i.functionDetails.fileName,placement:"top"},o().createElement("span",{className:a.textValue},"‎","/"===(null==(c=i.functionDetails.fileName)?void 0:c[0])?c.substring(1)+"/":c)),o().createElement(s.IconButton,{name:"clipboard-alt",tooltip:"Copy to clipboard",onClick:l.copyFilePathToClipboard})):"-")),i.shouldDisplayGitHubBanner&&o().createElement("div",{className:a.row,"data-testid":"row-github-banner"},o().createElement(Cl,{onDismiss:l.dismissGitHubBanner})),o().createElement("div",{className:a.row,"data-testid":"row-repository"},o().createElement(s.InlineLabel,{tooltip:"The repository configured for the selected service",width:Il.LABEL_WIDTH},"Repository"),o().createElement(Al,{isLoading:i.isLoading},i.repository?i.repository.isGitHub?o().createElement(Zs,{enableIntegration:i.isGitHubSupported,repository:i.repository}):o().createElement(s.TextLink,{href:i.repository,external:!0},i.repository):"-")),o().createElement("div",{className:a.row,"data-testid":"row-commit"},o().createElement(s.InlineLabel,{width:Il.LABEL_WIDTH,tooltip:"The version of the application (commit) where the function is defined. Use the dropdown menu to target a specific commit."},"Commit"),o().createElement(Al,{isLoading:i.isLoading},o().createElement(Ol,{commits:i.commits,selectedCommit:i.selectedCommit,onChange:l.selectCommit})))),o().createElement(Js,{dataSourceUid:i.dataSourceUid,functionDetails:i.functionDetails})));var c}));const _l=e=>({sidePanel:a.css`
    flex: 1 0 50%;
    margin-left: 8px;
    max-width: calc(50% - 4px);
  `,title:a.css`
    margin: -4px 0 4px 0;
  `,content:a.css`
    padding: ${e.spacing(1)};
  `,container:a.css`
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  `,row:a.css`
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-bottom: 10px;
    > * {
      margin-right: 10px !important;
    }
  `,textValue:a.css`
    // hack to have the ellipsis appear at the start of the string
    direction: rtl;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  `});function Rl(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ll extends i.Bs{onActivate(){let e;const t=this.subscribeToState(((t,n)=>{var r;t.$data!==n.$data&&(e&&e.unsubscribe(),e=null===(r=t.$data)||void 0===r?void 0:r.subscribeToState((e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===f.LoadingState.Done&&this.setState({lastTimeRange:e.data.timeRange})})))}));return()=>{t.unsubscribe(),null==e||e.unsubscribe()}}buildTitle(){const e=on(this,"serviceName"),t=At(on(this,"profileMetricId")).type;return o().createElement(o().Fragment,null,o().createElement(Ya.S,{size:"small"}),"Flame graph for ",e," (",t,")")}constructor(){super({key:"flame-graph",$data:new i.dt({datasource:Nt,queries:[]}),lastTimeRange:void 0,exportMenu:new Ns,aiPanel:new di,functionDetailsPanel:new Il}),Rl(this,"useSceneFlameGraph",(()=>{var e,t,n;const{isLight:o}=(0,s.useTheme2)(),a=(0,r.useMemo)((()=>()=>(0,f.createTheme)({colors:{mode:o?"light":"dark"}})),[o]),[i]=(0,hi.I)(),{settings:c,error:u}=(0,Oa._)(),{$data:d,lastTimeRange:p,exportMenu:m,aiPanel:h,functionDetailsPanel:g}=this.useState();u&&(0,l.HA)(["Error while retrieving the plugin settings!","Some features might not work as expected (e.g. collapsed flame graphs). Please try to reload the page, sorry for the inconvenience."]),(0,r.useEffect)((()=>{i&&this.setState({$data:ms({maxNodes:i})})}),[i]);const y=d.useState(),b=(null==y||null===(e=y.data)||void 0===e?void 0:e.state)===f.LoadingState.Loading,v=null==y||null===(n=y.data)||void 0===n||null===(t=n.series)||void 0===t?void 0:t[0],E=Number(null==v?void 0:v.length)>1,S=Gt(this,"filters");return{data:{title:this.buildTitle(),isLoading:b,isFetchingProfileData:b,hasProfileData:E,profileData:v,settings:c,export:{menu:m,query:S,timeRange:p},ai:{panel:h,fetchParams:[{query:S,timeRange:p}]},gitHub:{panel:g,timeRange:p}},actions:{getTheme:a}}})),this.addActivationHandler(this.onActivate.bind(this))}}Rl(Ll,"Component",(({model:e})=>{var t;const n=(0,s.useStyles2)(Dl),{data:a,actions:i}=e.useSceneFlameGraph(),l=wa(),c=_s(l),u=a.isLoading||!a.hasProfileData;(0,r.useEffect)((()=>{u&&l.close()}),[u,l]);const d=(0,r.useMemo)((()=>o().createElement(o().Fragment,null,a.title,a.isLoading&&o().createElement(s.Spinner,{inline:!0,className:n.spinner}))),[a.isLoading,a.title,n.spinner]);return o().createElement("div",{className:n.flex},o().createElement(za,{dataTestId:"flame-graph-panel",className:n.flamegraphPanel,title:d,isLoading:a.isLoading,headerActions:o().createElement(Ja,{disabled:u||l.isOpen("ai"),onClick:()=>l.open("ai"),interactionName:"g_pyroscope_app_explain_flamegraph_clicked"},"Explain Flame Graph")},o().createElement(ps.A,{data:a.profileData,disableCollapsing:!(null===(t=a.settings)||void 0===t?void 0:t.collapsedFlamegraphs),getTheme:i.getTheme,getExtraContextMenuButtons:c.actions.getExtraFlameGraphMenuItems,extraHeaderElements:o().createElement(a.export.menu.Component,{model:a.export.menu,query:a.export.query,timeRange:a.export.timeRange}),keepFocusOnDataChange:!0})),l.isOpen("ai")&&o().createElement(a.ai.panel.Component,{model:a.ai.panel,fetchParams:a.ai.fetchParams,onClose:l.close}),l.isOpen("function-details")&&o().createElement(a.gitHub.panel.Component,{model:a.gitHub.panel,timeRange:a.gitHub.timeRange,stackTrace:c.data.stacktrace,onClose:l.close}))}));const Dl=e=>({flex:a.css`
    display: flex;
  `,flamegraphPanel:a.css`
    min-width: 0;
    flex-grow: 1;
  `,spinner:a.css`
    margin-left: ${e.spacing(1)};
  `});class Fl extends i.Bs{onActivate(e){e&&this.initVariables(e);const t=i.jh.findByKeyAndType(this,"profileMetricId",Ft);return t.setState({query:Ft.QUERY_SERVICE_NAME_DEPENDENT}),t.update(!0),()=>{t.setState({query:Ft.QUERY_DEFAULT}),t.update(!0)}}initVariables(e){const{serviceName:t,profileMetricId:n,filters:r}=e.queryRunnerParams;if(t){i.jh.findByKeyAndType(this,"serviceName",Vt).changeValueTo(t)}if(n){i.jh.findByKeyAndType(this,"profileMetricId",Ft).changeValueTo(n)}if(r){i.jh.findByKeyAndType(this,"filters",rn).setState({filters:r})}}getVariablesAndGridControls(){return{variables:[i.jh.findByKeyAndType(this,"serviceName",Vt),i.jh.findByKeyAndType(this,"profileMetricId",Ft),i.jh.findByKeyAndType(this,"filters",rn)],gridControls:[]}}static Component({model:e}){const t=(0,s.useStyles2)($l),{mainTimeseries:n,body:r}=e.useState();return o().createElement("div",{className:t.flex},o().createElement("div",{className:t.mainTimeseries},o().createElement(n.Component,{model:n})),o().createElement(r.Component,{model:r}))}constructor({item:e}){super({key:"explore-service-flame-graph",mainTimeseries:new Wr({item:e,headerActions:e=>[new jr({type:"view-labels",item:e}),new gr({item:e})]}),body:new Ll}),this.addActivationHandler(this.onActivate.bind(this,e))}}const $l=e=>({flex:a.css`
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: ${e.spacing(1)};
  `,mainTimeseries:a.css`
    height: ${Wr.MIN_HEIGHT}px;
  `});var Bl=n(2932),Ml=n(1159),Ul=n(4137);function Vl(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function ql(){return Gl.apply(this,arguments)}function Gl(){var e;return e=function*(){(0,m.r)("g_pyroscope_app_share_link_clicked");try{yield navigator.clipboard.writeText(function(){const e=new URL(window.location.toString()),{searchParams:t}=e;return t.get("from")||t.set("from",_o().from),t.get("to")||t.set("to",_o().to),["from","to","from-2","to-2","from-3","to-3","diffFrom","diffTo","diffFrom-2","diffTo-2"].forEach((e=>{const n=t.get(e);n&&t.set(e,String(f.dateMath.parse(n).valueOf()))})),e}().toString()),(0,l.qq)(["Link copied to clipboard!"])}catch(e){g.v.error(e,{info:"Error while creating the shareable link!"})}},Gl=function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Vl(a,r,o,i,s,"next",e)}function s(e){Vl(a,r,o,i,s,"throw",e)}i(void 0)}))},Gl.apply(this,arguments)}function Kl({options:e,value:t,onChange:n}){const i=(0,s.useStyles2)(Hl),l=e.findIndex((e=>e.value===t));return o().createElement("div",{className:i.explorationTypeContainer,"data-testid":"exploration-types"},o().createElement("div",{className:i.label},"Exploration"),o().createElement("div",{className:i.breadcrumb},e.map(((c,u)=>{const d=t===c.value,p=(m=u,l===e.length-1?m===l?"primary":"secondary":m<=l?"primary":"secondary");var m;const f=[d&&"active","primary"===p&&"primary"];return o().createElement(r.Fragment,{key:c.value},o().createElement(s.Button,{className:(0,a.cx)(i.button,...f),variant:p,size:"sm","aria-label":c.label,icon:c.icon,onClick:d?h.f:()=>n(c.value),tooltip:c.description,tooltipPlacement:"top","data-testid":d?"is-active":void 0},c.label),u<e.length-3&&o().createElement("div",{className:l!==e.length-1&&u<=l-1?(0,a.cx)(i.arrow,"arrow",...f):i.arrow}))}))))}const Hl=e=>({explorationTypeContainer:a.css`
    display: flex;
    align-items: center;
  `,label:a.css`
    display: flex;
    gap: 2px;
    align-items: center;
    font-size: 14px;
    margin-right: ${e.spacing(1)};

    ${e.breakpoints.down("xxl")} {
      display: none;
    }
  `,breadcrumb:a.css`
    display: flex;
    align-items: center;
    height: 32px;
    line-height: 32px;

    .active {
      background-color: ${e.colors.primary.main};
    }

    .arrow.primary {
      background-color: ${e.colors.primary.main};
    }

    & button.primary:not(.active),
    & .arrow.primary:not(.active) {
      opacity: 0.7;
    }

    & button.primary:not(.active):hover {
      opacity: 1;
      background-color: ${e.colors.primary.main};
    }
  `,button:a.css`
    height: 27px;
    line-height: 27px;
    border-radius: 15px;

    &:hover {
      border-color: ${e.colors.primary.main};
    }

    &.active:hover {
      cursor: default;
      background-color: ${e.colors.primary.main};
    }

    &:nth-last-child(2) {
      margin-left: ${e.spacing(1)};
    }

    &:nth-last-child(1) {
      margin-left: ${e.spacing(2)};
    }
  `,arrow:a.css`
    background-color: ${e.colors.text.disabled};
    width: 10px;
    height: 2px;
  `});function zl(e){const t=null===C.useChromeHeaderHeight||void 0===C.useChromeHeaderHeight?void 0:(0,C.useChromeHeaderHeight)(),n=(0,s.useStyles2)(Yl,null!=t?t:0),{data:i,actions:l}=function({explorationType:e,controls:t,body:n,$variables:o,onChangeExplorationType:a}){const[i,s]=e===Wl.DIFF_FLAME_GRAPH?[]:t,l=o.state.variables[0],c=null==n?void 0:n.state.primary;if("function"!=typeof c.getVariablesAndGridControls)throw new Error(`Error while rendering "${c.constructor.name}": the "getVariablesAndGridControls" method is missing! Please implement it.`);const{variables:u,gridControls:d}=c.getVariablesAndGridControls(),p=l.useState().value,f=(0,Ml.useNavigate)();return{data:{explorationType:e,dataSourceVariable:l,timePickerControl:i,refreshPickerControl:s,sceneVariables:u,gridControls:d,body:n,dataSourceUid:p},actions:{onChangeExplorationType:a,onClickShareLink:ql,onClickAdHoc:(0,r.useCallback)((()=>{(0,m.r)("g_pyroscope_app_upload_ad_hoc_clicked"),f(`${Ul.Gy}${Ul.bw.ADHOC}`,{state:{referrer:window.location.href}})}),[f]),onClickUserSettings:(0,r.useCallback)((()=>{(0,m.r)("g_pyroscope_app_user_settings_clicked"),f(`${Ul.Gy}${Ul.bw.SETTINGS}`,{state:{referrer:window.location.href}})}),[f])}}}(e),{explorationType:c,dataSourceVariable:u,timePickerControl:d,refreshPickerControl:p,sceneVariables:f,gridControls:h}=i;return o().createElement("div",{className:n.header,"data-testid":"allControls"},o().createElement(Ci,null),o().createElement("div",{className:n.appControls,"data-testid":"appControls"},o().createElement("div",{className:n.appControlsLeft},o().createElement(Kl,{options:Jl.EXPLORATION_TYPE_OPTIONS,value:c,onChange:l.onChangeExplorationType})),o().createElement("div",{className:n.appControlsRight},d&&o().createElement(d.Component,{key:d.state.key,model:d}),p&&o().createElement(p.Component,{key:p.state.key,model:p}),o().createElement("div",{className:n.appMiscButtons},o().createElement(s.IconButton,{name:"upload",tooltip:"Upload ad hoc profiles",onClick:l.onClickAdHoc}),o().createElement(s.IconButton,{name:"cog",tooltip:"View/edit tenant settings",onClick:l.onClickUserSettings}),o().createElement(s.IconButton,{name:"share-alt",tooltip:"Copy shareable link to the clipboard",onClick:l.onClickShareLink}),o().createElement(Bl.U,null)))),o().createElement("div",{id:`scene-controls-${c}`,className:n.sceneControls,"data-testid":"sceneControls"},o().createElement(s.Field,{label:u.state.label,className:(0,a.cx)(n.sceneVariable,u.state.name),"data-testid":u.state.name},o().createElement(u.Component,{model:u})),f.map((e=>o().createElement(s.Field,{key:e.state.name,label:"Filters"===e.state.label?o().createElement("div",{className:n.sceneVariableLabel},o().createElement(s.Icon,{name:"filter",className:n.icon}),e.state.label):e.state.label,className:(0,a.cx)(n.sceneVariable,e.state.name),"data-testid":e.state.name},o().createElement(e.Component,{model:e})))),h.map((e=>o().createElement(s.Field,{key:e.state.key,id:e.state.key,className:n.gridControl,label:""},o().createElement(e.Component,{model:e}))))))}const Yl=(e,t)=>({header:a.css`
    background-color: ${e.colors.background.canvas};
    position: sticky;
    top: ${t}px;
    z-index: 1;
    padding-bottom: ${e.spacing(2)};
  `,appControls:a.css`
    display: flex;
    padding: ${e.spacing(1)} 0;
    justify-content: space-between;
    gap: ${e.spacing(2)};
  `,appControlsLeft:a.css`
    display: flex;
    gap: ${e.spacing(1)};
  `,appControlsRight:a.css`
    display: flex;
    gap: ${e.spacing(1)};
  `,appMiscButtons:a.css`
    display: flex;
    align-items: center;
    gap: 4px;
    border: 1px solid ${e.colors.border.weak};
    background-color: ${e.colors.background.secondary};
    height: 32px;
    padding: 0 ${e.spacing(1)};

    & svg {
      width: 18px;
      height: 18px;
    }
  `,sceneControls:a.css`
    display: flex;
    flex-wrap: wrap;
    gap: ${e.spacing(1)};
    padding: 0;
    margin-top: 20px;
  `,sceneVariable:a.css`
    display: flex;
    margin-bottom: 0;

    & #dataSource {
      width: ${e.spacing(32)};
    }

    &.filters {
      flex-grow: 1;
    }

    &.compare-presets {
      margin-left: auto;
      text-align: right;
    }
  `,sceneVariableLabel:a.css`
    font-size: 12px;
    font-weight: 500;
    line-height: 15px;
    height: 15px;
    margin-bottom: 4px;
    color: ${e.colors.text.primary};
    max-width: 480px;
  `,icon:a.css`
    display: inline-block;
    margin-right: 4px;
  `,gridControl:a.css`
    margin-bottom: 0;

    &#quick-filter {
      flex: 1;
      min-width: 112px;
    }
  `});function Ql(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Wl=function(e){return e.ALL_SERVICES="all",e.PROFILE_TYPES="profiles",e.LABELS="labels",e.FLAME_GRAPH="flame-graph",e.DIFF_FLAME_GRAPH="diff-flame-graph",e.FAVORITES="favorites",e}({});class Jl extends i.Bs{onActivate(){const e=this.subscribeToVariableChanges(),t=this.subscribeToEvents();return this.state.explorationType||this.setExplorationType({type:Jl.DEFAULT_EXPLORATION_TYPE}),()=>{t.unsubscribe(),e.unsubscribe()}}getUrlState(){return{explorationType:this.state.explorationType}}updateFromUrl(e){if("string"==typeof e.explorationType&&e.explorationType!==this.state.explorationType){const t=e.explorationType;this.setExplorationType({type:Object.values(Wl).includes(t)?t:Jl.DEFAULT_EXPLORATION_TYPE})}}registerRuntimeDataSources(){try{i.Go.registerRuntimeDataSource({dataSource:new Ko}),i.Go.registerRuntimeDataSource({dataSource:new mr}),i.Go.registerRuntimeDataSource({dataSource:new Nn})}catch(e){const{message:t}=e;/A runtime data source with uid (.+) has already been registered/.test(t)||(0,l.jx)(e,["Fail to register all the runtime data sources!","The application cannot work as expected, please try reloading the page or if the problem persists, contact your organization admin."])}}subscribeToVariableChanges(){const e=i.jh.findByKeyAndType(this,"dataSource",Kt).subscribeToState(((e,t)=>{e.value&&e.value!==t.value&&rn.resetAll(this)})),t=i.jh.findByKeyAndType(this,"serviceName",Vt).subscribeToState(((e,t)=>{e.value&&e.value!==t.value&&rn.resetAll(this)}));return{unsubscribe(){t.unsubscribe(),e.unsubscribe()}}}subscribeToEvents(){const e=this.subscribeToEvent(Tr,(e=>{this.setExplorationType({type:"profiles",comesFromUserAction:!0,item:e.payload.item})})),t=this.subscribeToEvent(xr,(e=>{this.setExplorationType({type:"labels",comesFromUserAction:!0,item:e.payload.item})})),n=this.subscribeToEvent(Or,(e=>{this.setExplorationType({type:"flame-graph",comesFromUserAction:!0,item:e.payload.item})})),r=this.subscribeToEvent(Jr,(e=>{const{useAncestorTimeRange:t,clearDiffRange:n,baselineFilters:r,comparisonFilters:o}=e.payload;this.setExplorationType({type:"diff-flame-graph",comesFromUserAction:!0,bodySceneOptions:{useAncestorTimeRange:t,clearDiffRange:n,baselineFilters:r,comparisonFilters:o}})}));return{unsubscribe(){r.unsubscribe(),n.unsubscribe(),t.unsubscribe(),e.unsubscribe()}}}setExplorationType({type:e,comesFromUserAction:t,item:n,bodySceneOptions:r}){t&&(c(),this.resetVariables(e)),this.setState({explorationType:e,body:this.buildBodyScene(e,n,r)})}resetVariables(e){i.jh.findByKeyAndType(this,"quick-filter",sr).reset(),i.jh.findByKeyAndType(this,"groupBy",zr).changeValueTo(zr.DEFAULT_VALUE),i.jh.findByKeyAndType(this,"panel-type-switcher",ln).reset(),["labels","flame-graph","diff-flame-graph"].includes(e)||i.jh.findByKeyAndType(this,"filters",rn).reset()}buildBodyScene(e,t,n){let r;switch(e){case"profiles":r=new No({item:t});break;case"labels":r=new jo({item:t});break;case"flame-graph":r=new Fl({item:t});break;case"diff-flame-graph":r=new _i(n||{});break;case"favorites":r=new Mr;break;default:r=new Ir}return new i.n1({direction:"column",primary:r})}static Component({model:e}){const t=(0,s.useStyles2)(Xl),{data:n,actions:r}=e.useProfilesExplorer(),{explorationType:a,controls:i,body:l,$variables:c,dataSourceUid:u}=n;return o().createElement(ds,{dataSourceUid:u},o().createElement(zl,{explorationType:a,controls:i,body:l,$variables:c,onChangeExplorationType:r.onChangeExplorationType}),o().createElement("div",{className:t.body,"data-testid":"sceneBody"},l&&o().createElement(l.Component,{model:l})))}constructor(){super({key:"profiles-explorer",explorationType:void 0,body:void 0,$timeRange:new i.JZ(_o()),$variables:new i.Pj({variables:[new Kt,new Vt,new Ft,new rn({key:"filters"}),new rn({key:"filtersBaseline"}),new rn({key:"filtersComparison"}),new zr]}),controls:[new i.KE({isOnCanvas:!0}),new i.WM({isOnCanvas:!0})],gridControls:[new sr({placeholder:""}),new ln,new rr,new ar]}),Ql(this,"_urlSync",new i.So(this,{keys:["explorationType"]})),Ql(this,"onChangeExplorationType",(e=>{(0,m.r)("g_pyroscope_app_exploration_type_clicked",{explorationType:e}),this.setExplorationType({type:e,comesFromUserAction:!0})})),Ql(this,"useProfilesExplorer",(()=>{const{explorationType:e,controls:t,body:n,$variables:r}=this.useState();return{data:{explorationType:e,controls:t,body:n,$variables:r,dataSourceUid:r.state.variables[0].useState().value},actions:{onChangeExplorationType:this.onChangeExplorationType}}})),(0,i.Is)().initSync(this),this.registerRuntimeDataSources(),this.addActivationHandler(this.onActivate.bind(this))}}Ql(Jl,"EXPLORATION_TYPE_OPTIONS",[{value:"all",label:"All services",description:"Overview of all services, for any given profile type"},{value:"profiles",label:"Profile types",description:"Overview of all the profile types for a single service"},{value:"labels",label:"Labels",description:"Single service label exploration and filtering"},{value:"flame-graph",label:"Flame graph",description:"Single service flame graph"},{value:"diff-flame-graph",label:"Diff flame graph",description:"Compare the differences between two flame graphs"},{value:"favorites",label:"Favorites",description:"Overview of favorited visualizations",icon:"favorite"}]),Ql(Jl,"DEFAULT_EXPLORATION_TYPE",Jl.EXPLORATION_TYPE_OPTIONS[0].value);const Xl=()=>({body:a.css`
    position: relative;
    z-index: 0;
    background: transparent;
  `});function Zl(){const e=(0,r.useMemo)((()=>new Jl),[]);return o().createElement(e.Component,{model:e})}},9897:(e,t,n)=>{n.d(t,{N:()=>r});var r=function(e){return e.BASELINE="baseline",e.COMPARISON="comparison",e}({})},8629:(e,t,n)=>{n.d(t,{C:()=>A});var r=n(7781),o=n(3062),a=n(2007),i=n(5959),s=n.n(i),l=n(2673),c=n(7907),u=(n(8727),n(2249)),d=n.n(u),p=n(9090);function m(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}class f extends p.Q{upload(e,t){var n,r=this;return(n=function*(){const n=yield r.fetch("/upload/v1",{method:"POST",body:JSON.stringify({name:e,profile:btoa(JSON.stringify(t)),fileTypeData:{units:t.metadata.units,spyName:t.metadata.spyName},type:"json"})});return yield n.json()},function(){var e=this,t=arguments;return new Promise((function(r,o){var a=n.apply(e,t);function i(e){m(a,r,o,i,s,"next",e)}function s(e){m(a,r,o,i,s,"throw",e)}i(void 0)}))})()}constructor(){super("https://flamegraph.com/api",{"content-type":"application/json"})}}const h=new f;var g=n(9897);const y=new Intl.DateTimeFormat("fr-CA",{year:"numeric",month:"2-digit",day:"2-digit",hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"});function b(e){const t=y.formatToParts(e).reduce(((e,{type:t,value:n})=>(e[t]=n,e)),{});return`${t.year}-${t.month}-${t.day}_${t.hour}${t.minute}`}function v(e){const t=new Date(Math.round(1e3*e.from.unix())),n=new Date(Math.round(1e3*e.to.unix()));return`${b(t)}-to-${b(n)}`}function E(e){const[t,n]=e===g.N.BASELINE?["diffFrom","diffTo"]:["diffFrom-2","diffTo-2"],o=new URLSearchParams(window.location.search),a=o.get(t),i=o.get(n);return{raw:{from:a,to:i},from:(0,r.dateTimeParse)(a),to:(0,r.dateTimeParse)(i)}}function S(e){const t=["baseline",v(E(g.N.BASELINE)),"comparison",v(E(g.N.COMPARISON))];return e?[e,...t].join("_"):["flamegraph",...t].join("_")}function w(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function O({profile:e,enableFlameGraphDotComExport:t}){const n=function(){var t,n=(t=function*(){(0,c.r)("g_pyroscope_app_export_profile",{format:"flamegraph.com"});const t=S(e.metadata.appName);let n;try{n=yield h.upload(t,e)}catch(e){return void(0,l.jx)(e,["Failed to export to flamegraph.com!",e.message])}const r=document.createElement("a");r.target="_blank",r.href=n.url,document.body.appendChild(r),r.click(),document.body.removeChild(r)},function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(e){w(a,r,o,i,s,"next",e)}function s(e){w(a,r,o,i,s,"throw",e)}i(void 0)}))});return function(){return n.apply(this,arguments)}}();return{data:{shouldDisplayFlamegraphDotCom:Boolean(t)},actions:{downloadPng:()=>{(0,c.r)("g_pyroscope_app_export_profile",{format:"png"});const t=`${S(e.metadata.appName)}.png`;document.querySelector('canvas[data-testid="flameGraph"]').toBlob((e=>{if(e)d()(e,t);else{const e=new Error("No Blob, the image cannot be created.");(0,l.jx)(e,["Failed to export to png!",e.message])}}),"image/png")},downloadJson:()=>{(0,c.r)("g_pyroscope_app_export_profile",{format:"json"});const t=`${S(e.metadata.appName)}.json`,n=`data:text/json;charset=utf-8,${encodeURIComponent(JSON.stringify(e))}`;try{d()(n,t)}catch(e){return void(0,l.jx)(e,["Failed to export to JSON!",e.message])}},uploadToFlamegraphDotCom:n}}}function x(e){const{actions:t}=O(e);return s().createElement(a.Menu,null,s().createElement(a.Menu.Item,{label:"png",onClick:t.downloadPng}),s().createElement(a.Menu.Item,{label:"json",onClick:t.downloadJson}))}function T(e){const{profile:t,enableFlameGraphDotComExport:n}=e;return s().createElement(a.Dropdown,{overlay:s().createElement(x,{profile:t,enableFlameGraphDotComExport:n})},s().createElement(a.Button,{icon:"download-alt",size:"sm",variant:"secondary",fill:"outline","aria-label":"Export profile data",tooltip:"Export profile data"}))}const P=(0,i.memo)(T);function C(e,t,n){const r=[],o=n?7:4;for(let a=0;a<e.length;a+=o)r.push({level:0,label:n?t[e[a+6]]:t[e[a+3]],offset:e[a],val:e[a+1],self:e[a+2],selfRight:n?e[a+5]:0,valRight:n?e[a+4]:0,valTotal:n?e[a+1]+e[a+4]:e[a+1],offsetRight:n?e[a+3]:0,offsetTotal:n?e[a]+e[a+3]:e[a],children:[]});return r}function k({profile:e,diff:t,vertical:n,enableFlameGraphDotComExport:l,collapsedFlamegraphs:c,getExtraContextMenuButtons:u}){const{isLight:d}=(0,a.useTheme2)(),p=(0,i.useMemo)((()=>function(e,t,n,o){if(!e.length)return;const a=[];for(let n=0;n<e.length;n++){a[n]=[];for(const r of C(e[n],t,o))if(r.level=n,a[n].push(r),n>0){const e=a[n].slice(0,-1).reduce(((e,t)=>t.offsetTotal+t.valTotal+e),0)+r.offsetTotal,t=a[n-1];let o=0;for(const n of t){const t=o+n.offsetTotal,a=t+n.valTotal;if(t<=e&&a>e){n.children.push(r);break}o+=n.offsetTotal+n.valTotal}}}const i=[a[0][0]],s=[],l=[],c=[],u=[],d=[],p=[];for(;i.length;){const e=i.shift();s.push(e.label),l.push(e.level),c.push(e.self),u.push(e.val),d.push(e.selfRight),p.push(e.valRight),i.unshift(...e.children)}let m="short";switch(n){case"samples":case"trace_samples":case"lock_nanoseconds":case"nanoseconds":m="ns";break;case"bytes":m="bytes"}const f=[{name:"level",values:l},{name:"label",values:s,type:r.FieldType.string},{name:"self",values:c,config:{unit:m}},{name:"value",values:u,config:{unit:m}}];o&&f.push({name:"selfRight",values:d,config:{unit:m}},{name:"valueRight",values:p,config:{unit:m}});const h={name:"response",meta:{preferredVisualisationType:"flamegraph"},fields:f};return(0,r.createDataFrame)(h)}(e.flamebearer.levels,e.flamebearer.names,e.metadata.units,Boolean(t))),[e,t]);return s().createElement(o.A,{data:p,disableCollapsing:!c,extraHeaderElements:s().createElement(P,{profile:e,enableFlameGraphDotComExport:l}),vertical:n,getTheme:()=>(0,r.createTheme)({colors:{mode:d?"light":"dark"}}),getExtraContextMenuButtons:u,keepFocusOnDataChange:!0})}const A=(0,i.memo)(k)},2673:(e,t,n)=>{n.d(t,{HA:()=>c,jx:()=>l,qq:()=>u});var r=n(7781),o=n(8531),a=n(2096);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function l(e,t){const n=t.reduce(((e,t,n)=>s(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){i(e,t,n[t])}))}return e}({},e),{[`info${n+1}`]:t})),{handheldBy:"displayError"});a.v.error(e,n),(0,o.getAppEvents)().publish({type:r.AppEvents.alertError.name,payload:t})}function c(e){a.v.warn(e),(0,o.getAppEvents)().publish({type:r.AppEvents.alertWarning.name,payload:e})}function u(e){(0,o.getAppEvents)().publish({type:r.AppEvents.alertSuccess.name,payload:e})}},7907:(e,t,n)=>{n.d(t,{r:()=>c});var r=n(8531),o=n(4137),a=n(5176);const i=o.bw.EXPLORE.slice(1);function s(){const{pathname:e}=new URL(window.location.toString());return e.split("/").pop()||""}function l(){const e={appRelease:r.config.apps[o.R2].version,appVersion:a.t,page:s()};return e.page===i&&(e.view=new URLSearchParams(window.location.search).get("explorationType")||""),e}function c(e,t){(0,r.reportInteraction)(e,{props:t,meta:l()})}},9993:(e,t,n)=>{n.d(t,{I:()=>l});var r=n(2673),o=n(9326),a=n(8873),i=n(2096),s=n(1159);function l(){const{searchParams:e,pushNewUrl:t}=function(){const e=(0,s.useNavigate)(),t=(0,s.useLocation)();return{searchParams:new URLSearchParams(t.search),pushNewUrl:t=>{const n=new URLSearchParams(window.location.search);for(const[e,r]of Object.entries(t))n.set(e,r);e({search:n.toString()},{replace:!0})}}}();var n;const l=Number(null!==(n=e.get("maxNodes"))&&void 0!==n?n:""),c=e=>{t({maxNodes:String(e)})};return function(e,t){const{isFetching:n,error:s,settings:l}=(0,a._)({enabled:!e});if(!e&&!n)s?((0,r.HA)(["Error while retrieving the plugin settings!","Some features might not work as expected (e.g. flame graph max nodes). Please try to reload the page, sorry for the inconvenience."]),i.v.error(s),t(o.a.maxNodes)):t(l.maxNodes)}(l>0,c),[l,c]}},9326:(e,t,n)=>{n.d(t,{a:()=>r});const r=Object.freeze({collapsedFlamegraphs:!1,maxNodes:16384,enableFlameGraphDotComExport:!0,enableFunctionDetails:!0})},8873:(e,t,n)=>{n.d(t,{_:()=>f});var r,o,a,i=n(7616),s=n(6667),l=n(9326),c=n(5656);function u(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function d(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){u(a,r,o,i,s,"next",e)}function s(e){u(a,r,o,i,s,"throw",e)}i(void 0)}))}}class p extends c.O{get(){var e=this,t=()=>super.fetch;return d((function*(){return t().call(e,"/settings.v1.SettingsService/Get",{method:"POST",body:JSON.stringify({})}).then((e=>e.json())).then((e=>{var t;const n=null===(t=e.settings)||void 0===t?void 0:t.find((({name:e})=>e===p.PLUGIN_SETTING_NAME));return n?JSON.parse(n.value):{}}))}))()}set(e){var t=this,n=()=>super.fetch;return d((function*(){return n().call(t,"/settings.v1.SettingsService/Set",{method:"POST",body:JSON.stringify({setting:{name:p.PLUGIN_SETTING_NAME,value:JSON.stringify(e)}})}).then((e=>e.json()))}))()}}a="pluginSettings",(o="PLUGIN_SETTING_NAME")in(r=p)?Object.defineProperty(r,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[o]=a;const m=new p;function f({enabled:e}={}){const{isFetching:t,error:n,data:r}=(0,i.I)({enabled:e,queryKey:["settings"],queryFn:()=>m.get().then((e=>Object.keys(l.a).reduce(((e,t)=>{var n,r,o;return null!==(o=(n=e)[r=t])&&void 0!==o||(n[r]=l.a[t]),e}),e)))}),{mutateAsync:o}=(0,s.n)({mutationFn:e=>m.set(e),networkMode:"always"});return{isFetching:t,error:m.isAbortError(n)?null:n,settings:r,mutate:o}}}}]);
//# sourceMappingURL=75.js.map