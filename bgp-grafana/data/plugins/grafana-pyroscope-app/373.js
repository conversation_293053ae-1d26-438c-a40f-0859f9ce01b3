"use strict";(self.webpackChunkgrafana_pyroscope_app=self.webpackChunkgrafana_pyroscope_app||[]).push([[373],{9373:(e,t,n)=>{n.r(t),n.d(t,{default:()=>v});var r=n(6089),s=n(2007),o=n(2673),a=n(1049),i=n(5959),l=n.n(i),c=n(9993),u=n(9326),p=n(8873),h=n(1159),d=n(4137);function m(e,t,n,r,s,o,a){try{var i=e[o](a),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,s)}function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){f(e,t,n[t])}))}return e}function b(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function y(){var e;const{settings:t,error:n,mutate:r}=(0,p._)(),[s,a]=(0,c.I)(),[l,f]=(0,i.useState)(null!=t?t:u.a),y=(0,h.useNavigate)(),v=(0,h.useLocation)(),O=(0,i.useRef)(null===(e=v.state)||void 0===e?void 0:e.referrer);return(0,i.useEffect)((()=>{t&&f(t)}),[t]),{data:b(g({},l),{fetchError:n}),actions:{toggleCollapsedFlamegraphs(){f((e=>b(g({},e),{collapsedFlamegraphs:!e.collapsedFlamegraphs})))},updateMaxNodes(e){f((t=>b(g({},t),{maxNodes:Number(e.target.value)})))},toggleEnableFlameGraphDotComExport(){f((e=>b(g({},e),{enableFlameGraphDotComExport:!e.enableFlameGraphDotComExport})))},toggleEnableFunctionDetails(){f((e=>b(g({},e),{enableFunctionDetails:!e.enableFunctionDetails})))},saveSettings(){return(e=function*(){a(l.maxNodes);try{yield r(l),(0,o.qq)(["Plugin settings successfully saved!"])}catch(e){(0,o.jx)(e,["Error while saving the plugin settings!","Please try again later, sorry for the inconvenience."])}},function(){var t=this,n=arguments;return new Promise((function(r,s){var o=e.apply(t,n);function a(e){m(o,r,s,a,i,"next",e)}function i(e){m(o,r,s,a,i,"throw",e)}a(void 0)}))})();var e},goBack(){if(!O.current)return void y(`${d.Gy}${d.bw.EXPLORE}`);const e=new URL(O.current);s&&e.searchParams.set("maxNodes",String(s)),y(`${e.pathname}${e.search}`)}}}}function v(){const e=(0,s.useStyles2)(O),{data:t,actions:n}=y();return t.fetchError&&(0,o.jx)(t.fetchError,["Error while retrieving the plugin settings!","Please try to reload the page, sorry for the inconvenience."]),l().createElement(l().Fragment,null,l().createElement(a.s,{title:"Profiles settings (tenant)"}),l().createElement("form",{className:e.settingsForm,onSubmit:function(e){e.preventDefault(),n.saveSettings()}},l().createElement(l().Fragment,null,l().createElement(s.FieldSet,{label:"Flame graph","data-testid":"flamegraph-settings"},l().createElement(s.InlineFieldRow,null,l().createElement(s.InlineField,{label:"Collapsed flame graphs",labelWidth:24},l().createElement(s.InlineSwitch,{label:"Toggle collapsed flame graphs",name:"collapsed-flamegraphs",value:t.collapsedFlamegraphs,onChange:n.toggleCollapsedFlamegraphs}))),l().createElement(s.InlineFieldRow,null,l().createElement(s.InlineField,{label:"Maximum number of nodes",tooltip:"",labelWidth:24},l().createElement(s.Input,{name:"max-nodes",type:"number",min:"1",value:t.maxNodes,onChange:n.updateMaxNodes})))),l().createElement(s.FieldSet,{label:"Function details","data-testid":"function-details-settings"},l().createElement(s.InlineFieldRow,null,l().createElement(s.InlineField,{label:"Enable function details",labelWidth:24,tooltip:l().createElement("div",{className:e.tooltip},l().createElement("p",null,"The function details feature enables mapping of resource usage to lines of source code. If the GitHub integration is configured, then the source code will be downloaded from GitHub."),l().createElement("p",null,l().createElement("a",{href:"https://grafana.com/docs/grafana-cloud/monitor-applications/profiles/pyroscope-github-integration/",target:"_blank",rel:"noreferrer noopener"},"Learn more"))),interactive:!0},l().createElement(s.InlineSwitch,{label:"Toggle function details",name:"function-details-feature",value:t.enableFunctionDetails,onChange:n.toggleEnableFunctionDetails})))),l().createElement("div",{className:e.buttons},l().createElement(s.Button,{variant:"primary",type:"submit"},"Save settings"),l().createElement(s.Button,{variant:"secondary",onClick:n.goBack,"aria-label":"Back to Explore Profiles"},"Back to Explore Profiles")))))}const O=e=>({settingsForm:r.css`
    & > fieldset {
      border: 0 none;
      border-bottom: 1px solid ${e.colors.border.weak};
      padding-left: 0;
    }

    & > fieldset > legend {
      font-size: ${e.typography.h4.fontSize};
    }
  `,buttons:r.css`
    display: flex;
    gap: ${e.spacing(1)};
  `,tooltip:r.css`
    p {
      margin: ${e.spacing(1)};
    }

    a {
      color: ${e.colors.text.link};
    }

    em {
      font-style: normal;
      font-weight: ${e.typography.fontWeightBold};
    }
  `})},2673:(e,t,n)=>{n.d(t,{HA:()=>c,jx:()=>l,qq:()=>u});var r=n(7781),s=n(8531),o=n(2096);function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function l(e,t){const n=t.reduce(((e,t,n)=>i(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){a(e,t,n[t])}))}return e}({},e),{[`info${n+1}`]:t})),{handheldBy:"displayError"});o.v.error(e,n),(0,s.getAppEvents)().publish({type:r.AppEvents.alertError.name,payload:t})}function c(e){o.v.warn(e),(0,s.getAppEvents)().publish({type:r.AppEvents.alertWarning.name,payload:e})}function u(e){(0,s.getAppEvents)().publish({type:r.AppEvents.alertSuccess.name,payload:e})}},9993:(e,t,n)=>{n.d(t,{I:()=>l});var r=n(2673),s=n(9326),o=n(8873),a=n(2096),i=n(1159);function l(){const{searchParams:e,pushNewUrl:t}=function(){const e=(0,i.useNavigate)(),t=(0,i.useLocation)();return{searchParams:new URLSearchParams(t.search),pushNewUrl:t=>{const n=new URLSearchParams(window.location.search);for(const[e,r]of Object.entries(t))n.set(e,r);e({search:n.toString()},{replace:!0})}}}();var n;const l=Number(null!==(n=e.get("maxNodes"))&&void 0!==n?n:""),c=e=>{t({maxNodes:String(e)})};return function(e,t){const{isFetching:n,error:i,settings:l}=(0,o._)({enabled:!e});if(!e&&!n)i?((0,r.HA)(["Error while retrieving the plugin settings!","Some features might not work as expected (e.g. flame graph max nodes). Please try to reload the page, sorry for the inconvenience."]),a.v.error(i),t(s.a.maxNodes)):t(l.maxNodes)}(l>0,c),[l,c]}},9326:(e,t,n)=>{n.d(t,{a:()=>r});const r=Object.freeze({collapsedFlamegraphs:!1,maxNodes:16384,enableFlameGraphDotComExport:!0,enableFunctionDetails:!0})},8873:(e,t,n)=>{n.d(t,{_:()=>m});var r,s,o,a=n(7616),i=n(6667),l=n(9326),c=n(5656);function u(e,t,n,r,s,o,a){try{var i=e[o](a),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,s)}function p(e){return function(){var t=this,n=arguments;return new Promise((function(r,s){var o=e.apply(t,n);function a(e){u(o,r,s,a,i,"next",e)}function i(e){u(o,r,s,a,i,"throw",e)}a(void 0)}))}}class h extends c.O{get(){var e=this,t=()=>super.fetch;return p((function*(){return t().call(e,"/settings.v1.SettingsService/Get",{method:"POST",body:JSON.stringify({})}).then((e=>e.json())).then((e=>{var t;const n=null===(t=e.settings)||void 0===t?void 0:t.find((({name:e})=>e===h.PLUGIN_SETTING_NAME));return n?JSON.parse(n.value):{}}))}))()}set(e){var t=this,n=()=>super.fetch;return p((function*(){return n().call(t,"/settings.v1.SettingsService/Set",{method:"POST",body:JSON.stringify({setting:{name:h.PLUGIN_SETTING_NAME,value:JSON.stringify(e)}})}).then((e=>e.json()))}))()}}o="pluginSettings",(s="PLUGIN_SETTING_NAME")in(r=h)?Object.defineProperty(r,s,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[s]=o;const d=new h;function m({enabled:e}={}){const{isFetching:t,error:n,data:r}=(0,a.I)({enabled:e,queryKey:["settings"],queryFn:()=>d.get().then((e=>Object.keys(l.a).reduce(((e,t)=>{var n,r,s;return null!==(s=(n=e)[r=t])&&void 0!==s||(n[r]=l.a[t]),e}),e)))}),{mutateAsync:s}=(0,i.n)({mutationFn:e=>d.set(e),networkMode:"always"});return{isFetching:t,error:d.isAbortError(n)?null:n,settings:r,mutate:s}}},6667:(e,t,n)=>{n.d(t,{n:()=>p});var r=n(5959),s=n(9304),o=n(3403),a=n(7794),i=n(9106),l=class extends a.Q{#e;#t=void 0;#n;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#s()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,i.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#n,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,i.EN)(t.mutationKey)!==(0,i.EN)(this.options.mutationKey)?this.reset():"pending"===this.#n?.state.status&&this.#n.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#n?.removeObserver(this)}onMutationUpdate(e){this.#s(),this.#o(e)}getCurrentResult(){return this.#t}reset(){this.#n?.removeObserver(this),this.#n=void 0,this.#s(),this.#o()}mutate(e,t){return this.#r=t,this.#n?.removeObserver(this),this.#n=this.#e.getMutationCache().build(this.#e,this.options),this.#n.addObserver(this),this.#n.execute(e)}#s(){const e=this.#n?.state??(0,s.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#o(e){o.j.batch((()=>{if(this.#r&&this.hasListeners()){const t=this.#t.variables,n=this.#t.context;"success"===e?.type?(this.#r.onSuccess?.(e.data,t,n),this.#r.onSettled?.(e.data,null,t,n)):"error"===e?.type&&(this.#r.onError?.(e.error,t,n),this.#r.onSettled?.(void 0,e.error,t,n))}this.listeners.forEach((e=>{e(this.#t)}))}))}},c=n(3715),u=n(908);function p(e,t){const n=(0,c.jE)(t),[s]=r.useState((()=>new l(n,e)));r.useEffect((()=>{s.setOptions(e)}),[s,e]);const a=r.useSyncExternalStore(r.useCallback((e=>s.subscribe(o.j.batchCalls(e))),[s]),(()=>s.getCurrentResult()),(()=>s.getCurrentResult())),i=r.useCallback(((e,t)=>{s.mutate(e,t).catch(u.l)}),[s]);if(a.error&&(0,u.G)(s.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:i,mutateAsync:a.mutate}}}}]);
//# sourceMappingURL=373.js.map