/*! For license information please see module.js.LICENSE.txt */
/* [create-plugin] version: 5.11.1 */
define(["@emotion/css","@grafana/data","@grafana/runtime","@grafana/ui","d3","lodash","module","react","react-dom","react-router","rxjs"],((e,t,n,r,i,s,a,o,c,u,l)=>(()=>{var d,p,f={5438:(e,t,n)=>{"use strict";var r;n.d(t,{$b:()=>r,HT:()=>s,Ic:()=>i}),function(e){e.TRACE="trace",e.DEBUG="debug",e.INFO="info",e.LOG="log",e.WARN="warn",e.ERROR="error"}(r||(r={}));const i=r.LOG,s=[r.TRA<PERSON>,r.DE<PERSON>,r.<PERSON>,r.<PERSON>,r.<PERSON>,r.<PERSON>R]},1664:e=>{"use strict";var t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable;e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(e){return!1}}()?Object.assign:function(e,i){for(var s,a,o=function(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),c=1;c<arguments.length;c++){for(var u in s=Object(arguments[c]))n.call(s,u)&&(o[u]=s[u]);if(t){a=t(s);for(var l=0;l<a.length;l++)r.call(s,a[l])&&(o[a[l]]=s[a[l]])}}return o}},362:(e,t,n)=>{"use strict";var r=n(6441);function i(){}function s(){}s.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,s,a){if(a!==r){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:i};return n.PropTypes=n,n}},2688:(e,t,n)=>{e.exports=n(362)()},6441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},7383:e=>{var t="undefined"!=typeof Element,n="function"==typeof Map,r="function"==typeof Set,i="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function s(e,a){if(e===a)return!0;if(e&&a&&"object"==typeof e&&"object"==typeof a){if(e.constructor!==a.constructor)return!1;var o,c,u,l;if(Array.isArray(e)){if((o=e.length)!=a.length)return!1;for(c=o;0!=c--;)if(!s(e[c],a[c]))return!1;return!0}if(n&&e instanceof Map&&a instanceof Map){if(e.size!==a.size)return!1;for(l=e.entries();!(c=l.next()).done;)if(!a.has(c.value[0]))return!1;for(l=e.entries();!(c=l.next()).done;)if(!s(c.value[1],a.get(c.value[0])))return!1;return!0}if(r&&e instanceof Set&&a instanceof Set){if(e.size!==a.size)return!1;for(l=e.entries();!(c=l.next()).done;)if(!a.has(c.value[0]))return!1;return!0}if(i&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(a)){if((o=e.length)!=a.length)return!1;for(c=o;0!=c--;)if(e[c]!==a[c])return!1;return!0}if(e.constructor===RegExp)return e.source===a.source&&e.flags===a.flags;if(e.valueOf!==Object.prototype.valueOf&&"function"==typeof e.valueOf&&"function"==typeof a.valueOf)return e.valueOf()===a.valueOf();if(e.toString!==Object.prototype.toString&&"function"==typeof e.toString&&"function"==typeof a.toString)return e.toString()===a.toString();if((o=(u=Object.keys(e)).length)!==Object.keys(a).length)return!1;for(c=o;0!=c--;)if(!Object.prototype.hasOwnProperty.call(a,u[c]))return!1;if(t&&e instanceof Element)return!1;for(c=o;0!=c--;)if(("_owner"!==u[c]&&"__v"!==u[c]&&"__o"!==u[c]||!e.$$typeof)&&!s(e[u[c]],a[u[c]]))return!1;return!0}return e!=e&&a!=a}e.exports=function(e,t){try{return s(e,t)}catch(e){if((e.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw e}}},9406:(e,t,n)=>{"use strict";var r,i=n(5959),s=(r=i)&&"object"==typeof r&&"default"in r?r.default:r;function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var o=!("undefined"==typeof window||!window.document||!window.document.createElement);e.exports=function(e,t,n){if("function"!=typeof e)throw new Error("Expected reducePropsToState to be a function.");if("function"!=typeof t)throw new Error("Expected handleStateChangeOnClient to be a function.");if(void 0!==n&&"function"!=typeof n)throw new Error("Expected mapStateOnServer to either be undefined or a function.");return function(r){if("function"!=typeof r)throw new Error("Expected WrappedComponent to be a React component.");var c,u=[];function l(){c=e(u.map((function(e){return e.props}))),d.canUseDOM?t(c):n&&(c=n(c))}var d=function(e){var t,n;function i(){return e.apply(this,arguments)||this}n=e,(t=i).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n,i.peek=function(){return c},i.rewind=function(){if(i.canUseDOM)throw new Error("You may only call rewind() on the server. Call peek() to read the current state.");var e=c;return c=void 0,u=[],e};var a=i.prototype;return a.UNSAFE_componentWillMount=function(){u.push(this),l()},a.componentDidUpdate=function(){l()},a.componentWillUnmount=function(){var e=u.indexOf(this);u.splice(e,1),l()},a.render=function(){return s.createElement(r,this.props)},i}(i.PureComponent);return a(d,"displayName","SideEffect("+function(e){return e.displayName||e.name||"Component"}(r)+")"),a(d,"canUseDOM",o),d}}},2192:(e,t,n)=>{"use strict";var r=n(5959),i=Symbol.for("react.element"),s=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,o=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,s={},u=null,l=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(l=t.ref),t)a.call(t,r)&&!c.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===s[r]&&(s[r]=t[r]);return{$$typeof:i,type:e,key:u,ref:l,props:s,_owner:o.current}}t.Fragment=s,t.jsx=u,t.jsxs=u},2540:(e,t,n)=>{"use strict";e.exports=n(2192)},4137:(e,t,n)=>{"use strict";n.d(t,{Gy:()=>i,R2:()=>r,bw:()=>s});const r=n(2533).id,i=`/a/${r}`;var s=function(e){return e.EXPLORE="/explore",e.ADHOC="/ad-hoc",e.SETTINGS="/settings",e}({})},897:(e,t,n)=>{"use strict";n.d(t,{f:()=>r});const r=()=>{}},5656:(e,t,n)=>{"use strict";n.d(t,{O:()=>o});var r=n(8531),i=n(2096),s=n(550),a=n(9090);class o extends a.Q{static getPyroscopeDataSources(){return Object.values(r.config.datasources).filter((e=>"grafana-pyroscope-datasource"===e.type))}static selectDefaultDataSource(){var e;const t=o.getPyroscopeDataSources(),n=new URL(window.location.href).searchParams.get("var-dataSource"),r=null===(e=s.x.get(s.x.KEYS.PROFILES_EXPLORER))||void 0===e?void 0:e.dataSource,a=t.find((e=>e.uid===n))||t.find((e=>e.uid===r))||t.find((e=>e.jsonData.overridesDefault))||t.find((e=>e.isDefault))||t[0];return a||(i.v.warn("Cannot find any Pyroscope data source! Please add and configure a Pyroscope data source to your Grafana instance."),{uid:"no-data-source-configured"})}static getBaseUrl(){const e=o.selectDefaultDataSource();let t=r.config.appSubUrl||"";return"/"!==t.at(-1)&&(t+="/"),`${t}api/datasources/proxy/uid/${e.uid}`}constructor(){var e,t;super(o.getBaseUrl().toString(),{"content-type":"application/json","X-Grafana-Org-Id":String((null===(t=r.config.bootData)||void 0===t||null===(e=t.user)||void 0===e?void 0:e.orgId)||"")})}}},9090:(e,t,n)=>{"use strict";n.d(t,{Q:()=>l});var r=n(897);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class s extends Error{constructor(e,t){let n=`HTTP ${e.status} (${e.statusText||"?"})`;(null==t?void 0:t.message)&&(n=`${n} → ${t.message}`),super(n),i(this,"response",void 0),i(this,"reason",void 0),this.response=e}}function a(e,t,n,r,i,s,a){try{var o=e[s](a),c=o.value}catch(e){return void n(e)}o.done?t(c):Promise.resolve(c).then(r,i)}function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){o(e,t,n[t])}))}return e}function u(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class l{fetch(e,t){var n,i=this;return(n=function*(){i.abortController=new AbortController;const{signal:n}=i.abortController,a=`${i.baseUrl}${e}`,o=c({},i.defaultHeaders,null==t?void 0:t.headers),l=u(c({signal:n},t),{headers:o});let d;try{if(d=yield fetch(a,l),!d.ok)throw new s(d,yield d.json().catch(r.f))}catch(e){var p;throw i.isAbortError(e)&&(e.reason=(null==t||null===(p=t.signal)||void 0===p?void 0:p.reason)||n.reason),e}finally{i.abortController=null}return d},function(){var e=this,t=arguments;return new Promise((function(r,i){var s=n.apply(e,t);function o(e){a(s,r,i,o,c,"next",e)}function c(e){a(s,r,i,o,c,"throw",e)}o(void 0)}))})()}abort(e){this.abortController&&this.abortController.abort(e)}isAbortError(e){return e instanceof DOMException&&"AbortError"===e.name}constructor(e,t={}){o(this,"baseUrl",""),o(this,"defaultHeaders",{}),o(this,"abortController",null),this.baseUrl=e,this.defaultHeaders=Object.freeze(t)}}},5377:(e,t,n)=>{"use strict";var r;n.d(t,{n1:()=>$r,Js:()=>Br}),function(e){e.EXCEPTION="exception",e.LOG="log",e.MEASUREMENT="measurement",e.TRACE="trace",e.EVENT="event"}(r||(r={}));const i={[r.EXCEPTION]:"exceptions",[r.LOG]:"logs",[r.MEASUREMENT]:"measurements",[r.TRACE]:"traces",[r.EVENT]:"events"};function s(){return Date.now()}function a(){return(new Date).toISOString()}function o(e){return new Date(e).toISOString()}function c(e,t){return typeof e===t}function u(e,t){return Object.prototype.toString.call(e)===`[object ${t}]`}function l(e,t){try{return e instanceof t}catch(e){return!1}}const d=e=>c(e,"null"),p=e=>c(e,"string"),f=e=>c(e,"number")&&!isNaN(e)||c(e,"bigint"),h=e=>!d(e)&&c(e,"object"),m=e=>c(e,"function"),g=e=>u(e,"Array"),v="undefined"!=typeof Event,b=e=>v&&l(e,Event),y="undefined"!=typeof Error,w=e=>y&&l(e,Error),E=e=>u(e,"ErrorEvent"),S=e=>u(e,"DOMError"),T=e=>u(e,"DOMException");function O(e,t){if(e===t)return!0;if(c(e,"number")&&isNaN(e))return c(t,"number")&&isNaN(t);const n=g(e),r=g(t);if(n!==r)return!1;if(n&&r){const n=e.length;if(n!==t.length)return!1;for(let r=n;0!=r--;)if(!O(e[r],t[r]))return!1;return!0}const i=h(e),s=h(t);if(i!==s)return!1;if(e&&t&&i&&s){const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let e of n)if(!r.includes(e))return!1;for(let r of n)if(!O(e[r],t[r]))return!1;return!0}return!1}const x="Error";let C;var P=n(5438);const I=e=>e.map((e=>{try{return String(e)}catch(e){return""}})).join(" ");function k(e,t,n,i,s){t.debug("Initializing API");const c=function(e,t,n,i,s){let a;return t.debug("Initializing traces API"),{getOTEL:()=>a,getTraceContext:()=>{const e=null==a?void 0:a.trace.getSpanContext(a.context.active());return e?{trace_id:e.traceId,span_id:e.spanId}:void 0},initOTEL:(e,n)=>{t.debug("Initializing OpenTelemetry"),a={trace:e,context:n}},isOTELInitialized:()=>!!a,pushTraces:e=>{try{const n={type:r.TRACE,payload:e,meta:i.value};t.debug("Pushing trace\n",n),s.execute(n)}catch(e){t.error("Error pushing trace\n",e)}}}}(0,t,0,i,s);return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},c),function(e,t,n,i,s,c){var u;t.debug("Initializing exceptions API");let l=null;C=null!==(u=n.parseStacktrace)&&void 0!==u?u:C;const p=e=>{t.debug("Changing stacktrace parser"),C=null!=e?e:C};return p(n.parseStacktrace),{changeStacktraceParser:p,getStacktraceParser:()=>C,pushError:(e,{skipDedupe:u,stackFrames:p,type:f,context:h,spanContext:m,timestampOverwriteMs:g}={})=>{f=f||e.name||x;const v={meta:i.value,payload:{type:f,value:e.message,timestamp:g?o(g):a(),trace:m?{trace_id:m.traceId,span_id:m.spanId}:c.getTraceContext(),context:null!=h?h:{}},type:r.EXCEPTION};(null==(p=null!=p?p:e.stack?null==C?void 0:C(e).frames:void 0)?void 0:p.length)&&(v.payload.stacktrace={frames:p});const b={type:v.payload.type,value:v.payload.value,stackTrace:v.payload.stacktrace,context:v.payload.context};u||!n.dedupe||d(l)||!O(b,l)?(l=b,t.debug("Pushing exception\n",v),s.execute(v)):t.debug("Skipping error push because it is the same as the last one\n",v.payload)}}}(0,t,n,i,s,c)),function(e,t,n,r,i){let s,a,o;t.debug("Initializing meta API");const c=e=>{a&&r.remove(a),a={user:e},r.add(a)},u=e=>{s&&r.remove(s),s={session:e},r.add(s)};return{setUser:c,resetUser:c,setSession:u,resetSession:u,getSession:()=>r.value.session,setView:e=>{var t;if((null===(t=null==o?void 0:o.view)||void 0===t?void 0:t.name)===(null==e?void 0:e.name))return;const n=o;o={view:e},r.add(o),n&&r.remove(n)},getView:()=>r.value.view}}(0,t,0,i)),function(e,t,n,i,s,c){var u;t.debug("Initializing logs API");let l=null;const p=null!==(u=n.logArgsSerializer)&&void 0!==u?u:I;return{pushLog:(e,{context:u,level:f,skipDedupe:h,spanContext:m,timestampOverwriteMs:g}={})=>{try{const v={type:r.LOG,payload:{message:p(e),level:null!=f?f:P.Ic,context:null!=u?u:{},timestamp:g?o(g):a(),trace:m?{trace_id:m.traceId,span_id:m.spanId}:c.getTraceContext()},meta:i.value},b={message:v.payload.message,level:v.payload.level,context:v.payload.context};if(!h&&n.dedupe&&!d(l)&&O(b,l))return void t.debug("Skipping log push because it is the same as the last one\n",v.payload);l=b,t.debug("Pushing log\n",v),s.execute(v)}catch(e){t.error("Error pushing log\n",e)}}}}(0,t,n,i,s,c)),function(e,t,n,i,s,c){t.debug("Initializing measurements API");let u=null;return{pushMeasurement:(e,{skipDedupe:l,context:p,spanContext:f,timestampOverwriteMs:h}={})=>{try{const m={type:r.MEASUREMENT,payload:Object.assign(Object.assign({},e),{trace:f?{trace_id:f.traceId,span_id:f.spanId}:c.getTraceContext(),timestamp:h?o(h):a(),context:null!=p?p:{}}),meta:i.value},g={type:m.payload.type,values:m.payload.values,context:m.payload.context};if(!l&&n.dedupe&&!d(u)&&O(g,u))return void t.debug("Skipping measurement push because it is the same as the last one\n",m.payload);u=g,t.debug("Pushing measurement\n",m),s.execute(m)}catch(e){t.error("Error pushing measurement\n",e)}}}}(0,t,n,i,s,c)),function(e,t,n,i,s,c){let u=null;return{pushEvent:(e,l,p,{skipDedupe:f,spanContext:h,timestampOverwriteMs:m}={})=>{try{const g={meta:i.value,payload:{name:e,domain:null!=p?p:n.eventDomain,attributes:l,timestamp:m?o(m):a(),trace:h?{trace_id:h.traceId,span_id:h.spanId}:c.getTraceContext()},type:r.EVENT},v={name:g.payload.name,attributes:g.payload.attributes,domain:g.payload.domain};if(!f&&n.dedupe&&!d(u)&&O(v,u))return void t.debug("Skipping event push because it is the same as the last one\n",g.payload);u=v,t.debug("Pushing event\n",g),s.execute(g)}catch(e){t.error("Error pushing event",e)}}}}(0,t,n,i,s,c))}function N(){}var R;!function(e){e[e.OFF=0]="OFF",e[e.ERROR=1]="ERROR",e[e.WARN=2]="WARN",e[e.INFO=3]="INFO",e[e.VERBOSE=4]="VERBOSE"}(R||(R={}));const A={debug:N,error:N,info:N,prefix:"Faro",warn:N},j=R.ERROR,L=Object.assign({},console);function D(e=L,t=j){const n=A;return t>R.OFF&&(n.error=t>=R.ERROR?function(...t){e.error(`${n.prefix}\n`,...t)}:N,n.warn=t>=R.WARN?function(...t){e.warn(`${n.prefix}\n`,...t)}:N,n.info=t>=R.INFO?function(...t){e.info(`${n.prefix}\n`,...t)}:N,n.debug=t>=R.VERBOSE?function(...t){e.debug(`${n.prefix}\n`,...t)}:N),n}let M=A;function F(e,t){return M=D(e,t.internalLoggerLevel),M}const q="undefined"!=typeof globalThis?globalThis:void 0!==n.g?n.g:"undefined"!=typeof self?self:void 0;const _="1.10.0";const U="_faroInternal";let $={};function Q(e,t,n,r,i,s,a){return t.debug("Initializing Faro"),$={api:s,config:n,instrumentations:a,internalLogger:t,metas:r,pause:i.pause,transports:i,unpatchedConsole:e,unpause:i.unpause},function(e){e.config.isolate?e.internalLogger.debug("Skipping registering internal Faro instance on global object"):(e.internalLogger.debug("Registering internal Faro instance on global object"),Object.defineProperty(q,U,{configurable:!1,enumerable:!1,writable:!1,value:e}))}($),function(e){if(e.config.preventGlobalExposure)e.internalLogger.debug("Skipping registering public Faro instance in the global scope");else{if(e.internalLogger.debug(`Registering public faro reference in the global scope using "${e.config.globalObjectKey}" key`),e.config.globalObjectKey in q)return void e.internalLogger.warn(`Skipping global registration due to key "${e.config.globalObjectKey}" being used already. Please set "globalObjectKey" to something else or set "preventGlobalExposure" to "true"`);Object.defineProperty(q,e.config.globalObjectKey,{configurable:!1,writable:!1,value:e})}}($),$}class B{constructor(e,t){var n,r;this.signalBuffer=[],this.itemLimit=null!==(n=null==t?void 0:t.itemLimit)&&void 0!==n?n:50,this.sendTimeout=null!==(r=null==t?void 0:t.sendTimeout)&&void 0!==r?r:250,this.paused=(null==t?void 0:t.paused)||!1,this.sendFn=e,this.flushInterval=-1,this.paused||this.start(),document.addEventListener("visibilitychange",(()=>{"hidden"===document.visibilityState&&this.flush()}))}addItem(e){this.paused||(this.signalBuffer.push(e),this.signalBuffer.length>=this.itemLimit&&this.flush())}start(){this.paused=!1,this.sendTimeout>0&&(this.flushInterval=window.setInterval((()=>this.flush()),this.sendTimeout))}pause(){this.paused=!0,clearInterval(this.flushInterval)}groupItems(e){const t=new Map;return e.forEach((e=>{const n=JSON.stringify(e.meta);let r=t.get(n);r=void 0===r?[e]:[...r,e],t.set(n,r)})),Array.from(t.values())}flush(){if(this.paused||0===this.signalBuffer.length)return;this.groupItems(this.signalBuffer).forEach(this.sendFn),this.signalBuffer=[]}}function H(e){return t=>{if(t.type===r.EXCEPTION&&t.payload){const n=t.payload,r=`${n.type}: ${n.value}`;if(function(e,t){return e.some((e=>p(e)?t.includes(e):!!t.match(e)))}(e,r))return null}return t}}function z(e,t,n,r){var i;t.debug("Initializing transports");const s=[];let a=n.paused,o=[];const c=e=>{let t=e;for(const e of o){const n=t.map(e).filter(Boolean);if(0===n.length)return[];t=n}return t},u=e=>{const n=c(e);if(0!==n.length)for(const e of s)t.debug(`Transporting item using ${e.name}\n`,n),e.isBatched()&&e.send(n)};let l;(null===(i=n.batching)||void 0===i?void 0:i.enabled)&&(l=new B(u,{sendTimeout:n.batching.sendTimeout,itemLimit:n.batching.itemLimit,paused:a}));return{add:(...i)=>{t.debug("Adding transports"),i.forEach((i=>{t.debug(`Adding "${i.name}" transport`);s.some((e=>e===i))?t.warn(`Transport ${i.name} is already added`):(i.unpatchedConsole=e,i.internalLogger=t,i.config=n,i.metas=r,s.push(i))}))},addBeforeSendHooks:(...e)=>{t.debug("Adding beforeSendHooks\n",o),e.forEach((e=>{e&&o.push(e)}))},addIgnoreErrorsPatterns:(...e)=>{t.debug("Adding ignoreErrorsPatterns\n",e),e.forEach((e=>{e&&o.push(H(e))}))},getBeforeSendHooks:()=>[...o],execute:e=>{var r;a||((null===(r=n.batching)||void 0===r?void 0:r.enabled)&&(null==l||l.addItem(e)),(e=>{var r,i;if((null===(r=n.batching)||void 0===r?void 0:r.enabled)&&s.every((e=>e.isBatched())))return;const[a]=c([e]);if(void 0!==a)for(const e of s)t.debug(`Transporting item using ${e.name}\n`,a),e.isBatched()?(null===(i=n.batching)||void 0===i?void 0:i.enabled)||e.send([a]):e.send(a)})(e))},isPaused:()=>a,pause:()=>{t.debug("Pausing transports"),null==l||l.pause(),a=!0},remove:(...e)=>{t.debug("Removing transports"),e.forEach((e=>{t.debug(`Removing "${e.name}" transport`);const n=s.indexOf(e);-1!==n?s.splice(n,1):t.warn(`Transport "${e.name}" is not added`)}))},removeBeforeSendHooks:(...e)=>{o.filter((t=>!e.includes(t)))},get transports(){return[...s]},unpause:()=>{t.debug("Unpausing transports"),null==l||l.start(),a=!1}}}let G=L;function K(e){var t;return G=null!==(t=e.unpatchedConsole)&&void 0!==t?t:G,G}function W(e){const t=K(e),n=F(t,e);if(U in q&&!e.isolate)return void n.error('Faro is already registered. Either add instrumentations, transports etc. to the global faro instance or use the "isolate" property');n.debug("Initializing");const r=function(e,t,n){let r=[],i=[];const s=()=>r.reduce(((e,t)=>Object.assign(e,m(t)?t():t)),{}),a=()=>{if(i.length){const e=s();i.forEach((t=>t(e)))}};return{add:(...e)=>{t.debug("Adding metas\n",e),r.push(...e),a()},remove:(...e)=>{t.debug("Removing metas\n",e),r=r.filter((t=>!e.includes(t))),a()},addListener:e=>{t.debug("Adding metas listener\n",e),i.push(e)},removeListener:e=>{t.debug("Removing metas listener\n",e),i=i.filter((t=>t!==e))},get value(){return s()}}}(0,n),i=z(t,n,e,r),s=k(0,n,e,r,i),a=function(e,t,n,r,i,s){t.debug("Initializing instrumentations");const a=[];return{add:(...o)=>{t.debug("Adding instrumentations"),o.forEach((o=>{t.debug(`Adding "${o.name}" instrumentation`),a.some((e=>e.name===o.name))?t.warn(`Instrumentation ${o.name} is already added`):(o.unpatchedConsole=e,o.internalLogger=t,o.config=n,o.metas=r,o.transports=i,o.api=s,a.push(o),o.initialize())}))},get instrumentations(){return[...a]},remove:(...e)=>{t.debug("Removing instrumentations"),e.forEach((e=>{var n,r;t.debug(`Removing "${e.name}" instrumentation`);const i=a.reduce(((t,n,r)=>null===t&&n.name===e.name?r:null),null);i?(null===(r=(n=a[i]).destroy)||void 0===r||r.call(n),a.splice(i,1)):t.warn(`Instrumentation "${e.name}" is not added`)}))}}}(t,n,e,r,i,s),o=Q(t,n,e,r,i,s,a);return function(e){var t,n;const r={sdk:{version:_},app:{bundleId:e.config.app.name&&(i=e.config.app.name,null==q?void 0:q[`__faroBundleId_${i}`])}};var i;const s=null===(t=e.config.sessionTracking)||void 0===t?void 0:t.session;s&&e.api.setSession(s),e.config.app&&(r.app=Object.assign(Object.assign({},e.config.app),r.app)),e.config.user&&(r.user=e.config.user),e.config.view&&(r.view=e.config.view),e.metas.add(r,...null!==(n=e.config.metas)&&void 0!==n?n:[])}(o),function(e){e.transports.add(...e.config.transports),e.transports.addBeforeSendHooks(e.config.beforeSend),e.transports.addIgnoreErrorsPatterns(e.config.ignoreErrors)}(o),function(e){e.instrumentations.add(...e.config.instrumentations)}(o),o}const V="faro",Y={enabled:!0,sendTimeout:250,itemLimit:50},J="browser",X="\n",Z="eval",ee="?",te="@",ne=/^\s*at (?:(.*\).*?|.*?) ?\((?:address at )?)?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,re=/\((\S*)(?::(\d+))(?::(\d+))\)/,ie="eval",se="address at ",ae=se.length,oe=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|safari-extension|safari-web-extension|capacitor)?:\/.*?|\[native code]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,ce=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,ue=" > eval",le="safari-extension",de="safari-web-extension",pe=/Minified React error #\d+;/i;function fe(e,t,n,r){const i={filename:e||document.location.href,function:t||ee};return void 0!==n&&(i.lineno=n),void 0!==r&&(i.colno=r),i}function he(e,t){const n=null==e?void 0:e.includes(le),r=!n&&(null==e?void 0:e.includes(de));return n||r?[(null==e?void 0:e.includes(te))?e.split(te)[0]:e,n?`${le}:${t}`:`${de}:${t}`]:[e,t]}function me(e){let t=[];e.stacktrace?t=e.stacktrace.split(X).filter(((e,t)=>t%2==0)):e.stack&&(t=e.stack.split(X));const n=t.reduce(((t,n,r)=>{let i,s,a,o,c;if(i=ne.exec(n)){if(s=i[1],a=i[2],o=i[3],c=i[4],null==a?void 0:a.startsWith(ie)){const e=re.exec(a);e&&(a=e[1],o=e[2],c=e[3])}a=(null==a?void 0:a.startsWith(se))?a.substring(ae):a,[s,a]=he(s,a)}else if(i=oe.exec(n)){if(s=i[1],a=i[3],o=i[4],c=i[5],a&&a.includes(ue)){const e=ce.exec(a);e&&(s=s||Z,a=e[1],o=e[2])}else 0===r&&!c&&f(e.columnNumber)&&(c=String(e.columnNumber+1));[s,a]=he(s,a)}return(a||s)&&t.push(fe(a,s,o?Number(o):void 0,c?Number(c):void 0)),t}),[]);return pe.test(e.message)?n.slice(1):n}function ge(e){return{frames:me(e)}}const ve="com.grafana.faro.session",be=144e5,ye=9e5,we={enabled:!0,persistent:!1,maxSessionPersistenceTime:ye};var Ee=n(6660);const Se="unknown",Te=[()=>{const e=new Ee.UAParser,{name:t,version:n}=e.getBrowser(),{name:r,version:i}=e.getOS(),s=e.getUA(),a=navigator.language,o=navigator.userAgent.includes("Mobi"),c=function(){if(!t||!n)return;if("userAgentData"in navigator&&navigator.userAgentData)return navigator.userAgentData.brands;return}();return{browser:{name:null!=t?t:Se,version:null!=n?n:Se,os:`${null!=r?r:Se} ${null!=i?i:Se}`,userAgent:null!=s?s:Se,language:null!=a?a:Se,mobile:o,brands:null!=c?c:Se,viewportWidth:`${window.innerWidth}`,viewportHeight:`${window.innerHeight}`}}},()=>({page:{url:location.href}})],Oe=()=>{const e=window.k6;return{k6:Object.assign({isK6Browser:!0},(null==e?void 0:e.testRunId)&&{testRunId:null==e?void 0:e.testRunId})}};class xe{constructor(){this.unpatchedConsole=L,this.internalLogger=A,this.config={},this.metas={}}logDebug(...e){this.internalLogger.debug(`${this.name}\n`,...e)}logInfo(...e){this.internalLogger.info(`${this.name}\n`,...e)}logWarn(...e){this.internalLogger.warn(`${this.name}\n`,...e)}logError(...e){this.internalLogger.error(`${this.name}\n`,...e)}}class Ce extends xe{isBatched(){return!1}getIgnoreUrls(){return[]}}function Pe(e,t){var n,r;if(void 0===t)return e;if(void 0===e)return{resourceSpans:t};const i=null===(n=e.resourceSpans)||void 0===n?void 0:n[0];if(void 0===i)return e;const s=(null==i?void 0:i.scopeSpans)||[],a=(null===(r=null==t?void 0:t[0])||void 0===r?void 0:r.scopeSpans)||[];return Object.assign(Object.assign({},e),{resourceSpans:[Object.assign(Object.assign({},i),{scopeSpans:[...s,...a]})]})}function Ie(e,t){let n,r=!1;const i=()=>{null!=n?(e(...n),n=null,setTimeout(i,t)):r=!1};return(...s)=>{r?n=s:(e(...s),r=!0,setTimeout(i,t))}}const ke={session:"sessionStorage",local:"localStorage"};function Ne(e){var t;try{let t;t=window[e];const n="__faro_storage_test__";return t.setItem(n,n),t.removeItem(n),!0}catch(n){return null===(t=$.internalLogger)||void 0===t||t.info(`Web storage of type ${e} is not available. Reason: ${n}`),!1}}function Re(e,t){return Me(t)?window[t].getItem(e):null}function Ae(e,t,n){if(Me(n))try{window[n].setItem(e,t)}catch(e){}}function je(e,t){Me(t)&&window[t].removeItem(e)}const Le=Ne(ke.local),De=Ne(ke.session);function Me(e){return e===ke.local?Le:e===ke.session&&De}function Fe(){var e,t,n;const r=$.config.sessionTracking;let i=null!==(n=null!==(t=null===(e=null==r?void 0:r.sampler)||void 0===e?void 0:e.call(r,{metas:$.metas.value}))&&void 0!==t?t:null==r?void 0:r.samplingRate)&&void 0!==n?n:1;if("number"!=typeof i){i=0}return Math.random()<i}const qe="abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ**********";function _e(e=10){return Array.from(Array(e)).map((()=>qe[Math.floor(Math.random()*qe.length)])).join("")}function Ue({sessionId:e,started:t,lastActivity:n,isSampled:r=!0}={}){var i,a;const o=s(),c=null===(a=null===(i=$.config)||void 0===i?void 0:i.sessionTracking)||void 0===a?void 0:a.generateSessionId;return null==e&&(e="function"==typeof c?c():_e()),{sessionId:e,lastActivity:null!=n?n:o,started:null!=t?t:o,isSampled:r}}function $e(e){if(null==e)return!1;const t=s();if(!(t-e.started<be))return!1;return t-e.lastActivity<ye}function Qe({fetchUserSession:e,storeUserSession:t}){return function({forceSessionExtend:n}={forceSessionExtend:!1}){var r,i,a;if(!e||!t)return;const o=$.config.sessionTracking,c=null==o?void 0:o.persistent;if(c&&!Le||!c&&!De)return;const u=e();if(!1===n&&$e(u))t(Object.assign(Object.assign({},u),{lastActivity:s()}));else{let e=Be(Ue({isSampled:Fe()}),u);t(e),null===(r=$.api)||void 0===r||r.setSession(e.sessionMeta),null===(i=null==o?void 0:o.onSessionChange)||void 0===i||i.call(o,null!==(a=null==u?void 0:u.sessionMeta)&&void 0!==a?a:null,e.sessionMeta)}}}function Be(e,t){var n,r,i,s;return Object.assign(Object.assign({},e),{sessionMeta:{id:e.sessionId,attributes:Object.assign(Object.assign(Object.assign(Object.assign({},null===(r=null===(n=$.config.sessionTracking)||void 0===n?void 0:n.session)||void 0===r?void 0:r.attributes),null!==(s=null===(i=$.metas.value.session)||void 0===i?void 0:i.attributes)&&void 0!==s?s:{}),null!=t?{previousSession:t.sessionId}:{}),{isSampled:e.isSampled.toString()})}})}class He{constructor(){this.updateSession=Ie((()=>this.updateUserSession()),1e3),this.updateUserSession=Qe({fetchUserSession:He.fetchUserSession,storeUserSession:He.storeUserSession}),this.init()}static removeUserSession(){je(ve,He.storageTypeLocal)}static storeUserSession(e){Ae(ve,JSON.stringify(e),He.storageTypeLocal)}static fetchUserSession(){const e=Re(ve,He.storageTypeLocal);return e?JSON.parse(e):null}init(){document.addEventListener("visibilitychange",(()=>{"visible"===document.visibilityState&&this.updateSession()})),$.metas.addListener((function(e){const t=e.session,n=He.fetchUserSession();if(t&&t.id!==(null==n?void 0:n.sessionId)){const e=Be(Ue({sessionId:t.id,isSampled:Fe()}),n);He.storeUserSession(e),$.api.setSession(e.sessionMeta)}}))}}He.storageTypeLocal=ke.local;class ze{constructor(){this.updateSession=Ie((()=>this.updateUserSession()),1e3),this.updateUserSession=Qe({fetchUserSession:ze.fetchUserSession,storeUserSession:ze.storeUserSession}),this.init()}static removeUserSession(){je(ve,ze.storageTypeSession)}static storeUserSession(e){Ae(ve,JSON.stringify(e),ze.storageTypeSession)}static fetchUserSession(){const e=Re(ve,ze.storageTypeSession);return e?JSON.parse(e):null}init(){document.addEventListener("visibilitychange",(()=>{"visible"===document.visibilityState&&this.updateSession()})),$.metas.addListener((function(e){const t=e.session,n=ze.fetchUserSession();if(t&&t.id!==(null==n?void 0:n.sessionId)){const e=Be(Ue({sessionId:t.id,isSampled:Fe()}),n);ze.storeUserSession(e),$.api.setSession(e.sessionMeta)}}))}}function Ge(e){return(null==e?void 0:e.persistent)?He:ze}ze.storageTypeSession=ke.session;var Ke=function(e,t,n,r){return new(n||(n=Promise))((function(i,s){function a(e){try{c(r.next(e))}catch(e){s(e)}}function o(e){try{c(r.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,o)}c((r=r.apply(e,t||[])).next())}))},We=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n};class Ve extends Ce{constructor(e){var t,n,r,i;super(),this.options=e,this.name="@grafana/faro-web-sdk:transport-fetch",this.version=_,this.disabledUntil=new Date,this.rateLimitBackoffMs=null!==(t=e.defaultRateLimitBackoffMs)&&void 0!==t?t:5e3,this.getNow=null!==(n=e.getNow)&&void 0!==n?n:()=>Date.now(),this.promiseBuffer=function(e){const{size:t,concurrency:n}=e,r=[];let i=0;const s=()=>{if(i<n&&r.length){const{producer:e,resolve:t,reject:n}=r.shift();i++,e().then((e=>{i--,s(),t(e)}),(e=>{i--,s(),n(e)}))}};return{add:e=>{if(r.length+i>=t)throw new Error("Task buffer full");return new Promise(((t,n)=>{r.push({producer:e,resolve:t,reject:n}),s()}))}}}({size:null!==(r=e.bufferSize)&&void 0!==r?r:30,concurrency:null!==(i=e.concurrency)&&void 0!==i?i:5})}send(e){return Ke(this,void 0,void 0,(function*(){try{if(this.disabledUntil>new Date(this.getNow()))return this.logWarn(`Dropping transport item due to too many requests. Backoff until ${this.disabledUntil}`),Promise.resolve();yield this.promiseBuffer.add((()=>{const t=JSON.stringify(function(e){let t={meta:{}};return void 0!==e[0]&&(t.meta=e[0].meta),e.forEach((e=>{switch(e.type){case r.LOG:case r.EVENT:case r.EXCEPTION:case r.MEASUREMENT:const n=i[e.type],s=t[n];t=Object.assign(Object.assign({},t),{[n]:void 0===s?[e.payload]:[...s,e.payload]});break;case r.TRACE:t=Object.assign(Object.assign({},t),{traces:Pe(t.traces,e.payload.resourceSpans)})}})),t}(e)),{url:n,requestOptions:s,apiKey:a}=this.options,o=null!=s?s:{},{headers:c}=o,u=We(o,["headers"]);let l;const d=this.metas.value.session;return null!=d&&(l=d.id),fetch(n,Object.assign({method:"POST",headers:Object.assign(Object.assign(Object.assign({"Content-Type":"application/json"},null!=c?c:{}),a?{"x-api-key":a}:{}),l?{"x-faro-session-id":l}:{}),body:t,keepalive:t.length<=6e4},null!=u?u:{})).then((e=>Ke(this,void 0,void 0,(function*(){if(202===e.status){"invalid"===e.headers.get("X-Faro-Session-Status")&&this.extendFaroSession(this.config,this.logDebug)}return 429===e.status&&(this.disabledUntil=this.getRetryAfterDate(e),this.logWarn(`Too many requests, backing off until ${this.disabledUntil}`)),e.text().catch(N),e})))).catch((e=>{this.logError("Failed sending payload to the receiver\n",JSON.parse(t),e)}))}))}catch(e){this.logError(e)}}))}getIgnoreUrls(){var e;return[this.options.url].concat(null!==(e=this.config.ignoreUrls)&&void 0!==e?e:[])}isBatched(){return!0}getRetryAfterDate(e){const t=this.getNow(),n=e.headers.get("Retry-After");if(n){const e=Number(n);if(!isNaN(e))return new Date(1e3*e+t);const r=Date.parse(n);if(!isNaN(r))return new Date(r)}return new Date(t+this.rateLimitBackoffMs)}extendFaroSession(e,t){const n="Session expired",r=e.sessionTracking;if(null==r?void 0:r.enabled){const{fetchUserSession:e,storeUserSession:i}=Ge(r);Qe({fetchUserSession:e,storeUserSession:i})({forceSessionExtend:!0}),t(`${n} created new session.`)}else t(`${n}.`)}}class Ye extends xe{constructor(){super(...arguments),this.api={},this.transports={}}}const Je="DOMError",Xe="DOMException",Ze="Non-Error exception captured with keys:",et=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function tt(e){let t,n,r,i,s=[];if(E(e)&&e.error)t=e.error.message,n=e.error.name,s=me(e.error);else if((r=S(e))||T(e)){const{name:i,message:s}=e;n=null!=i?i:r?Je:Xe,t=s?`${n}: ${s}`:n}else w(e)?(t=e.message,s=me(e)):(h(e)||(i=b(e)))&&(n=i?e.constructor.name:void 0,t=`${Ze} ${Object.keys(e)}`);return[t,n,s]}function nt(e){const t=window.onerror;window.onerror=(...n)=>{try{const[t,r,i,s,a]=n;let o,c,u=[];const l=p(t),d=fe(r,"?",i,s);a||!l?([o,c,u]=tt(null!=a?a:t),0===u.length&&(u=[d])):l&&([o,c]=function(e){var t,n;const r=e.match(et),i=null!==(t=null==r?void 0:r[1])&&void 0!==t?t:x;return[null!==(n=null==r?void 0:r[2])&&void 0!==n?n:e,i]}(t),u=[d]),o&&e.pushError(new Error(o),{type:c,stackFrames:u})}finally{null==t||t.apply(window,n)}}}function rt(e){window.addEventListener("unhandledrejection",(t=>{var n,r;let i,s,a=t;a.reason?a=t.reason:(null===(n=t.detail)||void 0===n?void 0:n.reason)&&(a=null===(r=t.detail)||void 0===r?void 0:r.reason);let o=[];(e=>!h(e)&&!m(e))(a)?(i=`Non-Error promise rejection captured with value: ${String(a)}`,s="UnhandledRejection"):[i,s,o]=tt(a),i&&e.pushError(new Error(i),{type:s,stackFrames:o})}))}class it extends Ye{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-errors",this.version=_}initialize(){this.logDebug("Initializing"),nt(this.api),rt(this.api)}}var st,at,ot,ct,ut,lt=-1,dt=function(e){addEventListener("pageshow",(function(t){t.persisted&&(lt=t.timeStamp,e(t))}),!0)},pt=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},ft=function(){var e=pt();return e&&e.activationStart||0},ht=function(e,t){var n=pt(),r="navigate";return lt>=0?r="back-forward-cache":n&&(document.prerendering||ft()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},mt=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},gt=function(e,t,n,r){var i,s;return function(a){t.value>=0&&(a||r)&&((s=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=s,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},vt=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},bt=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},yt=function(e){var t=!1;return function(){t||(e(),t=!0)}},wt=-1,Et=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},St=function(e){"hidden"===document.visibilityState&&wt>-1&&(wt="visibilitychange"===e.type?e.timeStamp:0,Ot())},Tt=function(){addEventListener("visibilitychange",St,!0),addEventListener("prerenderingchange",St,!0)},Ot=function(){removeEventListener("visibilitychange",St,!0),removeEventListener("prerenderingchange",St,!0)},xt=function(){return wt<0&&(wt=Et(),Tt(),dt((function(){setTimeout((function(){wt=Et(),Tt()}),0)}))),{get firstHiddenTime(){return wt}}},Ct=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},Pt=[1800,3e3],It=function(e,t){t=t||{},Ct((function(){var n,r=xt(),i=ht("FCP"),s=mt("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(s.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-ft(),0),i.entries.push(e),n(!0)))}))}));s&&(n=gt(e,i,Pt,t.reportAllChanges),dt((function(r){i=ht("FCP"),n=gt(e,i,Pt,t.reportAllChanges),vt((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},kt=[.1,.25],Nt=0,Rt=1/0,At=0,jt=function(e){e.forEach((function(e){e.interactionId&&(Rt=Math.min(Rt,e.interactionId),At=Math.max(At,e.interactionId),Nt=At?(At-Rt)/7+1:0)}))},Lt=function(){"interactionCount"in performance||st||(st=mt("event",jt,{type:"event",buffered:!0,durationThreshold:0}))},Dt=[],Mt=new Map,Ft=0,qt=function(){return(st?Nt:performance.interactionCount||0)-Ft},_t=[],Ut=function(e){if(_t.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=Dt[Dt.length-1],n=Mt.get(e.interactionId);if(n||Dt.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};Mt.set(r.id,r),Dt.push(r)}Dt.sort((function(e,t){return t.latency-e.latency})),Dt.length>10&&Dt.splice(10).forEach((function(e){return Mt.delete(e.id)}))}}},$t=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=yt(e),"hidden"===document.visibilityState?e():(n=t(e),bt(e)),n},Qt=[200,500],Bt=[2500,4e3],Ht={},zt=[800,1800],Gt=function e(t){document.prerendering?Ct((function(){return e(t)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(t)}),!0):setTimeout(t,0)},Kt={passive:!0,capture:!0},Wt=new Date,Vt=function(e,t){at||(at=t,ot=e,ct=new Date,Xt(removeEventListener),Yt())},Yt=function(){if(ot>=0&&ot<ct-Wt){var e={entryType:"first-input",name:at.type,target:at.target,cancelable:at.cancelable,startTime:at.timeStamp,processingStart:at.timeStamp+ot};ut.forEach((function(t){t(e)})),ut=[]}},Jt=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){Vt(e,t),i()},r=function(){i()},i=function(){removeEventListener("pointerup",n,Kt),removeEventListener("pointercancel",r,Kt)};addEventListener("pointerup",n,Kt),addEventListener("pointercancel",r,Kt)}(t,e):Vt(t,e)}},Xt=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,Jt,Kt)}))},Zt=[100,300];class en{constructor(e){this.pushMeasurement=e}initialize(){Object.entries(en.mapping).forEach((([e,t])=>{t((t=>{this.pushMeasurement({type:"web-vitals",values:{[e]:t.value}})}))}))}}en.mapping={cls:function(e,t){t=t||{},It(yt((function(){var n,r=ht("CLS",0),i=0,s=[],a=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=s[0],n=s[s.length-1];i&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,s.push(e)):(i=e.value,s=[e])}})),i>r.value&&(r.value=i,r.entries=s,n())},o=mt("layout-shift",a);o&&(n=gt(e,r,kt,t.reportAllChanges),bt((function(){a(o.takeRecords()),n(!0)})),dt((function(){i=0,r=ht("CLS",0),n=gt(e,r,kt,t.reportAllChanges),vt((function(){return n()}))})),setTimeout(n,0))})))},fcp:It,fid:function(e,t){t=t||{},Ct((function(){var n,r=xt(),i=ht("FID"),s=function(e){e.startTime<r.firstHiddenTime&&(i.value=e.processingStart-e.startTime,i.entries.push(e),n(!0))},a=function(e){e.forEach(s)},o=mt("first-input",a);n=gt(e,i,Zt,t.reportAllChanges),o&&(bt(yt((function(){a(o.takeRecords()),o.disconnect()}))),dt((function(){var r;i=ht("FID"),n=gt(e,i,Zt,t.reportAllChanges),ut=[],ot=-1,at=null,Xt(addEventListener),r=s,ut.push(r),Yt()})))}))},inp:function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},Ct((function(){var n;Lt();var r,i=ht("INP"),s=function(e){$t((function(){e.forEach(Ut);var t,n=(t=Math.min(Dt.length-1,Math.floor(qt()/50)),Dt[t]);n&&n.latency!==i.value&&(i.value=n.latency,i.entries=n.entries,r())}))},a=mt("event",s,{durationThreshold:null!==(n=t.durationThreshold)&&void 0!==n?n:40});r=gt(e,i,Qt,t.reportAllChanges),a&&(a.observe({type:"first-input",buffered:!0}),bt((function(){s(a.takeRecords()),r(!0)})),dt((function(){Ft=0,Dt.length=0,Mt.clear(),i=ht("INP"),r=gt(e,i,Qt,t.reportAllChanges)})))})))},lcp:function(e,t){t=t||{},Ct((function(){var n,r=xt(),i=ht("LCP"),s=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-ft(),0),i.entries=[e],n())}))},a=mt("largest-contentful-paint",s);if(a){n=gt(e,i,Bt,t.reportAllChanges);var o=yt((function(){Ht[i.id]||(s(a.takeRecords()),a.disconnect(),Ht[i.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return $t(o)}),!0)})),bt(o),dt((function(r){i=ht("LCP"),n=gt(e,i,Bt,t.reportAllChanges),vt((function(){i.value=performance.now()-r.timeStamp,Ht[i.id]=!0,n(!0)}))}))}}))},ttfb:function(e,t){t=t||{};var n=ht("TTFB"),r=gt(e,n,zt,t.reportAllChanges);Gt((function(){var i=pt();i&&(n.value=Math.max(i.responseStart-ft(),0),n.entries=[i],r(!0),dt((function(){n=ht("TTFB",0),(r=gt(e,n,zt,t.reportAllChanges))(!0)})))}))}};var tn,nn,rn,sn=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},an=function(e){if("loading"===document.readyState)return"loading";var t=sn();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"},on=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},cn=function(e,t){var n="";try{for(;e&&9!==e.nodeType;){var r=e,i=r.id?"#"+r.id:on(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?"."+r.classList.value.trim().replace(/\s+/g,"."):"");if(n.length+i.length>(t||100)-1)return n||i;if(n=n?i+">"+n:i,r.id)break;e=r.parentNode}}catch(e){}return n},un=-1,ln=function(){return un},dn=function(e){addEventListener("pageshow",(function(t){t.persisted&&(un=t.timeStamp,e(t))}),!0)},pn=function(){var e=sn();return e&&e.activationStart||0},fn=function(e,t){var n=sn(),r="navigate";return ln()>=0?r="back-forward-cache":n&&(document.prerendering||pn()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},hn=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},mn=function(e,t,n,r){var i,s;return function(a){t.value>=0&&(a||r)&&((s=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=s,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},gn=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},vn=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},bn=function(e){var t=!1;return function(){t||(e(),t=!0)}},yn=-1,wn=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},En=function(e){"hidden"===document.visibilityState&&yn>-1&&(yn="visibilitychange"===e.type?e.timeStamp:0,Tn())},Sn=function(){addEventListener("visibilitychange",En,!0),addEventListener("prerenderingchange",En,!0)},Tn=function(){removeEventListener("visibilitychange",En,!0),removeEventListener("prerenderingchange",En,!0)},On=function(){return yn<0&&(yn=wn(),Sn(),dn((function(){setTimeout((function(){yn=wn(),Sn()}),0)}))),{get firstHiddenTime(){return yn}}},xn=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},Cn=[1800,3e3],Pn=function(e,t){t=t||{},xn((function(){var n,r=On(),i=fn("FCP"),s=hn("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(s.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-pn(),0),i.entries.push(e),n(!0)))}))}));s&&(n=mn(e,i,Cn,t.reportAllChanges),dn((function(r){i=fn("FCP"),n=mn(e,i,Cn,t.reportAllChanges),gn((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},In=[.1,.25],kn=0,Nn=1/0,Rn=0,An=function(e){e.forEach((function(e){e.interactionId&&(Nn=Math.min(Nn,e.interactionId),Rn=Math.max(Rn,e.interactionId),kn=Rn?(Rn-Nn)/7+1:0)}))},jn=[],Ln=new Map,Dn=0,Mn=[],Fn=function(e){if(Mn.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=jn[jn.length-1],n=Ln.get(e.interactionId);if(n||jn.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};Ln.set(r.id,r),jn.push(r)}jn.sort((function(e,t){return t.latency-e.latency})),jn.length>10&&jn.splice(10).forEach((function(e){return Ln.delete(e.id)}))}}},qn=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=bn(e),"hidden"===document.visibilityState?e():(n=t(e),vn(e)),n},_n=[200,500],Un=function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},xn((function(){var n;"interactionCount"in performance||tn||(tn=hn("event",An,{type:"event",buffered:!0,durationThreshold:0}));var r,i=fn("INP"),s=function(e){qn((function(){e.forEach(Fn);var t,n=(t=Math.min(jn.length-1,Math.floor(((tn?kn:performance.interactionCount||0)-Dn)/50)),jn[t]);n&&n.latency!==i.value&&(i.value=n.latency,i.entries=n.entries,r())}))},a=hn("event",s,{durationThreshold:null!==(n=t.durationThreshold)&&void 0!==n?n:40});r=mn(e,i,_n,t.reportAllChanges),a&&(a.observe({type:"first-input",buffered:!0}),vn((function(){s(a.takeRecords()),r(!0)})),dn((function(){Dn=0,jn.length=0,Ln.clear(),i=fn("INP"),r=mn(e,i,_n,t.reportAllChanges)})))})))},$n=[],Qn=[],Bn=new WeakMap,Hn=new Map,zn=-1,Gn=function(e){$n=$n.concat(e),Kn()},Kn=function(){zn<0&&(zn=qn(Wn))},Wn=function(){Hn.size>10&&Hn.forEach((function(e,t){Ln.has(t)||Hn.delete(t)}));var e=jn.map((function(e){return Bn.get(e.entries[0])})),t=Qn.length-50;Qn=Qn.filter((function(n,r){return r>=t||e.includes(n)}));for(var n=new Set,r=0;r<Qn.length;r++){var i=Qn[r];Zn(i.startTime,i.processingEnd).forEach((function(e){n.add(e)}))}for(var s=0;s<50;s++){var a=$n[$n.length-1-s];if(!a||a.startTime<rn)break;n.add(a)}$n=Array.from(n),zn=-1};Mn.push((function(e){e.interactionId&&e.target&&!Hn.has(e.interactionId)&&Hn.set(e.interactionId,e.target)}),(function(e){var t,n=e.startTime+e.duration;rn=Math.max(rn,e.processingEnd);for(var r=Qn.length-1;r>=0;r--){var i=Qn[r];if(Math.abs(n-i.renderTime)<=8){(t=i).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:n,entries:[e]},Qn.push(t)),(e.interactionId||"first-input"===e.entryType)&&Bn.set(e,t),Kn()}));var Vn,Yn,Jn,Xn,Zn=function(e,t){for(var n,r=[],i=0;n=$n[i];i++)if(!(n.startTime+n.duration<e)){if(n.startTime>t)break;r.push(n)}return r},er=[2500,4e3],tr={},nr=[800,1800],rr=function e(t){document.prerendering?xn((function(){return e(t)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(t)}),!0):setTimeout(t,0)},ir=function(e,t){t=t||{};var n=fn("TTFB"),r=mn(e,n,nr,t.reportAllChanges);rr((function(){var i=sn();i&&(n.value=Math.max(i.responseStart-pn(),0),n.entries=[i],r(!0),dn((function(){n=fn("TTFB",0),(r=mn(e,n,nr,t.reportAllChanges))(!0)})))}))},sr={passive:!0,capture:!0},ar=new Date,or=function(e,t){Vn||(Vn=t,Yn=e,Jn=new Date,lr(removeEventListener),cr())},cr=function(){if(Yn>=0&&Yn<Jn-ar){var e={entryType:"first-input",name:Vn.type,target:Vn.target,cancelable:Vn.cancelable,startTime:Vn.timeStamp,processingStart:Vn.timeStamp+Yn};Xn.forEach((function(t){t(e)})),Xn=[]}},ur=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){or(e,t),i()},r=function(){i()},i=function(){removeEventListener("pointerup",n,sr),removeEventListener("pointercancel",r,sr)};addEventListener("pointerup",n,sr),addEventListener("pointercancel",r,sr)}(t,e):or(t,e)}},lr=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,ur,sr)}))},dr=[100,300],pr=function(e,t){!function(e,t){t=t||{},xn((function(){var n,r=On(),i=fn("FID"),s=function(e){e.startTime<r.firstHiddenTime&&(i.value=e.processingStart-e.startTime,i.entries.push(e),n(!0))},a=function(e){e.forEach(s)},o=hn("first-input",a);n=mn(e,i,dr,t.reportAllChanges),o&&(vn(bn((function(){a(o.takeRecords()),o.disconnect()}))),dn((function(){var r;i=fn("FID"),n=mn(e,i,dr,t.reportAllChanges),Xn=[],Yn=-1,Vn=null,lr(addEventListener),r=s,Xn.push(r),cr()})))}))}((function(t){var n=function(e){var t=e.entries[0],n={eventTarget:cn(t.target),eventType:t.name,eventTime:t.startTime,eventEntry:t,loadState:an(t.startTime)};return Object.assign(e,{attribution:n})}(t);e(n)}),t)};const fr="com.grafana.faro.lastNavigationId",hr="load_state",mr="time_to_first_byte";class gr{constructor(e){this.corePushMeasurement=e}initialize(){this.measureCLS(),this.measureFCP(),this.measureFID(),this.measureINP(),this.measureLCP(),this.measureTTFB()}measureCLS(){!function(e,t){!function(e,t){t=t||{},Pn(bn((function(){var n,r=fn("CLS",0),i=0,s=[],a=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=s[0],n=s[s.length-1];i&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,s.push(e)):(i=e.value,s=[e])}})),i>r.value&&(r.value=i,r.entries=s,n())},o=hn("layout-shift",a);o&&(n=mn(e,r,In,t.reportAllChanges),vn((function(){a(o.takeRecords()),n(!0)})),dn((function(){i=0,r=fn("CLS",0),n=mn(e,r,In,t.reportAllChanges),gn((function(){return n()}))})),setTimeout(n,0))})))}((function(t){var n=function(e){var t,n={};if(e.entries.length){var r=e.entries.reduce((function(e,t){return e&&e.value>t.value?e:t}));if(r&&r.sources&&r.sources.length){var i=(t=r.sources).find((function(e){return e.node&&1===e.node.nodeType}))||t[0];i&&(n={largestShiftTarget:cn(i.node),largestShiftTime:r.startTime,largestShiftValue:r.value,largestShiftSource:i,largestShiftEntry:r,loadState:an(r.startTime)})}}return Object.assign(e,{attribution:n})}(t);e(n)}),t)}((e=>{const{loadState:t,largestShiftValue:n,largestShiftTime:r,largestShiftTarget:i}=e.attribution,s=this.buildInitialValues(e);this.addIfPresent(s,"largest_shift_value",n),this.addIfPresent(s,"largest_shift_time",r);const a=this.buildInitialContext(e);this.addIfPresent(a,hr,t),this.addIfPresent(a,"largest_shift_target",i),this.pushMeasurement(s,a)}))}measureFCP(){!function(e,t){Pn((function(t){var n=function(e){var t={timeToFirstByte:0,firstByteToFCP:e.value,loadState:an(ln())};if(e.entries.length){var n=sn(),r=e.entries[e.entries.length-1];if(n){var i=n.activationStart||0,s=Math.max(0,n.responseStart-i);t={timeToFirstByte:s,firstByteToFCP:e.value-s,loadState:an(e.entries[0].startTime),navigationEntry:n,fcpEntry:r}}}return Object.assign(e,{attribution:t})}(t);e(n)}),t)}((e=>{const{firstByteToFCP:t,timeToFirstByte:n,loadState:r}=e.attribution,i=this.buildInitialValues(e);this.addIfPresent(i,"first_byte_to_fcp",t),this.addIfPresent(i,mr,n);const s=this.buildInitialContext(e);this.addIfPresent(s,hr,r),this.pushMeasurement(i,s)}))}measureFID(){pr((e=>{const{eventTime:t,eventTarget:n,eventType:r,loadState:i}=e.attribution,s=this.buildInitialValues(e);this.addIfPresent(s,"event_time",t);const a=this.buildInitialContext(e);this.addIfPresent(a,"event_target",n),this.addIfPresent(a,"event_type",r),this.addIfPresent(a,hr,i),this.pushMeasurement(s,a)}))}measureINP(){!function(e,t){nn||(nn=hn("long-animation-frame",Gn)),Un((function(t){var n=function(e){var t=e.entries[0],n=Bn.get(t),r=t.processingStart,i=n.processingEnd,s=n.entries.sort((function(e,t){return e.processingStart-t.processingStart})),a=Zn(t.startTime,i),o=e.entries.find((function(e){return e.target})),c=o&&o.target||Hn.get(t.interactionId),u=[t.startTime+t.duration,i].concat(a.map((function(e){return e.startTime+e.duration}))),l=Math.max.apply(Math,u),d={interactionTarget:cn(c),interactionTargetElement:c,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:l,processedEventEntries:s,longAnimationFrameEntries:a,inputDelay:r-t.startTime,processingDuration:i-r,presentationDelay:Math.max(l-i,0),loadState:an(t.startTime)};return Object.assign(e,{attribution:d})}(t);e(n)}),t)}((e=>{const{interactionTime:t,presentationDelay:n,inputDelay:r,processingDuration:i,nextPaintTime:s,loadState:a,interactionTarget:o,interactionType:c}=e.attribution,u=this.buildInitialValues(e);this.addIfPresent(u,"interaction_time",t),this.addIfPresent(u,"presentation_delay",n),this.addIfPresent(u,"input_delay",r),this.addIfPresent(u,"processing_duration",i),this.addIfPresent(u,"next_paint_time",s);const l=this.buildInitialContext(e);this.addIfPresent(l,hr,a),this.addIfPresent(l,"interaction_target",o),this.addIfPresent(l,"interaction_type",c),this.pushMeasurement(u,l)}))}measureLCP(){!function(e,t){!function(e,t){t=t||{},xn((function(){var n,r=On(),i=fn("LCP"),s=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-pn(),0),i.entries=[e],n())}))},a=hn("largest-contentful-paint",s);if(a){n=mn(e,i,er,t.reportAllChanges);var o=bn((function(){tr[i.id]||(s(a.takeRecords()),a.disconnect(),tr[i.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return qn(o)}),!0)})),vn(o),dn((function(r){i=fn("LCP"),n=mn(e,i,er,t.reportAllChanges),gn((function(){i.value=performance.now()-r.timeStamp,tr[i.id]=!0,n(!0)}))}))}}))}((function(t){var n=function(e){var t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){var n=sn();if(n){var r=n.activationStart||0,i=e.entries[e.entries.length-1],s=i.url&&performance.getEntriesByType("resource").filter((function(e){return e.name===i.url}))[0],a=Math.max(0,n.responseStart-r),o=Math.max(a,s?(s.requestStart||s.startTime)-r:0),c=Math.max(o,s?s.responseEnd-r:0),u=Math.max(c,i.startTime-r);t={element:cn(i.element),timeToFirstByte:a,resourceLoadDelay:o-a,resourceLoadDuration:c-o,elementRenderDelay:u-c,navigationEntry:n,lcpEntry:i},i.url&&(t.url=i.url),s&&(t.lcpResourceEntry=s)}}return Object.assign(e,{attribution:t})}(t);e(n)}),t)}((e=>{const{elementRenderDelay:t,resourceLoadDelay:n,resourceLoadDuration:r,timeToFirstByte:i,element:s}=e.attribution,a=this.buildInitialValues(e);this.addIfPresent(a,"element_render_delay",t),this.addIfPresent(a,"resource_load_delay",n),this.addIfPresent(a,"resource_load_duration",r),this.addIfPresent(a,mr,i);const o=this.buildInitialContext(e);this.addIfPresent(o,"element",s),this.pushMeasurement(a,o)}))}measureTTFB(){!function(e,t){ir((function(t){var n=function(e){var t={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(e.entries.length){var n=e.entries[0],r=n.activationStart||0,i=Math.max((n.workerStart||n.fetchStart)-r,0),s=Math.max(n.domainLookupStart-r,0),a=Math.max(n.connectStart-r,0),o=Math.max(n.connectEnd-r,0);t={waitingDuration:i,cacheDuration:s-i,dnsDuration:a-s,connectionDuration:o-a,requestDuration:e.value-o,navigationEntry:n}}return Object.assign(e,{attribution:t})}(t);e(n)}),t)}((e=>{const{dnsDuration:t,connectionDuration:n,requestDuration:r,waitingDuration:i,cacheDuration:s}=e.attribution,a=this.buildInitialValues(e);this.addIfPresent(a,"dns_duration",t),this.addIfPresent(a,"connection_duration",n),this.addIfPresent(a,"request_duration",r),this.addIfPresent(a,"waiting_duration",i),this.addIfPresent(a,"cache_duration",s);const o=this.buildInitialContext(e);this.pushMeasurement(a,o)}))}buildInitialValues(e){const t=e.name.toLowerCase();return{[t]:e.value,delta:e.delta}}buildInitialContext(e){var t;const n=null!==(t=Re(fr,ke.session))&&void 0!==t?t:Se;return{id:e.id,rating:e.rating,navigation_type:e.navigationType,navigation_entry_id:n}}pushMeasurement(e,t){this.corePushMeasurement({type:"web-vitals",values:e},{context:t})}addIfPresent(e,t,n){n&&(e[t]=n)}}class vr extends Ye{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-web-vitals",this.version=_}initialize(){this.logDebug("Initializing");this.intializeWebVitalsInstrumentation().initialize()}intializeWebVitalsInstrumentation(){return this.config.trackWebVitalsAttribution?new gr(this.api.pushMeasurement):new en(this.api.pushMeasurement)}}const br="session_start",yr="session_resume";class wr extends Ye{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-session",this.version=_}sendSessionStartEvent(e){var t,n;const r=e.session;if(r&&r.id!==(null===(t=this.notifiedSession)||void 0===t?void 0:t.id)){if(this.notifiedSession&&this.notifiedSession.id===(null===(n=r.attributes)||void 0===n?void 0:n.previousSession))return this.api.pushEvent("session_extend",{},void 0,{skipDedupe:!0}),void(this.notifiedSession=r);this.notifiedSession=r,this.api.pushEvent(br,{},void 0,{skipDedupe:!0})}}createInitialSession(e,t){var n,r,i,a,o;let c,u,l=e.fetchUserSession();if(t.persistent&&t.maxSessionPersistenceTime&&l){const e=s();l.lastActivity<e-t.maxSessionPersistenceTime&&(He.removeUserSession(),l=null)}if($e(l)){const e=null==l?void 0:l.sessionId;u=Ue({sessionId:e,isSampled:l.isSampled||!1,started:null==l?void 0:l.started}),u.sessionMeta={id:e,attributes:Object.assign(Object.assign(Object.assign({},null===(n=t.session)||void 0===n?void 0:n.attributes),null===(r=null==l?void 0:l.sessionMeta)||void 0===r?void 0:r.attributes),{isSampled:u.isSampled.toString()})},c=yr}else{const e=null!==(a=null===(i=t.session)||void 0===i?void 0:i.id)&&void 0!==a?a:function(e){var t,n,r,i;return{id:null!==(i=null===(r=null===(n=null===(t=$.config)||void 0===t?void 0:t.sessionTracking)||void 0===n?void 0:n.generateSessionId)||void 0===r?void 0:r.call(n))&&void 0!==i?i:_e(),attributes:e}}().id;u=Ue({sessionId:e,isSampled:Fe()}),u.sessionMeta={id:e,attributes:Object.assign({isSampled:u.isSampled.toString()},null===(o=t.session)||void 0===o?void 0:o.attributes)},c=br}return{initialSession:u,lifecycleType:c}}registerBeforeSendHook(e){var t;const{updateSession:n}=new e;null===(t=this.transports)||void 0===t||t.addBeforeSendHooks((e=>{var t,r,i;n();const s=null===(t=e.meta.session)||void 0===t?void 0:t.attributes;if(s&&"true"===(null==s?void 0:s.isSampled)){let t=JSON.parse(JSON.stringify(e));const n=null===(r=t.meta.session)||void 0===r?void 0:r.attributes;return null==n||delete n.isSampled,0===Object.keys(null!=n?n:{}).length&&(null===(i=t.meta.session)||void 0===i||delete i.attributes),t}return null}))}initialize(){this.logDebug("init session instrumentation");const e=this.config.sessionTracking;if(null==e?void 0:e.enabled){const t=Ge(e);this.registerBeforeSendHook(t);const{initialSession:n,lifecycleType:r}=this.createInitialSession(t,e);t.storeUserSession(n);const i=n.sessionMeta;this.notifiedSession=i,this.api.setSession(i),r===br&&this.api.pushEvent(br,{},void 0,{skipDedupe:!0}),r===yr&&this.api.pushEvent(yr,{},void 0,{skipDedupe:!0})}this.metas.addListener(this.sendSessionStartEvent.bind(this))}}class Er extends Ye{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-view",this.version=_}sendViewChangedEvent(e){var t,n,r,i;const s=e.view;s&&s.name!==(null===(t=this.notifiedView)||void 0===t?void 0:t.name)&&(this.api.pushEvent("view_changed",{fromView:null!==(r=null===(n=this.notifiedView)||void 0===n?void 0:n.name)&&void 0!==r?r:Se,toView:null!==(i=s.name)&&void 0!==i?i:Se},void 0,{skipDedupe:!0}),this.notifiedView=s)}initialize(){this.metas.addListener(this.sendViewChangedEvent.bind(this))}}const Sr=/^00-[a-f0-9]{32}-[a-f0-9]{16}-[0-9]{1,2}$/;function Tr(e=[]){for(const t of e)if("traceparent"===t.name){if(!Sr.test(t.description))continue;const[,e,n]=t.description.split("-");if(null!=e&&null!=n)return{traceId:e,spanId:n};break}}function Or(e=[],t){return e.some((e=>e&&null!=t.match(e)))}function xr(e,t={}){for(const[n,r]of Object.entries(t)){const t=e[n];return null!=t&&(g(r)?r.includes(t):t===r)}return!0}function Cr(e){const{connectEnd:t,connectStart:n,decodedBodySize:r,domainLookupEnd:i,domainLookupStart:s,duration:a,encodedBodySize:o,fetchStart:c,initiatorType:u,name:l,nextHopProtocol:d,redirectEnd:p,redirectStart:f,renderBlockingStatus:h,requestStart:m,responseEnd:g,responseStart:v,responseStatus:b,secureConnectionStart:y,transferSize:w,workerStart:E}=e;return{name:l,duration:Ir(a),tcpHandshakeTime:Ir(t-n),dnsLookupTime:Ir(i-s),tlsNegotiationTime:Ir(m-y),responseStatus:Ir(b),redirectTime:Ir(p-f),requestTime:Ir(v-m),responseTime:Ir(g-v),fetchTime:Ir(g-c),serviceWorkerTime:Ir(c-E),decodedBodySize:Ir(r),encodedBodySize:Ir(o),cacheHitStatus:function(){let e="fullLoad";0===w?r>0&&(e="cache"):null!=b?304===b&&(e="conditionalFetch"):o>0&&w<o&&(e="conditionalFetch");return e}(),renderBlockingStatus:Ir(h),protocol:d,initiatorType:u}}function Pr(e){const{activationStart:t,domComplete:n,domContentLoadedEventEnd:r,domContentLoadedEventStart:i,domInteractive:s,fetchStart:a,loadEventEnd:o,loadEventStart:c,responseStart:u,type:l}=e,d=function(){var e;if(null!=(null===(e=performance.timing)||void 0===e?void 0:e.domLoading))return performance.timing.domLoading-performance.timeOrigin;return null}();return Object.assign({visibilityState:document.visibilityState,pageLoadTime:Ir(n-a),documentParsingTime:Ir(d?s-d:null),domProcessingTime:Ir(n-s),domContentLoadHandlerTime:Ir(r-i),onLoadTime:Ir(o-c),ttfb:Ir(Math.max(u-(null!=t?t:0),0)),type:l},Cr(e))}function Ir(e){return null==e?Se:"number"==typeof e?Math.round(e).toString():e.toString()}const kr={initiatorType:["xmlhttprequest","fetch"]};var Nr=function(e,t,n,r){return new(n||(n=Promise))((function(i,s){function a(e){try{c(r.next(e))}catch(e){s(e)}}function o(e){try{c(r.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,o)}c((r=r.apply(e,t||[])).next())}))};class Rr extends Ye{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-performance",this.version=_}initialize(){"PerformanceObserver"in window?function(e){if("complete"===document.readyState)e();else{const t=()=>{"complete"===document.readyState&&(e(),document.removeEventListener("readystatechange",t))};document.addEventListener("readystatechange",t)}}((()=>Nr(this,void 0,void 0,(function*(){const e=this.api.pushEvent,t=this.getIgnoreUrls(),{faroNavigationId:n}=yield function(e,t){let n;const r=new Promise((e=>{n=e}));return new PerformanceObserver((r=>{var i;const[s]=r.getEntries();if(null==s||Or(t,s.name))return;const a=s.toJSON();let o=Tr(null==a?void 0:a.serverTiming);const c=null!==(i=Re(fr,ke.session))&&void 0!==i?i:Se,u=Object.assign(Object.assign({},Pr(a)),{faroNavigationId:_e(),faroPreviousNavigationId:c});Ae(fr,u.faroNavigationId,ke.session),e("faro.performance.navigation",u,void 0,{spanContext:o,timestampOverwriteMs:performance.timeOrigin+a.startTime}),n(u)})).observe({type:"navigation",buffered:!0}),r}(e,t);null!=n&&function(e,t,n){const r=$.config.trackResources;new PerformanceObserver((i=>{const s=i.getEntries();for(const i of s){if(Or(n,i.name))return;const s=i.toJSON();let a=Tr(null==s?void 0:s.serverTiming);if(null==r&&xr(s,kr)||r){const n=Object.assign(Object.assign({},Cr(s)),{faroNavigationId:e,faroResourceId:_e()});t("faro.performance.resource",n,void 0,{spanContext:a,timestampOverwriteMs:performance.timeOrigin+s.startTime})}}})).observe({type:"resource",buffered:!0})}(n,e,t)})))):this.logDebug("performance observer not supported. Disable performance instrumentation.")}getIgnoreUrls(){var e;return null===(e=this.transports.transports)||void 0===e?void 0:e.flatMap((e=>e.getIgnoreUrls()))}}class Ar extends Ye{constructor(e={}){super(),this.options=e,this.name="@grafana/faro-web-sdk:instrumentation-console",this.version=_}initialize(){this.logDebug("Initializing\n",this.options),P.HT.filter((e=>{var t;return!(null!==(t=this.options.disabledLevels)&&void 0!==t?t:Ar.defaultDisabledLevels).includes(e)})).forEach((e=>{console[e]=(...t)=>{try{this.api.pushLog(t,{level:e})}catch(e){this.logError(e)}finally{this.unpatchedConsole[e](...t)}}}))}}function jr(e={}){const t=[new it,new vr,new wr,new Er];return!1!==e.enablePerformanceInstrumentation&&t.unshift(new Rr),!1!==e.captureConsole&&t.push(new Ar({disabledLevels:e.captureConsoleDisabledLevels})),t}function Lr(e){const t=function(e){var t,n,r,i,s,a,o,c,u;const l=[],d=D(e.unpatchedConsole,e.internalLoggerLevel);return e.transports?((e.url||e.apiKey)&&d.error('if "transports" is defined, "url" and "apiKey" should not be defined'),l.push(...e.transports)):e.url?l.push(new Ve({url:e.url,apiKey:e.apiKey})):d.error('either "url" or "transports" must be defined'),{app:e.app,batching:Object.assign(Object.assign({},Y),e.batching),dedupe:null===(t=e.dedupe)||void 0===t||t,globalObjectKey:e.globalObjectKey||V,instrumentations:null!==(n=e.instrumentations)&&void 0!==n?n:jr(),internalLoggerLevel:null!==(r=e.internalLoggerLevel)&&void 0!==r?r:j,isolate:null!==(i=e.isolate)&&void 0!==i&&i,logArgsSerializer:null!==(s=e.logArgsSerializer)&&void 0!==s?s:I,metas:function(){const t=Te;return e.metas&&t.push(...e.metas),h(window.k6)?[...t,Oe]:t}(),parseStacktrace:ge,paused:null!==(a=e.paused)&&void 0!==a&&a,preventGlobalExposure:null!==(o=e.preventGlobalExposure)&&void 0!==o&&o,transports:l,unpatchedConsole:null!==(c=e.unpatchedConsole)&&void 0!==c?c:L,beforeSend:e.beforeSend,eventDomain:null!==(u=e.eventDomain)&&void 0!==u?u:J,ignoreErrors:e.ignoreErrors,ignoreUrls:e.ignoreUrls,sessionTracking:Object.assign(Object.assign({},we),e.sessionTracking),user:e.user,view:e.view,trackResources:e.trackResources,trackWebVitalsAttribution:e.trackWebVitalsAttribution}}(e);if(t)return W(t)}Ar.defaultDisabledLevels=[P.$b.DEBUG,P.$b.TRACE,P.$b.LOG];var Dr=n(8531),Mr=n(4137),Fr=n(5176),qr=n(7421);const _r=new Map([["dev",{environment:"dev",appName:"grafana-pyroscope-dev",faroUrl:"https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/48e03a2647389f2f6494af7f975b4084"}],["ops",{environment:"ops",appName:"grafana-pyroscope-ops",faroUrl:"https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/b5cfd5eeb412cf5e74bd828b4ddd17ff"}],["prod",{environment:"prod",appName:"grafana-pyroscope-prod",faroUrl:"https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/6cbe17b3af4b72ce5936bf4d15a5c393"}]]);let Ur=null;const $r=()=>Ur,Qr=e=>Ur=e;function Br(){if($r())return;const e=function(){const e=(0,qr.u)();if(e&&_r.has(e))return _r.get(e)}();if(!e)return;const{environment:t,faroUrl:n,appName:r}=e,{apps:i,bootData:s,buildInfo:a}=Dr.config,o=i[Mr.R2].version,c=s.user.email,u=`v${a.version} (${a.edition})`;Qr(Lr({url:n,app:{name:r,release:o,version:Fr.t,environment:t,namespace:u},user:{email:c},instrumentations:[...jr({captureConsole:!1})],isolate:!0,beforeSend:e=>{var t,n,r;return(null!==(n=null===(t=e.meta.page)||void 0===t?void 0:t.url)&&void 0!==n?n:"").includes(Mr.Gy)?(e.meta.view={name:new URLSearchParams(null===(r=e.meta.page)||void 0===r?void 0:r.url).get("explorationType")||""},e):null}}))}},7421:(e,t,n)=>{"use strict";n.d(t,{u:()=>i});const r=[{regExp:/localhost/,environment:"local"},{regExp:/grafana-dev\.net/,environment:"dev"},{regExp:/grafana-ops\.net/,environment:"ops"},{regExp:/grafana\.net/,environment:"prod"}];function i(){var e,t;if(!(null===(t=window)||void 0===t||null===(e=t.location)||void 0===e?void 0:e.host))return null;const n=r.find((({regExp:e})=>e.test(window.location.host)));return n?n.environment:null}},2096:(e,t,n)=>{"use strict";n.d(t,{v:()=>f});var r=n(5438),i=n(5377),s=n(7421);function a(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function o(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function c(e,t,n){return function(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=n}}(e,o(e,t,"set"),n),n}function u(e,t,n){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return n}var l=new WeakMap,d=new WeakSet;function p(e,t){var n;"prod"!==function(e,t){return t.get?t.get.call(e):t.value}(n=this,o(n,l,"get"))&&console[e](...t)}const f=new class{trace(){var e;u(this,d,p).call(this,"trace",[]),null===(e=(0,i.n1)())||void 0===e||e.api.pushLog([],{level:r.$b.TRACE})}debug(...e){var t;u(this,d,p).call(this,"debug",e),null===(t=(0,i.n1)())||void 0===t||t.api.pushLog(e,{level:r.$b.DEBUG})}info(...e){var t;u(this,d,p).call(this,"info",e),null===(t=(0,i.n1)())||void 0===t||t.api.pushLog(e,{level:r.$b.INFO})}log(...e){var t;u(this,d,p).call(this,"log",e),null===(t=(0,i.n1)())||void 0===t||t.api.pushLog(e,{level:r.$b.LOG})}warn(...e){var t;u(this,d,p).call(this,"warn",e),null===(t=(0,i.n1)())||void 0===t||t.api.pushLog(e,{level:r.$b.WARN})}error(e,t){var n;u(this,d,p).call(this,"error",[e]),t&&u(this,d,p).call(this,"error",["Error context",t]),null===(n=(0,i.n1)())||void 0===n||n.api.pushError(e,{context:t})}constructor(){var e,t;a(e=this,t=d),t.add(e),function(e,t,n){a(e,t),t.set(e,n)}(this,l,{writable:!0,value:void 0}),c(this,l,(0,s.u)())}}},550:(e,t,n)=>{"use strict";n.d(t,{x:()=>c});var r=n(2096),i=n(2533);function s(e,t){var n=function(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}(e,t,"get");return function(e,t){return t.get?t.get.call(e):t.value}(e,n)}function a(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var o=new WeakMap;const c=new class{has(e){return s(this,o).hasOwnProperty(e)}get(e){if(!this.has(e))return null;try{return JSON.parse(s(this,o).getItem(e))}catch(t){return r.v.error(t,{info:`Error parsing JSON for storage item "${e}"!`}),null}}set(e,t){try{s(this,o).setItem(e,JSON.stringify(t))}catch(t){r.v.error(t,{info:`Error setting storage item "${e}"!`})}}constructor(){var e,t,n;a(this,o,{writable:!0,value:window.localStorage}),e=this,t="KEYS",n={SETTINGS:`${i.id}.userSettings`,GITHUB_INTEGRATION:`${i.id}.gitHubIntegration`,PROFILES_EXPLORER:`${i.id}.profilesExplorer`},t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}}},8536:(e,t,n)=>{"use strict";n.d(t,{_:()=>o});var r=n(2007),i=n(2096),s=n(5959),a=n.n(s);function o({severity:e,title:t,message:n,error:s,errorContext:o}){return s&&i.v.error(s,o),a().createElement(r.Alert,{title:t,severity:e},s&&a().createElement(a().Fragment,null,s.message,a().createElement("br",null)),n)}},1049:(e,t,n)=>{"use strict";n.d(t,{s:()=>Ce});var r=n(6089),i=n(2007),s=n(7781),a=n(5959),o=n.n(a);const c=e=>({row:(0,r.css)({display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"flex-start"}),headerColumn:(0,r.css)({display:"flex",flexDirection:"column",minWidth:"120px",alignItems:"start"}),column:(0,r.css)({display:"flex",flexDirection:"column",minWidth:"120px",alignItems:"end"}),tooltip:(0,r.css)({display:"flex",color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize}),contentWithIcon:(0,r.css)({display:"none",[`@media ${i.styleMixins.mediaUp(e.v1.breakpoints.sm)}`]:{display:"block"}})}),u=e=>(0,s.formattedValueToString)((0,s.getValueFormat)("decbytes")(e)),l=e=>(0,s.formattedValueToString)((0,s.getValueFormat)("short")(e));function d(e){const t=(0,i.useStyles2)(c),{data:n}=e,r=u(n.queryImpact.totalBytesInTimeRange),s=(0,a.useMemo)((()=>o().createElement("div",{"data-testid":"queryAnalysis-popup"},o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Data in time range"),o().createElement("div",{className:t.column},r),o().createElement("div",{className:t.column}," ")),void 0!==n.queryImpact.totalQueriedSeries&&o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Series in query"),o().createElement("div",{className:t.column},l(n.queryImpact.totalQueriedSeries)),o().createElement("div",{className:t.column}," ")),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Deduplication"),o().createElement("div",{className:t.column},n.queryImpact.deduplicationNeeded?"yes":"no"),o().createElement("div",{className:t.column}," ")),o().createElement(i.Divider,null),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn}," "),n.queryScopes.map(((e,n)=>o().createElement("div",{key:n,className:t.column},o().createElement("strong",null,e.componentType))))),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Replicas"),n.queryScopes.map(((e,n)=>o().createElement("div",{key:n,className:t.column},e.componentCount||"/")))),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Blocks"),n.queryScopes.map(((e,n)=>o().createElement("div",{key:n,className:t.column},l(e.blockCount)||"/")))),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Series"),n.queryScopes.map(((e,n)=>o().createElement("div",{key:n,className:t.column},l(e.seriesCount)||"/")))),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Profiles"),n.queryScopes.map(((e,n)=>o().createElement("div",{key:n,className:t.column},l(e.profileCount)||"/")))),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Samples"),n.queryScopes.map(((e,n)=>o().createElement("div",{key:n,className:t.column},l(e.sampleCount)||"/")))),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn}," "),o().createElement("div",{className:t.column}," "),o().createElement("div",{className:t.column}," ")),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Index Store"),n.queryScopes.map(((e,n)=>o().createElement("div",{key:n,className:t.column},u(e.indexBytes)||"/")))),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Profiles Store"),n.queryScopes.map(((e,n)=>o().createElement("div",{key:n,className:t.column},u(e.profileBytes)||"/")))),o().createElement("div",{className:t.row},o().createElement("div",{className:t.headerColumn},"Symbols Store"),n.queryScopes.map(((e,n)=>o().createElement("div",{key:n,className:t.column},u(e.symbolBytes)||"/")))))),[n,t,r]);return o().createElement(o().Fragment,null,void 0!==n.queryImpact.totalBytesInTimeRange?o().createElement(i.Toggletip,{content:s,fitContent:!0},o().createElement("div",{className:t.tooltip,"data-testid":"queryAnalysis-tooltip"},o().createElement("span",{className:t.contentWithIcon},"Stored data in time range: ",r)," ",o().createElement(i.IconButton,{name:"database","aria-label":"Query info"}))):null)}var p,f,h,m,g=n(2688),v=n.n(g),b=n(9406),y=n.n(b),w=n(7383),E=n.n(w),S=n(1664),T=n.n(S),O="bodyAttributes",x="htmlAttributes",C="titleAttributes",P={BASE:"base",BODY:"body",HEAD:"head",HTML:"html",LINK:"link",META:"meta",NOSCRIPT:"noscript",SCRIPT:"script",STYLE:"style",TITLE:"title"},I=(Object.keys(P).map((function(e){return P[e]})),"charset"),k="cssText",N="href",R="http-equiv",A="innerHTML",j="itemprop",L="name",D="property",M="rel",F="src",q="target",_={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},U="defaultTitle",$="defer",Q="encodeSpecialCharacters",B="onChangeClientState",H="titleTemplate",z=Object.keys(_).reduce((function(e,t){return e[_[t]]=t,e}),{}),G=[P.NOSCRIPT,P.SCRIPT,P.STYLE],K="data-react-helmet",W="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},V=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Y=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},J=function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},X=function(e){return!1===(!(arguments.length>1&&void 0!==arguments[1])||arguments[1])?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")},Z=function(e){var t=ie(e,P.TITLE),n=ie(e,H);if(n&&t)return n.replace(/%s/g,(function(){return Array.isArray(t)?t.join(""):t}));var r=ie(e,U);return t||r||void 0},ee=function(e){return ie(e,B)||function(){}},te=function(e,t){return t.filter((function(t){return void 0!==t[e]})).map((function(t){return t[e]})).reduce((function(e,t){return Y({},e,t)}),{})},ne=function(e,t){return t.filter((function(e){return void 0!==e[P.BASE]})).map((function(e){return e[P.BASE]})).reverse().reduce((function(t,n){if(!t.length)for(var r=Object.keys(n),i=0;i<r.length;i++){var s=r[i].toLowerCase();if(-1!==e.indexOf(s)&&n[s])return t.concat(n)}return t}),[])},re=function(e,t,n){var r={};return n.filter((function(t){return!!Array.isArray(t[e])||(void 0!==t[e]&&ue("Helmet: "+e+' should be of type "Array". Instead found type "'+W(t[e])+'"'),!1)})).map((function(t){return t[e]})).reverse().reduce((function(e,n){var i={};n.filter((function(e){for(var n=void 0,s=Object.keys(e),a=0;a<s.length;a++){var o=s[a],c=o.toLowerCase();-1===t.indexOf(c)||n===M&&"canonical"===e[n].toLowerCase()||c===M&&"stylesheet"===e[c].toLowerCase()||(n=c),-1===t.indexOf(o)||o!==A&&o!==k&&o!==j||(n=o)}if(!n||!e[n])return!1;var u=e[n].toLowerCase();return r[n]||(r[n]={}),i[n]||(i[n]={}),!r[n][u]&&(i[n][u]=!0,!0)})).reverse().forEach((function(t){return e.push(t)}));for(var s=Object.keys(i),a=0;a<s.length;a++){var o=s[a],c=T()({},r[o],i[o]);r[o]=c}return e}),[]).reverse()},ie=function(e,t){for(var n=e.length-1;n>=0;n--){var r=e[n];if(r.hasOwnProperty(t))return r[t]}return null},se=(p=Date.now(),function(e){var t=Date.now();t-p>16?(p=t,e(t)):setTimeout((function(){se(e)}),0)}),ae=function(e){return clearTimeout(e)},oe="undefined"!=typeof window?window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||se:n.g.requestAnimationFrame||se,ce="undefined"!=typeof window?window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||ae:n.g.cancelAnimationFrame||ae,ue=function(e){return console&&"function"==typeof console.warn&&console.warn(e)},le=null,de=function(e,t){var n=e.baseTag,r=e.bodyAttributes,i=e.htmlAttributes,s=e.linkTags,a=e.metaTags,o=e.noscriptTags,c=e.onChangeClientState,u=e.scriptTags,l=e.styleTags,d=e.title,p=e.titleAttributes;he(P.BODY,r),he(P.HTML,i),fe(d,p);var f={baseTag:me(P.BASE,n),linkTags:me(P.LINK,s),metaTags:me(P.META,a),noscriptTags:me(P.NOSCRIPT,o),scriptTags:me(P.SCRIPT,u),styleTags:me(P.STYLE,l)},h={},m={};Object.keys(f).forEach((function(e){var t=f[e],n=t.newTags,r=t.oldTags;n.length&&(h[e]=n),r.length&&(m[e]=f[e].oldTags)})),t&&t(),c(e,h,m)},pe=function(e){return Array.isArray(e)?e.join(""):e},fe=function(e,t){void 0!==e&&document.title!==e&&(document.title=pe(e)),he(P.TITLE,t)},he=function(e,t){var n=document.getElementsByTagName(e)[0];if(n){for(var r=n.getAttribute(K),i=r?r.split(","):[],s=[].concat(i),a=Object.keys(t),o=0;o<a.length;o++){var c=a[o],u=t[c]||"";n.getAttribute(c)!==u&&n.setAttribute(c,u),-1===i.indexOf(c)&&i.push(c);var l=s.indexOf(c);-1!==l&&s.splice(l,1)}for(var d=s.length-1;d>=0;d--)n.removeAttribute(s[d]);i.length===s.length?n.removeAttribute(K):n.getAttribute(K)!==a.join(",")&&n.setAttribute(K,a.join(","))}},me=function(e,t){var n=document.head||document.querySelector(P.HEAD),r=n.querySelectorAll(e+"["+K+"]"),i=Array.prototype.slice.call(r),s=[],a=void 0;return t&&t.length&&t.forEach((function(t){var n=document.createElement(e);for(var r in t)if(t.hasOwnProperty(r))if(r===A)n.innerHTML=t.innerHTML;else if(r===k)n.styleSheet?n.styleSheet.cssText=t.cssText:n.appendChild(document.createTextNode(t.cssText));else{var o=void 0===t[r]?"":t[r];n.setAttribute(r,o)}n.setAttribute(K,"true"),i.some((function(e,t){return a=t,n.isEqualNode(e)}))?i.splice(a,1):s.push(n)})),i.forEach((function(e){return e.parentNode.removeChild(e)})),s.forEach((function(e){return n.appendChild(e)})),{oldTags:i,newTags:s}},ge=function(e){return Object.keys(e).reduce((function(t,n){var r=void 0!==e[n]?n+'="'+e[n]+'"':""+n;return t?t+" "+r:r}),"")},ve=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.keys(e).reduce((function(t,n){return t[_[n]||n]=e[n],t}),t)},be=function(e,t,n){switch(e){case P.TITLE:return{toComponent:function(){return e=t.title,n=t.titleAttributes,(r={key:e})[K]=!0,i=ve(n,r),[o().createElement(P.TITLE,i,e)];var e,n,r,i},toString:function(){return function(e,t,n,r){var i=ge(n),s=pe(t);return i?"<"+e+" "+K+'="true" '+i+">"+X(s,r)+"</"+e+">":"<"+e+" "+K+'="true">'+X(s,r)+"</"+e+">"}(e,t.title,t.titleAttributes,n)}};case O:case x:return{toComponent:function(){return ve(t)},toString:function(){return ge(t)}};default:return{toComponent:function(){return function(e,t){return t.map((function(t,n){var r,i=((r={key:n})[K]=!0,r);return Object.keys(t).forEach((function(e){var n=_[e]||e;if(n===A||n===k){var r=t.innerHTML||t.cssText;i.dangerouslySetInnerHTML={__html:r}}else i[n]=t[e]})),o().createElement(e,i)}))}(e,t)},toString:function(){return function(e,t,n){return t.reduce((function(t,r){var i=Object.keys(r).filter((function(e){return!(e===A||e===k)})).reduce((function(e,t){var i=void 0===r[t]?t:t+'="'+X(r[t],n)+'"';return e?e+" "+i:i}),""),s=r.innerHTML||r.cssText||"",a=-1===G.indexOf(e);return t+"<"+e+" "+K+'="true" '+i+(a?"/>":">"+s+"</"+e+">")}),"")}(e,t,n)}}}},ye=function(e){var t=e.baseTag,n=e.bodyAttributes,r=e.encode,i=e.htmlAttributes,s=e.linkTags,a=e.metaTags,o=e.noscriptTags,c=e.scriptTags,u=e.styleTags,l=e.title,d=void 0===l?"":l,p=e.titleAttributes;return{base:be(P.BASE,t,r),bodyAttributes:be(O,n,r),htmlAttributes:be(x,i,r),link:be(P.LINK,s,r),meta:be(P.META,a,r),noscript:be(P.NOSCRIPT,o,r),script:be(P.SCRIPT,c,r),style:be(P.STYLE,u,r),title:be(P.TITLE,{title:d,titleAttributes:p},r)}},we=y()((function(e){return{baseTag:ne([N,q],e),bodyAttributes:te(O,e),defer:ie(e,$),encode:ie(e,Q),htmlAttributes:te(x,e),linkTags:re(P.LINK,[M,N],e),metaTags:re(P.META,[L,I,R,D,j],e),noscriptTags:re(P.NOSCRIPT,[A],e),onChangeClientState:ee(e),scriptTags:re(P.SCRIPT,[F,A],e),styleTags:re(P.STYLE,[k],e),title:Z(e),titleAttributes:te(C,e)}}),(function(e){le&&ce(le),e.defer?le=oe((function(){de(e,(function(){le=null}))})):(de(e),le=null)}),ye)((function(){return null})),Ee=(f=we,m=h=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,e.apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t.prototype.shouldComponentUpdate=function(e){return!E()(this.props,e)},t.prototype.mapNestedChildrenToProps=function(e,t){if(!t)return null;switch(e.type){case P.SCRIPT:case P.NOSCRIPT:return{innerHTML:t};case P.STYLE:return{cssText:t}}throw new Error("<"+e.type+" /> elements are self-closing and can not contain children. Refer to our API for more information.")},t.prototype.flattenArrayTypeChildren=function(e){var t,n=e.child,r=e.arrayTypeChildren,i=e.newChildProps,s=e.nestedChildren;return Y({},r,((t={})[n.type]=[].concat(r[n.type]||[],[Y({},i,this.mapNestedChildrenToProps(n,s))]),t))},t.prototype.mapObjectTypeChildren=function(e){var t,n,r=e.child,i=e.newProps,s=e.newChildProps,a=e.nestedChildren;switch(r.type){case P.TITLE:return Y({},i,((t={})[r.type]=a,t.titleAttributes=Y({},s),t));case P.BODY:return Y({},i,{bodyAttributes:Y({},s)});case P.HTML:return Y({},i,{htmlAttributes:Y({},s)})}return Y({},i,((n={})[r.type]=Y({},s),n))},t.prototype.mapArrayTypeChildrenToProps=function(e,t){var n=Y({},t);return Object.keys(e).forEach((function(t){var r;n=Y({},n,((r={})[t]=e[t],r))})),n},t.prototype.warnOnInvalidChildren=function(e,t){return!0},t.prototype.mapChildrenToProps=function(e,t){var n=this,r={};return o().Children.forEach(e,(function(e){if(e&&e.props){var i=e.props,s=i.children,a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.keys(e).reduce((function(t,n){return t[z[n]||n]=e[n],t}),t)}(J(i,["children"]));switch(n.warnOnInvalidChildren(e,s),e.type){case P.LINK:case P.META:case P.NOSCRIPT:case P.SCRIPT:case P.STYLE:r=n.flattenArrayTypeChildren({child:e,arrayTypeChildren:r,newChildProps:a,nestedChildren:s});break;default:t=n.mapObjectTypeChildren({child:e,newProps:t,newChildProps:a,nestedChildren:s})}}})),t=this.mapArrayTypeChildrenToProps(r,t)},t.prototype.render=function(){var e=this.props,t=e.children,n=J(e,["children"]),r=Y({},n);return t&&(r=this.mapChildrenToProps(t,r)),o().createElement(f,r)},V(t,null,[{key:"canUseDOM",set:function(e){f.canUseDOM=e}}]),t}(o().Component),h.propTypes={base:v().object,bodyAttributes:v().object,children:v().oneOfType([v().arrayOf(v().node),v().node]),defaultTitle:v().string,defer:v().bool,encodeSpecialCharacters:v().bool,htmlAttributes:v().object,link:v().arrayOf(v().object),meta:v().arrayOf(v().object),noscript:v().arrayOf(v().object),onChangeClientState:v().func,script:v().arrayOf(v().object),style:v().arrayOf(v().object),title:v().string,titleAttributes:v().object,titleTemplate:v().string},h.defaultProps={defer:!0,encodeSpecialCharacters:!0},h.peek=f.peek,h.rewind=function(){var e=f.rewind();return e||(e=ye({baseTag:[],bodyAttributes:{},encodeSpecialCharacters:!0,htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}})),e},m);Ee.renderStatic=Ee.rewind;var Se=n(2932),Te=n(9660);function Oe({title:e,queryAnalysis:t}){const n=(0,i.useStyles2)(xe),r="string"==typeof e?`${e} | Pyroscope`:"Pyroscope";return o().createElement(o().Fragment,null,o().createElement(Ee,null,o().createElement("title",null,r)),o().createElement("div",{className:n.titleContainer},o().createElement(i.Stack,{justifyContent:"space-between"},o().createElement("div",null,o().createElement(Te.S,{size:"large"}),o().createElement("h1",{className:n.title,"data-testid":"page-title"},e)),o().createElement("div",{className:n.infoArea},o().createElement(Se.U,null),t?o().createElement(d,{data:t}):null))))}const xe=e=>({titleContainer:r.css`
    height: ${e.spacing(5)};
    line-height: ${e.spacing(5)};
    margin-bottom: ${e.spacing(3)};
  `,title:r.css`
    font-size: ${e.typography.h2.fontSize};
    display: inline-block;
    margin: 0;
    position: relative;
    top: 10px;
    left: ${e.spacing(1)};
  `,infoArea:r.css`
    align-self: end;
    margin-bottom: 0;
    line-height: 20px;
    text-align: right;
  `}),Ce=(0,a.memo)(Oe)},2932:(e,t,n)=>{"use strict";n.d(t,{U:()=>g});var r=n(6089),i=n(7781),s=n(8531),a=n(2007),o=n(5959),c=n.n(o),u=n(5176),l=n(9660);const d=u.t,p=`https://github.com/grafana/explore-profiles/commit/${d}`,{buildInfo:f}=s.config;function h(){const e=(0,a.useStyles2)(v),{meta:{info:{version:t,updated:n}}}=(0,i.usePluginContext)()||{meta:{info:{version:"?.?.?",updated:"?"}}};return c().createElement("div",{className:e.menuHeader},c().createElement("h5",null,c().createElement(l.S,{size:"small"}),"Explore profiles v",t),c().createElement("div",{className:e.subTitle},"Last update: ",n))}function m(){const e="dev"===d,t=e?d:d.slice(0,8);return c().createElement(a.Menu,{header:c().createElement(h,null)},c().createElement(a.Menu.Item,{label:`Commit SHA: ${t}`,icon:"github",onClick:()=>window.open(p),disabled:e}),c().createElement(a.Menu.Item,{label:"Changelog",icon:"list-ul",onClick:()=>window.open("https://github.com/grafana/explore-profiles/blob/main/CHANGELOG.md")}),c().createElement(a.Menu.Item,{label:"Contribute",icon:"external-link-alt",onClick:()=>window.open("https://github.com/grafana/explore-profiles/blob/main/docs/CONTRIBUTING.md")}),c().createElement(a.Menu.Item,{label:"Documentation",icon:"document-info",onClick:()=>window.open("https://grafana.com/docs/grafana/latest/explore/simplified-exploration/profiles")}),c().createElement(a.Menu.Item,{label:"Report an issue",icon:"bug",onClick:()=>window.open("https://github.com/grafana/explore-profiles/issues/new?template=bug_report.md")}),c().createElement(a.Menu.Divider,null),c().createElement(a.Menu.Item,{label:`Grafana ${f.edition} v${f.version} (${f.env})`,icon:"github",onClick:()=>window.open(`https://github.com/grafana/grafana/commit/${f.commit}`)}))}function g(){return c().createElement(a.Dropdown,{overlay:()=>c().createElement(m,null),placement:"bottom-end"},c().createElement(a.IconButton,{name:"info-circle","aria-label":"Plugin info",title:"Plugin info"}))}const v=e=>({menuHeader:r.css`
    padding: ${e.spacing(.5,1)};
    white-space: nowrap;
  `,subTitle:r.css`
    color: ${e.colors.text.secondary};
    font-size: ${e.typography.bodySmall.fontSize};
  `})},9660:(e,t,n)=>{"use strict";n.d(t,{S:()=>c});var r=n(6089),i=n(2007),s=n(5959),a=n.n(s);function o({size:e}){const t=(0,i.useStyles2)(u);return a().createElement("img",{className:(0,r.cx)(t.logo,e),src:"public/plugins/grafana-pyroscope-app/img/logo.svg"})}const c=a().memo(o),u=()=>({logo:r.css`
    &.small {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      position: relative;
      top: -2px;
    }

    &.large {
      width: 40px;
      height: 40px;
    }
  `})},5176:(e,t,n)=>{"use strict";n.d(t,{t:()=>r});const r="e07aff41c3f5e568cddcfb4ce36d38234403230a"},6660:function(e,t,n){var r;!function(i,s){"use strict";var a="function",o="undefined",c="object",u="string",l="major",d="model",p="name",f="type",h="vendor",m="version",g="architecture",v="console",b="mobile",y="tablet",w="smarttv",E="wearable",S="embedded",T="Amazon",O="Apple",x="ASUS",C="BlackBerry",P="Browser",I="Chrome",k="Firefox",N="Google",R="Huawei",A="LG",j="Microsoft",L="Motorola",D="Opera",M="Samsung",F="Sharp",q="Sony",_="Xiaomi",U="Zebra",$="Facebook",Q="Chromium OS",B="Mac OS",H=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},z=function(e,t){return typeof e===u&&-1!==G(t).indexOf(G(e))},G=function(e){return e.toLowerCase()},K=function(e,t){if(typeof e===u)return e=e.replace(/^\s\s*/,""),typeof t===o?e:e.substring(0,500)},W=function(e,t){for(var n,r,i,o,u,l,d=0;d<t.length&&!u;){var p=t[d],f=t[d+1];for(n=r=0;n<p.length&&!u&&p[n];)if(u=p[n++].exec(e))for(i=0;i<f.length;i++)l=u[++r],typeof(o=f[i])===c&&o.length>0?2===o.length?typeof o[1]==a?this[o[0]]=o[1].call(this,l):this[o[0]]=o[1]:3===o.length?typeof o[1]!==a||o[1].exec&&o[1].test?this[o[0]]=l?l.replace(o[1],o[2]):s:this[o[0]]=l?o[1].call(this,l,o[2]):s:4===o.length&&(this[o[0]]=l?o[3].call(this,l.replace(o[1],o[2])):s):this[o]=l||s;d+=2}},V=function(e,t){for(var n in t)if(typeof t[n]===c&&t[n].length>0){for(var r=0;r<t[n].length;r++)if(z(t[n][r],e))return"?"===n?s:n}else if(z(t[n],e))return"?"===n?s:n;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},J={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,m],[/opios[\/ ]+([\w\.]+)/i],[m,[p,D+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[m,[p,D+" GX"]],[/\bopr\/([\w\.]+)/i],[m,[p,D]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[m,[p,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,m],[/\bddg\/([\w\.]+)/i],[m,[p,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[p,"UC"+P]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[m,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[p,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[m,[p,"Smart Lenovo "+P]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+P],m],[/\bfocus\/([\w\.]+)/i],[m,[p,k+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[p,D+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[p,D+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[p,"MIUI "+P]],[/fxios\/([-\w\.]+)/i],[m,[p,k]],[/\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+P]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1 "+P],m],[/samsungbrowser\/([\w\.]+)/i],[m,[p,M+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[p,/_/g," "],m],[/metasr[\/ ]?([\d\.]+)/i],[m,[p,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[p,"Sogou Mobile"],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[p,m],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,$],m],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[p,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[p,I+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,I+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[p,"Android "+P]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[m,V,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[p,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[p,k+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[p,m],[/(cobalt)\/([\w\.]+)/i],[p,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,G]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",G]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,G]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[d,[h,M],[f,y]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[d,[h,M],[f,b]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[d,[h,O],[f,b]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[d,[h,O],[f,y]],[/(macintosh);/i],[d,[h,O]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[d,[h,F],[f,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[d,[h,R],[f,y]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[d,[h,R],[f,b]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[d,/_/g," "],[h,_],[f,b]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[d,/_/g," "],[h,_],[f,y]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[d,[h,"OPPO"],[f,b]],[/\b(opd2\d{3}a?) bui/i],[d,[h,"OPPO"],[f,y]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[d,[h,"Vivo"],[f,b]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[d,[h,"Realme"],[f,b]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[d,[h,L],[f,b]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[d,[h,L],[f,y]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[d,[h,A],[f,y]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[d,[h,A],[f,b]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[d,[h,"Lenovo"],[f,y]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[d,/_/g," "],[h,"Nokia"],[f,b]],[/(pixel c)\b/i],[d,[h,N],[f,y]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[d,[h,N],[f,b]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[d,[h,q],[f,b]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[d,"Xperia Tablet"],[h,q],[f,y]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[d,[h,"OnePlus"],[f,b]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[d,[h,T],[f,y]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[d,/(.+)/g,"Fire Phone $1"],[h,T],[f,b]],[/(playbook);[-\w\),; ]+(rim)/i],[d,h,[f,y]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[d,[h,C],[f,b]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[d,[h,x],[f,y]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[d,[h,x],[f,b]],[/(nexus 9)/i],[d,[h,"HTC"],[f,y]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[h,[d,/_/g," "],[f,b]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[d,[h,"Acer"],[f,y]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[d,[h,"Meizu"],[f,b]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[d,[h,"Ulefone"],[f,b]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[h,d,[f,b]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[h,d,[f,y]],[/(surface duo)/i],[d,[h,j],[f,y]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[d,[h,"Fairphone"],[f,b]],[/(u304aa)/i],[d,[h,"AT&T"],[f,b]],[/\bsie-(\w*)/i],[d,[h,"Siemens"],[f,b]],[/\b(rct\w+) b/i],[d,[h,"RCA"],[f,y]],[/\b(venue[\d ]{2,7}) b/i],[d,[h,"Dell"],[f,y]],[/\b(q(?:mv|ta)\w+) b/i],[d,[h,"Verizon"],[f,y]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[d,[h,"Barnes & Noble"],[f,y]],[/\b(tm\d{3}\w+) b/i],[d,[h,"NuVision"],[f,y]],[/\b(k88) b/i],[d,[h,"ZTE"],[f,y]],[/\b(nx\d{3}j) b/i],[d,[h,"ZTE"],[f,b]],[/\b(gen\d{3}) b.+49h/i],[d,[h,"Swiss"],[f,b]],[/\b(zur\d{3}) b/i],[d,[h,"Swiss"],[f,y]],[/\b((zeki)?tb.*\b) b/i],[d,[h,"Zeki"],[f,y]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[h,"Dragon Touch"],d,[f,y]],[/\b(ns-?\w{0,9}) b/i],[d,[h,"Insignia"],[f,y]],[/\b((nxa|next)-?\w{0,9}) b/i],[d,[h,"NextBook"],[f,y]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[h,"Voice"],d,[f,b]],[/\b(lvtel\-)?(v1[12]) b/i],[[h,"LvTel"],d,[f,b]],[/\b(ph-1) /i],[d,[h,"Essential"],[f,b]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[d,[h,"Envizen"],[f,y]],[/\b(trio[-\w\. ]+) b/i],[d,[h,"MachSpeed"],[f,y]],[/\btu_(1491) b/i],[d,[h,"Rotor"],[f,y]],[/(shield[\w ]+) b/i],[d,[h,"Nvidia"],[f,y]],[/(sprint) (\w+)/i],[h,d,[f,b]],[/(kin\.[onetw]{3})/i],[[d,/\./g," "],[h,j],[f,b]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[d,[h,U],[f,y]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[d,[h,U],[f,b]],[/smart-tv.+(samsung)/i],[h,[f,w]],[/hbbtv.+maple;(\d+)/i],[[d,/^/,"SmartTV"],[h,M],[f,w]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[h,A],[f,w]],[/(apple) ?tv/i],[h,[d,O+" TV"],[f,w]],[/crkey/i],[[d,I+"cast"],[h,N],[f,w]],[/droid.+aft(\w+)( bui|\))/i],[d,[h,T],[f,w]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[d,[h,F],[f,w]],[/(bravia[\w ]+)( bui|\))/i],[d,[h,q],[f,w]],[/(mitv-\w{5}) bui/i],[d,[h,_],[f,w]],[/Hbbtv.*(technisat) (.*);/i],[h,d,[f,w]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[h,K],[d,K],[f,w]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[f,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[h,d,[f,v]],[/droid.+; (shield) bui/i],[d,[h,"Nvidia"],[f,v]],[/(playstation [345portablevi]+)/i],[d,[h,q],[f,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[d,[h,j],[f,v]],[/((pebble))app/i],[h,d,[f,E]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[d,[h,O],[f,E]],[/droid.+; (glass) \d/i],[d,[h,N],[f,E]],[/droid.+; (wt63?0{2,3})\)/i],[d,[h,U],[f,E]],[/(quest( \d| pro)?)/i],[d,[h,$],[f,E]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[h,[f,S]],[/(aeobc)\b/i],[d,[h,T],[f,S]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[d,[f,b]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[d,[f,y]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[f,y]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[f,b]],[/(android[-\w\. ]{0,9});.+buil/i],[d,[h,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[p,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,m],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[p,[m,V,Y]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[m,V,Y],[p,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,B],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,m],[/\(bb(10);/i],[m,[p,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[p,k+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[p,I+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,Q],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,m],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,m]]},X=function(e,t){if(typeof e===c&&(t=e,e=s),!(this instanceof X))return new X(e,t).getResult();var n=typeof i!==o&&i.navigator?i.navigator:s,r=e||(n&&n.userAgent?n.userAgent:""),v=n&&n.userAgentData?n.userAgentData:s,w=t?function(e,t){var n={};for(var r in e)t[r]&&t[r].length%2==0?n[r]=t[r].concat(e[r]):n[r]=e[r];return n}(J,t):J,E=n&&n.userAgent==r;return this.getBrowser=function(){var e,t={};return t[p]=s,t[m]=s,W.call(t,r,w.browser),t[l]=typeof(e=t[m])===u?e.replace(/[^\d\.]/g,"").split(".")[0]:s,E&&n&&n.brave&&typeof n.brave.isBrave==a&&(t[p]="Brave"),t},this.getCPU=function(){var e={};return e[g]=s,W.call(e,r,w.cpu),e},this.getDevice=function(){var e={};return e[h]=s,e[d]=s,e[f]=s,W.call(e,r,w.device),E&&!e[f]&&v&&v.mobile&&(e[f]=b),E&&"Macintosh"==e[d]&&n&&typeof n.standalone!==o&&n.maxTouchPoints&&n.maxTouchPoints>2&&(e[d]="iPad",e[f]=y),e},this.getEngine=function(){var e={};return e[p]=s,e[m]=s,W.call(e,r,w.engine),e},this.getOS=function(){var e={};return e[p]=s,e[m]=s,W.call(e,r,w.os),E&&!e[p]&&v&&v.platform&&"Unknown"!=v.platform&&(e[p]=v.platform.replace(/chrome os/i,Q).replace(/macos/i,B)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===u&&e.length>500?K(e,500):e,this},this.setUA(r),this};X.VERSION="1.0.38",X.BROWSER=H([p,m,l]),X.CPU=H([g]),X.DEVICE=H([d,h,f,v,b,w,y,E,S]),X.ENGINE=X.OS=H([p,m]),typeof t!==o?(e.exports&&(t=e.exports=X),t.UAParser=X):n.amdO?(r=function(){return X}.call(t,n,t,e))===s||(e.exports=r):typeof i!==o&&(i.UAParser=X);var Z=typeof i!==o&&(i.jQuery||i.Zepto);if(Z&&!Z.ua){var ee=new X;Z.ua=ee.getResult(),Z.ua.get=function(){return ee.getUA()},Z.ua.set=function(e){ee.setUA(e);var t=ee.getResult();for(var n in t)Z.ua[n]=t[n]}}}("object"==typeof window?window:this)},6089:t=>{"use strict";t.exports=e},7781:e=>{"use strict";e.exports=t},8531:e=>{"use strict";e.exports=n},2007:e=>{"use strict";e.exports=r},4201:e=>{"use strict";e.exports=i},3241:e=>{"use strict";e.exports=s},1308:e=>{"use strict";e.exports=a},5959:e=>{"use strict";e.exports=o},8398:e=>{"use strict";e.exports=c},1159:e=>{"use strict";e.exports=u},1269:e=>{"use strict";e.exports=l},7528:(e,t,n)=>{"use strict";n.d(t,{m:()=>s});var r=n(7794),i=n(9106),s=new class extends r.Q{#e;#t;#n;constructor(){super(),this.#n=e=>{if(!i.S$&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}}},9304:(e,t,n)=>{"use strict";n.d(t,{$:()=>o,s:()=>a});var r=n(3403),i=n(8546),s=n(5610),a=class extends i.k{#r;#i;#s;constructor(e){super(),this.mutationId=e.mutationId,this.#i=e.mutationCache,this.#r=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#r.includes(e)||(this.#r.push(e),this.clearGcTimeout(),this.#i.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#r=this.#r.filter((t=>t!==e)),this.scheduleGc(),this.#i.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#r.length||("pending"===this.state.status?this.scheduleGc():this.#i.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(e){this.#s=(0,s.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:()=>{this.#a({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#i.canRun(this)});const t="pending"===this.state.status,n=!this.#s.canStart();try{if(!t){this.#a({type:"pending",variables:e,isPaused:n}),await(this.#i.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#a({type:"pending",context:t,variables:e,isPaused:n})}const r=await this.#s.start();return await(this.#i.config.onSuccess?.(r,e,this.state.context,this)),await(this.options.onSuccess?.(r,e,this.state.context)),await(this.#i.config.onSettled?.(r,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(r,null,e,this.state.context)),this.#a({type:"success",data:r}),r}catch(t){try{throw await(this.#i.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#i.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#a({type:"error",error:t})}}finally{this.#i.runNext(this)}}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),r.j.batch((()=>{this.#r.forEach((t=>{t.onMutationUpdate(e)})),this.#i.notify({mutation:this,type:"updated",action:e})}))}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},3403:(e,t,n)=>{"use strict";n.d(t,{j:()=>r});var r=function(){let e=[],t=0,n=e=>{e()},r=e=>{e()},i=e=>setTimeout(e,0);const s=r=>{t?e.push(r):i((()=>{n(r)}))},a=()=>{const t=e;e=[],t.length&&i((()=>{r((()=>{t.forEach((e=>{n(e)}))}))}))};return{batch:e=>{let n;t++;try{n=e()}finally{t--,t||a()}return n},batchCalls:e=>(...t)=>{s((()=>{e(...t)}))},schedule:s,setNotifyFunction:e=>{n=e},setBatchNotifyFunction:e=>{r=e},setScheduler:e=>{i=e}}}()},4841:(e,t,n)=>{"use strict";n.d(t,{t:()=>s});var r=n(7794),i=n(9106),s=new class extends r.Q{#o=!0;#t;#n;constructor(){super(),this.#n=e=>{if(!i.S$&&window.addEventListener){const t=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",n)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#o!==e&&(this.#o=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#o}}},2015:(e,t,n)=>{"use strict";n.d(t,{X:()=>o,k:()=>c});var r=n(9106),i=n(3403),s=n(5610),a=n(8546),o=class extends a.k{#c;#u;#l;#s;#d;#p;constructor(e){super(),this.#p=!1,this.#d=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#c=e.state||function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,n=void 0!==t,r=n?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=this.#c,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#s?.promise}setOptions(e){this.options={...this.#d,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#l.remove(this)}setData(e,t){const n=(0,r.pl)(this.state.data,e,this.options);return this.#a({data:n,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),n}setState(e,t){this.#a({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#s?.promise;return this.#s?.cancel(e),t?t.then(r.lQ).catch(r.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#c)}isActive(){return this.observers.some((e=>!1!==(0,r.Eh)(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,r.j3)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#s?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#s?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#l.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#s&&(this.#p?this.#s.cancel({revert:!0}):this.#s.cancelRetry()),this.scheduleGc()),this.#l.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#a({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#s)return this.#s.continueRetry(),this.#s.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const n=new AbortController,i=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#p=!0,n.signal)})},a={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const e=(0,r.ZM)(this.options,t),n={queryKey:this.queryKey,meta:this.meta};return i(n),this.#p=!1,this.options.persister?this.options.persister(e,n,this):e(n)}};i(a),this.options.behavior?.onFetch(a,this),this.#u=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===a.fetchOptions?.meta||this.#a({type:"fetch",meta:a.fetchOptions?.meta});const o=e=>{(0,s.wm)(e)&&e.silent||this.#a({type:"error",error:e}),(0,s.wm)(e)||(this.#l.config.onError?.(e,this),this.#l.config.onSettled?.(this.state.data,e,this)),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.#s=(0,s.II)({initialPromise:t?.initialPromise,fn:a.fetchFn,abort:n.abort.bind(n),onSuccess:e=>{void 0!==e?(this.setData(e),this.#l.config.onSuccess?.(e,this),this.#l.config.onSettled?.(e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1):o(new Error(`${this.queryHash} data is undefined`))},onError:o,onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:()=>{this.#a({type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0}),this.#s.start()}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...c(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=e.error;return(0,s.wm)(n)&&n.revert&&this.#u?{...this.#u,fetchStatus:"idle"}:{...t,error:n,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),i.j.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#l.notify({query:this,type:"updated",action:e})}))}};function c(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,s.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},8546:(e,t,n)=>{"use strict";n.d(t,{k:()=>i});var r=n(9106),i=class{#f;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,r.gn)(this.gcTime)&&(this.#f=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(r.S$?1/0:3e5))}clearGcTimeout(){this.#f&&(clearTimeout(this.#f),this.#f=void 0)}}},5610:(e,t,n)=>{"use strict";n.d(t,{II:()=>l,v_:()=>o,wm:()=>u});var r=n(7528),i=n(4841),s=n(9106);function a(e){return Math.min(1e3*2**e,3e4)}function o(e){return"online"!==(e??"online")||i.t.isOnline()}var c=class{constructor(e){this.revert=e?.revert,this.silent=e?.silent}};function u(e){return e instanceof c}function l(e){let t,n,u,l=!1,d=0,p=!1;const f=new Promise(((e,t)=>{n=e,u=t})),h=()=>r.m.isFocused()&&("always"===e.networkMode||i.t.isOnline())&&e.canRun(),m=()=>o(e.networkMode)&&e.canRun(),g=r=>{p||(p=!0,e.onSuccess?.(r),t?.(),n(r))},v=n=>{p||(p=!0,e.onError?.(n),t?.(),u(n))},b=()=>new Promise((n=>{t=e=>{(p||h())&&n(e)},e.onPause?.()})).then((()=>{t=void 0,p||e.onContinue?.()})),y=()=>{if(p)return;let t;const n=0===d?e.initialPromise:void 0;try{t=n??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(g).catch((t=>{if(p)return;const n=e.retry??(s.S$?0:3),r=e.retryDelay??a,i="function"==typeof r?r(d,t):r,o=!0===n||"number"==typeof n&&d<n||"function"==typeof n&&n(d,t);!l&&o?(d++,e.onFail?.(d,t),(0,s.yy)(i).then((()=>h()?void 0:b())).then((()=>{l?v(t):y()}))):v(t)}))};return{promise:f,cancel:t=>{p||(v(new c(t)),e.abort?.())},continue:()=>(t?.(),f),cancelRetry:()=>{l=!0},continueRetry:()=>{l=!1},canStart:m,start:()=>(m()?y():b().then(y),f)}}},7794:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});var r=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},9106:(e,t,n)=>{"use strict";n.d(t,{Cp:()=>h,EN:()=>f,Eh:()=>u,F$:()=>p,MK:()=>l,S$:()=>r,ZM:()=>x,ZZ:()=>T,Zw:()=>s,d2:()=>c,f8:()=>g,gn:()=>a,hT:()=>O,j3:()=>o,lQ:()=>i,nJ:()=>d,pl:()=>E,y9:()=>S,yy:()=>w});var r="undefined"==typeof window||"Deno"in globalThis;function i(){}function s(e,t){return"function"==typeof e?e(t):e}function a(e){return"number"==typeof e&&e>=0&&e!==1/0}function o(e,t){return Math.max(e+(t||0)-Date.now(),0)}function c(e,t){return"function"==typeof e?e(t):e}function u(e,t){return"function"==typeof e?e(t):e}function l(e,t){const{type:n="all",exact:r,fetchStatus:i,predicate:s,queryKey:a,stale:o}=e;if(a)if(r){if(t.queryHash!==p(a,t.options))return!1}else if(!h(t.queryKey,a))return!1;if("all"!==n){const e=t.isActive();if("active"===n&&!e)return!1;if("inactive"===n&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&((!i||i===t.state.fetchStatus)&&!(s&&!s(t)))}function d(e,t){const{exact:n,status:r,predicate:i,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(n){if(f(t.options.mutationKey)!==f(s))return!1}else if(!h(t.options.mutationKey,s))return!1}return(!r||t.state.status===r)&&!(i&&!i(t))}function p(e,t){return(t?.queryKeyHashFn||f)(e)}function f(e){return JSON.stringify(e,((e,t)=>b(t)?Object.keys(t).sort().reduce(((e,n)=>(e[n]=t[n],e)),{}):t))}function h(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((n=>!h(e[n],t[n]))))}function m(e,t){if(e===t)return e;const n=v(e)&&v(t);if(n||b(e)&&b(t)){const r=n?e:Object.keys(e),i=r.length,s=n?t:Object.keys(t),a=s.length,o=n?[]:{};let c=0;for(let i=0;i<a;i++){const a=n?i:s[i];(!n&&r.includes(a)||n)&&void 0===e[a]&&void 0===t[a]?(o[a]=void 0,c++):(o[a]=m(e[a],t[a]),o[a]===e[a]&&void 0!==e[a]&&c++)}return i===a&&c===i?e:o}return t}function g(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function v(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function b(e){if(!y(e))return!1;const t=e.constructor;if(void 0===t)return!0;const n=t.prototype;return!!y(n)&&(!!n.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype)}function y(e){return"[object Object]"===Object.prototype.toString.call(e)}function w(e){return new Promise((t=>{setTimeout(t,e)}))}function E(e,t,n){return"function"==typeof n.structuralSharing?n.structuralSharing(e,t):!1!==n.structuralSharing?m(e,t):t}function S(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function T(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var O=Symbol(),x=(e,t)=>!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==O?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))},3715:(e,t,n)=>{"use strict";n.d(t,{Ht:()=>o,jE:()=>a});var r=n(5959),i=n(2540),s=r.createContext(void 0),a=e=>{const t=r.useContext(s);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},o=({client:e,children:t})=>(r.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,i.jsx)(s.Provider,{value:e,children:t}))},7616:(e,t,n)=>{"use strict";n.d(t,{I:()=>P});var r=n(9106),i=n(3403),s=n(7528),a=n(7794),o=n(2015),c=class extends a.Q{constructor(e,t){super(),this.options=t,this.#h=e,this.#m=null,this.bindMethods(),this.setOptions(t)}#h;#g=void 0;#v=void 0;#b=void 0;#y;#w;#m;#E;#S;#T;#O;#x;#C;#P=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#g.addObserver(this),u(this.#g,this.options)?this.#I():this.updateResult(),this.#k())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return l(this.#g,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return l(this.#g,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#N(),this.#R(),this.#g.removeObserver(this)}setOptions(e,t){const n=this.options,i=this.#g;if(this.options=this.#h.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,r.Eh)(this.options.enabled,this.#g))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#A(),this.#g.setOptions(this.options),n._defaulted&&!(0,r.f8)(this.options,n)&&this.#h.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#g,observer:this});const s=this.hasListeners();s&&d(this.#g,i,this.options,n)&&this.#I(),this.updateResult(t),!s||this.#g===i&&(0,r.Eh)(this.options.enabled,this.#g)===(0,r.Eh)(n.enabled,this.#g)&&(0,r.d2)(this.options.staleTime,this.#g)===(0,r.d2)(n.staleTime,this.#g)||this.#j();const a=this.#L();!s||this.#g===i&&(0,r.Eh)(this.options.enabled,this.#g)===(0,r.Eh)(n.enabled,this.#g)&&a===this.#C||this.#D(a)}getOptimisticResult(e){const t=this.#h.getQueryCache().build(this.#h,e),n=this.createResult(t,e);return function(e,t){if(!(0,r.f8)(e.getCurrentResult(),t))return!0;return!1}(this,n)&&(this.#b=n,this.#w=this.options,this.#y=this.#g.state),n}getCurrentResult(){return this.#b}trackResult(e,t){const n={};return Object.keys(e).forEach((r=>{Object.defineProperty(n,r,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(r),t?.(r),e[r])})})),n}trackProp(e){this.#P.add(e)}getCurrentQuery(){return this.#g}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#h.defaultQueryOptions(e),n=this.#h.getQueryCache().build(this.#h,t);return n.isFetchingOptimistic=!0,n.fetch().then((()=>this.createResult(n,t)))}fetch(e){return this.#I({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#b)))}#I(e){this.#A();let t=this.#g.fetch(this.options,e);return e?.throwOnError||(t=t.catch(r.lQ)),t}#j(){this.#N();const e=(0,r.d2)(this.options.staleTime,this.#g);if(r.S$||this.#b.isStale||!(0,r.gn)(e))return;const t=(0,r.j3)(this.#b.dataUpdatedAt,e)+1;this.#O=setTimeout((()=>{this.#b.isStale||this.updateResult()}),t)}#L(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#g):this.options.refetchInterval)??!1}#D(e){this.#R(),this.#C=e,!r.S$&&!1!==(0,r.Eh)(this.options.enabled,this.#g)&&(0,r.gn)(this.#C)&&0!==this.#C&&(this.#x=setInterval((()=>{(this.options.refetchIntervalInBackground||s.m.isFocused())&&this.#I()}),this.#C))}#k(){this.#j(),this.#D(this.#L())}#N(){this.#O&&(clearTimeout(this.#O),this.#O=void 0)}#R(){this.#x&&(clearInterval(this.#x),this.#x=void 0)}createResult(e,t){const n=this.#g,i=this.options,s=this.#b,a=this.#y,c=this.#w,l=e!==n?e.state:this.#v,{state:f}=e;let h,m={...f},g=!1;if(t._optimisticResults){const r=this.hasListeners(),s=!r&&u(e,t),a=r&&d(e,n,t,i);(s||a)&&(m={...m,...(0,o.k)(f.data,e.options)}),"isRestoring"===t._optimisticResults&&(m.fetchStatus="idle")}let{error:v,errorUpdatedAt:b,status:y}=m;if(t.select&&void 0!==m.data)if(s&&m.data===a?.data&&t.select===this.#E)h=this.#S;else try{this.#E=t.select,h=t.select(m.data),h=(0,r.pl)(s?.data,h,t),this.#S=h,this.#m=null}catch(e){this.#m=e}else h=m.data;if(void 0!==t.placeholderData&&void 0===h&&"pending"===y){let e;if(s?.isPlaceholderData&&t.placeholderData===c?.placeholderData)e=s.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#T?.state.data,this.#T):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#m=null}catch(e){this.#m=e}void 0!==e&&(y="success",h=(0,r.pl)(s?.data,e,t),g=!0)}this.#m&&(v=this.#m,h=this.#S,b=Date.now(),y="error");const w="fetching"===m.fetchStatus,E="pending"===y,S="error"===y,T=E&&w,O=void 0!==h;return{status:y,fetchStatus:m.fetchStatus,isPending:E,isSuccess:"success"===y,isError:S,isInitialLoading:T,isLoading:T,data:h,dataUpdatedAt:m.dataUpdatedAt,error:v,errorUpdatedAt:b,failureCount:m.fetchFailureCount,failureReason:m.fetchFailureReason,errorUpdateCount:m.errorUpdateCount,isFetched:m.dataUpdateCount>0||m.errorUpdateCount>0,isFetchedAfterMount:m.dataUpdateCount>l.dataUpdateCount||m.errorUpdateCount>l.errorUpdateCount,isFetching:w,isRefetching:w&&!E,isLoadingError:S&&!O,isPaused:"paused"===m.fetchStatus,isPlaceholderData:g,isRefetchError:S&&O,isStale:p(e,t),refetch:this.refetch}}updateResult(e){const t=this.#b,n=this.createResult(this.#g,this.options);if(this.#y=this.#g.state,this.#w=this.options,void 0!==this.#y.data&&(this.#T=this.#g),(0,r.f8)(n,t))return;this.#b=n;const i={};!1!==e?.listeners&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,n="function"==typeof e?e():e;if("all"===n||!n&&!this.#P.size)return!0;const r=new Set(n??this.#P);return this.options.throwOnError&&r.add("error"),Object.keys(this.#b).some((e=>{const n=e;return this.#b[n]!==t[n]&&r.has(n)}))})()&&(i.listeners=!0),this.#M({...i,...e})}#A(){const e=this.#h.getQueryCache().build(this.#h,this.options);if(e===this.#g)return;const t=this.#g;this.#g=e,this.#v=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#k()}#M(e){i.j.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#b)})),this.#h.getQueryCache().notify({query:this.#g,type:"observerResultsUpdated"})}))}};function u(e,t){return function(e,t){return!1!==(0,r.Eh)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&l(e,t,t.refetchOnMount)}function l(e,t,n){if(!1!==(0,r.Eh)(t.enabled,e)){const r="function"==typeof n?n(e):n;return"always"===r||!1!==r&&p(e,t)}return!1}function d(e,t,n,i){return(e!==t||!1===(0,r.Eh)(i.enabled,e))&&(!n.suspense||"error"!==e.state.status)&&p(e,n)}function p(e,t){return!1!==(0,r.Eh)(t.enabled,e)&&e.isStaleByTime((0,r.d2)(t.staleTime,e))}var f=n(5959);n(2540);function h(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var m=f.createContext(h()),g=()=>f.useContext(m),v=n(3715),b=f.createContext(!1),y=()=>f.useContext(b),w=(b.Provider,n(908)),E=(e,t)=>{(e.suspense||e.throwOnError)&&(t.isReset()||(e.retryOnMount=!1))},S=e=>{f.useEffect((()=>{e.clearReset()}),[e])},T=({result:e,errorResetBoundary:t,throwOnError:n,query:r})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(0,w.G)(n,[e.error,r]),O=e=>{e.suspense&&"number"!=typeof e.staleTime&&(e.staleTime=1e3)},x=(e,t)=>e?.suspense&&t.isPending,C=(e,t,n)=>t.fetchOptimistic(e).catch((()=>{n.clearReset()}));function P(e,t){return function(e,t,n){const r=(0,v.jE)(n),s=y(),a=g(),o=r.defaultQueryOptions(e);r.getDefaultOptions().queries?._experimental_beforeQuery?.(o),o._optimisticResults=s?"isRestoring":"optimistic",O(o),E(o,a),S(a);const[c]=f.useState((()=>new t(r,o))),u=c.getOptimisticResult(o);if(f.useSyncExternalStore(f.useCallback((e=>{const t=s?()=>{}:c.subscribe(i.j.batchCalls(e));return c.updateResult(),t}),[c,s]),(()=>c.getCurrentResult()),(()=>c.getCurrentResult())),f.useEffect((()=>{c.setOptions(o,{listeners:!1})}),[o,c]),x(o,u))throw C(o,c,a);if(T({result:u,errorResetBoundary:a,throwOnError:o.throwOnError,query:r.getQueryCache().get(o.queryHash)}))throw u.error;return r.getDefaultOptions().queries?._experimental_afterQuery?.(o,u),o.notifyOnChangeProps?u:c.trackResult(u)}(e,c,t)}},908:(e,t,n)=>{"use strict";function r(e,t){return"function"==typeof e?e(...t):!!e}function i(){}n.d(t,{G:()=>r,l:()=>i})},2533:e=>{"use strict";e.exports=JSON.parse('{"id":"grafana-pyroscope-app"}')}},h={};function m(e){var t=h[e];if(void 0!==t)return t.exports;var n=h[e]={id:e,loaded:!1,exports:{}};return f[e].call(n.exports,n,n.exports,m),n.loaded=!0,n.exports}m.m=f,m.amdO={},m.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return m.d(t,{a:t}),t},m.d=(e,t)=>{for(var n in t)m.o(t,n)&&!m.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},m.f={},m.e=e=>Promise.all(Object.keys(m.f).reduce(((t,n)=>(m.f[n](e,t),t)),[])),m.u=e=>e+".js",m.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),m.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),d={},p="grafana-pyroscope-app:",m.l=(e,t,n,r)=>{if(d[e])d[e].push(t);else{var i,s;if(void 0!==n)for(var a=document.getElementsByTagName("script"),o=0;o<a.length;o++){var c=a[o];if(c.getAttribute("src")==e||c.getAttribute("data-webpack")==p+n){i=c;break}}i||(s=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,m.nc&&i.setAttribute("nonce",m.nc),i.setAttribute("data-webpack",p+n),i.src=e,0!==i.src.indexOf(window.location.origin+"/")&&(i.crossOrigin="anonymous"),i.integrity=m.sriHashes[r],i.crossOrigin="anonymous"),d[e]=[t];var u=(t,n)=>{i.onerror=i.onload=null,clearTimeout(l);var r=d[e];if(delete d[e],i.parentNode&&i.parentNode.removeChild(i),r&&r.forEach((e=>e(n))),t)return t(n)},l=setTimeout(u.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=u.bind(null,i.onerror),i.onload=u.bind(null,i.onload),s&&document.head.appendChild(i)}},m.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},m.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),m.p="public/plugins/grafana-pyroscope-app/",m.sriHashes={75:"sha256-RBRryzBVsbMkhV7se/rmF2da0ND4qBStJhaK0HsTGbE=",373:"sha256-VPPgxyGNV9F7WNf6JipWGNv7aTerPRGXcAya9z045zs=",608:"sha256-hTIL0Am2rVa1SD57FsIwERUDRZgBUeD5oviZC6G4OuM=",702:"sha256-x9pG0DXyxRkUOf724Qgx6MB9FKf8DCVH9NGtqBuyX+4=",755:"sha256-8Y88fSVA1bIU196clAJLMbMBT2vgRyBshRy8FMGWPAs="},(()=>{var e={231:0};m.f.j=(t,n)=>{var r=m.o(e,t)?e[t]:void 0;if(0!==r)if(r)n.push(r[2]);else{var i=new Promise(((n,i)=>r=e[t]=[n,i]));n.push(r[2]=i);var s=m.p+m.u(t),a=new Error;m.l(s,(n=>{if(m.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var i=n&&("load"===n.type?"missing":n.type),s=n&&n.target&&n.target.src;a.message="Loading chunk "+t+" failed.\n("+i+": "+s+")",a.name="ChunkLoadError",a.type=i,a.request=s,r[1](a)}}),"chunk-"+t,t)}};var t=(t,n)=>{var r,i,[s,a,o]=n,c=0;if(s.some((t=>0!==e[t]))){for(r in a)m.o(a,r)&&(m.m[r]=a[r]);if(o)o(m)}for(t&&t(n);c<s.length;c++)i=s[c],m.o(e,i)&&e[i]&&e[i][0](),e[i]=0},n=self.webpackChunkgrafana_pyroscope_app=self.webpackChunkgrafana_pyroscope_app||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var g={};return(()=>{"use strict";m.r(g),m.d(g,{plugin:()=>ae});var e=m(1308),t=m.n(e);m.p=t()&&t().uri?t().uri.slice(0,t().uri.lastIndexOf("/")+1):"public/plugins/grafana-pyroscope-app/";var n=m(7781),r=m(6089),i=m(8531),s=m(2007),a=m(9106),o=m(2015),c=m(3403),u=m(7794),l=class extends u.Q{constructor(e={}){super(),this.config=e,this.#F=new Map}#F;build(e,t,n){const r=t.queryKey,i=t.queryHash??(0,a.F$)(r,t);let s=this.get(i);return s||(s=new o.X({cache:this,queryKey:r,queryHash:i,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(r)}),this.add(s)),s}add(e){this.#F.has(e.queryHash)||(this.#F.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#F.get(e.queryHash);t&&(e.destroy(),t===e&&this.#F.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){c.j.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#F.get(e)}getAll(){return[...this.#F.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,a.MK)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,a.MK)(e,t))):t}notify(e){c.j.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){c.j.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){c.j.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}},d=m(9304),p=class extends u.Q{constructor(e={}){super(),this.config=e,this.#q=new Map,this.#_=Date.now()}#q;#_;build(e,t,n){const r=new d.s({mutationCache:this,mutationId:++this.#_,options:e.defaultMutationOptions(t),state:n});return this.add(r),r}add(e){const t=f(e),n=this.#q.get(t)??[];n.push(e),this.#q.set(t,n),this.notify({type:"added",mutation:e})}remove(e){const t=f(e);if(this.#q.has(t)){const n=this.#q.get(t)?.filter((t=>t!==e));n&&(0===n.length?this.#q.delete(t):this.#q.set(t,n))}this.notify({type:"removed",mutation:e})}canRun(e){const t=this.#q.get(f(e))?.find((e=>"pending"===e.state.status));return!t||t===e}runNext(e){const t=this.#q.get(f(e))?.find((t=>t!==e&&t.state.isPaused));return t?.continue()??Promise.resolve()}clear(){c.j.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}getAll(){return[...this.#q.values()].flat()}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,a.nJ)(t,e)))}findAll(e={}){return this.getAll().filter((t=>(0,a.nJ)(e,t)))}notify(e){c.j.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return c.j.batch((()=>Promise.all(e.map((e=>e.continue().catch(a.lQ))))))}};function f(e){return e.options.scope?.id??String(e.mutationId)}var h=m(7528),v=m(4841);function b(e){return{onFetch:(t,n)=>{const r=async()=>{const n=t.options,r=t.fetchOptions?.meta?.fetchMore?.direction,i=t.state.data?.pages||[],s=t.state.data?.pageParams||[],o={pages:[],pageParams:[]};let c=!1;const u=(0,a.ZM)(t.options,t.fetchOptions),l=async(e,n,r)=>{if(c)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);const i={queryKey:t.queryKey,pageParam:n,direction:r?"backward":"forward",meta:t.options.meta};var s;s=i,Object.defineProperty(s,"signal",{enumerable:!0,get:()=>(t.signal.aborted?c=!0:t.signal.addEventListener("abort",(()=>{c=!0})),t.signal)});const o=await u(i),{maxPages:l}=t.options,d=r?a.ZZ:a.y9;return{pages:d(e.pages,o,l),pageParams:d(e.pageParams,n,l)}};let d;if(r&&i.length){const e="backward"===r,t={pages:i,pageParams:s},a=(e?w:y)(n,t);d=await l(t,a,e)}else{d=await l(o,s[0]??n.initialPageParam);const t=e??i.length;for(let e=1;e<t;e++){const e=y(n,d);d=await l(d,e)}}return d};t.options.persister?t.fetchFn=()=>t.options.persister?.(r,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n):t.fetchFn=r}}}function y(e,{pages:t,pageParams:n}){const r=t.length-1;return e.getNextPageParam(t[r],t,n[r],n)}function w(e,{pages:t,pageParams:n}){return e.getPreviousPageParam?.(t[0],t,n[0],n)}const E=new class{#U;#i;#d;#$;#Q;#B;#H;#z;constructor(e={}){this.#U=e.queryCache||new l,this.#i=e.mutationCache||new p,this.#d=e.defaultOptions||{},this.#$=new Map,this.#Q=new Map,this.#B=0}mount(){this.#B++,1===this.#B&&(this.#H=h.m.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#U.onFocus())})),this.#z=v.t.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#U.onOnline())})))}unmount(){this.#B--,0===this.#B&&(this.#H?.(),this.#H=void 0,this.#z?.(),this.#z=void 0)}isFetching(e){return this.#U.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#i.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#U.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(void 0===t)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=this.#U.build(this,n);return e.revalidateIfStale&&r.isStaleByTime((0,a.d2)(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return this.#U.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),i=this.#U.get(r.queryHash),s=i?.state.data,o=(0,a.Zw)(t,s);if(void 0!==o)return this.#U.build(this,r).setData(o,{...n,manual:!0})}setQueriesData(e,t,n){return c.j.batch((()=>this.#U.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,n)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#U.get(t.queryHash)?.state}removeQueries(e){const t=this.#U;c.j.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const n=this.#U,r={type:"active",...e};return c.j.batch((()=>(n.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries(r,t))))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=c.j.batch((()=>this.#U.findAll(e).map((e=>e.cancel(n)))));return Promise.all(r).then(a.lQ).catch(a.lQ)}invalidateQueries(e={},t={}){return c.j.batch((()=>{if(this.#U.findAll(e).forEach((e=>{e.invalidate()})),"none"===e.refetchType)return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)}))}refetchQueries(e={},t){const n={...t,cancelRefetch:t?.cancelRefetch??!0},r=c.j.batch((()=>this.#U.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,n);return n.throwOnError||(t=t.catch(a.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(r).then(a.lQ)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const n=this.#U.build(this,t);return n.isStaleByTime((0,a.d2)(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(a.lQ).catch(a.lQ)}fetchInfiniteQuery(e){return e.behavior=b(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(a.lQ).catch(a.lQ)}resumePausedMutations(){return v.t.isOnline()?this.#i.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#U}getMutationCache(){return this.#i}getDefaultOptions(){return this.#d}setDefaultOptions(e){this.#d=e}setQueryDefaults(e,t){this.#$.set((0,a.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#$.values()];let n={};return t.forEach((t=>{(0,a.Cp)(e,t.queryKey)&&(n={...n,...t.defaultOptions})})),n}setMutationDefaults(e,t){this.#Q.set((0,a.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#Q.values()];let n={};return t.forEach((t=>{(0,a.Cp)(e,t.mutationKey)&&(n={...n,...t.defaultOptions})})),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#d.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,a.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),!0!==t.enabled&&t.queryFn===a.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#d.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#U.clear(),this.#i.clear()}}({defaultOptions:{queries:{networkMode:"always",retry:!1,refetchOnWindowFocus:!1}}});var S=m(5377),T=m(3715),O=m(5959),x=m.n(O),C=m(5656),P=m(7616);function I(e,t,n,r,i,s,a){try{var o=e[s](a),c=o.value}catch(e){return void n(e)}o.done?t(c):Promise.resolve(c).then(r,i)}class k extends C.O{get(){var e,t=this;return(e=function*(){const e=yield t.fetch("/querier.v1.QuerierService/GetProfileStats",{method:"POST",body:JSON.stringify({})}),n=yield e.json();return{hasIngestedData:n.dataIngested,oldestProfileTime:Number(n.oldestProfileTime),newestProfileTime:Number(n.newestProfileTime)}},function(){var t=this,n=arguments;return new Promise((function(r,i){var s=e.apply(t,n);function a(e){I(s,r,i,a,o,"next",e)}function o(e){I(s,r,i,a,o,"throw",e)}a(void 0)}))})()}}const N=new k;function R(){const[e,t]=(0,O.useState)(!1),n=C.O.getPyroscopeDataSources().length,{isFetching:r,error:i,stats:s}=function({enabled:e}){const{isFetching:t,error:n,data:r,refetch:i}=(0,P.I)({enabled:e,placeholderData:()=>({hasIngestedData:!0,oldestProfileTime:0,newestProfileTime:0}),queryKey:["tenant-stats"],queryFn:()=>(N.abort(),N.get())});return{isFetching:t,error:N.isAbortError(n)?null:n,stats:r,refetch:i}}({enabled:n>0}),a=!r&&!(null==s?void 0:s.hasIngestedData);return{data:{shouldShowLoadingPage:!i&&r,shouldShowOnboardingPage:(i||!n||a)&&!e,shouldShowNoDataSourceBanner:!n},actions:{closeModal(){t(!0)}}}}var A=m(1049);function j(){return x().createElement(i.PluginPage,{layout:n.PageLayoutType.Canvas},x().createElement(A.s,{title:x().createElement("span",null,"Loading... ",x().createElement(s.Icon,{name:"fa fa-spinner"}))}))}const L=e=>({link:r.css`
    color: ${e.colors.text.link};
    &:hover {
      text-decoration: underline;
    }
  `});function D({href:e,children:t}){const n=(0,s.useStyles2)(L);return x().createElement("a",{className:n.link,href:e,target:"_blank",rel:"noreferrer"},t," ",x().createElement(s.Icon,{name:"external-link-alt"}))}function M(){return x().createElement(i.PluginPage,{layout:n.PageLayoutType.Canvas},x().createElement(A.s,{title:"Explore Profiles"}),x().createElement(s.Alert,{severity:"error",title:"Missing Pyroscope data source!"},"This plugin requires a Pyroscope data source. Please"," ",x().createElement(D,{href:"/connections/datasources/new"},"add and configure a Pyroscope data source")," to your Grafana instance."))}const F="public/plugins/grafana-pyroscope-app/img/61b4cf746a6f58780f27.png",q="public/plugins/grafana-pyroscope-app/img/0324b23e949528cbda73.png",_="public/plugins/grafana-pyroscope-app/img/3e820d7b934ac6c337fc.png",U="public/plugins/grafana-pyroscope-app/img/9c9cdd5175734d579007.png",$="public/plugins/grafana-pyroscope-app/img/bafee50693eb02088442.png";function Q(){const{instances:e}=function(){const{isFetching:e,error:t,data:n}=(0,P.I)({queryKey:["instances"],queryFn:()=>fetch("/api/plugin-proxy/cloud-home-app/grafanacom-api/instances").then((e=>e.json()))});return{isFetching:e,error:t,instances:n}}(),[t,n]=(0,O.useState)("https://grafana.com/auth/sign-in/"),r=/grafana(-dev|-ops)?\.net/.test(window.location.host);if(e&&e.orgSlug&&e.hpInstanceId){const r=`https://grafana.com/orgs/${e.orgSlug}/hosted-profiles/${e.hpInstanceId}`;t!==r&&n(r)}return{data:{settingsUrl:t,isCloud:r},actions:{}}}const B=e=>({onboardingRow:r.css`
    background: ${e.colors.background.secondary};
    display: flex;
    margin-top: 16px;
    gap: 20px;
    padding: 20px;
    margin-bottom: 2.5rem;
  `,onboardingParagraph:r.css`
    padding: 20px 64px;
    text-align: center;
    line-height: 2;
    flex: 1;
    margin: 0;
  `,onboardingPanel:r.css`
    flex: 1;
    display: flex;
    flex-flow: column wrap;
    -webkit-box-align: center;
    align-items: center;
    margin-top: 16px;
    text-align: center;
  `,onboardingPanelHeader:r.css`
    line-height: 1.5;
    margin-bottom: 1em;
  `,onboardingPanelImage:r.css`
    width: 5rem;
    margin-bottom: 1em;
  `,hero:r.css`
    display: flex;
    flex-direction: row;
  `,heroTitles:r.css`
    flex: 1;
  `,heroImage:r.css`
    width: 40%;
    margin-left: 16px;
    margin-top: 16px;
    margin-bottom: 16px;
    border-radius: 3px;
  `,onboardingPanelNumber:r.css`
    color: rgb(236, 109, 19);
    text-align: center;
    display: grid;
    place-items: center;
    background-image: linear-gradient(135deg, currentcolor, 75%, rgb(204, 204, 220));
    border-radius: 100%;
    font-size: 2.5rem;
    line-height: 5rem;
    height: 5rem;
    width: 5rem;
    margin-bottom: 1em;
  `,color2:r.css`
    color: rgb(190, 85, 190);
  `,color3:r.css`
    color: rgb(126, 108, 218);
  `,onboardingPanelNumberSpan:r.css`
    color: rgb(220, 220, 220);
  `,onboardingPanelDescription:r.css`
    text-align: justify;
    text-align: center;
    line-height: 1.66;
    margin-top: 0;
  `,title:r.css`
    margin-bottom: 0.5em;
    line-height: 1.5;
  `,subtitle:r.css`
    margin-bottom: 1em;
    line-height: 1.5;
    font-size: 1.25rem;
  `});function H(){const e=(0,s.useStyles2)(B),{data:t}=Q();return x().createElement("div",{"data-testid":"onboarding-modal"},x().createElement("div",{className:e.hero,"data-testid":"hero"},x().createElement("div",{className:e.heroTitles},x().createElement("h1",{className:e.title},"Welcome to ",t.isCloud?"Grafana Cloud Profiles":"Explore Profiles"),x().createElement("h2",{className:e.subtitle},"Optimize infrastructure spend, simplify debugging, and enhance application performance")),x().createElement("img",{src:t.isCloud?_:q,className:e.heroImage})),x().createElement("div",{"data-testid":"what-you-can-do"},x().createElement("h3",null,"What You Can Do"),x().createElement("div",{className:e.onboardingRow},x().createElement("div",{className:e.onboardingPanel},x().createElement("img",{className:e.onboardingPanelImage,src:U}),x().createElement("h3",{className:e.onboardingPanelHeader},"Reduce Costs"),x().createElement("p",{className:e.onboardingPanelDescription},"Spot CPU spikes, memory leaks, and other inefficiencies with code-level visibility into resource usage. Teams can then optimize their code and lower infrastructure costs.")),x().createElement("div",{className:e.onboardingPanel},x().createElement("img",{className:e.onboardingPanelImage,src:F}),x().createElement("h3",{className:e.onboardingPanelHeader},"Decrease Latency"),x().createElement("p",{className:e.onboardingPanelDescription},"Maintain high speed and efficiency and improve application performance. In a competitive digital world, decreasing latency translates to increasing revenue.")),x().createElement("div",{className:e.onboardingPanel},x().createElement("img",{className:e.onboardingPanelImage,src:$}),x().createElement("h3",{className:e.onboardingPanelHeader},"Resolve Incidents Faster"),x().createElement("p",{className:e.onboardingPanelDescription},"Cut down the mean time to resolution (MTTR) by correlating continuous profiling data with metrics, logs, and traces to quickly identify the root cause of any issue.")))),x().createElement("div",{"data-testid":"how-to-get-started"},x().createElement("h3",null,"How to Get Started"),x().createElement("div",{className:e.onboardingRow},t.isCloud?x().createElement(x().Fragment,null,x().createElement("div",{className:e.onboardingPanel},x().createElement("div",{className:e.onboardingPanelNumber},x().createElement("span",{className:e.onboardingPanelNumberSpan},"1")),x().createElement("h3",{className:e.onboardingPanelHeader},"Add Profiling to Your Application"),x().createElement("p",{className:e.onboardingPanelDescription},"Use"," ",x().createElement(D,{href:"https://grafana.com/docs/pyroscope/latest/configure-client/grafana-alloy/"},"Grafana Alloy")," ","or"," ",x().createElement(D,{href:"https://grafana.com/docs/pyroscope/next/configure-client/language-sdks/"},"Pyroscope SDKs")," ","to push profiles from your applications to Grafana Cloud.")),x().createElement("div",{className:e.onboardingPanel},x().createElement("div",{className:(0,r.cx)(e.onboardingPanelNumber,e.color2)},x().createElement("span",{className:e.onboardingPanelNumberSpan},"2")),x().createElement("h3",{className:e.onboardingPanelHeader},"Configure Your Applications"),x().createElement("p",{className:e.onboardingPanelDescription},"Go to ",x().createElement(D,{href:t.settingsUrl},"Grafana Cloud Stack settings")," to find your Grafana Cloud Credentials.")),x().createElement("div",{className:e.onboardingPanel},x().createElement("div",{className:(0,r.cx)(e.onboardingPanelNumber,e.color3)},x().createElement("span",{className:e.onboardingPanelNumberSpan},"3")),x().createElement("h3",{className:e.onboardingPanelHeader},"Start Getting Performance Insights"),x().createElement("p",{className:e.onboardingPanelDescription},"Once you're done with initial setup, refresh this page to see your profiling data."))):x().createElement(x().Fragment,null,x().createElement("div",{className:e.onboardingPanel},x().createElement("div",{className:e.onboardingPanelNumber},x().createElement("span",{className:e.onboardingPanelNumberSpan},"1")),x().createElement("h3",{className:e.onboardingPanelHeader},"Set Up Your Pyroscope Server"),x().createElement("p",{className:e.onboardingPanelDescription},"Install ",x().createElement(D,{href:"https://grafana.com/docs/pyroscope/latest/"},"Pyroscope Server")," on your infrastructure. Or if you want to use a hosted service, go to"," ",x().createElement(D,{href:t.settingsUrl},"Grafana Cloud Stack settings")," to find your Grafana Cloud Credentials.")),x().createElement("div",{className:e.onboardingPanel},x().createElement("div",{className:(0,r.cx)(e.onboardingPanelNumber,e.color2)},x().createElement("span",{className:e.onboardingPanelNumberSpan},"2")),x().createElement("h3",{className:e.onboardingPanelHeader},"Configure Grafana"),x().createElement("p",{className:e.onboardingPanelDescription},"Add a new ",x().createElement(D,{href:"/connections/datasources/new"},"Pyroscope datasource"),". Use your Pyroscope server URL and appropriate security credentials if you use Grafana Cloud Profiles.")),x().createElement("div",{className:e.onboardingPanel},x().createElement("div",{className:(0,r.cx)(e.onboardingPanelNumber,e.color3)},x().createElement("span",{className:e.onboardingPanelNumberSpan},"3")),x().createElement("h3",{className:e.onboardingPanelHeader},"Add Profiling to Your Application"),x().createElement("p",{className:e.onboardingPanelDescription},"Use"," ",x().createElement(D,{href:"https://grafana.com/docs/pyroscope/latest/configure-client/grafana-alloy/"},"Grafana Alloy")," ","or"," ",x().createElement(D,{href:"https://grafana.com/docs/pyroscope/next/configure-client/language-sdks/"},"Pyroscope SDKs")," ","to push profiles from your applications to Grafana Cloud."))))),t.isCloud&&x().createElement("div",{"data-testid":"how-billing-works"},x().createElement("h3",null,"How Billing Works"),x().createElement("div",{className:e.onboardingRow},x().createElement("p",{className:e.onboardingParagraph},"Usage of Grafana Cloud Profiles is subject to"," ",x().createElement(D,{href:"https://grafana.com/pricing/"},"Grafana Cloud Pricing")," for Profiles.",x().createElement("br",null),"For additional information, read the announcement ",x().createElement(D,{href:"https://grafana.com/blog/2023/08/09/grafana-cloud-profiles-for-continuous-profiling/"},"blog post"),"."))))}const z=e=>({onboardingPage:r.css`
    padding: 16px;
    margin: 64px;
    position: relative;
    background-color: ${e.colors.background.primary};
  `,closeButton:r.css`
    position: absolute;
    top: -30px;
    opacity: 0.8;
    right: -32px;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    line-height: 40px;
    display: block;
    padding: 0;
    margin: 0;
    font-size: 22px;
  `}),G={text:"Onboarding"};function K({onCloseModal:e}){const t=(0,s.useStyles2)(z);return x().createElement(i.PluginPage,{pageNav:G,layout:n.PageLayoutType.Custom},x().createElement("div",{className:t.onboardingPage},x().createElement("button",{className:t.closeButton,onClick:e,title:"Close","data-testid":"close-onboarding-modal"},"×"),x().createElement(H,null)))}function W({children:e}){const{data:t,actions:n}=R();return t.shouldShowLoadingPage?x().createElement(j,null):t.shouldShowOnboardingPage?x().createElement(K,{onCloseModal:n.closeModal}):t.shouldShowNoDataSourceBanner?x().createElement(M,null):x().createElement(x().Fragment,null,e)}var V=m(1159),Y=m(4137);const J=x().lazy((()=>Promise.all([m.e(608),m.e(755),m.e(75)]).then(m.bind(m,7746)))),X=x().lazy((()=>Promise.all([m.e(608),m.e(702)]).then(m.bind(m,702)))),Z=x().lazy((()=>m.e(373).then(m.bind(m,9373))));function ee(){return x().createElement(V.Routes,null,x().createElement(V.Route,{path:`${Y.bw.EXPLORE}/*`,element:x().createElement(J,null)}),x().createElement(V.Route,{path:`${Y.bw.ADHOC}/*`,element:x().createElement(X,null)}),x().createElement(V.Route,{path:`${Y.bw.SETTINGS}/*`,element:x().createElement(Z,null)}),x().createElement(V.Route,{path:"/*",element:x().createElement(J,null)}))}var te=m(8536);function ne({error:e}){return x().createElement(i.PluginPage,{layout:n.PageLayoutType.Canvas},x().createElement("div",{className:"pyroscope-app"},x().createElement(A.s,{title:"Explore Profiles"}),x().createElement(te._,{severity:"error",title:"Fatal error!",message:"Please try reloading the page or, if the problem persists, contact your organization admin. Sorry for the inconvenience.",error:e,errorContext:{handheldBy:"React error boundary"}})))}(0,S.Js)();const re=e=>({pageContainer:r.css`
    display: flex;
    flex-direction: column;
    padding: ${e.spacing(1)} ${e.spacing(2)} ${e.spacing(2)} ${e.spacing(2)};
    flex-basis: 100%;
    flex-grow: 1;
  `});function ie(e){var t,n,r;const{timeRange:i,pyroscopeQuery:s}=e;let a="",o="all",c=null===(n=e.pyroscopeQuery.labelSelector)||void 0===n||null===(t=n.match(/service_name="([^"]+)"/))||void 0===t?void 0:t[1];c&&(o="labels");const u=`var-dataSource=${null===(r=s.datasource)||void 0===r?void 0:r.uid}`,l=c?`&var-serviceName=${c}`:"",d=`&var-profileMetricId=${s.profileTypeId}`,p=`&explorationType=${o}`;i&&(a=`&from=${i.from}&to=${i.to}`);return`/a/grafana-pyroscope-app/profiles-explorer?${new URLSearchParams(`${u}${l}${d}${a}${p}`).toString()}`}const se={targets:[n.PluginExtensionPoints.ExploreToolbarAction],title:"Open in Explore Profiles",icon:"fire",description:"Try our new queryless experience for profiles",path:"/a/grafana-pyroscope-app/profiles-explorer",configure(e){if(!e||!e.targets||!e.timeRange||e.targets.length>1)return;const t=e.targets[0];return t.datasource&&"grafana-pyroscope-datasource"===t.datasource.type?{path:ie({pyroscopeQuery:t,timeRange:e.timeRange})}:void 0}},ae=(new n.AppPlugin).addLink(se).setRootPage((function(){const e=(0,s.useStyles2)(re),[t,r]=(0,O.useState)();return t?x().createElement(ne,{error:t}):x().createElement(s.ErrorBoundary,{onError:r},(()=>x().createElement(T.Ht,{client:E},x().createElement(W,null,x().createElement("div",{className:e.pageContainer},x().createElement(i.PluginPage,{layout:n.PageLayoutType.Custom},x().createElement("div",{className:"pyroscope-app"},x().createElement(ee,null))))))))}))})(),g})()));
//# sourceMappingURL=module.js.map