{"version": 3, "file": "373.js", "mappings": "6wCASO,SAASA,I,IAQaC,EAP3B,MAAM,SAAEC,EAAUC,MAAOC,EAAU,OAAEC,IAAWC,EAAAA,EAAAA,MACzCC,EAAiBC,IAAeC,EAAAA,EAAAA,MAChCC,EAAiBC,IAAsBC,EAAAA,EAAAA,UAAyBV,QAAAA,EAAYW,EAAAA,GAE7EC,GAAWC,EAAAA,EAAAA,eACXd,GAAWe,EAAAA,EAAAA,eAEXC,GAAcC,EAAAA,EAAAA,QAAqB,QAAdjB,EAAAA,EAASkB,aAATlB,IAAAA,OAAAA,EAAAA,EAAgBmB,UAQ3C,OANAC,EAAAA,EAAAA,YAAU,KACJnB,GACFS,EAAmBT,EACrB,GACC,CAACA,IAEG,CACLoB,KAAM,OACDZ,GAAAA,CACHN,eAEFmB,QAAS,CACPC,0BAAAA,GACEb,GAAoBc,GAAO,OACtBA,GAAAA,CACHC,sBAAuBD,EAAEC,wBAE7B,EACAC,cAAAA,CAAeC,GACbjB,GAAoBc,GAAO,OACtBA,GAAAA,CACHI,SAAUC,OAAOF,EAAMG,OAAOC,UAElC,EACAC,kCAAAA,GACEtB,GAAoBc,GAAO,OACtBA,GAAAA,CACHS,8BAA+BT,EAAES,gCAErC,EACAC,2BAAAA,GACExB,GAAoBc,GAAO,OACtBA,GAAAA,CACHW,uBAAwBX,EAAEW,yBAE9B,EACMC,YAAAA,G,SAAe,YACnB7B,EAAYE,EAAgBmB,UAE5B,UACQxB,EAAOK,IAEb4B,EAAAA,EAAAA,IAAe,CAAC,uCAClB,CAAE,MAAOnC,IACPoC,EAAAA,EAAAA,IAAapC,EAAgB,CAC3B,0CACA,wDAEJ,CACF,E,wLACAqC,MAAAA,GACE,IAAKvB,EAAYwB,QAEf,YADA3B,EAAS,GAAG4B,EAAAA,KAAkBC,EAAAA,GAAOC,WAIvC,MAAMC,EAAU,IAAIC,IAAI7B,EAAYwB,SAGhClC,GACFsC,EAAQE,aAAaC,IAAI,WAAYC,OAAO1C,IAG9CO,EAAS,GAAG+B,EAAQK,WAAWL,EAAQM,SACzC,GAGN,CC7Ee,SAASC,IACtB,MAAMC,GAASC,EAAAA,EAAAA,YAAWC,IACpB,KAAEjC,EAAI,QAAEC,GAAYvB,IAc1B,OAZIsB,EAAKlB,aACPmC,EAAAA,EAAAA,IAAajB,EAAKlB,WAAY,CAC5B,8CACA,gEAUF,oCACE,kBAACoD,EAAAA,EAASA,CAACC,MAAM,+BACjB,kBAACC,OAAAA,CAAKC,UAAWN,EAAOO,aAAcC,SAR1C,SAAkBjC,GAChBA,EAAMkC,iBACNvC,EAAQc,cACV,GAMM,oCACE,kBAAC0B,EAAAA,SAAQA,CAACC,MAAM,cAAcC,cAAY,uBACxC,kBAACC,EAAAA,eAAcA,KACb,kBAACC,EAAAA,YAAWA,CAACH,MAAM,yBAAyBI,WAAY,IACtD,kBAACC,EAAAA,aAAYA,CACXL,MAAM,gCACNM,KAAK,wBACLtC,MAAOV,EAAKI,qBACZ6C,SAAUhD,EAAQC,+BAIxB,kBAAC0C,EAAAA,eAAcA,KACb,kBAACC,EAAAA,YAAWA,CAACH,MAAM,0BAA0BQ,QAAQ,GAAGJ,WAAY,IAClE,kBAACK,EAAAA,MAAKA,CAACH,KAAK,YAAYI,KAAK,SAASC,IAAI,IAAI3C,MAAOV,EAAKO,SAAU0C,SAAUhD,EAAQI,oBAI5F,kBAACoC,EAAAA,SAAQA,CAACC,MAAM,mBAAmBC,cAAY,6BAC7C,kBAACC,EAAAA,eAAcA,KACb,kBAACC,EAAAA,YAAWA,CACVH,MAAM,0BACNI,WAAY,GACZI,QACE,kBAACI,MAAAA,CAAIjB,UAAWN,EAAOmB,SACrB,kBAACK,IAAAA,KAAE,yLAIH,kBAACA,IAAAA,KACC,kBAACC,IAAAA,CACCC,KAAK,qGACLhD,OAAO,SACPiD,IAAI,uBACL,gBAMPC,aAAAA,GAEA,kBAACZ,EAAAA,aAAYA,CACXL,MAAM,0BACNM,KAAK,2BACLtC,MAAOV,EAAKc,sBACZmC,SAAUhD,EAAQY,iCAM1B,kBAACyC,MAAAA,CAAIjB,UAAWN,EAAO6B,SACrB,kBAACC,EAAAA,OAAMA,CAACC,QAAQ,UAAUV,KAAK,UAAS,iBAGxC,kBAACS,EAAAA,OAAMA,CAACC,QAAQ,YAAYC,QAAS9D,EAAQiB,OAAQ8C,aAAW,4BAA2B,+BAQvG,CAEA,MAAM/B,EAAagC,IAA0B,CAC3C3B,aAAc4B,EAAAA,GAAG;;;iCAGcD,EAAME,OAAOC,OAAOC;;;;;mBAKlCJ,EAAMK,WAAWC,GAAGC;;IAGrCZ,QAASM,EAAAA,GAAG;;WAEHD,EAAMQ,QAAQ;IAEvBvB,QAASgB,EAAAA,GAAG;;gBAEED,EAAMQ,QAAQ;;;;eAIfR,EAAME,OAAOO,KAAKC;;;;;qBAKZV,EAAMK,WAAWM;;4pBCtH/B,SAAS3D,EAAapC,EAAcgG,GACzC,MAAMC,EAAUD,EAAKE,QAAO,CAACC,EAAKC,EAAKC,IAAO,E,sUAAA,IAAKF,GAAAA,CAAK,CAAC,OAAOE,EAAI,KAAMD,KAAQ,CAAEE,WAAY,iBAEhGC,EAAAA,EAAOvG,MAAMA,EAAOiG,IAEpBO,EAAAA,EAAAA,gBAAeC,QAAQ,CACrBlC,KAAMmC,EAAAA,UAAUC,WAAWxC,KAC3ByC,QAASZ,GAEb,CAEO,SAASa,EAAeb,GAC7BO,EAAAA,EAAOO,KAAKd,IAEZQ,EAAAA,EAAAA,gBAAeC,QAAQ,CACrBlC,KAAMmC,EAAAA,UAAUK,aAAa5C,KAC7ByC,QAASZ,GAEb,CAEO,SAAS7D,EAAe6D,IAC7BQ,EAAAA,EAAAA,gBAAeC,QAAQ,CACrBlC,KAAMmC,EAAAA,UAAUM,aAAa7C,KAC7ByC,QAASZ,GAEb,C,wFCAO,SAAS1F,IACd,MAAM,aAAEsC,EAAY,WAAEqE,GC5BjB,WACL,MAAMtG,GAAWC,EAAAA,EAAAA,eACXd,GAAWe,EAAAA,EAAAA,eAEjB,MAAO,CACL+B,aAAc,IAAIsE,gBAAgBpH,EAASkD,QAC3CiE,WAAaE,IACX,MAAMC,EAAkB,IAAIF,gBAAgBG,OAAOvH,SAASkD,QAE5D,IAAK,MAAOsE,EAAKzF,KAAU0F,OAAOC,QAAQL,GACxCC,EAAgBvE,IAAIyE,EAAKzF,GAG3BlB,EAAS,CAAEqC,OAAQoE,EAAgBK,YAAc,CAAEC,SAAS,GAAO,EAGzE,CDYuCC,G,IACb/E,EAAxB,MAAMlB,EAAWC,OAAwB,QAAjBiB,EAAAA,EAAagF,IAAI,mBAAjBhF,IAAAA,EAAAA,EAAgC,IAElDvC,EAAewH,IACnBZ,EAAW,CAAEvF,SAAUoB,OAAO+E,IAAe,EAK/C,OAhCF,SAA+BC,EAAsBzH,GACnD,MAAM,WAAE0H,EAAU,MAAE/H,EAAK,SAAED,IAAaI,EAAAA,EAAAA,GAAuB,CAAE6H,SAAUF,IAE3E,IAAIA,IAAeC,EAIf/H,IACF6G,EAAAA,EAAAA,IAAe,CACb,8CACA,uIAEFN,EAAAA,EAAOvG,MAAMA,GAEbK,EAAYK,EAAAA,EAAiBgB,WAK/BrB,EAAYN,EAAU2B,SACxB,CAUEuG,CAAsBvG,EAAW,EAAGrB,GAE7B,CAACqB,EAAUrB,EACpB,C,kCEjCO,MAAMK,EAAmC6G,OAAOW,OAAO,CAC5D3G,sBAAsB,EACtBG,SAAU,MACVK,8BAA8B,EAC9BE,uBAAuB,G,gZCJzB,MAAMkG,UAA0BC,EAAAA,EAGxBR,GAAAA,G,oCAAN,eACE,OAAO,WACE,mCAAoC,CAAES,OAAQ,OAAQC,KAAMC,KAAKC,UAAU,CAAC,KAClFC,MAAMC,GAAaA,EAASC,SAC5BF,MAAME,I,IACWA,EAAhB,MAAMC,EAAuB,QAAbD,EAAAA,EAAK5I,gBAAL4I,IAAAA,OAAAA,EAAAA,EAAeE,MAAK,EAAG1E,UAAWA,IAASgE,EAAkBW,sBAE7E,OAAKF,EAIEL,KAAKQ,MAAMH,EAAQ/G,OAHjB,CAAC,CAGsB,GAEtC,GAbA,E,CAeMgB,GAAAA,CAAImG,G,oCAAV,eACE,OAAO,WACE,mCAAoC,CACzCX,OAAQ,OACRC,KAAMC,KAAKC,UAAU,CACnBI,QAAS,CACPzE,KAAMgE,EAAkBW,oBACxBjH,MAAO0G,KAAKC,UAAUQ,QAI3BP,MAAMC,GAAaA,EAASC,QACjC,GAZA,E,IAjB6B,kB,EAAtBG,yB,EADHX,G,sFAiCC,MAAMc,EAAoB,IAAId,ECrB9B,SAAShI,GAAuB,QAAE6H,GAAyB,CAAC,GACjE,MAAM,WAAED,EAAU,MAAE/H,EAAK,KAAEmB,IAAS+H,EAAAA,EAAAA,GAAS,CAC3ClB,UACAmB,SAAU,CAAC,YACXC,QAAS,IACPH,EAAkBrB,MAAMa,MACrBE,GAECpB,OAAO8B,KAAK3I,EAAAA,GAAkBwF,QAAO,CAACC,EAAKmB,K,IACzCnB,EAAImB,E,EACJ,OADQ,QAARnB,GAAAA,EAAAA,GAAImB,EAAAA,UAAI,QAARnB,EAAImB,GAAS5G,EAAAA,EAAiB4G,IACvBnB,CAAG,GACTwC,QAIHW,YAAapJ,IAAWqJ,EAAAA,EAAAA,GAAY,CAC1CC,WAAaR,GAAgCC,EAAkBpG,IAAImG,GACnES,YAAa,WAGf,MAAO,CACL1B,aACA/H,MAAOiJ,EAAkBS,aAAa1J,GAAS,KAAOA,EACtDD,SAAUoB,EACVjB,SAEJ,C,wFCxCIyJ,EAAmB,cAAc,IACnC,GACA,QAAiB,EACjB,GACA,GACA,WAAAC,CAAYC,EAAQC,GAClBC,QACAC,MAAK,EAAUH,EACfG,KAAKC,WAAWH,GAChBE,KAAKE,cACLF,MAAK,GACP,CACA,WAAAE,GACEF,KAAK9J,OAAS8J,KAAK9J,OAAOiK,KAAKH,MAC/BA,KAAKI,MAAQJ,KAAKI,MAAMD,KAAKH,KAC/B,CACA,UAAAC,CAAWH,GACT,MAAMO,EAAcL,KAAKF,QACzBE,KAAKF,QAAUE,MAAK,EAAQM,uBAAuBR,IAC9C,QAAoBE,KAAKF,QAASO,IACrCL,MAAK,EAAQO,mBAAmBC,OAAO,CACrCjG,KAAM,yBACNkG,SAAUT,MAAK,EACfU,SAAUV,OAGVK,GAAaM,aAAeX,KAAKF,QAAQa,cAAe,QAAQN,EAAYM,gBAAiB,QAAQX,KAAKF,QAAQa,aACpHX,KAAKI,QAC4C,YAAxCJ,MAAK,GAAkBhJ,MAAM4J,QACtCZ,MAAK,EAAiBC,WAAWD,KAAKF,QAE1C,CACA,aAAAe,GACOb,KAAKc,gBACRd,MAAK,GAAkBe,eAAef,KAE1C,CACA,gBAAAgB,CAAiBC,GACfjB,MAAK,IACLA,MAAK,EAAQiB,EACf,CACA,gBAAAC,GACE,OAAOlB,MAAK,CACd,CACA,KAAAI,GACEJ,MAAK,GAAkBe,eAAef,MACtCA,MAAK,OAAmB,EACxBA,MAAK,IACLA,MAAK,GACP,CACA,MAAA9J,CAAOiL,EAAWrB,GAKhB,OAJAE,MAAK,EAAiBF,EACtBE,MAAK,GAAkBe,eAAef,MACtCA,MAAK,EAAmBA,MAAK,EAAQO,mBAAmBa,MAAMpB,MAAK,EAASA,KAAKF,SACjFE,MAAK,EAAiBqB,YAAYrB,MAC3BA,MAAK,EAAiBsB,QAAQH,EACvC,CACA,KACE,MAAMnK,EAAQgJ,MAAK,GAAkBhJ,QAAS,SAC9CgJ,MAAK,EAAiB,IACjBhJ,EACHuK,UAA4B,YAAjBvK,EAAM4J,OACjBY,UAA4B,YAAjBxK,EAAM4J,OACjBa,QAA0B,UAAjBzK,EAAM4J,OACfc,OAAyB,SAAjB1K,EAAM4J,OACd1K,OAAQ8J,KAAK9J,OACbkK,MAAOJ,KAAKI,MAEhB,CACA,GAAQa,GACNU,EAAA,EAAcC,OAAM,KAClB,GAAI5B,MAAK,GAAkBA,KAAKc,eAAgB,CAC9C,MAAMK,EAAYnB,MAAK,EAAemB,UAChClF,EAAU+D,MAAK,EAAe/D,QACf,YAAjBgF,GAAQ1G,MACVyF,MAAK,EAAe6B,YAAYZ,EAAO9J,KAAMgK,EAAWlF,GACxD+D,MAAK,EAAe8B,YAAYb,EAAO9J,KAAM,KAAMgK,EAAWlF,IACpC,UAAjBgF,GAAQ1G,OACjByF,MAAK,EAAe+B,UAAUd,EAAOjL,MAAOmL,EAAWlF,GACvD+D,MAAK,EAAe8B,iBAClB,EACAb,EAAOjL,MACPmL,EACAlF,GAGN,CACA+D,KAAKgC,UAAUC,SAASC,IACtBA,EAASlC,MAAK,EAAe,GAC7B,GAEN,G,mBCzFF,SAAST,EAAYO,EAASqC,GAC5B,MAAMtC,GAAS,QAAesC,IACvBzB,GAAY,YACjB,IAAM,IAAIf,EACRE,EACAC,KAGJ,aAAgB,KACdY,EAAST,WAAWH,EAAQ,GAC3B,CAACY,EAAUZ,IACd,MAAMsC,EAAS,uBACb,eACGC,GAAkB3B,EAAS4B,UAAUX,EAAA,EAAcY,WAAWF,KAC/D,CAAC3B,KAEH,IAAMA,EAASQ,qBACf,IAAMR,EAASQ,qBAEXhL,EAAS,eACb,CAACiL,EAAWqB,KACV9B,EAASxK,OAAOiL,EAAWqB,GAAeC,MAAM,IAAK,GAEvD,CAAC/B,IAEH,GAAI0B,EAAOpM,QAAS,OAAiB0K,EAASZ,QAAQ4C,aAAc,CAACN,EAAOpM,QAC1E,MAAMoM,EAAOpM,MAEf,MAAO,IAAKoM,EAAQlM,SAAQoJ,YAAa8C,EAAOlM,OAClD,C", "sources": ["webpack://grafana-pyroscope-app/./pages/SettingsView/domain/useSettingsView.ts", "webpack://grafana-pyroscope-app/./pages/SettingsView/SettingsView.tsx", "webpack://grafana-pyroscope-app/./shared/domain/displayStatus.ts", "webpack://grafana-pyroscope-app/./shared/domain/url-params/useMaxNodesFromUrl.ts", "webpack://grafana-pyroscope-app/./shared/domain/url-params/useUrlSearchParams.ts", "webpack://grafana-pyroscope-app/./shared/infrastructure/settings/PluginSettings.ts", "webpack://grafana-pyroscope-app/./shared/infrastructure/settings/settingsApiClient.ts", "webpack://grafana-pyroscope-app/./shared/infrastructure/settings/useFetchPluginSettings.ts", "webpack://grafana-pyroscope-app/../node_modules/@tanstack/query-core/build/modern/mutationObserver.js", "webpack://grafana-pyroscope-app/../node_modules/@tanstack/react-query/build/modern/useMutation.js"], "sourcesContent": ["import { displayError, displaySuccess } from '@shared/domain/displayStatus';\nimport { useMaxNodesFromUrl } from '@shared/domain/url-params/useMaxNodesFromUrl';\nimport { DEFAULT_SETTINGS, PluginSettings } from '@shared/infrastructure/settings/PluginSettings';\nimport { useFetchPluginSettings } from '@shared/infrastructure/settings/useFetchPluginSettings';\nimport { useEffect, useRef, useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\n\nimport { PLUGIN_BASE_URL, ROUTES } from '../../../constants';\n\nexport function useSettingsView() {\n  const { settings, error: fetchError, mutate } = useFetchPluginSettings();\n  const [maxNodesFromUrl, setMaxNodes] = useMaxNodesFromUrl();\n  const [currentSettings, setCurrentSettings] = useState<PluginSettings>(settings ?? DEFAULT_SETTINGS);\n\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const referrerRef = useRef(location.state?.referrer);\n\n  useEffect(() => {\n    if (settings) {\n      setCurrentSettings(settings);\n    }\n  }, [settings]);\n\n  return {\n    data: {\n      ...currentSettings,\n      fetchError,\n    },\n    actions: {\n      toggleCollapsedFlamegraphs() {\n        setCurrentSettings((s) => ({\n          ...s,\n          collapsedFlamegraphs: !s.collapsedFlamegraphs,\n        }));\n      },\n      updateMaxNodes(event: React.ChangeEvent<HTMLInputElement>) {\n        setCurrentSettings((s) => ({\n          ...s,\n          maxNodes: Number(event.target.value),\n        }));\n      },\n      toggleEnableFlameGraphDotComExport() {\n        setCurrentSettings((s) => ({\n          ...s,\n          enableFlameGraphDotComExport: !s.enableFlameGraphDotComExport,\n        }));\n      },\n      toggleEnableFunctionDetails() {\n        setCurrentSettings((s) => ({\n          ...s,\n          enableFunctionDetails: !s.enableFunctionDetails,\n        }));\n      },\n      async saveSettings() {\n        setMaxNodes(currentSettings.maxNodes);\n\n        try {\n          await mutate(currentSettings);\n\n          displaySuccess(['Plugin settings successfully saved!']);\n        } catch (error) {\n          displayError(error as Error, [\n            'Error while saving the plugin settings!',\n            'Please try again later, sorry for the inconvenience.',\n          ]);\n        }\n      },\n      goBack() {\n        if (!referrerRef.current) {\n          navigate(`${PLUGIN_BASE_URL}${ROUTES.EXPLORE}`);\n          return;\n        }\n\n        const backUrl = new URL(referrerRef.current);\n\n        // when calling saveSettings() above, the new maxNodes value is set and the URL search parameter is updated (see useMaxNodesFromUrl.ts)\n        if (maxNodesFromUrl) {\n          backUrl.searchParams.set('maxNodes', String(maxNodesFromUrl));\n        }\n\n        navigate(`${backUrl.pathname}${backUrl.search}`);\n      },\n    },\n  };\n}\n", "import { css } from '@emotion/css';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Button, FieldSet, InlineField, InlineFieldRow, InlineSwitch, Input, useStyles2 } from '@grafana/ui';\nimport { displayError } from '@shared/domain/displayStatus';\nimport { PageTitle } from '@shared/ui/PageTitle';\nimport React from 'react';\n\nimport { useSettingsView } from './domain/useSettingsView';\n\nexport default function SettingsView() {\n  const styles = useStyles2(getStyles);\n  const { data, actions } = useSettingsView();\n\n  if (data.fetchError) {\n    displayError(data.fetchError, [\n      'Error while retrieving the plugin settings!',\n      'Please try to reload the page, sorry for the inconvenience.',\n    ]);\n  }\n\n  function onSubmit(event: React.FormEvent) {\n    event.preventDefault();\n    actions.saveSettings();\n  }\n\n  return (\n    <>\n      <PageTitle title=\"Profiles settings (tenant)\" />\n      <form className={styles.settingsForm} onSubmit={onSubmit}>\n        <>\n          <FieldSet label=\"Flame graph\" data-testid=\"flamegraph-settings\">\n            <InlineFieldRow>\n              <InlineField label=\"Collapsed flame graphs\" labelWidth={24}>\n                <InlineSwitch\n                  label=\"Toggle collapsed flame graphs\"\n                  name=\"collapsed-flamegraphs\"\n                  value={data.collapsedFlamegraphs}\n                  onChange={actions.toggleCollapsedFlamegraphs}\n                />\n              </InlineField>\n            </InlineFieldRow>\n            <InlineFieldRow>\n              <InlineField label=\"Maximum number of nodes\" tooltip=\"\" labelWidth={24}>\n                <Input name=\"max-nodes\" type=\"number\" min=\"1\" value={data.maxNodes} onChange={actions.updateMaxNodes} />\n              </InlineField>\n            </InlineFieldRow>\n          </FieldSet>\n          <FieldSet label=\"Function details\" data-testid=\"function-details-settings\">\n            <InlineFieldRow>\n              <InlineField\n                label=\"Enable function details\"\n                labelWidth={24}\n                tooltip={\n                  <div className={styles.tooltip}>\n                    <p>\n                      The function details feature enables mapping of resource usage to lines of source code. If the\n                      GitHub integration is configured, then the source code will be downloaded from GitHub.\n                    </p>\n                    <p>\n                      <a\n                        href=\"https://grafana.com/docs/grafana-cloud/monitor-applications/profiles/pyroscope-github-integration/\"\n                        target=\"_blank\"\n                        rel=\"noreferrer noopener\"\n                      >\n                        Learn more\n                      </a>\n                    </p>\n                  </div>\n                }\n                interactive\n              >\n                <InlineSwitch\n                  label=\"Toggle function details\"\n                  name=\"function-details-feature\"\n                  value={data.enableFunctionDetails}\n                  onChange={actions.toggleEnableFunctionDetails}\n                />\n              </InlineField>\n            </InlineFieldRow>\n          </FieldSet>\n\n          <div className={styles.buttons}>\n            <Button variant=\"primary\" type=\"submit\">\n              Save settings\n            </Button>\n            <Button variant=\"secondary\" onClick={actions.goBack} aria-label=\"Back to Explore Profiles\">\n              Back to Explore Profiles\n            </Button>\n          </div>\n        </>\n      </form>\n    </>\n  );\n}\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  settingsForm: css`\n    & > fieldset {\n      border: 0 none;\n      border-bottom: 1px solid ${theme.colors.border.weak};\n      padding-left: 0;\n    }\n\n    & > fieldset > legend {\n      font-size: ${theme.typography.h4.fontSize};\n    }\n  `,\n  buttons: css`\n    display: flex;\n    gap: ${theme.spacing(1)};\n  `,\n  tooltip: css`\n    p {\n      margin: ${theme.spacing(1)};\n    }\n\n    a {\n      color: ${theme.colors.text.link};\n    }\n\n    em {\n      font-style: normal;\n      font-weight: ${theme.typography.fontWeightBold};\n    }\n  `,\n});\n", "import { AppEvents } from '@grafana/data';\nimport { getAppEvents } from '@grafana/runtime';\nimport { logger } from '@shared/infrastructure/tracking/logger';\n\nexport function displayError(error: Error, msgs: string[]) {\n  const context = msgs.reduce((acc, msg, i) => ({ ...acc, [`info${i + 1}`]: msg }), { handheldBy: 'displayError' });\n\n  logger.error(error, context);\n\n  getAppEvents().publish({\n    type: AppEvents.alertError.name,\n    payload: msgs,\n  });\n}\n\nexport function displayWarning(msgs: string[]) {\n  logger.warn(msgs);\n\n  getAppEvents().publish({\n    type: AppEvents.alertWarning.name,\n    payload: msgs,\n  });\n}\n\nexport function displaySuccess(msgs: string[]) {\n  getAppEvents().publish({\n    type: AppEvents.alertSuccess.name,\n    payload: msgs,\n  });\n}\n", "import { displayWarning } from '@shared/domain/displayStatus';\nimport { DEFAULT_SETTINGS } from '@shared/infrastructure/settings/PluginSettings';\nimport { useFetchPluginSettings } from '@shared/infrastructure/settings/useFetchPluginSettings';\nimport { logger } from '@shared/infrastructure/tracking/logger';\n\nimport { useUrlSearchParams } from './useUrlSearchParams';\n\nfunction useSetDefaultMaxNodes(hasMaxNodes: boolean, setMaxNodes: (newMaxNodes: number) => void) {\n  const { isFetching, error, settings } = useFetchPluginSettings({ enabled: !hasMaxNodes });\n\n  if (hasMaxNodes || isFetching) {\n    return;\n  }\n\n  if (error) {\n    displayWarning([\n      'Error while retrieving the plugin settings!',\n      'Some features might not work as expected (e.g. flame graph max nodes). Please try to reload the page, sorry for the inconvenience.',\n    ]);\n    logger.error(error);\n\n    setMaxNodes(DEFAULT_SETTINGS.maxNodes);\n\n    return;\n  }\n\n  setMaxNodes(settings!.maxNodes);\n}\n\nexport function useMaxNodesFromUrl(): [number | null, (newMaxNodes: number) => void] {\n  const { searchParams, pushNewUrl } = useUrlSearchParams();\n  const maxNodes = Number(searchParams.get('maxNodes') ?? '');\n\n  const setMaxNodes = (newMaxNodes: number) => {\n    pushNewUrl({ maxNodes: String(newMaxNodes) });\n  };\n\n  useSetDefaultMaxNodes(maxNodes > 0, setMaxNodes);\n\n  return [maxNodes, setMaxNodes];\n}\n", "import { useLocation, useNavigate } from 'react-router-dom';\n\nexport function useUrlSearchParams() {\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  return {\n    searchParams: new URLSearchParams(location.search),\n    pushNewUrl: (newParams: Record<string, string>) => {\n      const newSearchParams = new URLSearchParams(window.location.search);\n\n      for (const [key, value] of Object.entries(newParams)) {\n        newSearchParams.set(key, value);\n      }\n\n      navigate({ search: newSearchParams.toString() }, { replace: true });\n    },\n  };\n}\n", "export type PluginSettings = {\n  collapsedFlamegraphs: boolean;\n  maxNodes: number;\n  enableFlameGraphDotComExport: boolean;\n  enableFunctionDetails: boolean;\n};\n\nexport const DEFAULT_SETTINGS: PluginSettings = Object.freeze({\n  collapsedFlamegraphs: false,\n  maxNodes: 16384,\n  enableFlameGraphDotComExport: true,\n  enableFunctionDetails: true,\n});\n", "import { ApiClient } from '../http/ApiClient';\nimport { PluginSettings } from './PluginSettings';\n\ntype ApiResponse = {\n  settings: Array<{ name: string; value: string }>;\n};\n\nclass SettingsApiClient extends ApiClient {\n  static PLUGIN_SETTING_NAME = 'pluginSettings';\n\n  async get(): Promise<PluginSettings> {\n    return super\n      .fetch('/settings.v1.SettingsService/Get', { method: 'POST', body: JSON.stringify({}) })\n      .then((response) => response.json())\n      .then((json: ApiResponse) => {\n        const setting = json.settings?.find(({ name }) => name === SettingsApiClient.PLUGIN_SETTING_NAME);\n\n        if (!setting) {\n          return {};\n        }\n\n        return JSON.parse(setting.value);\n      });\n  }\n\n  async set(newSettings: PluginSettings) {\n    return super\n      .fetch('/settings.v1.SettingsService/Set', {\n        method: 'POST',\n        body: JSON.stringify({\n          setting: {\n            name: SettingsApiClient.PLUGIN_SETTING_NAME,\n            value: JSON.stringify(newSettings),\n          },\n        }),\n      })\n      .then((response) => response.json());\n  }\n}\n\nexport const settingsApiClient = new SettingsApiClient();\n", "import { useMutation, useQuery } from '@tanstack/react-query';\n\nimport { DEFAULT_SETTINGS, PluginSettings } from './PluginSettings';\nimport { settingsApiClient } from './settingsApiClient';\n\ntype FetchParams = {\n  enabled?: boolean;\n};\n\ntype FetchResponse = {\n  isFetching: boolean;\n  error: Error | null;\n  settings?: PluginSettings;\n  mutate: (newSettings: PluginSettings) => Promise<void>;\n};\n\n/**\n * Fetches the plugin settings and, if none/only some have been stored previously, returns adequate default values for the rest of the application\n */\nexport function useFetchPluginSettings({ enabled }: FetchParams = {}): FetchResponse {\n  const { isFetching, error, data } = useQuery({\n    enabled,\n    queryKey: ['settings'],\n    queryFn: () =>\n      settingsApiClient.get().then(\n        (json) =>\n          // provide defaults if any value comes null or undefined from the API (which can be the case ;))\n          Object.keys(DEFAULT_SETTINGS).reduce((acc, key) => {\n            acc[key] ??= DEFAULT_SETTINGS[key as keyof PluginSettings]; // TS luv :man_shrug:\n            return acc;\n          }, json as Record<string, any>) as PluginSettings // TS luv :man_shrug:\n      ),\n  });\n\n  const { mutateAsync: mutate } = useMutation({\n    mutationFn: (newSettings: PluginSettings) => settingsApiClient.set(newSettings),\n    networkMode: 'always',\n  });\n\n  return {\n    isFetching,\n    error: settingsApiClient.isAbortError(error) ? null : error,\n    settings: data,\n    mutate,\n  };\n}\n", "// src/mutationObserver.ts\nimport { getDefaultState } from \"./mutation.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { hashKey, shallowEqualObjects } from \"./utils.js\";\nvar MutationObserver = class extends Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? getDefaultState();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const context = this.#currentResult.context;\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context);\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context);\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(action.error, variables, context);\n          this.#mutateOptions.onSettled?.(\n            void 0,\n            action.error,\n            variables,\n            context\n          );\n        }\n      }\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\nexport {\n  MutationObserver\n};\n//# sourceMappingURL=mutationObserver.js.map", "\"use client\";\n\n// src/useMutation.ts\nimport * as React from \"react\";\nimport { MutationObserver, notifyManager } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nimport { noop, shouldThrowError } from \"./utils.js\";\nfunction useMutation(options, queryClient) {\n  const client = useQueryClient(queryClient);\n  const [observer] = React.useState(\n    () => new MutationObserver(\n      client,\n      options\n    )\n  );\n  React.useEffect(() => {\n    observer.setOptions(options);\n  }, [observer, options]);\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer]\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult()\n  );\n  const mutate = React.useCallback(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop);\n    },\n    [observer]\n  );\n  if (result.error && shouldThrowError(observer.options.throwOnError, [result.error])) {\n    throw result.error;\n  }\n  return { ...result, mutate, mutateAsync: result.mutate };\n}\nexport {\n  useMutation\n};\n//# sourceMappingURL=useMutation.js.map"], "names": ["useSettingsView", "location", "settings", "error", "fetchError", "mutate", "useFetchPluginSettings", "maxNodesFromUrl", "setMaxNodes", "useMaxNodesFromUrl", "currentSettings", "setCurrentSettings", "useState", "DEFAULT_SETTINGS", "navigate", "useNavigate", "useLocation", "referrerRef", "useRef", "state", "referrer", "useEffect", "data", "actions", "toggleCollapsedFlamegraphs", "s", "collapsedFlamegraphs", "updateMaxNodes", "event", "maxNodes", "Number", "target", "value", "toggleEnableFlameGraphDotComExport", "enableFlameGraphDotComExport", "toggleEnableFunctionDetails", "enableFunctionDetails", "saveSettings", "displaySuccess", "displayError", "goBack", "current", "PLUGIN_BASE_URL", "ROUTES", "EXPLORE", "backUrl", "URL", "searchParams", "set", "String", "pathname", "search", "SettingsView", "styles", "useStyles2", "getStyles", "Page<PERSON><PERSON>le", "title", "form", "className", "settingsForm", "onSubmit", "preventDefault", "FieldSet", "label", "data-testid", "InlineFieldRow", "InlineField", "labelWidth", "InlineSwitch", "name", "onChange", "tooltip", "Input", "type", "min", "div", "p", "a", "href", "rel", "interactive", "buttons", "<PERSON><PERSON>", "variant", "onClick", "aria-label", "theme", "css", "colors", "border", "weak", "typography", "h4", "fontSize", "spacing", "text", "link", "fontWeightBold", "msgs", "context", "reduce", "acc", "msg", "i", "handheldBy", "logger", "getAppEvents", "publish", "AppEvents", "alertError", "payload", "displayWarning", "warn", "alertWarning", "alertSuccess", "pushNewUrl", "URLSearchParams", "newParams", "newSearchParams", "window", "key", "Object", "entries", "toString", "replace", "useUrlSearchParams", "get", "newMaxNodes", "hasMaxNodes", "isFetching", "enabled", "useSetDefaultMaxNodes", "freeze", "SettingsApiClient", "ApiClient", "method", "body", "JSON", "stringify", "then", "response", "json", "setting", "find", "PLUGIN_SETTING_NAME", "parse", "newSettings", "settingsApiClient", "useQuery", "query<PERSON><PERSON>", "queryFn", "keys", "mutateAsync", "useMutation", "mutationFn", "networkMode", "isAbortError", "MutationObserver", "constructor", "client", "options", "super", "this", "setOptions", "bindMethods", "bind", "reset", "prevOptions", "defaultMutationOptions", "getMutationCache", "notify", "mutation", "observer", "<PERSON><PERSON><PERSON>", "status", "onUnsubscribe", "hasListeners", "removeObserver", "onMutationUpdate", "action", "getCurrentResult", "variables", "build", "addObserver", "execute", "isPending", "isSuccess", "isError", "isIdle", "notify<PERSON><PERSON>ger", "batch", "onSuccess", "onSettled", "onError", "listeners", "for<PERSON>ach", "listener", "queryClient", "result", "onStoreChange", "subscribe", "batchCalls", "mutateOptions", "catch", "throwOnError"], "sourceRoot": ""}