<div class="editor-row">
  <div class="section gf-form-group">
    <div class="grafana-info-box">
      <h5>Migration</h5>
      <p>
        Consider switching to the <a href="https://grafana.com/docs/grafana/latest/panels-visualizations/visualizations/geomap/" target="_blank">Geomap</a> visualization type. It is a more capable and performant version of this panel.
      </p>
      <ul>
        <li>Multiple layers with opacity</li>
        <li>Multiple basemap options, that can also be layered</li>
        <li>More viewport options, including real time data fit</li>
        <li>Markers, Heatmap, GeoJSON, CARTO, XYZ, OSM, ArcGIS, Night / Day, Routes, Photos, and more...</li>
      </ul>
      <br>
      <p>
        <button class="btn btn-primary" ng-click="ctrl.migrateToReact()">
          Migrate
        </button>
      </p>
    </div>

    <h5 class="section-heading">Map Visual Options</h5>
    <div class="gf-form">
      <label class="gf-form-label width-10">Center</label>
      <div class="gf-form-select-wrapper max-width-10">
        <select class="input-small gf-form-input" ng-model="ctrl.panel.mapCenter" ng-options="t for t in ['(0°, 0°)', 'North America', 'Europe', 'West Asia', 'SE Asia', 'custom', 'Last GeoHash']"
          ng-change="ctrl.setNewMapCenter()"></select>
      </div>
      <div class="gf-form" ng-show="ctrl.panel.mapCenter === 'custom'">
        <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.mapCenterLatitude" ng-change="ctrl.setNewMapCenter()"
          ng-model-onblur />
        <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.mapCenterLongitude" ng-change="ctrl.setNewMapCenter()"
          ng-model-onblur />
      </div>
    </div>
    <div class="gf-form">
      <label class="gf-form-label width-10">Initial Zoom</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.initialZoom" ng-change="ctrl.setZoom()"
        placeholder="1" ng-model-onblur />
    </div>
    <div class="gf-form">
      <label class="gf-form-label width-10">Min Circle Size</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.circleMinSize" ng-change="ctrl.render()"
        placeholder="2" ng-model-onblur />
    </div>
    <div class="gf-form">
      <label class="gf-form-label width-10">Max Circle Size</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.circleMaxSize" ng-change="ctrl.render()"
        placeholder="30" ng-model-onblur />
    </div>
    <gf-form-switch class="gf-form" label="Sticky Labels" label-class="width-10" checked="ctrl.panel.stickyLabels" on-change="ctrl.toggleStickyLabels()">
    </gf-form-switch>
    <div class="gf-form">
      <label class="gf-form-label width-10">Decimals</label>
      <input type="number" class="input-small gf-form-input width-10" ng-model="ctrl.panel.decimals" ng-change="ctrl.refresh()"
        ng-model-onblur />
    </div>
    <div class="gf-form">
      <label class="gf-form-label width-10">Unit</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.unitSingular" placeholder="singular form"
        ng-change="ctrl.render()" ng-model-onblur />
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.unitPlural" placeholder="plural form"
        ng-change="ctrl.render()" ng-model-onblur />
    </div>
    <gf-form-switch class="gf-form" label="Show Legend" label-class="width-10" checked="ctrl.panel.showLegend" on-change="ctrl.toggleLegend()"></gf-form-switch>
    <gf-form-switch class="gf-form" label="Mouse Wheel Zoom" label-class="width-10" checked="ctrl.panel.mouseWheelZoom" on-change="ctrl.toggleMouseWheelZoom()"></gf-form-switch>
  </div>
  <div class="section gf-form-group">
    <h5 class="section-heading">Map Data Options</h5>
    <div class="gf-form-group">
      <div class="gf-form">
        <label class="gf-form-label width-12">Location Data</label>
        <div class="gf-form-select-wrapper max-width-10">
          <select class="input-small gf-form-input" ng-model="ctrl.panel.locationData" ng-options="t for t in ['countries', 'countries_3letter', 'states', 'probes', 'geohash', 'json endpoint', 'jsonp endpoint', 'json result', 'table']"
            ng-change="ctrl.changeLocationData()"></select>
        </div>
      </div>
      <div class="gf-form" ng-show="ctrl.panel.locationData !== 'geohash'">
        <label class="gf-form-label width-12">Aggregation</label>
        <div class="gf-form-select-wrapper max-width-10">
          <select class="input-small gf-form-input" ng-model="ctrl.panel.valueName" ng-options="f for f in ['min','max','avg', 'current', 'total']"
            ng-change="ctrl.refresh()"></select>
        </div>
      </div>
      <div class="gf-form" ng-show="ctrl.panel.locationData === 'json endpoint'">
        <label class="gf-form-label width-12">Endpoint url</label>
        <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.jsonUrl" ng-change="ctrl.refresh()" ng-model-onblur
        />
      </div>
      <div class="gf-form" ng-show="ctrl.panel.locationData === 'jsonp endpoint'">
        <label class="gf-form-label width-12">Endpoint url</label>
        <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.jsonpUrl" ng-change="ctrl.refresh()" ng-model-onblur
        />
      </div>
      <div class="gf-form" ng-show="ctrl.panel.locationData === 'jsonp endpoint'">
        <label class="gf-form-label width-12">Jsonp Callback</label>
        <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.jsonpCallback" ng-change="ctrl.refresh()"
          ng-model-onblur />
      </div>
    </div>
    <div class="grafana-info-box max-width-28" ng-show="ctrl.panel.locationData ==='countries'">
      <h5>Mapping Between Time Series Query and Worldmap</h5>
      <p>The query should be formatted as Time Series data. The time series (group by) name should be a 2-letter country code.
        For example: US for United States or FR for France.</p>
    </div>
    <div class="grafana-info-box max-width-28" ng-show="ctrl.panel.locationData ==='countries_3letter'">
      <h5>Mapping Between Time Series Query and Worldmap</h5>
      <p>The query should be formatted as Time Series data. The time series (group by) name should be a 3-letter country code.
        For example: USA for United States or FRA for France.</p>
    </div>
    <div class="grafana-info-box max-width-28" ng-show="ctrl.panel.locationData ==='states'">
        <h5>Mapping Between Time Series Query and Worldmap</h5>
        <p>The query should be formatted as Time Series data. The time series (group by) name should be a 2-letter US state code.
          For example: CA for California.</p>
      </div>
    <div class="grafana-info-box max-width-28" ng-show="ctrl.panel.locationData ==='geohash'">
      <h5>Mapping Between Geohash Query and Worldmap</h5>
      <p>The query should be an Elasticsearch using the Geo Hash Grid feature or a Prometheus query that returns geohashes.</p>
      <ul>
        <li>
          <b>Location Name Field (optional)</b>: enter the name of the Location Name column. Used to label each circle on the
          map. If it is empty then the geohash value is used as the label.</li>
        <li>
          <b>geo_point/geohash Field</b>: enter the name of the geo_point/geohash column. This is used to calculate where the
          circle should be drawn.</li>
        <li>
          <b>Metric Field</b>: enter the name of the metric column. This is used to give the circle a value - this determines
          how large the circle is.</li>
      </ul>
    </div>
    <div class="grafana-info-box max-width-28" ng-show="ctrl.showTableGeohashOptions()">
      <h5>Mapping Between Table Query and Worldmap</h5>
      <p>The query should be formatted as Table data and have a geohash column and a numeric metric column.</p>
      <ul>
        <li>
          <b>Location Name Field (optional)</b>: enter the name of the Location Name column. Used to label each circle on the
          map. If it is empty then the geohash value is used as the label.</li>
        <li>
          <b>Geohash Field</b>: enter the name of the geohash column. This is used to calculate where the circle should be drawn.</li>
        <li>
          <b>Metric Field</b>: enter the name of the metric column. This is used to give the circle a value - this determines
          how large the circle is.</li>
      </ul>
    </div>
    <div class="grafana-info-box max-width-28" ng-show="ctrl.showTableCoordinateOptions()">
      <h5>Mapping Between Table Query and Worldmap</h5>
      <p>The query should be formatted as Table data and contain latitude, longitude columns and a numeric metric column.</p>
      <ul>
        <li>
          <b>Location Name Field (optional)</b>: enter the name of the Location Name column. Used to label each circle on the
          map. If it is empty then the value N/A is used as the label.</li>
        <li>
          <b>Latitude/Longitude Fields</b>: enter the name of the latitude and longitude columns. These are used to calculate
          where the circle should be drawn.</li>
        <li>
          <b>Metric Field</b>: enter the name of the metric column. This is used to give the circle a value - this determines
          how large the circle is.</li>
      </ul>
    </div>
    <h6 ng-show="ctrl.panel.locationData === 'table' || ctrl.panel.locationData === 'geohash'">Field Mapping</h6>
    <div class="gf-form" ng-show="ctrl.panel.locationData === 'table'">
      <label class="gf-form-label width-12">Table Query Format</label>
      <div class="gf-form-select-wrapper max-width-10">
        <select class="input-small gf-form-input" ng-model="ctrl.panel.tableQueryOptions.queryType" ng-options="t for t in ['geohash', 'coordinates']"
          ng-change="ctrl.refresh()"></select>
      </div>
    </div>
    <div class="gf-form" ng-show="ctrl.panel.locationData === 'table'">
      <label class="gf-form-label width-12">Location Name Field</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.tableQueryOptions.labelField" ng-change="ctrl.refresh()"
        ng-model-onblur />
    </div>
    <div class="gf-form" ng-show="ctrl.panel.locationData === 'table'">
      <label class="gf-form-label width-12">Metric Field</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.tableQueryOptions.metricField" ng-change="ctrl.refresh()"
        ng-model-onblur />
    </div>
    <div class="gf-form" ng-show="ctrl.showTableGeohashOptions()">
      <label class="gf-form-label width-12">Geohash Field</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.tableQueryOptions.geohashField" ng-change="ctrl.refresh()"
        ng-model-onblur />
    </div>
    <div class="gf-form" ng-show="ctrl.showTableCoordinateOptions()">
      <label class="gf-form-label width-12">Latitude Field</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.tableQueryOptions.latitudeField" ng-change="ctrl.refresh()"
        ng-model-onblur />
    </div>
    <div class="gf-form" ng-show="ctrl.showTableCoordinateOptions()">
      <label class="gf-form-label width-12">Longitude Field</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.tableQueryOptions.longitudeField" ng-change="ctrl.refresh()"
        ng-model-onblur />
    </div>
    <div class="gf-form" ng-show="ctrl.panel.locationData === 'geohash'">
      <label class="gf-form-label width-12">Location Name Field</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.esLocationName" ng-change="ctrl.refresh()"
        ng-model-onblur />
    </div>
    <div class="gf-form" ng-show="ctrl.panel.locationData === 'geohash'">
      <label class="gf-form-label width-12">geo_point/geohash Field</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.esGeoPoint" ng-change="ctrl.refresh()"
        ng-model-onblur />
    </div>
    <div class="gf-form" ng-show="ctrl.panel.locationData === 'geohash'">
      <label class="gf-form-label width-12">Metric Field</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.esMetric" ng-change="ctrl.refresh()" ng-model-onblur
      />
    </div>
  </div>

  <div class="section gf-form-group">
    <h5 class="section-heading">Threshold Options</h5>
    <div class="gf-form">
      <label class="gf-form-label width-10">Thresholds</label>
      <input type="text" class="input-small gf-form-input width-10" ng-model="ctrl.panel.thresholds" ng-change="ctrl.changeThresholds()"
        placeholder="0,10" ng-model-onblur />
    </div>
    <div class="gf-form">
      <label class="gf-form-label width-10">Colors</label>
      <spectrum-picker class="gf-form-input width-3" ng-repeat="color in ctrl.panel.colors track by $index" ng-model="ctrl.panel.colors[$index]"
        ng-change="ctrl.changeThresholds()"></spectrum-picker>
    </div>
  </div>

  <div class="section gf-form-group">
    <h5 class="section-heading">Hide series</h5>
    <gf-form-switch class="gf-form" label="With only nulls" label-class="width-10" checked="ctrl.panel.hideEmpty" on-change="ctrl.render()">
    </gf-form-switch>
    <gf-form-switch class="gf-form" label="With only zeros" label-class="width-10" checked="ctrl.panel.hideZero" on-change="ctrl.render()">
    </gf-form-switch>
  </div>
</div>
