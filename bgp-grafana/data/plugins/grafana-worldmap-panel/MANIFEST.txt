
-----<PERSON><PERSON><PERSON> PGP SIGNED MESSAGE-----
Hash: SHA512

{
  "manifestVersion": "2.0.0",
  "signatureType": "grafana",
  "signedByOrg": "grafana",
  "signedByOrgName": "Grafana Labs",
  "plugin": "grafana-worldmap-panel",
  "version": "1.0.6",
  "time": 1686842764822,
  "keyId": "7e4d0c6a708866e7",
  "files": {
    "CHANGELOG.md": "4905ac143debd821476df3fa0b8d008bfe3b7898080be6aa5d35d0ad0a6ce1c3",
    "LICENSE": "b46400c1ab5630edeb4e43b240a027d203375ebe5a9efb7de88c604c5c5cace0",
    "README.md": "afebc0832de8f404b3ab5452ad335b64c5accaa671c7245c8226089004dc203c",
    "dark.js": "e2f77e18dfe225538e2d4ac1356f562c82699c14347df7f755efe80073f6bb4b",
    "dark.js.map": "0de83e40efa795707916586de9a53e9462cfa48465eff286946b6becebdf3225",
    "data/countries.json": "3cc16444bc2120d1800d24ab552c8d5f42ea0cd7a8699a915f979586f7c4cb50",
    "data/countries_3letter.json": "cd87072b672087dfe8ab304b649109e42bb2d28a68860385fabeb083299177da",
    "data/probes.json": "105af94868db132e430499a43db868a13148506306234f9b0ba514e7df5ee9a2",
    "data/states.json": "460de2fa8bc1b6cca6f76ab72b172f37a54ffa03a2dbc0e5da7d42336884a4d9",
    "images/countries-option.png": "29b04d97b2d426605828206a8fae7eda349d59ee9c372669036db66cee009e2e",
    "images/elasticsearch-query.png": "d6fa38d8d97630afc277382267e4e5b6eef9c221f60ebcc8b1b4e1478bb4949a",
    "images/es-options.png": "8c6514bd386089dfdbafda0d35e7fb98bdad1cc4d65bb18d9057e83b4f39bb35",
    "images/graphite-consolidateby.png": "479a60efb178c6c3b0a22eddbb974b25555765e38f12b43d870d0c39853d79b1",
    "images/graphite-maxdatapoints.png": "7087c30c4c432ffa706f48d03e6acf513c7d12783bad2ab2942b22b1c5a3651c",
    "images/influx-query.png": "3d360abd6e961091a0f35a0c6cfda2ce323e249a9a616cb7f70f40cadec49d2f",
    "images/json-endpoint.png": "111a1d4473d8e586498b5d3d1ea6ae4ca7bd935f3f31f47158e1ffadc47408de",
    "images/jsonp-endpoint.png": "9ef127439e65371ea6d00824e46b43e5ba62f85c171551e96021a005939797d7",
    "images/layers-2x.png": "1f50e0ee5409b512cce602dcd1a81e2a9386c561421c6c58b1527532b7e9c659",
    "images/layers.png": "cb07b394e0e448cce75ecca76d5fbe8f1aee0c3f5333ca1520a3368c68c80dc4",
    "images/marker-icon-2x.png": "6b3997d367766d63858f95db5167d28b4c9c80e8a1db0afc0846ad2b0c5d5da6",
    "images/marker-icon.png": "ff44ffda09f3bd1ed45918e7a7af933832e0aaa71fdcc63b81bf78977b4f086b",
    "images/marker-shadow.png": "8f6b92aef1ea7fbd843150ca25a18c967977108b0ec58df98dbd3cae4286aad7",
    "images/worldmap-geohash-query.png": "143b255384f2b2de28b340b8b53f4bc147677f68b8b1595e9ef8853ceb2a373b",
    "images/worldmap-light-theme.png": "5632a67982b31d412a7b13420d7e3165d4fc577ee2c6d0f9327fd1a2f5d1cdb4",
    "images/worldmap-timeseries-query.png": "ad7289db533b7d6345cb9d0e34081ce124a8a799c277a4cf6513dfbce3f23f79",
    "images/worldmap-usa.png": "4a1b437f93e58391993c546a9f1263b447cd051da3044fa3ff9af18f7c6d7a03",
    "images/worldmap-world.png": "0872053149ee496349d8049e40a58dea39b2a0ce81f174ab44962d7e8640ec23",
    "images/worldmap_logo.svg": "a4b0c2dfd94b48289d1e8f37b5eb324ecede9e88f6ef1027f9778f35064f936e",
    "libs/leaflet.js": "19163b5e01993736bd14d8b5cd5083181dc954350a8e2b542fc48f0c2b4744f4",
    "light.js": "aba5a0ce8aedaf5c34f4a8e4e130028ec3c0e75c0e0cf54c09bfecab5d0a82bc",
    "light.js.map": "6081e8a6e7199b9c41efd387066bfe9c8a56a3e6192889a5acdf079fa0d88ec4",
    "module.js": "e38a75dce09e6a355438a1d895e2f6add64722395343bd0dd6305d7fcedd3c6b",
    "module.js.LICENSE.txt": "423921440f7f7435bb46e8c0dc3e88b6bba2346ec5c50d90689054ba93826e44",
    "module.js.map": "91be5a2e2dcb69c5631d5745b84d9a81eed18afc94045de49eb829bad749a2f7",
    "partials/editor.html": "bc85b0e9b3aca46b9855084cbdff32fd11baac729b77c0122840451fc8939b0a",
    "partials/module.html": "3b9506b70ef254f5c617bd78957a05972939085ea8a06206eeb68deef1adbe7a",
    "plugin.json": "09dbe624618b0dd4c7f44d0fe8c38fffb1beb5dbc77ca6d05ebf4177a31e0cbb",
    "styles/dark.css": "a5f1a67134d4da0a3af0f717006eed620e291bda7e604437a91a4e4bd07c0767",
    "styles/light.css": "3739bb10a26857182578c280da1a2fca439ba05a932bc9c523cca557ee373184"
  }
}
-----BEGIN PGP SIGNATURE-----
Version: OpenPGP.js v4.10.10
Comment: https://openpgpjs.org

wrkEARMKAAYFAmSLLY0AIQkQfk0ManCIZucWIQTzOyW2kQdOhGNlcPN+TQxq
cIhm5wdTAgkBt0w/mq/yF1VE7vPPGiEYOnyA9urfVKk+2g7GyS//s///f4e4
Z8UPh2iBIcJicaWLhpbzczw7H3vsz1hbSgC5p44CCQFZSpXd80UcB4H2pXoF
HmW2aBmuLMzS4D8/pWflCW3dk8osqtTi+dp0gWogLeq2ULkmlbN0p8Up/Lee
VGazBiESXg==
=8zDQ
-----END PGP SIGNATURE-----
