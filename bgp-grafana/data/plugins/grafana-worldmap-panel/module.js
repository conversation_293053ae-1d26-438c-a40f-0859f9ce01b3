/*! For license information please see module.js.LICENSE.txt */
define(["lodash","@grafana/data","app/plugins/sdk","app/core/utils/kbn","jquery","app/core/config","app/core/time_series2"],(function(t,e,i,o,n,a,r){return function(t){var e={};function i(o){if(e[o])return e[o].exports;var n=e[o]={i:o,l:!1,exports:{}};return t[o].call(n.exports,n,n.exports,i),n.l=!0,n.exports}return i.m=t,i.c=e,i.d=function(t,e,o){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(i.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)i.d(o,n,function(e){return t[e]}.bind(null,n));return o},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="/",i(i.s=21)}([function(e,i){e.exports=t},function(t,i){t.exports=e},function(t,e){t.exports=i},function(t,e){t.exports=o},function(t,e){t.exports=n},function(t,e,i){"use strict";var o,n=function(){return void 0===o&&(o=Boolean(window&&document&&document.all&&!window.atob)),o},a=function(){var t={};return function(e){if(void 0===t[e]){var i=document.querySelector(e);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(t){i=null}t[e]=i}return t[e]}}(),r=[];function s(t){for(var e=-1,i=0;i<r.length;i++)if(r[i].identifier===t){e=i;break}return e}function l(t,e){for(var i={},o=[],n=0;n<t.length;n++){var a=t[n],l=e.base?a[0]+e.base:a[0],h=i[l]||0,u="".concat(l," ").concat(h);i[l]=h+1;var c=s(u),d={css:a[1],media:a[2],sourceMap:a[3]};-1!==c?(r[c].references++,r[c].updater(d)):r.push({identifier:u,updater:_(d,e),references:1}),o.push(u)}return o}function h(t){var e=document.createElement("style"),o=t.attributes||{};if(void 0===o.nonce){var n=i.nc;n&&(o.nonce=n)}if(Object.keys(o).forEach((function(t){e.setAttribute(t,o[t])})),"function"==typeof t.insert)t.insert(e);else{var r=a(t.insert||"head");if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(e)}return e}var u,c=(u=[],function(t,e){return u[t]=e,u.filter(Boolean).join("\n")});function d(t,e,i,o){var n=i?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(t.styleSheet)t.styleSheet.cssText=c(e,n);else{var a=document.createTextNode(n),r=t.childNodes;r[e]&&t.removeChild(r[e]),r.length?t.insertBefore(a,r[e]):t.appendChild(a)}}function p(t,e,i){var o=i.css,n=i.media,a=i.sourceMap;if(n?t.setAttribute("media",n):t.removeAttribute("media"),a&&btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),t.styleSheet)t.styleSheet.cssText=o;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(o))}}var f=null,m=0;function _(t,e){var i,o,n;if(e.singleton){var a=m++;i=f||(f=h(e)),o=d.bind(null,i,a,!1),n=d.bind(null,i,a,!0)}else i=h(e),o=p.bind(null,i,e),n=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(i)};return o(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;o(t=e)}else n()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=n());var i=l(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var o=0;o<i.length;o++){var n=s(i[o]);r[n].references--}for(var a=l(t,e),h=0;h<i.length;h++){var u=s(i[h]);0===r[u].references&&(r[u].updater(),r.splice(u,1))}i=a}}}},function(t,e,i){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var i=function(t,e){var i=t[1]||"",o=t[3];if(!o)return i;if(e&&"function"==typeof btoa){var n=(r=o,s=btoa(unescape(encodeURIComponent(JSON.stringify(r)))),l="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(s),"/*# ".concat(l," */")),a=o.sources.map((function(t){return"/*# sourceURL=".concat(o.sourceRoot||"").concat(t," */")}));return[i].concat(a).concat([n]).join("\n")}var r,s,l;return[i].join("\n")}(e,t);return e[2]?"@media ".concat(e[2]," {").concat(i,"}"):i})).join("")},e.i=function(t,i,o){"string"==typeof t&&(t=[[null,t,""]]);var n={};if(o)for(var a=0;a<this.length;a++){var r=this[a][0];null!=r&&(n[r]=!0)}for(var s=0;s<t.length;s++){var l=[].concat(t[s]);o&&n[l[0]]||(i&&(l[2]?l[2]="".concat(i," and ").concat(l[2]):l[2]=i),e.push(l))}},e}},function(t,e){t.exports=a},function(t,e){t.exports=r},function(t,e,i){var o,n,a,r;function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}window,r=function(t){"use strict";var e=Object.freeze;function i(t){var e,i,o,n;for(i=1,o=arguments.length;i<o;i++)for(e in n=arguments[i])t[e]=n[e];return t}Object.freeze=function(t){return t};var o=Object.create||function(){function t(){}return function(e){return t.prototype=e,new t}}();function n(t,e){var i=Array.prototype.slice;if(t.bind)return t.bind.apply(t,i.call(arguments,1));var o=i.call(arguments,2);return function(){return t.apply(e,o.length?o.concat(i.call(arguments)):arguments)}}var a=0;function r(t){return t._leaflet_id=t._leaflet_id||++a,t._leaflet_id}function l(t,e,i){var o,n,a,r;return r=function(){o=!1,n&&(a.apply(i,n),n=!1)},a=function(){o?n=arguments:(t.apply(i,arguments),setTimeout(r,e),o=!0)}}function h(t,e,i){var o=e[1],n=e[0],a=o-n;return t===o&&i?t:((t-n)%a+a)%a+n}function u(){return!1}function c(t,e){var i=Math.pow(10,void 0===e?6:e);return Math.round(t*i)/i}function d(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function p(t){return d(t).split(/\s+/)}function f(t,e){for(var i in t.hasOwnProperty("options")||(t.options=t.options?o(t.options):{}),e)t.options[i]=e[i];return t.options}function m(t,e,i){var o=[];for(var n in t)o.push(encodeURIComponent(i?n.toUpperCase():n)+"="+encodeURIComponent(t[n]));return(e&&-1!==e.indexOf("?")?"&":"?")+o.join("&")}var _=/\{ *([\w_-]+) *\}/g;function g(t,e){return t.replace(_,(function(t,i){var o=e[i];if(void 0===o)throw new Error("No value provided for variable "+t);return"function"==typeof o&&(o=o(e)),o}))}var v=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function A(t,e){for(var i=0;i<t.length;i++)if(t[i]===e)return i;return-1}var y="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function b(t){return window["webkit"+t]||window["moz"+t]||window["ms"+t]}var x=0;function C(t){var e=+new Date,i=Math.max(0,16-(e-x));return x=e+i,window.setTimeout(t,i)}var w=window.requestAnimationFrame||b("RequestAnimationFrame")||C,B=window.cancelAnimationFrame||b("CancelAnimationFrame")||b("CancelRequestAnimationFrame")||function(t){window.clearTimeout(t)};function P(t,e,i){if(!i||w!==C)return w.call(window,n(t,e));t.call(e)}function M(t){t&&B.call(window,t)}var T=(Object.freeze||Object)({freeze:e,extend:i,create:o,bind:n,lastId:a,stamp:r,throttle:l,wrapNum:h,falseFn:u,formatNum:c,trim:d,splitWords:p,setOptions:f,getParamString:m,template:g,isArray:v,indexOf:A,emptyImageUrl:y,requestFn:w,cancelFn:B,requestAnimFrame:P,cancelAnimFrame:M});function k(){}k.extend=function(t){var e=function(){this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},n=e.__super__=this.prototype,a=o(n);for(var r in a.constructor=e,e.prototype=a,this)this.hasOwnProperty(r)&&"prototype"!==r&&"__super__"!==r&&(e[r]=this[r]);return t.statics&&(i(e,t.statics),delete t.statics),t.includes&&(function(t){if("undefined"!=typeof L&&L&&L.Mixin){t=v(t)?t:[t];for(var e=0;e<t.length;e++)t[e],L.Mixin.Events}}(t.includes),i.apply(null,[a].concat(t.includes)),delete t.includes),a.options&&(t.options=i(o(a.options),t.options)),i(a,t),a._initHooks=[],a.callInitHooks=function(){if(!this._initHooksCalled){n.callInitHooks&&n.callInitHooks.call(this),this._initHooksCalled=!0;for(var t=0,e=a._initHooks.length;t<e;t++)a._initHooks[t].call(this)}},e},k.include=function(t){return i(this.prototype,t),this},k.mergeOptions=function(t){return i(this.prototype.options,t),this},k.addInitHook=function(t){var e=Array.prototype.slice.call(arguments,1),i="function"==typeof t?t:function(){this[t].apply(this,e)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(i),this};var z={on:function(t,e,i){if("object"===s(t))for(var o in t)this._on(o,t[o],e);else for(var n=0,a=(t=p(t)).length;n<a;n++)this._on(t[n],e,i);return this},off:function(t,e,i){if(t)if("object"===s(t))for(var o in t)this._off(o,t[o],e);else for(var n=0,a=(t=p(t)).length;n<a;n++)this._off(t[n],e,i);else delete this._events;return this},_on:function(t,e,i){this._events=this._events||{};var o=this._events[t];o||(o=[],this._events[t]=o),i===this&&(i=void 0);for(var n={fn:e,ctx:i},a=o,r=0,s=a.length;r<s;r++)if(a[r].fn===e&&a[r].ctx===i)return;a.push(n)},_off:function(t,e,i){var o,n,a;if(this._events&&(o=this._events[t]))if(e){if(i===this&&(i=void 0),o)for(n=0,a=o.length;n<a;n++){var r=o[n];if(r.ctx===i&&r.fn===e)return r.fn=u,this._firingCount&&(this._events[t]=o=o.slice()),void o.splice(n,1)}}else{for(n=0,a=o.length;n<a;n++)o[n].fn=u;delete this._events[t]}},fire:function(t,e,o){if(!this.listens(t,o))return this;var n=i({},e,{type:t,target:this,sourceTarget:e&&e.sourceTarget||this});if(this._events){var a=this._events[t];if(a){this._firingCount=this._firingCount+1||1;for(var r=0,s=a.length;r<s;r++){var l=a[r];l.fn.call(l.ctx||this,n)}this._firingCount--}}return o&&this._propagateEvent(n),this},listens:function(t,e){var i=this._events&&this._events[t];if(i&&i.length)return!0;if(e)for(var o in this._eventParents)if(this._eventParents[o].listens(t,e))return!0;return!1},once:function(t,e,i){if("object"===s(t)){for(var o in t)this.once(o,t[o],e);return this}var a=n((function(){this.off(t,e,i).off(t,a,i)}),this);return this.on(t,e,i).on(t,a,i)},addEventParent:function(t){return this._eventParents=this._eventParents||{},this._eventParents[r(t)]=t,this},removeEventParent:function(t){return this._eventParents&&delete this._eventParents[r(t)],this},_propagateEvent:function(t){for(var e in this._eventParents)this._eventParents[e].fire(t.type,i({layer:t.target,propagatedFrom:t.target},t),!0)}};z.addEventListener=z.on,z.removeEventListener=z.clearAllEventListeners=z.off,z.addOneTimeEventListener=z.once,z.fireEvent=z.fire,z.hasEventListeners=z.listens;var S=k.extend(z);function E(t,e,i){this.x=i?Math.round(t):t,this.y=i?Math.round(e):e}var Z=Math.trunc||function(t){return t>0?Math.floor(t):Math.ceil(t)};function O(t,e,i){return t instanceof E?t:v(t)?new E(t[0],t[1]):null==t?t:"object"===s(t)&&"x"in t&&"y"in t?new E(t.x,t.y):new E(t,e,i)}function I(t,e){if(t)for(var i=e?[t,e]:t,o=0,n=i.length;o<n;o++)this.extend(i[o])}function D(t,e){return!t||t instanceof I?t:new I(t,e)}function R(t,e){if(t)for(var i=e?[t,e]:t,o=0,n=i.length;o<n;o++)this.extend(i[o])}function N(t,e){return t instanceof R?t:new R(t,e)}function j(t,e,i){if(isNaN(t)||isNaN(e))throw new Error("Invalid LatLng object: ("+t+", "+e+")");this.lat=+t,this.lng=+e,void 0!==i&&(this.alt=+i)}function W(t,e,i){return t instanceof j?t:v(t)&&"object"!==s(t[0])?3===t.length?new j(t[0],t[1],t[2]):2===t.length?new j(t[0],t[1]):null:null==t?t:"object"===s(t)&&"lat"in t?new j(t.lat,"lng"in t?t.lng:t.lon,t.alt):void 0===e?null:new j(t,e,i)}E.prototype={clone:function(){return new E(this.x,this.y)},add:function(t){return this.clone()._add(O(t))},_add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.clone()._subtract(O(t))},_subtract:function(t){return this.x-=t.x,this.y-=t.y,this},divideBy:function(t){return this.clone()._divideBy(t)},_divideBy:function(t){return this.x/=t,this.y/=t,this},multiplyBy:function(t){return this.clone()._multiplyBy(t)},_multiplyBy:function(t){return this.x*=t,this.y*=t,this},scaleBy:function(t){return new E(this.x*t.x,this.y*t.y)},unscaleBy:function(t){return new E(this.x/t.x,this.y/t.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=Z(this.x),this.y=Z(this.y),this},distanceTo:function(t){var e=(t=O(t)).x-this.x,i=t.y-this.y;return Math.sqrt(e*e+i*i)},equals:function(t){return(t=O(t)).x===this.x&&t.y===this.y},contains:function(t){return t=O(t),Math.abs(t.x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)},toString:function(){return"Point("+c(this.x)+", "+c(this.y)+")"}},I.prototype={extend:function(t){return t=O(t),this.min||this.max?(this.min.x=Math.min(t.x,this.min.x),this.max.x=Math.max(t.x,this.max.x),this.min.y=Math.min(t.y,this.min.y),this.max.y=Math.max(t.y,this.max.y)):(this.min=t.clone(),this.max=t.clone()),this},getCenter:function(t){return new E((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)},getBottomLeft:function(){return new E(this.min.x,this.max.y)},getTopRight:function(){return new E(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(t){var e,i;return(t="number"==typeof t[0]||t instanceof E?O(t):D(t))instanceof I?(e=t.min,i=t.max):e=i=t,e.x>=this.min.x&&i.x<=this.max.x&&e.y>=this.min.y&&i.y<=this.max.y},intersects:function(t){t=D(t);var e=this.min,i=this.max,o=t.min,n=t.max,a=n.x>=e.x&&o.x<=i.x,r=n.y>=e.y&&o.y<=i.y;return a&&r},overlaps:function(t){t=D(t);var e=this.min,i=this.max,o=t.min,n=t.max,a=n.x>e.x&&o.x<i.x,r=n.y>e.y&&o.y<i.y;return a&&r},isValid:function(){return!(!this.min||!this.max)}},R.prototype={extend:function(t){var e,i,o=this._southWest,n=this._northEast;if(t instanceof j)e=t,i=t;else{if(!(t instanceof R))return t?this.extend(W(t)||N(t)):this;if(e=t._southWest,i=t._northEast,!e||!i)return this}return o||n?(o.lat=Math.min(e.lat,o.lat),o.lng=Math.min(e.lng,o.lng),n.lat=Math.max(i.lat,n.lat),n.lng=Math.max(i.lng,n.lng)):(this._southWest=new j(e.lat,e.lng),this._northEast=new j(i.lat,i.lng)),this},pad:function(t){var e=this._southWest,i=this._northEast,o=Math.abs(e.lat-i.lat)*t,n=Math.abs(e.lng-i.lng)*t;return new R(new j(e.lat-o,e.lng-n),new j(i.lat+o,i.lng+n))},getCenter:function(){return new j((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new j(this.getNorth(),this.getWest())},getSouthEast:function(){return new j(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(t){t="number"==typeof t[0]||t instanceof j||"lat"in t?W(t):N(t);var e,i,o=this._southWest,n=this._northEast;return t instanceof R?(e=t.getSouthWest(),i=t.getNorthEast()):e=i=t,e.lat>=o.lat&&i.lat<=n.lat&&e.lng>=o.lng&&i.lng<=n.lng},intersects:function(t){t=N(t);var e=this._southWest,i=this._northEast,o=t.getSouthWest(),n=t.getNorthEast(),a=n.lat>=e.lat&&o.lat<=i.lat,r=n.lng>=e.lng&&o.lng<=i.lng;return a&&r},overlaps:function(t){t=N(t);var e=this._southWest,i=this._northEast,o=t.getSouthWest(),n=t.getNorthEast(),a=n.lat>e.lat&&o.lat<i.lat,r=n.lng>e.lng&&o.lng<i.lng;return a&&r},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(t,e){return!!t&&(t=N(t),this._southWest.equals(t.getSouthWest(),e)&&this._northEast.equals(t.getNorthEast(),e))},isValid:function(){return!(!this._southWest||!this._northEast)}},j.prototype={equals:function(t,e){return!!t&&(t=W(t),Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng))<=(void 0===e?1e-9:e))},toString:function(t){return"LatLng("+c(this.lat,t)+", "+c(this.lng,t)+")"},distanceTo:function(t){return F.distance(this,W(t))},wrap:function(){return F.wrapLatLng(this)},toBounds:function(t){var e=180*t/40075017,i=e/Math.cos(Math.PI/180*this.lat);return N([this.lat-e,this.lng-i],[this.lat+e,this.lng+i])},clone:function(){return new j(this.lat,this.lng,this.alt)}};var U,H={latLngToPoint:function(t,e){var i=this.projection.project(t),o=this.scale(e);return this.transformation._transform(i,o)},pointToLatLng:function(t,e){var i=this.scale(e),o=this.transformation.untransform(t,i);return this.projection.unproject(o)},project:function(t){return this.projection.project(t)},unproject:function(t){return this.projection.unproject(t)},scale:function(t){return 256*Math.pow(2,t)},zoom:function(t){return Math.log(t/256)/Math.LN2},getProjectedBounds:function(t){if(this.infinite)return null;var e=this.projection.bounds,i=this.scale(t);return new I(this.transformation.transform(e.min,i),this.transformation.transform(e.max,i))},infinite:!1,wrapLatLng:function(t){var e=this.wrapLng?h(t.lng,this.wrapLng,!0):t.lng;return new j(this.wrapLat?h(t.lat,this.wrapLat,!0):t.lat,e,t.alt)},wrapLatLngBounds:function(t){var e=t.getCenter(),i=this.wrapLatLng(e),o=e.lat-i.lat,n=e.lng-i.lng;if(0===o&&0===n)return t;var a=t.getSouthWest(),r=t.getNorthEast();return new R(new j(a.lat-o,a.lng-n),new j(r.lat-o,r.lng-n))}},F=i({},H,{wrapLng:[-180,180],R:6371e3,distance:function(t,e){var i=Math.PI/180,o=t.lat*i,n=e.lat*i,a=Math.sin((e.lat-t.lat)*i/2),r=Math.sin((e.lng-t.lng)*i/2),s=a*a+Math.cos(o)*Math.cos(n)*r*r,l=2*Math.atan2(Math.sqrt(s),Math.sqrt(1-s));return this.R*l}}),q={R:6378137,MAX_LATITUDE:85.0511287798,project:function(t){var e=Math.PI/180,i=this.MAX_LATITUDE,o=Math.max(Math.min(i,t.lat),-i),n=Math.sin(o*e);return new E(this.R*t.lng*e,this.R*Math.log((1+n)/(1-n))/2)},unproject:function(t){var e=180/Math.PI;return new j((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*e,t.x*e/this.R)},bounds:(U=6378137*Math.PI,new I([-U,-U],[U,U]))};function V(t,e,i,o){if(v(t))return this._a=t[0],this._b=t[1],this._c=t[2],void(this._d=t[3]);this._a=t,this._b=e,this._c=i,this._d=o}function G(t,e,i,o){return new V(t,e,i,o)}V.prototype={transform:function(t,e){return this._transform(t.clone(),e)},_transform:function(t,e){return e=e||1,t.x=e*(this._a*t.x+this._b),t.y=e*(this._c*t.y+this._d),t},untransform:function(t,e){return e=e||1,new E((t.x/e-this._b)/this._a,(t.y/e-this._d)/this._c)}};var K=i({},F,{code:"EPSG:3857",projection:q,transformation:function(){var t=.5/(Math.PI*q.R);return G(t,.5,-t,.5)}()}),Y=i({},K,{code:"EPSG:900913"});function Q(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function X(t,e){var i,o,n,a,r,s,l="";for(i=0,n=t.length;i<n;i++){for(o=0,a=(r=t[i]).length;o<a;o++)l+=(o?"L":"M")+(s=r[o]).x+" "+s.y;l+=e?Mt?"z":"x":""}return l||"M0 0"}var J=document.documentElement.style,$="ActiveXObject"in window,tt=$&&!document.addEventListener,et="msLaunchUri"in navigator&&!("documentMode"in document),it=kt("webkit"),ot=kt("android"),nt=kt("android 2")||kt("android 3"),at=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),rt=ot&&kt("Google")&&at<537&&!("AudioNode"in window),st=!!window.opera,lt=kt("chrome"),ht=kt("gecko")&&!it&&!st&&!$,ut=!lt&&kt("safari"),ct=kt("phantom"),dt="OTransition"in J,pt=0===navigator.platform.indexOf("Win"),ft=$&&"transition"in J,mt="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!nt,_t="MozPerspective"in J,gt=!window.L_DISABLE_3D&&(ft||mt||_t)&&!dt&&!ct,vt="undefined"!=typeof orientation||kt("mobile"),At=vt&&it,yt=vt&&mt,bt=!window.PointerEvent&&window.MSPointerEvent,xt=!(!window.PointerEvent&&!bt),Ct=!window.L_NO_TOUCH&&(xt||"ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch),wt=vt&&st,Lt=vt&&ht,Bt=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,Pt=!!document.createElement("canvas").getContext,Mt=!(!document.createElementNS||!Q("svg").createSVGRect),Tt=!Mt&&function(){try{var t=document.createElement("div");t.innerHTML='<v:shape adj="1"/>';var e=t.firstChild;return e.style.behavior="url(#default#VML)",e&&"object"===s(e.adj)}catch(t){return!1}}();function kt(t){return navigator.userAgent.toLowerCase().indexOf(t)>=0}var zt=(Object.freeze||Object)({ie:$,ielt9:tt,edge:et,webkit:it,android:ot,android23:nt,androidStock:rt,opera:st,chrome:lt,gecko:ht,safari:ut,phantom:ct,opera12:dt,win:pt,ie3d:ft,webkit3d:mt,gecko3d:_t,any3d:gt,mobile:vt,mobileWebkit:At,mobileWebkit3d:yt,msPointer:bt,pointer:xt,touch:Ct,mobileOpera:wt,mobileGecko:Lt,retina:Bt,canvas:Pt,svg:Mt,vml:Tt}),St=bt?"MSPointerDown":"pointerdown",Et=bt?"MSPointerMove":"pointermove",Zt=bt?"MSPointerUp":"pointerup",Ot=bt?"MSPointerCancel":"pointercancel",It=["INPUT","SELECT","OPTION"],Dt={},Rt=!1,Nt=0;function jt(t,e,i,o){return"touchstart"===e?function(t,e,i){var o=n((function(t){if("mouse"!==t.pointerType&&t.MSPOINTER_TYPE_MOUSE&&t.pointerType!==t.MSPOINTER_TYPE_MOUSE){if(!(It.indexOf(t.target.tagName)<0))return;ne(t)}Ft(t,e)}));t["_leaflet_touchstart"+i]=o,t.addEventListener(St,o,!1),Rt||(document.documentElement.addEventListener(St,Wt,!0),document.documentElement.addEventListener(Et,Ut,!0),document.documentElement.addEventListener(Zt,Ht,!0),document.documentElement.addEventListener(Ot,Ht,!0),Rt=!0)}(t,i,o):"touchmove"===e?function(t,e,i){var o=function(t){(t.pointerType!==t.MSPOINTER_TYPE_MOUSE&&"mouse"!==t.pointerType||0!==t.buttons)&&Ft(t,e)};t["_leaflet_touchmove"+i]=o,t.addEventListener(Et,o,!1)}(t,i,o):"touchend"===e&&function(t,e,i){var o=function(t){Ft(t,e)};t["_leaflet_touchend"+i]=o,t.addEventListener(Zt,o,!1),t.addEventListener(Ot,o,!1)}(t,i,o),this}function Wt(t){Dt[t.pointerId]=t,Nt++}function Ut(t){Dt[t.pointerId]&&(Dt[t.pointerId]=t)}function Ht(t){delete Dt[t.pointerId],Nt--}function Ft(t,e){for(var i in t.touches=[],Dt)t.touches.push(Dt[i]);t.changedTouches=[t],e(t)}var qt=bt?"MSPointerDown":xt?"pointerdown":"touchstart",Vt=bt?"MSPointerUp":xt?"pointerup":"touchend",Gt="_leaflet_";function Kt(t,e,i){var o,n,a=!1;function r(t){var e;if(xt){if(!et||"mouse"===t.pointerType)return;e=Nt}else e=t.touches.length;if(!(e>1)){var i=Date.now(),r=i-(o||i);n=t.touches?t.touches[0]:t,a=r>0&&r<=250,o=i}}function s(t){if(a&&!n.cancelBubble){if(xt){if(!et||"mouse"===t.pointerType)return;var i,r,s={};for(r in n)i=n[r],s[r]=i&&i.bind?i.bind(n):i;n=s}n.type="dblclick",e(n),o=null}}return t[Gt+qt+i]=r,t[Gt+Vt+i]=s,t[Gt+"dblclick"+i]=e,t.addEventListener(qt,r,!1),t.addEventListener(Vt,s,!1),t.addEventListener("dblclick",e,!1),this}function Yt(t,e){var i=t[Gt+qt+e],o=t[Gt+Vt+e],n=t[Gt+"dblclick"+e];return t.removeEventListener(qt,i,!1),t.removeEventListener(Vt,o,!1),et||t.removeEventListener("dblclick",n,!1),this}function Qt(t,e,i,o){if("object"===s(e))for(var n in e)$t(t,n,e[n],i);else for(var a=0,r=(e=p(e)).length;a<r;a++)$t(t,e[a],i,o);return this}var Xt="_leaflet_events";function Jt(t,e,i,o){if("object"===s(e))for(var n in e)te(t,n,e[n],i);else if(e)for(var a=0,r=(e=p(e)).length;a<r;a++)te(t,e[a],i,o);else{for(var l in t[Xt])te(t,l,t[Xt][l]);delete t[Xt]}return this}function $t(t,e,i,o){var n=e+r(i)+(o?"_"+r(o):"");if(t[Xt]&&t[Xt][n])return this;var a=function(e){return i.call(o||t,e||window.event)},s=a;xt&&0===e.indexOf("touch")?jt(t,e,a,n):!Ct||"dblclick"!==e||!Kt||xt&&lt?"addEventListener"in t?"mousewheel"===e?t.addEventListener("onwheel"in t?"wheel":"mousewheel",a,!1):"mouseenter"===e||"mouseleave"===e?(a=function(e){e=e||window.event,pe(t,e)&&s(e)},t.addEventListener("mouseenter"===e?"mouseover":"mouseout",a,!1)):("click"===e&&ot&&(a=function(t){!function(t,e){var i=t.timeStamp||t.originalEvent&&t.originalEvent.timeStamp,o=he&&i-he;o&&o>100&&o<500||t.target._simulatedClick&&!t._simulated?ae(t):(he=i,e(t))}(t,s)}),t.addEventListener(e,a,!1)):"attachEvent"in t&&t.attachEvent("on"+e,a):Kt(t,a,n),t[Xt]=t[Xt]||{},t[Xt][n]=a}function te(t,e,i,o){var n=e+r(i)+(o?"_"+r(o):""),a=t[Xt]&&t[Xt][n];if(!a)return this;xt&&0===e.indexOf("touch")?function(t,e,i){var o=t["_leaflet_"+e+i];"touchstart"===e?t.removeEventListener(St,o,!1):"touchmove"===e?t.removeEventListener(Et,o,!1):"touchend"===e&&(t.removeEventListener(Zt,o,!1),t.removeEventListener(Ot,o,!1))}(t,e,n):!Ct||"dblclick"!==e||!Yt||xt&&lt?"removeEventListener"in t?"mousewheel"===e?t.removeEventListener("onwheel"in t?"wheel":"mousewheel",a,!1):t.removeEventListener("mouseenter"===e?"mouseover":"mouseleave"===e?"mouseout":e,a,!1):"detachEvent"in t&&t.detachEvent("on"+e,a):Yt(t,n),t[Xt][n]=null}function ee(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,de(t),this}function ie(t){return $t(t,"mousewheel",ee),this}function oe(t){return Qt(t,"mousedown touchstart dblclick",ee),$t(t,"click",ce),this}function ne(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function ae(t){return ne(t),ee(t),this}function re(t,e){if(!e)return new E(t.clientX,t.clientY);var i=e.getBoundingClientRect(),o=i.width/e.offsetWidth||1,n=i.height/e.offsetHeight||1;return new E(t.clientX/o-i.left-e.clientLeft,t.clientY/n-i.top-e.clientTop)}var se=pt&&lt?2*window.devicePixelRatio:ht?window.devicePixelRatio:1;function le(t){return et?t.wheelDeltaY/2:t.deltaY&&0===t.deltaMode?-t.deltaY/se:t.deltaY&&1===t.deltaMode?20*-t.deltaY:t.deltaY&&2===t.deltaMode?60*-t.deltaY:t.deltaX||t.deltaZ?0:t.wheelDelta?(t.wheelDeltaY||t.wheelDelta)/2:t.detail&&Math.abs(t.detail)<32765?20*-t.detail:t.detail?t.detail/-32765*60:0}var he,ue={};function ce(t){ue[t.type]=!0}function de(t){var e=ue[t.type];return ue[t.type]=!1,e}function pe(t,e){var i=e.relatedTarget;if(!i)return!0;try{for(;i&&i!==t;)i=i.parentNode}catch(t){return!1}return i!==t}var fe,me,_e,ge,ve,Ae=(Object.freeze||Object)({on:Qt,off:Jt,stopPropagation:ee,disableScrollPropagation:ie,disableClickPropagation:oe,preventDefault:ne,stop:ae,getMousePosition:re,getWheelDelta:le,fakeStop:ce,skipped:de,isExternalTarget:pe,addListener:Qt,removeListener:Jt}),ye=Ie(["transform","WebkitTransform","OTransform","MozTransform","msTransform"]),be=Ie(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),xe="webkitTransition"===be||"OTransition"===be?be+"End":"transitionend";function Ce(t){return"string"==typeof t?document.getElementById(t):t}function we(t,e){var i=t.style[e]||t.currentStyle&&t.currentStyle[e];if((!i||"auto"===i)&&document.defaultView){var o=document.defaultView.getComputedStyle(t,null);i=o?o[e]:null}return"auto"===i?null:i}function Le(t,e,i){var o=document.createElement(t);return o.className=e||"",i&&i.appendChild(o),o}function Be(t){var e=t.parentNode;e&&e.removeChild(t)}function Pe(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function Me(t){var e=t.parentNode;e.lastChild!==t&&e.appendChild(t)}function Te(t){var e=t.parentNode;e.firstChild!==t&&e.insertBefore(t,e.firstChild)}function ke(t,e){if(void 0!==t.classList)return t.classList.contains(e);var i=Ze(t);return i.length>0&&new RegExp("(^|\\s)"+e+"(\\s|$)").test(i)}function ze(t,e){if(void 0!==t.classList)for(var i=p(e),o=0,n=i.length;o<n;o++)t.classList.add(i[o]);else if(!ke(t,e)){var a=Ze(t);Ee(t,(a?a+" ":"")+e)}}function Se(t,e){void 0!==t.classList?t.classList.remove(e):Ee(t,d((" "+Ze(t)+" ").replace(" "+e+" "," ")))}function Ee(t,e){void 0===t.className.baseVal?t.className=e:t.className.baseVal=e}function Ze(t){return void 0===t.className.baseVal?t.className:t.className.baseVal}function Oe(t,e){"opacity"in t.style?t.style.opacity=e:"filter"in t.style&&function(t,e){var i=!1,o="DXImageTransform.Microsoft.Alpha";try{i=t.filters.item(o)}catch(t){if(1===e)return}e=Math.round(100*e),i?(i.Enabled=100!==e,i.Opacity=e):t.style.filter+=" progid:"+o+"(opacity="+e+")"}(t,e)}function Ie(t){for(var e=document.documentElement.style,i=0;i<t.length;i++)if(t[i]in e)return t[i];return!1}function De(t,e,i){var o=e||new E(0,0);t.style[ye]=(ft?"translate("+o.x+"px,"+o.y+"px)":"translate3d("+o.x+"px,"+o.y+"px,0)")+(i?" scale("+i+")":"")}function Re(t,e){t._leaflet_pos=e,gt?De(t,e):(t.style.left=e.x+"px",t.style.top=e.y+"px")}function Ne(t){return t._leaflet_pos||new E(0,0)}if("onselectstart"in document)fe=function(){Qt(window,"selectstart",ne)},me=function(){Jt(window,"selectstart",ne)};else{var je=Ie(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);fe=function(){if(je){var t=document.documentElement.style;_e=t[je],t[je]="none"}},me=function(){je&&(document.documentElement.style[je]=_e,_e=void 0)}}function We(){Qt(window,"dragstart",ne)}function Ue(){Jt(window,"dragstart",ne)}function He(t){for(;-1===t.tabIndex;)t=t.parentNode;t.style&&(Fe(),ge=t,ve=t.style.outline,t.style.outline="none",Qt(window,"keydown",Fe))}function Fe(){ge&&(ge.style.outline=ve,ge=void 0,ve=void 0,Jt(window,"keydown",Fe))}var qe=(Object.freeze||Object)({TRANSFORM:ye,TRANSITION:be,TRANSITION_END:xe,get:Ce,getStyle:we,create:Le,remove:Be,empty:Pe,toFront:Me,toBack:Te,hasClass:ke,addClass:ze,removeClass:Se,setClass:Ee,getClass:Ze,setOpacity:Oe,testProp:Ie,setTransform:De,setPosition:Re,getPosition:Ne,disableTextSelection:fe,enableTextSelection:me,disableImageDrag:We,enableImageDrag:Ue,preventOutline:He,restoreOutline:Fe}),Ve=S.extend({run:function(t,e,i,o){this.stop(),this._el=t,this._inProgress=!0,this._duration=i||.25,this._easeOutPower=1/Math.max(o||.5,.2),this._startPos=Ne(t),this._offset=e.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=P(this._animate,this),this._step()},_step:function(t){var e=+new Date-this._startTime,i=1e3*this._duration;e<i?this._runFrame(this._easeOut(e/i),t):(this._runFrame(1),this._complete())},_runFrame:function(t,e){var i=this._startPos.add(this._offset.multiplyBy(t));e&&i._round(),Re(this._el,i),this.fire("step")},_complete:function(){M(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),Ge=S.extend({options:{crs:K,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(t,e){e=f(this,e),this._initContainer(t),this._initLayout(),this._onResize=n(this._onResize,this),this._initEvents(),e.maxBounds&&this.setMaxBounds(e.maxBounds),void 0!==e.zoom&&(this._zoom=this._limitZoom(e.zoom)),e.center&&void 0!==e.zoom&&this.setView(W(e.center),e.zoom,{reset:!0}),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this.callInitHooks(),this._zoomAnimated=be&&gt&&!wt&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),Qt(this._proxy,xe,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(t,e,o){return e=void 0===e?this._zoom:this._limitZoom(e),t=this._limitCenter(W(t),e,this.options.maxBounds),o=o||{},this._stop(),this._loaded&&!o.reset&&!0!==o&&(void 0!==o.animate&&(o.zoom=i({animate:o.animate},o.zoom),o.pan=i({animate:o.animate,duration:o.duration},o.pan)),this._zoom!==e?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,e,o.zoom):this._tryAnimatedPan(t,o.pan))?(clearTimeout(this._sizeTimer),this):(this._resetView(t,e),this)},setZoom:function(t,e){return this._loaded?this.setView(this.getCenter(),t,{zoom:e}):(this._zoom=t,this)},zoomIn:function(t,e){return t=t||(gt?this.options.zoomDelta:1),this.setZoom(this._zoom+t,e)},zoomOut:function(t,e){return t=t||(gt?this.options.zoomDelta:1),this.setZoom(this._zoom-t,e)},setZoomAround:function(t,e,i){var o=this.getZoomScale(e),n=this.getSize().divideBy(2),a=(t instanceof E?t:this.latLngToContainerPoint(t)).subtract(n).multiplyBy(1-1/o),r=this.containerPointToLatLng(n.add(a));return this.setView(r,e,{zoom:i})},_getBoundsCenterZoom:function(t,e){e=e||{},t=t.getBounds?t.getBounds():N(t);var i=O(e.paddingTopLeft||e.padding||[0,0]),o=O(e.paddingBottomRight||e.padding||[0,0]),n=this.getBoundsZoom(t,!1,i.add(o));if((n="number"==typeof e.maxZoom?Math.min(e.maxZoom,n):n)===1/0)return{center:t.getCenter(),zoom:n};var a=o.subtract(i).divideBy(2),r=this.project(t.getSouthWest(),n),s=this.project(t.getNorthEast(),n);return{center:this.unproject(r.add(s).divideBy(2).add(a),n),zoom:n}},fitBounds:function(t,e){if(!(t=N(t)).isValid())throw new Error("Bounds are not valid.");var i=this._getBoundsCenterZoom(t,e);return this.setView(i.center,i.zoom,e)},fitWorld:function(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo:function(t,e){return this.setView(t,this._zoom,{pan:e})},panBy:function(t,e){if(e=e||{},!(t=O(t).round()).x&&!t.y)return this.fire("moveend");if(!0!==e.animate&&!this.getSize().contains(t))return this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new Ve,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),e.noMoveStart||this.fire("movestart"),!1!==e.animate){ze(this._mapPane,"leaflet-pan-anim");var i=this._getMapPanePos().subtract(t).round();this._panAnim.run(this._mapPane,i,e.duration||.25,e.easeLinearity)}else this._rawPanBy(t),this.fire("move").fire("moveend");return this},flyTo:function(t,e,i){if(!1===(i=i||{}).animate||!gt)return this.setView(t,e,i);this._stop();var o=this.project(this.getCenter()),n=this.project(t),a=this.getSize(),r=this._zoom;t=W(t),e=void 0===e?r:e;var s=Math.max(a.x,a.y),l=s*this.getZoomScale(r,e),h=n.distanceTo(o)||1;function u(t){var e=(l*l-s*s+2.0164*(t?-1:1)*2.0164*h*h)/(2*(t?l:s)*2.0164*h),i=Math.sqrt(e*e+1)-e;return i<1e-9?-18:Math.log(i)}function c(t){return(Math.exp(t)-Math.exp(-t))/2}function d(t){return(Math.exp(t)+Math.exp(-t))/2}var p=u(0);function f(t){return s*(d(p)*(c(e=p+1.42*t)/d(e))-c(p))/2.0164;var e}var m=Date.now(),_=(u(1)-p)/1.42,g=i.duration?1e3*i.duration:1e3*_*.8;return this._moveStart(!0,i.noMoveStart),function i(){var a=(Date.now()-m)/g,l=function(t){return 1-Math.pow(1-t,1.5)}(a)*_;a<=1?(this._flyToFrame=P(i,this),this._move(this.unproject(o.add(n.subtract(o).multiplyBy(f(l)/h)),r),this.getScaleZoom(s/function(t){return s*(d(p)/d(p+1.42*t))}(l),r),{flyTo:!0})):this._move(t,e)._moveEnd(!0)}.call(this),this},flyToBounds:function(t,e){var i=this._getBoundsCenterZoom(t,e);return this.flyTo(i.center,i.zoom,e)},setMaxBounds:function(t){return(t=N(t)).isValid()?(this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this.off("moveend",this._panInsideMaxBounds))},setMinZoom:function(t){var e=this.options.minZoom;return this.options.minZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(t):this},setMaxZoom:function(t){var e=this.options.maxZoom;return this.options.maxZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(t):this},panInsideBounds:function(t,e){this._enforcingBounds=!0;var i=this.getCenter(),o=this._limitCenter(i,this._zoom,N(t));return i.equals(o)||this.panTo(o,e),this._enforcingBounds=!1,this},invalidateSize:function(t){if(!this._loaded)return this;t=i({animate:!1,pan:!0},!0===t?{animate:!0}:t);var e=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var o=this.getSize(),a=e.divideBy(2).round(),r=o.divideBy(2).round(),s=a.subtract(r);return s.x||s.y?(t.animate&&t.pan?this.panBy(s):(t.pan&&this._rawPanBy(s),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(n(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:e,newSize:o})):this},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(t){if(t=this._locateOptions=i({timeout:1e4,watch:!1},t),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var e=n(this._handleGeolocationResponse,this),o=n(this._handleGeolocationError,this);return t.watch?this._locationWatchId=navigator.geolocation.watchPosition(e,o,t):navigator.geolocation.getCurrentPosition(e,o,t),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(t){var e=t.code,i=t.message||(1===e?"permission denied":2===e?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:e,message:"Geolocation error: "+i+"."})},_handleGeolocationResponse:function(t){var e=new j(t.coords.latitude,t.coords.longitude),i=e.toBounds(t.coords.accuracy),o=this._locateOptions;if(o.setView){var n=this.getBoundsZoom(i);this.setView(e,o.maxZoom?Math.min(n,o.maxZoom):n)}var a={latlng:e,bounds:i,timestamp:t.timestamp};for(var r in t.coords)"number"==typeof t.coords[r]&&(a[r]=t.coords[r]);this.fire("locationfound",a)},addHandler:function(t,e){if(!e)return this;var i=this[t]=new e(this);return this._handlers.push(i),this.options[t]&&i.enable(),this},remove:function(){if(this._initEvents(!0),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch(t){this._container._leaflet_id=void 0,this._containerId=void 0}var t;for(t in void 0!==this._locationWatchId&&this.stopLocate(),this._stop(),Be(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._clearHandlers(),this._loaded&&this.fire("unload"),this._layers)this._layers[t].remove();for(t in this._panes)Be(this._panes[t]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(t,e){var i=Le("div","leaflet-pane"+(t?" leaflet-"+t.replace("Pane","")+"-pane":""),e||this._mapPane);return t&&(this._panes[t]=i),i},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter:this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var t=this.getPixelBounds();return new R(this.unproject(t.getBottomLeft()),this.unproject(t.getTopRight()))},getMinZoom:function(){return void 0===this.options.minZoom?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return void 0===this.options.maxZoom?void 0===this._layersMaxZoom?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(t,e,i){t=N(t),i=O(i||[0,0]);var o=this.getZoom()||0,n=this.getMinZoom(),a=this.getMaxZoom(),r=t.getNorthWest(),s=t.getSouthEast(),l=this.getSize().subtract(i),h=D(this.project(s,o),this.project(r,o)).getSize(),u=gt?this.options.zoomSnap:1,c=l.x/h.x,d=l.y/h.y,p=e?Math.max(c,d):Math.min(c,d);return o=this.getScaleZoom(p,o),u&&(o=Math.round(o/(u/100))*(u/100),o=e?Math.ceil(o/u)*u:Math.floor(o/u)*u),Math.max(n,Math.min(a,o))},getSize:function(){return this._size&&!this._sizeChanged||(this._size=new E(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(t,e){var i=this._getTopLeftPoint(t,e);return new I(i,i.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(t){return this.options.crs.getProjectedBounds(void 0===t?this.getZoom():t)},getPane:function(t){return"string"==typeof t?this._panes[t]:t},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(t,e){var i=this.options.crs;return e=void 0===e?this._zoom:e,i.scale(t)/i.scale(e)},getScaleZoom:function(t,e){var i=this.options.crs;e=void 0===e?this._zoom:e;var o=i.zoom(t*i.scale(e));return isNaN(o)?1/0:o},project:function(t,e){return e=void 0===e?this._zoom:e,this.options.crs.latLngToPoint(W(t),e)},unproject:function(t,e){return e=void 0===e?this._zoom:e,this.options.crs.pointToLatLng(O(t),e)},layerPointToLatLng:function(t){var e=O(t).add(this.getPixelOrigin());return this.unproject(e)},latLngToLayerPoint:function(t){return this.project(W(t))._round()._subtract(this.getPixelOrigin())},wrapLatLng:function(t){return this.options.crs.wrapLatLng(W(t))},wrapLatLngBounds:function(t){return this.options.crs.wrapLatLngBounds(N(t))},distance:function(t,e){return this.options.crs.distance(W(t),W(e))},containerPointToLayerPoint:function(t){return O(t).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(t){return O(t).add(this._getMapPanePos())},containerPointToLatLng:function(t){var e=this.containerPointToLayerPoint(O(t));return this.layerPointToLatLng(e)},latLngToContainerPoint:function(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint(W(t)))},mouseEventToContainerPoint:function(t){return re(t,this._container)},mouseEventToLayerPoint:function(t){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))},mouseEventToLatLng:function(t){return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))},_initContainer:function(t){var e=this._container=Ce(t);if(!e)throw new Error("Map container not found.");if(e._leaflet_id)throw new Error("Map container is already initialized.");Qt(e,"scroll",this._onScroll,this),this._containerId=r(e)},_initLayout:function(){var t=this._container;this._fadeAnimated=this.options.fadeAnimation&&gt,ze(t,"leaflet-container"+(Ct?" leaflet-touch":"")+(Bt?" leaflet-retina":"")+(tt?" leaflet-oldie":"")+(ut?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var e=we(t,"position");"absolute"!==e&&"relative"!==e&&"fixed"!==e&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),Re(this._mapPane,new E(0,0)),this.createPane("tilePane"),this.createPane("shadowPane"),this.createPane("overlayPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(ze(t.markerPane,"leaflet-zoom-hide"),ze(t.shadowPane,"leaflet-zoom-hide"))},_resetView:function(t,e){Re(this._mapPane,new E(0,0));var i=!this._loaded;this._loaded=!0,e=this._limitZoom(e),this.fire("viewprereset");var o=this._zoom!==e;this._moveStart(o,!1)._move(t,e)._moveEnd(o),this.fire("viewreset"),i&&this.fire("load")},_moveStart:function(t,e){return t&&this.fire("zoomstart"),e||this.fire("movestart"),this},_move:function(t,e,i){void 0===e&&(e=this._zoom);var o=this._zoom!==e;return this._zoom=e,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),(o||i&&i.pinch)&&this.fire("zoom",i),this.fire("move",i)},_moveEnd:function(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return M(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(t){Re(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(t){this._targets={},this._targets[r(this._container)]=this;var e=t?Jt:Qt;e(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress",this._handleDOMEvent,this),this.options.trackResize&&e(window,"resize",this._onResize,this),gt&&this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){M(this._resizeRequest),this._resizeRequest=P((function(){this.invalidateSize({debounceMoveend:!0})}),this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(t,e){for(var i,o=[],n="mouseout"===e||"mouseover"===e,a=t.target||t.srcElement,s=!1;a;){if((i=this._targets[r(a)])&&("click"===e||"preclick"===e)&&!t._simulated&&this._draggableMoved(i)){s=!0;break}if(i&&i.listens(e,!0)){if(n&&!pe(a,t))break;if(o.push(i),n)break}if(a===this._container)break;a=a.parentNode}return o.length||s||n||!pe(a,t)||(o=[this]),o},_handleDOMEvent:function(t){if(this._loaded&&!de(t)){var e=t.type;"mousedown"!==e&&"keypress"!==e||He(t.target||t.srcElement),this._fireDOMEvent(t,e)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(t,e,o){if("click"===t.type){var n=i({},t);n.type="preclick",this._fireDOMEvent(n,n.type,o)}if(!t._stopped&&(o=(o||[]).concat(this._findEventTargets(t,e))).length){var a=o[0];"contextmenu"===e&&a.listens(e,!0)&&ne(t);var r={originalEvent:t};if("keypress"!==t.type){var s=a.getLatLng&&(!a._radius||a._radius<=10);r.containerPoint=s?this.latLngToContainerPoint(a.getLatLng()):this.mouseEventToContainerPoint(t),r.layerPoint=this.containerPointToLayerPoint(r.containerPoint),r.latlng=s?a.getLatLng():this.layerPointToLatLng(r.layerPoint)}for(var l=0;l<o.length;l++)if(o[l].fire(e,r,!0),r.originalEvent._stopped||!1===o[l].options.bubblingMouseEvents&&-1!==A(this._mouseEvents,e))return}},_draggableMoved:function(t){return(t=t.dragging&&t.dragging.enabled()?t:this).dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var t=0,e=this._handlers.length;t<e;t++)this._handlers[t].disable()},whenReady:function(t,e){return this._loaded?t.call(e||this,{target:this}):this.on("load",t,e),this},_getMapPanePos:function(){return Ne(this._mapPane)||new E(0,0)},_moved:function(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint:function(t,e){return(t&&void 0!==e?this._getNewPixelOrigin(t,e):this.getPixelOrigin()).subtract(this._getMapPanePos())},_getNewPixelOrigin:function(t,e){var i=this.getSize()._divideBy(2);return this.project(t,e)._subtract(i)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(t,e,i){var o=this._getNewPixelOrigin(i,e);return this.project(t,e)._subtract(o)},_latLngBoundsToNewLayerBounds:function(t,e,i){var o=this._getNewPixelOrigin(i,e);return D([this.project(t.getSouthWest(),e)._subtract(o),this.project(t.getNorthWest(),e)._subtract(o),this.project(t.getSouthEast(),e)._subtract(o),this.project(t.getNorthEast(),e)._subtract(o)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter:function(t,e,i){if(!i)return t;var o=this.project(t,e),n=this.getSize().divideBy(2),a=new I(o.subtract(n),o.add(n)),r=this._getBoundsOffset(a,i,e);return r.round().equals([0,0])?t:this.unproject(o.add(r),e)},_limitOffset:function(t,e){if(!e)return t;var i=this.getPixelBounds(),o=new I(i.min.add(t),i.max.add(t));return t.add(this._getBoundsOffset(o,e))},_getBoundsOffset:function(t,e,i){var o=D(this.project(e.getNorthEast(),i),this.project(e.getSouthWest(),i)),n=o.min.subtract(t.min),a=o.max.subtract(t.max);return new E(this._rebound(n.x,-a.x),this._rebound(n.y,-a.y))},_rebound:function(t,e){return t+e>0?Math.round(t-e)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(e))},_limitZoom:function(t){var e=this.getMinZoom(),i=this.getMaxZoom(),o=gt?this.options.zoomSnap:1;return o&&(t=Math.round(t/o)*o),Math.max(e,Math.min(i,t))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){Se(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(t,e){var i=this._getCenterOffset(t)._trunc();return!(!0!==(e&&e.animate)&&!this.getSize().contains(i)||(this.panBy(i,e),0))},_createAnimProxy:function(){var t=this._proxy=Le("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(t),this.on("zoomanim",(function(t){var e=ye,i=this._proxy.style[e];De(this._proxy,this.project(t.center,t.zoom),this.getZoomScale(t.zoom,1)),i===this._proxy.style[e]&&this._animatingZoom&&this._onZoomTransitionEnd()}),this),this.on("load moveend",(function(){var t=this.getCenter(),e=this.getZoom();De(this._proxy,this.project(t,e),this.getZoomScale(e,1))}),this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){Be(this._proxy),delete this._proxy},_catchTransitionEnd:function(t){this._animatingZoom&&t.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(t,e,i){if(this._animatingZoom)return!0;if(i=i||{},!this._zoomAnimated||!1===i.animate||this._nothingToAnimate()||Math.abs(e-this._zoom)>this.options.zoomAnimationThreshold)return!1;var o=this.getZoomScale(e),n=this._getCenterOffset(t)._divideBy(1-1/o);return!(!0!==i.animate&&!this.getSize().contains(n)||(P((function(){this._moveStart(!0,!1)._animateZoom(t,e,!0)}),this),0))},_animateZoom:function(t,e,i,o){this._mapPane&&(i&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=e,ze(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:e,noUpdate:o}),setTimeout(n(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&Se(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom),P((function(){this._moveEnd(!0)}),this))}}),Ke=k.extend({options:{position:"topright"},initialize:function(t){f(this,t)},getPosition:function(){return this.options.position},setPosition:function(t){var e=this._map;return e&&e.removeControl(this),this.options.position=t,e&&e.addControl(this),this},getContainer:function(){return this._container},addTo:function(t){this.remove(),this._map=t;var e=this._container=this.onAdd(t),i=this.getPosition(),o=t._controlCorners[i];return ze(e,"leaflet-control"),-1!==i.indexOf("bottom")?o.insertBefore(e,o.firstChild):o.appendChild(e),this},remove:function(){return this._map?(Be(this._container),this.onRemove&&this.onRemove(this._map),this._map=null,this):this},_refocusOnMap:function(t){this._map&&t&&t.screenX>0&&t.screenY>0&&this._map.getContainer().focus()}}),Ye=function(t){return new Ke(t)};Ge.include({addControl:function(t){return t.addTo(this),this},removeControl:function(t){return t.remove(),this},_initControlPos:function(){var t=this._controlCorners={},e="leaflet-",i=this._controlContainer=Le("div",e+"control-container",this._container);function o(o,n){var a=e+o+" "+e+n;t[o+n]=Le("div",a,i)}o("top","left"),o("top","right"),o("bottom","left"),o("bottom","right")},_clearControlPos:function(){for(var t in this._controlCorners)Be(this._controlCorners[t]);Be(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var Qe=Ke.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(t,e,i,o){return i<o?-1:o<i?1:0}},initialize:function(t,e,i){for(var o in f(this,i),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,t)this._addLayer(t[o],o);for(o in e)this._addLayer(e[o],o,!0)},onAdd:function(t){this._initLayout(),this._update(),this._map=t,t.on("zoomend",this._checkDisabledLayers,this);for(var e=0;e<this._layers.length;e++)this._layers[e].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(t){return Ke.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(t,e){return this._addLayer(t,e),this._map?this._update():this},addOverlay:function(t,e){return this._addLayer(t,e,!0),this._map?this._update():this},removeLayer:function(t){t.off("add remove",this._onLayerChange,this);var e=this._getLayer(r(t));return e&&this._layers.splice(this._layers.indexOf(e),1),this._map?this._update():this},expand:function(){ze(this._container,"leaflet-control-layers-expanded"),this._form.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._form.clientHeight?(ze(this._form,"leaflet-control-layers-scrollbar"),this._form.style.height=t+"px"):Se(this._form,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return Se(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var t="leaflet-control-layers",e=this._container=Le("div",t),i=this.options.collapsed;e.setAttribute("aria-haspopup",!0),oe(e),ie(e);var o=this._form=Le("form",t+"-list");i&&(this._map.on("click",this.collapse,this),ot||Qt(e,{mouseenter:this.expand,mouseleave:this.collapse},this));var n=this._layersLink=Le("a",t+"-toggle",e);n.href="#",n.title="Layers",Ct?(Qt(n,"click",ae),Qt(n,"click",this.expand,this)):Qt(n,"focus",this.expand,this),i||this.expand(),this._baseLayersList=Le("div",t+"-base",o),this._separator=Le("div",t+"-separator",o),this._overlaysList=Le("div",t+"-overlays",o),e.appendChild(o)},_getLayer:function(t){for(var e=0;e<this._layers.length;e++)if(this._layers[e]&&r(this._layers[e].layer)===t)return this._layers[e]},_addLayer:function(t,e,i){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:e,overlay:i}),this.options.sortLayers&&this._layers.sort(n((function(t,e){return this.options.sortFunction(t.layer,e.layer,t.name,e.name)}),this)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;Pe(this._baseLayersList),Pe(this._overlaysList),this._layerControlInputs=[];var t,e,i,o,n=0;for(i=0;i<this._layers.length;i++)o=this._layers[i],this._addItem(o),e=e||o.overlay,t=t||!o.overlay,n+=o.overlay?0:1;return this.options.hideSingleBase&&(t=t&&n>1,this._baseLayersList.style.display=t?"":"none"),this._separator.style.display=e&&t?"":"none",this},_onLayerChange:function(t){this._handlingClick||this._update();var e=this._getLayer(r(t.target)),i=e.overlay?"add"===t.type?"overlayadd":"overlayremove":"add"===t.type?"baselayerchange":null;i&&this._map.fire(i,e)},_createRadioElement:function(t,e){var i='<input type="radio" class="leaflet-control-layers-selector" name="'+t+'"'+(e?' checked="checked"':"")+"/>",o=document.createElement("div");return o.innerHTML=i,o.firstChild},_addItem:function(t){var e,i=document.createElement("label"),o=this._map.hasLayer(t.layer);t.overlay?((e=document.createElement("input")).type="checkbox",e.className="leaflet-control-layers-selector",e.defaultChecked=o):e=this._createRadioElement("leaflet-base-layers",o),this._layerControlInputs.push(e),e.layerId=r(t.layer),Qt(e,"click",this._onInputClick,this);var n=document.createElement("span");n.innerHTML=" "+t.name;var a=document.createElement("div");return i.appendChild(a),a.appendChild(e),a.appendChild(n),(t.overlay?this._overlaysList:this._baseLayersList).appendChild(i),this._checkDisabledLayers(),i},_onInputClick:function(){var t,e,i=this._layerControlInputs,o=[],n=[];this._handlingClick=!0;for(var a=i.length-1;a>=0;a--)t=i[a],e=this._getLayer(t.layerId).layer,t.checked?o.push(e):t.checked||n.push(e);for(a=0;a<n.length;a++)this._map.hasLayer(n[a])&&this._map.removeLayer(n[a]);for(a=0;a<o.length;a++)this._map.hasLayer(o[a])||this._map.addLayer(o[a]);this._handlingClick=!1,this._refocusOnMap()},_checkDisabledLayers:function(){for(var t,e,i=this._layerControlInputs,o=this._map.getZoom(),n=i.length-1;n>=0;n--)t=i[n],e=this._getLayer(t.layerId).layer,t.disabled=void 0!==e.options.minZoom&&o<e.options.minZoom||void 0!==e.options.maxZoom&&o>e.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expand:function(){return this.expand()},_collapse:function(){return this.collapse()}}),Xe=Ke.extend({options:{position:"topleft",zoomInText:"+",zoomInTitle:"Zoom in",zoomOutText:"&#x2212;",zoomOutTitle:"Zoom out"},onAdd:function(t){var e="leaflet-control-zoom",i=Le("div",e+" leaflet-bar"),o=this.options;return this._zoomInButton=this._createButton(o.zoomInText,o.zoomInTitle,e+"-in",i,this._zoomIn),this._zoomOutButton=this._createButton(o.zoomOutText,o.zoomOutTitle,e+"-out",i,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),i},onRemove:function(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut:function(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton:function(t,e,i,o,n){var a=Le("a",i,o);return a.innerHTML=t,a.href="#",a.title=e,a.setAttribute("role","button"),a.setAttribute("aria-label",e),oe(a),Qt(a,"click",ae),Qt(a,"click",n,this),Qt(a,"click",this._refocusOnMap,this),a},_updateDisabled:function(){var t=this._map,e="leaflet-disabled";Se(this._zoomInButton,e),Se(this._zoomOutButton,e),(this._disabled||t._zoom===t.getMinZoom())&&ze(this._zoomOutButton,e),(this._disabled||t._zoom===t.getMaxZoom())&&ze(this._zoomInButton,e)}});Ge.mergeOptions({zoomControl:!0}),Ge.addInitHook((function(){this.options.zoomControl&&(this.zoomControl=new Xe,this.addControl(this.zoomControl))}));var Je=Ke.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(t){var e=Le("div","leaflet-control-scale"),i=this.options;return this._addScales(i,"leaflet-control-scale-line",e),t.on(i.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),e},onRemove:function(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(t,e,i){t.metric&&(this._mScale=Le("div",e,i)),t.imperial&&(this._iScale=Le("div",e,i))},_update:function(){var t=this._map,e=t.getSize().y/2,i=t.distance(t.containerPointToLatLng([0,e]),t.containerPointToLatLng([this.options.maxWidth,e]));this._updateScales(i)},_updateScales:function(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric:function(t){var e=this._getRoundNum(t),i=e<1e3?e+" m":e/1e3+" km";this._updateScale(this._mScale,i,e/t)},_updateImperial:function(t){var e,i,o,n=3.2808399*t;n>5280?(e=n/5280,i=this._getRoundNum(e),this._updateScale(this._iScale,i+" mi",i/e)):(o=this._getRoundNum(n),this._updateScale(this._iScale,o+" ft",o/n))},_updateScale:function(t,e,i){t.style.width=Math.round(this.options.maxWidth*i)+"px",t.innerHTML=e},_getRoundNum:function(t){var e=Math.pow(10,(Math.floor(t)+"").length-1),i=t/e;return e*(i=i>=10?10:i>=5?5:i>=3?3:i>=2?2:1)}}),$e=Ke.extend({options:{position:"bottomright",prefix:'<a href="http://leafletjs.com" title="A JS library for interactive maps">Leaflet</a>'},initialize:function(t){f(this,t),this._attributions={}},onAdd:function(t){for(var e in t.attributionControl=this,this._container=Le("div","leaflet-control-attribution"),oe(this._container),t._layers)t._layers[e].getAttribution&&this.addAttribution(t._layers[e].getAttribution());return this._update(),this._container},setPrefix:function(t){return this.options.prefix=t,this._update(),this},addAttribution:function(t){return t?(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update(),this):this},removeAttribution:function(t){return t?(this._attributions[t]&&(this._attributions[t]--,this._update()),this):this},_update:function(){if(this._map){var t=[];for(var e in this._attributions)this._attributions[e]&&t.push(e);var i=[];this.options.prefix&&i.push(this.options.prefix),t.length&&i.push(t.join(", ")),this._container.innerHTML=i.join(" | ")}}});Ge.mergeOptions({attributionControl:!0}),Ge.addInitHook((function(){this.options.attributionControl&&(new $e).addTo(this)})),Ke.Layers=Qe,Ke.Zoom=Xe,Ke.Scale=Je,Ke.Attribution=$e,Ye.layers=function(t,e,i){return new Qe(t,e,i)},Ye.zoom=function(t){return new Xe(t)},Ye.scale=function(t){return new Je(t)},Ye.attribution=function(t){return new $e(t)};var ti=k.extend({initialize:function(t){this._map=t},enable:function(){return this._enabled||(this._enabled=!0,this.addHooks()),this},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});ti.addTo=function(t,e){return t.addHandler(e,this),this};var ei,ii={Events:z},oi=Ct?"touchstart mousedown":"mousedown",ni={mousedown:"mouseup",touchstart:"touchend",pointerdown:"touchend",MSPointerDown:"touchend"},ai={mousedown:"mousemove",touchstart:"touchmove",pointerdown:"touchmove",MSPointerDown:"touchmove"},ri=S.extend({options:{clickTolerance:3},initialize:function(t,e,i,o){f(this,o),this._element=t,this._dragStartTarget=e||t,this._preventOutline=i},enable:function(){this._enabled||(Qt(this._dragStartTarget,oi,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(ri._dragging===this&&this.finishDrag(),Jt(this._dragStartTarget,oi,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(t){if(!t._simulated&&this._enabled&&(this._moved=!1,!ke(this._element,"leaflet-zoom-anim")&&!(ri._dragging||t.shiftKey||1!==t.which&&1!==t.button&&!t.touches||(ri._dragging=this,this._preventOutline&&He(this._element),We(),fe(),this._moving)))){this.fire("down");var e=t.touches?t.touches[0]:t;this._startPoint=new E(e.clientX,e.clientY),Qt(document,ai[t.type],this._onMove,this),Qt(document,ni[t.type],this._onUp,this)}},_onMove:function(t){if(!t._simulated&&this._enabled)if(t.touches&&t.touches.length>1)this._moved=!0;else{var e=t.touches&&1===t.touches.length?t.touches[0]:t,i=new E(e.clientX,e.clientY).subtract(this._startPoint);(i.x||i.y)&&(Math.abs(i.x)+Math.abs(i.y)<this.options.clickTolerance||(ne(t),this._moved||(this.fire("dragstart"),this._moved=!0,this._startPos=Ne(this._element).subtract(i),ze(document.body,"leaflet-dragging"),this._lastTarget=t.target||t.srcElement,window.SVGElementInstance&&this._lastTarget instanceof SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),ze(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(i),this._moving=!0,M(this._animRequest),this._lastEvent=t,this._animRequest=P(this._updatePosition,this,!0)))}},_updatePosition:function(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),Re(this._element,this._newPos),this.fire("drag",t)},_onUp:function(t){!t._simulated&&this._enabled&&this.finishDrag()},finishDrag:function(){for(var t in Se(document.body,"leaflet-dragging"),this._lastTarget&&(Se(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),ai)Jt(document,ai[t],this._onMove,this),Jt(document,ni[t],this._onUp,this);Ue(),me(),this._moved&&this._moving&&(M(this._animRequest),this.fire("dragend",{distance:this._newPos.distanceTo(this._startPos)})),this._moving=!1,ri._dragging=!1}});function si(t,e){if(!e||!t.length)return t.slice();var i=e*e;return t=function(t,e){var i=t.length,o=new(("undefined"==typeof Uint8Array?"undefined":s(Uint8Array))!==void 0+""?Uint8Array:Array)(i);o[0]=o[i-1]=1,function t(e,i,o,n,a){var r,s,l,h=0;for(s=n+1;s<=a-1;s++)(l=di(e[s],e[n],e[a],!0))>h&&(r=s,h=l);h>o&&(i[r]=1,t(e,i,o,n,r),t(e,i,o,r,a))}(t,o,e,0,i-1);var n,a=[];for(n=0;n<i;n++)o[n]&&a.push(t[n]);return a}(t=function(t,e){for(var i=[t[0]],o=1,n=0,a=t.length;o<a;o++)r=t[o],s=t[n],l=void 0,h=void 0,l=s.x-r.x,h=s.y-r.y,l*l+h*h>e&&(i.push(t[o]),n=o);var r,s,l,h;return n<a-1&&i.push(t[a-1]),i}(t,i),i)}function li(t,e,i){return Math.sqrt(di(t,e,i,!0))}function hi(t,e,i,o,n){var a,r,s,l=o?ei:ci(t,i),h=ci(e,i);for(ei=h;;){if(!(l|h))return[t,e];if(l&h)return!1;s=ci(r=ui(t,e,a=l||h,i,n),i),a===l?(t=r,l=s):(e=r,h=s)}}function ui(t,e,i,o,n){var a,r,s=e.x-t.x,l=e.y-t.y,h=o.min,u=o.max;return 8&i?(a=t.x+s*(u.y-t.y)/l,r=u.y):4&i?(a=t.x+s*(h.y-t.y)/l,r=h.y):2&i?(a=u.x,r=t.y+l*(u.x-t.x)/s):1&i&&(a=h.x,r=t.y+l*(h.x-t.x)/s),new E(a,r,n)}function ci(t,e){var i=0;return t.x<e.min.x?i|=1:t.x>e.max.x&&(i|=2),t.y<e.min.y?i|=4:t.y>e.max.y&&(i|=8),i}function di(t,e,i,o){var n,a=e.x,r=e.y,s=i.x-a,l=i.y-r,h=s*s+l*l;return h>0&&((n=((t.x-a)*s+(t.y-r)*l)/h)>1?(a=i.x,r=i.y):n>0&&(a+=s*n,r+=l*n)),s=t.x-a,l=t.y-r,o?s*s+l*l:new E(a,r)}function pi(t){return!v(t[0])||"object"!==s(t[0][0])&&void 0!==t[0][0]}function fi(t){return pi(t)}var mi=(Object.freeze||Object)({simplify:si,pointToSegmentDistance:li,closestPointOnSegment:function(t,e,i){return di(t,e,i)},clipSegment:hi,_getEdgeIntersection:ui,_getBitCode:ci,_sqClosestPointOnSegment:di,isFlat:pi,_flat:fi});function _i(t,e,i){var o,n,a,r,s,l,h,u,c,d=[1,4,2,8];for(n=0,h=t.length;n<h;n++)t[n]._code=ci(t[n],e);for(r=0;r<4;r++){for(u=d[r],o=[],n=0,a=(h=t.length)-1;n<h;a=n++)s=t[n],l=t[a],s._code&u?l._code&u||((c=ui(l,s,u,e,i))._code=ci(c,e),o.push(c)):(l._code&u&&((c=ui(l,s,u,e,i))._code=ci(c,e),o.push(c)),o.push(s));t=o}return t}var gi=(Object.freeze||Object)({clipPolygon:_i}),vi={project:function(t){return new E(t.lng,t.lat)},unproject:function(t){return new j(t.y,t.x)},bounds:new I([-180,-90],[180,90])},Ai={R:6378137,R_MINOR:6356752.314245179,bounds:new I([-20037508.34279,-15496570.73972],[20037508.34279,18764656.23138]),project:function(t){var e=Math.PI/180,i=this.R,o=t.lat*e,n=this.R_MINOR/i,a=Math.sqrt(1-n*n),r=a*Math.sin(o),s=Math.tan(Math.PI/4-o/2)/Math.pow((1-r)/(1+r),a/2);return o=-i*Math.log(Math.max(s,1e-10)),new E(t.lng*e*i,o)},unproject:function(t){for(var e,i=180/Math.PI,o=this.R,n=this.R_MINOR/o,a=Math.sqrt(1-n*n),r=Math.exp(-t.y/o),s=Math.PI/2-2*Math.atan(r),l=0,h=.1;l<15&&Math.abs(h)>1e-7;l++)e=a*Math.sin(s),e=Math.pow((1-e)/(1+e),a/2),s+=h=Math.PI/2-2*Math.atan(r*e)-s;return new j(s*i,t.x*i/o)}},yi=(Object.freeze||Object)({LonLat:vi,Mercator:Ai,SphericalMercator:q}),bi=i({},F,{code:"EPSG:3395",projection:Ai,transformation:function(){var t=.5/(Math.PI*Ai.R);return G(t,.5,-t,.5)}()}),xi=i({},F,{code:"EPSG:4326",projection:vi,transformation:G(1/180,1,-1/180,.5)}),Ci=i({},H,{projection:vi,transformation:G(1,0,-1,0),scale:function(t){return Math.pow(2,t)},zoom:function(t){return Math.log(t)/Math.LN2},distance:function(t,e){var i=e.lng-t.lng,o=e.lat-t.lat;return Math.sqrt(i*i+o*o)},infinite:!0});H.Earth=F,H.EPSG3395=bi,H.EPSG3857=K,H.EPSG900913=Y,H.EPSG4326=xi,H.Simple=Ci;var wi=S.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(t){return t.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(t){return t&&t.removeLayer(this),this},getPane:function(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget:function(t){return this._map._targets[r(t)]=this,this},removeInteractiveTarget:function(t){return delete this._map._targets[r(t)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(t){var e=t.target;if(e.hasLayer(this)){if(this._map=e,this._zoomAnimated=e._zoomAnimated,this.getEvents){var i=this.getEvents();e.on(i,this),this.once("remove",(function(){e.off(i,this)}),this)}this.onAdd(e),this.getAttribution&&e.attributionControl&&e.attributionControl.addAttribution(this.getAttribution()),this.fire("add"),e.fire("layeradd",{layer:this})}}});Ge.include({addLayer:function(t){if(!t._layerAdd)throw new Error("The provided object is not a Layer.");var e=r(t);return this._layers[e]||(this._layers[e]=t,t._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t)),this},removeLayer:function(t){var e=r(t);return this._layers[e]?(this._loaded&&t.onRemove(this),t.getAttribution&&this.attributionControl&&this.attributionControl.removeAttribution(t.getAttribution()),delete this._layers[e],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null,this):this},hasLayer:function(t){return!!t&&r(t)in this._layers},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},_addLayers:function(t){for(var e=0,i=(t=t?v(t)?t:[t]:[]).length;e<i;e++)this.addLayer(t[e])},_addZoomLimit:function(t){!isNaN(t.options.maxZoom)&&isNaN(t.options.minZoom)||(this._zoomBoundLayers[r(t)]=t,this._updateZoomLevels())},_removeZoomLimit:function(t){var e=r(t);this._zoomBoundLayers[e]&&(delete this._zoomBoundLayers[e],this._updateZoomLevels())},_updateZoomLevels:function(){var t=1/0,e=-1/0,i=this._getZoomSpan();for(var o in this._zoomBoundLayers){var n=this._zoomBoundLayers[o].options;t=void 0===n.minZoom?t:Math.min(t,n.minZoom),e=void 0===n.maxZoom?e:Math.max(e,n.maxZoom)}this._layersMaxZoom=e===-1/0?void 0:e,this._layersMinZoom=t===1/0?void 0:t,i!==this._getZoomSpan()&&this.fire("zoomlevelschange"),void 0===this.options.maxZoom&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),void 0===this.options.minZoom&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var Li=wi.extend({initialize:function(t,e){var i,o;if(f(this,e),this._layers={},t)for(i=0,o=t.length;i<o;i++)this.addLayer(t[i])},addLayer:function(t){var e=this.getLayerId(t);return this._layers[e]=t,this._map&&this._map.addLayer(t),this},removeLayer:function(t){var e=t in this._layers?t:this.getLayerId(t);return this._map&&this._layers[e]&&this._map.removeLayer(this._layers[e]),delete this._layers[e],this},hasLayer:function(t){return!!t&&(t in this._layers||this.getLayerId(t)in this._layers)},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(t){var e,i,o=Array.prototype.slice.call(arguments,1);for(e in this._layers)(i=this._layers[e])[t]&&i[t].apply(i,o);return this},onAdd:function(t){this.eachLayer(t.addLayer,t)},onRemove:function(t){this.eachLayer(t.removeLayer,t)},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},getLayer:function(t){return this._layers[t]},getLayers:function(){var t=[];return this.eachLayer(t.push,t),t},setZIndex:function(t){return this.invoke("setZIndex",t)},getLayerId:function(t){return r(t)}}),Bi=Li.extend({addLayer:function(t){return this.hasLayer(t)?this:(t.addEventParent(this),Li.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer:function(t){return this.hasLayer(t)?(t in this._layers&&(t=this._layers[t]),t.removeEventParent(this),Li.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle:function(t){return this.invoke("setStyle",t)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var t=new R;for(var e in this._layers){var i=this._layers[e];t.extend(i.getBounds?i.getBounds():i.getLatLng())}return t}}),Pi=k.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0]},initialize:function(t){f(this,t)},createIcon:function(t){return this._createIcon("icon",t)},createShadow:function(t){return this._createIcon("shadow",t)},_createIcon:function(t,e){var i=this._getIconUrl(t);if(!i){if("icon"===t)throw new Error("iconUrl not set in Icon options (see the docs).");return null}var o=this._createImg(i,e&&"IMG"===e.tagName?e:null);return this._setIconStyles(o,t),o},_setIconStyles:function(t,e){var i=this.options,o=i[e+"Size"];"number"==typeof o&&(o=[o,o]);var n=O(o),a=O("shadow"===e&&i.shadowAnchor||i.iconAnchor||n&&n.divideBy(2,!0));t.className="leaflet-marker-"+e+" "+(i.className||""),a&&(t.style.marginLeft=-a.x+"px",t.style.marginTop=-a.y+"px"),n&&(t.style.width=n.x+"px",t.style.height=n.y+"px")},_createImg:function(t,e){return(e=e||document.createElement("img")).src=t,e},_getIconUrl:function(t){return Bt&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}}),Mi=Pi.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(t){return Mi.imagePath||(Mi.imagePath=this._detectIconPath()),(this.options.imagePath||Mi.imagePath)+Pi.prototype._getIconUrl.call(this,t)},_detectIconPath:function(){var t=Le("div","leaflet-default-icon-path",document.body),e=we(t,"background-image")||we(t,"backgroundImage");return document.body.removeChild(t),e=null===e||0!==e.indexOf("url")?"":e.replace(/^url\(["']?/,"").replace(/marker-icon\.png["']?\)$/,"")}}),Ti=ti.extend({initialize:function(t){this._marker=t},addHooks:function(){var t=this._marker._icon;this._draggable||(this._draggable=new ri(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),ze(t,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&Se(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(t){var e=this._marker,i=e._map,o=this._marker.options.autoPanSpeed,n=this._marker.options.autoPanPadding,a=L.DomUtil.getPosition(e._icon),r=i.getPixelBounds(),s=i.getPixelOrigin(),l=D(r.min._subtract(s).add(n),r.max._subtract(s).subtract(n));if(!l.contains(a)){var h=O((Math.max(l.max.x,a.x)-l.max.x)/(r.max.x-l.max.x)-(Math.min(l.min.x,a.x)-l.min.x)/(r.min.x-l.min.x),(Math.max(l.max.y,a.y)-l.max.y)/(r.max.y-l.max.y)-(Math.min(l.min.y,a.y)-l.min.y)/(r.min.y-l.min.y)).multiplyBy(o);i.panBy(h,{animate:!1}),this._draggable._newPos._add(h),this._draggable._startPos._add(h),L.DomUtil.setPosition(e._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=P(this._adjustPan.bind(this,t))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup().fire("movestart").fire("dragstart")},_onPreDrag:function(t){this._marker.options.autoPan&&(M(this._panRequest),this._panRequest=P(this._adjustPan.bind(this,t)))},_onDrag:function(t){var e=this._marker,i=e._shadow,o=Ne(e._icon),n=e._map.layerPointToLatLng(o);i&&Re(i,o),e._latlng=n,t.latlng=n,t.oldLatLng=this._oldLatLng,e.fire("move",t).fire("drag",t)},_onDragEnd:function(t){M(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),ki=wi.extend({options:{icon:new Mi,interactive:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10,keyboard:!0,title:"",alt:"",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",bubblingMouseEvents:!1},initialize:function(t,e){f(this,e),this._latlng=W(t)},onAdd:function(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(t){var e=this._latlng;return this._latlng=W(t),this.update(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},setZIndexOffset:function(t){return this.options.zIndexOffset=t,this.update()},setIcon:function(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var t=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(t)}return this},_initIcon:function(){var t=this.options,e="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),i=t.icon.createIcon(this._icon),o=!1;i!==this._icon&&(this._icon&&this._removeIcon(),o=!0,t.title&&(i.title=t.title),"IMG"===i.tagName&&(i.alt=t.alt||"")),ze(i,e),t.keyboard&&(i.tabIndex="0"),this._icon=i,t.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex});var n=t.icon.createShadow(this._shadow),a=!1;n!==this._shadow&&(this._removeShadow(),a=!0),n&&(ze(n,e),n.alt=""),this._shadow=n,t.opacity<1&&this._updateOpacity(),o&&this.getPane().appendChild(this._icon),this._initInteraction(),n&&a&&this.getPane("shadowPane").appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),Be(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&Be(this._shadow),this._shadow=null},_setPos:function(t){Re(this._icon,t),this._shadow&&Re(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(t){this._icon.style.zIndex=this._zIndex+t},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round();this._setPos(e)},_initInteraction:function(){if(this.options.interactive&&(ze(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),Ti)){var t=this.options.draggable;this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new Ti(this),t&&this.dragging.enable()}},setOpacity:function(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var t=this.options.opacity;Oe(this._icon,t),this._shadow&&Oe(this._shadow,t)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}}),zi=wi.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(t){this._renderer=t.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(t){return f(this,t),this._renderer&&this._renderer._updateStyle(this),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+this._renderer.options.tolerance}}),Si=zi.extend({options:{fill:!0,radius:10},initialize:function(t,e){f(this,e),this._latlng=W(t),this._radius=this.options.radius},setLatLng:function(t){return this._latlng=W(t),this.redraw(),this.fire("move",{latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(t){return this.options.radius=this._radius=t,this.redraw()},getRadius:function(){return this._radius},setStyle:function(t){var e=t&&t.radius||this._radius;return zi.prototype.setStyle.call(this,t),this.setRadius(e),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var t=this._radius,e=this._radiusY||t,i=this._clickTolerance(),o=[t+i,e+i];this._pxBounds=new I(this._point.subtract(o),this._point.add(o))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}}),Ei=Si.extend({initialize:function(t,e,o){if("number"==typeof e&&(e=i({},o,{radius:e})),f(this,e),this._latlng=W(t),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(t){return this._mRadius=t,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var t=[this._radius,this._radiusY||this._radius];return new R(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:zi.prototype.setStyle,_project:function(){var t=this._latlng.lng,e=this._latlng.lat,i=this._map,o=i.options.crs;if(o.distance===F.distance){var n=Math.PI/180,a=this._mRadius/F.R/n,r=i.project([e+a,t]),s=i.project([e-a,t]),l=r.add(s).divideBy(2),h=i.unproject(l).lat,u=Math.acos((Math.cos(a*n)-Math.sin(e*n)*Math.sin(h*n))/(Math.cos(e*n)*Math.cos(h*n)))/n;(isNaN(u)||0===u)&&(u=a/Math.cos(Math.PI/180*e)),this._point=l.subtract(i.getPixelOrigin()),this._radius=isNaN(u)?0:l.x-i.project([h,t-u]).x,this._radiusY=l.y-r.y}else{var c=o.unproject(o.project(this._latlng).subtract([this._mRadius,0]));this._point=i.latLngToLayerPoint(this._latlng),this._radius=this._point.x-i.latLngToLayerPoint(c).x}this._updateBounds()}}),Zi=zi.extend({options:{smoothFactor:1,noClip:!1},initialize:function(t,e){f(this,e),this._setLatLngs(t)},getLatLngs:function(){return this._latlngs},setLatLngs:function(t){return this._setLatLngs(t),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(t){for(var e,i,o=1/0,n=null,a=di,r=0,s=this._parts.length;r<s;r++)for(var l=this._parts[r],h=1,u=l.length;h<u;h++){var c=a(t,e=l[h-1],i=l[h],!0);c<o&&(o=c,n=a(t,e,i))}return n&&(n.distance=Math.sqrt(o)),n},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");var t,e,i,o,n,a,r,s=this._rings[0],l=s.length;if(!l)return null;for(t=0,e=0;t<l-1;t++)e+=s[t].distanceTo(s[t+1])/2;if(0===e)return this._map.layerPointToLatLng(s[0]);for(t=0,o=0;t<l-1;t++)if(n=s[t],a=s[t+1],(o+=i=n.distanceTo(a))>e)return r=(o-e)/i,this._map.layerPointToLatLng([a.x-r*(a.x-n.x),a.y-r*(a.y-n.y)])},getBounds:function(){return this._bounds},addLatLng:function(t,e){return e=e||this._defaultShape(),t=W(t),e.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs:function(t){this._bounds=new R,this._latlngs=this._convertLatLngs(t)},_defaultShape:function(){return pi(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(t){for(var e=[],i=pi(t),o=0,n=t.length;o<n;o++)i?(e[o]=W(t[o]),this._bounds.extend(e[o])):e[o]=this._convertLatLngs(t[o]);return e},_project:function(){var t=new I;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t);var e=this._clickTolerance(),i=new E(e,e);this._bounds.isValid()&&t.isValid()&&(t.min._subtract(i),t.max._add(i),this._pxBounds=t)},_projectLatlngs:function(t,e,i){var o,n,a=t[0]instanceof j,r=t.length;if(a){for(n=[],o=0;o<r;o++)n[o]=this._map.latLngToLayerPoint(t[o]),i.extend(n[o]);e.push(n)}else for(o=0;o<r;o++)this._projectLatlngs(t[o],e,i)},_clipPoints:function(){var t=this._renderer._bounds;if(this._parts=[],this._pxBounds&&this._pxBounds.intersects(t))if(this.options.noClip)this._parts=this._rings;else{var e,i,o,n,a,r,s,l=this._parts;for(e=0,o=0,n=this._rings.length;e<n;e++)for(i=0,a=(s=this._rings[e]).length;i<a-1;i++)(r=hi(s[i],s[i+1],t,i,!0))&&(l[o]=l[o]||[],l[o].push(r[0]),r[1]===s[i+1]&&i!==a-2||(l[o].push(r[1]),o++))}},_simplifyPoints:function(){for(var t=this._parts,e=this.options.smoothFactor,i=0,o=t.length;i<o;i++)t[i]=si(t[i],e)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(t,e){var i,o,n,a,r,s,l=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(i=0,a=this._parts.length;i<a;i++)for(o=0,n=(r=(s=this._parts[i]).length)-1;o<r;n=o++)if((e||0!==o)&&li(t,s[n],s[o])<=l)return!0;return!1}});Zi._flat=fi;var Oi=Zi.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");var t,e,i,o,n,a,r,s,l,h=this._rings[0],u=h.length;if(!u)return null;for(a=r=s=0,t=0,e=u-1;t<u;e=t++)i=h[t],o=h[e],n=i.y*o.x-o.y*i.x,r+=(i.x+o.x)*n,s+=(i.y+o.y)*n,a+=3*n;return l=0===a?h[0]:[r/a,s/a],this._map.layerPointToLatLng(l)},_convertLatLngs:function(t){var e=Zi.prototype._convertLatLngs.call(this,t),i=e.length;return i>=2&&e[0]instanceof j&&e[0].equals(e[i-1])&&e.pop(),e},_setLatLngs:function(t){Zi.prototype._setLatLngs.call(this,t),pi(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return pi(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var t=this._renderer._bounds,e=this.options.weight,i=new E(e,e);if(t=new I(t.min.subtract(i),t.max.add(i)),this._parts=[],this._pxBounds&&this._pxBounds.intersects(t))if(this.options.noClip)this._parts=this._rings;else for(var o,n=0,a=this._rings.length;n<a;n++)(o=_i(this._rings[n],t,!0)).length&&this._parts.push(o)},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(t){var e,i,o,n,a,r,s,l,h=!1;if(!this._pxBounds.contains(t))return!1;for(n=0,s=this._parts.length;n<s;n++)for(a=0,r=(l=(e=this._parts[n]).length)-1;a<l;r=a++)i=e[a],o=e[r],i.y>t.y!=o.y>t.y&&t.x<(o.x-i.x)*(t.y-i.y)/(o.y-i.y)+i.x&&(h=!h);return h||Zi.prototype._containsPoint.call(this,t,!0)}}),Ii=Bi.extend({initialize:function(t,e){f(this,e),this._layers={},t&&this.addData(t)},addData:function(t){var e,i,o,n=v(t)?t:t.features;if(n){for(e=0,i=n.length;e<i;e++)((o=n[e]).geometries||o.geometry||o.features||o.coordinates)&&this.addData(o);return this}var a=this.options;if(a.filter&&!a.filter(t))return this;var r=Di(t,a);return r?(r.feature=Hi(t),r.defaultOptions=r.options,this.resetStyle(r),a.onEachFeature&&a.onEachFeature(t,r),this.addLayer(r)):this},resetStyle:function(t){return t.options=i({},t.defaultOptions),this._setLayerStyle(t,this.options.style),this},setStyle:function(t){return this.eachLayer((function(e){this._setLayerStyle(e,t)}),this)},_setLayerStyle:function(t,e){"function"==typeof e&&(e=e(t.feature)),t.setStyle&&t.setStyle(e)}});function Di(t,e){var i,o,n,a,r="Feature"===t.type?t.geometry:t,s=r?r.coordinates:null,l=[],h=e&&e.pointToLayer,u=e&&e.coordsToLatLng||Ri;if(!s&&!r)return null;switch(r.type){case"Point":return i=u(s),h?h(t,i):new ki(i);case"MultiPoint":for(n=0,a=s.length;n<a;n++)i=u(s[n]),l.push(h?h(t,i):new ki(i));return new Bi(l);case"LineString":case"MultiLineString":return o=Ni(s,"LineString"===r.type?0:1,u),new Zi(o,e);case"Polygon":case"MultiPolygon":return o=Ni(s,"Polygon"===r.type?1:2,u),new Oi(o,e);case"GeometryCollection":for(n=0,a=r.geometries.length;n<a;n++){var c=Di({geometry:r.geometries[n],type:"Feature",properties:t.properties},e);c&&l.push(c)}return new Bi(l);default:throw new Error("Invalid GeoJSON object.")}}function Ri(t){return new j(t[1],t[0],t[2])}function Ni(t,e,i){for(var o,n=[],a=0,r=t.length;a<r;a++)o=e?Ni(t[a],e-1,i):(i||Ri)(t[a]),n.push(o);return n}function ji(t,e){return e="number"==typeof e?e:6,void 0!==t.alt?[c(t.lng,e),c(t.lat,e),c(t.alt,e)]:[c(t.lng,e),c(t.lat,e)]}function Wi(t,e,i,o){for(var n=[],a=0,r=t.length;a<r;a++)n.push(e?Wi(t[a],e-1,i,o):ji(t[a],o));return!e&&i&&n.push(n[0]),n}function Ui(t,e){return t.feature?i({},t.feature,{geometry:e}):Hi(e)}function Hi(t){return"Feature"===t.type||"FeatureCollection"===t.type?t:{type:"Feature",properties:{},geometry:t}}var Fi={toGeoJSON:function(t){return Ui(this,{type:"Point",coordinates:ji(this.getLatLng(),t)})}};function qi(t,e){return new Ii(t,e)}ki.include(Fi),Ei.include(Fi),Si.include(Fi),Zi.include({toGeoJSON:function(t){var e=!pi(this._latlngs);return Ui(this,{type:(e?"Multi":"")+"LineString",coordinates:Wi(this._latlngs,e?1:0,!1,t)})}}),Oi.include({toGeoJSON:function(t){var e=!pi(this._latlngs),i=e&&!pi(this._latlngs[0]),o=Wi(this._latlngs,i?2:e?1:0,!0,t);return e||(o=[o]),Ui(this,{type:(i?"Multi":"")+"Polygon",coordinates:o})}}),Li.include({toMultiPoint:function(t){var e=[];return this.eachLayer((function(i){e.push(i.toGeoJSON(t).geometry.coordinates)})),Ui(this,{type:"MultiPoint",coordinates:e})},toGeoJSON:function(t){var e=this.feature&&this.feature.geometry&&this.feature.geometry.type;if("MultiPoint"===e)return this.toMultiPoint(t);var i="GeometryCollection"===e,o=[];return this.eachLayer((function(e){if(e.toGeoJSON){var n=e.toGeoJSON(t);if(i)o.push(n.geometry);else{var a=Hi(n);"FeatureCollection"===a.type?o.push.apply(o,a.features):o.push(a)}}})),i?Ui(this,{geometries:o,type:"GeometryCollection"}):{type:"FeatureCollection",features:o}}});var Vi=qi,Gi=wi.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(t,e,i){this._url=t,this._bounds=N(e),f(this,i)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(ze(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){Be(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle:function(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront:function(){return this._map&&Me(this._image),this},bringToBack:function(){return this._map&&Te(this._image),this},setUrl:function(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds:function(t){return this._bounds=N(t),this._map&&this._reset(),this},getEvents:function(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var t="IMG"===this._url.tagName,e=this._image=t?this._url:Le("img");ze(e,"leaflet-image-layer"),this._zoomAnimated&&ze(e,"leaflet-zoom-animated"),this.options.className&&ze(e,this.options.className),e.onselectstart=u,e.onmousemove=u,e.onload=n(this.fire,this,"load"),e.onerror=n(this._overlayOnError,this,"error"),this.options.crossOrigin&&(e.crossOrigin=""),this.options.zIndex&&this._updateZIndex(),t?this._url=e.src:(e.src=this._url,e.alt=this.options.alt)},_animateZoom:function(t){var e=this._map.getZoomScale(t.zoom),i=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;De(this._image,i,e)},_reset:function(){var t=this._image,e=new I(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),i=e.getSize();Re(t,e.min),t.style.width=i.x+"px",t.style.height=i.y+"px"},_updateOpacity:function(){Oe(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&void 0!==this.options.zIndex&&null!==this.options.zIndex&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)}}),Ki=Gi.extend({options:{autoplay:!0,loop:!0},_initImage:function(){var t="VIDEO"===this._url.tagName,e=this._image=t?this._url:Le("video");if(ze(e,"leaflet-image-layer"),this._zoomAnimated&&ze(e,"leaflet-zoom-animated"),e.onselectstart=u,e.onmousemove=u,e.onloadeddata=n(this.fire,this,"load"),t){for(var i=e.getElementsByTagName("source"),o=[],a=0;a<i.length;a++)o.push(i[a].src);this._url=i.length>0?o:[e.src]}else{v(this._url)||(this._url=[this._url]),e.autoplay=!!this.options.autoplay,e.loop=!!this.options.loop;for(var r=0;r<this._url.length;r++){var s=Le("source");s.src=this._url[r],e.appendChild(s)}}}}),Yi=wi.extend({options:{offset:[0,7],className:"",pane:"popupPane"},initialize:function(t,e){f(this,t),this._source=e},onAdd:function(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&Oe(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&Oe(this._container,1),this.bringToFront()},onRemove:function(t){t._fadeAnimated?(Oe(this._container,0),this._removeTimeout=setTimeout(n(Be,void 0,this._container),200)):Be(this._container)},getLatLng:function(){return this._latlng},setLatLng:function(t){return this._latlng=W(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(t){return this._content=t,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Me(this._container),this},bringToBack:function(){return this._map&&Te(this._container),this},_updateContent:function(){if(this._content){var t=this._contentNode,e="function"==typeof this._content?this._content(this._source||this):this._content;if("string"==typeof e)t.innerHTML=e;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(e)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var t=this._map.latLngToLayerPoint(this._latlng),e=O(this.options.offset),i=this._getAnchor();this._zoomAnimated?Re(this._container,t.add(i)):e=e.add(t).add(i);var o=this._containerBottom=-e.y,n=this._containerLeft=-Math.round(this._containerWidth/2)+e.x;this._container.style.bottom=o+"px",this._container.style.left=n+"px"}},_getAnchor:function(){return[0,0]}}),Qi=Yi.extend({options:{maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(t){return t.openPopup(this),this},onAdd:function(t){Yi.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof zi||this._source.on("preclick",ee))},onRemove:function(t){Yi.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof zi||this._source.off("preclick",ee))},getEvents:function(){var t=Yi.prototype.getEvents.call(this);return(void 0!==this.options.closeOnClick?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(t.preclick=this._close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_close:function(){this._map&&this._map.closePopup(this)},_initLayout:function(){var t="leaflet-popup",e=this._container=Le("div",t+" "+(this.options.className||"")+" leaflet-zoom-animated"),i=this._wrapper=Le("div",t+"-content-wrapper",e);if(this._contentNode=Le("div",t+"-content",i),oe(i),ie(this._contentNode),Qt(i,"contextmenu",ee),this._tipContainer=Le("div",t+"-tip-container",e),this._tip=Le("div",t+"-tip",this._tipContainer),this.options.closeButton){var o=this._closeButton=Le("a",t+"-close-button",e);o.href="#close",o.innerHTML="&#215;",Qt(o,"click",this._onCloseButtonClick,this)}},_updateLayout:function(){var t=this._contentNode,e=t.style;e.width="",e.whiteSpace="nowrap";var i=t.offsetWidth;i=Math.min(i,this.options.maxWidth),i=Math.max(i,this.options.minWidth),e.width=i+1+"px",e.whiteSpace="",e.height="";var o=t.offsetHeight,n=this.options.maxHeight;n&&o>n?(e.height=n+"px",ze(t,"leaflet-popup-scrolled")):Se(t,"leaflet-popup-scrolled"),this._containerWidth=this._container.offsetWidth},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),i=this._getAnchor();Re(this._container,e.add(i))},_adjustPan:function(){if(!(!this.options.autoPan||this._map._panAnim&&this._map._panAnim._inProgress)){var t=this._map,e=parseInt(we(this._container,"marginBottom"),10)||0,i=this._container.offsetHeight+e,o=this._containerWidth,n=new E(this._containerLeft,-i-this._containerBottom);n._add(Ne(this._container));var a=t.layerPointToContainerPoint(n),r=O(this.options.autoPanPadding),s=O(this.options.autoPanPaddingTopLeft||r),l=O(this.options.autoPanPaddingBottomRight||r),h=t.getSize(),u=0,c=0;a.x+o+l.x>h.x&&(u=a.x+o-h.x+l.x),a.x-u-s.x<0&&(u=a.x-s.x),a.y+i+l.y>h.y&&(c=a.y+i-h.y+l.y),a.y-c-s.y<0&&(c=a.y-s.y),(u||c)&&t.fire("autopanstart").panBy([u,c])}},_onCloseButtonClick:function(t){this._close(),ae(t)},_getAnchor:function(){return O(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}});Ge.mergeOptions({closePopupOnClick:!0}),Ge.include({openPopup:function(t,e,i){return t instanceof Qi||(t=new Qi(i).setContent(t)),e&&t.setLatLng(e),this.hasLayer(t)?this:(this._popup&&this._popup.options.autoClose&&this.closePopup(),this._popup=t,this.addLayer(t))},closePopup:function(t){return t&&t!==this._popup||(t=this._popup,this._popup=null),t&&this.removeLayer(t),this}}),wi.include({bindPopup:function(t,e){return t instanceof Qi?(f(t,e),this._popup=t,t._source=this):(this._popup&&!e||(this._popup=new Qi(e,this)),this._popup.setContent(t)),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(t,e){if(t instanceof wi||(e=t,t=this),t instanceof Bi)for(var i in this._layers){t=this._layers[i];break}return e||(e=t.getCenter?t.getCenter():t.getLatLng()),this._popup&&this._map&&(this._popup._source=t,this._popup.update(),this._map.openPopup(this._popup,e)),this},closePopup:function(){return this._popup&&this._popup._close(),this},togglePopup:function(t){return this._popup&&(this._popup._map?this.closePopup():this.openPopup(t)),this},isPopupOpen:function(){return!!this._popup&&this._popup.isOpen()},setPopupContent:function(t){return this._popup&&this._popup.setContent(t),this},getPopup:function(){return this._popup},_openPopup:function(t){var e=t.layer||t.target;this._popup&&this._map&&(ae(t),e instanceof zi?this.openPopup(t.layer||t.target,t.latlng):this._map.hasLayer(this._popup)&&this._popup._source===e?this.closePopup():this.openPopup(e,t.latlng))},_movePopup:function(t){this._popup.setLatLng(t.latlng)},_onKeyPress:function(t){13===t.originalEvent.keyCode&&this._openPopup(t)}});var Xi=Yi.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,interactive:!1,opacity:.9},onAdd:function(t){Yi.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&this._source.fire("tooltipopen",{tooltip:this},!0)},onRemove:function(t){Yi.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&this._source.fire("tooltipclose",{tooltip:this},!0)},getEvents:function(){var t=Yi.prototype.getEvents.call(this);return Ct&&!this.options.permanent&&(t.preclick=this._close),t},_close:function(){this._map&&this._map.closeTooltip(this)},_initLayout:function(){var t="leaflet-tooltip "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=Le("div",t)},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(t){var e=this._map,i=this._container,o=e.latLngToContainerPoint(e.getCenter()),n=e.layerPointToContainerPoint(t),a=this.options.direction,r=i.offsetWidth,s=i.offsetHeight,l=O(this.options.offset),h=this._getAnchor();"top"===a?t=t.add(O(-r/2+l.x,-s+l.y+h.y,!0)):"bottom"===a?t=t.subtract(O(r/2-l.x,-l.y,!0)):"center"===a?t=t.subtract(O(r/2+l.x,s/2-h.y+l.y,!0)):"right"===a||"auto"===a&&n.x<o.x?(a="right",t=t.add(O(l.x+h.x,h.y-s/2+l.y,!0))):(a="left",t=t.subtract(O(r+h.x-l.x,s/2-h.y-l.y,!0))),Se(i,"leaflet-tooltip-right"),Se(i,"leaflet-tooltip-left"),Se(i,"leaflet-tooltip-top"),Se(i,"leaflet-tooltip-bottom"),ze(i,"leaflet-tooltip-"+a),Re(i,t)},_updatePosition:function(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity:function(t){this.options.opacity=t,this._container&&Oe(this._container,t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center);this._setPosition(e)},_getAnchor:function(){return O(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}});Ge.include({openTooltip:function(t,e,i){return t instanceof Xi||(t=new Xi(i).setContent(t)),e&&t.setLatLng(e),this.hasLayer(t)?this:this.addLayer(t)},closeTooltip:function(t){return t&&this.removeLayer(t),this}}),wi.include({bindTooltip:function(t,e){return t instanceof Xi?(f(t,e),this._tooltip=t,t._source=this):(this._tooltip&&!e||(this._tooltip=new Xi(e,this)),this._tooltip.setContent(t)),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(t){if(t||!this._tooltipHandlersAdded){var e=t?"off":"on",i={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?i.add=this._openTooltip:(i.mouseover=this._openTooltip,i.mouseout=this.closeTooltip,this._tooltip.options.sticky&&(i.mousemove=this._moveTooltip),Ct&&(i.click=this._openTooltip)),this[e](i),this._tooltipHandlersAdded=!t}},openTooltip:function(t,e){if(t instanceof wi||(e=t,t=this),t instanceof Bi)for(var i in this._layers){t=this._layers[i];break}return e||(e=t.getCenter?t.getCenter():t.getLatLng()),this._tooltip&&this._map&&(this._tooltip._source=t,this._tooltip.update(),this._map.openTooltip(this._tooltip,e),this._tooltip.options.interactive&&this._tooltip._container&&(ze(this._tooltip._container,"leaflet-clickable"),this.addInteractiveTarget(this._tooltip._container))),this},closeTooltip:function(){return this._tooltip&&(this._tooltip._close(),this._tooltip.options.interactive&&this._tooltip._container&&(Se(this._tooltip._container,"leaflet-clickable"),this.removeInteractiveTarget(this._tooltip._container))),this},toggleTooltip:function(t){return this._tooltip&&(this._tooltip._map?this.closeTooltip():this.openTooltip(t)),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip:function(){return this._tooltip},_openTooltip:function(t){var e=t.layer||t.target;this._tooltip&&this._map&&this.openTooltip(e,this._tooltip.options.sticky?t.latlng:void 0)},_moveTooltip:function(t){var e,i,o=t.latlng;this._tooltip.options.sticky&&t.originalEvent&&(e=this._map.mouseEventToContainerPoint(t.originalEvent),i=this._map.containerPointToLayerPoint(e),o=this._map.layerPointToLatLng(i)),this._tooltip.setLatLng(o)}});var Ji=Pi.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(t){var e=t&&"DIV"===t.tagName?t:document.createElement("div"),i=this.options;if(e.innerHTML=!1!==i.html?i.html:"",i.bgPos){var o=O(i.bgPos);e.style.backgroundPosition=-o.x+"px "+-o.y+"px"}return this._setIconStyles(e,"icon"),e},createShadow:function(){return null}});Pi.Default=Mi;var $i=wi.extend({options:{tileSize:256,opacity:1,updateWhenIdle:vt,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(t){f(this,t)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView(),this._update()},beforeAdd:function(t){t._addZoomLimit(this)},onRemove:function(t){this._removeAllTiles(),Be(this._container),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Me(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(Te(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){return this._map&&(this._removeAllTiles(),this._update()),this},getEvents:function(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=l(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile:function(){return document.createElement("div")},getTileSize:function(){var t=this.options.tileSize;return t instanceof E?t:new E(t,t)},_updateZIndex:function(){this._container&&void 0!==this.options.zIndex&&null!==this.options.zIndex&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(t){for(var e,i=this.getPane().children,o=-t(-1/0,1/0),n=0,a=i.length;n<a;n++)e=i[n].style.zIndex,i[n]!==this._container&&e&&(o=t(o,+e));isFinite(o)&&(this.options.zIndex=o+t(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!tt){Oe(this._container,this.options.opacity);var t=+new Date,e=!1,i=!1;for(var o in this._tiles){var n=this._tiles[o];if(n.current&&n.loaded){var a=Math.min(1,(t-n.loaded)/200);Oe(n.el,a),a<1?e=!0:(n.active?i=!0:this._onOpaqueTile(n),n.active=!0)}}i&&!this._noPrune&&this._pruneTiles(),e&&(M(this._fadeFrame),this._fadeFrame=P(this._updateOpacity,this))}},_onOpaqueTile:u,_initContainer:function(){this._container||(this._container=Le("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var t=this._tileZoom,e=this.options.maxZoom;if(void 0!==t){for(var i in this._levels)this._levels[i].el.children.length||i===t?(this._levels[i].el.style.zIndex=e-Math.abs(t-i),this._onUpdateLevel(i)):(Be(this._levels[i].el),this._removeTilesAtZoom(i),this._onRemoveLevel(i),delete this._levels[i]);var o=this._levels[t],n=this._map;return o||((o=this._levels[t]={}).el=Le("div","leaflet-tile-container leaflet-zoom-animated",this._container),o.el.style.zIndex=e,o.origin=n.project(n.unproject(n.getPixelOrigin()),t).round(),o.zoom=t,this._setZoomTransform(o,n.getCenter(),n.getZoom()),o.el.offsetWidth,this._onCreateLevel(o)),this._level=o,o}},_onUpdateLevel:u,_onRemoveLevel:u,_onCreateLevel:u,_pruneTiles:function(){if(this._map){var t,e,i=this._map.getZoom();if(i>this.options.maxZoom||i<this.options.minZoom)this._removeAllTiles();else{for(t in this._tiles)(e=this._tiles[t]).retain=e.current;for(t in this._tiles)if((e=this._tiles[t]).current&&!e.active){var o=e.coords;this._retainParent(o.x,o.y,o.z,o.z-5)||this._retainChildren(o.x,o.y,o.z,o.z+2)}for(t in this._tiles)this._tiles[t].retain||this._removeTile(t)}}},_removeTilesAtZoom:function(t){for(var e in this._tiles)this._tiles[e].coords.z===t&&this._removeTile(e)},_removeAllTiles:function(){for(var t in this._tiles)this._removeTile(t)},_invalidateAll:function(){for(var t in this._levels)Be(this._levels[t].el),this._onRemoveLevel(t),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(t,e,i,o){var n=Math.floor(t/2),a=Math.floor(e/2),r=i-1,s=new E(+n,+a);s.z=+r;var l=this._tileCoordsToKey(s),h=this._tiles[l];return h&&h.active?(h.retain=!0,!0):(h&&h.loaded&&(h.retain=!0),r>o&&this._retainParent(n,a,r,o))},_retainChildren:function(t,e,i,o){for(var n=2*t;n<2*t+2;n++)for(var a=2*e;a<2*e+2;a++){var r=new E(n,a);r.z=i+1;var s=this._tileCoordsToKey(r),l=this._tiles[s];l&&l.active?l.retain=!0:(l&&l.loaded&&(l.retain=!0),i+1<o&&this._retainChildren(n,a,i+1,o))}},_resetView:function(t){var e=t&&(t.pinch||t.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),e,e)},_animateZoom:function(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom:function(t){var e=this.options;return void 0!==e.minNativeZoom&&t<e.minNativeZoom?e.minNativeZoom:void 0!==e.maxNativeZoom&&e.maxNativeZoom<t?e.maxNativeZoom:t},_setView:function(t,e,i,o){var n=this._clampZoom(Math.round(e));(void 0!==this.options.maxZoom&&n>this.options.maxZoom||void 0!==this.options.minZoom&&n<this.options.minZoom)&&(n=void 0);var a=this.options.updateWhenZooming&&n!==this._tileZoom;o&&!a||(this._tileZoom=n,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),void 0!==n&&this._update(t),i||this._pruneTiles(),this._noPrune=!!i),this._setZoomTransforms(t,e)},_setZoomTransforms:function(t,e){for(var i in this._levels)this._setZoomTransform(this._levels[i],t,e)},_setZoomTransform:function(t,e,i){var o=this._map.getZoomScale(i,t.zoom),n=t.origin.multiplyBy(o).subtract(this._map._getNewPixelOrigin(e,i)).round();gt?De(t.el,n,o):Re(t.el,n)},_resetGrid:function(){var t=this._map,e=t.options.crs,i=this._tileSize=this.getTileSize(),o=this._tileZoom,n=this._map.getPixelWorldBounds(this._tileZoom);n&&(this._globalTileRange=this._pxBoundsToTileRange(n)),this._wrapX=e.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,e.wrapLng[0]],o).x/i.x),Math.ceil(t.project([0,e.wrapLng[1]],o).x/i.y)],this._wrapY=e.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([e.wrapLat[0],0],o).y/i.x),Math.ceil(t.project([e.wrapLat[1],0],o).y/i.y)]},_onMoveEnd:function(){this._map&&!this._map._animatingZoom&&this._update()},_getTiledPixelBounds:function(t){var e=this._map,i=e._animatingZoom?Math.max(e._animateToZoom,e.getZoom()):e.getZoom(),o=e.getZoomScale(i,this._tileZoom),n=e.project(t,this._tileZoom).floor(),a=e.getSize().divideBy(2*o);return new I(n.subtract(a),n.add(a))},_update:function(t){var e=this._map;if(e){var i=this._clampZoom(e.getZoom());if(void 0===t&&(t=e.getCenter()),void 0!==this._tileZoom){var o=this._getTiledPixelBounds(t),n=this._pxBoundsToTileRange(o),a=n.getCenter(),r=[],s=this.options.keepBuffer,l=new I(n.getBottomLeft().subtract([s,-s]),n.getTopRight().add([s,-s]));if(!(isFinite(n.min.x)&&isFinite(n.min.y)&&isFinite(n.max.x)&&isFinite(n.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var h in this._tiles){var u=this._tiles[h].coords;u.z===this._tileZoom&&l.contains(new E(u.x,u.y))||(this._tiles[h].current=!1)}if(Math.abs(i-this._tileZoom)>1)this._setView(t,i);else{for(var c=n.min.y;c<=n.max.y;c++)for(var d=n.min.x;d<=n.max.x;d++){var p=new E(d,c);if(p.z=this._tileZoom,this._isValidTile(p)){var f=this._tiles[this._tileCoordsToKey(p)];f?f.current=!0:r.push(p)}}if(r.sort((function(t,e){return t.distanceTo(a)-e.distanceTo(a)})),0!==r.length){this._loading||(this._loading=!0,this.fire("loading"));var m=document.createDocumentFragment();for(d=0;d<r.length;d++)this._addTile(r[d],m);this._level.el.appendChild(m)}}}}},_isValidTile:function(t){var e=this._map.options.crs;if(!e.infinite){var i=this._globalTileRange;if(!e.wrapLng&&(t.x<i.min.x||t.x>i.max.x)||!e.wrapLat&&(t.y<i.min.y||t.y>i.max.y))return!1}if(!this.options.bounds)return!0;var o=this._tileCoordsToBounds(t);return N(this.options.bounds).overlaps(o)},_keyToBounds:function(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe:function(t){var e=this._map,i=this.getTileSize(),o=t.scaleBy(i),n=o.add(i);return[e.unproject(o,t.z),e.unproject(n,t.z)]},_tileCoordsToBounds:function(t){var e=this._tileCoordsToNwSe(t),i=new R(e[0],e[1]);return this.options.noWrap||(i=this._map.wrapLatLngBounds(i)),i},_tileCoordsToKey:function(t){return t.x+":"+t.y+":"+t.z},_keyToTileCoords:function(t){var e=t.split(":"),i=new E(+e[0],+e[1]);return i.z=+e[2],i},_removeTile:function(t){var e=this._tiles[t];e&&(rt||e.el.setAttribute("src",y),Be(e.el),delete this._tiles[t],this.fire("tileunload",{tile:e.el,coords:this._keyToTileCoords(t)}))},_initTile:function(t){ze(t,"leaflet-tile");var e=this.getTileSize();t.style.width=e.x+"px",t.style.height=e.y+"px",t.onselectstart=u,t.onmousemove=u,tt&&this.options.opacity<1&&Oe(t,this.options.opacity),ot&&!nt&&(t.style.WebkitBackfaceVisibility="hidden")},_addTile:function(t,e){var i=this._getTilePos(t),o=this._tileCoordsToKey(t),a=this.createTile(this._wrapCoords(t),n(this._tileReady,this,t));this._initTile(a),this.createTile.length<2&&P(n(this._tileReady,this,t,null,a)),Re(a,i),this._tiles[o]={el:a,coords:t,current:!0},e.appendChild(a),this.fire("tileloadstart",{tile:a,coords:t})},_tileReady:function(t,e,i){if(this._map){e&&this.fire("tileerror",{error:e,tile:i,coords:t});var o=this._tileCoordsToKey(t);(i=this._tiles[o])&&(i.loaded=+new Date,this._map._fadeAnimated?(Oe(i.el,0),M(this._fadeFrame),this._fadeFrame=P(this._updateOpacity,this)):(i.active=!0,this._pruneTiles()),e||(ze(i.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:i.el,coords:t})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),tt||!this._map._fadeAnimated?P(this._pruneTiles,this):setTimeout(n(this._pruneTiles,this),250)))}},_getTilePos:function(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(t){var e=new E(this._wrapX?h(t.x,this._wrapX):t.x,this._wrapY?h(t.y,this._wrapY):t.y);return e.z=t.z,e},_pxBoundsToTileRange:function(t){var e=this.getTileSize();return new I(t.min.unscaleBy(e).floor(),t.max.unscaleBy(e).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var t in this._tiles)if(!this._tiles[t].loaded)return!1;return!0}}),to=$i.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1},initialize:function(t,e){this._url=t,(e=f(this,e)).detectRetina&&Bt&&e.maxZoom>0&&(e.tileSize=Math.floor(e.tileSize/2),e.zoomReverse?(e.zoomOffset--,e.minZoom++):(e.zoomOffset++,e.maxZoom--),e.minZoom=Math.max(0,e.minZoom)),"string"==typeof e.subdomains&&(e.subdomains=e.subdomains.split("")),ot||this.on("tileunload",this._onTileRemove)},setUrl:function(t,e){return this._url=t,e||this.redraw(),this},createTile:function(t,e){var i=document.createElement("img");return Qt(i,"load",n(this._tileOnLoad,this,e,i)),Qt(i,"error",n(this._tileOnError,this,e,i)),this.options.crossOrigin&&(i.crossOrigin=""),i.alt="",i.setAttribute("role","presentation"),i.src=this.getTileUrl(t),i},getTileUrl:function(t){var e={r:Bt?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var o=this._globalTileRange.max.y-t.y;this.options.tms&&(e.y=o),e["-y"]=o}return g(this._url,i(e,this.options))},_tileOnLoad:function(t,e){tt?setTimeout(n(t,this,null,e),0):t(null,e)},_tileOnError:function(t,e,i){var o=this.options.errorTileUrl;o&&e.getAttribute("src")!==o&&(e.src=o),t(i,e)},_onTileRemove:function(t){t.tile.onload=null},_getZoomForUrl:function(){var t=this._tileZoom,e=this.options.maxZoom;return this.options.zoomReverse&&(t=e-t),t+this.options.zoomOffset},_getSubdomain:function(t){var e=Math.abs(t.x+t.y)%this.options.subdomains.length;return this.options.subdomains[e]},_abortLoading:function(){var t,e;for(t in this._tiles)this._tiles[t].coords.z!==this._tileZoom&&((e=this._tiles[t].el).onload=u,e.onerror=u,e.complete||(e.src=y,Be(e),delete this._tiles[t]))}});function eo(t,e){return new to(t,e)}var io=to.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(t,e){this._url=t;var o=i({},this.defaultWmsParams);for(var n in e)n in this.options||(o[n]=e[n]);var a=(e=f(this,e)).detectRetina&&Bt?2:1,r=this.getTileSize();o.width=r.x*a,o.height=r.y*a,this.wmsParams=o},onAdd:function(t){this._crs=this.options.crs||t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var e=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[e]=this._crs.code,to.prototype.onAdd.call(this,t)},getTileUrl:function(t){var e=this._tileCoordsToNwSe(t),i=this._crs,o=D(i.project(e[0]),i.project(e[1])),n=o.min,a=o.max,r=(this._wmsVersion>=1.3&&this._crs===xi?[n.y,n.x,a.y,a.x]:[n.x,n.y,a.x,a.y]).join(","),s=L.TileLayer.prototype.getTileUrl.call(this,t);return s+m(this.wmsParams,s,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+r},setParams:function(t,e){return i(this.wmsParams,t),e||this.redraw(),this}});to.WMS=io,eo.wms=function(t,e){return new io(t,e)};var oo=wi.extend({options:{padding:.1,tolerance:0},initialize:function(t){f(this,t),r(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),this._zoomAnimated&&ze(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),t},_onAnimZoom:function(t){this._updateTransform(t.center,t.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(t,e){var i=this._map.getZoomScale(e,this._zoom),o=Ne(this._container),n=this._map.getSize().multiplyBy(.5+this.options.padding),a=this._map.project(this._center,e),r=this._map.project(t,e).subtract(a),s=n.multiplyBy(-i).add(o).add(n).subtract(r);gt?De(this._container,s,i):Re(this._container,s)},_reset:function(){for(var t in this._update(),this._updateTransform(this._center,this._zoom),this._layers)this._layers[t]._reset()},_onZoomEnd:function(){for(var t in this._layers)this._layers[t]._project()},_updatePaths:function(){for(var t in this._layers)this._layers[t]._update()},_update:function(){var t=this.options.padding,e=this._map.getSize(),i=this._map.containerPointToLayerPoint(e.multiplyBy(-t)).round();this._bounds=new I(i,i.add(e.multiplyBy(1+2*t)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),no=oo.extend({getEvents:function(){var t=oo.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){oo.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var t=this._container=document.createElement("canvas");Qt(t,"mousemove",l(this._onMouseMove,32,this),this),Qt(t,"click dblclick mousedown mouseup contextmenu",this._onClick,this),Qt(t,"mouseout",this._handleMouseOut,this),this._ctx=t.getContext("2d")},_destroyContainer:function(){delete this._ctx,Be(this._container),Jt(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){for(var t in this._redrawBounds=null,this._layers)this._layers[t]._update();this._redraw()}},_update:function(){if(!this._map._animatingZoom||!this._bounds){this._drawnLayers={},oo.prototype._update.call(this);var t=this._bounds,e=this._container,i=t.getSize(),o=Bt?2:1;Re(e,t.min),e.width=o*i.x,e.height=o*i.y,e.style.width=i.x+"px",e.style.height=i.y+"px",Bt&&this._ctx.scale(2,2),this._ctx.translate(-t.min.x,-t.min.y),this.fire("update")}},_reset:function(){oo.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(t){this._updateDashArray(t),this._layers[r(t)]=t;var e=t._order={layer:t,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=e),this._drawLast=e,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(t){this._requestRedraw(t)},_removePath:function(t){var e=t._order,i=e.next,o=e.prev;i?i.prev=o:this._drawLast=o,o?o.next=i:this._drawFirst=i,delete t._order,delete this._layers[L.stamp(t)],this._requestRedraw(t)},_updatePath:function(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle:function(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray:function(t){if(t.options.dashArray){var e,i=t.options.dashArray.split(","),o=[];for(e=0;e<i.length;e++)o.push(Number(i[e]));t.options._dashArray=o}},_requestRedraw:function(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest=this._redrawRequest||P(this._redraw,this))},_extendRedrawBounds:function(t){if(t._pxBounds){var e=(t.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new I,this._redrawBounds.extend(t._pxBounds.min.subtract([e,e])),this._redrawBounds.extend(t._pxBounds.max.add([e,e]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var t=this._redrawBounds;if(t){var e=t.getSize();this._ctx.clearRect(t.min.x,t.min.y,e.x,e.y)}else this._ctx.clearRect(0,0,this._container.width,this._container.height)},_draw:function(){var t,e=this._redrawBounds;if(this._ctx.save(),e){var i=e.getSize();this._ctx.beginPath(),this._ctx.rect(e.min.x,e.min.y,i.x,i.y),this._ctx.clip()}this._drawing=!0;for(var o=this._drawFirst;o;o=o.next)t=o.layer,(!e||t._pxBounds&&t._pxBounds.intersects(e))&&t._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(t,e){if(this._drawing){var i,o,n,a,r=t._parts,s=r.length,l=this._ctx;if(s){for(this._drawnLayers[t._leaflet_id]=t,l.beginPath(),i=0;i<s;i++){for(o=0,n=r[i].length;o<n;o++)a=r[i][o],l[o?"lineTo":"moveTo"](a.x,a.y);e&&l.closePath()}this._fillStroke(l,t)}}},_updateCircle:function(t){if(this._drawing&&!t._empty()){var e=t._point,i=this._ctx,o=Math.max(Math.round(t._radius),1),n=(Math.max(Math.round(t._radiusY),1)||o)/o;this._drawnLayers[t._leaflet_id]=t,1!==n&&(i.save(),i.scale(1,n)),i.beginPath(),i.arc(e.x,e.y/n,o,0,2*Math.PI,!1),1!==n&&i.restore(),this._fillStroke(i,t)}},_fillStroke:function(t,e){var i=e.options;i.fill&&(t.globalAlpha=i.fillOpacity,t.fillStyle=i.fillColor||i.color,t.fill(i.fillRule||"evenodd")),i.stroke&&0!==i.weight&&(t.setLineDash&&t.setLineDash(e.options&&e.options._dashArray||[]),t.globalAlpha=i.opacity,t.lineWidth=i.weight,t.strokeStyle=i.color,t.lineCap=i.lineCap,t.lineJoin=i.lineJoin,t.stroke())},_onClick:function(t){for(var e,i,o=this._map.mouseEventToLayerPoint(t),n=this._drawFirst;n;n=n.next)(e=n.layer).options.interactive&&e._containsPoint(o)&&!this._map._draggableMoved(e)&&(i=e);i&&(ce(t),this._fireEvent([i],t))},_onMouseMove:function(t){if(this._map&&!this._map.dragging.moving()&&!this._map._animatingZoom){var e=this._map.mouseEventToLayerPoint(t);this._handleMouseHover(t,e)}},_handleMouseOut:function(t){var e=this._hoveredLayer;e&&(Se(this._container,"leaflet-interactive"),this._fireEvent([e],t,"mouseout"),this._hoveredLayer=null)},_handleMouseHover:function(t,e){for(var i,o,n=this._drawFirst;n;n=n.next)(i=n.layer).options.interactive&&i._containsPoint(e)&&(o=i);o!==this._hoveredLayer&&(this._handleMouseOut(t),o&&(ze(this._container,"leaflet-interactive"),this._fireEvent([o],t,"mouseover"),this._hoveredLayer=o)),this._hoveredLayer&&this._fireEvent([this._hoveredLayer],t)},_fireEvent:function(t,e,i){this._map._fireDOMEvent(e,i||e.type,t)},_bringToFront:function(t){var e=t._order,i=e.next,o=e.prev;i&&(i.prev=o,o?o.next=i:i&&(this._drawFirst=i),e.prev=this._drawLast,this._drawLast.next=e,e.next=null,this._drawLast=e,this._requestRedraw(t))},_bringToBack:function(t){var e=t._order,i=e.next,o=e.prev;o&&(o.next=i,i?i.prev=o:o&&(this._drawLast=o),e.prev=null,e.next=this._drawFirst,this._drawFirst.prev=e,this._drawFirst=e,this._requestRedraw(t))}});function ao(t){return Pt?new no(t):null}var ro=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<lvml:"+t+' class="lvml">')}}catch(t){return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}}(),so={_initContainer:function(){this._container=Le("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(oo.prototype._update.call(this),this.fire("update"))},_initPath:function(t){var e=t._container=ro("shape");ze(e,"leaflet-vml-shape "+(this.options.className||"")),e.coordsize="1 1",t._path=ro("path"),e.appendChild(t._path),this._updateStyle(t),this._layers[r(t)]=t},_addPath:function(t){var e=t._container;this._container.appendChild(e),t.options.interactive&&t.addInteractiveTarget(e)},_removePath:function(t){var e=t._container;Be(e),t.removeInteractiveTarget(e),delete this._layers[r(t)]},_updateStyle:function(t){var e=t._stroke,i=t._fill,o=t.options,n=t._container;n.stroked=!!o.stroke,n.filled=!!o.fill,o.stroke?(e||(e=t._stroke=ro("stroke")),n.appendChild(e),e.weight=o.weight+"px",e.color=o.color,e.opacity=o.opacity,o.dashArray?e.dashStyle=v(o.dashArray)?o.dashArray.join(" "):o.dashArray.replace(/( *, *)/g," "):e.dashStyle="",e.endcap=o.lineCap.replace("butt","flat"),e.joinstyle=o.lineJoin):e&&(n.removeChild(e),t._stroke=null),o.fill?(i||(i=t._fill=ro("fill")),n.appendChild(i),i.color=o.fillColor||o.color,i.opacity=o.fillOpacity):i&&(n.removeChild(i),t._fill=null)},_updateCircle:function(t){var e=t._point.round(),i=Math.round(t._radius),o=Math.round(t._radiusY||i);this._setPath(t,t._empty()?"M0 0":"AL "+e.x+","+e.y+" "+i+","+o+" 0,23592600")},_setPath:function(t,e){t._path.v=e},_bringToFront:function(t){Me(t._container)},_bringToBack:function(t){Te(t._container)}},lo=Tt?ro:Q,ho=oo.extend({getEvents:function(){var t=oo.prototype.getEvents.call(this);return t.zoomstart=this._onZoomStart,t},_initContainer:function(){this._container=lo("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=lo("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){Be(this._container),Jt(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_onZoomStart:function(){this._update()},_update:function(){if(!this._map._animatingZoom||!this._bounds){oo.prototype._update.call(this);var t=this._bounds,e=t.getSize(),i=this._container;this._svgSize&&this._svgSize.equals(e)||(this._svgSize=e,i.setAttribute("width",e.x),i.setAttribute("height",e.y)),Re(i,t.min),i.setAttribute("viewBox",[t.min.x,t.min.y,e.x,e.y].join(" ")),this.fire("update")}},_initPath:function(t){var e=t._path=lo("path");t.options.className&&ze(e,t.options.className),t.options.interactive&&ze(e,"leaflet-interactive"),this._updateStyle(t),this._layers[r(t)]=t},_addPath:function(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath:function(t){Be(t._path),t.removeInteractiveTarget(t._path),delete this._layers[r(t)]},_updatePath:function(t){t._project(),t._update()},_updateStyle:function(t){var e=t._path,i=t.options;e&&(i.stroke?(e.setAttribute("stroke",i.color),e.setAttribute("stroke-opacity",i.opacity),e.setAttribute("stroke-width",i.weight),e.setAttribute("stroke-linecap",i.lineCap),e.setAttribute("stroke-linejoin",i.lineJoin),i.dashArray?e.setAttribute("stroke-dasharray",i.dashArray):e.removeAttribute("stroke-dasharray"),i.dashOffset?e.setAttribute("stroke-dashoffset",i.dashOffset):e.removeAttribute("stroke-dashoffset")):e.setAttribute("stroke","none"),i.fill?(e.setAttribute("fill",i.fillColor||i.color),e.setAttribute("fill-opacity",i.fillOpacity),e.setAttribute("fill-rule",i.fillRule||"evenodd")):e.setAttribute("fill","none"))},_updatePoly:function(t,e){this._setPath(t,X(t._parts,e))},_updateCircle:function(t){var e=t._point,i=Math.max(Math.round(t._radius),1),o="a"+i+","+(Math.max(Math.round(t._radiusY),1)||i)+" 0 1,0 ",n=t._empty()?"M0 0":"M"+(e.x-i)+","+e.y+o+2*i+",0 "+o+2*-i+",0 ";this._setPath(t,n)},_setPath:function(t,e){t._path.setAttribute("d",e)},_bringToFront:function(t){Me(t._path)},_bringToBack:function(t){Te(t._path)}});function uo(t){return Mt||Tt?new ho(t):null}Tt&&ho.include(so),Ge.include({getRenderer:function(t){var e=t.options.renderer||this._getPaneRenderer(t.options.pane)||this.options.renderer||this._renderer;return e||(e=this._renderer=this.options.preferCanvas&&ao()||uo()),this.hasLayer(e)||this.addLayer(e),e},_getPaneRenderer:function(t){if("overlayPane"===t||void 0===t)return!1;var e=this._paneRenderers[t];return void 0===e&&(e=ho&&uo({pane:t})||no&&ao({pane:t}),this._paneRenderers[t]=e),e}});var co=Oi.extend({initialize:function(t,e){Oi.prototype.initialize.call(this,this._boundsToLatLngs(t),e)},setBounds:function(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs:function(t){return[(t=N(t)).getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});ho.create=lo,ho.pointsToPath=X,Ii.geometryToLayer=Di,Ii.coordsToLatLng=Ri,Ii.coordsToLatLngs=Ni,Ii.latLngToCoords=ji,Ii.latLngsToCoords=Wi,Ii.getFeature=Ui,Ii.asFeature=Hi,Ge.mergeOptions({boxZoom:!0});var po=ti.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks:function(){Qt(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){Jt(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){Be(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){0!==this._resetStateTimeout&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(t){if(!t.shiftKey||1!==t.which&&1!==t.button)return!1;this._clearDeferredResetState(),this._resetState(),fe(),We(),this._startPoint=this._map.mouseEventToContainerPoint(t),Qt(document,{contextmenu:ae,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(t){this._moved||(this._moved=!0,this._box=Le("div","leaflet-zoom-box",this._container),ze(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(t);var e=new I(this._point,this._startPoint),i=e.getSize();Re(this._box,e.min),this._box.style.width=i.x+"px",this._box.style.height=i.y+"px"},_finish:function(){this._moved&&(Be(this._box),Se(this._container,"leaflet-crosshair")),me(),Ue(),Jt(document,{contextmenu:ae,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(t){if((1===t.which||1===t.button)&&(this._finish(),this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(n(this._resetState,this),0);var e=new R(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(e).fire("boxzoomend",{boxZoomBounds:e})}},_onKeyDown:function(t){27===t.keyCode&&this._finish()}});Ge.addInitHook("addHandler","boxZoom",po),Ge.mergeOptions({doubleClickZoom:!0});var fo=ti.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(t){var e=this._map,i=e.getZoom(),o=e.options.zoomDelta,n=t.originalEvent.shiftKey?i-o:i+o;"center"===e.options.doubleClickZoom?e.setZoom(n):e.setZoomAround(t.containerPoint,n)}});Ge.addInitHook("addHandler","doubleClickZoom",fo),Ge.mergeOptions({dragging:!0,inertia:!nt,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var mo=ti.extend({addHooks:function(){if(!this._draggable){var t=this._map;this._draggable=new ri(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))}ze(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){Se(this._map._container,"leaflet-grab"),Se(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var t=this._map;if(t._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var e=N(this._map.options.maxBounds);this._offsetLimit=D(this._map.latLngToContainerPoint(e.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(e.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;t.fire("movestart").fire("dragstart"),t.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(t){if(this._map.options.inertia){var e=this._lastTime=+new Date,i=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(i),this._times.push(e),this._prunePositions(e)}this._map.fire("move",t).fire("drag",t)},_prunePositions:function(t){for(;this._positions.length>1&&t-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var t=this._map.getSize().divideBy(2),e=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=e.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(t,e){return t-(t-e)*this._viscosity},_onPreDragLimit:function(){if(this._viscosity&&this._offsetLimit){var t=this._draggable._newPos.subtract(this._draggable._startPos),e=this._offsetLimit;t.x<e.min.x&&(t.x=this._viscousLimit(t.x,e.min.x)),t.y<e.min.y&&(t.y=this._viscousLimit(t.y,e.min.y)),t.x>e.max.x&&(t.x=this._viscousLimit(t.x,e.max.x)),t.y>e.max.y&&(t.y=this._viscousLimit(t.y,e.max.y)),this._draggable._newPos=this._draggable._startPos.add(t)}},_onPreDragWrap:function(){var t=this._worldWidth,e=Math.round(t/2),i=this._initialWorldOffset,o=this._draggable._newPos.x,n=(o-e+i)%t+e-i,a=(o+e+i)%t-e-i,r=Math.abs(n+i)<Math.abs(a+i)?n:a;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=r},_onDragEnd:function(t){var e=this._map,i=e.options,o=!i.inertia||this._times.length<2;if(e.fire("dragend",t),o)e.fire("moveend");else{this._prunePositions(+new Date);var n=this._lastPos.subtract(this._positions[0]),a=(this._lastTime-this._times[0])/1e3,r=i.easeLinearity,s=n.multiplyBy(r/a),l=s.distanceTo([0,0]),h=Math.min(i.inertiaMaxSpeed,l),u=s.multiplyBy(h/l),c=h/(i.inertiaDeceleration*r),d=u.multiplyBy(-c/2).round();d.x||d.y?(d=e._limitOffset(d,e.options.maxBounds),P((function(){e.panBy(d,{duration:c,easeLinearity:r,noMoveStart:!0,animate:!0})}))):e.fire("moveend")}}});Ge.addInitHook("addHandler","dragging",mo),Ge.mergeOptions({keyboard:!0,keyboardPanDelta:80});var _o=ti.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks:function(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),Qt(t,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),Jt(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var t=document.body,e=document.documentElement,i=t.scrollTop||e.scrollTop,o=t.scrollLeft||e.scrollLeft;this._map._container.focus(),window.scrollTo(o,i)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(t){var e,i,o=this._panKeys={},n=this.keyCodes;for(e=0,i=n.left.length;e<i;e++)o[n.left[e]]=[-1*t,0];for(e=0,i=n.right.length;e<i;e++)o[n.right[e]]=[t,0];for(e=0,i=n.down.length;e<i;e++)o[n.down[e]]=[0,t];for(e=0,i=n.up.length;e<i;e++)o[n.up[e]]=[0,-1*t]},_setZoomDelta:function(t){var e,i,o=this._zoomKeys={},n=this.keyCodes;for(e=0,i=n.zoomIn.length;e<i;e++)o[n.zoomIn[e]]=t;for(e=0,i=n.zoomOut.length;e<i;e++)o[n.zoomOut[e]]=-t},_addHooks:function(){Qt(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){Jt(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(t){if(!(t.altKey||t.ctrlKey||t.metaKey)){var e,i=t.keyCode,o=this._map;if(i in this._panKeys){if(o._panAnim&&o._panAnim._inProgress)return;e=this._panKeys[i],t.shiftKey&&(e=O(e).multiplyBy(3)),o.panBy(e),o.options.maxBounds&&o.panInsideBounds(o.options.maxBounds)}else if(i in this._zoomKeys)o.setZoom(o.getZoom()+(t.shiftKey?3:1)*this._zoomKeys[i]);else{if(27!==i||!o._popup||!o._popup.options.closeOnEscapeKey)return;o.closePopup()}ae(t)}}});Ge.addInitHook("addHandler","keyboard",_o),Ge.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var go=ti.extend({addHooks:function(){Qt(this._map._container,"mousewheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){Jt(this._map._container,"mousewheel",this._onWheelScroll,this)},_onWheelScroll:function(t){var e=le(t),i=this._map.options.wheelDebounceTime;this._delta+=e,this._lastMousePos=this._map.mouseEventToContainerPoint(t),this._startTime||(this._startTime=+new Date);var o=Math.max(i-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(n(this._performZoom,this),o),ae(t)},_performZoom:function(){var t=this._map,e=t.getZoom(),i=this._map.options.zoomSnap||0;t._stop();var o=this._delta/(4*this._map.options.wheelPxPerZoomLevel),n=4*Math.log(2/(1+Math.exp(-Math.abs(o))))/Math.LN2,a=i?Math.ceil(n/i)*i:n,r=t._limitZoom(e+(this._delta>0?a:-a))-e;this._delta=0,this._startTime=null,r&&("center"===t.options.scrollWheelZoom?t.setZoom(e+r):t.setZoomAround(this._lastMousePos,e+r))}});Ge.addInitHook("addHandler","scrollWheelZoom",go),Ge.mergeOptions({tap:!0,tapTolerance:15});var vo=ti.extend({addHooks:function(){Qt(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){Jt(this._map._container,"touchstart",this._onDown,this)},_onDown:function(t){if(t.touches){if(ne(t),this._fireClick=!0,t.touches.length>1)return this._fireClick=!1,void clearTimeout(this._holdTimeout);var e=t.touches[0],i=e.target;this._startPos=this._newPos=new E(e.clientX,e.clientY),i.tagName&&"a"===i.tagName.toLowerCase()&&ze(i,"leaflet-active"),this._holdTimeout=setTimeout(n((function(){this._isTapValid()&&(this._fireClick=!1,this._onUp(),this._simulateEvent("contextmenu",e))}),this),1e3),this._simulateEvent("mousedown",e),Qt(document,{touchmove:this._onMove,touchend:this._onUp},this)}},_onUp:function(t){if(clearTimeout(this._holdTimeout),Jt(document,{touchmove:this._onMove,touchend:this._onUp},this),this._fireClick&&t&&t.changedTouches){var e=t.changedTouches[0],i=e.target;i&&i.tagName&&"a"===i.tagName.toLowerCase()&&Se(i,"leaflet-active"),this._simulateEvent("mouseup",e),this._isTapValid()&&this._simulateEvent("click",e)}},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_onMove:function(t){var e=t.touches[0];this._newPos=new E(e.clientX,e.clientY),this._simulateEvent("mousemove",e)},_simulateEvent:function(t,e){var i=document.createEvent("MouseEvents");i._simulated=!0,e.target._simulatedClick=!0,i.initMouseEvent(t,!0,!0,window,1,e.screenX,e.screenY,e.clientX,e.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(i)}});Ct&&!xt&&Ge.addInitHook("addHandler","tap",vo),Ge.mergeOptions({touchZoom:Ct&&!nt,bounceAtZoomLimits:!0});var Ao=ti.extend({addHooks:function(){ze(this._map._container,"leaflet-touch-zoom"),Qt(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){Se(this._map._container,"leaflet-touch-zoom"),Jt(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(t){var e=this._map;if(t.touches&&2===t.touches.length&&!e._animatingZoom&&!this._zooming){var i=e.mouseEventToContainerPoint(t.touches[0]),o=e.mouseEventToContainerPoint(t.touches[1]);this._centerPoint=e.getSize()._divideBy(2),this._startLatLng=e.containerPointToLatLng(this._centerPoint),"center"!==e.options.touchZoom&&(this._pinchStartLatLng=e.containerPointToLatLng(i.add(o)._divideBy(2))),this._startDist=i.distanceTo(o),this._startZoom=e.getZoom(),this._moved=!1,this._zooming=!0,e._stop(),Qt(document,"touchmove",this._onTouchMove,this),Qt(document,"touchend",this._onTouchEnd,this),ne(t)}},_onTouchMove:function(t){if(t.touches&&2===t.touches.length&&this._zooming){var e=this._map,i=e.mouseEventToContainerPoint(t.touches[0]),o=e.mouseEventToContainerPoint(t.touches[1]),a=i.distanceTo(o)/this._startDist;if(this._zoom=e.getScaleZoom(a,this._startZoom),!e.options.bounceAtZoomLimits&&(this._zoom<e.getMinZoom()&&a<1||this._zoom>e.getMaxZoom()&&a>1)&&(this._zoom=e._limitZoom(this._zoom)),"center"===e.options.touchZoom){if(this._center=this._startLatLng,1===a)return}else{var r=i._add(o)._divideBy(2)._subtract(this._centerPoint);if(1===a&&0===r.x&&0===r.y)return;this._center=e.unproject(e.project(this._pinchStartLatLng,this._zoom).subtract(r),this._zoom)}this._moved||(e._moveStart(!0,!1),this._moved=!0),M(this._animRequest);var s=n(e._move,e,this._center,this._zoom,{pinch:!0,round:!1});this._animRequest=P(s,this,!0),ne(t)}},_onTouchEnd:function(){this._moved&&this._zooming?(this._zooming=!1,M(this._animRequest),Jt(document,"touchmove",this._onTouchMove),Jt(document,"touchend",this._onTouchEnd),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))):this._zooming=!1}});Ge.addInitHook("addHandler","touchZoom",Ao),Ge.BoxZoom=po,Ge.DoubleClickZoom=fo,Ge.Drag=mo,Ge.Keyboard=_o,Ge.ScrollWheelZoom=go,Ge.Tap=vo,Ge.TouchZoom=Ao;var yo=window.L;window.L=t,Object.freeze=e,t.version="1.3.1+HEAD.ba6f97f",t.noConflict=function(){return window.L=yo,this},t.Control=Ke,t.control=Ye,t.Browser=zt,t.Evented=S,t.Mixin=ii,t.Util=T,t.Class=k,t.Handler=ti,t.extend=i,t.bind=n,t.stamp=r,t.setOptions=f,t.DomEvent=Ae,t.DomUtil=qe,t.PosAnimation=Ve,t.Draggable=ri,t.LineUtil=mi,t.PolyUtil=gi,t.Point=E,t.point=O,t.Bounds=I,t.bounds=D,t.Transformation=V,t.transformation=G,t.Projection=yi,t.LatLng=j,t.latLng=W,t.LatLngBounds=R,t.latLngBounds=N,t.CRS=H,t.GeoJSON=Ii,t.geoJSON=qi,t.geoJson=Vi,t.Layer=wi,t.LayerGroup=Li,t.layerGroup=function(t,e){return new Li(t,e)},t.FeatureGroup=Bi,t.featureGroup=function(t){return new Bi(t)},t.ImageOverlay=Gi,t.imageOverlay=function(t,e,i){return new Gi(t,e,i)},t.VideoOverlay=Ki,t.videoOverlay=function(t,e,i){return new Ki(t,e,i)},t.DivOverlay=Yi,t.Popup=Qi,t.popup=function(t,e){return new Qi(t,e)},t.Tooltip=Xi,t.tooltip=function(t,e){return new Xi(t,e)},t.Icon=Pi,t.icon=function(t){return new Pi(t)},t.DivIcon=Ji,t.divIcon=function(t){return new Ji(t)},t.Marker=ki,t.marker=function(t,e){return new ki(t,e)},t.TileLayer=to,t.tileLayer=eo,t.GridLayer=$i,t.gridLayer=function(t){return new $i(t)},t.SVG=ho,t.svg=uo,t.Renderer=oo,t.Canvas=no,t.canvas=ao,t.Path=zi,t.CircleMarker=Si,t.circleMarker=function(t,e){return new Si(t,e)},t.Circle=Ei,t.circle=function(t,e,i){return new Ei(t,e,i)},t.Polyline=Zi,t.polyline=function(t,e){return new Zi(t,e)},t.Polygon=Oi,t.polygon=function(t,e){return new Oi(t,e)},t.Rectangle=co,t.rectangle=function(t,e){return new co(t,e)},t.Map=Ge,t.map=function(t,e){return new Ge(t,e)}},"object"===s(e)&&void 0!==t?r(e):(n=[e],void 0===(a="function"==typeof(o=r)?o.apply(e,n):o)||(t.exports=a))},function(t,e,i){t.exports=function(){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}var e=/^\s+/,i=/\s+$/;function o(n,a){if(a=a||{},(n=n||"")instanceof o)return n;if(!(this instanceof o))return new o(n,a);var r=function(o){var n,a,r,s={r:0,g:0,b:0},l=1,h=null,u=null,c=null,d=!1,p=!1;return"string"==typeof o&&(o=function(t){t=t.replace(e,"").replace(i,"").toLowerCase();var o,n=!1;if(y[t])t=y[t],n=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};return(o=E.rgb.exec(t))?{r:o[1],g:o[2],b:o[3]}:(o=E.rgba.exec(t))?{r:o[1],g:o[2],b:o[3],a:o[4]}:(o=E.hsl.exec(t))?{h:o[1],s:o[2],l:o[3]}:(o=E.hsla.exec(t))?{h:o[1],s:o[2],l:o[3],a:o[4]}:(o=E.hsv.exec(t))?{h:o[1],s:o[2],v:o[3]}:(o=E.hsva.exec(t))?{h:o[1],s:o[2],v:o[3],a:o[4]}:(o=E.hex8.exec(t))?{r:L(o[1]),g:L(o[2]),b:L(o[3]),a:T(o[4]),format:n?"name":"hex8"}:(o=E.hex6.exec(t))?{r:L(o[1]),g:L(o[2]),b:L(o[3]),format:n?"name":"hex"}:(o=E.hex4.exec(t))?{r:L(o[1]+""+o[1]),g:L(o[2]+""+o[2]),b:L(o[3]+""+o[3]),a:T(o[4]+""+o[4]),format:n?"name":"hex8"}:!!(o=E.hex3.exec(t))&&{r:L(o[1]+""+o[1]),g:L(o[2]+""+o[2]),b:L(o[3]+""+o[3]),format:n?"name":"hex"}}(o)),"object"==t(o)&&(Z(o.r)&&Z(o.g)&&Z(o.b)?(n=o.r,a=o.g,r=o.b,s={r:255*C(n,255),g:255*C(a,255),b:255*C(r,255)},d=!0,p="%"===String(o.r).substr(-1)?"prgb":"rgb"):Z(o.h)&&Z(o.s)&&Z(o.v)?(h=P(o.s),u=P(o.v),s=function(t,e,i){t=6*C(t,360),e=C(e,100),i=C(i,100);var o=Math.floor(t),n=t-o,a=i*(1-e),r=i*(1-n*e),s=i*(1-(1-n)*e),l=o%6;return{r:255*[i,r,a,a,s,i][l],g:255*[s,i,i,r,a,a][l],b:255*[a,a,s,i,i,r][l]}}(o.h,h,u),d=!0,p="hsv"):Z(o.h)&&Z(o.s)&&Z(o.l)&&(h=P(o.s),c=P(o.l),s=function(t,e,i){var o,n,a;function r(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}if(t=C(t,360),e=C(e,100),i=C(i,100),0===e)o=n=a=i;else{var s=i<.5?i*(1+e):i+e-i*e,l=2*i-s;o=r(l,s,t+1/3),n=r(l,s,t),a=r(l,s,t-1/3)}return{r:255*o,g:255*n,b:255*a}}(o.h,h,c),d=!0,p="hsl"),o.hasOwnProperty("a")&&(l=o.a)),l=x(l),{ok:d,format:o.format||p,r:Math.min(255,Math.max(s.r,0)),g:Math.min(255,Math.max(s.g,0)),b:Math.min(255,Math.max(s.b,0)),a:l}}(n);this._originalInput=n,this._r=r.r,this._g=r.g,this._b=r.b,this._a=r.a,this._roundA=Math.round(100*this._a)/100,this._format=a.format||r.format,this._gradientType=a.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=r.ok}function n(t,e,i){t=C(t,255),e=C(e,255),i=C(i,255);var o,n,a=Math.max(t,e,i),r=Math.min(t,e,i),s=(a+r)/2;if(a==r)o=n=0;else{var l=a-r;switch(n=s>.5?l/(2-a-r):l/(a+r),a){case t:o=(e-i)/l+(e<i?6:0);break;case e:o=(i-t)/l+2;break;case i:o=(t-e)/l+4}o/=6}return{h:o,s:n,l:s}}function a(t,e,i){t=C(t,255),e=C(e,255),i=C(i,255);var o,n,a=Math.max(t,e,i),r=Math.min(t,e,i),s=a,l=a-r;if(n=0===a?0:l/a,a==r)o=0;else{switch(a){case t:o=(e-i)/l+(e<i?6:0);break;case e:o=(i-t)/l+2;break;case i:o=(t-e)/l+4}o/=6}return{h:o,s:n,v:s}}function r(t,e,i,o){var n=[B(Math.round(t).toString(16)),B(Math.round(e).toString(16)),B(Math.round(i).toString(16))];return o&&n[0].charAt(0)==n[0].charAt(1)&&n[1].charAt(0)==n[1].charAt(1)&&n[2].charAt(0)==n[2].charAt(1)?n[0].charAt(0)+n[1].charAt(0)+n[2].charAt(0):n.join("")}function s(t,e,i,o){return[B(M(o)),B(Math.round(t).toString(16)),B(Math.round(e).toString(16)),B(Math.round(i).toString(16))].join("")}function l(t,e){e=0===e?0:e||10;var i=o(t).toHsl();return i.s-=e/100,i.s=w(i.s),o(i)}function h(t,e){e=0===e?0:e||10;var i=o(t).toHsl();return i.s+=e/100,i.s=w(i.s),o(i)}function u(t){return o(t).desaturate(100)}function c(t,e){e=0===e?0:e||10;var i=o(t).toHsl();return i.l+=e/100,i.l=w(i.l),o(i)}function d(t,e){e=0===e?0:e||10;var i=o(t).toRgb();return i.r=Math.max(0,Math.min(255,i.r-Math.round(-e/100*255))),i.g=Math.max(0,Math.min(255,i.g-Math.round(-e/100*255))),i.b=Math.max(0,Math.min(255,i.b-Math.round(-e/100*255))),o(i)}function p(t,e){e=0===e?0:e||10;var i=o(t).toHsl();return i.l-=e/100,i.l=w(i.l),o(i)}function f(t,e){var i=o(t).toHsl(),n=(i.h+e)%360;return i.h=n<0?360+n:n,o(i)}function m(t){var e=o(t).toHsl();return e.h=(e.h+180)%360,o(e)}function _(t,e){if(isNaN(e)||e<=0)throw new Error("Argument to polyad must be a positive number");for(var i=o(t).toHsl(),n=[o(t)],a=360/e,r=1;r<e;r++)n.push(o({h:(i.h+r*a)%360,s:i.s,l:i.l}));return n}function g(t){var e=o(t).toHsl(),i=e.h;return[o(t),o({h:(i+72)%360,s:e.s,l:e.l}),o({h:(i+216)%360,s:e.s,l:e.l})]}function v(t,e,i){e=e||6,i=i||30;var n=o(t).toHsl(),a=360/i,r=[o(t)];for(n.h=(n.h-(a*e>>1)+720)%360;--e;)n.h=(n.h+a)%360,r.push(o(n));return r}function A(t,e){e=e||6;for(var i=o(t).toHsv(),n=i.h,a=i.s,r=i.v,s=[],l=1/e;e--;)s.push(o({h:n,s:a,v:r})),r=(r+l)%1;return s}o.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,e,i,o=this.toRgb();return t=o.r/255,e=o.g/255,i=o.b/255,.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))+.0722*(i<=.03928?i/12.92:Math.pow((i+.055)/1.055,2.4))},setAlpha:function(t){return this._a=x(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=a(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=a(this._r,this._g,this._b),e=Math.round(360*t.h),i=Math.round(100*t.s),o=Math.round(100*t.v);return 1==this._a?"hsv("+e+", "+i+"%, "+o+"%)":"hsva("+e+", "+i+"%, "+o+"%, "+this._roundA+")"},toHsl:function(){var t=n(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=n(this._r,this._g,this._b),e=Math.round(360*t.h),i=Math.round(100*t.s),o=Math.round(100*t.l);return 1==this._a?"hsl("+e+", "+i+"%, "+o+"%)":"hsla("+e+", "+i+"%, "+o+"%, "+this._roundA+")"},toHex:function(t){return r(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return function(t,e,i,o,n){var a=[B(Math.round(t).toString(16)),B(Math.round(e).toString(16)),B(Math.round(i).toString(16)),B(M(o))];return n&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)&&a[3].charAt(0)==a[3].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0):a.join("")}(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(100*C(this._r,255))+"%",g:Math.round(100*C(this._g,255))+"%",b:Math.round(100*C(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+Math.round(100*C(this._r,255))+"%, "+Math.round(100*C(this._g,255))+"%, "+Math.round(100*C(this._b,255))+"%)":"rgba("+Math.round(100*C(this._r,255))+"%, "+Math.round(100*C(this._g,255))+"%, "+Math.round(100*C(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(b[r(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var e="#"+s(this._r,this._g,this._b,this._a),i=e,n=this._gradientType?"GradientType = 1, ":"";if(t){var a=o(t);i="#"+s(a._r,a._g,a._b,a._a)}return"progid:DXImageTransform.Microsoft.gradient("+n+"startColorstr="+e+",endColorstr="+i+")"},toString:function(t){var e=!!t;t=t||this._format;var i=!1,o=this._a<1&&this._a>=0;return e||!o||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(i=this.toRgbString()),"prgb"===t&&(i=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(i=this.toHexString()),"hex3"===t&&(i=this.toHexString(!0)),"hex4"===t&&(i=this.toHex8String(!0)),"hex8"===t&&(i=this.toHex8String()),"name"===t&&(i=this.toName()),"hsl"===t&&(i=this.toHslString()),"hsv"===t&&(i=this.toHsvString()),i||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return o(this.toString())},_applyModification:function(t,e){var i=t.apply(null,[this].concat([].slice.call(e)));return this._r=i._r,this._g=i._g,this._b=i._b,this.setAlpha(i._a),this},lighten:function(){return this._applyModification(c,arguments)},brighten:function(){return this._applyModification(d,arguments)},darken:function(){return this._applyModification(p,arguments)},desaturate:function(){return this._applyModification(l,arguments)},saturate:function(){return this._applyModification(h,arguments)},greyscale:function(){return this._applyModification(u,arguments)},spin:function(){return this._applyModification(f,arguments)},_applyCombination:function(t,e){return t.apply(null,[this].concat([].slice.call(e)))},analogous:function(){return this._applyCombination(v,arguments)},complement:function(){return this._applyCombination(m,arguments)},monochromatic:function(){return this._applyCombination(A,arguments)},splitcomplement:function(){return this._applyCombination(g,arguments)},triad:function(){return this._applyCombination(_,[3])},tetrad:function(){return this._applyCombination(_,[4])}},o.fromRatio=function(e,i){if("object"==t(e)){var n={};for(var a in e)e.hasOwnProperty(a)&&(n[a]="a"===a?e[a]:P(e[a]));e=n}return o(e,i)},o.equals=function(t,e){return!(!t||!e)&&o(t).toRgbString()==o(e).toRgbString()},o.random=function(){return o.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})},o.mix=function(t,e,i){i=0===i?0:i||50;var n=o(t).toRgb(),a=o(e).toRgb(),r=i/100;return o({r:(a.r-n.r)*r+n.r,g:(a.g-n.g)*r+n.g,b:(a.b-n.b)*r+n.b,a:(a.a-n.a)*r+n.a})},o.readability=function(t,e){var i=o(t),n=o(e);return(Math.max(i.getLuminance(),n.getLuminance())+.05)/(Math.min(i.getLuminance(),n.getLuminance())+.05)},o.isReadable=function(t,e,i){var n,a,r,s,l,h=o.readability(t,e);switch(a=!1,(r=i,s=((r=r||{level:"AA",size:"small"}).level||"AA").toUpperCase(),l=(r.size||"small").toLowerCase(),"AA"!==s&&"AAA"!==s&&(s="AA"),"small"!==l&&"large"!==l&&(l="small"),n={level:s,size:l}).level+n.size){case"AAsmall":case"AAAlarge":a=h>=4.5;break;case"AAlarge":a=h>=3;break;case"AAAsmall":a=h>=7}return a},o.mostReadable=function(t,e,i){var n,a,r,s,l=null,h=0;a=(i=i||{}).includeFallbackColors,r=i.level,s=i.size;for(var u=0;u<e.length;u++)(n=o.readability(t,e[u]))>h&&(h=n,l=o(e[u]));return o.isReadable(t,l,{level:r,size:s})||!a?l:(i.includeFallbackColors=!1,o.mostReadable(t,["#fff","#000"],i))};var y=o.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},b=o.hexNames=function(t){var e={};for(var i in t)t.hasOwnProperty(i)&&(e[t[i]]=i);return e}(y);function x(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function C(t,e){(function(t){return"string"==typeof t&&-1!=t.indexOf(".")&&1===parseFloat(t)})(t)&&(t="100%");var i=function(t){return"string"==typeof t&&-1!=t.indexOf("%")}(t);return t=Math.min(e,Math.max(0,parseFloat(t))),i&&(t=parseInt(t*e,10)/100),Math.abs(t-e)<1e-6?1:t%e/parseFloat(e)}function w(t){return Math.min(1,Math.max(0,t))}function L(t){return parseInt(t,16)}function B(t){return 1==t.length?"0"+t:""+t}function P(t){return t<=1&&(t=100*t+"%"),t}function M(t){return Math.round(255*parseFloat(t)).toString(16)}function T(t){return L(t)/255}var k,z,S,E=(z="[\\s|\\(]+("+(k="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+k+")[,|\\s]+("+k+")\\s*\\)?",S="[\\s|\\(]+("+k+")[,|\\s]+("+k+")[,|\\s]+("+k+")[,|\\s]+("+k+")\\s*\\)?",{CSS_UNIT:new RegExp(k),rgb:new RegExp("rgb"+z),rgba:new RegExp("rgba"+S),hsl:new RegExp("hsl"+z),hsla:new RegExp("hsla"+S),hsv:new RegExp("hsv"+z),hsva:new RegExp("hsva"+S),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function Z(t){return!!E.CSS_UNIT.exec(t)}return o}()},function(t,e,i){var o=i(5),n=i(12);"string"==typeof(n=n.__esModule?n.default:n)&&(n=[[t.i,n,""]]);var a={insert:"head",singleton:!1},r=(o(n,a),n.locals?n.locals:{});t.exports=r},function(t,e,i){(e=i(6)(!0)).push([t.i,".mapcontainer{overflow:hidden}.info{padding:6px 8px;font:14px/16px Arial,Helvetica,sans-serif;background:#fff;background:rgba(255,255,255,.8);box-shadow:0 0 15px rgba(0,0,0,.2);border-radius:5px}.info h4{margin:0 0 5px;color:#777}.legend{line-height:18px;color:#555;display:flex;flex-direction:column}.legend-item{white-space:nowrap}.legend i{width:18px;height:18px;float:left;margin-right:8px;opacity:.7}.leaflet-top.leaflet-left,.leaflet-bottom.leaflet-left,.leaflet-bottom.leaflet-right{z-index:1000}.leaflet-popup-content-wrapper,.leaflet-popup-tip{color:#d8d9da !important}","",{version:3,sources:["worldmap-panel.css"],names:[],mappings:"AAAA,cAAc,eAAe,CAAC,MAAM,eAAe,CAAC,yCAAyC,CAAC,eAAe,CAAC,+BAA+B,CAAC,kCAAkC,CAAC,iBAAiB,CAAC,SAAS,cAAc,CAAC,UAAU,CAAC,QAAQ,gBAAgB,CAAC,UAAU,CAAC,YAAY,CAAC,qBAAqB,CAAC,aAAa,kBAAkB,CAAC,UAAU,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,UAAU,CAAC,qFAAqF,YAAY,CAAC,kDAAkD,wBAAwB",file:"worldmap-panel.css",sourcesContent:[".mapcontainer{overflow:hidden}.info{padding:6px 8px;font:14px/16px Arial,Helvetica,sans-serif;background:#fff;background:rgba(255,255,255,.8);box-shadow:0 0 15px rgba(0,0,0,.2);border-radius:5px}.info h4{margin:0 0 5px;color:#777}.legend{line-height:18px;color:#555;display:flex;flex-direction:column}.legend-item{white-space:nowrap}.legend i{width:18px;height:18px;float:left;margin-right:8px;opacity:.7}.leaflet-top.leaflet-left,.leaflet-bottom.leaflet-left,.leaflet-bottom.leaflet-right{z-index:1000}.leaflet-popup-content-wrapper,.leaflet-popup-tip{color:#d8d9da !important}"]}]),t.exports=e},function(t,e,i){var o=i(5),n=i(14);"string"==typeof(n=n.__esModule?n.default:n)&&(n=[[t.i,n,""]]);var a={insert:"head",singleton:!1},r=(o(n,a),n.locals?n.locals:{});t.exports=r},function(t,e,i){var o=i(6),n=i(15),a=i(16),r=i(17),s=i(18);e=o(!0);var l=n(a),h=n(r),u=n(s);e.push([t.i,'.leaflet-pane,.leaflet-tile,.leaflet-marker-icon,.leaflet-marker-shadow,.leaflet-tile-container,.leaflet-pane>svg,.leaflet-pane>canvas,.leaflet-zoom-box,.leaflet-image-layer,.leaflet-layer{position:absolute;left:0;top:0}.leaflet-container{overflow:hidden}.leaflet-tile,.leaflet-marker-icon,.leaflet-marker-shadow{-webkit-user-select:none;-moz-user-select:none;user-select:none;-webkit-user-drag:none}.leaflet-safari .leaflet-tile{image-rendering:-webkit-optimize-contrast}.leaflet-safari .leaflet-tile-container{width:1600px;height:1600px;-webkit-transform-origin:0 0}.leaflet-marker-icon,.leaflet-marker-shadow{display:block}.leaflet-container .leaflet-overlay-pane svg,.leaflet-container .leaflet-marker-pane img,.leaflet-container .leaflet-shadow-pane img,.leaflet-container .leaflet-tile-pane img,.leaflet-container img.leaflet-image-layer{max-width:none !important;max-height:none !important}.leaflet-container.leaflet-touch-zoom{touch-action:pan-x pan-y}.leaflet-container.leaflet-touch-drag{touch-action:none;touch-action:pinch-zoom}.leaflet-container.leaflet-touch-drag.leaflet-touch-zoom{touch-action:none}.leaflet-container{-webkit-tap-highlight-color:transparent}.leaflet-container a{-webkit-tap-highlight-color:rgba(51,181,229,.4)}.leaflet-tile{filter:inherit;visibility:hidden}.leaflet-tile-loaded{visibility:inherit}.leaflet-zoom-box{width:0;height:0;box-sizing:border-box;z-index:800}.leaflet-overlay-pane svg{-moz-user-select:none}.leaflet-pane{z-index:400}.leaflet-tile-pane{z-index:200}.leaflet-overlay-pane{z-index:400}.leaflet-shadow-pane{z-index:500}.leaflet-marker-pane{z-index:600}.leaflet-tooltip-pane{z-index:650}.leaflet-popup-pane{z-index:700}.leaflet-map-pane canvas{z-index:100}.leaflet-map-pane svg{z-index:200}.leaflet-vml-shape{width:1px;height:1px}.lvml{behavior:url(#default#VML);display:inline-block;position:absolute}.leaflet-control{position:relative;z-index:800;pointer-events:visiblePainted;pointer-events:auto}.leaflet-top,.leaflet-bottom{position:absolute;z-index:1000;pointer-events:none}.leaflet-top{top:0}.leaflet-right{right:0}.leaflet-bottom{bottom:0}.leaflet-left{left:0}.leaflet-control{float:left;clear:both}.leaflet-right .leaflet-control{float:right}.leaflet-top .leaflet-control{margin-top:10px}.leaflet-bottom .leaflet-control{margin-bottom:10px}.leaflet-left .leaflet-control{margin-left:10px}.leaflet-right .leaflet-control{margin-right:10px}.leaflet-fade-anim .leaflet-tile{will-change:opacity}.leaflet-fade-anim .leaflet-popup{opacity:0;transition:opacity .2s linear}.leaflet-fade-anim .leaflet-map-pane .leaflet-popup{opacity:1}.leaflet-zoom-animated{transform-origin:0 0}.leaflet-zoom-anim .leaflet-zoom-animated{will-change:transform}.leaflet-zoom-anim .leaflet-zoom-animated{transition:transform .25s cubic-bezier(0, 0, 0.25, 1)}.leaflet-zoom-anim .leaflet-tile,.leaflet-pan-anim .leaflet-tile{transition:none}.leaflet-zoom-anim .leaflet-zoom-hide{visibility:hidden}.leaflet-interactive{cursor:pointer}.leaflet-grab{cursor:-moz-grab}.leaflet-crosshair,.leaflet-crosshair .leaflet-interactive{cursor:crosshair}.leaflet-popup-pane,.leaflet-control{cursor:auto}.leaflet-dragging .leaflet-grab,.leaflet-dragging .leaflet-grab .leaflet-interactive,.leaflet-dragging .leaflet-marker-draggable{cursor:move;cursor:-moz-grabbing}.leaflet-marker-icon,.leaflet-marker-shadow,.leaflet-image-layer,.leaflet-pane>svg path,.leaflet-tile-container{pointer-events:none}.leaflet-marker-icon.leaflet-interactive,.leaflet-image-layer.leaflet-interactive,.leaflet-pane>svg path.leaflet-interactive{pointer-events:visiblePainted;pointer-events:auto}.leaflet-container{background:#ddd;outline:0}.leaflet-container a{color:#0078a8}.leaflet-container a.leaflet-active{outline:2px solid orange}.leaflet-zoom-box{border:2px dotted #38f;background:rgba(255,255,255,.5)}.leaflet-container{font:12px/1.5 "Helvetica Neue",Arial,Helvetica,sans-serif}.leaflet-bar{box-shadow:0 1px 5px rgba(0,0,0,.65);border-radius:4px}.leaflet-bar a,.leaflet-bar a:hover{background-color:#fff;border-bottom:1px solid #ccc;width:26px;height:26px;line-height:26px;display:block;text-align:center;text-decoration:none;color:#000}.leaflet-bar a,.leaflet-control-layers-toggle{background-position:50% 50%;background-repeat:no-repeat;display:block}.leaflet-bar a:hover{background-color:#f4f4f4}.leaflet-bar a:first-child{border-top-left-radius:4px;border-top-right-radius:4px}.leaflet-bar a:last-child{border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-bottom:none}.leaflet-bar a.leaflet-disabled{cursor:default;background-color:#f4f4f4;color:#bbb}.leaflet-touch .leaflet-bar a{width:30px;height:30px;line-height:30px}.leaflet-touch .leaflet-bar a:first-child{border-top-left-radius:2px;border-top-right-radius:2px}.leaflet-touch .leaflet-bar a:last-child{border-bottom-left-radius:2px;border-bottom-right-radius:2px}.leaflet-control-zoom-in,.leaflet-control-zoom-out{font:bold 18px "Lucida Console",Monaco,monospace;text-indent:1px}.leaflet-touch .leaflet-control-zoom-in,.leaflet-touch .leaflet-control-zoom-out{font-size:22px}.leaflet-control-layers{box-shadow:0 1px 5px rgba(0,0,0,.4);background:#fff;border-radius:5px}.leaflet-control-layers-toggle{background-image:url('+l+");width:36px;height:36px}.leaflet-retina .leaflet-control-layers-toggle{background-image:url("+h+");background-size:26px 26px}.leaflet-touch .leaflet-control-layers-toggle{width:44px;height:44px}.leaflet-control-layers .leaflet-control-layers-list,.leaflet-control-layers-expanded .leaflet-control-layers-toggle{display:none}.leaflet-control-layers-expanded .leaflet-control-layers-list{display:block;position:relative}.leaflet-control-layers-expanded{padding:6px 10px 6px 6px;color:#333;background:#fff}.leaflet-control-layers-scrollbar{overflow-y:scroll;overflow-x:hidden;padding-right:5px}.leaflet-control-layers-selector{margin-top:2px;position:relative;top:1px}.leaflet-control-layers label{display:block}.leaflet-control-layers-separator{height:0;border-top:1px solid #ddd;margin:5px -10px 5px -6px}.leaflet-default-icon-path{background-image:url("+u+')}.leaflet-container .leaflet-control-attribution{background:#fff;background:rgba(255,255,255,.7);margin:0}.leaflet-control-attribution,.leaflet-control-scale-line{padding:0 5px;color:#333}.leaflet-control-attribution a{text-decoration:none}.leaflet-control-attribution a:hover{text-decoration:underline}.leaflet-container .leaflet-control-attribution,.leaflet-container .leaflet-control-scale{font-size:11px}.leaflet-left .leaflet-control-scale{margin-left:5px}.leaflet-bottom .leaflet-control-scale{margin-bottom:5px}.leaflet-control-scale-line{border:2px solid #777;border-top:none;line-height:1.1;padding:2px 5px 1px;font-size:11px;white-space:nowrap;overflow:hidden;box-sizing:border-box;background:#fff;background:rgba(255,255,255,.5)}.leaflet-control-scale-line:not(:first-child){border-top:2px solid #777;border-bottom:none;margin-top:-2px}.leaflet-control-scale-line:not(:first-child):not(:last-child){border-bottom:2px solid #777}.leaflet-touch .leaflet-control-attribution,.leaflet-touch .leaflet-control-layers,.leaflet-touch .leaflet-bar{box-shadow:none}.leaflet-touch .leaflet-control-layers,.leaflet-touch .leaflet-bar{border:2px solid rgba(0,0,0,.2);background-clip:padding-box}.leaflet-popup{position:absolute;text-align:center;margin-bottom:20px}.leaflet-popup-content-wrapper{padding:1px;text-align:left;border-radius:12px}.leaflet-popup-content{margin:13px 19px;line-height:1.4}.leaflet-popup-content p{margin:18px 0}.leaflet-popup-tip-container{width:40px;height:20px;position:absolute;left:50%;margin-left:-20px;overflow:hidden;pointer-events:none}.leaflet-popup-tip{width:17px;height:17px;padding:1px;margin:-10px auto 0;transform:rotate(45deg)}.leaflet-popup-content-wrapper,.leaflet-popup-tip{background:#fff;color:#333;box-shadow:0 3px 14px rgba(0,0,0,.4)}.leaflet-container a.leaflet-popup-close-button{position:absolute;top:0;right:0;padding:4px 4px 0 0;border:none;text-align:center;width:18px;height:14px;font:16px/14px Tahoma,Verdana,sans-serif;color:#c3c3c3;text-decoration:none;font-weight:bold;background:transparent}.leaflet-container a.leaflet-popup-close-button:hover{color:#999}.leaflet-popup-scrolled{overflow:auto;border-bottom:1px solid #ddd;border-top:1px solid #ddd}.leaflet-oldie .leaflet-popup-content-wrapper{zoom:1}.leaflet-oldie .leaflet-popup-tip{width:24px;margin:0 auto;-ms-filter:"progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)";filter:progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)}.leaflet-oldie .leaflet-popup-tip-container{margin-top:-1px}.leaflet-oldie .leaflet-control-zoom,.leaflet-oldie .leaflet-control-layers,.leaflet-oldie .leaflet-popup-content-wrapper,.leaflet-oldie .leaflet-popup-tip{border:1px solid #999}.leaflet-div-icon{background:#fff;border:1px solid #666}.leaflet-tooltip{position:absolute;padding:6px;background-color:#fff;border:1px solid #fff;border-radius:3px;color:#222;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;user-select:none;pointer-events:none;box-shadow:0 1px 3px rgba(0,0,0,.4)}.leaflet-tooltip.leaflet-clickable{cursor:pointer;pointer-events:auto}.leaflet-tooltip-top:before,.leaflet-tooltip-bottom:before,.leaflet-tooltip-left:before,.leaflet-tooltip-right:before{position:absolute;pointer-events:none;border:6px solid transparent;background:transparent;content:""}.leaflet-tooltip-bottom{margin-top:6px}.leaflet-tooltip-top{margin-top:-6px}.leaflet-tooltip-bottom:before,.leaflet-tooltip-top:before{left:50%;margin-left:-6px}.leaflet-tooltip-top:before{bottom:0;margin-bottom:-12px;border-top-color:#fff}.leaflet-tooltip-bottom:before{top:0;margin-top:-12px;margin-left:-6px;border-bottom-color:#fff}.leaflet-tooltip-left{margin-left:-6px}.leaflet-tooltip-right{margin-left:6px}.leaflet-tooltip-left:before,.leaflet-tooltip-right:before{top:50%;margin-top:-6px}.leaflet-tooltip-left:before{right:0;margin-right:-12px;border-left-color:#fff}.leaflet-tooltip-right:before{left:0;margin-left:-12px;border-right-color:#fff}',"",{version:3,sources:["leaflet.css"],names:[],mappings:"AAAA,6LAA6L,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,eAAe,CAAC,0DAA0D,wBAAwB,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,8BAA8B,yCAAyC,CAAC,wCAAwC,YAAY,CAAC,aAAa,CAAC,4BAA4B,CAAC,4CAA4C,aAAa,CAAC,0NAA0N,yBAAyB,CAAC,0BAA0B,CAAC,sCAAmE,wBAAwB,CAAC,sCAAkE,iBAAiB,CAAC,uBAAuB,CAAC,yDAA+E,iBAAiB,CAAC,mBAAmB,uCAAuC,CAAC,qBAAqB,+CAA+C,CAAC,cAAc,cAAc,CAAC,iBAAiB,CAAC,qBAAqB,kBAAkB,CAAC,kBAAkB,OAAO,CAAC,QAAQ,CAA4B,qBAAqB,CAAC,WAAW,CAAC,0BAA0B,qBAAqB,CAAC,cAAc,WAAW,CAAC,mBAAmB,WAAW,CAAC,sBAAsB,WAAW,CAAC,qBAAqB,WAAW,CAAC,qBAAqB,WAAW,CAAC,sBAAsB,WAAW,CAAC,oBAAoB,WAAW,CAAC,yBAAyB,WAAW,CAAC,sBAAsB,WAAW,CAAC,mBAAmB,SAAS,CAAC,UAAU,CAAC,MAAM,0BAA0B,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,iBAAiB,iBAAiB,CAAC,WAAW,CAAC,6BAA6B,CAAC,mBAAmB,CAAC,6BAA6B,iBAAiB,CAAC,YAAY,CAAC,mBAAmB,CAAC,aAAa,KAAK,CAAC,eAAe,OAAO,CAAC,gBAAgB,QAAQ,CAAC,cAAc,MAAM,CAAC,iBAAiB,UAAU,CAAC,UAAU,CAAC,gCAAgC,WAAW,CAAC,8BAA8B,eAAe,CAAC,iCAAiC,kBAAkB,CAAC,+BAA+B,gBAAgB,CAAC,gCAAgC,iBAAiB,CAAC,iCAAiC,mBAAmB,CAAC,kCAAkC,SAAS,CAA2G,6BAA6B,CAAC,oDAAoD,SAAS,CAAC,uBAA6E,oBAAoB,CAAC,0CAA0C,qBAAqB,CAAC,0CAA4O,qDAAqD,CAAC,iEAAiI,eAAe,CAAC,sCAAsC,iBAAiB,CAAC,qBAAqB,cAAc,CAAC,cAAkC,gBAAgB,CAAC,2DAA2D,gBAAgB,CAAC,qCAAqC,WAAW,CAAC,iIAAiI,WAAW,CAAyB,oBAAoB,CAAC,gHAAgH,mBAAmB,CAAC,6HAA6H,6BAA6B,CAAC,mBAAmB,CAAC,mBAAmB,eAAe,CAAC,SAAS,CAAC,qBAAqB,aAAa,CAAC,oCAAoC,wBAAwB,CAAC,kBAAkB,sBAAsB,CAAC,+BAA+B,CAAC,mBAAmB,yDAAyD,CAAC,aAAa,oCAAoC,CAAC,iBAAiB,CAAC,oCAAoC,qBAAqB,CAAC,4BAA4B,CAAC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,aAAa,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,UAAU,CAAC,8CAA8C,2BAA2B,CAAC,2BAA2B,CAAC,aAAa,CAAC,qBAAqB,wBAAwB,CAAC,2BAA2B,0BAA0B,CAAC,2BAA2B,CAAC,0BAA0B,6BAA6B,CAAC,8BAA8B,CAAC,kBAAkB,CAAC,gCAAgC,cAAc,CAAC,wBAAwB,CAAC,UAAU,CAAC,8BAA8B,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,0CAA0C,0BAA0B,CAAC,2BAA2B,CAAC,yCAAyC,6BAA6B,CAAC,8BAA8B,CAAC,mDAAmD,gDAAgD,CAAC,eAAe,CAAC,iFAAiF,cAAc,CAAC,wBAAwB,mCAAmC,CAAC,eAAe,CAAC,iBAAiB,CAAC,+BAA+B,wDAA0C,CAAC,UAAU,CAAC,WAAW,CAAC,+CAA+C,wDAA6C,CAAC,yBAAyB,CAAC,8CAA8C,UAAU,CAAC,WAAW,CAAC,qHAAqH,YAAY,CAAC,8DAA8D,aAAa,CAAC,iBAAiB,CAAC,iCAAiC,wBAAwB,CAAC,UAAU,CAAC,eAAe,CAAC,kCAAkC,iBAAiB,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,iCAAiC,cAAc,CAAC,iBAAiB,CAAC,OAAO,CAAC,8BAA8B,aAAa,CAAC,kCAAkC,QAAQ,CAAC,yBAAyB,CAAC,yBAAyB,CAAC,2BAA2B,wDAA+C,CAAC,gDAAgD,eAAe,CAAC,+BAA+B,CAAC,QAAQ,CAAC,yDAAyD,aAAa,CAAC,UAAU,CAAC,+BAA+B,oBAAoB,CAAC,qCAAqC,yBAAyB,CAAC,0FAA0F,cAAc,CAAC,qCAAqC,eAAe,CAAC,uCAAuC,iBAAiB,CAAC,4BAA4B,qBAAqB,CAAC,eAAe,CAAC,eAAe,CAAC,mBAAmB,CAAC,cAAc,CAAC,kBAAkB,CAAC,eAAe,CAA4B,qBAAqB,CAAC,eAAe,CAAC,+BAA+B,CAAC,8CAA8C,yBAAyB,CAAC,kBAAkB,CAAC,eAAe,CAAC,+DAA+D,4BAA4B,CAAC,+GAA+G,eAAe,CAAC,mEAAmE,+BAA+B,CAAC,2BAA2B,CAAC,eAAe,iBAAiB,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,+BAA+B,WAAW,CAAC,eAAe,CAAC,kBAAkB,CAAC,uBAAuB,gBAAgB,CAAC,eAAe,CAAC,yBAAyB,aAAa,CAAC,6BAA6B,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,eAAe,CAAC,mBAAmB,CAAC,mBAAmB,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,mBAAmB,CAAqH,uBAAuB,CAAC,kDAAkD,eAAe,CAAC,UAAU,CAAC,oCAAoC,CAAC,gDAAgD,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,wCAAwC,CAAC,aAAa,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,sDAAsD,UAAU,CAAC,wBAAwB,aAAa,CAAC,4BAA4B,CAAC,yBAAyB,CAAC,8CAA8C,MAAM,CAAC,kCAAkC,UAAU,CAAC,aAAa,CAAC,sHAAsH,CAAC,gHAAgH,CAAC,4CAA4C,eAAe,CAAC,4JAA4J,qBAAqB,CAAC,kBAAkB,eAAe,CAAC,qBAAqB,CAAC,iBAAiB,iBAAiB,CAAC,WAAW,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,UAAU,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,qBAAqB,CAAsB,gBAAgB,CAAC,mBAAmB,CAAC,mCAAmC,CAAC,mCAAmC,cAAc,CAAC,mBAAmB,CAAC,sHAAsH,iBAAiB,CAAC,mBAAmB,CAAC,4BAA4B,CAAC,sBAAsB,CAAC,UAAU,CAAC,wBAAwB,cAAc,CAAC,qBAAqB,eAAe,CAAC,2DAA2D,QAAQ,CAAC,gBAAgB,CAAC,4BAA4B,QAAQ,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,+BAA+B,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,sBAAsB,gBAAgB,CAAC,uBAAuB,eAAe,CAAC,2DAA2D,OAAO,CAAC,eAAe,CAAC,6BAA6B,OAAO,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,8BAA8B,MAAM,CAAC,iBAAiB,CAAC,uBAAuB",file:"leaflet.css",sourcesContent:['.leaflet-pane,.leaflet-tile,.leaflet-marker-icon,.leaflet-marker-shadow,.leaflet-tile-container,.leaflet-pane>svg,.leaflet-pane>canvas,.leaflet-zoom-box,.leaflet-image-layer,.leaflet-layer{position:absolute;left:0;top:0}.leaflet-container{overflow:hidden}.leaflet-tile,.leaflet-marker-icon,.leaflet-marker-shadow{-webkit-user-select:none;-moz-user-select:none;user-select:none;-webkit-user-drag:none}.leaflet-safari .leaflet-tile{image-rendering:-webkit-optimize-contrast}.leaflet-safari .leaflet-tile-container{width:1600px;height:1600px;-webkit-transform-origin:0 0}.leaflet-marker-icon,.leaflet-marker-shadow{display:block}.leaflet-container .leaflet-overlay-pane svg,.leaflet-container .leaflet-marker-pane img,.leaflet-container .leaflet-shadow-pane img,.leaflet-container .leaflet-tile-pane img,.leaflet-container img.leaflet-image-layer{max-width:none !important;max-height:none !important}.leaflet-container.leaflet-touch-zoom{-ms-touch-action:pan-x pan-y;touch-action:pan-x pan-y}.leaflet-container.leaflet-touch-drag{-ms-touch-action:pinch-zoom;touch-action:none;touch-action:pinch-zoom}.leaflet-container.leaflet-touch-drag.leaflet-touch-zoom{-ms-touch-action:none;touch-action:none}.leaflet-container{-webkit-tap-highlight-color:transparent}.leaflet-container a{-webkit-tap-highlight-color:rgba(51,181,229,.4)}.leaflet-tile{filter:inherit;visibility:hidden}.leaflet-tile-loaded{visibility:inherit}.leaflet-zoom-box{width:0;height:0;-moz-box-sizing:border-box;box-sizing:border-box;z-index:800}.leaflet-overlay-pane svg{-moz-user-select:none}.leaflet-pane{z-index:400}.leaflet-tile-pane{z-index:200}.leaflet-overlay-pane{z-index:400}.leaflet-shadow-pane{z-index:500}.leaflet-marker-pane{z-index:600}.leaflet-tooltip-pane{z-index:650}.leaflet-popup-pane{z-index:700}.leaflet-map-pane canvas{z-index:100}.leaflet-map-pane svg{z-index:200}.leaflet-vml-shape{width:1px;height:1px}.lvml{behavior:url(#default#VML);display:inline-block;position:absolute}.leaflet-control{position:relative;z-index:800;pointer-events:visiblePainted;pointer-events:auto}.leaflet-top,.leaflet-bottom{position:absolute;z-index:1000;pointer-events:none}.leaflet-top{top:0}.leaflet-right{right:0}.leaflet-bottom{bottom:0}.leaflet-left{left:0}.leaflet-control{float:left;clear:both}.leaflet-right .leaflet-control{float:right}.leaflet-top .leaflet-control{margin-top:10px}.leaflet-bottom .leaflet-control{margin-bottom:10px}.leaflet-left .leaflet-control{margin-left:10px}.leaflet-right .leaflet-control{margin-right:10px}.leaflet-fade-anim .leaflet-tile{will-change:opacity}.leaflet-fade-anim .leaflet-popup{opacity:0;-webkit-transition:opacity .2s linear;-moz-transition:opacity .2s linear;-o-transition:opacity .2s linear;transition:opacity .2s linear}.leaflet-fade-anim .leaflet-map-pane .leaflet-popup{opacity:1}.leaflet-zoom-animated{-webkit-transform-origin:0 0;-ms-transform-origin:0 0;transform-origin:0 0}.leaflet-zoom-anim .leaflet-zoom-animated{will-change:transform}.leaflet-zoom-anim .leaflet-zoom-animated{-webkit-transition:-webkit-transform .25s cubic-bezier(0, 0, 0.25, 1);-moz-transition:-moz-transform .25s cubic-bezier(0, 0, 0.25, 1);-o-transition:-o-transform .25s cubic-bezier(0, 0, 0.25, 1);transition:transform .25s cubic-bezier(0, 0, 0.25, 1)}.leaflet-zoom-anim .leaflet-tile,.leaflet-pan-anim .leaflet-tile{-webkit-transition:none;-moz-transition:none;-o-transition:none;transition:none}.leaflet-zoom-anim .leaflet-zoom-hide{visibility:hidden}.leaflet-interactive{cursor:pointer}.leaflet-grab{cursor:-webkit-grab;cursor:-moz-grab}.leaflet-crosshair,.leaflet-crosshair .leaflet-interactive{cursor:crosshair}.leaflet-popup-pane,.leaflet-control{cursor:auto}.leaflet-dragging .leaflet-grab,.leaflet-dragging .leaflet-grab .leaflet-interactive,.leaflet-dragging .leaflet-marker-draggable{cursor:move;cursor:-webkit-grabbing;cursor:-moz-grabbing}.leaflet-marker-icon,.leaflet-marker-shadow,.leaflet-image-layer,.leaflet-pane>svg path,.leaflet-tile-container{pointer-events:none}.leaflet-marker-icon.leaflet-interactive,.leaflet-image-layer.leaflet-interactive,.leaflet-pane>svg path.leaflet-interactive{pointer-events:visiblePainted;pointer-events:auto}.leaflet-container{background:#ddd;outline:0}.leaflet-container a{color:#0078a8}.leaflet-container a.leaflet-active{outline:2px solid orange}.leaflet-zoom-box{border:2px dotted #38f;background:rgba(255,255,255,.5)}.leaflet-container{font:12px/1.5 "Helvetica Neue",Arial,Helvetica,sans-serif}.leaflet-bar{box-shadow:0 1px 5px rgba(0,0,0,.65);border-radius:4px}.leaflet-bar a,.leaflet-bar a:hover{background-color:#fff;border-bottom:1px solid #ccc;width:26px;height:26px;line-height:26px;display:block;text-align:center;text-decoration:none;color:#000}.leaflet-bar a,.leaflet-control-layers-toggle{background-position:50% 50%;background-repeat:no-repeat;display:block}.leaflet-bar a:hover{background-color:#f4f4f4}.leaflet-bar a:first-child{border-top-left-radius:4px;border-top-right-radius:4px}.leaflet-bar a:last-child{border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-bottom:none}.leaflet-bar a.leaflet-disabled{cursor:default;background-color:#f4f4f4;color:#bbb}.leaflet-touch .leaflet-bar a{width:30px;height:30px;line-height:30px}.leaflet-touch .leaflet-bar a:first-child{border-top-left-radius:2px;border-top-right-radius:2px}.leaflet-touch .leaflet-bar a:last-child{border-bottom-left-radius:2px;border-bottom-right-radius:2px}.leaflet-control-zoom-in,.leaflet-control-zoom-out{font:bold 18px "Lucida Console",Monaco,monospace;text-indent:1px}.leaflet-touch .leaflet-control-zoom-in,.leaflet-touch .leaflet-control-zoom-out{font-size:22px}.leaflet-control-layers{box-shadow:0 1px 5px rgba(0,0,0,.4);background:#fff;border-radius:5px}.leaflet-control-layers-toggle{background-image:url(../images/layers.png);width:36px;height:36px}.leaflet-retina .leaflet-control-layers-toggle{background-image:url(../images/layers-2x.png);background-size:26px 26px}.leaflet-touch .leaflet-control-layers-toggle{width:44px;height:44px}.leaflet-control-layers .leaflet-control-layers-list,.leaflet-control-layers-expanded .leaflet-control-layers-toggle{display:none}.leaflet-control-layers-expanded .leaflet-control-layers-list{display:block;position:relative}.leaflet-control-layers-expanded{padding:6px 10px 6px 6px;color:#333;background:#fff}.leaflet-control-layers-scrollbar{overflow-y:scroll;overflow-x:hidden;padding-right:5px}.leaflet-control-layers-selector{margin-top:2px;position:relative;top:1px}.leaflet-control-layers label{display:block}.leaflet-control-layers-separator{height:0;border-top:1px solid #ddd;margin:5px -10px 5px -6px}.leaflet-default-icon-path{background-image:url(../images/marker-icon.png)}.leaflet-container .leaflet-control-attribution{background:#fff;background:rgba(255,255,255,.7);margin:0}.leaflet-control-attribution,.leaflet-control-scale-line{padding:0 5px;color:#333}.leaflet-control-attribution a{text-decoration:none}.leaflet-control-attribution a:hover{text-decoration:underline}.leaflet-container .leaflet-control-attribution,.leaflet-container .leaflet-control-scale{font-size:11px}.leaflet-left .leaflet-control-scale{margin-left:5px}.leaflet-bottom .leaflet-control-scale{margin-bottom:5px}.leaflet-control-scale-line{border:2px solid #777;border-top:none;line-height:1.1;padding:2px 5px 1px;font-size:11px;white-space:nowrap;overflow:hidden;-moz-box-sizing:border-box;box-sizing:border-box;background:#fff;background:rgba(255,255,255,.5)}.leaflet-control-scale-line:not(:first-child){border-top:2px solid #777;border-bottom:none;margin-top:-2px}.leaflet-control-scale-line:not(:first-child):not(:last-child){border-bottom:2px solid #777}.leaflet-touch .leaflet-control-attribution,.leaflet-touch .leaflet-control-layers,.leaflet-touch .leaflet-bar{box-shadow:none}.leaflet-touch .leaflet-control-layers,.leaflet-touch .leaflet-bar{border:2px solid rgba(0,0,0,.2);background-clip:padding-box}.leaflet-popup{position:absolute;text-align:center;margin-bottom:20px}.leaflet-popup-content-wrapper{padding:1px;text-align:left;border-radius:12px}.leaflet-popup-content{margin:13px 19px;line-height:1.4}.leaflet-popup-content p{margin:18px 0}.leaflet-popup-tip-container{width:40px;height:20px;position:absolute;left:50%;margin-left:-20px;overflow:hidden;pointer-events:none}.leaflet-popup-tip{width:17px;height:17px;padding:1px;margin:-10px auto 0;-webkit-transform:rotate(45deg);-moz-transform:rotate(45deg);-ms-transform:rotate(45deg);-o-transform:rotate(45deg);transform:rotate(45deg)}.leaflet-popup-content-wrapper,.leaflet-popup-tip{background:#fff;color:#333;box-shadow:0 3px 14px rgba(0,0,0,.4)}.leaflet-container a.leaflet-popup-close-button{position:absolute;top:0;right:0;padding:4px 4px 0 0;border:none;text-align:center;width:18px;height:14px;font:16px/14px Tahoma,Verdana,sans-serif;color:#c3c3c3;text-decoration:none;font-weight:bold;background:transparent}.leaflet-container a.leaflet-popup-close-button:hover{color:#999}.leaflet-popup-scrolled{overflow:auto;border-bottom:1px solid #ddd;border-top:1px solid #ddd}.leaflet-oldie .leaflet-popup-content-wrapper{zoom:1}.leaflet-oldie .leaflet-popup-tip{width:24px;margin:0 auto;-ms-filter:"progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)";filter:progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)}.leaflet-oldie .leaflet-popup-tip-container{margin-top:-1px}.leaflet-oldie .leaflet-control-zoom,.leaflet-oldie .leaflet-control-layers,.leaflet-oldie .leaflet-popup-content-wrapper,.leaflet-oldie .leaflet-popup-tip{border:1px solid #999}.leaflet-div-icon{background:#fff;border:1px solid #666}.leaflet-tooltip{position:absolute;padding:6px;background-color:#fff;border:1px solid #fff;border-radius:3px;color:#222;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none;box-shadow:0 1px 3px rgba(0,0,0,.4)}.leaflet-tooltip.leaflet-clickable{cursor:pointer;pointer-events:auto}.leaflet-tooltip-top:before,.leaflet-tooltip-bottom:before,.leaflet-tooltip-left:before,.leaflet-tooltip-right:before{position:absolute;pointer-events:none;border:6px solid transparent;background:transparent;content:""}.leaflet-tooltip-bottom{margin-top:6px}.leaflet-tooltip-top{margin-top:-6px}.leaflet-tooltip-bottom:before,.leaflet-tooltip-top:before{left:50%;margin-left:-6px}.leaflet-tooltip-top:before{bottom:0;margin-bottom:-12px;border-top-color:#fff}.leaflet-tooltip-bottom:before{top:0;margin-top:-12px;margin-left:-6px;border-bottom-color:#fff}.leaflet-tooltip-left{margin-left:-6px}.leaflet-tooltip-right{margin-left:6px}.leaflet-tooltip-left:before,.leaflet-tooltip-right:before{top:50%;margin-top:-6px}.leaflet-tooltip-left:before{right:0;margin-right:-12px;border-left-color:#fff}.leaflet-tooltip-right:before{left:0;margin-left:-12px;border-right-color:#fff}']}]),t.exports=e},function(t,e,i){"use strict";t.exports=function(t,e){return e||(e={}),"string"!=typeof(t=t&&t.__esModule?t.default:t)?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),e.hash&&(t+=e.hash),/["'() \t\n]/.test(t)||e.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},function(t,e,i){"use strict";i.r(e),e.default=i.p+"/images/layers.png"},function(t,e,i){"use strict";i.r(e),e.default=i.p+"/images/layers-2x.png"},function(t,e,i){"use strict";i.r(e),e.default=i.p+"/images/marker-icon.png"},,,function(t,e,i){"use strict";i.r(e);var o=i(2),n=i(1),a=i(7),r=i.n(a),s=i(8),l=i.n(s),h=i(0);function u(t){if(!t||0===t.length)throw new Error("Missing geohash value");var e,i=[16,8,4,2,1],o=!0,n=[],a=[];return n[0]=-90,n[1]=90,a[0]=-180,a[1]=180,t.split("").forEach((function(t){e="0123456789bcdefghjkmnpqrstuvwxyz".indexOf(t),i.forEach((function(t){c(o?a:n,e,t),o=!o}))})),{latitude:(n[0]+n[1])/2,longitude:(a[0]+a[1])/2}}function c(t,e,i){e&i?t[0]=(t[0]+t[1])/2:t[1]=(t[0]+t[1])/2}var d=i(3),p=i.n(d),f=function(){function t(t){this.ctrl=t}return t.prototype.setValues=function(t){var e=this;if(this.ctrl.series&&this.ctrl.series.length>0){var i=0,o=Number.MAX_VALUE;this.ctrl.series.forEach((function(n){var a=h.last(n.datapoints),r=h.isArray(a)?a[0]:null,s=h.find(e.ctrl.locations,(function(t){return t.key.toUpperCase()===n.alias.toUpperCase()}));if(s)if(h.isString(r))t.push({key:n.alias,value:0,valueFormatted:r,valueRounded:0});else{var l={key:n.alias,locationName:s.name,locationLatitude:s.latitude,locationLongitude:s.longitude,value:n.stats[e.ctrl.panel.valueName],valueFormatted:r,valueRounded:0};l.value>i&&(i=l.value),l.value<o&&(o=l.value),l.valueRounded=p.a.roundValue(l.value,parseInt(e.ctrl.panel.decimals,10)||0),t.push(l)}})),t.highestValue=i,t.lowestValue=o,t.valueRange=i-o}},t.prototype.createDataValue=function(t,e,i,o){var n={key:t,locationName:i,locationLatitude:e.latitude,locationLongitude:e.longitude,value:o,valueFormatted:o,valueRounded:0};return n.valueRounded=p.a.roundValue(n.value,this.ctrl.panel.decimals||0),n},t.prototype.setGeohashValues=function(t,e){var i=this;if(this.ctrl.panel.esGeoPoint&&this.ctrl.panel.esMetric&&t&&t.length>0){var o=0,n=Number.MAX_VALUE;t.forEach((function(t){if("table"===t.type){var a={};t.columns.forEach((function(t,e){a[t.text]=e})),t.rows.forEach((function(t){var r=t[a[i.ctrl.panel.esGeoPoint]],s=u(r),l=i.ctrl.panel.esLocationName?t[a[i.ctrl.panel.esLocationName]]:r,h=t[a[i.ctrl.panel.esMetric]],c=i.createDataValue(r,s,l,h);c.value>o&&(o=c.value),c.value<n&&(n=c.value),e.push(c)})),e.highestValue=o,e.lowestValue=n,e.valueRange=o-n}else t.datapoints.forEach((function(t){var a=t[i.ctrl.panel.esGeoPoint],r=u(a),s=i.ctrl.panel.esLocationName?t[i.ctrl.panel.esLocationName]:a,l=t[i.ctrl.panel.esMetric],h=i.createDataValue(a,r,s,l);h.value>o&&(o=h.value),h.value<n&&(n=h.value),e.push(h)})),e.highestValue=o,e.lowestValue=n,e.valueRange=o-n}))}},t.tableHandler=function(t){var e=[];if("table"===t.type){var i={};t.columns.forEach((function(t,e){i[e]=t.text})),t.rows.forEach((function(t){var o={};t.forEach((function(t,e){var n=i[e];o[n]=t})),e.push(o)}))}return e},t.prototype.setTableValues=function(t,e){var i=this;if(t&&t.length>0){var o=0,n=Number.MAX_VALUE;t[0].forEach((function(t){var a,r,s;if("geohash"===i.ctrl.panel.tableQueryOptions.queryType){var l=t[i.ctrl.panel.tableQueryOptions.geohashField],h=u(l);s=h.latitude,r=h.longitude,a=l}else a=(s=t[i.ctrl.panel.tableQueryOptions.latitudeField])+"_"+(r=t[i.ctrl.panel.tableQueryOptions.longitudeField]);var c={key:a,locationName:t[i.ctrl.panel.tableQueryOptions.labelField]||"n/a",locationLatitude:s,locationLongitude:r,value:t[i.ctrl.panel.tableQueryOptions.metricField],valueFormatted:t[i.ctrl.panel.tableQueryOptions.metricField],valueRounded:0};c.value>o&&(o=c.value),c.value<n&&(n=c.value),c.valueRounded=p.a.roundValue(c.value,i.ctrl.panel.decimals||0),e.push(c)})),e.highestValue=o,e.lowestValue=n,e.valueRange=o-n}},t.prototype.setJsonValues=function(t){if(this.ctrl.series&&this.ctrl.series.length>0){var e=0,i=Number.MAX_VALUE;this.ctrl.series.forEach((function(o){var n={key:o.key,locationName:o.name,locationLatitude:o.latitude,locationLongitude:o.longitude,value:void 0!==o.value?o.value:1,valueRounded:0};n.value>e&&(e=n.value),n.value<i&&(i=n.value),n.valueRounded=Math.round(n.value),t.push(n)})),t.highestValue=e,t.lowestValue=i,t.valueRange=e-i}},t}(),m=(i(11),i(4)),_=i.n(m),g=(i(13),i(9)),v=i(10),A=i.n(v),y={"CartoDB Positron":{url:"https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png",attribution:'&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> &copy; <a href="http://cartodb.com/attributions">CartoDB</a>',subdomains:"abcd"},"CartoDB Dark":{url:"https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png",attribution:'&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> &copy; <a href="http://cartodb.com/attributions">CartoDB</a>',subdomains:"abcd"}},b=function(){function t(t,e){this.ctrl=t,this.mapContainer=e,this.circles=[]}return t.prototype.createMap=function(){var t=window.L.latLng(parseFloat(this.ctrl.panel.mapCenterLatitude),parseFloat(this.ctrl.panel.mapCenterLongitude));this.map=g.map(this.mapContainer,{worldCopyJump:!0,preferCanvas:!0,center:t,zoom:parseInt(this.ctrl.panel.initialZoom,10)||1}),this.setMouseWheelZoom();var e=y[this.ctrl.tileServer];window.L.tileLayer(e.url,{maxZoom:18,subdomains:e.subdomains,reuseTiles:!0,detectRetina:!0,attribution:e.attribution}).addTo(this.map)},t.prototype.createLegend=function(){var t=this;this.legend=window.L.control({position:"bottomleft"}),this.legend.onAdd=function(){return t.legend._div=window.L.DomUtil.create("div","info legend"),t.legend.update(),t.legend._div},this.legend.update=function(){var e=t.ctrl.data.thresholds,i="";i+='<div class="legend-item"><i style="background:'+x(t.ctrl.panel.colors[0])+'"></i> &lt; '+e[0]+"</div>";for(var o=0;o<e.length;o+=1)i+='<div class="legend-item"><i style="background:'+x(t.ctrl.panel.colors[o+1])+'"></i> '+e[o]+(e[o+1]?"&ndash;"+e[o+1]+"</div>":"+");t.legend._div.innerHTML=i},this.legend.addTo(this.map)},t.prototype.needToRedrawCircles=function(t){if(0===this.circles.length&&t.length>0)return!0;if(this.circles.length!==t.length)return!0;var e=h.map(h.map(this.circles,"options"),"location").sort(),i=h.map(t,"key").sort();return!h.isEqual(e,i)},t.prototype.filterEmptyAndZeroValues=function(t){var e=this;return h.filter(t,(function(t){return!(e.ctrl.panel.hideEmpty&&h.isNil(t.value)||e.ctrl.panel.hideZero&&0===t.value)}))},t.prototype.clearCircles=function(){this.circlesLayer&&(this.circlesLayer.clearLayers(),this.removeCircles(),this.circles=[])},t.prototype.drawCircles=function(){var t=this.filterEmptyAndZeroValues(this.ctrl.data);this.needToRedrawCircles(t)?(this.clearCircles(),this.createCircles(t)):this.updateCircles(t)},t.prototype.createCircles=function(t){var e=this,i=[];t.forEach((function(t){t.locationName&&i.push(e.createCircle(t))})),this.circlesLayer=this.addCircles(i),this.circles=i},t.prototype.updateCircles=function(t){var e=this;t.forEach((function(t){if(t.locationName){var i=h.find(e.circles,(function(e){return e.options.location===t.key}));i&&(i.setRadius(e.calcCircleSize(t.value||0)),i.setStyle({color:e.getColor(t.value),fillColor:e.getColor(t.value),fillOpacity:.5,location:t.key}),i.unbindPopup(),e.createPopup(i,t.locationName,t.valueRounded))}}))},t.prototype.createCircle=function(t){var e=window.L.circleMarker([t.locationLatitude,t.locationLongitude],{radius:this.calcCircleSize(t.value||0),color:this.getColor(t.value),fillColor:this.getColor(t.value),fillOpacity:.5,location:t.key});return this.createPopup(e,t.locationName,t.valueRounded),e},t.prototype.calcCircleSize=function(t){var e=parseInt(this.ctrl.panel.circleMinSize,10)||2,i=parseInt(this.ctrl.panel.circleMaxSize,10)||30;return 0===this.ctrl.data.valueRange?i:(i-e)*((t-this.ctrl.data.lowestValue)/this.ctrl.data.valueRange)+e},t.prototype.createPopup=function(t,e,i){var o=(e+": "+i+" "+((i&&1===i?this.ctrl.panel.unitSingular:this.ctrl.panel.unitPlural)||"")).trim();t.bindPopup(o,{offset:window.L.point(0,-2),className:"worldmap-popup",closeButton:this.ctrl.panel.stickyLabels}),t.on("mouseover",(function(t){t.target.bringToFront(),this.openPopup()})),this.ctrl.panel.stickyLabels||t.on("mouseout",(function(){t.closePopup()}))},t.prototype.getColor=function(t){for(var e=this.ctrl.data.thresholds.length;e>0;e-=1)if(t>=this.ctrl.data.thresholds[e-1])return this.ctrl.panel.colors[e];return h.first(this.ctrl.panel.colors)},t.prototype.resize=function(){this.map.invalidateSize()},t.prototype.panToMapCenter=function(){this.map.panTo([parseFloat(this.ctrl.panel.mapCenterLatitude),parseFloat(this.ctrl.panel.mapCenterLongitude)]),this.ctrl.mapCenterMoved=!1},t.prototype.removeLegend=function(){this.legend.remove(this.map),this.legend=null},t.prototype.setMouseWheelZoom=function(){this.ctrl.panel.mouseWheelZoom?this.map.scrollWheelZoom.enable():this.map.scrollWheelZoom.disable()},t.prototype.addCircles=function(t){return window.L.layerGroup(t).addTo(this.map)},t.prototype.removeCircles=function(){this.map.removeLayer(this.circlesLayer)},t.prototype.setZoom=function(t){this.map.setZoom(parseInt(t,10))},t.prototype.remove=function(){this.circles=[],this.circlesLayer&&this.removeCircles(),this.legend&&this.removeLegend(),this.map.remove()},t}();function x(t){return A()(t).isValid()?t:"#CCC"}var C,w=(C=function(t,e){return(C=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}C(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),L={maxDataPoints:1,mapCenter:"(0°, 0°)",mapCenterLatitude:0,mapCenterLongitude:0,initialZoom:1,valueName:"total",circleMinSize:2,circleMaxSize:30,locationData:"countries",thresholds:"0,10",colors:["rgba(245, 54, 54, 0.9)","rgba(237, 129, 40, 0.89)","rgba(50, 172, 45, 0.97)"],unitSingle:"",unitPlural:"",showLegend:!0,mouseWheelZoom:!1,esMetric:"Count",decimals:0,hideEmpty:!1,hideZero:!1,stickyLabels:!1,tableQueryOptions:{queryType:"geohash",geohashField:"geohash",latitudeField:"latitude",longitudeField:"longitude",metricField:"metric"}},B={"(0°, 0°)":{mapCenterLatitude:0,mapCenterLongitude:0},"North America":{mapCenterLatitude:40,mapCenterLongitude:-100},Europe:{mapCenterLatitude:46,mapCenterLongitude:14},"West Asia":{mapCenterLatitude:26,mapCenterLongitude:53},"SE Asia":{mapCenterLatitude:10,mapCenterLongitude:106},"Last GeoHash":{mapCenterLatitude:0,mapCenterLongitude:0}},P=function(t){function e(e,i,o){var a=t.call(this,e,i)||this;return a.setMapProvider(o),h.defaults(a.panel,L),a.dataFormatter=new f(a),void 0===n.PanelEvents?(a.events.on("init-edit-mode",a.onInitEditMode.bind(a)),a.events.on("data-received",a.onDataReceived.bind(a)),a.events.on("data-error",a.onDataError.bind(a)),a.events.on("data-snapshot-load",a.onDataReceived.bind(a)),a.events.on("panel-teardown",a.onPanelTeardown.bind(a))):(a.events.on(n.PanelEvents.editModeInitialized,a.onInitEditMode.bind(a)),a.events.on(n.PanelEvents.dataReceived,a.onDataReceived.bind(a)),a.events.on(n.PanelEvents.dataError,a.onDataError.bind(a)),a.events.on(n.PanelEvents.dataSnapshotLoad,a.onDataReceived.bind(a)),a.events.on(n.PanelEvents.panelTeardown,a.onPanelTeardown.bind(a))),a.loadLocationDataFromFile(),a}return e.$inject=["$scope","$injector","contextSrv"],w(e,t),e.prototype.setMapProvider=function(t){this.tileServer=t.user.lightTheme?"CartoDB Positron":"CartoDB Dark",this.setMapSaturationClass()},e.prototype.setMapSaturationClass=function(){"CartoDB Dark"===this.tileServer?this.saturationClass="map-darken":this.saturationClass=""},e.prototype.loadLocationDataFromFile=function(t){var e=this;if(!this.map||t)if(this.panel.snapshotLocationData)this.locations=this.panel.snapshotLocationData;else if("jsonp endpoint"===this.panel.locationData){if(!this.panel.jsonpUrl||!this.panel.jsonpCallback)return;this.panel.jsonpUrl=n.textUtil.sanitizeUrl(this.panel.jsonpUrl),this.panel.jsonpCallback=n.textUtil.sanitizeUrl(this.panel.jsonpCallback),_.a.ajax({type:"GET",url:this.panel.jsonpUrl+"?callback=?",contentType:"application/json",jsonpCallback:this.panel.jsonpCallback,dataType:"jsonp",success:function(t){e.locations=t,e.render()}})}else if("json endpoint"===this.panel.locationData){if(!this.panel.jsonUrl)return;this.panel.jsonUrl=n.textUtil.sanitizeUrl(this.panel.jsonUrl),_.a.getJSON(this.panel.jsonUrl).then((function(t){e.locations=t,e.render()}))}else"table"===this.panel.locationData||"geohash"!==this.panel.locationData&&"json result"!==this.panel.locationData&&_.a.getJSON("public/plugins/grafana-worldmap-panel/data/"+this.panel.locationData+".json").then(this.reloadLocations.bind(this))},e.prototype.reloadLocations=function(t){this.locations=t,this.refresh()},e.prototype.showTableGeohashOptions=function(){return"table"===this.panel.locationData&&"geohash"===this.panel.tableQueryOptions.queryType},e.prototype.showTableCoordinateOptions=function(){return"table"===this.panel.locationData&&"coordinates"===this.panel.tableQueryOptions.queryType},e.prototype.onPanelTeardown=function(){this.map&&this.map.remove()},e.prototype.onInitEditMode=function(){this.addEditorTab("Worldmap","public/plugins/grafana-worldmap-panel/partials/editor.html",2)},e.prototype.onDataReceived=function(t){if(t)try{this.dashboard.snapshot&&this.locations&&(this.panel.snapshotLocationData=this.locations);var e=[];if("geohash"===this.panel.locationData)this.dataFormatter.setGeohashValues(t,e);else if("table"===this.panel.locationData){var i=t.map(f.tableHandler.bind(this));this.dataFormatter.setTableValues(i,e)}else"json result"===this.panel.locationData?(this.series=t,this.dataFormatter.setJsonValues(e)):(this.series=t.map(this.seriesHandler.bind(this)),this.dataFormatter.setValues(e));this.data=e,this.updateThresholdData(),this.data.length&&"Last GeoHash"===this.panel.mapCenter?this.centerOnLastGeoHash():this.render()}catch(t){}},e.prototype.onDataError=function(t){this.onDataReceived([])},e.prototype.centerOnLastGeoHash=function(){var t=h.last(this.data);B[this.panel.mapCenter].mapCenterLatitude=t.locationLatitude,B[this.panel.mapCenter].mapCenterLongitude=t.locationLongitude,this.setNewMapCenter()},e.prototype.onDataSnapshotLoad=function(t){this.onDataReceived(t)},e.prototype.seriesHandler=function(t){var e=new l.a({datapoints:t.datapoints,alias:t.target});return e.flotpairs=e.getFlotPairs(this.panel.nullPointMode),e},e.prototype.setNewMapCenter=function(){"custom"!==this.panel.mapCenter&&(this.panel.mapCenterLatitude=B[this.panel.mapCenter].mapCenterLatitude,this.panel.mapCenterLongitude=B[this.panel.mapCenter].mapCenterLongitude),this.mapCenterMoved=!0,this.render()},e.prototype.setZoom=function(){this.map.setZoom(this.panel.initialZoom||1)},e.prototype.toggleLegend=function(){this.panel.showLegend||this.map.removeLegend(),this.render()},e.prototype.toggleMouseWheelZoom=function(){this.map.setMouseWheelZoom(),this.render()},e.prototype.toggleStickyLabels=function(){this.map.clearCircles(),this.render()},e.prototype.changeThresholds=function(){this.updateThresholdData(),this.map.legend.update(),this.render()},e.prototype.updateThresholdData=function(){for(this.data.thresholds=this.panel.thresholds.split(",").map((function(t){return Number(t.trim())}));h.size(this.panel.colors)>h.size(this.data.thresholds)+1;)this.panel.colors.pop();for(;h.size(this.panel.colors)<h.size(this.data.thresholds)+1;){this.panel.colors.push("rgba(50, 172, 45, 0.97)")}},e.prototype.changeLocationData=function(){this.loadLocationDataFromFile(!0),"geohash"===this.panel.locationData&&this.render()},e.prototype.link=function(t,e,i,o){var n=!0;function a(){if(o.data){if(n)return n=!1,void setTimeout(a,100);var t=e.find(".mapcontainer");if(!(t[0].id.indexOf("{{")>-1)){if(!o.map){var i=new b(o,t[0]);i.createMap(),o.map=i}o.map.resize(),o.mapCenterMoved&&o.map.panToMapCenter(),!o.map.legend&&o.panel.showLegend&&o.map.createLegend(),o.map.drawCircles()}}}o.events.on("render",(function(){a(),o.renderingCompleted()}))},e.prototype.migrateToReact=function(){this.onPluginTypeChange(r.a.panels.geomap)},e.templateUrl="partials/module.html",e}(o.MetricsPanelCtrl);i.d(e,"PanelCtrl",(function(){return P})),Object(o.loadPluginCss)({dark:"plugins/grafana-worldmap-panel/styles/dark.css",light:"plugins/grafana-worldmap-panel/styles/light.css"})}])}));
//# sourceMappingURL=module.js.map