{"$schema": "https://raw.githubusercontent.com/grafana/grafana/main/docs/sources/developers/plugins/plugin.schema.json", "alerting": true, "annotations": true, "backend": true, "dependencies": {"grafanaDependency": ">=9.5.15", "grafanaVersion": "9.5.x", "plugins": []}, "executable": "gpx_infinity", "id": "yesoreyeram-infinity-datasource", "includes": [], "info": {"author": {"name": "Grafana Labs", "url": "https://grafana.com"}, "build": {"time": 1728033076608, "repo": "https://github.com/grafana/grafana-infinity-datasource", "branch": "main", "hash": "50aead8b6b8fe72a6bbdf9e390a2d7a23f959981", "build": 430}, "description": "JSON API, CSV, TSV, XML, GraphQL, HTML, Google Sheets and HTTP/REST API datasource for Grafana. Do infinite things with Grafana. Transform data with UQL/GROQ. Visualize data from many apis including Amazon AWS, Microsoft Azure, Google Cloud / GCP and RSS/ATOM feeds directly. Also support visualizing logs and traces.", "keywords": ["atom", "aws", "azure", "csv", "feed", "gcp", "google cloud", "grafana", "graphql", "groq", "html", "inline", "json api", "json", "node graph", "random walk", "rest api", "rss feed", "rss", "scraping", "simple-json", "soap", "transformations", "uql", "url", "xml"], "links": [{"name": "Docs", "url": "https://grafana.com/docs/plugins/yesoreyeram-infinity-datasource"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/grafana/grafana-infinity-datasource"}, {"name": "Need Help?", "url": "https://github.com/grafana/grafana-infinity-datasource/discussions/categories/q-a"}, {"name": "Report Bug", "url": "https://github.com/grafana/grafana-infinity-datasource/issues/new/choose"}, {"name": "Examples", "url": "https://github.com/grafana/grafana-infinity-datasource/discussions/categories/show-and-tell"}], "logos": {"large": "img/icon.svg", "small": "img/icon.svg"}, "screenshots": [{"name": "features", "path": "img/slide-features.png"}, {"name": "rest-api", "path": "img/slide-rest-api.png"}, {"name": "variables", "path": "img/slide-variables.png"}, {"name": "series", "path": "img/slide-series.png"}], "updated": "2024-10-04", "version": "2.11.0"}, "logs": true, "metrics": true, "name": "Infinity", "routes": [], "tracing": true, "type": "datasource"}