# Grafana Infinity Datasource

Visualize data from JSON, CSV, XML, GraphQL and HTML endpoints.

[![click here for documentation](https://user-images.githubusercontent.com/153843/189100076-7fe3535d-0bc3-4e4a-b37d-14934ae621db.png)](https://grafana.com/docs/plugins/yesoreyeram-infinity-datasource)

## Documentation

Detailed documentation and examples are available in [plugin website](https://grafana.com/docs/plugins/yesoreyeram-infinity-datasource)

Docs on how to use JSON API - [Docs](https://grafana.com/docs/plugins/yesoreyeram-infinity-datasource/latest/json)

### [Demo video](https://youtu.be/Wmgs1E9Ry-s)

## Known limitations

- Backend features such as Alerting, Recorded Queries, Enterprise query caching, public dashboards only works if the `backend` parser option is selected in queries.
