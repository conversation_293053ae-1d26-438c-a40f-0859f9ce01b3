
-----<PERSON><PERSON><PERSON> SIGNED MESSAGE-----
Hash: SHA512

{
  "manifestVersion": "2.0.0",
  "signatureType": "community",
  "signedByOrg": "marc<PERSON><PERSON><PERSON>",
  "signedByOrgName": "<PERSON>",
  "plugin": "marcus<PERSON>son-dynamictext-panel",
  "version": "4.5.0",
  "time": 1710388990696,
  "keyId": "7e4d0c6a708866e7",
  "files": {
    "module.js.map": "e2a623e2245cda9c3e0a0215b30b30b37a4803cd570a8bc8478481757c76e1b1",
    "module.js": "3d08c87dc41242c7885d74fcd12045e8a872d04993cab65b258492e54c44f233",
    "img/logo.svg": "655cfc72eec8d19bda8f74d9fa3449b7b133b5ee66c7bd3db407fdfeada1cbd1",
    "img/screenshot.png": "68d355ffafe66ce4855566ccd7bf393e4c0dad66bddb3a2d2adb5e3eb057f50c",
    "plugin.json": "8cf4e743932b52d5753b06ba88c4689ca95a8c97f50f91c32bf2293c554d7a3e",
    "module.js.LICENSE.txt": "8d464bed9eb74d695ff4f0bbc0fa06f2ab5ae627595672cff7df21da88e5a7ef",
    "README.md": "eaa17e9fae0202251f68c44d5a3d0455ac29c7e7e3864a39cdcfbbaf36215171",
    "LICENSE": "d92d4f253fa8d798aed23b851736cc93fb460725aa172c5df85f603acd658703",
    "CHANGELOG.md": "8be140c9cc819d43a31990f987cca9cbb02862eb52774305fef9451eb31ab5ba"
  }
}
-----BEGIN PGP SIGNATURE-----
Version: OpenPGP.js v4.10.11
Comment: https://openpgpjs.org

wrkEARMKAAYFAmXydv4AIQkQfk0ManCIZucWIQTzOyW2kQdOhGNlcPN+TQxq
cIhm50NRAgkBr1rckFmWHZ5Pd2s+FFAtYMHjH4xtku60eFzoFTiQhYK1VZPp
7KtwSr6ou6aO2D/usqw40Pr6grpaz5uuZtYvcK4CCQGT4s40smGrWrh9OL9o
6uNadhAO2OqvbRXb195AkPO25TDFjhnKd5LU7zuYaHaGX6psyx+kEZ/suJul
eaO7WWtGtg==
=Lknn
-----END PGP SIGNATURE-----
