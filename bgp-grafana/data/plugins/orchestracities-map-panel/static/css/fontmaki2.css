@font-face {
  font-family: 'fontmaki2';
  src: url('../font/fontmaki2.eot?38664031');
  src: url('../font/fontmaki2.eot?38664031#iefix') format('embedded-opentype'),
       url('../font/fontmaki2.woff2?38664031') format('woff2'),
       url('../font/fontmaki2.woff?38664031') format('woff'),
       url('../font/fontmaki2.ttf?38664031') format('truetype'),
       url('../font/fontmaki2.svg?38664031#fontmaki2') format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'fontmaki2';
    src: url('../font/fontmaki2.svg?38664031#fontmaki2') format('svg');
  }
}
*/
 
 [class^="maki2-"]:before, [class*=" maki2-"]:before {
  font-family: "fontmaki2";
  font-style: normal;
  font-weight: normal;
  speak: none;
 
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */
 
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;
 
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
 
  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;
 
  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */
 
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
 
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}
 
.maki2-aerialway:before { content: '\e800'; } /* '' */
.maki2-airfield:before { content: '\e801'; } /* '' */
.maki2-airport:before { content: '\e802'; } /* '' */
.maki2-alcohol-shop:before { content: '\e803'; } /* '' */
.maki2-america-football:before { content: '\e804'; } /* '' */
.maki2-amusement-park:before { content: '\e805'; } /* '' */
.maki2-aquarium:before { content: '\e806'; } /* '' */
.maki2-art-gallery:before { content: '\e807'; } /* '' */
.maki2-attraction:before { content: '\e808'; } /* '' */
.maki2-bakery:before { content: '\e809'; } /* '' */
.maki2-bank:before { content: '\e80a'; } /* '' */
.maki2-bar:before { content: '\e80b'; } /* '' */
.maki2-barrier:before { content: '\e80c'; } /* '' */
.maki2-baseball:before { content: '\e80d'; } /* '' */
.maki2-basketball:before { content: '\e80e'; } /* '' */
.maki2-bbq:before { content: '\e80f'; } /* '' */
.maki2-beer:before { content: '\e810'; } /* '' */
.maki2-bicycle:before { content: '\e811'; } /* '' */
.maki2-bicycle-share:before { content: '\e812'; } /* '' */
.maki2-blood-bank:before { content: '\e813'; } /* '' */
.maki2-buddhism:before { content: '\e814'; } /* '' */
.maki2-building:before { content: '\e815'; } /* '' */
.maki2-building-alt1:before { content: '\e816'; } /* '' */
.maki2-bus:before { content: '\e817'; } /* '' */
.maki2-cafe:before { content: '\e818'; } /* '' */
.maki2-campsite:before { content: '\e819'; } /* '' */
.maki2-car:before { content: '\e81a'; } /* '' */
.maki2-car-rental:before { content: '\e81b'; } /* '' */
.maki2-car-repair:before { content: '\e81c'; } /* '' */
.maki2-casino:before { content: '\e81d'; } /* '' */
.maki2-castle:before { content: '\e81e'; } /* '' */
.maki2-cemetery:before { content: '\e81f'; } /* '' */
.maki2-charging-station:before { content: '\e820'; } /* '' */
.maki2-cinema:before { content: '\e821'; } /* '' */
.maki2-circle:before { content: '\e822'; } /* '' */
.maki2-circle-stroked:before { content: '\e823'; } /* '' */
.maki2-city:before { content: '\e824'; } /* '' */
.maki2-clothing-store:before { content: '\e825'; } /* '' */
.maki2-college:before { content: '\e826'; } /* '' */
.maki2-commercial:before { content: '\e827'; } /* '' */
.maki2-cricket:before { content: '\e828'; } /* '' */
.maki2-cross:before { content: '\e829'; } /* '' */
.maki2-dam:before { content: '\e82a'; } /* '' */
.maki2-danger:before { content: '\e82b'; } /* '' */
.maki2-defibrillator:before { content: '\e82c'; } /* '' */
.maki2-dentist:before { content: '\e82d'; } /* '' */
.maki2-doctor:before { content: '\e82e'; } /* '' */
.maki2-dog-park:before { content: '\e82f'; } /* '' */
.maki2-drinking-water:before { content: '\e830'; } /* '' */
.maki2-embassy:before { content: '\e831'; } /* '' */
.maki2-emergency-phone:before { content: '\e832'; } /* '' */
.maki2-entrance:before { content: '\e833'; } /* '' */
.maki2-entrance-alt1:before { content: '\e834'; } /* '' */
.maki2-farm:before { content: '\e835'; } /* '' */
.maki2-fast-food:before { content: '\e836'; } /* '' */
.maki2-fence:before { content: '\e837'; } /* '' */
.maki2-ferry:before { content: '\e838'; } /* '' */
.maki2-fire-station:before { content: '\e839'; } /* '' */
.maki2-florist:before { content: '\e83a'; } /* '' */
.maki2-fuel:before { content: '\e83b'; } /* '' */
.maki2-furniture:before { content: '\e83c'; } /* '' */
.maki2-gaming:before { content: '\e83d'; } /* '' */
.maki2-garden:before { content: '\e83e'; } /* '' */
.maki2-garden-center:before { content: '\e83f'; } /* '' */
.maki2-gift:before { content: '\e840'; } /* '' */
.maki2-globe:before { content: '\e841'; } /* '' */
.maki2-golf:before { content: '\e842'; } /* '' */
.maki2-grocery:before { content: '\e843'; } /* '' */
.maki2-hairdresser:before { content: '\e844'; } /* '' */
.maki2-harbor:before { content: '\e845'; } /* '' */
.maki2-hardware:before { content: '\e846'; } /* '' */
.maki2-heart:before { content: '\e847'; } /* '' */
.maki2-heliport:before { content: '\e848'; } /* '' */
.maki2-home:before { content: '\e849'; } /* '' */
.maki2-horse-riding:before { content: '\e84a'; } /* '' */
.maki2-hospital:before { content: '\e84b'; } /* '' */
.maki2-ice-cream:before { content: '\e84c'; } /* '' */
.maki2-industry:before { content: '\e84d'; } /* '' */
.maki2-information:before { content: '\e84e'; } /* '' */
.maki2-jewelry:before { content: '\e84f'; } /* '' */
.maki2-karaoke:before { content: '\e850'; } /* '' */
.maki2-landmark:before { content: '\e851'; } /* '' */
.maki2-landuse:before { content: '\e852'; } /* '' */
.maki2-laundry:before { content: '\e853'; } /* '' */
.maki2-library:before { content: '\e854'; } /* '' */
.maki2-lighthouse:before { content: '\e855'; } /* '' */
.maki2-lodging:before { content: '\e856'; } /* '' */
.maki2-logging:before { content: '\e857'; } /* '' */
.maki2-marker:before { content: '\e858'; } /* '' */
.maki2-marker-stroked:before { content: '\e859'; } /* '' */
.maki2-mobile-phone:before { content: '\e85a'; } /* '' */
.maki2-monument:before { content: '\e85b'; } /* '' */
.maki2-mountain:before { content: '\e85c'; } /* '' */
.maki2-museum:before { content: '\e85d'; } /* '' */
.maki2-music:before { content: '\e85e'; } /* '' */
.maki2-natural:before { content: '\e85f'; } /* '' */
.maki2-optician:before { content: '\e860'; } /* '' */
.maki2-paint:before { content: '\e861'; } /* '' */
.maki2-park:before { content: '\e862'; } /* '' */
.maki2-park-alt1:before { content: '\e863'; } /* '' */
.maki2-parking:before { content: '\e864'; } /* '' */
.maki2-parking-garage:before { content: '\e865'; } /* '' */
.maki2-pharmacy:before { content: '\e866'; } /* '' */
.maki2-picnic-site:before { content: '\e867'; } /* '' */
.maki2-pitch:before { content: '\e868'; } /* '' */
.maki2-place-of-worship:before { content: '\e869'; } /* '' */
.maki2-playground:before { content: '\e86a'; } /* '' */
.maki2-police:before { content: '\e86b'; } /* '' */
.maki2-post:before { content: '\e86c'; } /* '' */
.maki2-prison:before { content: '\e86d'; } /* '' */
.maki2-rail:before { content: '\e86e'; } /* '' */
.maki2-rail-light:before { content: '\e86f'; } /* '' */
.maki2-rail-metro:before { content: '\e870'; } /* '' */
.maki2-ranger-station:before { content: '\e871'; } /* '' */
.maki2-recycling:before { content: '\e872'; } /* '' */
.maki2-religious-christian:before { content: '\e873'; } /* '' */
.maki2-religious-jewish:before { content: '\e874'; } /* '' */
.maki2-religious-muslim:before { content: '\e875'; } /* '' */
.maki2-residential-community:before { content: '\e876'; } /* '' */
.maki2-restaurant:before { content: '\e877'; } /* '' */
.maki2-roadblock:before { content: '\e878'; } /* '' */
.maki2-rocket:before { content: '\e879'; } /* '' */
.maki2-school:before { content: '\e87a'; } /* '' */
.maki2-scooter:before { content: '\e87b'; } /* '' */
.maki2-shelter:before { content: '\e87c'; } /* '' */
.maki2-shoe:before { content: '\e87d'; } /* '' */
.maki2-shop:before { content: '\e87e'; } /* '' */
.maki2-skiing:before { content: '\e87f'; } /* '' */
.maki2-slaughterhouse:before { content: '\e880'; } /* '' */
.maki2-snowmobile:before { content: '\e881'; } /* '' */
.maki2-soccer:before { content: '\e882'; } /* '' */
.maki2-square:before { content: '\e883'; } /* '' */
.maki2-square-stroked:before { content: '\e884'; } /* '' */
.maki2-stadium:before { content: '\e885'; } /* '' */
.maki2-star:before { content: '\e886'; } /* '' */
.maki2-star-stroked:before { content: '\e887'; } /* '' */
.maki2-suitcase:before { content: '\e888'; } /* '' */
.maki2-sushi:before { content: '\e889'; } /* '' */
.maki2-swimming:before { content: '\e88a'; } /* '' */
.maki2-teahouse:before { content: '\e88b'; } /* '' */
.maki2-telephone:before { content: '\e88c'; } /* '' */
.maki2-tennis:before { content: '\e88d'; } /* '' */
.maki2-theatre:before { content: '\e88e'; } /* '' */
.maki2-toilet:before { content: '\e88f'; } /* '' */
.maki2-town:before { content: '\e890'; } /* '' */
.maki2-town-hall:before { content: '\e891'; } /* '' */
.maki2-triangle:before { content: '\e892'; } /* '' */
.maki2-triangle-stroked:before { content: '\e893'; } /* '' */
.maki2-veterinary:before { content: '\e894'; } /* '' */
.maki2-village:before { content: '\e895'; } /* '' */
.maki2-volcano:before { content: '\e896'; } /* '' */
.maki2-warehouse:before { content: '\e897'; } /* '' */
.maki2-waste-basket:before { content: '\e898'; } /* '' */
.maki2-watch:before { content: '\e899'; } /* '' */
.maki2-water:before { content: '\e89a'; } /* '' */
.maki2-watermill:before { content: '\e89b'; } /* '' */
.maki2-wetland:before { content: '\e89c'; } /* '' */
.maki2-wheelchair:before { content: '\e89d'; } /* '' */
.maki2-windmill:before { content: '\e89e'; } /* '' */
.maki2-zoo:before { content: '\e89f'; } /* '' */