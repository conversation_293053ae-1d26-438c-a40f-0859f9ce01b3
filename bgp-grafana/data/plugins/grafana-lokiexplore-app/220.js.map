{"version": 3, "file": "220.js", "mappings": "wOAQA,MAAMA,EAAc,IAClB,IAAIC,EAAAA,GAAS,CACXC,MAAO,EAACC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OACzBC,eAAgB,CACdC,2BAA2B,EAC3BC,iBAAiB,KA8BvB,EA1BA,WACE,MAAOC,EAAeC,GAAoBC,IAAAA,UAAe,IAEzDC,EAAAA,EAAAA,MAEA,MAAMC,GAAQC,EAAAA,EAAAA,IAAYb,IAE1Bc,EAAAA,EAAAA,YAAU,KACHN,GACHC,GAAiB,EACnB,GACC,CAACG,EAAOJ,IAEX,MAAMO,EAAkBC,EAAAA,OAAOC,SAASC,KAAKC,YAE7C,OADkBJ,aAAAA,EAAAA,EAAkB,mCAAmCA,aAAAA,EAAAA,EAAkB,wBAKpFP,EAIE,kBAACI,EAAMQ,UAAS,CAACC,MAAOT,IAHtB,KAJA,kBAACU,EAAAA,SAAQA,CAACC,GAAG,KAQxB,C", "sources": ["webpack://grafana-lokiexplore-app/./Components/LogExplorationPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\n\nimport { SceneApp, useSceneApp } from '@grafana/scenes';\nimport { config } from '@grafana/runtime';\nimport { Redirect } from 'react-router-dom';\nimport { makeIndexPage, makeRedirectPage } from './Pages';\nimport { initializeMetadataService } from '../services/metadata';\n\nconst getSceneApp = () =>\n  new SceneApp({\n    pages: [makeIndexPage(), makeRedirectPage()],\n    urlSyncOptions: {\n      createBrowserHistorySteps: false,\n      updateUrlOnInit: true,\n    },\n  });\n\nfunction LogExplorationView() {\n  const [isInitialized, setIsInitialized] = React.useState(false);\n\n  initializeMetadataService();\n\n  const scene = useSceneApp(getSceneApp);\n\n  useEffect(() => {\n    if (!isInitialized) {\n      setIsInitialized(true);\n    }\n  }, [scene, isInitialized]);\n\n  const userPermissions = config.bootData.user.permissions;\n  const canUseApp = userPermissions?.['grafana-lokiexplore-app:read'] || userPermissions?.['datasources:explore'];\n  if (!canUseApp) {\n    return <Redirect to=\"/\" />;\n  }\n\n  if (!isInitialized) {\n    return null;\n  }\n\n  return <scene.Component model={scene} />;\n}\n\nexport default LogExplorationView;\n"], "names": ["getSceneApp", "SceneApp", "pages", "makeIndexPage", "makeRedirectPage", "urlSyncOptions", "createBrowserHistorySteps", "updateUrlOnInit", "isInitialized", "setIsInitialized", "React", "initializeMetadataService", "scene", "useSceneApp", "useEffect", "userPermissions", "config", "bootData", "user", "permissions", "Component", "model", "Redirect", "to"], "sourceRoot": ""}