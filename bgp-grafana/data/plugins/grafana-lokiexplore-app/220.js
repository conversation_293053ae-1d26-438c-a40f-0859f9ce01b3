"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[220],{6220:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var n=a(5959),o=a.n(n),r=a(2672),s=a(8531),l=a(3806),p=a(4106),c=a(6949);const u=()=>new r.Oh({pages:[(0,p.c)(),(0,p.Oo)()],urlSyncOptions:{createBrowserHistorySteps:!1,updateUrlOnInit:!0}}),i=function(){const[e,t]=o().useState(!1);(0,c.rX)();const a=(0,r.TG)(u);(0,n.useEffect)((()=>{e||t(!0)}),[a,e]);const p=s.config.bootData.user.permissions;return(null==p?void 0:p["grafana-lokiexplore-app:read"])||(null==p?void 0:p["datasources:explore"])?e?o().createElement(a.Component,{model:a}):null:o().createElement(l.Redirect,{to:"/"})}}}]);
//# sourceMappingURL=220.js.map