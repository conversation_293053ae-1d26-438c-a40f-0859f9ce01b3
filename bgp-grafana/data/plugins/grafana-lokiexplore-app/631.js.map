{"version": 3, "file": "631.js", "mappings": "sLAEA,MAAMA,GAAqBC,EAAAA,EAAAA,OAAK,IAAM,mFAEhCC,EAAqBC,IAAAA,cAAyC,MAEpE,MAAMC,UAAYD,IAAAA,cAChBE,MAAAA,GACE,OACE,kBAACH,EAAmBI,SAAQ,CAACC,MAAOC,KAAKC,OACvC,kBAACT,EAAAA,MAGP,EAGF,S", "sources": ["webpack://grafana-lokiexplore-app/./Components/App.tsx"], "sourcesContent": ["import React, { lazy } from 'react';\nimport { AppRootProps } from '@grafana/data';\nconst LogExplorationView = lazy(() => import('./LogExplorationPage'));\n\nconst PluginPropsContext = React.createContext<AppRootProps | null>(null);\n\nclass App extends React.PureComponent<AppRootProps> {\n  render() {\n    return (\n      <PluginPropsContext.Provider value={this.props}>\n        <LogExplorationView />\n      </PluginPropsContext.Provider>\n    );\n  }\n}\n\nexport default App;\n"], "names": ["LogExplorationView", "lazy", "PluginPropsContext", "React", "App", "render", "Provider", "value", "this", "props"], "sourceRoot": ""}