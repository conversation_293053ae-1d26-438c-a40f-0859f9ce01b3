"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[631],{9631:(e,a,n)=>{n.r(a),n.d(a,{default:()=>p});var r=n(5959),l=n.n(r);const t=(0,r.lazy)((()=>Promise.all([n.e(854),n.e(944),n.e(105),n.e(747),n.e(220)]).then(n.bind(n,6220)))),s=l().createContext(null);class o extends l().PureComponent{render(){return l().createElement(s.Provider,{value:this.props},l().createElement(t,null))}}const p=o}}]);
//# sourceMappingURL=631.js.map