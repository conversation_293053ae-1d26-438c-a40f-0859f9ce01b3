{"version": 3, "file": "695.js", "mappings": "wOAiBA,MAAMA,EAAc,CAClB,CAACC,EAAAA,sBAAsBC,OAAQC,EAAAA,GAAcD,MAC7C,CAACD,EAAAA,sBAAsBG,UAAWD,EAAAA,GAAcC,SAChD,CAACH,EAAAA,sBAAsBI,YAAaF,EAAAA,GAAcG,WAClD,CAACL,EAAAA,sBAAsBM,eAAgBJ,EAAAA,GAAcK,eAGxC,SAASC,GAAwB,cAC9CC,EAAa,gBACbC,EAAe,KACfC,EAAI,GACJC,EAAE,uBACFC,EAAsB,aACtBC,IAEA,MAAMC,GAAsBC,EAAAA,EAAAA,uBAEtBC,GAAOC,EAAAA,EAAAA,UAAQ,KACnB,MAAMC,EAAYT,EAAgB,GAElC,IACGS,IAEDA,aAAAA,EAAAA,EAAWC,YAAapB,EAAAA,sBAAsBC,MAE9C,OAAO,KAGT,MAAMoB,GAAaC,EAAAA,EAAAA,IAAaH,EAAUI,OAE1C,IAAIC,EAAS,IAAIC,gBAwBjB,OAtBIhB,IACFe,GAASE,EAAAA,EAAAA,IAAgBC,EAAAA,GAAcC,aAAcnB,EAAee,IAGlEb,IACFa,GAASE,EAAAA,EAAAA,IAAgBC,EAAAA,GAAcE,cAAelB,EAAMa,IAG1DZ,IACFY,GAASE,EAAAA,EAAAA,IAAgBC,EAAAA,GAAcG,YAAalB,EAAIY,IAG1Dd,EAAgBqB,SAASC,IACvBR,GAASS,EAAAA,EAAAA,IACPN,EAAAA,GAAcO,OACd,GAAGF,EAAeG,QAAQpC,EAAYiC,EAAeZ,cAAagB,EAAAA,EAAAA,KAChEC,EAAAA,EAAAA,IAAqBL,EAAeT,YACjCa,EAAAA,EAAAA,KAAoBE,EAAAA,EAAAA,IAAmBN,EAAeT,UAC3DC,EAAAA,KAIGe,EAAAA,EAAAA,IAAa,YAAYpB,EAAUgB,QAAQd,SAAmBG,EAAO,GAC3E,CAACf,EAAeE,EAAMC,EAAIF,IAE7B,OAAKO,EAIDH,EACKA,EAAa,CAAEG,SAItB,kBAACuB,EAAAA,WAAUA,CACTC,QAAQ,YACRxB,KAAMA,EACNyB,QAAS,IAAM3B,EAAoBF,GAA0B,aAC9D,wBAZM,IAgBX,C", "sources": ["webpack://grafana-lokiexplore-app/./Components/OpenInExploreLogsButton/OpenInExploreLogsButton.tsx"], "sourcesContent": ["import { useReturnToPrevious } from '@grafana/runtime';\nimport { LinkButton } from '@grafana/ui';\nimport React, { useMemo } from 'react';\nimport {\n  appendUrlParameter,\n  createAppUrl,\n  escapeURLDelimiters,\n  replaceEscapeChars,\n  replaceSlash,\n  setUrlParameter,\n  stringifyAdHocValues,\n  UrlParameters,\n} from 'services/extensions/links';\nimport { OpenInExploreLogsButtonProps } from './types';\nimport { AbstractLabelOperator } from '@grafana/data';\nimport { LabelFilterOp } from 'services/filterTypes';\n\nconst operatorMap = {\n  [AbstractLabelOperator.Equal]: LabelFilterOp.Equal,\n  [AbstractLabelOperator.NotEqual]: LabelFilterOp.NotEqual,\n  [AbstractLabelOperator.EqualRegEx]: LabelFilterOp.RegexEqual,\n  [AbstractLabelOperator.NotEqualRegEx]: LabelFilterOp.RegexNotEqual,\n};\n\nexport default function OpenInExploreLogsButton({\n  datasourceUid,\n  streamSelectors,\n  from,\n  to,\n  returnToPreviousSource,\n  renderButton,\n}: OpenInExploreLogsButtonProps) {\n  const setReturnToPrevious = useReturnToPrevious();\n\n  const href = useMemo(() => {\n    const mainLabel = streamSelectors[0];\n\n    if (\n      !mainLabel ||\n      // we can't open in explore logs if main label matcher is something different from equal\n      mainLabel?.operator !== AbstractLabelOperator.Equal\n    ) {\n      return null;\n    }\n\n    const labelValue = replaceSlash(mainLabel.value);\n\n    let params = new URLSearchParams();\n\n    if (datasourceUid) {\n      params = setUrlParameter(UrlParameters.DatasourceId, datasourceUid, params);\n    }\n\n    if (from) {\n      params = setUrlParameter(UrlParameters.TimeRangeFrom, from, params);\n    }\n\n    if (to) {\n      params = setUrlParameter(UrlParameters.TimeRangeTo, to, params);\n    }\n\n    streamSelectors.forEach((streamSelector) => {\n      params = appendUrlParameter(\n        UrlParameters.Labels,\n        `${streamSelector.name}|${operatorMap[streamSelector.operator]}|${escapeURLDelimiters(\n          stringifyAdHocValues(streamSelector.value)\n        )},${escapeURLDelimiters(replaceEscapeChars(streamSelector.value))}`,\n        params\n      );\n    });\n\n    return createAppUrl(`/explore/${mainLabel.name}/${labelValue}/logs`, params);\n  }, [datasourceUid, from, to, streamSelectors]);\n\n  if (!href) {\n    return null;\n  }\n\n  if (renderButton) {\n    return renderButton({ href });\n  }\n\n  return (\n    <LinkButton\n      variant=\"secondary\"\n      href={href}\n      onClick={() => setReturnToPrevious(returnToPreviousSource || 'previous')}\n    >\n      Open in Explore Logs\n    </LinkButton>\n  );\n}\n"], "names": ["operatorMap", "AbstractLabelOperator", "Equal", "LabelFilterOp", "NotEqual", "EqualRegEx", "RegexEqual", "NotEqualRegEx", "RegexNotEqual", "OpenInExploreLogsButton", "datasourceUid", "streamSelectors", "from", "to", "returnToPreviousSource", "renderButton", "setReturnToPrevious", "useReturnToPrevious", "href", "useMemo", "mainLabel", "operator", "labelValue", "replaceSlash", "value", "params", "URLSearchParams", "setUrlParameter", "UrlParameters", "DatasourceId", "TimeRangeFrom", "TimeRangeTo", "for<PERSON>ach", "streamSelector", "appendUrlParameter", "Labels", "name", "escapeURLDelimiters", "stringifyAdHocValues", "replaceEscapeChars", "createAppUrl", "LinkButton", "variant", "onClick"], "sourceRoot": ""}