"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[695],{8695:(e,a,r)=>{r.r(a),r.d(a,{default:()=>i});var t=r(8531),o=r(2007),l=r(5959),u=r.n(l),n=r(7608),s=r(7781),c=r(4793);const p={[s.AbstractLabelOperator.Equal]:c.KQ.Equal,[s.AbstractLabelOperator.NotEqual]:c.KQ.NotEqual,[s.AbstractLabelOperator.EqualRegEx]:c.KQ.RegexEqual,[s.AbstractLabelOperator.NotEqualRegEx]:c.KQ.RegexNotEqual};function i({datasourceUid:e,streamSelectors:a,from:r,to:c,returnToPreviousSource:i,renderButton:E}){const b=(0,t.useReturnToPrevious)(),x=(0,l.useMemo)((()=>{const t=a[0];if(!t||(null==t?void 0:t.operator)!==s.AbstractLabelOperator.Equal)return null;const o=(0,n.uu)(t.value);let l=new URLSearchParams;return e&&(l=(0,n.xh)(n.I8.DatasourceId,e,l)),r&&(l=(0,n.xh)(n.I8.TimeRangeFrom,r,l)),c&&(l=(0,n.xh)(n.I8.TimeRangeTo,c,l)),a.forEach((e=>{l=(0,n.zH)(n.I8.Labels,`${e.name}|${p[e.operator]}|${(0,n.XH)((0,n.vh)(e.value))},${(0,n.XH)((0,n.rx)(e.value))}`,l)})),(0,n.Rk)(`/explore/${t.name}/${o}/logs`,l)}),[e,r,c,a]);return x?E?E({href:x}):u().createElement(o.LinkButton,{variant:"secondary",href:x,onClick:()=>b(i||"previous")},"Open in Explore Logs"):null}}}]);
//# sourceMappingURL=695.js.map