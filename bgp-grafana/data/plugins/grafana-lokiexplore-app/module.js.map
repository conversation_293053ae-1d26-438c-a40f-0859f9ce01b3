{"version": 3, "file": "module.js", "mappings": "uMACIA,EADAC,ECAAC,EACAC,E,oGCEJ,MAAMC,GAA0BC,EAAAA,EAAAA,OAAK,IAAM,gCAgB9BC,EAAoB,CAC/B,CACEC,GAAI,yDACJC,MAAO,8BACPC,YAAa,2DACbC,UAnBJ,SAA0CC,GACxC,OACE,kBAACC,EAAAA,SAAQA,CACPC,SACE,kBAACC,EAAAA,WAAUA,CAACC,QAAQ,YAAYC,UAAAA,GAAS,yBAK3C,kBAACZ,EAA4BO,GAGnC,I,4TCTA,MAAMM,GAAMZ,EAAAA,EAAAA,MAAIA,GAAC,YACf,MAAM,cAAEa,SAAwB,yEAExBC,QAASC,SAAwB,yEACjCD,QAASE,SAA0B,+BACnCF,QAASG,SAAsB,8BAQvC,OANAF,IAEIF,YACIK,QAAQC,IAAI,CAACH,IAAmBC,OAGjC,6BACT,KAEMG,GAAYpB,EAAAA,EAAAA,MAAIA,GAAC,YACrB,aAAa,6BACf,KAEaqB,GAAS,IAAIC,EAAAA,WAAgBC,YAAYX,GAAKY,cAAc,CACvErB,MAAO,gBACPsB,KAAM,MACNC,KAAMN,EACNlB,GAAI,kBAGN,IAAK,MAAMyB,KAAcC,EAAAA,GACvBP,EAAOQ,QAAQF,GAGjB,IAAK,MAAMG,KAA0B7B,EACnCoB,EAAOU,gBAAgBD,E,uMCbzB,MAAME,EAAe,yBACf7B,EAAQ,WAAW6B,IACnB5B,EAAc,6BAA6B4B,SAC3CP,EAAO,UAEAQ,EAAkB,CAC7BC,oBAAqB,4CAYVN,EAA2B,CACtC,CACEO,QAASC,EAAAA,sBAAsBC,mBAC/BlC,QACAC,cACAqB,OACAa,KAAMC,IACNC,UAAWC,GAEb,CACEN,QAASC,EAAAA,sBAAsBM,qBAC/BvC,QACAC,cACAqB,OACAa,KAAMC,IACNC,UAAWC,IAIf,SAASE,EAAgBC,GACvB,OAAKA,GACI,IAGX,CAGO,SAASC,EAAmBD,GACjC,OAAOA,aAAAA,EAAAA,EAAOE,QAAQ,QAAS,KACjC,CAEO,SAASC,EAAqBH,GACnC,OAAKA,GAKEI,EAAAA,EAAAA,IAA8BH,EAAmBD,IAJ/C,IAKX,CAEA,SAASH,EAAqDQ,G,IAKzCC,EAoBsCA,EAxBzD,IAAKD,EACH,OAEF,MAAMC,EAAYD,EAAQd,QAAQgB,MAAMC,I,IAAWA,E,MAA4B,UAAX,QAAjBA,EAAAA,EAAOC,kBAAPD,IAAAA,OAAAA,EAAAA,EAAmBE,KAAe,IACrF,IAAKJ,KAAkC,QAApBA,EAAAA,EAAUG,kBAAVH,IAAAA,OAAAA,EAAAA,EAAsBK,KACvC,OAGF,MAAMC,EAAON,EAAUM,MACjB,aAAEC,EAAY,YAAEC,EAAW,OAAEC,EAAM,eAAEC,IAAmBC,EAAAA,EAAAA,IAAoBL,EAAMP,EAASC,GAC3FY,EAAgBL,EAAaN,MAAMY,IAAaC,EAAAA,EAAAA,IAAoBD,EAASE,YAGnF,IAAKH,EACH,OAIF,MACMI,EAAaC,EADGL,EAAclB,MAAMwB,MAAM,KAAK,IAErD,IAAIC,EAAYP,EAAcQ,MAAQC,EAAAA,GAAe,UAAYT,EAAcQ,IAE/Eb,EAAae,MAAMC,GAAOA,EAAEH,MAAQD,GAAa,EAAI,IAErD,IAAIK,EAASC,EAAgBC,EAAcC,aAAkC,QAApB3B,EAAAA,EAAUG,kBAAVH,IAAAA,OAAAA,EAAAA,EAAsBK,IAAK,IAAIuB,iBACxFJ,EAASC,EAAgBC,EAAcG,cAAe9B,EAAQ+B,UAAUC,KAAKC,UAAUC,WAAYT,GACnGA,EAASC,EAAgBC,EAAcQ,YAAanC,EAAQ+B,UAAUK,GAAGH,UAAUC,WAAYT,GAE/F,IAAK,MAAMY,KAAe7B,EAAc,CAEtC,GAAI6B,EAAYhC,OAASiC,EAAAA,EAAUC,QACjC,SAGF,MAAMC,EAA6B,GAAGH,EAAYhB,OAAOgB,EAAYrB,YAAYyB,EAC/E3C,EAAqBuC,EAAY1C,WAC9B8C,EAAoB7C,EAAmByC,EAAY1C,UAExD8B,EAASiB,EAAmBf,EAAcgB,OAAQH,EAA4Bf,EAChF,CAEA,GAAIhB,EACF,IAAK,MAAMmC,KAAcnC,EACvBgB,EAASiB,EACPf,EAAckB,YACd,GAAGD,EAAWvB,OAAOoB,EAAoBG,EAAW5B,aAAayB,EAC/D/C,EAAgBkD,EAAWjD,UAE7B8B,GAIN,GAAIf,aAAAA,EAAAA,EAAQoC,OACV,IAAK,MAAMC,KAASrC,EAClB,GAAIqC,EAAM1C,OAASiC,EAAAA,EAAUU,mBAEzBvB,EADEsB,EAAM1B,MAAQ4B,EAAAA,GACPP,EACPf,EAAcuB,OACd,GAAGH,EAAM1B,OAAO0B,EAAM/B,YAAYyB,EAAoB/C,EAAgBqD,EAAMpD,UAC5E8B,GAGOiB,EACPf,EAAcwB,SACd,GAAGJ,EAAM1B,OAAO0B,EAAM/B,YAAYyB,EAChC3C,EAAqBiD,EAAMpD,WACxB8C,EAAoB7C,EAAmBmD,EAAMpD,UAClD8B,OAGC,CACL,MAAM2B,EAA8B,CAClCzD,MAAOoD,EAAMpD,MACb0D,OAAQN,EAAMM,QAGVC,EAAuB,GAAGP,EAAM1B,OAAO0B,EAAM/B,YAAYyB,EAC7D3C,EAAqByD,KAAKC,UAAUJ,QACjCX,EAAoB7C,EAAmBwD,EAAWzD,UAEvD8B,EAASiB,EAAmBf,EAAc8B,OAAQH,EAAsB7B,EAC1E,CAGJ,GAAId,aAAAA,EAAAA,EAAgBmC,OAAQ,CAC1B,MAAMY,EAA6B,GAEnC,IAAK,MAAMX,KAASpC,EAClB+C,EAASC,KAAK,CACZtD,KAAM0C,EAAM/B,WAAa4C,EAAAA,GAAgBC,MAAQ,UAAY,UAC7DC,QAASpE,EAAgBqD,EAAMpD,SAInC,IAAIoE,GAAiBC,EAAAA,EAAAA,GAAqBN,GAE1CjC,EAASiB,EAAmBf,EAAcsC,SAAUV,KAAKC,UAAUE,GAAWjC,GAC9EA,EAASiB,EAAmBf,EAAcuC,iBAAkBH,EAAgBtC,EAC9E,CAEA,MAAO,CACLpC,KAAMC,EAAa,YAAY8B,KAAaH,SAAmBQ,GAEnE,CAEO,SAASnC,EAAaD,EAAO,WAAY8E,GAC9C,MAAO,MAAMC,EAAAA,KAAgB/E,IAAO8E,EAAY,IAAIA,EAAUjC,aAAe,IAC/E,CAEO,MAAMP,EAAgB,CAC3BC,aAAc,OAAOyC,EAAAA,KACrBvC,cAAe,OACfK,YAAa,KACbQ,OAAQ,OAAO2B,EAAAA,KACfb,OAAQ,OAAOc,EAAAA,KACfpB,SAAU,OAAOqB,EAAAA,KACjBtB,OAAQ,OAAOuB,EAAAA,KACf5B,YAAa,OAAO6B,EAAAA,KACpBT,SAAUU,EAAAA,GACVT,iBAAkB,OAAOS,EAAAA,MAIpB,SAASjD,EAAgBL,EAAuB1B,EAAeiF,G,IAC3BA,EAAzC,MAAMC,EAAe,IAAIhD,gBAAsC,QAAtB+C,EAAAA,aAAAA,EAAAA,EAAc1C,kBAAd0C,IAAAA,EAAAA,EAA4BE,SAASC,QAG9E,OAFAF,EAAaG,IAAI3D,EAAK1B,GAEfkF,CACT,CAEO,SAASnC,EACdrB,EACA1B,EACAiF,G,IAEyCA,EAAzC,MAAMC,EAAe,IAAIhD,gBAAsC,QAAtB+C,EAAAA,aAAAA,EAAAA,EAAc1C,kBAAd0C,IAAAA,EAAAA,EAA4BE,SAASC,QAG9E,OAFAF,EAAaI,OAAO5D,EAAK1B,GAElBkF,CACT,CAEO,SAAS3D,EAAagE,GAC3B,OACEC,EAAAA,EAAAA,IAAgCD,GAE7BrF,QAAQ,MAAO,KACfA,QAAQ,MAAO,IAEtB,CAqBO,SAAS4C,EAAoB9C,GAClC,OAnBF,SAAkCA,GAChC,OAAIA,QACK,GAIF,KAAKyF,OAAOvF,SAASF,EAAO,UACrC,CAYS0F,CAVF,SAAiC1F,GACtC,OAAIA,QACK,GAIO,MAAMyF,OAAOvF,SAASF,EAAO,UAC/C,CAGkC2F,CAAwB3F,GAC1D,C,kCChQO,eAAK2C,G,2DAAAA,C,CAAL,C,2MCKA,eAAKiD,G,0EAAAA,C,CAAL,C,IAOA,WAAKC,G,+CAAAA,C,CAAL,C,IAMA,MAAMC,E,sUAAW,IAAKF,EAAkBC,GA4BxC,eAAKE,G,2EAAAA,C,CAAL,C,IAOA,WAAK9B,G,yCAAAA,C,CAAL,C,IAKA,WAAK+B,G,2EAAAA,C,CAAL,C,ugBCrDP,MAAMC,EAAiB,CACrBC,IAAKzB,EAAAA,GACL0B,Q,SAGWC,EAAS,CACpBC,KAAM,CAACC,EAAajG,KAClB,MAAMkG,EAAM,KAAKN,EAAmB5F,GACpCmG,QAAQC,IAAIH,EAAKC,GACjBG,EAAgBJ,EAAKC,EAAI,EAE3BI,KAAM,CAACL,EAAajG,KAClB,MAAMkG,EAAM,KAAKN,EAAmB5F,GACpCmG,QAAQG,KAAKL,EAAKC,GAClBK,EAAgBN,EAAKC,EAAI,EAE3BM,MAAO,CAACC,EAAsBzG,KAC5B,MAAMkG,EAAM,KAAKN,EAAmB5F,GACpCmG,QAAQK,MAAMC,EAAKP,GACnBQ,EAAeD,EAAKP,EAAI,GAItBG,EAAkB,CAACJ,EAAajG,KACpC,KACE2G,EAAAA,EAAAA,SAAQV,EAAKjG,EACf,CAAE,MAAO4G,GACPT,QAAQG,KAAK,4BACf,GAGIC,EAAkB,CAACN,EAAajG,KACpC,KACE6G,EAAAA,EAAAA,YAAWZ,EAAKjG,EAClB,CAAE,MAAO4G,GACPT,QAAQG,KAAK,8BAA+B,CAAEL,MAAKjG,WACrD,GAgCI0G,EAAiB,CAACD,EAAmCK,KACzD,IAAI9G,EAAU8G,EACd,KA3BF,SAAmCL,EAA2BzG,GAC5D,GAAmB,iBAARyG,GAA4B,OAARA,KACzBM,EAAAA,EAAAA,IAASN,IACXO,OAAOC,KAAKR,GAAKS,SAAS7F,IACxB,MAAM1B,EAAQ8G,EAAIpF,GACG,iBAAV1B,GAAuC,kBAAVA,GAAwC,iBAAVA,IACpEK,EAAQqB,GAAO1B,EAAMuC,WACvB,IAIAiF,EAAQV,IACV,GAAwB,iBAAbA,EAAIW,MAAkC,OAAbX,EAAIW,KACtC,IACEpH,EAAQoH,KAAO7D,KAAKC,UAAUiD,EAAIW,KACpC,CAAE,MAAOR,GAET,KAC6B,iBAAbH,EAAIW,MAAyC,kBAAbX,EAAIW,MAA0C,iBAAbX,EAAIW,OACrFpH,EAAQoH,KAAOX,EAAIW,KAAKlF,WAIhC,CAKImF,CAA0BZ,EAAKzG,GAE3ByG,aAAea,OACjBC,EAAAA,EAAAA,UAASd,EAAKzG,GACU,iBAARyG,GAChBc,EAAAA,EAAAA,UAAS,IAAID,MAAMb,GAAMzG,GAChByG,GAAsB,iBAARA,EACnBzG,EAAQiG,KACVsB,EAAAA,EAAAA,UAAS,IAAID,MAAMtH,EAAQiG,KAAMjG,IAEjCuH,EAAAA,EAAAA,UAAS,IAAID,MAAM,gBAAiBtH,IAGtCuH,EAAAA,EAAAA,UAAS,IAAID,MAAM,iBAAkBtH,EAEzC,CAAE,MAAO4G,GACPT,QAAQK,MAAM,4BAA6B,CAAEC,MAAKzG,WACpD,GAGImH,EAAWxH,GACR,SAAUA,C,wNCnDZ,MAAM6H,EAaX,eAAOC,CAASC,GACd,OAAO,IAAIF,EAAaE,EAAK1F,KAAM0F,EAAKtF,GAAIsF,EAAMA,EAAKrH,KACzD,CAEAsH,QAAAA,CAASC,GACP,OAAOC,KAAK7F,MAAQ4F,EAAS5F,MAAQ6F,KAAKzF,IAAMwF,EAASxF,EAC3D,CAEA0F,aAAAA,CAAcC,GACZ,OAAOA,EAAMC,UAAUH,KAAK7F,KAAM6F,KAAKzF,GACzC,CAjBA6F,WAAAA,CAAYjG,EAAcI,EAAY8F,EAAyB7H,GAL/D2B,EAAAA,KAAAA,YAAAA,GACAI,EAAAA,KAAAA,UAAAA,GACA/B,EAAAA,KAAAA,YAAAA,GACA6H,EAAAA,KAAAA,kBAAAA,GAGEL,KAAK7F,KAAOA,EACZ6F,KAAKzF,GAAKA,EACVyF,KAAKxH,KAAOA,EACZwH,KAAKK,WAAaA,CACpB,EAeK,SAASC,EAAkBJ,EAAeK,GAC/C,MAAMC,EAAsB,GAS5B,OARmBhF,EAAAA,GAAOiF,MAAMP,GAC3BQ,QAAQ,CACXC,MAAQd,UACYe,IAAdL,GAA2BA,EAAUM,SAAShB,EAAKrH,KAAKpD,MAC1DoL,EAAM1E,KAAK+D,EAAKA,KAClB,IAGGW,CACT,CA4BA,SAASM,EAA4BjB,EAAkBrH,GACrD,GAAIqH,EAAKrH,KAAKpD,KAAOoD,EACnB,MAAO,CAACmH,EAAaC,SAASC,IAGhC,MAAMkB,EAA4B,GAClC,IAAIC,EAAM,EACNC,EAAQpB,EAAKqB,WAAWF,GAC5B,KAAOC,GACLF,EAAUjF,QAAQgF,EAA4BG,EAAOzI,IACrDwI,EAAMC,EAAM1G,GACZ0G,EAAQpB,EAAKqB,WAAWF,GAE1B,OAAOD,CACT,CAgCA,SAASI,EACPC,EACAC,EACAzI,EACA0I,EACAnI,GAEA,MAAMoI,EAAkBpI,IAAa0E,EAAAA,GAAa2D,OAASrI,IAAa0E,EAAAA,GAAa4D,cAC/EC,EAAoBN,EAAgBP,SAAS,SAAWU,EAI9D,GAAoB,MAAhBF,GAAuBE,EAAiB,CAC1C,MAAMI,EAAsB,IAAIC,OAAO,OAAQ,KAC/CR,EAAkBA,EAAgBpJ,QAAQ2J,EAAqB,KACjE,MAAO,GAAoB,MAAhBN,EAAqB,CAC9B,MAAMQ,EAA2B,IAAID,OAAO,SAAU,KACtDR,EAAkBA,EAAgBpJ,QAAQ6J,EAA0B,KAEpE,MAAMF,EAAsB,IAAIC,OAAO,OAAQ,KAC/CR,EAAkBA,EAAgBpJ,QAAQ2J,EAAqB,KACjE,CAeA,OAbID,IAEFN,EAAkBA,EAAgBpJ,QAAQ,OAAQ,KAGpDY,EAAYkD,KAAK,CACftC,IAAKkI,EACD5D,EAAAA,GAAwBgE,gBAAgBzH,WACxCyD,EAAAA,GAAwBiE,cAAc1H,WAAa,IAAMiH,EAAMjH,WACnElB,SAAUA,EACVrB,MAAOsJ,IAGFA,CACT,CAEA,SAASY,EAAoBZ,EAAyBtI,EAAqCK,GACzF,MAAM0I,EAA2B,IAAID,OAAO,MAAO,KACnDR,EAAkBA,EAAgBpJ,QAAQ6J,EAA0B,KACpE/I,EAAegD,KAAK,CAClB3C,WACArB,MAAOsJ,GAEX,CAoDA,SAASa,EAAwBC,GAC/B,OAAIpB,EAA4BoB,EAASC,EAAAA,IAAKlH,OACrCmH,EAAAA,GAAeC,IACbvB,EAA4BoB,EAASI,EAAAA,IAAKrH,OAC5CmH,EAAAA,GAAeG,GACbzB,EAA4BoB,EAASM,EAAAA,IAAKvH,OAC5CmH,EAAAA,GAAeK,IACb3B,EAA4BoB,EAASQ,EAAAA,IAAKzH,OAC5CmH,EAAAA,GAAeO,QAGxBrE,QAAQG,KAAK,2BAGf,CAEA,SAASmE,EAAuBV,GAC9B,OAAIpB,EAA4BoB,EAASW,EAAAA,IAAI5H,OACpCmH,EAAAA,GAAeU,MACbhC,EAA4BoB,EAASa,EAAAA,IAAK9H,OAC5CmH,EAAAA,GAAeY,SACblC,EAA4BoB,EAASe,EAAAA,IAAIhI,OAC3CmH,EAAAA,GAAec,WACbpC,EAA4BoB,EAASiB,EAAAA,IAAKlI,OAC5CmH,EAAAA,GAAegB,mBADjB,CAKT,CAiFO,SAASrK,EACdmH,EACA/H,EACAC,GAOA,MAAMiL,EAA+B,GAC/BzK,EAAgC,GAChCE,EAAsC,GACtCD,EAAwB,GACxBI,EAAWqH,EAAkBJ,EAAO,CAACoD,EAAAA,KAE3C,OAAwB,IAApBrK,EAASgC,OACJ,CAAEtC,aAAc0K,IA9P3B,SAA2BnD,EAAemD,GAExC,MAAME,EAAajD,EAAkBJ,EAAO,CAACsD,EAAAA,KAC7C,IAAK,MAAMtB,KAAWqB,EAAY,C,IAGCE,EAA2BC,EAF5D,MAAMD,EAAqB3C,EAA4BoB,EAASyB,EAAAA,IAC1DD,EAAgB5C,EAA4BoB,EAAS0B,EAAAA,IACrDzK,EAAW+G,EAAMC,UAA+B,QAArBsD,EAAAA,EAAmB,UAAnBA,IAAAA,OAAAA,EAAAA,EAAuBlJ,GAAoB,QAAhBmJ,EAAAA,EAAc,UAAdA,IAAAA,OAAAA,EAAAA,EAAkBvJ,MACxEX,EAAMiK,EAAmB,GAAGxD,cAAcC,GAC1CpI,EAAQ4L,EAAcG,KAAK9D,GAAaG,EAAMC,UAAUJ,EAAS5F,KAAO,EAAG4F,EAASxF,GAAK,KAAI,GAGhGf,GACA1B,IACAqB,IAAaiJ,EAAAA,GAAeY,UAC3B7J,IAAaiJ,EAAAA,GAAeU,OAC5B3J,IAAaiJ,EAAAA,GAAec,YAC5B/J,IAAaiJ,EAAAA,GAAegB,gBAKhCC,EAAOvH,KAAK,CACVtC,MACAL,WACArB,QACAU,KAAMiC,EAAAA,EAAUC,SAEpB,CACF,CAwOEoJ,CAFsBhD,EAA4B7H,EAAS,GAAIqK,EAAAA,IAAU,GAAGrD,cAAcC,GAEzDmD,GAtLnC,SAA0BnD,EAAetH,EAA+BE,GACtE,MAAMiL,EAAiBzD,EAAkBJ,EAAO,CAAC8D,EAAAA,KACjD,IAAK,MAAO1C,EAAOY,KAAY6B,EAAeE,UAAW,CACvD,MAAMC,EAAQpD,EAA4BoB,EAASiC,EAAAA,IAC7CC,EAAatD,EAA4BoB,EAASmC,EAAAA,IAClDC,EAAWxD,EAA4BoB,EAASa,EAAAA,IAChDwB,EAAiBzD,EAA4BoB,EAASiB,EAAAA,IACtDqB,EAAiB1D,EAA4BoB,EAASuC,EAAAA,IACtDC,EAAiB5D,EAA4BoB,EAASyC,EAAAA,IAEtDC,EAAuBC,EAAyB3C,GAEtD,IAAK,MAAM4C,KAAuBF,EAAsB,CACtD,MAAMvD,EAAcnB,EAAMC,WAAU2E,aAAAA,EAAAA,EAAqB3K,MAAO,EAAG2K,aAAAA,EAAAA,EAAqB3K,MAGxF,IAAIiH,EAAkBlB,EAAMC,WAAU2E,aAAAA,EAAAA,EAAqB3K,MAAO,GAAG2K,aAAAA,EAAAA,EAAqBvK,IAAK,GAE/F,GAAI6G,EAAgBnG,OAAQ,CAC1B,IAAI9B,EACJ,GAAI+K,EAAMjJ,OACR9B,EAAW0E,EAAAA,GAAa7B,WACnB,GAAIsI,EAASrJ,OAClB9B,EAAW0E,EAAAA,GAAakH,mBACnB,GAAIR,EAAetJ,OACxB9B,EAAW0E,EAAAA,GAAa4D,mBACnB,GAAI2C,EAAWnJ,OACpB9B,EAAW0E,EAAAA,GAAa2D,WACnB,GAAIgD,EAAevJ,OACxB9B,EAAW4C,EAAAA,GAAgBC,UACtB,KAAI0I,EAAezJ,OAEnB,CACLqD,QAAQG,KAAK,sBAAuB,CAClCyB,MAAOA,EAAMC,UAAU+B,EAAQ/H,KAAM+H,EAAQ3H,MAG/C,QACF,CAPEpB,EAAW4C,EAAAA,GAAgBgJ,aAO7B,CAEM5L,IAAa4C,EAAAA,GAAgBC,OAAS7C,IAAa4C,EAAAA,GAAgBgJ,cACvE5D,EAAuBC,EAAiBC,EAAazI,EAAa0I,EAAOnI,GAEzE6I,EAAoBZ,EAAiBtI,EAAgBK,EAEzD,CACF,CACF,CACF,CAuIE6L,CAAiB9E,EAAOtH,EAAaE,GAvGvC,SAAqBoH,EAAerH,EAAuBV,EAAsCC,G,IAC7ED,EAAlB,MAAM8M,EAAwB,QAAZ9M,EAAAA,EAAQoH,YAARpH,IAAAA,OAAAA,EAAAA,EAAc+M,OAAO7M,MAAM8M,GAAUA,EAAMC,QAAUhN,EAAUgN,QAE3EC,EAtMD,SAAkCnF,EAAeK,GACtD,MAAMC,EAAsB,GAiB5B,OAhBmBhF,EAAAA,GAAOiF,MAAMP,GAE3BQ,QAAQ,CACXC,MAAQd,IACN,GAAIU,EAAUM,SAAShB,EAAKrH,KAAKpD,IAAK,CACpC,IAAIkQ,EACJ,KAA8C,QAAtCA,EAAYzF,EAAKA,KAAK0F,aAAsB,CAClD,IAAKhF,EAAUM,SAASyE,EAAUzF,KAAKrH,KAAKpD,IAE1C,OADAoL,EAAM1E,KAAK+D,EAAKA,OACT,EAETA,EAAOyF,CACT,CACF,KAGG9E,CACT,CAmLoBgF,CAAyBtF,EAAO,CAACuF,EAAAA,KAEnD,IAAK,MAAMvD,KAAWmD,EAAW,C,IAgBbK,EAXlB,GAAmC,cAJlB/F,EAAaC,SAASsC,GACXjC,cAAcC,GAG3BC,UAAU,EAAG,GAC1B,SAKF,MAAMwF,EAAerF,EAAkBJ,EAAMC,UAAU,EAAG+B,EAAQrC,KAAKtF,IAAK,CAACqL,EAAAA,KACvEC,EAAavF,EAAkBJ,EAAMC,UAAU,EAAG+B,EAAQrC,KAAKtF,IAAK,CAACuL,EAAAA,KAIrEC,EAA4B,QAAhBL,EADI5E,EAA4BoB,EAASyB,EAAAA,IAC3B,UAAd+B,IAAAA,OAAAA,EAAAA,EAAkBzF,cAAcC,GAG5C8F,EAAmBlF,EAA4BoB,EAAS0B,EAAAA,IACxDqC,EAAmBnF,EAA4BoB,EAASgE,EAAAA,IACxDC,EAAkBrF,EAA4BoB,EAASkE,EAAAA,IACvDC,EAAqBvF,EAA4BoB,EAASoE,EAAAA,IAEhE,IAAI/K,EAAoBpC,EAmBpBoN,EAlBJ,GAAIP,EAAiB/K,OACnB9B,EAAWyJ,EAAuBV,GAElC3G,EAAa2E,EAAMC,UAAU6F,EAAiB,GAAG7L,KAAO,EAAG6L,EAAiB,GAAGzL,GAAK,QAC/E,GAAI0L,EAAiBhL,OAC1BM,EAAa0K,EAAiB,GAAGhG,cAAcC,GAC/C/G,EAAW8I,EAAwBC,QAC9B,GAAImE,EAAmBpL,OAC5B9B,EAAW8I,EAAwBC,GACnC3G,EAAa8K,EAAmB,GAAGpG,cAAcC,OAC5C,KAAIiG,EAAgBlL,OAIzB,SAHA9B,EAAW8I,EAAwBC,GACnC3G,EAAa4K,EAAgB,GAAGlG,cAAcC,EAGhD,C,IAOcsG,EAGd,GANIvB,IAGFsB,EAA6CtB,QAAjCuB,GAAAA,EAAAA,EAAAA,GAAsBT,EAAWd,UAAjCuB,IAAAA,EAAAA,OAA+C5F,GAGzDzH,EAAU,CACZ,IAAIqC,EACAmK,EAAa1K,QAAU4K,EAAW5K,OACpCO,EAAS,QACAmK,EAAa1K,OACtBO,EAAS,SACAqK,EAAW5K,OACpBO,EAAS,OAGT+K,EAAY9L,EAAAA,EAAUU,mBAGxBtC,EAAOiD,KAAK,CACVtC,IAAKuM,EACL5M,SAAUA,EACVX,KAAM+N,QAAAA,EAAa9L,EAAAA,EAAUgM,OAC7BjL,SACA1D,MAAOyD,GAEX,CACF,CACF,CA2BEmL,CAAYxG,EAAOrH,EAAQV,EAASC,GAE7B,CAAEO,aAAc0K,EAAQzK,cAAaC,SAAQC,kBACtD,CAqBO,MAAM6N,EAAU,EAChB,SAASC,EAAa1G,GAC3B,OAA2C,IArBtC,SAAyBA,EAAe2G,GAC7C,IAAIC,GAAkB,EAUtB,OATatL,EAAAA,GAAOiF,MAAMP,GACrBQ,QAAQ,CACXC,MAAO,EAAGnI,WACR,GAAIA,EAAKpD,KAAOyR,EAEd,OADAC,GAAkB,GACX,CACT,IAGGA,CACT,CASSA,CAAgB5G,EAAOyG,EAChC,CAEA,SAAS9B,EAAyBxB,GAChC,MAAM7C,EAAsB,GAC5B,IAAIX,EAA0BwD,EAC9B,EAAG,CACD,MAAM0D,EAASlH,EAAKmH,SAASpD,EAAAA,IACzBmD,IAAWlH,EAAKmH,SAASpJ,EAAAA,KAC3B4C,EAAM1E,KAAKiL,GAEblH,EAAOA,EAAKmH,SAASC,EAAAA,GACvB,OAAiB,MAARpH,GAET,OAAOW,CACT,C,uDCzaO,WAAK0G,G,+DAAAA,C,CAAL,C,IA6BA,SAASV,EAAsBW,EAAkBhC,EAAkB7D,EAAQ,G,IAC9D6D,EAAlB,MAAMiC,EAAwD,QAA5CjC,EAAAA,EAAMtM,OAAOR,MAAM6C,GAAyB,eAAfA,EAAMmM,cAAnClC,IAAAA,OAAAA,EAAAA,EAA2DmC,OAAOhG,GACpF,IAAK8F,EACH,OAAO,KAET,OAAQA,EAAUD,IAChB,IAAK,IACH,OAAO1M,EAAAA,EAAUC,QACnB,IAAK,IACH,OAAOD,EAAAA,EAAUU,mBACnB,IAAK,IACH,OAAOV,EAAAA,EAAUgM,OACnB,QACE,OAAO,KAEb,C,4IC5CA,MAAMc,EAASC,GAAyC,iBAANA,GAAwB,OAANA,EAEpE,SAASC,EAA+BlI,EAAcmI,GACpD,OAAOA,KAAQnI,CACjB,CAEA,MAAMoI,EAAYC,GAA6B,iBAANA,GAAkBA,GAAM,GAEpD1I,EAAY2I,GAAgE,iBAARA,EAE1E,SAASC,EAAiBnO,GAC/B,IAAIoO,EAAoB,GACxB,GAAIC,MAAMC,QAAQtO,GAChB,IAAK,IAAIuO,EAAI,EAAGA,EAAIvO,EAAEsB,OAAQiN,IAC5BH,EAAQjM,KAAK6L,EAAShO,EAAEuO,KAG5B,OAAOH,CACT,CAEO,SAASI,EAAuBX,GACrC,MAAMY,EAAWb,EAAMC,IAAMC,EAAQD,EAAG,QAAUC,EAAQD,EAAG,OAASA,EAEtE,GAAIY,EAAU,CACZ,MAAMC,EAA8B,iBAAjBD,EAASC,KAAoBD,EAASC,IACnDjT,EAA4B,iBAAhBgT,EAAShT,IAAmBgT,EAAShT,GACvD,IAAW,IAAPA,IAAwB,IAARiT,EAClB,MAAO,CAAEA,MAAKjT,KAElB,CAEA,OAAO,CACT,CAEO,SAASkT,EAA4Bd,GAC1C,MAAoB,iBAANA,IAAyB,SAANA,GAAsB,UAANA,IAAkBA,CACrE,CACO,SAASe,EAAoBf,GAClC,MAAiB,iBAANA,GAAkBA,IAAMgB,EAAAA,cAAcC,UAAUpO,WAClDmO,EAAAA,cAAcC,UAGN,iBAANjB,GAAkBA,IAAMgB,EAAAA,cAAcE,WAAWrO,YACnDmO,EAAAA,cAAcE,UAIzB,CAEO,SAASC,EAAiBnB,GAC/B,MAAMY,EAAWb,EAAMC,IAAMC,EAAQD,EAAG,UAAYC,EAAQD,EAAG,WAAaA,EAE5E,GAAIY,EAAU,CACZ,MAAM5M,EACuB,iBAApB4M,EAAS5M,SACK,WAApB4M,EAAS5M,QACY,SAApB4M,EAAS5M,QACW,UAApB4M,EAAS5M,QACW,uBAApB4M,EAAS5M,SACX4M,EAAS5M,OACL1D,EAAkC,iBAAnBsQ,EAAStQ,OAAsBsQ,EAAStQ,MAE7D,IAAe,IAAX0D,IAA8B,IAAV1D,EACtB,MAAO,CAAE0D,SAAQ1D,QAErB,CAEA,OAAO,CACT,CAEO,SAAS8Q,EAAyBpB,GACvC,MAAMY,EAAWb,EAAMC,IAAMtI,EAASsI,IAAMA,EAE5C,GAAIY,EAAU,CACZ,MAAMhJ,EAAOD,OAAOC,KAAKgJ,GACnBS,EAAuC,CAAC,EAC9C,IAAK,IAAIX,EAAI,EAAGA,EAAI9I,EAAKnE,OAAQiN,IAAK,CACpC,MAAM1O,EAAM4F,EAAK8I,GACXpQ,EAAQsQ,EAAShJ,EAAK8I,IACP,iBAAVpQ,IACT+Q,EAAarP,GAAO1B,EAExB,CAEA,OAAO+Q,CACT,CAEA,OAAO,CACT,CAEO,SAASC,EAAgBC,GAC9B,MAAMC,EAAQzB,EAAMwB,IAAiBtB,EAAQsB,EAAc,OAAStB,EAAQsB,EAAc,SAAWA,EACrG,GAAIC,EAAO,CACT,MAAMzO,EAAKoN,EAASqB,EAAMzO,IACpBJ,EAAOwN,EAASqB,EAAM7O,MAC5B,GAAII,GAAMJ,EACR,MAAO,CAAEI,KAAIJ,OAEjB,CAGF,CAEO,SAAS8O,EAAqBC,GACnC,OAAQA,GACN,KAAKxL,EAAAA,GAAcoF,MACnB,KAAKpF,EAAAA,GAAcsF,SACnB,KAAKtF,EAAAA,GAAcwF,WACnB,KAAKxF,EAAAA,GAAc0F,cACnB,KAAKzF,EAAAA,GAAgBgF,GACrB,KAAKhF,EAAAA,GAAgB8E,IACrB,KAAK9E,EAAAA,GAAgB4E,GACrB,KAAK5E,EAAAA,GAAgB0E,IACnB,OAAO6G,EACT,QACE,MAAM,IAAIC,EAAe,wBAE/B,CAEO,MAAMA,UAAuB1J,O,sFC1H7B,MAAMvG,EAAuBgQ,GAC3BA,IAAOtL,EAAAA,GAASkF,OAASoG,IAAOtL,EAAAA,GAASsF,WAErCkG,EAAuBF,GAC3BA,IAAOtL,EAAAA,GAASoF,UAAYkG,IAAOtL,EAAAA,GAASwF,cAExCiG,EAAmBH,GACvBA,IAAOtL,EAAAA,GAASsF,YAAcgG,IAAOtL,EAAAA,GAASwF,cAE1CkG,EAAqBJ,GACzBK,EAAAA,GAAqB1I,SAASqI,E,+FCVhC,SAASM,EAAuBN,GACrC,GAAIA,IAAOtL,EAAAA,GAASoF,SAClB,MAAO,YAET,GAAIkG,IAAOtL,EAAAA,GAASwF,cAClB,MAAO,uBAET,GAAI8F,IAAOtL,EAAAA,GAASkF,MAClB,MAAO,SAET,GAAIoG,IAAOtL,EAAAA,GAASsF,WAClB,MAAO,gBAET,GAAIgG,IAAOtL,EAAAA,GAAS2E,GAClB,MAAO,YAET,GAAI2G,IAAOtL,EAAAA,GAAS+E,GAClB,MAAO,eAET,GAAIuG,IAAOtL,EAAAA,GAAS6E,IAClB,MAAO,2BAET,GAAIyG,IAAOtL,EAAAA,GAASyE,IAClB,MAAO,wBAGT,MAAM1D,EAAQ,IAAIc,MAAM,qBAExB,MADAvB,EAAAA,EAAOS,MAAMA,EAAO,CAAEP,IAAK,mBAAoBjF,SAAU+P,IACnDvK,CACR,CC5BO,MAAM8K,EAAY,CAAC7L,EAAAA,GAASkF,MAAOlF,EAAAA,GAASoF,SAAUpF,EAAAA,GAASsF,WAAYtF,EAAAA,GAASwF,eAAeS,KAExG,CAAC/L,EAAOwJ,EAAOoI,KACR,CACLpU,YAAakU,EAAuB1R,GACpC6R,MAAO7R,EACPA,YAIS8R,EAAmB,CAAChM,EAAAA,GAASkF,MAAOlF,EAAAA,GAASsF,YAAYW,KAA8B/L,IAAW,CAC7GxC,YAAakU,EAAuB1R,GACpC6R,MAAO7R,EACPA,YAGWyR,EAAuB,CAAC3L,EAAAA,GAAS+E,GAAI/E,EAAAA,GAAS6E,IAAK7E,EAAAA,GAAS2E,GAAI3E,EAAAA,GAASyE,KAEzEwH,EAAmBN,EAAqB1F,KAA8B/L,IAAW,CAC5FxC,YAAakU,EAAuB1R,GACpC6R,MAAO7R,EACPA,YAGWgS,EAAyC,CACpD,CAAEH,MAAO,QAAS7R,MAAO+F,EAAAA,GAAa7B,OACtC,CAAE2N,MAAO,gBAAiB7R,MAAO+F,EAAAA,GAAakH,eAC9C,CAAE4E,MAAO,QAAS7R,MAAO+F,EAAAA,GAAa2D,OACtC,CAAEmI,MAAO,gBAAiB7R,MAAO+F,EAAAA,GAAa4D,e,gBCtBzC,SAASsI,EAAgC3Q,GAC9C,OAAOA,EAAWpB,QAAQ,MAAO,QAAQA,QAAQ,MAAO,OAAOA,QAAQ,KAAM,MAC/E,CCRO,SAASmE,EAAqBN,GACnC,MACMmO,EADkBnO,EAASwH,QAAQpH,GAA6B,YAAjBA,EAAQzD,OAE1DqL,KAAKoG,GAAM,OAAOF,EAAgCE,EAAEhO,cACpDiO,KAAK,KACLC,OAEGC,EAAkBvO,EAASwH,QAAQpH,GAA6B,YAAjBA,EAAQzD,OAC7D,IAAI6R,EAAsB,GAU1B,OATID,EAAgBnP,OAAS,IAEzBoP,EAD6B,IAA3BD,EAAgBnP,OACI,OAAO8O,EAAgCK,EAAgB,GAAGnO,YAE1D,MAAMmO,EACzBvG,KAAKoG,GAAM,IAAIF,EAAgCE,EAAEhO,cACjDiO,KAAK,WAGL,GAAGF,KAAuBK,IAAsBF,MACzD,C,+bCQO,MAAM1N,EAAa,UACb6N,EAAkB,aAClBC,EAAqB,kBACrBC,EAA0B,qBAC1B9N,EAAa,SACb+N,EAAkB,YAClBC,EAAsB,mBACtBC,EAAwB,qBACxBC,EAA0B,aAC1BjO,EAAe,WACfkO,EAAoB,cACpB/N,EAAe,WACfgO,EAAoB,cACpBlO,EAAa,SACbmO,EAAkB,YAClBC,EAAqB,UACrBC,EAAqB,UACrBC,EAA0B,aAC1BC,EAA2B,uBAE3BC,EAAoB,gBACpBC,EAAyB,mBACzB7O,EAAiB,KACjB8O,EAAsB,QACtBC,EAAoB,sDACpBC,EAAmB,6CACnBC,EAAmB,WAEnBC,EAAkB,aAClBC,EAAuB,gBAIvBC,EAAkB,eAGlB/O,EAAmB,cACnBgP,EAAwB,iBACxBC,EAA2B,IAAIxB,MAAoBS,KAAmBF,KAAqBC,KAAqBe,KAAyBF,KAAwBlB,IAGjKsB,EAA0C,IAAIzB,MAAoBS,KAAmBJ,KAAyBG,KAAqBe,KAAyBF,KAAwBjB,IAEpLsB,EAA8B,IAAI1B,MAAoBI,KAAuBG,KAAqBC,KAAqBe,KAAyBF,KAAwBlB,IACxKwB,EAAgC,IAAI3B,MAAoBO,KAAqBC,KAAqBa,IAClGO,EAAkC,GAAG5B,KAAmBS,KAAmBF,KAAqBC,KAAqBe,KAAyBpB,IAC9I0B,EAAiB,CAAE1T,IAAK6S,GACxBc,EAAqB,SACrBhR,EAAuB,iBACvB3B,EAAe,eACf4S,EAAmB,UACnBC,EAAyB,yBAEzBC,EAAuB,KAIvBC,EAAgC,UACtC,SAASlP,EAAgCxF,EAAQ,IACtD,OAAIA,EAAM2U,WAAWD,GACZ1U,EAAMqI,UAAUqM,EAA8BvR,QAEhDnD,CACT,CACO,SAAS4U,EAA4B5U,EAAQ,IAClD,OAAOA,EAAM2U,WAAWD,EAC1B,CACO,SAAStU,EAA8BJ,EAAQ,IACpD,OAAO0U,EAAgC1U,CACzC,C,WCpGA6U,EAAOC,QAAUC,C,WCAjBF,EAAOC,QAAUE,C,WCAjBH,EAAOC,QAAUG,C,WCAjBJ,EAAOC,QAAUI,C,WCAjBL,EAAOC,QAAUK,C,WCAjBN,EAAOC,QAAUM,C,WCAjBP,EAAOC,QAAUO,C,UCAjBR,EAAOC,QAAUQ,C,WCAjBT,EAAOC,QAAUS,C,WCAjBV,EAAOC,QAAUU,C,WCAjBX,EAAOC,QAAUW,C,wSCGjB,MAAMC,EAAsB,KAC5B,IAAIC,EAAa,EACjB,MAAMC,EACF,WAAAtN,CAAYjG,EAAMI,GACdyF,KAAK7F,KAAOA,EACZ6F,KAAKzF,GAAKA,CACd,EAOJ,MAAMoT,EAIF,WAAAvN,CAAYwN,EAAS,CAAC,GAClB5N,KAAK5K,GAAKqY,IACVzN,KAAK6N,UAAYD,EAAOC,QACxB7N,KAAK8N,YAAcF,EAAOE,aAAe,MACrC,MAAM,IAAIrO,MAAM,uDACnB,EACL,CAUA,GAAAsO,CAAI/R,GACA,GAAIgE,KAAK6N,QACL,MAAM,IAAIG,WAAW,0CAGzB,MAFoB,mBAAThS,IACPA,EAAQiS,EAASjS,MAAMA,IACnBxD,IACJ,IAAI0V,EAASlS,EAAMxD,GACnB,YAAkBoI,IAAXsN,EAAuB,KAAO,CAAClO,KAAMkO,EAAO,CAE3D,EAQJP,EAASQ,SAAW,IAAIR,EAAS,CAAEG,YAAaM,GAAOA,EAAI9U,MAAM,OAMjEqU,EAASU,SAAW,IAAIV,EAAS,CAAEG,YAAaM,GAAOA,EAAI9U,MAAM,OAMjEqU,EAASW,MAAQ,IAAIX,EAAS,CAAEG,YAAaM,GAAOA,EAAI9U,MAAM,OAY9DqU,EAASY,QAAU,IAAIZ,EAAS,CAAEG,YAAahW,IACvC,GAAIA,GAAkB,OAATA,GAA2B,OAATA,GAA2B,QAATA,EAC7C,MAAM,IAAIkW,WAAW,8BAAgClW,GACzD,OAAOA,GAAS,MAAM,IAO9B6V,EAASa,YAAc,IAAIb,EAAS,CAAEE,SAAS,IAO/CF,EAASc,UAAY,IAAId,EAAS,CAAEE,SAAS,IAM7CF,EAASe,QAAU,IAAIf,EAAS,CAAEE,SAAS,IAM3C,MAAMc,EACF,WAAAvO,CAIAwO,EAUAC,EAIArT,GACIwE,KAAK4O,KAAOA,EACZ5O,KAAK6O,QAAUA,EACf7O,KAAKxE,OAASA,CAClB,CAIA,UAAOsT,CAAIF,GACP,OAAOA,GAAQA,EAAKpZ,OAASoZ,EAAKpZ,MAAMmY,EAASe,QAAQtZ,GAC7D,EAEJ,MAAM2Z,EAAU5P,OAAO6P,OAAO,MAI9B,MAAMf,EAIF,WAAA7N,CAOAiH,EAIA7R,EAKAJ,EAIA6Z,EAAQ,GACJjP,KAAKqH,KAAOA,EACZrH,KAAKxK,MAAQA,EACbwK,KAAK5K,GAAKA,EACV4K,KAAKiP,MAAQA,CACjB,CAIA,aAAOC,CAAOC,GACV,IAAI3Z,EAAQ2Z,EAAK3Z,OAAS2Z,EAAK3Z,MAAMyF,OAASkE,OAAO6P,OAAO,MAAQD,EAChEE,GAASE,EAAKC,IAAM,EAAuB,IAAMD,EAAKE,QAAU,EAA2B,IAC1FF,EAAKxQ,MAAQ,EAAyB,IAAmB,MAAbwQ,EAAK9H,KAAe,EAA6B,GAC9F7O,EAAO,IAAIyV,EAASkB,EAAK9H,MAAQ,GAAI7R,EAAO2Z,EAAK/Z,GAAI6Z,GACzD,GAAIE,EAAK3Z,MACL,IAAK,IAAI8Z,KAAOH,EAAK3Z,MAGjB,GAFKwS,MAAMC,QAAQqH,KACfA,EAAMA,EAAI9W,IACV8W,EAAK,CACL,GAAIA,EAAI,GAAGzB,QACP,MAAM,IAAIG,WAAW,8CACzBxY,EAAM8Z,EAAI,GAAGla,IAAMka,EAAI,EAC3B,CAER,OAAO9W,CACX,CAKA,IAAAkP,CAAKA,GAAQ,OAAO1H,KAAKxK,MAAMkS,EAAKtS,GAAK,CAIzC,SAAIma,GAAU,OAAqB,EAAbvP,KAAKiP,OAAgC,CAAG,CAI9D,aAAIO,GAAc,OAAqB,EAAbxP,KAAKiP,OAAoC,CAAG,CAItE,WAAIQ,GAAY,OAAqB,EAAbzP,KAAKiP,OAAkC,CAAG,CAKlE,eAAIS,GAAgB,OAAqB,EAAb1P,KAAKiP,OAAsC,CAAG,CAK1E,EAAAU,CAAGtI,GACC,GAAmB,iBAARA,EAAkB,CACzB,GAAIrH,KAAKqH,MAAQA,EACb,OAAO,EACX,IAAIiH,EAAQtO,KAAK0H,KAAKiG,EAASW,OAC/B,QAAOA,GAAQA,EAAMsB,QAAQvI,IAAS,CAC1C,CACA,OAAOrH,KAAK5K,IAAMiS,CACtB,CASA,YAAOrL,CAAM6H,GACT,IAAIgM,EAAS1Q,OAAO6P,OAAO,MAC3B,IAAK,IAAItH,KAAQ7D,EACb,IAAK,IAAIwD,KAAQK,EAAKpO,MAAM,KACxBuW,EAAOxI,GAAQxD,EAAI6D,GAC3B,OAAQ7H,IACJ,IAAK,IAAIiQ,EAASjQ,EAAK6H,KAAKiG,EAASW,OAAQpG,GAAK,EAAGA,GAAK4H,EAASA,EAAO7U,OAAS,GAAIiN,IAAK,CACxF,IAAI6H,EAAQF,EAAO3H,EAAI,EAAIrI,EAAKwH,KAAOyI,EAAO5H,IAC9C,GAAI6H,EACA,OAAOA,CACf,EAER,EAKJ9B,EAAS+B,KAAO,IAAI/B,EAAS,GAAI9O,OAAO6P,OAAO,MAAO,EAAG,GAUzD,MAAMiB,EAKF,WAAA7P,CAIA8P,GACIlQ,KAAKkQ,MAAQA,EACb,IAAK,IAAIhI,EAAI,EAAGA,EAAIgI,EAAMjV,OAAQiN,IAC9B,GAAIgI,EAAMhI,GAAG9S,IAAM8S,EACf,MAAM,IAAI8F,WAAW,8EACjC,CAMA,MAAAmC,IAAU3a,GACN,IAAI4a,EAAW,GACf,IAAK,IAAI5X,KAAQwH,KAAKkQ,MAAO,CACzB,IAAIG,EAAW,KACf,IAAK,IAAIC,KAAU9a,EAAO,CACtB,IAAIuY,EAAMuC,EAAO9X,GACbuV,IACKsC,IACDA,EAAWlR,OAAOoR,OAAO,CAAC,EAAG/X,EAAKhD,QACtC6a,EAAStC,EAAI,GAAG3Y,IAAM2Y,EAAI,GAElC,CACAqC,EAAStU,KAAKuU,EAAW,IAAIpC,EAASzV,EAAK6O,KAAMgJ,EAAU7X,EAAKpD,GAAIoD,EAAKyW,OAASzW,EACtF,CACA,OAAO,IAAIyX,EAAQG,EACvB,EAEJ,MAAMI,EAAa,IAAIC,QAAWC,EAAkB,IAAID,QAKxD,IAAIE,GACJ,SAAWA,GAMPA,EAASA,EAAyB,eAAI,GAAK,iBAM3CA,EAASA,EAA2B,iBAAI,GAAK,mBAM7CA,EAASA,EAAuB,aAAI,GAAK,eAOzCA,EAASA,EAAyB,eAAI,GAAK,gBAC9C,CA1BD,CA0BGA,IAAaA,EAAW,CAAC,IAiB5B,MAAMC,EAIF,WAAAxQ,CAIA5H,EAIAqY,EAKA9P,EAIA9F,EAIAzF,GASI,GARAwK,KAAKxH,KAAOA,EACZwH,KAAK6Q,SAAWA,EAChB7Q,KAAKe,UAAYA,EACjBf,KAAK/E,OAASA,EAId+E,KAAKxK,MAAQ,KACTA,GAASA,EAAMyF,OAAQ,CACvB+E,KAAKxK,MAAQ2J,OAAO6P,OAAO,MAC3B,IAAK,IAAKtH,EAAM5P,KAAUtC,EACtBwK,KAAKxK,MAAqB,iBAARkS,EAAmBA,EAAOA,EAAKtS,IAAM0C,CAC/D,CACJ,CAIA,QAAAuC,GACI,IAAIqU,EAAUC,EAAYG,IAAI9O,MAC9B,GAAI0O,IAAYA,EAAQG,QACpB,OAAOH,EAAQE,KAAKvU,WACxB,IAAIwW,EAAW,GACf,IAAK,IAAIC,KAAM9Q,KAAK6Q,SAAU,CAC1B,IAAIzC,EAAM0C,EAAGzW,WACT+T,IACIyC,IACAA,GAAY,KAChBA,GAAYzC,EAEpB,CACA,OAAQpO,KAAKxH,KAAK6O,MACb,KAAK0J,KAAK/Q,KAAKxH,KAAK6O,QAAUrH,KAAKxH,KAAKiX,QAAU/T,KAAKC,UAAUqE,KAAKxH,KAAK6O,MAAQrH,KAAKxH,KAAK6O,OACzFwJ,EAAS5V,OAAS,IAAM4V,EAAW,IAAM,IAFzBA,CAG7B,CAMA,MAAAG,CAAOC,EAAO,GACV,OAAO,IAAIC,EAAWlR,KAAKmR,QAASF,EACxC,CAMA,QAAAG,CAASpQ,EAAKqQ,EAAO,EAAGJ,EAAO,GAC3B,IAAIK,EAAQd,EAAW1B,IAAI9O,OAASA,KAAKmR,QACrCH,EAAS,IAAIE,EAAWI,GAG5B,OAFAN,EAAOO,OAAOvQ,EAAKqQ,GACnBb,EAAWrT,IAAI6C,KAAMgR,EAAOQ,OACrBR,CACX,CAKA,WAAIG,GACA,OAAO,IAAIM,EAASzR,KAAM,EAAG,EAAG,KACpC,CAYA,OAAA0R,CAAQ1Q,EAAKqQ,EAAO,GAChB,IAAIxR,EAAO8R,EAAYnB,EAAW1B,IAAI9O,OAASA,KAAKmR,QAASnQ,EAAKqQ,GAAM,GAExE,OADAb,EAAWrT,IAAI6C,KAAMH,GACdA,CACX,CAQA,YAAA+R,CAAa5Q,EAAKqQ,EAAO,GACrB,IAAIxR,EAAO8R,EAAYjB,EAAgB5B,IAAI9O,OAASA,KAAKmR,QAASnQ,EAAKqQ,GAAM,GAE7E,OADAX,EAAgBvT,IAAI6C,KAAMH,GACnBA,CACX,CAQA,YAAAgS,CAAa7Q,EAAKqQ,EAAO,GACrB,OAwcR,SAAuBzC,EAAM5N,EAAKqQ,GAC9B,IAAIS,EAAQlD,EAAKgD,aAAa5Q,EAAKqQ,GAAOU,EAAS,KACnD,IAAK,IAAIC,EAAOF,aAAiBL,EAAWK,EAAQA,EAAM3Z,QAAQ8Z,OAAQD,EAAMA,EAAOA,EAAKC,OACxF,GAAID,EAAK1Q,MAAQ,EAAG,CAChB,IAAI2Q,EAASD,EAAKC,QACjBF,IAAWA,EAAS,CAACD,KAAShW,KAAKmW,EAAOP,QAAQ1Q,EAAKqQ,IACxDW,EAAOC,CACX,KACK,CACD,IAAIC,EAAQvD,EAAYG,IAAIkD,EAAKpD,MAEjC,GAAIsD,GAASA,EAAMrD,SAAWqD,EAAMrD,QAAQ,GAAG1U,MAAQ6G,GAAOkR,EAAMrD,QAAQqD,EAAMrD,QAAQ5T,OAAS,GAAGV,IAAMyG,EAAK,CAC7G,IAAImR,EAAO,IAAIV,EAASS,EAAMtD,KAAMsD,EAAMrD,QAAQ,GAAG1U,KAAO6X,EAAK7X,MAAO,EAAG6X,IAC1ED,IAAWA,EAAS,CAACD,KAAShW,KAAK6V,EAAYQ,EAAMnR,EAAKqQ,GAAM,GACrE,CACJ,CAEJ,OAAOU,EAASK,EAAUL,GAAUD,CACxC,CA1deO,CAAcrS,KAAMgB,EAAKqQ,EACpC,CAQA,OAAA3Q,CAAQyO,GACJ,IAAI,MAAExO,EAAK,MAAE2R,EAAK,KAAEnY,EAAO,EAAC,GAAEI,EAAKyF,KAAK/E,QAAWkU,EAC/C8B,EAAO9B,EAAK8B,MAAQ,EAAGsB,GAAQtB,EAAON,EAAS6B,kBAAoB,EACvE,IAAK,IAAIC,EAAIzS,KAAKgR,OAAOC,EAAON,EAAS6B,oBAAqB,CAC1D,IAAIE,GAAU,EACd,GAAID,EAAEtY,MAAQI,GAAMkY,EAAElY,IAAMJ,KAAUoY,GAAQE,EAAEja,KAAKkX,cAA4B,IAAb/O,EAAM8R,IAAe,CACrF,GAAIA,EAAElN,aACF,SACJmN,GAAU,CACd,CACA,KACQA,GAAWJ,IAAUC,IAASE,EAAEja,KAAKkX,cACrC4C,EAAMG,IACNA,EAAEE,eAHD,CAKL,IAAKF,EAAER,SACH,OACJS,GAAU,CACd,CACJ,CACJ,CAKA,IAAAhL,CAAKA,GACD,OAAQA,EAAKmG,QAAiC7N,KAAKxK,MAAQwK,KAAKxK,MAAMkS,EAAKtS,SAAMwL,EAA1DZ,KAAKxH,KAAKkP,KAAKA,EAC1C,CAMA,cAAIkL,GACA,IAAI1E,EAAS,GACb,GAAIlO,KAAKxK,MACL,IAAK,IAAIJ,KAAM4K,KAAKxK,MAChB0Y,EAAOpS,KAAK,EAAE1G,EAAI4K,KAAKxK,MAAMJ,KACrC,OAAO8Y,CACX,CAMA,OAAA2E,CAAQjF,EAAS,CAAC,GACd,OAAO5N,KAAK6Q,SAAS5V,QAAU,EAA+B+E,KAC1D8S,EAAa7E,EAAS+B,KAAMhQ,KAAK6Q,SAAU7Q,KAAKe,UAAW,EAAGf,KAAK6Q,SAAS5V,OAAQ,EAAG+E,KAAK/E,QAAQ,CAAC4V,EAAU9P,EAAW9F,IAAW,IAAI2V,EAAK5Q,KAAKxH,KAAMqY,EAAU9P,EAAW9F,EAAQ+E,KAAK4S,aAAahF,EAAOmF,UAAY,EAAElC,EAAU9P,EAAW9F,IAAW,IAAI2V,EAAK3C,EAAS+B,KAAMa,EAAU9P,EAAW9F,IAClT,CAKA,YAAO+X,CAAMzT,GAAQ,OA4tBzB,SAAmBA,GACf,IAAI0T,EACJ,IAAI,OAAEC,EAAM,QAAEC,EAAO,gBAAEC,EAAkB5F,EAAmB,OAAE6F,EAAS,GAAE,cAAEC,EAAgBH,EAAQjD,MAAMjV,QAAWsE,EAChHyR,EAAShJ,MAAMC,QAAQiL,GAAU,IAAIK,EAAiBL,EAAQA,EAAOjY,QAAUiY,EAC/EhD,EAAQiD,EAAQjD,MAChB1B,EAAc,EAAGC,EAAY,EACjC,SAAS+E,EAASC,EAAaC,EAAQ7C,EAAU9P,EAAW4S,EAAUC,GAClE,IAAI,GAAExe,EAAE,MAAEye,EAAK,IAAEC,EAAG,KAAEC,GAAS/C,EAC3BgD,EAAmBvF,EAAWwF,EAAiBzF,EACnD,KAAOuF,EAAO,GAAG,CAEb,GADA/C,EAAOkD,QACM,GAATH,EAAsC,CACtC,IAAIlU,EAAOwT,EAAOje,GAGlB,OAFAyb,EAAS/U,KAAK+D,QACdkB,EAAUjF,KAAK+X,EAAQJ,EAE3B,CACK,IAAa,GAATM,EAEL,YADAvF,EAAcpZ,GAGb,IAAa,GAAT2e,EAEL,YADAtF,EAAYrZ,GAIZ,MAAM,IAAI4Y,WAAW,6BAA6B+F,IAE1D,CACA,IAAsBlU,EAAMqT,EAAxB1a,EAAO0X,EAAM9a,GACb+e,EAAWN,EAAQJ,EACvB,GAAIK,EAAMD,GAAST,IAAoBF,EA8G3C,SAAwBkB,EAAST,GAO7B,IAAIU,EAAOrD,EAAOqD,OACdN,EAAO,EAAGF,EAAQ,EAAGS,EAAO,EAAGC,EAAWF,EAAKP,IAAMV,EACrDlF,EAAS,CAAE6F,KAAM,EAAGF,MAAO,EAAGS,KAAM,GACxCtC,EAAM,IAAK,IAAI0B,EAASW,EAAKrT,IAAMoT,EAASC,EAAKrT,IAAM0S,GAAS,CAC5D,IAAIc,EAAWH,EAAKN,KAEpB,GAAIM,EAAKjf,IAAMue,GAAYa,GAAY,EAAG,CAGtCtG,EAAO6F,KAAOA,EACd7F,EAAO2F,MAAQA,EACf3F,EAAOoG,KAAOA,EACdA,GAAQ,EACRP,GAAQ,EACRM,EAAKH,OACL,QACJ,CACA,IAAIC,EAAWE,EAAKrT,IAAMwT,EAC1B,GAAIA,EAAW,GAAKL,EAAWT,GAAUW,EAAKR,MAAQU,EAClD,MACJ,IAAIE,EAAeJ,EAAKjf,IAAMke,EAAgB,EAAI,EAC9CoB,EAAYL,EAAKR,MAErB,IADAQ,EAAKH,OACEG,EAAKrT,IAAMmT,GAAU,CACxB,GAAIE,EAAKN,KAAO,EAAG,CACf,IAAkB,GAAdM,EAAKN,KAGL,MAAM/B,EAFNyC,GAAgB,CAGxB,MACSJ,EAAKjf,IAAMke,IAChBmB,GAAgB,GAEpBJ,EAAKH,MACT,CACAL,EAAQa,EACRX,GAAQS,EACRF,GAAQG,CACZ,CAMA,OALId,EAAW,GAAKI,GAAQK,KACxBlG,EAAO6F,KAAOA,EACd7F,EAAO2F,MAAQA,EACf3F,EAAOoG,KAAOA,GAEXpG,EAAO6F,KAAO,EAAI7F,OAAStN,CACtC,CAlKoD+T,CAAe3D,EAAOhQ,IAAM0S,EAAQC,IAAY,CAE5F,IAAIpU,EAAO,IAAIqV,YAAY1B,EAAOa,KAAOb,EAAOoB,MAC5CO,EAAS7D,EAAOhQ,IAAMkS,EAAOa,KAAMzS,EAAQ/B,EAAKtE,OACpD,KAAO+V,EAAOhQ,IAAM6T,GAChBvT,EAAQwT,EAAa5B,EAAOW,MAAOtU,EAAM+B,GAC7CzB,EAAO,IAAIkV,EAAWxV,EAAMuU,EAAMZ,EAAOW,MAAOV,GAChDgB,EAAWjB,EAAOW,MAAQJ,CAC9B,KACK,CACD,IAAIoB,EAAS7D,EAAOhQ,IAAM+S,EAC1B/C,EAAOkD,OACP,IAAIc,EAAgB,GAAIC,EAAiB,GACrCC,EAAgB9f,GAAMke,EAAgBle,GAAM,EAC5C+f,EAAY,EAAGC,EAAUtB,EAC7B,KAAO9C,EAAOhQ,IAAM6T,GACZK,GAAiB,GAAKlE,EAAO5b,IAAM8f,GAAiBlE,EAAO+C,MAAQ,GAC/D/C,EAAO8C,KAAOsB,EAAUhC,IACxBiC,EAAeL,EAAeC,EAAgBpB,EAAOsB,EAAWnE,EAAO8C,IAAKsB,EAASF,EAAelB,EAAkBC,GACtHkB,EAAYH,EAAc/Z,OAC1Bma,EAAUpE,EAAO8C,KAErB9C,EAAOkD,QAEFN,EAAQ,KACb0B,EAAazB,EAAOgB,EAAQG,EAAeC,GAG3CzB,EAASK,EAAOgB,EAAQG,EAAeC,EAAgBC,EAAetB,EAAQ,GAOtF,GAJIsB,GAAiB,GAAKC,EAAY,GAAKA,EAAYH,EAAc/Z,QACjEoa,EAAeL,EAAeC,EAAgBpB,EAAOsB,EAAWtB,EAAOuB,EAASF,EAAelB,EAAkBC,GACrHe,EAAcO,UACdN,EAAeM,UACXL,GAAiB,GAAKC,EAAY,EAAG,CACrC,IAAIK,EA0ChB,SAAsBhd,EAAMgW,GACxB,MAAO,CAACqC,EAAU9P,EAAW9F,KACzB,IAAgDwa,EAAMC,EAAlDjH,EAAY,EAAGkH,EAAQ9E,EAAS5V,OAAS,EAC7C,GAAI0a,GAAS,IAAMF,EAAO5E,EAAS8E,cAAmB/E,EAAM,CACxD,IAAK+E,GAASF,EAAKjd,MAAQA,GAAQid,EAAKxa,QAAUA,EAC9C,OAAOwa,GACPC,EAAgBD,EAAK/N,KAAKiG,EAASc,cACnCA,EAAY1N,EAAU4U,GAASF,EAAKxa,OAASya,EACrD,CACA,OAAO3C,EAASva,EAAMqY,EAAU9P,EAAW9F,EAAQwT,EAAWD,EAAY,CAElF,CArDuBoH,CAAapd,EAAMyb,GAC9BpU,EAAOiT,EAAata,EAAMwc,EAAeC,EAAgB,EAAGD,EAAc/Z,OAAQ,EAAG6Y,EAAMD,EAAO2B,EAAMA,EAC5G,MAEI3V,EAAOkT,EAASva,EAAMwc,EAAeC,EAAgBnB,EAAMD,EAAOG,EAAmBF,EAAKG,EAElG,CACApD,EAAS/U,KAAK+D,GACdkB,EAAUjF,KAAKqY,EACnB,CACA,SAASmB,EAAa7B,EAAaC,EAAQ7C,EAAU9P,GACjD,IAAIP,EAAQ,GACRqV,EAAY,EAAGC,GAAU,EAC7B,KAAO9E,EAAOhQ,IAAM0S,GAAQ,CACxB,IAAI,GAAEte,EAAE,MAAEye,EAAK,IAAEC,EAAG,KAAEC,GAAS/C,EAC/B,GAAI+C,EAAO,EACP/C,EAAOkD,WAEN,IAAI4B,GAAU,GAAKjC,EAAQiC,EAC5B,MAGIA,EAAS,IACTA,EAAShC,EAAMV,GACnB5S,EAAM1E,KAAK1G,EAAIye,EAAOC,GACtB+B,IACA7E,EAAOkD,MACX,CACJ,CACA,GAAI2B,EAAW,CACX,IAAI3C,EAAS,IAAI0B,YAAwB,EAAZiB,GACzBhC,EAAQrT,EAAMA,EAAMvF,OAAS,GACjC,IAAK,IAAIiN,EAAI1H,EAAMvF,OAAS,EAAG8a,EAAI,EAAG7N,GAAK,EAAGA,GAAK,EAC/CgL,EAAO6C,KAAOvV,EAAM0H,GACpBgL,EAAO6C,KAAOvV,EAAM0H,EAAI,GAAK2L,EAC7BX,EAAO6C,KAAOvV,EAAM0H,EAAI,GAAK2L,EAC7BX,EAAO6C,KAAOA,EAElBlF,EAAS/U,KAAK,IAAIiZ,EAAW7B,EAAQ1S,EAAM,GAAKqT,EAAOV,IACvDpS,EAAUjF,KAAK+X,EAAQJ,EAC3B,CACJ,CAaA,SAAS4B,EAAexE,EAAU9P,EAAWiV,EAAM9N,EAAG/N,EAAMI,EAAI/B,EAAMiW,EAAWD,GAC7E,IAAIwG,EAAgB,GAAIC,EAAiB,GACzC,KAAOpE,EAAS5V,OAASiN,GACrB8M,EAAclZ,KAAK+U,EAASoF,OAC5BhB,EAAenZ,KAAKiF,EAAUkV,MAAQD,EAAO7b,GAEjD0W,EAAS/U,KAAKiX,EAASI,EAAQjD,MAAM1X,GAAOwc,EAAeC,EAAgB1a,EAAKJ,EAAMsU,EAAYlU,EAAIiU,IACtGzN,EAAUjF,KAAK3B,EAAO6b,EAC1B,CACA,SAASjD,EAASva,EAAMqY,EAAU9P,EAAW9F,EAAQwT,EAAWD,EAAahZ,GACzE,GAAIgZ,EAAa,CACb,IAAI0H,EAAO,CAACvI,EAASa,YAAaA,GAClChZ,EAAQA,EAAQ,CAAC0gB,GAAMC,OAAO3gB,GAAS,CAAC0gB,EAC5C,CACA,GAAIzH,EAAY,GAAI,CAChB,IAAIyH,EAAO,CAACvI,EAASc,UAAWA,GAChCjZ,EAAQA,EAAQ,CAAC0gB,GAAMC,OAAO3gB,GAAS,CAAC0gB,EAC5C,CACA,OAAO,IAAItF,EAAKpY,EAAMqY,EAAU9P,EAAW9F,EAAQzF,EACvD,CAsDA,SAASsf,EAAasB,EAAalD,EAAQ5R,GACvC,IAAI,GAAElM,EAAE,MAAEye,EAAK,IAAEC,EAAG,KAAEC,GAAS/C,EAE/B,GADAA,EAAOkD,OACHH,GAAQ,GAAK3e,EAAKke,EAAe,CACjC,IAAI+C,EAAa/U,EACjB,GAAIyS,EAAO,EAAG,CACV,IAAIc,EAAS7D,EAAOhQ,KAAO+S,EAAO,GAClC,KAAO/C,EAAOhQ,IAAM6T,GAChBvT,EAAQwT,EAAasB,EAAalD,EAAQ5R,EAClD,CACA4R,IAAS5R,GAAS+U,EAClBnD,IAAS5R,GAASwS,EAAMsC,EACxBlD,IAAS5R,GAASuS,EAAQuC,EAC1BlD,IAAS5R,GAASlM,CACtB,MACkB,GAAT2e,EACLvF,EAAcpZ,GAEA,GAAT2e,IACLtF,EAAYrZ,GAEhB,OAAOkM,CACX,CACA,IAAIuP,EAAW,GAAI9P,EAAY,GAC/B,KAAOiQ,EAAOhQ,IAAM,GAChBwS,EAASjU,EAAKsU,OAAS,EAAGtU,EAAK6W,aAAe,EAAGvF,EAAU9P,GAAY,EAAG,GAC9E,IAAI9F,EAAgC,QAAtBgY,EAAK1T,EAAKtE,cAA2B,IAAPgY,EAAgBA,EAAMpC,EAAS5V,OAAS8F,EAAU,GAAK8P,EAAS,GAAG5V,OAAS,EACxH,OAAO,IAAI2V,EAAKV,EAAM3Q,EAAK+W,OAAQzF,EAAS0E,UAAWxU,EAAUwU,UAAWta,EAChF,CA17BgCsb,CAAUhX,EAAO,EAKjDqR,EAAK4F,MAAQ,IAAI5F,EAAK3C,EAAS+B,KAAM,GAAI,GAAI,GAC7C,MAAMuD,EACF,WAAAnT,CAAY8S,EAAQ5R,GAChBtB,KAAKkT,OAASA,EACdlT,KAAKsB,MAAQA,CACjB,CACA,MAAIlM,GAAO,OAAO4K,KAAKkT,OAAOlT,KAAKsB,MAAQ,EAAI,CAC/C,SAAIuS,GAAU,OAAO7T,KAAKkT,OAAOlT,KAAKsB,MAAQ,EAAI,CAClD,OAAIwS,GAAQ,OAAO9T,KAAKkT,OAAOlT,KAAKsB,MAAQ,EAAI,CAChD,QAAIyS,GAAS,OAAO/T,KAAKkT,OAAOlT,KAAKsB,MAAQ,EAAI,CACjD,OAAIN,GAAQ,OAAOhB,KAAKsB,KAAO,CAC/B,IAAA4S,GAASlU,KAAKsB,OAAS,CAAG,CAC1B,IAAA+S,GAAS,OAAO,IAAId,EAAiBvT,KAAKkT,OAAQlT,KAAKsB,MAAQ,EAQnE,MAAMyT,EAIF,WAAA3U,CAIA8S,EAIAjY,EAIAkC,GACI6C,KAAKkT,OAASA,EACdlT,KAAK/E,OAASA,EACd+E,KAAK7C,IAAMA,CACf,CAIA,QAAI3E,GAAS,OAAOyV,EAAS+B,IAAM,CAInC,QAAA3V,GACI,IAAI6T,EAAS,GACb,IAAK,IAAI5M,EAAQ,EAAGA,EAAQtB,KAAKkT,OAAOjY,QACpCiT,EAAOpS,KAAKkE,KAAKyW,YAAYnV,IAC7BA,EAAQtB,KAAKkT,OAAO5R,EAAQ,GAEhC,OAAO4M,EAAOhE,KAAK,IACvB,CAIA,WAAAuM,CAAYnV,GACR,IAAIlM,EAAK4K,KAAKkT,OAAO5R,GAAQoV,EAAW1W,KAAKkT,OAAO5R,EAAQ,GACxD9I,EAAOwH,KAAK7C,IAAI+S,MAAM9a,GAAK8Y,EAAS1V,EAAK6O,KAI7C,GAHI,KAAK0J,KAAK7C,KAAY1V,EAAKiX,UAC3BvB,EAASxS,KAAKC,UAAUuS,IAExBwI,IADJpV,GAAS,GAEL,OAAO4M,EACX,IAAI2C,EAAW,GACf,KAAOvP,EAAQoV,GACX7F,EAAS/U,KAAKkE,KAAKyW,YAAYnV,IAC/BA,EAAQtB,KAAKkT,OAAO5R,EAAQ,GAEhC,OAAO4M,EAAS,IAAM2C,EAAS3G,KAAK,KAAO,GAC/C,CAIA,SAAAyM,CAAUN,EAAYK,EAAUE,EAAK5V,EAAKqQ,GACtC,IAAI,OAAE6B,GAAWlT,KAAM6W,GAAQ,EAC/B,IAAK,IAAI3O,EAAImO,EAAYnO,GAAKwO,KACtBI,EAAUzF,EAAMrQ,EAAKkS,EAAOhL,EAAI,GAAIgL,EAAOhL,EAAI,MAC/C2O,EAAO3O,EACH0O,EAAM,IAHsB1O,EAAIgL,EAAOhL,EAAI,IAOvD,OAAO2O,CACX,CAIA,KAAAE,CAAMC,EAAQC,EAAM9c,GAChB,IAAI+c,EAAIlX,KAAKkT,OACTiE,EAAO,IAAIvC,YAAYqC,EAAOD,GAASI,EAAM,EACjD,IAAK,IAAIlP,EAAI8O,EAAQjB,EAAI,EAAG7N,EAAI+O,GAAO,CACnCE,EAAKpB,KAAOmB,EAAEhP,KACdiP,EAAKpB,KAAOmB,EAAEhP,KAAO/N,EACrB,IAAII,EAAK4c,EAAKpB,KAAOmB,EAAEhP,KAAO/N,EAC9Bgd,EAAKpB,KAAOmB,EAAEhP,KAAO8O,EACrBI,EAAMC,KAAKC,IAAIF,EAAK7c,EACxB,CACA,OAAO,IAAIwa,EAAWoC,EAAMC,EAAKpX,KAAK7C,IAC1C,EAEJ,SAAS2Z,EAAUzF,EAAMrQ,EAAK7G,EAAMI,GAChC,OAAQ8W,GACJ,KAAM,EAAqB,OAAOlX,EAAO6G,EACzC,KAAM,EAAyB,OAAOzG,GAAMyG,GAAO7G,EAAO6G,EAC1D,KAAK,EAAqB,OAAO7G,EAAO6G,GAAOzG,EAAKyG,EACpD,KAAK,EAAwB,OAAO7G,GAAQ6G,GAAOzG,EAAKyG,EACxD,KAAK,EAAoB,OAAOzG,EAAKyG,EACrC,KAAK,EAAuB,OAAO,EAE3C,CACA,SAAS2Q,EAAY9R,EAAMmB,EAAKqQ,EAAMkG,GAGlC,IAFA,IAAItE,EAEGpT,EAAK1F,MAAQ0F,EAAKtF,KACpB8W,EAAO,EAAIxR,EAAK1F,MAAQ6G,EAAMnB,EAAK1F,KAAO6G,KAC1CqQ,GAAQ,EAAIxR,EAAKtF,IAAMyG,EAAMnB,EAAKtF,GAAKyG,IAAM,CAC9C,IAAIiR,GAAUsF,GAAY1X,aAAgB4R,GAAY5R,EAAKyB,MAAQ,EAAI,KAAOzB,EAAKoS,OACnF,IAAKA,EACD,OAAOpS,EACXA,EAAOoS,CACX,CACA,IAAIhB,EAAOsG,EAAW,EAAI5G,EAAS6G,eAEnC,GAAID,EACA,IAAK,IAAIvF,EAAOnS,EAAMoS,EAASD,EAAKC,OAAQA,EAAQD,EAAOC,EAAQA,EAASD,EAAKC,OACzED,aAAgBP,GAAYO,EAAK1Q,MAAQ,IAA+C,QAAxC2R,EAAKhB,EAAOtR,MAAMK,EAAKqQ,EAAMJ,UAA0B,IAAPgC,OAAgB,EAASA,EAAG9Y,OAAS6X,EAAK7X,OAC1I0F,EAAOoS,GAEnB,OAAS,CACL,IAAIH,EAAQjS,EAAKc,MAAMK,EAAKqQ,EAAMJ,GAClC,IAAKa,EACD,OAAOjS,EACXA,EAAOiS,CACX,CACJ,CACA,MAAM2F,EACF,MAAAzG,CAAOC,EAAO,GAAK,OAAO,IAAIC,EAAWlR,KAAMiR,EAAO,CACtD,QAAAjK,CAASxO,EAAMkf,EAAS,KAAMC,EAAQ,MAClC,IAAIC,EAAIC,EAAY7X,KAAMxH,EAAMkf,EAAQC,GACxC,OAAOC,EAAE3c,OAAS2c,EAAE,GAAK,IAC7B,CACA,WAAAC,CAAYrf,EAAMkf,EAAS,KAAMC,EAAQ,MACrC,OAAOE,EAAY7X,KAAMxH,EAAMkf,EAAQC,EAC3C,CACA,OAAAjG,CAAQ1Q,EAAKqQ,EAAO,GAChB,OAAOM,EAAY3R,KAAMgB,EAAKqQ,GAAM,EACxC,CACA,YAAAO,CAAa5Q,EAAKqQ,EAAO,GACrB,OAAOM,EAAY3R,KAAMgB,EAAKqQ,GAAM,EACxC,CACA,YAAAyG,CAAa3f,GACT,OAAO4f,EAAiB/X,KAAKiS,OAAQ9Z,EACzC,CACA,0BAAA6f,CAA2BhX,GACvB,IAAIgR,EAAOhS,KAAKiY,YAAYjX,GAAMnB,EAAOG,KACzC,KAAOgS,GAAM,CACT,IAAIyD,EAAOzD,EAAKkG,UAChB,IAAKzC,GAAQA,EAAKlb,IAAMyX,EAAKzX,GACzB,MACAkb,EAAKjd,KAAKiX,SAAWgG,EAAKtb,MAAQsb,EAAKlb,IACvCsF,EAAOmS,EACPA,EAAOyD,EAAK0C,aAGZnG,EAAOyD,CAEf,CACA,OAAO5V,CACX,CACA,QAAIA,GAAS,OAAOG,IAAM,CAC1B,QAAIkU,GAAS,OAAOlU,KAAKiS,MAAQ,EAErC,MAAMR,UAAiBgG,EACnB,WAAArX,CAAYoR,EAAOrX,EAEnBmH,EAAO8W,GACHC,QACArY,KAAKwR,MAAQA,EACbxR,KAAK7F,KAAOA,EACZ6F,KAAKsB,MAAQA,EACbtB,KAAKoY,QAAUA,CACnB,CACA,QAAI5f,GAAS,OAAOwH,KAAKwR,MAAMhZ,IAAM,CACrC,QAAI6O,GAAS,OAAOrH,KAAKwR,MAAMhZ,KAAK6O,IAAM,CAC1C,MAAI9M,GAAO,OAAOyF,KAAK7F,KAAO6F,KAAKwR,MAAMvW,MAAQ,CACjD,SAAAqd,CAAUpQ,EAAG0O,EAAK5V,EAAKqQ,EAAMJ,EAAO,GAChC,IAAK,IAAIgB,EAASjS,OAAQ,CACtB,IAAK,IAAI,SAAE6Q,EAAQ,UAAE9P,GAAckR,EAAOT,MAAOzS,EAAI6X,EAAM,EAAI/F,EAAS5V,QAAU,EAAGiN,GAAKnJ,EAAGmJ,GAAK0O,EAAK,CACnG,IAAI1C,EAAOrD,EAAS3I,GAAI2L,EAAQ9S,EAAUmH,GAAK+J,EAAO9X,KACtD,GAAK2c,EAAUzF,EAAMrQ,EAAK6S,EAAOA,EAAQK,EAAKjZ,QAE9C,GAAIiZ,aAAgBa,EAAY,CAC5B,GAAI9D,EAAON,EAAS4H,eAChB,SACJ,IAAIjX,EAAQ4S,EAAKyC,UAAU,EAAGzC,EAAKhB,OAAOjY,OAAQ2b,EAAK5V,EAAM6S,EAAOxC,GACpE,GAAI/P,GAAS,EACT,OAAO,IAAIkX,EAAW,IAAIC,EAAcxG,EAAQiC,EAAMhM,EAAG2L,GAAQ,KAAMvS,EAC/E,MACK,GAAK2P,EAAON,EAAS6B,mBAAuB0B,EAAK1b,KAAKkX,aAAegJ,EAASxE,GAAQ,CACvF,IAAIxF,EACJ,KAAMuC,EAAON,EAASgI,gBAAkBjK,EAAUC,EAAYG,IAAIoF,MAAWxF,EAAQG,QACjF,OAAO,IAAI4C,EAAS/C,EAAQE,KAAMiF,EAAO3L,EAAG+J,GAChD,IAAIH,EAAQ,IAAIL,EAASyC,EAAML,EAAO3L,EAAG+J,GACzC,OAAQhB,EAAON,EAAS6B,mBAAsBV,EAAMtZ,KAAKkX,YAAcoC,EACjEA,EAAMwG,UAAU1B,EAAM,EAAI1C,EAAKrD,SAAS5V,OAAS,EAAI,EAAG2b,EAAK5V,EAAKqQ,EAC5E,CACJ,CACA,GAAKJ,EAAON,EAAS6B,mBAAsBP,EAAOzZ,KAAKkX,YACnD,OAAO,KAMX,GAJIxH,EADA+J,EAAO3Q,OAAS,EACZ2Q,EAAO3Q,MAAQsV,EAEfA,EAAM,GAAK,EAAI3E,EAAOmG,QAAQ5G,MAAMX,SAAS5V,OACrDgX,EAASA,EAAOmG,SACXnG,EACD,OAAO,IACf,CACJ,CACA,cAAI1M,GAAe,OAAOvF,KAAKsY,UAAU,EAAG,EAAG,EAAG,EAAwB,CAC1E,aAAIJ,GAAc,OAAOlY,KAAKsY,UAAUtY,KAAKwR,MAAMX,SAAS5V,OAAS,GAAI,EAAG,EAAG,EAAwB,CACvG,UAAAiG,CAAWF,GAAO,OAAOhB,KAAKsY,UAAU,EAAG,EAAGtX,EAAK,EAAqB,CACxE,WAAAiX,CAAYjX,GAAO,OAAOhB,KAAKsY,UAAUtY,KAAKwR,MAAMX,SAAS5V,OAAS,GAAI,EAAG+F,GAAM,EAAsB,CACzG,KAAAL,CAAMK,EAAKqQ,EAAMJ,EAAO,GACpB,IAAIvC,EACJ,KAAMuC,EAAON,EAAS6G,kBAAoB9I,EAAUC,EAAYG,IAAI9O,KAAKwR,SAAW9C,EAAQG,QAAS,CACjG,IAAI+J,EAAO5X,EAAMhB,KAAK7F,KACtB,IAAK,IAAI,KAAEA,EAAI,GAAEI,KAAQmU,EAAQG,QAC7B,IAAKwC,EAAO,EAAIlX,GAAQye,EAAOze,EAAOye,KACjCvH,EAAO,EAAI9W,GAAMqe,EAAOre,EAAKqe,GAC9B,OAAO,IAAInH,EAAS/C,EAAQE,KAAMF,EAAQG,QAAQ,GAAG1U,KAAO6F,KAAK7F,MAAO,EAAG6F,KAEvF,CACA,OAAOA,KAAKsY,UAAU,EAAG,EAAGtX,EAAKqQ,EAAMJ,EAC3C,CACA,qBAAA4H,GACI,IAAIC,EAAM9Y,KACV,KAAO8Y,EAAItgB,KAAKkX,aAAeoJ,EAAIV,SAC/BU,EAAMA,EAAIV,QACd,OAAOU,CACX,CACA,UAAI7G,GACA,OAAOjS,KAAKoY,QAAUpY,KAAKoY,QAAQS,wBAA0B,IACjE,CACA,eAAIlG,GACA,OAAO3S,KAAKoY,SAAWpY,KAAKsB,OAAS,EAAItB,KAAKoY,QAAQE,UAAUtY,KAAKsB,MAAQ,EAAG,EAAG,EAAG,GAAyB,IACnH,CACA,eAAI6W,GACA,OAAOnY,KAAKoY,SAAWpY,KAAKsB,OAAS,EAAItB,KAAKoY,QAAQE,UAAUtY,KAAKsB,MAAQ,GAAI,EAAG,EAAG,GAAyB,IACpH,CACA,QAAIsN,GAAS,OAAO5O,KAAKwR,KAAO,CAChC,MAAAuH,GAAW,OAAO/Y,KAAKwR,KAAO,CAI9B,QAAAnX,GAAa,OAAO2F,KAAKwR,MAAMnX,UAAY,EAE/C,SAASwd,EAAYhY,EAAMrH,EAAMkf,EAAQC,GACrC,IAAIqB,EAAMnZ,EAAKmR,SAAU9C,EAAS,GAClC,IAAK8K,EAAIzT,aACL,OAAO2I,EACX,GAAc,MAAVwJ,EACA,IAAK,IAAI3H,GAAQ,GAAQA,GAErB,GADAA,EAAQiJ,EAAIxgB,KAAKmX,GAAG+H,IACfsB,EAAIrG,cACL,OAAOzE,EAEnB,OAAS,CACL,GAAa,MAATyJ,GAAiBqB,EAAIxgB,KAAKmX,GAAGgI,GAC7B,OAAOzJ,EAGX,GAFI8K,EAAIxgB,KAAKmX,GAAGnX,IACZ0V,EAAOpS,KAAKkd,EAAInZ,OACfmZ,EAAIrG,cACL,OAAgB,MAATgF,EAAgBzJ,EAAS,EACxC,CACJ,CACA,SAAS6J,EAAiBlY,EAAM1H,EAAS+P,EAAI/P,EAAQ8C,OAAS,GAC1D,IAAK,IAAIgP,EAAIpK,EAAMqI,GAAK,EAAG+B,EAAIA,EAAEgI,OAAQ,CACrC,IAAKhI,EACD,OAAO,EACX,IAAKA,EAAEzR,KAAKkX,YAAa,CACrB,GAAIvX,EAAQ+P,IAAM/P,EAAQ+P,IAAM+B,EAAE5C,KAC9B,OAAO,EACXa,GACJ,CACJ,CACA,OAAO,CACX,CACA,MAAMuQ,EACF,WAAArY,CAAY6R,EAAQiB,EAAQ5R,EAAOuS,GAC/B7T,KAAKiS,OAASA,EACdjS,KAAKkT,OAASA,EACdlT,KAAKsB,MAAQA,EACbtB,KAAK6T,MAAQA,CACjB,EAEJ,MAAM2E,UAAmBf,EACrB,QAAIpQ,GAAS,OAAOrH,KAAKxH,KAAK6O,IAAM,CACpC,QAAIlN,GAAS,OAAO6F,KAAK7H,QAAQ0b,MAAQ7T,KAAK7H,QAAQ+a,OAAOA,OAAOlT,KAAKsB,MAAQ,EAAI,CACrF,MAAI/G,GAAO,OAAOyF,KAAK7H,QAAQ0b,MAAQ7T,KAAK7H,QAAQ+a,OAAOA,OAAOlT,KAAKsB,MAAQ,EAAI,CACnF,WAAAlB,CAAYjI,EAASigB,EAAS9W,GAC1B+W,QACArY,KAAK7H,QAAUA,EACf6H,KAAKoY,QAAUA,EACfpY,KAAKsB,MAAQA,EACbtB,KAAKxH,KAAOL,EAAQ+a,OAAO/V,IAAI+S,MAAM/X,EAAQ+a,OAAOA,OAAO5R,GAC/D,CACA,KAAAL,CAAM2V,EAAK5V,EAAKqQ,GACZ,IAAI,OAAE6B,GAAWlT,KAAK7H,QAClBmJ,EAAQ4R,EAAOyD,UAAU3W,KAAKsB,MAAQ,EAAG4R,EAAOA,OAAOlT,KAAKsB,MAAQ,GAAIsV,EAAK5V,EAAMhB,KAAK7H,QAAQ0b,MAAOxC,GAC3G,OAAO/P,EAAQ,EAAI,KAAO,IAAIkX,EAAWxY,KAAK7H,QAAS6H,KAAMsB,EACjE,CACA,cAAIiE,GAAe,OAAOvF,KAAKiB,MAAM,EAAG,EAAG,EAAwB,CACnE,aAAIiX,GAAc,OAAOlY,KAAKiB,OAAO,EAAG,EAAG,EAAwB,CACnE,UAAAC,CAAWF,GAAO,OAAOhB,KAAKiB,MAAM,EAAGD,EAAK,EAAqB,CACjE,WAAAiX,CAAYjX,GAAO,OAAOhB,KAAKiB,OAAO,EAAGD,GAAM,EAAsB,CACrE,KAAAL,CAAMK,EAAKqQ,EAAMJ,EAAO,GACpB,GAAIA,EAAON,EAAS4H,eAChB,OAAO,KACX,IAAI,OAAErF,GAAWlT,KAAK7H,QAClBmJ,EAAQ4R,EAAOyD,UAAU3W,KAAKsB,MAAQ,EAAG4R,EAAOA,OAAOlT,KAAKsB,MAAQ,GAAI+P,EAAO,EAAI,GAAK,EAAGrQ,EAAMhB,KAAK7H,QAAQ0b,MAAOxC,GACzH,OAAO/P,EAAQ,EAAI,KAAO,IAAIkX,EAAWxY,KAAK7H,QAAS6H,KAAMsB,EACjE,CACA,UAAI2Q,GACA,OAAOjS,KAAKoY,SAAWpY,KAAK7H,QAAQ8Z,OAAO4G,uBAC/C,CACA,eAAAI,CAAgBrC,GACZ,OAAO5W,KAAKoY,QAAU,KAAOpY,KAAK7H,QAAQ8Z,OAAOqG,UAAUtY,KAAK7H,QAAQmJ,MAAQsV,EAAKA,EAAK,EAAG,EACjG,CACA,eAAIjE,GACA,IAAI,OAAEO,GAAWlT,KAAK7H,QAClBwf,EAAQzE,EAAOA,OAAOlT,KAAKsB,MAAQ,GACvC,OAAIqW,GAAS3X,KAAKoY,QAAUlF,EAAOA,OAAOlT,KAAKoY,QAAQ9W,MAAQ,GAAK4R,EAAOA,OAAOjY,QACvE,IAAIud,EAAWxY,KAAK7H,QAAS6H,KAAKoY,QAAST,GAC/C3X,KAAKiZ,gBAAgB,EAChC,CACA,eAAId,GACA,IAAI,OAAEjF,GAAWlT,KAAK7H,QAClBsb,EAAczT,KAAKoY,QAAUpY,KAAKoY,QAAQ9W,MAAQ,EAAI,EAC1D,OAAItB,KAAKsB,OAASmS,EACPzT,KAAKiZ,iBAAiB,GAC1B,IAAIT,EAAWxY,KAAK7H,QAAS6H,KAAKoY,QAASlF,EAAOyD,UAAUlD,EAAazT,KAAKsB,OAAQ,EAAG,EAAG,GACvG,CACA,QAAIsN,GAAS,OAAO,IAAM,CAC1B,MAAAmK,GACI,IAAIlI,EAAW,GAAI9P,EAAY,IAC3B,OAAEmS,GAAWlT,KAAK7H,QAClB6e,EAAShX,KAAKsB,MAAQ,EAAG2V,EAAO/D,EAAOA,OAAOlT,KAAKsB,MAAQ,GAC/D,GAAI2V,EAAOD,EAAQ,CACf,IAAI7c,EAAO+Y,EAAOA,OAAOlT,KAAKsB,MAAQ,GACtCuP,EAAS/U,KAAKoX,EAAO6D,MAAMC,EAAQC,EAAM9c,IACzC4G,EAAUjF,KAAK,EACnB,CACA,OAAO,IAAI8U,EAAK5Q,KAAKxH,KAAMqY,EAAU9P,EAAWf,KAAKzF,GAAKyF,KAAK7F,KACnE,CAIA,QAAAE,GAAa,OAAO2F,KAAK7H,QAAQ+a,OAAOuD,YAAYzW,KAAKsB,MAAQ,EAErE,SAAS8Q,EAAU8G,GACf,IAAKA,EAAMje,OACP,OAAO,KACX,IAAI4b,EAAO,EAAGsC,EAASD,EAAM,GAC7B,IAAK,IAAIhR,EAAI,EAAGA,EAAIgR,EAAMje,OAAQiN,IAAK,CACnC,IAAIrI,EAAOqZ,EAAMhR,IACbrI,EAAK1F,KAAOgf,EAAOhf,MAAQ0F,EAAKtF,GAAK4e,EAAO5e,MAC5C4e,EAAStZ,EACTgX,EAAO3O,EAEf,CACA,IAAIgM,EAAOiF,aAAkB1H,GAAY0H,EAAO7X,MAAQ,EAAI,KAAO6X,EAAOlH,OACtEmH,EAAWF,EAAMnC,QAKrB,OAJI7C,EACAkF,EAASvC,GAAQ3C,EAEjBkF,EAASC,OAAOxC,EAAM,GACnB,IAAIyC,EAAcF,EAAUD,EACvC,CACA,MAAMG,EACF,WAAAlZ,CAAY8Y,EAAOrZ,GACfG,KAAKkZ,MAAQA,EACblZ,KAAKH,KAAOA,CAChB,CACA,QAAIqU,GAAS,OAAO9B,EAAUpS,KAAKkZ,MAAQ,EAyB/C,MAAMhI,EAIF,QAAI7J,GAAS,OAAOrH,KAAKxH,KAAK6O,IAAM,CAIpC,WAAAjH,CAAYP,EAIZoR,EAAO,GAYH,GAXAjR,KAAKiR,KAAOA,EAIZjR,KAAKkT,OAAS,KACdlT,KAAKuZ,MAAQ,GAIbvZ,KAAKsB,MAAQ,EACbtB,KAAKwZ,WAAa,KACd3Z,aAAgB4R,EAChBzR,KAAKyZ,UAAU5Z,OAEd,CACDG,KAAKwR,MAAQ3R,EAAK1H,QAAQ8Z,OAC1BjS,KAAKkT,OAASrT,EAAK1H,QACnB,IAAK,IAAIuhB,EAAI7Z,EAAKuY,QAASsB,EAAGA,EAAIA,EAAEtB,QAChCpY,KAAKuZ,MAAMI,QAAQD,EAAEpY,OACzBtB,KAAKwZ,WAAa3Z,EAClBG,KAAK4Z,SAAS/Z,EAAKyB,MACvB,CACJ,CACA,SAAAmY,CAAU5Z,GACN,QAAKA,IAELG,KAAKwR,MAAQ3R,EACbG,KAAKxH,KAAOqH,EAAKrH,KACjBwH,KAAK7F,KAAO0F,EAAK1F,KACjB6F,KAAKzF,GAAKsF,EAAKtF,IACR,EACX,CACA,QAAAqf,CAAStY,EAAO9I,GACZwH,KAAKsB,MAAQA,EACb,IAAI,MAAEuS,EAAK,OAAEX,GAAWlT,KAAKkT,OAI7B,OAHAlT,KAAKxH,KAAOA,GAAQ0a,EAAO/V,IAAI+S,MAAMgD,EAAOA,OAAO5R,IACnDtB,KAAK7F,KAAO0Z,EAAQX,EAAOA,OAAO5R,EAAQ,GAC1CtB,KAAKzF,GAAKsZ,EAAQX,EAAOA,OAAO5R,EAAQ,IACjC,CACX,CAIA,KAAAuY,CAAMha,GACF,QAAKA,IAEDA,aAAgB4R,GAChBzR,KAAKkT,OAAS,KACPlT,KAAKyZ,UAAU5Z,KAE1BG,KAAKkT,OAASrT,EAAK1H,QACZ6H,KAAK4Z,SAAS/Z,EAAKyB,MAAOzB,EAAKrH,OAC1C,CAIA,QAAA6B,GACI,OAAO2F,KAAKkT,OAASlT,KAAKkT,OAAOA,OAAOuD,YAAYzW,KAAKsB,OAAStB,KAAKwR,MAAMnX,UACjF,CAIA,UAAAyf,CAAWlD,EAAK5V,EAAKqQ,GACjB,IAAKrR,KAAKkT,OACN,OAAOlT,KAAK6Z,MAAM7Z,KAAKwR,MAAM8G,UAAU1B,EAAM,EAAI5W,KAAKwR,MAAMA,MAAMX,SAAS5V,OAAS,EAAI,EAAG2b,EAAK5V,EAAKqQ,EAAMrR,KAAKiR,OACpH,IAAI,OAAEiC,GAAWlT,KAAKkT,OAClB5R,EAAQ4R,EAAOyD,UAAU3W,KAAKsB,MAAQ,EAAG4R,EAAOA,OAAOlT,KAAKsB,MAAQ,GAAIsV,EAAK5V,EAAMhB,KAAKkT,OAAOW,MAAOxC,GAC1G,QAAI/P,EAAQ,KAEZtB,KAAKuZ,MAAMzd,KAAKkE,KAAKsB,OACdtB,KAAK4Z,SAAStY,GACzB,CAKA,UAAAiE,GAAe,OAAOvF,KAAK8Z,WAAW,EAAG,EAAG,EAAwB,CAIpE,SAAA5B,GAAc,OAAOlY,KAAK8Z,YAAY,EAAG,EAAG,EAAwB,CAIpE,UAAA5Y,CAAWF,GAAO,OAAOhB,KAAK8Z,WAAW,EAAG9Y,EAAK,EAAqB,CAItE,WAAAiX,CAAYjX,GAAO,OAAOhB,KAAK8Z,YAAY,EAAG9Y,GAAM,EAAsB,CAQ1E,KAAAL,CAAMK,EAAKqQ,EAAMJ,EAAOjR,KAAKiR,MACzB,OAAKjR,KAAKkT,SAEHjC,EAAON,EAAS4H,iBAAyBvY,KAAK8Z,WAAW,EAAG9Y,EAAKqQ,GAD7DrR,KAAK6Z,MAAM7Z,KAAKwR,MAAM7Q,MAAMK,EAAKqQ,EAAMJ,GAEtD,CAIA,MAAAgB,GACI,IAAKjS,KAAKkT,OACN,OAAOlT,KAAKyZ,UAAWzZ,KAAKiR,KAAON,EAAS6B,iBAAoBxS,KAAKwR,MAAM4G,QAAUpY,KAAKwR,MAAMS,QACpG,GAAIjS,KAAKuZ,MAAMte,OACX,OAAO+E,KAAK4Z,SAAS5Z,KAAKuZ,MAAMtD,OACpC,IAAIhE,EAAUjS,KAAKiR,KAAON,EAAS6B,iBAAoBxS,KAAKkT,OAAOjB,OAASjS,KAAKkT,OAAOjB,OAAO4G,wBAE/F,OADA7Y,KAAKkT,OAAS,KACPlT,KAAKyZ,UAAUxH,EAC1B,CAIA,OAAA8H,CAAQnD,GACJ,IAAK5W,KAAKkT,OACN,QAAQlT,KAAKwR,MAAM4G,SACbpY,KAAK6Z,MAAM7Z,KAAKwR,MAAMlQ,MAAQ,EAAI,KAC9BtB,KAAKwR,MAAM4G,QAAQE,UAAUtY,KAAKwR,MAAMlQ,MAAQsV,EAAKA,EAAK,EAAG,EAAuB5W,KAAKiR,OACvG,IAAI,OAAEiC,GAAWlT,KAAKkT,OAAQ8G,EAAIha,KAAKuZ,MAAMte,OAAS,EACtD,GAAI2b,EAAM,EAAG,CACT,IAAInD,EAAcuG,EAAI,EAAI,EAAIha,KAAKuZ,MAAMS,GAAK,EAC9C,GAAIha,KAAKsB,OAASmS,EACd,OAAOzT,KAAK4Z,SAAS1G,EAAOyD,UAAUlD,EAAazT,KAAKsB,OAAQ,EAAG,EAAG,GAC9E,KACK,CACD,IAAIqW,EAAQzE,EAAOA,OAAOlT,KAAKsB,MAAQ,GACvC,GAAIqW,GAASqC,EAAI,EAAI9G,EAAOA,OAAOjY,OAASiY,EAAOA,OAAOlT,KAAKuZ,MAAMS,GAAK,IACtE,OAAOha,KAAK4Z,SAASjC,EAC7B,CACA,OAAOqC,EAAI,GAAIha,KAAK6Z,MAAM7Z,KAAKkT,OAAOjB,OAAOqG,UAAUtY,KAAKkT,OAAO5R,MAAQsV,EAAKA,EAAK,EAAG,EAAuB5W,KAAKiR,MACxH,CAIA,WAAA0B,GAAgB,OAAO3S,KAAK+Z,QAAQ,EAAI,CAIxC,WAAA5B,GAAgB,OAAOnY,KAAK+Z,SAAS,EAAI,CACzC,UAAAE,CAAWrD,GACP,IAAItV,EAAO2Q,GAAQ,OAAEiB,GAAWlT,KAChC,GAAIkT,EAAQ,CACR,GAAI0D,EAAM,GACN,GAAI5W,KAAKsB,MAAQ4R,EAAOA,OAAOA,OAAOjY,OAClC,OAAO,OAGX,IAAK,IAAIiN,EAAI,EAAGA,EAAIlI,KAAKsB,MAAO4G,IAC5B,GAAIgL,EAAOA,OAAOA,OAAOhL,EAAI,GAAKlI,KAAKsB,MACnC,OAAO,IAEhBA,QAAO2Q,UAAWiB,EACzB,OAEO5R,QAAO8W,QAASnG,GAAWjS,KAAKwR,OAEvC,KAAOS,IAAU3Q,QAAO8W,QAASnG,GAAWA,GACxC,GAAI3Q,GAAS,EACT,IAAK,IAAI4G,EAAI5G,EAAQsV,EAAK7X,EAAI6X,EAAM,GAAK,EAAI3E,EAAOT,MAAMX,SAAS5V,OAAQiN,GAAKnJ,EAAGmJ,GAAK0O,EAAK,CACzF,IAAI3V,EAAQgR,EAAOT,MAAMX,SAAS3I,GAClC,GAAKlI,KAAKiR,KAAON,EAAS6B,kBACtBvR,aAAiB8T,IAChB9T,EAAMzI,KAAKkX,aACZgJ,EAASzX,GACT,OAAO,CACf,CAER,OAAO,CACX,CACA,IAAAiZ,CAAKtD,EAAKjW,GACN,GAAIA,GAASX,KAAK8Z,WAAWlD,EAAK,EAAG,GACjC,OAAO,EACX,OAAS,CACL,GAAI5W,KAAK+Z,QAAQnD,GACb,OAAO,EACX,GAAI5W,KAAKia,WAAWrD,KAAS5W,KAAKiS,SAC9B,OAAO,CACf,CACJ,CAQA,IAAAiC,CAAKvT,GAAQ,GAAQ,OAAOX,KAAKka,KAAK,EAAGvZ,EAAQ,CAOjD,IAAAwZ,CAAKxZ,GAAQ,GAAQ,OAAOX,KAAKka,MAAM,EAAGvZ,EAAQ,CAMlD,MAAA4Q,CAAOvQ,EAAKqQ,EAAO,GAEf,MAAOrR,KAAK7F,MAAQ6F,KAAKzF,KACpB8W,EAAO,EAAIrR,KAAK7F,MAAQ6G,EAAMhB,KAAK7F,KAAO6G,KAC1CqQ,GAAQ,EAAIrR,KAAKzF,IAAMyG,EAAMhB,KAAKzF,GAAKyG,KACnChB,KAAKiS,WAGd,KAAOjS,KAAK8Z,WAAW,EAAG9Y,EAAKqQ,KAC/B,OAAOrR,IACX,CAKA,QAAIH,GACA,IAAKG,KAAKkT,OACN,OAAOlT,KAAKwR,MAChB,IAAI4I,EAAQpa,KAAKwZ,WAAYtL,EAAS,KAAM0F,EAAQ,EACpD,GAAIwG,GAASA,EAAMjiB,SAAW6H,KAAKkT,OAC/BlB,EAAM,IAAK,IAAI1Q,EAAQtB,KAAKsB,MAAO0Y,EAAIha,KAAKuZ,MAAMte,OAAQ+e,GAAK,GAAI,CAC/D,IAAK,IAAIvH,EAAI2H,EAAO3H,EAAGA,EAAIA,EAAE2F,QACzB,GAAI3F,EAAEnR,OAASA,EAAO,CAClB,GAAIA,GAAStB,KAAKsB,MACd,OAAOmR,EACXvE,EAASuE,EACTmB,EAAQoG,EAAI,EACZ,MAAMhI,CACV,CACJ1Q,EAAQtB,KAAKuZ,QAAQS,EACzB,CAEJ,IAAK,IAAI9R,EAAI0L,EAAO1L,EAAIlI,KAAKuZ,MAAMte,OAAQiN,IACvCgG,EAAS,IAAIsK,EAAWxY,KAAKkT,OAAQhF,EAAQlO,KAAKuZ,MAAMrR,IAC5D,OAAOlI,KAAKwZ,WAAa,IAAIhB,EAAWxY,KAAKkT,OAAQhF,EAAQlO,KAAKsB,MACtE,CAMA,QAAIsN,GACA,OAAO5O,KAAKkT,OAAS,KAAOlT,KAAKwR,MAAMA,KAC3C,CAOA,OAAA9Q,CAAQC,EAAO2R,GACX,IAAK,IAAIsB,EAAQ,IAAK,CAClB,IAAIyG,GAAY,EAChB,GAAIra,KAAKxH,KAAKkX,cAA+B,IAAhB/O,EAAMX,MAAiB,CAChD,GAAIA,KAAKuF,aAAc,CACnBqO,IACA,QACJ,CACK5T,KAAKxH,KAAKkX,cACX2K,GAAY,EACpB,CACA,OAAS,CAIL,GAHIA,GAAa/H,GACbA,EAAMtS,MACVqa,EAAYra,KAAKxH,KAAKkX,aACjBkE,EACD,OACJ,GAAI5T,KAAK2S,cACL,MACJ3S,KAAKiS,SACL2B,IACAyG,GAAY,CAChB,CACJ,CACJ,CAMA,YAAAvC,CAAa3f,GACT,IAAK6H,KAAKkT,OACN,OAAO6E,EAAiB/X,KAAKH,KAAKoS,OAAQ9Z,GAC9C,IAAI,OAAE+a,GAAWlT,KAAKkT,QAAQ,MAAEhD,GAAUgD,EAAO/V,IACjD,IAAK,IAAI+K,EAAI/P,EAAQ8C,OAAS,EAAG+e,EAAIha,KAAKuZ,MAAMte,OAAS,EAAGiN,GAAK,EAAG8R,IAAK,CACrE,GAAIA,EAAI,EACJ,OAAOjC,EAAiB/X,KAAKwR,MAAOrZ,EAAS+P,GACjD,IAAI1P,EAAO0X,EAAMgD,EAAOA,OAAOlT,KAAKuZ,MAAMS,KAC1C,IAAKxhB,EAAKkX,YAAa,CACnB,GAAIvX,EAAQ+P,IAAM/P,EAAQ+P,IAAM1P,EAAK6O,KACjC,OAAO,EACXa,GACJ,CACJ,CACA,OAAO,CACX,EAEJ,SAASwQ,EAAS9J,GACd,OAAOA,EAAKiC,SAASyJ,MAAKxJ,GAAMA,aAAciE,IAAejE,EAAGtY,KAAKkX,aAAegJ,EAAS5H,IACjG,CAgOA,MAAMyJ,EAAgB,IAAI9J,QAC1B,SAAS+D,EAASgG,EAAa3a,GAC3B,IAAK2a,EAAY9K,aAAe7P,aAAgBkV,GAAclV,EAAKrH,MAAQgiB,EACvE,OAAO,EACX,IAAIzG,EAAOwG,EAAczL,IAAIjP,GAC7B,GAAY,MAARkU,EAAc,CACdA,EAAO,EACP,IAAK,IAAI9S,KAASpB,EAAKgR,SAAU,CAC7B,GAAI5P,EAAMzI,MAAQgiB,KAAiBvZ,aAAiB2P,GAAO,CACvDmD,EAAO,EACP,KACJ,CACAA,GAAQS,EAASgG,EAAavZ,EAClC,CACAsZ,EAAcpd,IAAI0C,EAAMkU,EAC5B,CACA,OAAOA,CACX,CACA,SAASjB,EAET0H,EAEA3J,EAAU9P,EAEV5G,EAAMI,EAENsZ,EAEA5Y,EAEAwf,EAEAC,GACI,IAAIC,EAAQ,EACZ,IAAK,IAAIzS,EAAI/N,EAAM+N,EAAI3N,EAAI2N,IACvByS,GAASnG,EAASgG,EAAa3J,EAAS3I,IAC5C,IAAI0S,EAAWvD,KAAKwD,KAAc,IAARF,EAAe,GACrC3F,EAAgB,GAAIC,EAAiB,GA2BzC,OA1BA,SAAS6F,EAAOjK,EAAU9P,EAAW5G,EAAMI,EAAIwgB,GAC3C,IAAK,IAAI7S,EAAI/N,EAAM+N,EAAI3N,GAAK,CACxB,IAAIygB,EAAY9S,EAAG+S,EAAala,EAAUmH,GAAIgT,EAAY1G,EAASgG,EAAa3J,EAAS3I,IAEzF,IADAA,IACOA,EAAI3N,EAAI2N,IAAK,CAChB,IAAIiT,EAAW3G,EAASgG,EAAa3J,EAAS3I,IAC9C,GAAIgT,EAAYC,GAAYP,EACxB,MACJM,GAAaC,CACjB,CACA,GAAIjT,GAAK8S,EAAY,EAAG,CACpB,GAAIE,EAAYN,EAAU,CACtB,IAAIQ,EAAOvK,EAASmK,GACpBF,EAAOM,EAAKvK,SAAUuK,EAAKra,UAAW,EAAGqa,EAAKvK,SAAS5V,OAAQ8F,EAAUia,GAAaD,GACtF,QACJ,CACA/F,EAAclZ,KAAK+U,EAASmK,GAChC,KACK,CACD,IAAI/f,EAAS8F,EAAUmH,EAAI,GAAK2I,EAAS3I,EAAI,GAAGjN,OAASggB,EACzDjG,EAAclZ,KAAKgX,EAAa0H,EAAa3J,EAAU9P,EAAWia,EAAW9S,EAAG+S,EAAYhgB,EAAQ,KAAMyf,GAC9G,CACAzF,EAAenZ,KAAKmf,EAAaF,EAASlH,EAC9C,CACJ,CACAiH,CAAOjK,EAAU9P,EAAW5G,EAAMI,EAAI,IAC9BkgB,GAASC,GAAQ1F,EAAeC,EAAgBha,EAC5D,CAkKA,MAAMogB,EAWF,UAAAC,CAAWC,EAAOC,EAAWC,GAIzB,MAHoB,iBAATF,IACPA,EAAQ,IAAIG,EAAYH,IAC5BE,EAAUA,EAAwCA,EAAOxgB,OAASwgB,EAAO5X,KAAI+T,GAAK,IAAIlK,EAAMkK,EAAEzd,KAAMyd,EAAErd,MAAO,CAAC,IAAImT,EAAM,EAAG,IAAxG,CAAC,IAAIA,EAAM,EAAG6N,EAAMtgB,SAChC+E,KAAK2b,YAAYJ,EAAOC,GAAa,GAAIC,EACpD,CAIA,KAAAhb,CAAM8a,EAAOC,EAAWC,GACpB,IAAIhb,EAAQT,KAAKsb,WAAWC,EAAOC,EAAWC,GAC9C,OAAS,CACL,IAAIG,EAAOnb,EAAMob,UACjB,GAAID,EACA,OAAOA,CACf,CACJ,EAEJ,MAAMF,EACF,WAAAtb,CAAY2G,GACR/G,KAAK+G,OAASA,CAClB,CACA,UAAI9L,GAAW,OAAO+E,KAAK+G,OAAO9L,MAAQ,CAC1C,KAAA6gB,CAAM3hB,GAAQ,OAAO6F,KAAK+G,OAAOgQ,MAAM5c,EAAO,CAC9C,cAAI4hB,GAAe,OAAO,CAAO,CACjC,IAAAC,CAAK7hB,EAAMI,GAAM,OAAOyF,KAAK+G,OAAOgQ,MAAM5c,EAAMI,EAAK,EAuCpC,IAAIoT,EAAS,CAAEE,SAAS,ICrvD7C,MAAMoO,EAIF,WAAA7b,CAIA6J,EAKAsP,EAIA2C,EAQAC,EAIAnb,EAMAob,EAOAlJ,EASAmJ,EAIAC,EAIA7N,EAAY,EAQZwD,GACIjS,KAAKiK,EAAIA,EACTjK,KAAKuZ,MAAQA,EACbvZ,KAAKkc,MAAQA,EACblc,KAAKmc,UAAYA,EACjBnc,KAAKgB,IAAMA,EACXhB,KAAKoc,MAAQA,EACbpc,KAAKkT,OAASA,EACdlT,KAAKqc,WAAaA,EAClBrc,KAAKsc,WAAaA,EAClBtc,KAAKyO,UAAYA,EACjBzO,KAAKiS,OAASA,CAClB,CAIA,QAAA5X,GACI,MAAO,IAAI2F,KAAKuZ,MAAMlW,QAAO,CAACkZ,EAAGrU,IAAMA,EAAI,GAAK,IAAGiO,OAAOnW,KAAKkc,WAAWlc,KAAKgB,MAAMhB,KAAKoc,MAAQ,IAAMpc,KAAKoc,MAAQ,IACzH,CAKA,YAAOvI,CAAM5J,EAAGiS,EAAOlb,EAAM,GACzB,IAAIwb,EAAKvS,EAAEzO,OAAOrD,QAClB,OAAO,IAAI8jB,EAAMhS,EAAG,GAAIiS,EAAOlb,EAAKA,EAAK,EAAG,GAAI,EAAGwb,EAAK,IAAIC,EAAaD,EAAIA,EAAG3I,OAAS,KAAM,EAAG,KACtG,CAOA,WAAI1b,GAAY,OAAO6H,KAAKsc,WAAatc,KAAKsc,WAAWnkB,QAAU,IAAM,CAMzE,SAAAukB,CAAUR,EAAOrI,GACb7T,KAAKuZ,MAAMzd,KAAKkE,KAAKkc,MAAOrI,EAAO7T,KAAKqc,WAAarc,KAAKkT,OAAOjY,QACjE+E,KAAKkc,MAAQA,CACjB,CAKA,MAAAS,CAAOC,GACH,IAAI3J,EACJ,IAAIW,EAAQgJ,GAAU,GAAkCpkB,EAAgB,MAATokB,GAC3D,OAAEphB,GAAWwE,KAAKiK,EAClB4S,EAAkB7c,KAAKmc,UAAYnc,KAAKgB,IAAM,GAC9C6b,GACA7c,KAAK8c,aAAa9c,KAAKgB,KAC3B,IAAI+b,EAAQvhB,EAAOwhB,kBAAkBxkB,GAGrC,GAFIukB,IACA/c,KAAKoc,OAASW,GACL,GAATnJ,EAOA,OANA5T,KAAK0c,UAAUlhB,EAAOyhB,QAAQjd,KAAKkc,MAAO1jB,GAAM,GAAOwH,KAAKmc,WAGxD3jB,EAAOgD,EAAO0hB,eACdld,KAAKmd,UAAU3kB,EAAMwH,KAAKmc,UAAWnc,KAAKmc,UAAWU,EAAkB,EAAI,GAAG,QAClF7c,KAAKod,cAAc5kB,EAAMwH,KAAKmc,WAQlC,IAAInG,EAAOhW,KAAKuZ,MAAMte,OAAwB,GAAb2Y,EAAQ,IAAoB,OAATgJ,EAAwC,EAAI,GAC5F/I,EAAQmC,EAAOhW,KAAKuZ,MAAMvD,EAAO,GAAKhW,KAAKiK,EAAEwR,OAAO,GAAGthB,KAAM4Z,EAAO/T,KAAKmc,UAAYtI,EAIrFE,GAAQ,OAAqF,QAA5Cd,EAAKjT,KAAKiK,EAAEzO,OAAO2X,QAAQjD,MAAM1X,UAA0B,IAAPya,OAAgB,EAASA,EAAGvD,eAC7HmE,GAAS7T,KAAKiK,EAAEoT,uBAChBrd,KAAKiK,EAAEqT,oBACPtd,KAAKiK,EAAEsT,qBAAuBxJ,GAEzB/T,KAAKiK,EAAEsT,qBAAuBxJ,IACnC/T,KAAKiK,EAAEqT,kBAAoB,EAC3Btd,KAAKiK,EAAEoT,sBAAwBxJ,EAC/B7T,KAAKiK,EAAEsT,qBAAuBxJ,IAGtC,IAAIsI,EAAarG,EAAOhW,KAAKuZ,MAAMvD,EAAO,GAAK,EAAGwH,EAAQxd,KAAKqc,WAAarc,KAAKkT,OAAOjY,OAASohB,EAEjG,GAAI7jB,EAAOgD,EAAO0hB,eAA2B,OAATN,EAA0C,CAC1E,IAAI5b,EAAMxF,EAAOiiB,UAAUzd,KAAKkc,MAAO,GAA6Blc,KAAKgB,IAAMhB,KAAKmc,UACpFnc,KAAKmd,UAAU3kB,EAAMqb,EAAO7S,EAAKwc,EAAQ,GAAG,EAChD,CACA,GAAa,OAATZ,EACA5c,KAAKkc,MAAQlc,KAAKuZ,MAAMvD,OAEvB,CACD,IAAI0H,EAAc1d,KAAKuZ,MAAMvD,EAAO,GACpChW,KAAKkc,MAAQ1gB,EAAOyhB,QAAQS,EAAallB,GAAM,EACnD,CACA,KAAOwH,KAAKuZ,MAAMte,OAAS+a,GACvBhW,KAAKuZ,MAAMtD,MACfjW,KAAKod,cAAc5kB,EAAMqb,EAC7B,CAKA,SAAAsJ,CAAUQ,EAAM9J,EAAOC,EAAKC,EAAO,EAAG6J,GAAW,GAC7C,GAAY,GAARD,KACE3d,KAAKuZ,MAAMte,QAAU+E,KAAKuZ,MAAMvZ,KAAKuZ,MAAMte,OAAS,GAAK+E,KAAKkT,OAAOjY,OAAS+E,KAAKqc,YAAa,CAElG,IAAIrD,EAAMhZ,KAAMoP,EAAMpP,KAAKkT,OAAOjY,OAKlC,GAJW,GAAPmU,GAAY4J,EAAI/G,SAChB7C,EAAM4J,EAAIqD,WAAarD,EAAI/G,OAAOoK,WAClCrD,EAAMA,EAAI/G,QAEV7C,EAAM,GAA4B,GAAvB4J,EAAI9F,OAAO9D,EAAM,IAA0B4J,EAAI9F,OAAO9D,EAAM,IAAM,EAAG,CAChF,GAAIyE,GAASC,EACT,OACJ,GAAIkF,EAAI9F,OAAO9D,EAAM,IAAMyE,EAEvB,YADAmF,EAAI9F,OAAO9D,EAAM,GAAK0E,EAG9B,CACJ,CACA,GAAK8J,GAAY5d,KAAKgB,KAAO8S,EAGxB,CACD,IAAIxS,EAAQtB,KAAKkT,OAAOjY,OACxB,GAAIqG,EAAQ,GAA+B,GAA1BtB,KAAKkT,OAAO5R,EAAQ,GAAwB,CACzD,IAAIuc,GAAW,EACf,IAAK,IAAI7L,EAAO1Q,EAAO0Q,EAAO,GAAKhS,KAAKkT,OAAOlB,EAAO,GAAK8B,EAAK9B,GAAQ,EACpE,GAAIhS,KAAKkT,OAAOlB,EAAO,IAAM,EAAG,CAC5B6L,GAAW,EACX,KACJ,CAEJ,GAAIA,EACA,KAAOvc,EAAQ,GAAKtB,KAAKkT,OAAO5R,EAAQ,GAAKwS,GAEzC9T,KAAKkT,OAAO5R,GAAStB,KAAKkT,OAAO5R,EAAQ,GACzCtB,KAAKkT,OAAO5R,EAAQ,GAAKtB,KAAKkT,OAAO5R,EAAQ,GAC7CtB,KAAKkT,OAAO5R,EAAQ,GAAKtB,KAAKkT,OAAO5R,EAAQ,GAC7CtB,KAAKkT,OAAO5R,EAAQ,GAAKtB,KAAKkT,OAAO5R,EAAQ,GAC7CA,GAAS,EACLyS,EAAO,IACPA,GAAQ,EAExB,CACA/T,KAAKkT,OAAO5R,GAASqc,EACrB3d,KAAKkT,OAAO5R,EAAQ,GAAKuS,EACzB7T,KAAKkT,OAAO5R,EAAQ,GAAKwS,EACzB9T,KAAKkT,OAAO5R,EAAQ,GAAKyS,CAC7B,MA5BI/T,KAAKkT,OAAOpX,KAAK6hB,EAAM9J,EAAOC,EAAKC,EA6B3C,CAKA,KAAA+J,CAAMlB,EAAQpkB,EAAMqb,EAAOC,GACvB,GAAa,OAAT8I,EACA5c,KAAK0c,UAAmB,MAATE,EAAuC5c,KAAKgB,UAE1D,GAAc,OAAT4b,EAaN5c,KAAKgB,IAAM8S,EACX9T,KAAK+d,aAAavlB,EAAMqb,GACpBrb,GAAQwH,KAAKiK,EAAEzO,OAAOwiB,SACtBhe,KAAKkT,OAAOpX,KAAKtD,EAAMqb,EAAOC,EAAK,OAhBY,CACnD,IAAImK,EAAYrB,GAAQ,OAAEphB,GAAWwE,KAAKiK,GACtC6J,EAAM9T,KAAKgB,KAAOxI,GAAQgD,EAAOwiB,WACjChe,KAAKgB,IAAM8S,EACNtY,EAAOiiB,UAAUQ,EAAW,KAC7Bje,KAAKmc,UAAYrI,IAEzB9T,KAAK0c,UAAUuB,EAAWpK,GAC1B7T,KAAK+d,aAAavlB,EAAMqb,GACpBrb,GAAQgD,EAAOwiB,SACfhe,KAAKkT,OAAOpX,KAAKtD,EAAMqb,EAAOC,EAAK,EAC3C,CAOJ,CAKA,KAAAoK,CAAMtB,EAAQ1I,EAAMiK,EAAWC,GACd,MAATxB,EACA5c,KAAK2c,OAAOC,GAEZ5c,KAAK8d,MAAMlB,EAAQ1I,EAAMiK,EAAWC,EAC5C,CAKA,OAAAC,CAAQvmB,EAAOoc,GACX,IAAI5S,EAAQtB,KAAKiK,EAAEoJ,OAAOpY,OAAS,GAC/BqG,EAAQ,GAAKtB,KAAKiK,EAAEoJ,OAAO/R,IAAUxJ,KACrCkI,KAAKiK,EAAEoJ,OAAOvX,KAAKhE,GACnBwJ,KAEJ,IAAIuS,EAAQ7T,KAAKgB,IACjBhB,KAAKmc,UAAYnc,KAAKgB,IAAM6S,EAAQ/b,EAAMmD,OAC1C+E,KAAK0c,UAAUxI,EAAML,GACrB7T,KAAKkT,OAAOpX,KAAKwF,EAAOuS,EAAO7T,KAAKmc,WAAY,GAC5Cnc,KAAKsc,YACLtc,KAAKse,cAActe,KAAKsc,WAAWiC,QAAQC,MAAMxe,KAAKsc,WAAWnkB,QAASL,EAAOkI,KAAMA,KAAKiK,EAAEwU,OAAOC,MAAM1e,KAAKgB,IAAMlJ,EAAMmD,SACpI,CAOA,KAAA3B,GACI,IAAI2Y,EAASjS,KACT2e,EAAM1M,EAAOiB,OAAOjY,OAKxB,KAAO0jB,EAAM,GAAK1M,EAAOiB,OAAOyL,EAAM,GAAK1M,EAAOkK,WAC9CwC,GAAO,EACX,IAAIzL,EAASjB,EAAOiB,OAAO6D,MAAM4H,GAAM3I,EAAO/D,EAAOoK,WAAasC,EAElE,KAAO1M,GAAU+D,GAAQ/D,EAAOoK,YAC5BpK,EAASA,EAAOA,OACpB,OAAO,IAAIgK,EAAMjc,KAAKiK,EAAGjK,KAAKuZ,MAAMxC,QAAS/W,KAAKkc,MAAOlc,KAAKmc,UAAWnc,KAAKgB,IAAKhB,KAAKoc,MAAOlJ,EAAQ8C,EAAMhW,KAAKsc,WAAYtc,KAAKyO,UAAWwD,EAClJ,CAKA,eAAA2M,CAAgB1K,EAAMkK,GAClB,IAAIS,EAAS3K,GAAQlU,KAAKiK,EAAEzO,OAAOwiB,QAC/Ba,GACA7e,KAAKmd,UAAUjJ,EAAMlU,KAAKgB,IAAKod,EAAS,GAC5Cpe,KAAKmd,UAAU,EAAkBnd,KAAKgB,IAAKod,EAASS,EAAS,EAAI,GACjE7e,KAAKgB,IAAMhB,KAAKmc,UAAYiC,EAC5Bpe,KAAKoc,OAAS,GAClB,CAOA,QAAA0C,CAASnB,GACL,IAAK,IAAIoB,EAAM,IAAIC,EAAehf,QAAS,CACvC,IAAI4c,EAAS5c,KAAKiK,EAAEzO,OAAOyjB,UAAUF,EAAI7C,MAAO,IAAqClc,KAAKiK,EAAEzO,OAAO0jB,UAAUH,EAAI7C,MAAOyB,GACxH,GAAc,GAAVf,EACA,OAAO,EACX,KAAc,MAATA,GACD,OAAO,EACXmC,EAAIpC,OAAOC,EACf,CACJ,CAMA,eAAAuC,CAAgBjL,GACZ,GAAIlU,KAAKuZ,MAAMte,QAAU,IACrB,MAAO,GACX,IAAImkB,EAAapf,KAAKiK,EAAEzO,OAAO4jB,WAAWpf,KAAKkc,OAC/C,GAAIkD,EAAWnkB,OAAS,GAAgC+E,KAAKuZ,MAAMte,QAAU,IAA0C,CACnH,IAAIokB,EAAO,GACX,IAAK,IAAWzX,EAAPM,EAAI,EAAMA,EAAIkX,EAAWnkB,OAAQiN,GAAK,GACtCN,EAAIwX,EAAWlX,EAAI,KAAOlI,KAAKkc,OAASlc,KAAKiK,EAAEzO,OAAO0jB,UAAUtX,EAAGsM,IACpEmL,EAAKvjB,KAAKsjB,EAAWlX,GAAIN,GAEjC,GAAI5H,KAAKuZ,MAAMte,OAAS,IACpB,IAAK,IAAIiN,EAAI,EAAGmX,EAAKpkB,OAAS,GAAgCiN,EAAIkX,EAAWnkB,OAAQiN,GAAK,EAAG,CACzF,IAAIN,EAAIwX,EAAWlX,EAAI,GAClBmX,EAAK/E,MAAK,CAACgF,EAAGpX,IAAW,EAAJA,GAAUoX,GAAK1X,KACrCyX,EAAKvjB,KAAKsjB,EAAWlX,GAAIN,EACjC,CACJwX,EAAaC,CACjB,CACA,IAAInR,EAAS,GACb,IAAK,IAAIhG,EAAI,EAAGA,EAAIkX,EAAWnkB,QAAUiT,EAAOjT,OAAS,EAAyBiN,GAAK,EAAG,CACtF,IAAIN,EAAIwX,EAAWlX,EAAI,GACvB,GAAIN,GAAK5H,KAAKkc,MACV,SACJ,IAAI3C,EAAQvZ,KAAK1G,QACjBigB,EAAMmD,UAAU9U,EAAG5H,KAAKgB,KACxBuY,EAAM4D,UAAU,EAAkB5D,EAAMvY,IAAKuY,EAAMvY,IAAK,GAAG,GAC3DuY,EAAMwE,aAAaqB,EAAWlX,GAAIlI,KAAKgB,KACvCuY,EAAM4C,UAAYnc,KAAKgB,IACvBuY,EAAM6C,OAAS,IACflO,EAAOpS,KAAKyd,EAChB,CACA,OAAOrL,CACX,CAMA,WAAAqR,GACI,IAAI,OAAE/jB,GAAWwE,KAAKiK,EAClB0S,EAASnhB,EAAOyjB,UAAUjf,KAAKkc,MAAO,GAC1C,KAAc,MAATS,GACD,OAAO,EACX,IAAKnhB,EAAOgkB,YAAYxf,KAAKkc,MAAOS,GAAS,CACzC,IAAI/I,EAAQ+I,GAAU,GAAkCgB,EAAgB,MAAThB,EAC3DrkB,EAAS0H,KAAKuZ,MAAMte,OAAiB,EAAR2Y,EACjC,GAAItb,EAAS,GAAKkD,EAAOyhB,QAAQjd,KAAKuZ,MAAMjhB,GAASqlB,GAAM,GAAS,EAAG,CACnE,IAAI8B,EAASzf,KAAK0f,sBAClB,GAAc,MAAVD,EACA,OAAO,EACX9C,EAAS8C,CACb,CACAzf,KAAKmd,UAAU,EAAkBnd,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxDhB,KAAKoc,OAAS,GAClB,CAGA,OAFApc,KAAKmc,UAAYnc,KAAKgB,IACtBhB,KAAK2c,OAAOA,IACL,CACX,CAMA,mBAAA+C,GACI,IAAI,OAAElkB,GAAWwE,KAAKiK,EAAG0V,EAAO,GAC5BC,EAAU,CAAC1D,EAAOtI,KAClB,IAAI+L,EAAK9e,SAASqb,GAGlB,OADAyD,EAAK7jB,KAAKogB,GACH1gB,EAAOqkB,WAAW3D,GAAQU,IAC7B,GAAa,OAATA,QACC,GAAa,MAATA,EAAwC,CAC7C,IAAIkD,GAAUlD,GAAU,IAAoChJ,EAC5D,GAAIkM,EAAS,EAAG,CACZ,IAAInC,EAAgB,MAATf,EAAuCtkB,EAAS0H,KAAKuZ,MAAMte,OAAkB,EAAT6kB,EAC/E,GAAIxnB,GAAU,GAAKkD,EAAOyhB,QAAQjd,KAAKuZ,MAAMjhB,GAASqlB,GAAM,IAAU,EAClE,OAAQmC,GAAU,GAAoC,MAAgCnC,CAC9F,CACJ,KACK,CACD,IAAI5N,EAAQ6P,EAAQhD,EAAQhJ,EAAQ,GACpC,GAAa,MAAT7D,EACA,OAAOA,CACf,IACF,EAEN,OAAO6P,EAAQ5f,KAAKkc,MAAO,EAC/B,CAIA,QAAA6D,GACI,MAAQ/f,KAAKiK,EAAEzO,OAAOiiB,UAAUzd,KAAKkc,MAAO,IACxC,IAAKlc,KAAKuf,cAAe,CACrBvf,KAAKmd,UAAU,EAAkBnd,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxD,KACJ,CAEJ,OAAOhB,IACX,CAMA,WAAIggB,GACA,GAAyB,GAArBhgB,KAAKuZ,MAAMte,OACX,OAAO,EACX,IAAI,OAAEO,GAAWwE,KAAKiK,EACtB,OAAgF,OAAzEzO,EAAO+D,KAAK/D,EAAOyjB,UAAUjf,KAAKkc,MAAO,MAC3C1gB,EAAOyjB,UAAUjf,KAAKkc,MAAO,EACtC,CAMA,OAAA+D,GACIjgB,KAAKmd,UAAU,EAAkBnd,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxDhB,KAAKkc,MAAQlc,KAAKuZ,MAAM,GACxBvZ,KAAKuZ,MAAMte,OAAS,CACxB,CAIA,SAAAilB,CAAUC,GACN,GAAIngB,KAAKkc,OAASiE,EAAMjE,OAASlc,KAAKuZ,MAAMte,QAAUklB,EAAM5G,MAAMte,OAC9D,OAAO,EACX,IAAK,IAAIiN,EAAI,EAAGA,EAAIlI,KAAKuZ,MAAMte,OAAQiN,GAAK,EACxC,GAAIlI,KAAKuZ,MAAMrR,IAAMiY,EAAM5G,MAAMrR,GAC7B,OAAO,EACf,OAAO,CACX,CAIA,UAAI1M,GAAW,OAAOwE,KAAKiK,EAAEzO,MAAQ,CAKrC,cAAA4kB,CAAeC,GAAa,OAAOrgB,KAAKiK,EAAEzO,OAAO8kB,QAAQrR,MAAMoR,EAAY,CAC3E,YAAAtC,CAAaJ,EAAM9J,GACX7T,KAAKsc,YACLtc,KAAKse,cAActe,KAAKsc,WAAWiC,QAAQT,MAAM9d,KAAKsc,WAAWnkB,QAASwlB,EAAM3d,KAAMA,KAAKiK,EAAEwU,OAAOC,MAAM7K,IAClH,CACA,aAAAuJ,CAAcO,EAAM9J,GACZ7T,KAAKsc,YACLtc,KAAKse,cAActe,KAAKsc,WAAWiC,QAAQ5B,OAAO3c,KAAKsc,WAAWnkB,QAASwlB,EAAM3d,KAAMA,KAAKiK,EAAEwU,OAAOC,MAAM7K,IACnH,CAIA,WAAA0M,GACI,IAAI9K,EAAOzV,KAAKkT,OAAOjY,OAAS,GAC5Bwa,EAAO,IAA2B,GAAtBzV,KAAKkT,OAAOuC,KACxBzV,KAAKkT,OAAOpX,KAAKkE,KAAKsc,WAAWkE,KAAMxgB,KAAKgB,IAAKhB,KAAKgB,KAAM,EACpE,CAIA,aAAAyf,GACI,IAAIhL,EAAOzV,KAAKkT,OAAOjY,OAAS,GAC5Bwa,EAAO,IAA2B,GAAtBzV,KAAKkT,OAAOuC,KACxBzV,KAAKkT,OAAOpX,KAAKkE,KAAKyO,UAAWzO,KAAKgB,IAAKhB,KAAKgB,KAAM,EAC9D,CACA,aAAAsd,CAAcnmB,GACV,GAAIA,GAAW6H,KAAKsc,WAAWnkB,QAAS,CACpC,IAAIuoB,EAAQ,IAAIjE,EAAazc,KAAKsc,WAAWiC,QAASpmB,GAClDuoB,EAAMF,MAAQxgB,KAAKsc,WAAWkE,MAC9BxgB,KAAKugB,cACTvgB,KAAKsc,WAAaoE,CACtB,CACJ,CAIA,YAAA5D,CAAarO,GACLA,EAAYzO,KAAKyO,YACjBzO,KAAKygB,gBACLzgB,KAAKyO,UAAYA,EAEzB,CAIA,KAAAkS,GACQ3gB,KAAKsc,YAActc,KAAKsc,WAAWiC,QAAQqC,QAC3C5gB,KAAKugB,cACLvgB,KAAKyO,UAAY,GACjBzO,KAAKygB,eACb,EAEJ,MAAMhE,EACF,WAAArc,CAAYme,EAASpmB,GACjB6H,KAAKue,QAAUA,EACfve,KAAK7H,QAAUA,EACf6H,KAAKwgB,KAAOjC,EAAQqC,OAASrC,EAAQiC,KAAKroB,GAAW,CACzD,EAIJ,MAAM6mB,EACF,WAAA5e,CAAYyT,GACR7T,KAAK6T,MAAQA,EACb7T,KAAKkc,MAAQrI,EAAMqI,MACnBlc,KAAKuZ,MAAQ1F,EAAM0F,MACnBvZ,KAAKgW,KAAOhW,KAAKuZ,MAAMte,MAC3B,CACA,MAAA0hB,CAAOC,GACH,IAAIe,EAAgB,MAATf,EAAuChJ,EAAQgJ,GAAU,GACvD,GAAThJ,GACI5T,KAAKuZ,OAASvZ,KAAK6T,MAAM0F,QACzBvZ,KAAKuZ,MAAQvZ,KAAKuZ,MAAMxC,SAC5B/W,KAAKuZ,MAAMzd,KAAKkE,KAAKkc,MAAO,EAAG,GAC/Blc,KAAKgW,MAAQ,GAGbhW,KAAKgW,MAAsB,GAAbpC,EAAQ,GAE1B,IAAIiN,EAAO7gB,KAAK6T,MAAM5J,EAAEzO,OAAOyhB,QAAQjd,KAAKuZ,MAAMvZ,KAAKgW,KAAO,GAAI2H,GAAM,GACxE3d,KAAKkc,MAAQ2E,CACjB,EAIJ,MAAMC,EACF,WAAA1gB,CAAYmZ,EAAOvY,EAAKM,GACpBtB,KAAKuZ,MAAQA,EACbvZ,KAAKgB,IAAMA,EACXhB,KAAKsB,MAAQA,EACbtB,KAAKkT,OAASqG,EAAMrG,OACF,GAAdlT,KAAKsB,OACLtB,KAAK+gB,WACb,CACA,aAAO/R,CAAOuK,EAAOvY,EAAMuY,EAAM8C,WAAa9C,EAAMrG,OAAOjY,QACvD,OAAO,IAAI6lB,EAAkBvH,EAAOvY,EAAKA,EAAMuY,EAAM8C,WACzD,CACA,SAAA0E,GACI,IAAI7M,EAAOlU,KAAKuZ,MAAMtH,OACV,MAARiC,IACAlU,KAAKsB,MAAQtB,KAAKuZ,MAAM8C,WAAanI,EAAKmI,WAC1Crc,KAAKuZ,MAAQrF,EACblU,KAAKkT,OAASgB,EAAKhB,OAE3B,CACA,MAAI9d,GAAO,OAAO4K,KAAKkT,OAAOlT,KAAKsB,MAAQ,EAAI,CAC/C,SAAIuS,GAAU,OAAO7T,KAAKkT,OAAOlT,KAAKsB,MAAQ,EAAI,CAClD,OAAIwS,GAAQ,OAAO9T,KAAKkT,OAAOlT,KAAKsB,MAAQ,EAAI,CAChD,QAAIyS,GAAS,OAAO/T,KAAKkT,OAAOlT,KAAKsB,MAAQ,EAAI,CACjD,IAAA4S,GACIlU,KAAKsB,OAAS,EACdtB,KAAKgB,KAAO,EACM,GAAdhB,KAAKsB,OACLtB,KAAK+gB,WACb,CACA,IAAA1M,GACI,OAAO,IAAIyM,EAAkB9gB,KAAKuZ,MAAOvZ,KAAKgB,IAAKhB,KAAKsB,MAC5D,EAKJ,SAAS0f,EAAYzF,EAAO0F,EAAOrM,aAC/B,GAAoB,iBAAT2G,EACP,OAAOA,EACX,IAAI7R,EAAQ,KACZ,IAAK,IAAI1I,EAAM,EAAGkgB,EAAM,EAAGlgB,EAAMua,EAAMtgB,QAAS,CAC5C,IAAInD,EAAQ,EACZ,OAAS,CACL,IAAIoc,EAAOqH,EAAM4F,WAAWngB,KAAQogB,GAAO,EAC3C,GAAY,KAARlN,EAAqC,CACrCpc,EAAQ,MACR,KACJ,CACIoc,GAAQ,IACRA,IACAA,GAAQ,IACRA,IACJ,IAAImN,EAAQnN,EAAO,GAMnB,GALImN,GAAS,KACTA,GAAS,GACTD,GAAO,GAEXtpB,GAASupB,EACLD,EACA,MACJtpB,GAAS,EACb,CACI4R,EACAA,EAAMwX,KAASppB,EAEf4R,EAAQ,IAAIuX,EAAKnpB,EACzB,CACA,OAAO4R,CACX,CAEA,MAAM4X,EACF,WAAAlhB,GACIJ,KAAK6T,OAAS,EACd7T,KAAKlI,OAAS,EACdkI,KAAK8T,KAAO,EACZ9T,KAAKuhB,UAAY,EACjBvhB,KAAKyO,UAAY,EACjBzO,KAAKwhB,KAAO,EACZxhB,KAAK7H,QAAU,CACnB,EAEJ,MAAMspB,EAAY,IAAIH,EAOtB,MAAMI,EAIF,WAAAthB,CAIAmb,EAIAE,GACIzb,KAAKub,MAAQA,EACbvb,KAAKyb,OAASA,EAIdzb,KAAK8b,MAAQ,GAIb9b,KAAK2hB,SAAW,EAIhB3hB,KAAK4hB,OAAS,GACd5hB,KAAK6hB,UAAY,EAKjB7hB,KAAKkU,MAAQ,EAIblU,KAAK8hB,MAAQL,EACbzhB,KAAK+hB,WAAa,EAClB/hB,KAAKgB,IAAMhB,KAAKgiB,SAAWvG,EAAO,GAAGthB,KACrC6F,KAAKgJ,MAAQyS,EAAO,GACpBzb,KAAK8T,IAAM2H,EAAOA,EAAOxgB,OAAS,GAAGV,GACrCyF,KAAKiiB,UACT,CAIA,aAAAC,CAAcnH,EAAQoH,GAClB,IAAInZ,EAAQhJ,KAAKgJ,MAAO1H,EAAQtB,KAAK+hB,WACjC/gB,EAAMhB,KAAKgB,IAAM+Z,EACrB,KAAO/Z,EAAMgI,EAAM7O,MAAM,CACrB,IAAKmH,EACD,OAAO,KACX,IAAI4S,EAAOlU,KAAKyb,SAASna,GACzBN,GAAOgI,EAAM7O,KAAO+Z,EAAK3Z,GACzByO,EAAQkL,CACZ,CACA,KAAOiO,EAAQ,EAAInhB,EAAMgI,EAAMzO,GAAKyG,GAAOgI,EAAMzO,IAAI,CACjD,GAAI+G,GAAStB,KAAKyb,OAAOxgB,OAAS,EAC9B,OAAO,KACX,IAAIiZ,EAAOlU,KAAKyb,SAASna,GACzBN,GAAOkT,EAAK/Z,KAAO6O,EAAMzO,GACzByO,EAAQkL,CACZ,CACA,OAAOlT,CACX,CAIA,OAAAohB,CAAQphB,GACJ,GAAIA,GAAOhB,KAAKgJ,MAAM7O,MAAQ6G,EAAMhB,KAAKgJ,MAAMzO,GAC3C,OAAOyG,EACX,IAAK,IAAIgI,KAAShJ,KAAKyb,OACnB,GAAIzS,EAAMzO,GAAKyG,EACX,OAAOqW,KAAKC,IAAItW,EAAKgI,EAAM7O,MACnC,OAAO6F,KAAK8T,GAChB,CAYA,IAAAuO,CAAKtH,GACD,IAAkC/Z,EAAKkN,EAAnCoU,EAAMtiB,KAAK2hB,SAAW5G,EAC1B,GAAIuH,GAAO,GAAKA,EAAMtiB,KAAK8b,MAAM7gB,OAC7B+F,EAAMhB,KAAKgB,IAAM+Z,EACjB7M,EAASlO,KAAK8b,MAAMqF,WAAWmB,OAE9B,CACD,IAAIC,EAAWviB,KAAKkiB,cAAcnH,EAAQ,GAC1C,GAAgB,MAAZwH,EACA,OAAQ,EAEZ,GADAvhB,EAAMuhB,EACFvhB,GAAOhB,KAAK6hB,WAAa7gB,EAAMhB,KAAK6hB,UAAY7hB,KAAK4hB,OAAO3mB,OAC5DiT,EAASlO,KAAK4hB,OAAOT,WAAWngB,EAAMhB,KAAK6hB,eAE1C,CACD,IAAI3Z,EAAIlI,KAAK+hB,WAAY/Y,EAAQhJ,KAAKgJ,MACtC,KAAOA,EAAMzO,IAAMyG,GACfgI,EAAQhJ,KAAKyb,SAASvT,GAC1BlI,KAAK4hB,OAAS5hB,KAAKub,MAAMO,MAAM9b,KAAK6hB,UAAY7gB,GAC5CA,EAAMhB,KAAK4hB,OAAO3mB,OAAS+N,EAAMzO,KACjCyF,KAAK4hB,OAAS5hB,KAAK4hB,OAAO7K,MAAM,EAAG/N,EAAMzO,GAAKyG,IAClDkN,EAASlO,KAAK4hB,OAAOT,WAAW,EACpC,CACJ,CAGA,OAFIngB,GAAOhB,KAAK8hB,MAAMrT,YAClBzO,KAAK8hB,MAAMrT,UAAYzN,EAAM,GAC1BkN,CACX,CAMA,WAAAsU,CAAYV,EAAOW,EAAY,GAC3B,IAAI3O,EAAM2O,EAAYziB,KAAKkiB,cAAcO,GAAY,GAAKziB,KAAKgB,IAC/D,GAAW,MAAP8S,GAAeA,EAAM9T,KAAK8hB,MAAMjO,MAChC,MAAM,IAAI7F,WAAW,2BACzBhO,KAAK8hB,MAAMhqB,MAAQgqB,EACnB9hB,KAAK8hB,MAAMhO,IAAMA,CACrB,CAIA,aAAA4O,CAAcZ,EAAOjN,GACjB7U,KAAK8hB,MAAMhqB,MAAQgqB,EACnB9hB,KAAK8hB,MAAMhO,IAAMe,CACrB,CACA,QAAA8N,GACI,GAAI3iB,KAAKgB,KAAOhB,KAAK6hB,WAAa7hB,KAAKgB,IAAMhB,KAAK6hB,UAAY7hB,KAAK4hB,OAAO3mB,OAAQ,CAC9E,IAAI,MAAE6gB,EAAK,SAAEkG,GAAahiB,KAC1BA,KAAK8b,MAAQ9b,KAAK4hB,OAClB5hB,KAAKgiB,SAAWhiB,KAAK6hB,UACrB7hB,KAAK4hB,OAAS9F,EACd9b,KAAK6hB,UAAYG,EACjBhiB,KAAK2hB,SAAW3hB,KAAKgB,IAAMhB,KAAKgiB,QACpC,KACK,CACDhiB,KAAK4hB,OAAS5hB,KAAK8b,MACnB9b,KAAK6hB,UAAY7hB,KAAKgiB,SACtB,IAAIY,EAAY5iB,KAAKub,MAAMO,MAAM9b,KAAKgB,KAClC8S,EAAM9T,KAAKgB,IAAM4hB,EAAU3nB,OAC/B+E,KAAK8b,MAAQhI,EAAM9T,KAAKgJ,MAAMzO,GAAKqoB,EAAU7L,MAAM,EAAG/W,KAAKgJ,MAAMzO,GAAKyF,KAAKgB,KAAO4hB,EAClF5iB,KAAKgiB,SAAWhiB,KAAKgB,IACrBhB,KAAK2hB,SAAW,CACpB,CACJ,CACA,QAAAM,GACI,OAAIjiB,KAAK2hB,UAAY3hB,KAAK8b,MAAM7gB,SAC5B+E,KAAK2iB,WACD3iB,KAAK2hB,UAAY3hB,KAAK8b,MAAM7gB,QACrB+E,KAAKkU,MAAQ,EAErBlU,KAAKkU,KAAOlU,KAAK8b,MAAMqF,WAAWnhB,KAAK2hB,SAClD,CAKA,OAAA9F,CAAQnC,EAAI,GAER,IADA1Z,KAAK2hB,UAAYjI,EACV1Z,KAAKgB,IAAM0Y,GAAK1Z,KAAKgJ,MAAMzO,IAAI,CAClC,GAAIyF,KAAK+hB,YAAc/hB,KAAKyb,OAAOxgB,OAAS,EACxC,OAAO+E,KAAK6iB,UAChBnJ,GAAK1Z,KAAKgJ,MAAMzO,GAAKyF,KAAKgB,IAC1BhB,KAAKgJ,MAAQhJ,KAAKyb,SAASzb,KAAK+hB,YAChC/hB,KAAKgB,IAAMhB,KAAKgJ,MAAM7O,IAC1B,CAIA,OAHA6F,KAAKgB,KAAO0Y,EACR1Z,KAAKgB,KAAOhB,KAAK8hB,MAAMrT,YACvBzO,KAAK8hB,MAAMrT,UAAYzO,KAAKgB,IAAM,GAC/BhB,KAAKiiB,UAChB,CACA,OAAAY,GAII,OAHA7iB,KAAKgB,IAAMhB,KAAKgiB,SAAWhiB,KAAK8T,IAChC9T,KAAKgJ,MAAQhJ,KAAKyb,OAAOzb,KAAK+hB,WAAa/hB,KAAKyb,OAAOxgB,OAAS,GAChE+E,KAAK8b,MAAQ,GACN9b,KAAKkU,MAAQ,CACxB,CAIA,KAAAwK,CAAM1d,EAAK8gB,GAUP,GATIA,GACA9hB,KAAK8hB,MAAQA,EACbA,EAAMjO,MAAQ7S,EACd8gB,EAAMrT,UAAYzN,EAAM,EACxB8gB,EAAMhqB,MAAQgqB,EAAMP,UAAY,GAGhCvhB,KAAK8hB,MAAQL,EAEbzhB,KAAKgB,KAAOA,EAAK,CAEjB,GADAhB,KAAKgB,IAAMA,EACPA,GAAOhB,KAAK8T,IAEZ,OADA9T,KAAK6iB,UACE7iB,KAEX,KAAOgB,EAAMhB,KAAKgJ,MAAM7O,MACpB6F,KAAKgJ,MAAQhJ,KAAKyb,SAASzb,KAAK+hB,YACpC,KAAO/gB,GAAOhB,KAAKgJ,MAAMzO,IACrByF,KAAKgJ,MAAQhJ,KAAKyb,SAASzb,KAAK+hB,YAChC/gB,GAAOhB,KAAKgiB,UAAYhhB,EAAMhB,KAAKgiB,SAAWhiB,KAAK8b,MAAM7gB,OACzD+E,KAAK2hB,SAAW3gB,EAAMhB,KAAKgiB,UAG3BhiB,KAAK8b,MAAQ,GACb9b,KAAK2hB,SAAW,GAEpB3hB,KAAKiiB,UACT,CACA,OAAOjiB,IACX,CAIA,IAAAgc,CAAK7hB,EAAMI,GACP,GAAIJ,GAAQ6F,KAAKgiB,UAAYznB,GAAMyF,KAAKgiB,SAAWhiB,KAAK8b,MAAM7gB,OAC1D,OAAO+E,KAAK8b,MAAM/E,MAAM5c,EAAO6F,KAAKgiB,SAAUznB,EAAKyF,KAAKgiB,UAC5D,GAAI7nB,GAAQ6F,KAAK6hB,WAAatnB,GAAMyF,KAAK6hB,UAAY7hB,KAAK4hB,OAAO3mB,OAC7D,OAAO+E,KAAK4hB,OAAO7K,MAAM5c,EAAO6F,KAAK6hB,UAAWtnB,EAAKyF,KAAK6hB,WAC9D,GAAI1nB,GAAQ6F,KAAKgJ,MAAM7O,MAAQI,GAAMyF,KAAKgJ,MAAMzO,GAC5C,OAAOyF,KAAKub,MAAMS,KAAK7hB,EAAMI,GACjC,IAAI2T,EAAS,GACb,IAAK,IAAI0J,KAAK5X,KAAKyb,OAAQ,CACvB,GAAI7D,EAAEzd,MAAQI,EACV,MACAqd,EAAErd,GAAKJ,IACP+T,GAAUlO,KAAKub,MAAMS,KAAK3E,KAAKC,IAAIM,EAAEzd,KAAMA,GAAOkd,KAAKyL,IAAIlL,EAAErd,GAAIA,IACzE,CACA,OAAO2T,CACX,EAKJ,MAAM6U,EACF,WAAA3iB,CAAYb,EAAMnK,GACd4K,KAAKT,KAAOA,EACZS,KAAK5K,GAAKA,CACd,CACA,KAAA0sB,CAAMvG,EAAOhC,GACT,IAAI,OAAE/d,GAAW+d,EAAMtP,GA+E/B,SAAmB1K,EAAMgc,EAAOhC,EAAOjL,EAAO0U,EAAWC,GACrD,IAAI/G,EAAQ,EAAGgH,EAAY,GAAK5U,GAAO,QAAEgS,GAAY/G,EAAMtP,EAAEzO,OAC7DwW,EAAM,KACGkR,EAAY3jB,EAAK2c,IADX,CAGX,IAAIiH,EAAS5jB,EAAK2c,EAAQ,GAI1B,IAAK,IAAIhU,EAAIgU,EAAQ,EAAGhU,EAAIib,EAAQjb,GAAK,EACrC,IAAK3I,EAAK2I,EAAI,GAAKgb,GAAa,EAAG,CAC/B,IAAIvF,EAAOpe,EAAK2I,GAChB,GAAIoY,EAAQ8C,OAAOzF,MACQ,GAAtBpC,EAAMuG,MAAMhqB,OAAeyjB,EAAMuG,MAAMhqB,OAAS6lB,GAC7C0F,EAAU1F,EAAMpC,EAAMuG,MAAMhqB,MAAOkrB,EAAWC,IAAc,CAChE1H,EAAMiH,YAAY7E,GAClB,KACJ,CACJ,CACJ,IAAIzJ,EAAOqH,EAAMrH,KAAMoP,EAAM,EAAGC,EAAOhkB,EAAK2c,EAAQ,GAEpD,KAAIX,EAAMrH,KAAO,GAAKqP,EAAOD,GAAsC,OAA/B/jB,EAAK4jB,EAAgB,EAAPI,EAAW,IAA7D,CAKA,KAAOD,EAAMC,GAAO,CAChB,IAAIC,EAAOF,EAAMC,GAAS,EACtBjiB,EAAQ6hB,EAASK,GAAOA,GAAO,GAC/BrpB,EAAOoF,EAAK+B,GAAQ/G,EAAKgF,EAAK+B,EAAQ,IAAM,MAChD,GAAI4S,EAAO/Z,EACPopB,EAAOC,MACN,MAAItP,GAAQ3Z,GAEZ,CACD2hB,EAAQ3c,EAAK+B,EAAQ,GACrBia,EAAMM,UACN,SAAS7J,CACb,CALIsR,EAAME,EAAM,CAKhB,CACJ,CACA,KAhBA,CAFItH,EAAQ3c,EAAK4jB,EAAgB,EAAPI,EAAW,EAmBzC,CACJ,CAxHQE,CAAUzjB,KAAKT,KAAMgc,EAAOhC,EAAOvZ,KAAK5K,GAAIoG,EAAO+D,KAAM/D,EAAOkoB,eACpE,EAwHJ,SAASC,EAAWpkB,EAAMsU,EAAO8J,GAC7B,IAAK,IAAezJ,EAAXhM,EAAI2L,EAAiC,QAAnBK,EAAO3U,EAAK2I,IAA4BA,IAC/D,GAAIgM,GAAQyJ,EACR,OAAOzV,EAAI2L,EACnB,OAAQ,CACZ,CACA,SAASwP,EAAUvB,EAAO3H,EAAMyJ,EAAWC,GACvC,IAAIC,EAAQH,EAAWC,EAAWC,EAAa1J,GAC/C,OAAO2J,EAAQ,GAAKH,EAAWC,EAAWC,EAAa/B,GAASgC,CACpE,CA/HAf,EAAWgB,UAAUC,WAAajB,EAAWgB,UAAUruB,SAAWqtB,EAAWgB,UAAU5T,QAAS,EA+BzD4S,EAAWgB,UAAUruB,SAAWqtB,EAAWgB,UAAU5T,QAAS,EAmGrG,MAAM8T,EAA4B,oBAAXC,SAA0BA,QAAQC,KAAO,YAAYpT,KAAKmT,QAAQC,IAAIC,KAC7F,IAAIC,EAAW,KACf,SAASC,EAAM1V,EAAM5N,EAAKqQ,GACtB,IAAIL,EAASpC,EAAKoC,OAAOL,EAAS6B,kBAElC,IADAxB,EAAOO,OAAOvQ,KAEV,KAAMqQ,EAAO,EAAIL,EAAOiH,YAAYjX,GAAOgQ,EAAO9P,WAAWF,IACzD,OAAS,CACL,IAAKqQ,EAAO,EAAIL,EAAOzW,GAAKyG,EAAMgQ,EAAO7W,KAAO6G,KAASgQ,EAAOxY,KAAKiX,QACjE,OAAO4B,EAAO,EAAIgG,KAAKC,IAAI,EAAGD,KAAKyL,IAAI9R,EAAOzW,GAAK,EAAGyG,EAAM,KACtDqW,KAAKyL,IAAIlU,EAAK3T,OAAQoc,KAAKC,IAAItG,EAAO7W,KAAO,EAAG6G,EAAM,KAChE,GAAIqQ,EAAO,EAAIL,EAAOmH,cAAgBnH,EAAO2B,cACzC,MACJ,IAAK3B,EAAOiB,SACR,OAAOZ,EAAO,EAAI,EAAIzC,EAAK3T,MACnC,CAEZ,CACA,MAAM,EACF,WAAAmF,CAAYob,EAAWrI,GACnBnT,KAAKwb,UAAYA,EACjBxb,KAAKmT,QAAUA,EACfnT,KAAKkI,EAAI,EACTlI,KAAKukB,SAAW,KAChBvkB,KAAKwkB,UAAY,EACjBxkB,KAAKykB,QAAU,EACfzkB,KAAK0kB,MAAQ,GACb1kB,KAAK6T,MAAQ,GACb7T,KAAKsB,MAAQ,GACbtB,KAAK2kB,cACT,CACA,YAAAA,GACI,IAAIC,EAAK5kB,KAAKukB,SAAWvkB,KAAKkI,GAAKlI,KAAKwb,UAAUvgB,OAAS,KAAO+E,KAAKwb,UAAUxb,KAAKkI,KACtF,GAAI0c,EAAI,CAGJ,IAFA5kB,KAAKwkB,SAAWI,EAAGC,UAAYP,EAAMM,EAAGhW,KAAMgW,EAAGzqB,KAAOyqB,EAAG7J,OAAQ,GAAK6J,EAAG7J,OAAS6J,EAAGzqB,KACvF6F,KAAKykB,OAASG,EAAGE,QAAUR,EAAMM,EAAGhW,KAAMgW,EAAGrqB,GAAKqqB,EAAG7J,QAAS,GAAK6J,EAAG7J,OAAS6J,EAAGrqB,GAC3EyF,KAAK0kB,MAAMzpB,QACd+E,KAAK0kB,MAAMzO,MACXjW,KAAK6T,MAAMoC,MACXjW,KAAKsB,MAAM2U,MAEfjW,KAAK0kB,MAAM5oB,KAAK8oB,EAAGhW,MACnB5O,KAAK6T,MAAM/X,MAAM8oB,EAAG7J,QACpB/a,KAAKsB,MAAMxF,KAAK,GAChBkE,KAAKme,UAAYne,KAAKwkB,QAC1B,MAEIxkB,KAAKme,UAAY,GAEzB,CAEA,MAAA4G,CAAO/jB,GACH,GAAIA,EAAMhB,KAAKme,UACX,OAAO,KACX,KAAOne,KAAKukB,UAAYvkB,KAAKykB,QAAUzjB,GACnChB,KAAK2kB,eACT,IAAK3kB,KAAKukB,SACN,OAAO,KACX,OAAS,CACL,IAAI9O,EAAOzV,KAAK0kB,MAAMzpB,OAAS,EAC/B,GAAIwa,EAAO,EAEP,OADAzV,KAAK2kB,eACE,KAEX,IAAIvV,EAAMpP,KAAK0kB,MAAMjP,GAAOnU,EAAQtB,KAAKsB,MAAMmU,GAC/C,GAAInU,GAAS8N,EAAIyB,SAAS5V,OAAQ,CAC9B+E,KAAK0kB,MAAMzO,MACXjW,KAAK6T,MAAMoC,MACXjW,KAAKsB,MAAM2U,MACX,QACJ,CACA,IAAI/B,EAAO9E,EAAIyB,SAASvP,GACpBuS,EAAQ7T,KAAK6T,MAAM4B,GAAQrG,EAAIrO,UAAUO,GAC7C,GAAIuS,EAAQ7S,EAER,OADAhB,KAAKme,UAAYtK,EACV,KAEX,GAAIK,aAAgBtD,EAAM,CACtB,GAAIiD,GAAS7S,EAAK,CACd,GAAI6S,EAAQ7T,KAAKwkB,SACb,OAAO,KACX,IAAI1Q,EAAMD,EAAQK,EAAKjZ,OACvB,GAAI6Y,GAAO9T,KAAKykB,OAAQ,CACpB,IAAIhW,EAAYyF,EAAKxM,KAAKiG,EAASc,WACnC,IAAKA,GAAaqF,EAAMrF,EAAYzO,KAAKukB,SAAShqB,GAC9C,OAAO2Z,CACf,CACJ,CACAlU,KAAKsB,MAAMmU,KACP5B,EAAQK,EAAKjZ,QAAUoc,KAAKC,IAAItX,KAAKwkB,SAAUxjB,KAC/ChB,KAAK0kB,MAAM5oB,KAAKoY,GAChBlU,KAAK6T,MAAM/X,KAAK+X,GAChB7T,KAAKsB,MAAMxF,KAAK,GAExB,MAEIkE,KAAKsB,MAAMmU,KACXzV,KAAKme,UAAYtK,EAAQK,EAAKjZ,MAEtC,CACJ,EAEJ,MAAM+pB,EACF,WAAA5kB,CAAY5E,EAAQijB,GAChBze,KAAKye,OAASA,EACdze,KAAKilB,OAAS,GACdjlB,KAAKklB,UAAY,KACjBllB,KAAKmlB,QAAU,GACfnlB,KAAKilB,OAASzpB,EAAO4pB,WAAWvhB,KAAI0Y,GAAK,IAAI+E,GACjD,CACA,UAAA+D,CAAW9L,GACP,IAAI+L,EAAc,EACdC,EAAO,MACP,OAAE/pB,GAAW+d,EAAMtP,GAAG,WAAEmb,GAAe5pB,EACvCgmB,EAAOhmB,EAAOyjB,UAAU1F,EAAM2C,MAAO,GACrC/jB,EAAUohB,EAAM+C,WAAa/C,EAAM+C,WAAWkE,KAAO,EACrD/R,EAAY,EAChB,IAAK,IAAIvG,EAAI,EAAGA,EAAIkd,EAAWnqB,OAAQiN,IAAK,CACxC,KAAM,GAAKA,EAAKsZ,GACZ,SACJ,IAAIgE,EAAYJ,EAAWld,GAAI4Z,EAAQ9hB,KAAKilB,OAAO/c,GACnD,KAAIqd,GAASC,EAAU9vB,aAEnB8vB,EAAUxB,YAAclC,EAAMjO,OAAS0F,EAAMvY,KAAO8gB,EAAMN,MAAQA,GAAQM,EAAM3pB,SAAWA,KAC3F6H,KAAKylB,kBAAkB3D,EAAO0D,EAAWjM,GACzCuI,EAAMN,KAAOA,EACbM,EAAM3pB,QAAUA,GAEhB2pB,EAAMrT,UAAYqT,EAAMhO,IAAM,KAC9BrF,EAAY4I,KAAKC,IAAIwK,EAAMrT,UAAWA,IACvB,GAAfqT,EAAMhqB,OAA2B,CACjC,IAAIue,EAAaiP,EAIjB,GAHIxD,EAAMP,UAAY,IAClB+D,EAActlB,KAAK0lB,WAAWnM,EAAOuI,EAAMP,SAAUO,EAAMhO,IAAKwR,IACpEA,EAActlB,KAAK0lB,WAAWnM,EAAOuI,EAAMhqB,MAAOgqB,EAAMhO,IAAKwR,IACxDE,EAAUrV,SACXoV,EAAOzD,EACHwD,EAAcjP,GACd,KAEZ,CACJ,CACA,KAAOrW,KAAKmlB,QAAQlqB,OAASqqB,GACzBtlB,KAAKmlB,QAAQlP,MAUjB,OATIxH,GACA8K,EAAMuD,aAAarO,GAClB8W,GAAQhM,EAAMvY,KAAOhB,KAAKye,OAAO3K,MAClCyR,EAAO,IAAIjE,EACXiE,EAAKztB,MAAQyhB,EAAMtP,EAAEzO,OAAOmqB,QAC5BJ,EAAK1R,MAAQ0R,EAAKzR,IAAMyF,EAAMvY,IAC9BskB,EAActlB,KAAK0lB,WAAWnM,EAAOgM,EAAKztB,MAAOytB,EAAKzR,IAAKwR,IAE/DtlB,KAAKklB,UAAYK,EACVvlB,KAAKmlB,OAChB,CACA,YAAAS,CAAarM,GACT,GAAIvZ,KAAKklB,UACL,OAAOllB,KAAKklB,UAChB,IAAIK,EAAO,IAAIjE,GAAa,IAAEtgB,EAAG,EAAEiJ,GAAMsP,EAIzC,OAHAgM,EAAK1R,MAAQ7S,EACbukB,EAAKzR,IAAMuD,KAAKyL,IAAI9hB,EAAM,EAAGiJ,EAAEwU,OAAO3K,KACtCyR,EAAKztB,MAAQkJ,GAAOiJ,EAAEwU,OAAO3K,IAAM7J,EAAEzO,OAAOmqB,QAAU,EAC/CJ,CACX,CACA,iBAAAE,CAAkB3D,EAAO0D,EAAWjM,GAChC,IAAI1F,EAAQ7T,KAAKye,OAAO2D,QAAQ7I,EAAMvY,KAEtC,GADAwkB,EAAU1D,MAAM9hB,KAAKye,OAAOC,MAAM7K,EAAOiO,GAAQvI,GAC7CuI,EAAMhqB,OAAS,EAAG,CAClB,IAAI,OAAE0D,GAAW+d,EAAMtP,EACvB,IAAK,IAAI/B,EAAI,EAAGA,EAAI1M,EAAOqqB,YAAY5qB,OAAQiN,IAC3C,GAAI1M,EAAOqqB,YAAY3d,IAAM4Z,EAAMhqB,MAAO,CACtC,IAAIoW,EAAS1S,EAAOsqB,aAAa5d,GAAGlI,KAAKye,OAAOzC,KAAK8F,EAAMjO,MAAOiO,EAAMhO,KAAMyF,GAC9E,GAAIrL,GAAU,GAAKqL,EAAMtP,EAAEzO,OAAO8kB,QAAQ8C,OAAOlV,GAAU,GAAI,CAC7C,EAATA,EAGD4T,EAAMP,SAAWrT,GAAU,EAF3B4T,EAAMhqB,MAAQoW,GAAU,EAG5B,KACJ,CACJ,CACR,MAEI4T,EAAMhqB,MAAQ,EACdgqB,EAAMhO,IAAM9T,KAAKye,OAAO2D,QAAQvO,EAAQ,EAEhD,CACA,SAAAkS,CAAUnJ,EAAQkF,EAAOhO,EAAKxS,GAE1B,IAAK,IAAI4G,EAAI,EAAGA,EAAI5G,EAAO4G,GAAK,EAC5B,GAAIlI,KAAKmlB,QAAQjd,IAAM0U,EACnB,OAAOtb,EAIf,OAHAtB,KAAKmlB,QAAQ7jB,KAAWsb,EACxB5c,KAAKmlB,QAAQ7jB,KAAWwgB,EACxB9hB,KAAKmlB,QAAQ7jB,KAAWwS,EACjBxS,CACX,CACA,UAAAokB,CAAWnM,EAAOuI,EAAOhO,EAAKxS,GAC1B,IAAI,MAAE4a,GAAU3C,GAAO,OAAE/d,GAAW+d,EAAMtP,GAAG,KAAE1K,GAAS/D,EACxD,IAAK,IAAI2B,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAAI+K,EAAI1M,EAAOyjB,UAAU/C,EAAO/e,EAAM,EAA0B,IAA8B+K,GAAK,EAAG,CACvG,GAAe,OAAX3I,EAAK2I,GAA2B,CAChC,GAAmB,GAAf3I,EAAK2I,EAAI,GAGR,CACY,GAAT5G,GAA6B,GAAf/B,EAAK2I,EAAI,KACvB5G,EAAQtB,KAAK+lB,UAAU7P,GAAK3W,EAAM2I,EAAI,GAAI4Z,EAAOhO,EAAKxS,IAC1D,KACJ,CANI4G,EAAIgO,GAAK3W,EAAM2I,EAAI,EAO3B,CACI3I,EAAK2I,IAAM4Z,IACXxgB,EAAQtB,KAAK+lB,UAAU7P,GAAK3W,EAAM2I,EAAI,GAAI4Z,EAAOhO,EAAKxS,GAC9D,CAEJ,OAAOA,CACX,EAEJ,MAAM0kB,EACF,WAAA5lB,CAAY5E,EAAQ+f,EAAOC,EAAWC,GAClCzb,KAAKxE,OAASA,EACdwE,KAAKub,MAAQA,EACbvb,KAAKyb,OAASA,EACdzb,KAAKimB,WAAa,EAClBjmB,KAAKkmB,YAAc,KACnBlmB,KAAKmmB,YAAc,EACnBnmB,KAAKqT,OAAS,GACdrT,KAAKomB,UAAY,KACjBpmB,KAAKqd,uBAAyB,EAC9Brd,KAAKud,qBAAuB,EAC5Bvd,KAAKsd,kBAAoB,EACzBtd,KAAKye,OAAS,IAAIiD,EAAYnG,EAAOE,GACrCzb,KAAKilB,OAAS,IAAID,EAAWxpB,EAAQwE,KAAKye,QAC1Cze,KAAKqmB,QAAU7qB,EAAO4T,IAAI,GAC1B,IAAI,KAAEjV,GAASshB,EAAO,GACtBzb,KAAKsmB,OAAS,CAACrK,EAAMpI,MAAM7T,KAAMxE,EAAO4T,IAAI,GAAIjV,IAChD6F,KAAKwb,UAAYA,EAAUvgB,QAAU+E,KAAKye,OAAO3K,IAAM3Z,EAA6B,EAAtBqB,EAAO+qB,aAC/D,IAAI,EAAe/K,EAAWhgB,EAAO2X,SAAW,IAC1D,CACA,aAAIqT,GACA,OAAOxmB,KAAKmmB,WAChB,CAOA,OAAAtK,GACI,IAGI4K,EAASC,EAHTJ,EAAStmB,KAAKsmB,OAAQtlB,EAAMhB,KAAKmmB,YAEjCQ,EAAY3mB,KAAKsmB,OAAS,GAS9B,GAAItmB,KAAKsd,kBAAoB,KAAmE,GAAjBgJ,EAAOrrB,OAAa,CAC/F,IAAK2M,GAAK0e,EACV,KAAO1e,EAAE2X,eAAiB3X,EAAE2R,MAAMte,QAAU2M,EAAE2R,MAAM3R,EAAE2R,MAAMte,OAAS,IAAM+E,KAAKqd,wBAChFrd,KAAKsd,kBAAoBtd,KAAKud,qBAAuB,CACzD,CAIA,IAAK,IAAIrV,EAAI,EAAGA,EAAIoe,EAAOrrB,OAAQiN,IAAK,CACpC,IAAIqR,EAAQ+M,EAAOpe,GACnB,OAAS,CAEL,GADAlI,KAAKilB,OAAOC,UAAY,KACpB3L,EAAMvY,IAAMA,EACZ2lB,EAAU7qB,KAAKyd,OAEd,IAAIvZ,KAAK4mB,aAAarN,EAAOoN,EAAWL,GACzC,SAEC,CACIG,IACDA,EAAU,GACVC,EAAgB,IAEpBD,EAAQ3qB,KAAKyd,GACb,IAAIsN,EAAM7mB,KAAKilB,OAAOW,aAAarM,GACnCmN,EAAc5qB,KAAK+qB,EAAI/uB,MAAO+uB,EAAI/S,IACtC,EACA,KACJ,CACJ,CACA,IAAK6S,EAAU1rB,OAAQ,CACnB,IAAI6rB,EAAWL,GAuhB3B,SAAsBH,GAClB,IAAIjH,EAAO,KACX,IAAK,IAAI9F,KAAS+M,EAAQ,CACtB,IAAIG,EAAUlN,EAAMtP,EAAEmc,WACjB7M,EAAMvY,KAAOuY,EAAMtP,EAAEwU,OAAO3K,KAAkB,MAAX2S,GAAmBlN,EAAMvY,IAAMylB,IACnElN,EAAMtP,EAAEzO,OAAOiiB,UAAUlE,EAAM2C,MAAO,MACpCmD,GAAQA,EAAKjD,MAAQ7C,EAAM6C,SAC7BiD,EAAO9F,EACf,CACA,OAAO8F,CACX,CAjiBsC0H,CAAaN,GACvC,GAAIK,EAGA,OAFI7C,GACA3lB,QAAQC,IAAI,eAAiByB,KAAKgnB,QAAQF,IACvC9mB,KAAKinB,YAAYH,GAE5B,GAAI9mB,KAAKxE,OAAOolB,OAGZ,MAFIqD,GAAWwC,GACXnoB,QAAQC,IAAI,qBAAuByB,KAAKilB,OAAOC,UAAYllB,KAAKxE,OAAO0rB,QAAQlnB,KAAKilB,OAAOC,UAAUptB,OAAS,SAC5G,IAAIqvB,YAAY,eAAiBnmB,GAEtChB,KAAKimB,aACNjmB,KAAKimB,WAAa,EAC1B,CACA,GAAIjmB,KAAKimB,YAAcQ,EAAS,CAC5B,IAAIK,EAA6B,MAAlB9mB,KAAKomB,WAAqBK,EAAQ,GAAGzlB,IAAMhB,KAAKomB,UAAYK,EAAQ,GAC7EzmB,KAAKonB,YAAYX,EAASC,EAAeC,GAC/C,GAAIG,EAGA,OAFI7C,GACA3lB,QAAQC,IAAI,gBAAkByB,KAAKgnB,QAAQF,IACxC9mB,KAAKinB,YAAYH,EAAS/G,WAEzC,CACA,GAAI/f,KAAKimB,WAAY,CACjB,IAAIoB,EAAkC,GAAnBrnB,KAAKimB,WAAkB,EAAsB,EAAlBjmB,KAAKimB,WACnD,GAAIU,EAAU1rB,OAASosB,EAEnB,IADAV,EAAUjtB,MAAK,CAACC,EAAGud,IAAMA,EAAEkF,MAAQziB,EAAEyiB,QAC9BuK,EAAU1rB,OAASosB,GACtBV,EAAU1Q,MAEd0Q,EAAUrM,MAAK1S,GAAKA,EAAEuU,UAAYnb,KAClChB,KAAKimB,YACb,MACK,GAAIU,EAAU1rB,OAAS,EAAG,CAI3BqsB,EAAO,IAAK,IAAIpf,EAAI,EAAGA,EAAIye,EAAU1rB,OAAS,EAAGiN,IAAK,CAClD,IAAIqR,EAAQoN,EAAUze,GACtB,IAAK,IAAI6N,EAAI7N,EAAI,EAAG6N,EAAI4Q,EAAU1rB,OAAQ8a,IAAK,CAC3C,IAAIoK,EAAQwG,EAAU5Q,GACtB,GAAIwD,EAAM2G,UAAUC,IAChB5G,EAAMrG,OAAOjY,OAAS,KAAsCklB,EAAMjN,OAAOjY,OAAS,IAAoC,CACtH,MAAMse,EAAM6C,MAAQ+D,EAAM/D,OAAW7C,EAAMrG,OAAOjY,OAASklB,EAAMjN,OAAOjY,QAAW,GAG9E,CACD0rB,EAAUtN,OAAOnR,IAAK,GACtB,SAASof,CACb,CALIX,EAAUtN,OAAOtD,IAAK,EAM9B,CACJ,CACJ,CACI4Q,EAAU1rB,OAAS,IACnB0rB,EAAUtN,OAAO,GAA4BsN,EAAU1rB,OAAS,GACxE,CACA+E,KAAKmmB,YAAcQ,EAAU,GAAG3lB,IAChC,IAAK,IAAIkH,EAAI,EAAGA,EAAIye,EAAU1rB,OAAQiN,IAC9Bye,EAAUze,GAAGlH,IAAMhB,KAAKmmB,cACxBnmB,KAAKmmB,YAAcQ,EAAUze,GAAGlH,KACxC,OAAO,IACX,CACA,MAAA8U,CAAO9U,GACH,GAAsB,MAAlBhB,KAAKomB,WAAqBpmB,KAAKomB,UAAYplB,EAC3C,MAAM,IAAIgN,WAAW,gCACzBhO,KAAKomB,UAAYplB,CACrB,CAKA,YAAA4lB,CAAarN,EAAO+M,EAAQhtB,GACxB,IAAIua,EAAQ0F,EAAMvY,KAAK,OAAExF,GAAWwE,KAChCgW,EAAOiO,EAAUjkB,KAAKgnB,QAAQzN,GAAS,OAAS,GACpD,GAAsB,MAAlBvZ,KAAKomB,WAAqBvS,EAAQ7T,KAAKomB,UACvC,OAAO7M,EAAMgG,cAAgBhG,EAAQ,KACzC,GAAIvZ,KAAKwb,UAAW,CAChB,IAAI+L,EAAWhO,EAAM+C,YAAc/C,EAAM+C,WAAWiC,QAAQqC,OAAQ4G,EAASD,EAAWhO,EAAM+C,WAAWkE,KAAO,EAChH,IAAK,IAAIiH,EAASznB,KAAKwb,UAAUuJ,OAAOlR,GAAQ4T,GAAS,CACrD,IAAIzrB,EAAQgE,KAAKxE,OAAO2X,QAAQjD,MAAMuX,EAAOjvB,KAAKpD,KAAOqyB,EAAOjvB,KAAOgD,EAAOyhB,QAAQ1D,EAAM2C,MAAOuL,EAAOjvB,KAAKpD,KAAO,EACtH,GAAI4G,GAAS,GAAKyrB,EAAOxsB,UAAYssB,IAAaE,EAAO/f,KAAKiG,EAASa,cAAgB,IAAMgZ,GAIzF,OAHAjO,EAAM8E,QAAQoJ,EAAQzrB,GAClBioB,GACA3lB,QAAQC,IAAIyX,EAAOhW,KAAKgnB,QAAQzN,GAAS,kBAAkB/d,EAAO0rB,QAAQO,EAAOjvB,KAAKpD,SACnF,EAEX,KAAMqyB,aAAkB7W,IAAmC,GAA1B6W,EAAO5W,SAAS5V,QAAewsB,EAAO1mB,UAAU,GAAK,EAClF,MACJ,IAAI+Q,EAAQ2V,EAAO5W,SAAS,GAC5B,KAAIiB,aAAiBlB,GAA+B,GAAvB6W,EAAO1mB,UAAU,IAG1C,MAFA0mB,EAAS3V,CAGjB,CACJ,CACA,IAAI4V,EAAgBlsB,EAAOyjB,UAAU1F,EAAM2C,MAAO,GAClD,GAAIwL,EAAgB,EAIhB,OAHAnO,EAAMoD,OAAO+K,GACTzD,GACA3lB,QAAQC,IAAIyX,EAAOhW,KAAKgnB,QAAQzN,GAAS,uBAAuB/d,EAAO0rB,QAAwB,MAAhBQ,QAC5E,EAEX,GAAInO,EAAMA,MAAMte,QAAU,KACtB,KAAOse,EAAMA,MAAMte,OAAS,KAAwBse,EAAMgG,gBAE9D,IAAI4F,EAAUnlB,KAAKilB,OAAOI,WAAW9L,GACrC,IAAK,IAAIrR,EAAI,EAAGA,EAAIid,EAAQlqB,QAAS,CACjC,IAAI2hB,EAASuI,EAAQjd,KAAMyV,EAAOwH,EAAQjd,KAAM4L,EAAMqR,EAAQjd,KAC1DuN,EAAOvN,GAAKid,EAAQlqB,SAAW3B,EAC/BquB,EAAalS,EAAO8D,EAAQA,EAAMjgB,QAClCisB,EAAOvlB,KAAKilB,OAAOC,UAKvB,GAJAyC,EAAWzJ,MAAMtB,EAAQe,EAAM4H,EAAOA,EAAK1R,MAAQ8T,EAAW3mB,IAAK8S,GAC/DmQ,GACA3lB,QAAQC,IAAIyX,EAAOhW,KAAKgnB,QAAQW,GAAc,SAAmB,MAAT/K,EAClD,aAAaphB,EAAO0rB,QAAiB,MAATtK,KADqE,eACrBphB,EAAO0rB,QAAQvJ,QAAW9J,IAAQ8T,GAAcpO,EAAQ,GAAK,cAC/I9D,EACA,OAAO,EACFkS,EAAW3mB,IAAM6S,EACtByS,EAAOxqB,KAAK6rB,GAEZruB,EAAMwC,KAAK6rB,EACnB,CACA,OAAO,CACX,CAIA,YAAAC,CAAarO,EAAOoN,GAChB,IAAI3lB,EAAMuY,EAAMvY,IAChB,OAAS,CACL,IAAKhB,KAAK4mB,aAAarN,EAAO,KAAM,MAChC,OAAO,EACX,GAAIA,EAAMvY,IAAMA,EAEZ,OADA6mB,EAAetO,EAAOoN,IACf,CAEf,CACJ,CACA,WAAAS,CAAYd,EAAQrB,EAAQ0B,GACxB,IAAIG,EAAW,KAAMgB,GAAY,EACjC,IAAK,IAAI5f,EAAI,EAAGA,EAAIoe,EAAOrrB,OAAQiN,IAAK,CACpC,IAAIqR,EAAQ+M,EAAOpe,GAAI4Z,EAAQmD,EAAO/c,GAAK,GAAI6f,EAAW9C,EAAkB,GAAV/c,GAAK,IACnE8N,EAAOiO,EAAUjkB,KAAKgnB,QAAQzN,GAAS,OAAS,GACpD,GAAIA,EAAMyG,QAAS,CACf,GAAI8H,EACA,SAMJ,GALAA,GAAY,EACZvO,EAAM0G,UACFgE,GACA3lB,QAAQC,IAAIyX,EAAOhW,KAAKgnB,QAAQzN,GAAS,gBAClCvZ,KAAK4nB,aAAarO,EAAOoN,GAEhC,QACR,CACA,IAAIqB,EAAQzO,EAAMjgB,QAAS2uB,EAAYjS,EACvC,IAAK,IAAID,EAAI,EAAGiS,EAAMzI,eAAiBxJ,EAAI,KACnCkO,GACA3lB,QAAQC,IAAI0pB,EAAYjoB,KAAKgnB,QAAQgB,GAAS,wBACvChoB,KAAK4nB,aAAaI,EAAOrB,IAHkC5Q,IAMlEkO,IACAgE,EAAYjoB,KAAKgnB,QAAQgB,GAAS,QAE1C,IAAK,IAAIE,KAAU3O,EAAM4F,gBAAgB2C,GACjCmC,GACA3lB,QAAQC,IAAIyX,EAAOhW,KAAKgnB,QAAQkB,GAAU,yBAC9CloB,KAAK4nB,aAAaM,EAAQvB,GAE1B3mB,KAAKye,OAAO3K,IAAMyF,EAAMvY,KACpB+mB,GAAYxO,EAAMvY,MAClB+mB,IACAjG,EAAQ,GAEZvI,EAAMqF,gBAAgBkD,EAAOiG,GACzB9D,GACA3lB,QAAQC,IAAIyX,EAAOhW,KAAKgnB,QAAQzN,GAAS,wBAAwBvZ,KAAKxE,OAAO0rB,QAAQpF,OACzF+F,EAAetO,EAAOoN,MAEhBG,GAAYA,EAAS1K,MAAQ7C,EAAM6C,SACzC0K,EAAWvN,EAEnB,CACA,OAAOuN,CACX,CAEA,WAAAG,CAAY1N,GAER,OADAA,EAAMoH,QACC/P,EAAKoC,MAAM,CAAEE,OAAQ4N,EAAkB9R,OAAOuK,GACjDpG,QAASnT,KAAKxE,OAAO2X,QACrBmD,MAAOtW,KAAKqmB,QACZjT,gBAAiBpT,KAAKxE,OAAO+qB,aAC7BlT,OAAQrT,KAAKqT,OACbQ,MAAO7T,KAAKyb,OAAO,GAAGthB,KACtBc,OAAQse,EAAMvY,IAAMhB,KAAKyb,OAAO,GAAGthB,KACnCmZ,cAAetT,KAAKxE,OAAO0hB,eACnC,CACA,OAAA8J,CAAQzN,GACJ,IAAInkB,GAAMivB,IAAaA,EAAW,IAAI5T,UAAU3B,IAAIyK,GAGpD,OAFKnkB,GACDivB,EAASlnB,IAAIoc,EAAOnkB,EAAKwO,OAAOukB,cAAcnoB,KAAKkmB,gBAChD9wB,EAAKmkB,CAChB,EAEJ,SAASsO,EAAetO,EAAOoN,GAC3B,IAAK,IAAIze,EAAI,EAAGA,EAAIye,EAAU1rB,OAAQiN,IAAK,CACvC,IAAIiY,EAAQwG,EAAUze,GACtB,GAAIiY,EAAMnf,KAAOuY,EAAMvY,KAAOmf,EAAMD,UAAU3G,GAG1C,YAFIoN,EAAUze,GAAGkU,MAAQ7C,EAAM6C,QAC3BuK,EAAUze,GAAKqR,GAG3B,CACAoN,EAAU7qB,KAAKyd,EACnB,CACA,MAAM6O,EACF,WAAAhoB,CAAYkQ,EAAQrB,EAAOpZ,GACvBmK,KAAKsQ,OAASA,EACdtQ,KAAKiP,MAAQA,EACbjP,KAAKnK,SAAWA,CACpB,CACA,MAAAutB,CAAOzF,GAAQ,OAAQ3d,KAAKnK,UAAmC,GAAvBmK,KAAKnK,SAAS8nB,EAAY,EAiCtE,MAAM0K,UAAiBhN,EAInB,WAAAjb,CAAY+O,GAMR,GALAkJ,QAIArY,KAAKsoB,SAAW,GACI,IAAhBnZ,EAAKlR,QACL,MAAM,IAAI+P,WAAW,mBAAmBmB,EAAKlR,+CACjD,IAAIsqB,EAAYpZ,EAAKoZ,UAAUjvB,MAAM,KACrC0G,KAAKkd,cAAgBqL,EAAUttB,OAC/B,IAAK,IAAIiN,EAAI,EAAGA,EAAIiH,EAAKqZ,gBAAiBtgB,IACtCqgB,EAAUzsB,KAAK,IACnB,IAAI2sB,EAAWtpB,OAAOC,KAAK+P,EAAKuZ,UAAU7kB,KAAI+T,GAAKzI,EAAKuZ,SAAS9Q,GAAG,KAChE+Q,EAAY,GAChB,IAAK,IAAIzgB,EAAI,EAAGA,EAAIqgB,EAAUttB,OAAQiN,IAClCygB,EAAU7sB,KAAK,IACnB,SAAS8sB,EAAQC,EAAQnhB,EAAM5P,GAC3B6wB,EAAUE,GAAQ/sB,KAAK,CAAC4L,EAAMA,EAAKoG,YAAYlK,OAAO9L,KAC1D,CACA,GAAIqX,EAAKwZ,UACL,IAAK,IAAIG,KAAY3Z,EAAKwZ,UAAW,CACjC,IAAIjhB,EAAOohB,EAAS,GACD,iBAARphB,IACPA,EAAOiG,EAASjG,IACpB,IAAK,IAAIQ,EAAI,EAAGA,EAAI4gB,EAAS7tB,QAAS,CAClC,IAAIiZ,EAAO4U,EAAS5gB,KACpB,GAAIgM,GAAQ,EACR0U,EAAQ1U,EAAMxM,EAAMohB,EAAS5gB,UAE5B,CACD,IAAIpQ,EAAQgxB,EAAS5gB,GAAKgM,GAC1B,IAAK,IAAI6B,GAAK7B,EAAM6B,EAAI,EAAGA,IACvB6S,EAAQE,EAAS5gB,KAAMR,EAAM5P,GACjCoQ,GACJ,CACJ,CACJ,CACJlI,KAAKmT,QAAU,IAAIlD,EAAQsY,EAAU1kB,KAAI,CAACwD,EAAMa,IAAM+F,EAASiB,OAAO,CAClE7H,KAAMa,GAAKlI,KAAKkd,mBAAgBtc,EAAYyG,EAC5CjS,GAAI8S,EACJ1S,MAAOmzB,EAAUzgB,GACjBkH,IAAKqZ,EAAS7Y,QAAQ1H,IAAM,EAC5BvJ,MAAY,GAALuJ,EACPmH,QAASF,EAAK4Z,cAAgB5Z,EAAK4Z,aAAanZ,QAAQ1H,IAAM,OAE9DiH,EAAK6Z,cACLhpB,KAAKmT,QAAUnT,KAAKmT,QAAQhD,UAAUhB,EAAK6Z,cAC/ChpB,KAAK4gB,QAAS,EACd5gB,KAAKumB,aAAe/Y,EACpB,IAAIyb,EAAajI,EAAY7R,EAAK+Z,WAClClpB,KAAK7H,QAAUgX,EAAKhX,QACpB6H,KAAKmpB,iBAAmBha,EAAK0W,aAAe,GAC5C7lB,KAAK6lB,YAAc,IAAIjR,YAAY5U,KAAKmpB,iBAAiBluB,QACzD,IAAK,IAAIiN,EAAI,EAAGA,EAAIlI,KAAKmpB,iBAAiBluB,OAAQiN,IAC9ClI,KAAK6lB,YAAY3d,GAAKlI,KAAKmpB,iBAAiBjhB,GAAGyV,KACnD3d,KAAK8lB,aAAe9lB,KAAKmpB,iBAAiBtlB,IAAIulB,IAC9CppB,KAAKqpB,OAASrI,EAAY7R,EAAKka,OAAQC,aACvCtpB,KAAKT,KAAOyhB,EAAY7R,EAAKoa,WAC7BvpB,KAAK6gB,KAAOG,EAAY7R,EAAK0R,MAC7B7gB,KAAKwpB,QAAUra,EAAKqa,QACpBxpB,KAAKolB,WAAajW,EAAKiW,WAAWvhB,KAAI/L,GAAyB,iBAATA,EAAoB,IAAIirB,EAAWkG,EAAYnxB,GAASA,IAC9GkI,KAAK0oB,SAAWvZ,EAAKuZ,SACrB1oB,KAAKypB,SAAWta,EAAKsa,UAAY,CAAC,EAClCzpB,KAAK0pB,mBAAqBva,EAAKua,oBAAsB,KACrD1pB,KAAK0jB,eAAiBvU,EAAKwa,UAC3B3pB,KAAK4pB,UAAYza,EAAKya,WAAa,KACnC5pB,KAAKge,QAAUhe,KAAKmT,QAAQjD,MAAMjV,OAAS,EAC3C+E,KAAKsgB,QAAUtgB,KAAK6pB,eACpB7pB,KAAKoP,IAAMpP,KAAK0oB,SAASvpB,OAAOC,KAAKY,KAAK0oB,UAAU,GACxD,CACA,WAAA/M,CAAYJ,EAAOC,EAAWC,GAC1B,IAAIhb,EAAQ,IAAIulB,EAAMhmB,KAAMub,EAAOC,EAAWC,GAC9C,IAAK,IAAIqO,KAAK9pB,KAAKsoB,SACf7nB,EAAQqpB,EAAErpB,EAAO8a,EAAOC,EAAWC,GACvC,OAAOhb,CACX,CAIA,OAAAwc,CAAQf,EAAOyB,EAAMoM,GAAQ,GACzB,IAAIC,EAAQhqB,KAAK6gB,KACjB,GAAIlD,GAAQqM,EAAM,GACd,OAAQ,EACZ,IAAK,IAAIhpB,EAAMgpB,EAAMrM,EAAO,KAAM,CAC9B,IAAIsM,EAAWD,EAAMhpB,KAAQyU,EAAkB,EAAXwU,EAChC3xB,EAAS0xB,EAAMhpB,KACnB,GAAIyU,GAAQsU,EACR,OAAOzxB,EACX,IAAK,IAAIwb,EAAM9S,GAAOipB,GAAY,GAAIjpB,EAAM8S,EAAK9S,IAC7C,GAAIgpB,EAAMhpB,IAAQkb,EACd,OAAO5jB,EACf,GAAImd,EACA,OAAQ,CAChB,CACJ,CAIA,SAAAyJ,CAAUhD,EAAOgO,GACb,IAAI3qB,EAAOS,KAAKT,KAChB,IAAK,IAAIpC,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAA2F+W,EAAvFhM,EAAIlI,KAAKif,UAAU/C,EAAO/e,EAAM,EAA0B,IAAoC+K,GAAK,EAAG,CAC3G,GAAwB,QAAnBgM,EAAO3U,EAAK2I,IAA4B,CACzC,GAAmB,GAAf3I,EAAK2I,EAAI,GAER,IAAmB,GAAf3I,EAAK2I,EAAI,GACd,OAAOgO,GAAK3W,EAAM2I,EAAI,GAEtB,KAAK,CAJLgM,EAAO3U,EAAK2I,EAAIgO,GAAK3W,EAAM2I,EAAI,GAKvC,CACA,GAAIgM,GAAQgW,GAAoB,GAARhW,EACpB,OAAOgC,GAAK3W,EAAM2I,EAAI,EAC9B,CAEJ,OAAO,CACX,CAIA,SAAA+W,CAAU/C,EAAOiO,GACb,OAAOnqB,KAAKqpB,OAAgB,EAARnN,EAAmCiO,EAC3D,CAIA,SAAA1M,CAAUvB,EAAOkO,GACb,OAAQpqB,KAAKif,UAAU/C,EAAO,GAA4BkO,GAAQ,CACtE,CAIA,WAAA5K,CAAYtD,EAAOU,GACf,QAAS5c,KAAK6f,WAAW3D,GAAOviB,GAAKA,GAAKijB,GAAgB,MAC9D,CAIA,UAAAiD,CAAW3D,EAAOU,GACd,IAAIyN,EAAQrqB,KAAKif,UAAU/C,EAAO,GAC9BhO,EAASmc,EAAQzN,EAAOyN,QAASzpB,EACrC,IAAK,IAAIsH,EAAIlI,KAAKif,UAAU/C,EAAO,GAAuC,MAAVhO,EAAgBhG,GAAK,EAAG,CACpF,GAAoB,OAAhBlI,KAAKT,KAAK2I,GAA2B,CACrC,GAAwB,GAApBlI,KAAKT,KAAK2I,EAAI,GAGd,MAFAA,EAAIgO,GAAKlW,KAAKT,KAAM2I,EAAI,EAGhC,CACAgG,EAAS0O,EAAO1G,GAAKlW,KAAKT,KAAM2I,EAAI,GACxC,CACA,OAAOgG,CACX,CAKA,UAAAkR,CAAWlD,GACP,IAAIhO,EAAS,GACb,IAAK,IAAIhG,EAAIlI,KAAKif,UAAU/C,EAAO,IAA8BhU,GAAK,EAAG,CACrE,GAAoB,OAAhBlI,KAAKT,KAAK2I,GAA2B,CACrC,GAAwB,GAApBlI,KAAKT,KAAK2I,EAAI,GAGd,MAFAA,EAAIgO,GAAKlW,KAAKT,KAAM2I,EAAI,EAGhC,CACA,KAAwB,EAAnBlI,KAAKT,KAAK2I,EAAI,IAAkD,CACjE,IAAIpQ,EAAQkI,KAAKT,KAAK2I,EAAI,GACrBgG,EAAOoM,MAAK,CAACgF,EAAGpX,IAAW,EAAJA,GAAUoX,GAAKxnB,KACvCoW,EAAOpS,KAAKkE,KAAKT,KAAK2I,GAAIpQ,EAClC,CACJ,CACA,OAAOoW,CACX,CAMA,SAAAxW,CAAUkW,GAGN,IAAIuJ,EAAOhY,OAAOoR,OAAOpR,OAAO6P,OAAOqZ,EAAStE,WAAY/jB,MAG5D,GAFI4N,EAAOpY,QACP2hB,EAAKhE,QAAUnT,KAAKmT,QAAQhD,UAAUvC,EAAOpY,QAC7CoY,EAAOwB,IAAK,CACZ,IAAIjR,EAAO6B,KAAK0oB,SAAS9a,EAAOwB,KAChC,IAAKjR,EACD,MAAM,IAAI6P,WAAW,yBAAyBJ,EAAOwB,OACzD+H,EAAK/H,IAAMjR,CACf,CA2BA,OA1BIyP,EAAOwX,aACPjO,EAAKiO,WAAaplB,KAAKolB,WAAWvhB,KAAIymB,IAClC,IAAIva,EAAQnC,EAAOwX,WAAW/sB,MAAKuf,GAAKA,EAAEzd,MAAQmwB,IAClD,OAAOva,EAAQA,EAAMxV,GAAK+vB,CAAC,KAE/B1c,EAAOkY,eACP3O,EAAK2O,aAAe9lB,KAAK8lB,aAAa/O,QACtCI,EAAKgS,iBAAmBnpB,KAAKmpB,iBAAiBtlB,KAAI,CAAC+D,EAAGM,KAClD,IAAI6H,EAAQnC,EAAOkY,aAAaztB,MAAKuf,GAAKA,EAAEzd,MAAQyN,EAAE2iB,WACtD,IAAKxa,EACD,OAAOnI,EACX,IAAIuH,EAAOhQ,OAAOoR,OAAOpR,OAAOoR,OAAO,CAAC,EAAG3I,GAAI,CAAE2iB,SAAUxa,EAAMxV,KAEjE,OADA4c,EAAK2O,aAAa5d,GAAKkhB,GAAeja,GAC/BA,CAAI,KAGfvB,EAAO4c,iBACPrT,EAAKhf,QAAUyV,EAAO4c,gBACtB5c,EAAO0S,UACPnJ,EAAKmJ,QAAUtgB,KAAK6pB,aAAajc,EAAO0S,UACvB,MAAjB1S,EAAOgT,SACPzJ,EAAKyJ,OAAShT,EAAOgT,QACrBhT,EAAO6c,OACPtT,EAAKmR,SAAWnR,EAAKmR,SAASnS,OAAOvI,EAAO6c,OACrB,MAAvB7c,EAAO2Y,eACPpP,EAAKoP,aAAe3Y,EAAO2Y,cACxBpP,CACX,CAKA,WAAAuT,GACI,OAAO1qB,KAAKsoB,SAASrtB,OAAS,CAClC,CAOA,OAAAisB,CAAQvJ,GACJ,OAAO3d,KAAK4pB,UAAY5pB,KAAK4pB,UAAUjM,GAAQ/Z,OAAO+Z,GAAQ3d,KAAKge,SAAWhe,KAAKmT,QAAQjD,MAAMyN,GAAMtW,MAAQsW,EACnH,CAKA,WAAIgI,GAAY,OAAO3lB,KAAKge,QAAU,CAAG,CAIzC,WAAI7M,GAAY,OAAOnR,KAAKmT,QAAQjD,MAAMlQ,KAAKoP,IAAI,GAAK,CAIxD,iBAAA4N,CAAkBW,GACd,IAAIgN,EAAO3qB,KAAK0pB,mBAChB,OAAe,MAARiB,EAAe,EAAIA,EAAKhN,IAAS,CAC5C,CAIA,YAAAkM,CAAavJ,GACT,IAAIhZ,EAASnI,OAAOC,KAAKY,KAAKypB,UAAWxa,EAAQ3H,EAAOzD,KAAI,KAAM,IAClE,GAAIyc,EACA,IAAK,IAAIsK,KAAQtK,EAAQhnB,MAAM,KAAM,CACjC,IAAIlE,EAAKkS,EAAOsI,QAAQgb,GACpBx1B,GAAM,IACN6Z,EAAM7Z,IAAM,EACpB,CACJ,IAAIS,EAAW,KACf,IAAK,IAAIqS,EAAI,EAAGA,EAAIZ,EAAOrM,OAAQiN,IAC/B,IAAK+G,EAAM/G,GACP,IAAK,IAAkC9S,EAA9B2gB,EAAI/V,KAAKypB,SAASniB,EAAOY,IAAkC,QAAxB9S,EAAK4K,KAAKT,KAAKwW,QACtDlgB,IAAaA,EAAW,IAAIg1B,WAAW7qB,KAAKwpB,QAAU,KAAKp0B,GAAM,EAE9E,OAAO,IAAIgzB,EAAQ9H,EAASrR,EAAOpZ,EACvC,CAKA,kBAAOiY,CAAYqB,GACf,OAAO,IAAIkZ,EAASlZ,EACxB,EAEJ,SAAS+G,GAAK3W,EAAMof,GAAO,OAAOpf,EAAKof,GAAQpf,EAAKof,EAAM,IAAM,EAAK,CAYrE,SAASyK,GAAeja,GACpB,GAAIA,EAAKob,SAAU,CACf,IAAI/I,EAAOrS,EAAKgB,OAAS,EAA4B,EACrD,MAAO,CAACrY,EAAOyhB,IAAWpK,EAAKob,SAASzyB,EAAOyhB,IAAU,EAAKiI,CAClE,CACA,OAAOrS,EAAKL,GAChB,CCr1DA,MAoCMgc,GAAgB,CACpBC,KArCa,EAsCbC,OArCW,EAsCXC,OArCW,EAsCXhvB,QArCY,EAsCZivB,OArCW,EAsCXC,aApCgB,EAqChBC,YApCe,EAqCfC,cApCiB,EAqCjBC,OApCW,GAqCXvQ,OApCW,GAqCXwQ,KApCS,GAqCTC,GApCO,GAqCPC,SApCa,GAqCbC,WApCc,GAqCdC,YApCe,GAqCfC,OA/CW,EAgDXC,WArCe,GAsCfC,KArCS,GAsCTC,KArCS,IA4CLC,GAA0B,CAC9BC,GA5CO,GA6CPC,QA5CY,GA6CZC,IA5CQ,GA6CRC,GA5CO,GA6CPC,OA5CW,GA6CXC,IA5CQ,GA6CRC,IA5CQ,GA6CR/O,MA5CU,GA6CVlG,IA5CQ,GA6CRwL,IA5CQ,GA6CR0J,OA5CW,GA6CXC,OA5CW,GA6CXC,QA5CY,GA6CZC,KA5CS,GA6CTjzB,KA5CS,GA6CTkzB,UA5Cc,IAoDVC,GAAkB,CAACC,UAAU,KAAKC,GAAG,IAAKC,gBAAgB,IAAKC,KAAK,IAAKC,aAAa,IAAKC,gBAAgB,IAAKC,WAAW,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,iBAAiB,IAAKC,iBAAiB,IAAKC,mBAAmB,IAAKC,gBAAgB,IAAKC,eAAe,IAAKC,iBAAiB,IAAKC,MAAM,IAAKC,SAAS,IAAKC,iBAAiB,KACzXzyB,GAAS6sB,EAASva,YAAY,CAClC7P,QAAS,GACTorB,OAAQ,ygGACRE,UAAW,+9KACX1I,KAAM,wvCACN0H,UAAW,k4CACXiB,QAAS,IACTT,aAAc,CAAC,EAAE,IACjBP,gBAAiB,EACjBU,UAAW,4tEACX9D,WAAY,CAAC,EAAG,GAChBsD,SAAU,CAAC,MAAQ,CAAC,EAAE,KACtB7C,YAAa,CAAC,CAAClI,KAAM,GAAI7O,IAAK,CAAChX,EAAOyhB,IAzCX,CAACzhB,GACrBgzB,GAAchzB,EAAMo2B,iBAAmB,EAwCGC,CAAqBr2B,IAAU,GAAI,CAAC6lB,KAAM,GAAI7O,IAAK,CAAChX,EAAOyhB,IAlBrF,CAACzhB,GACjBk0B,GAAwBl0B,EAAMo2B,iBAAmB,EAiB+DE,CAAiBt2B,IAAU,EAAK,GAAG,CAAC6lB,KAAM,GAAI7O,IAAKhX,GAAS+0B,GAAgB/0B,KAAW,IAC9M6xB,UAAW,IAGP7jB,GAAO,EACXF,GAAS,EAsCTtC,GAAW,GAEXE,GAAU,GACVG,GAAa,GACbd,GAAK,GACL,GAAS,GACTE,GAAM,GACNE,GAAK,GACLE,GAAM,GAINa,GAAa,GAEbG,GAAY,GACZE,GAAY,GACZI,GAAc,GACdE,GAAM,GACN/G,GAAW,GAEXqJ,GAAW,GAUXxB,GAAc,GAId/C,GAAM,GACN4D,GAAW,GACX9D,GAAM,GACNF,GAAM,GACNH,GAAM,GAGNiE,GAAQ,GAGR,GAAS,GAcTioB,GAAa,E,sEC1MXC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqB5tB,IAAjB6tB,EACH,OAAOA,EAAa7hB,QAGrB,IAAID,EAAS2hB,EAAyBE,GAAY,CAGjD5hB,QAAS,CAAC,GAOX,OAHA8hB,EAAoBF,GAAUG,KAAKhiB,EAAOC,QAASD,EAAQA,EAAOC,QAAS2hB,GAGpE5hB,EAAOC,OACf,C,OAGA2hB,EAAoBK,EAAIF,ECxBxBH,EAAoB7U,EAAK/M,IACxB,IAAIkiB,EAASliB,GAAUA,EAAOmiB,WAC7B,IAAOniB,EAAiB,QACxB,IAAM,EAEP,OADA4hB,EAAoBvU,EAAE6U,EAAQ,CAAEl1B,EAAGk1B,IAC5BA,CAAM,EhCNV/5B,EAAWqK,OAAO4vB,eAAkBlnB,GAAS1I,OAAO4vB,eAAelnB,GAASA,GAASA,EAAa,UAQtG0mB,EAAoBjE,EAAI,SAASxyB,EAAOmZ,GAEvC,GADU,EAAPA,IAAUnZ,EAAQkI,KAAKlI,IAChB,EAAPmZ,EAAU,OAAOnZ,EACpB,GAAoB,iBAAVA,GAAsBA,EAAO,CACtC,GAAW,EAAPmZ,GAAanZ,EAAMg3B,WAAY,OAAOh3B,EAC1C,GAAW,GAAPmZ,GAAoC,mBAAfnZ,EAAMk3B,KAAqB,OAAOl3B,CAC5D,CACA,IAAIm3B,EAAK9vB,OAAO6P,OAAO,MACvBuf,EAAoB3W,EAAEqX,GACtB,IAAIC,EAAM,CAAC,EACXr6B,EAAiBA,GAAkB,CAAC,KAAMC,EAAS,CAAC,GAAIA,EAAS,IAAKA,EAASA,IAC/E,IAAI,IAAIq6B,EAAiB,EAAPle,GAAYnZ,EAAyB,iBAAXq3B,KAAyBt6B,EAAe+a,QAAQuf,GAAUA,EAAUr6B,EAASq6B,GACxHhwB,OAAOiwB,oBAAoBD,GAAS9vB,SAAS7F,GAAS01B,EAAI11B,GAAO,IAAO1B,EAAM0B,KAI/E,OAFA01B,EAAa,QAAI,IAAM,EACvBX,EAAoBvU,EAAEiV,EAAIC,GACnBD,CACR,EiCxBAV,EAAoBvU,EAAI,CAACpN,EAASyiB,KACjC,IAAI,IAAI71B,KAAO61B,EACXd,EAAoB/mB,EAAE6nB,EAAY71B,KAAS+0B,EAAoB/mB,EAAEoF,EAASpT,IAC5E2F,OAAOmwB,eAAe1iB,EAASpT,EAAK,CAAE+1B,YAAY,EAAMzgB,IAAKugB,EAAW71B,IAE1E,ECND+0B,EAAoBiB,EAAI,CAAC,EAGzBjB,EAAoBxvB,EAAK0wB,GACjBr5B,QAAQC,IAAI8I,OAAOC,KAAKmvB,EAAoBiB,GAAG7S,QAAO,CAAC+S,EAAUl2B,KACvE+0B,EAAoBiB,EAAEh2B,GAAKi2B,EAASC,GAC7BA,IACL,KCNJnB,EAAoBoB,EAAKF,GAEZA,EAAU,MCHvBlB,EAAoBqB,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO7vB,MAAQ,IAAI8vB,SAAS,cAAb,EAChB,CAAE,MAAO/wB,GACR,GAAsB,iBAAXgxB,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBxB,EAAoB/mB,EAAI,CAACK,EAAKH,IAAUvI,OAAO4kB,UAAUiM,eAAerB,KAAK9mB,EAAKH,GpCA9E3S,EAAa,CAAC,EACdC,EAAoB,2BAExBu5B,EAAoB0B,EAAI,CAACC,EAAKtU,EAAMpiB,EAAKi2B,KACxC,GAAG16B,EAAWm7B,GAAQn7B,EAAWm7B,GAAKp0B,KAAK8f,OAA3C,CACA,IAAIuU,EAAQC,EACZ,QAAWxvB,IAARpH,EAEF,IADA,IAAI62B,EAAUC,SAASC,qBAAqB,UACpCroB,EAAI,EAAGA,EAAImoB,EAAQp1B,OAAQiN,IAAK,CACvC,IAAIN,EAAIyoB,EAAQnoB,GAChB,GAAGN,EAAE4oB,aAAa,QAAUN,GAAOtoB,EAAE4oB,aAAa,iBAAmBx7B,EAAoBwE,EAAK,CAAE22B,EAASvoB,EAAG,KAAO,CACpH,CAEGuoB,IACHC,GAAa,GACbD,EAASG,SAASG,cAAc,WAEzBC,QAAU,QACjBP,EAAOQ,QAAU,IACbpC,EAAoBqC,IACvBT,EAAOU,aAAa,QAAStC,EAAoBqC,IAElDT,EAAOU,aAAa,eAAgB77B,EAAoBwE,GAExD22B,EAAO7gB,IAAM4gB,GAEdn7B,EAAWm7B,GAAO,CAACtU,GACnB,IAAIkV,EAAmB,CAAC3W,EAAM4W,KAE7BZ,EAAOa,QAAUb,EAAOc,OAAS,KACjCC,aAAaP,GACb,IAAIQ,EAAUp8B,EAAWm7B,GAIzB,UAHOn7B,EAAWm7B,GAClBC,EAAOiB,YAAcjB,EAAOiB,WAAWC,YAAYlB,GACnDgB,GAAWA,EAAQ9xB,SAASiyB,GAAQA,EAAGP,KACpC5W,EAAM,OAAOA,EAAK4W,EAAM,EAExBJ,EAAUY,WAAWT,EAAiBU,KAAK,UAAM5wB,EAAW,CAAEpI,KAAM,UAAWF,OAAQ63B,IAAW,MACtGA,EAAOa,QAAUF,EAAiBU,KAAK,KAAMrB,EAAOa,SACpDb,EAAOc,OAASH,EAAiBU,KAAK,KAAMrB,EAAOc,QACnDb,GAAcE,SAASmB,KAAKC,YAAYvB,EApCkB,CAoCX,EqCvChD5B,EAAoB3W,EAAKhL,IACH,oBAAXrP,QAA0BA,OAAOo0B,aAC1CxyB,OAAOmwB,eAAe1iB,EAASrP,OAAOo0B,YAAa,CAAE75B,MAAO,WAE7DqH,OAAOmwB,eAAe1iB,EAAS,aAAc,CAAE9U,OAAO,GAAO,ECL9Dy2B,EAAoBtkB,EAAI,0C,MCAxBskB,EAAoBrX,EAAIoZ,SAASsB,SAAWC,KAAK50B,SAAS60B,KAK1D,IAAIC,EAAkB,CACrB,IAAK,GAGNxD,EAAoBiB,EAAEzZ,EAAI,CAAC0Z,EAASC,KAElC,IAAIsC,EAAqBzD,EAAoB/mB,EAAEuqB,EAAiBtC,GAAWsC,EAAgBtC,QAAW7uB,EACtG,GAA0B,IAAvBoxB,EAGF,GAAGA,EACFtC,EAAS5zB,KAAKk2B,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAI77B,SAAQ,CAACsb,EAASwgB,IAAYF,EAAqBD,EAAgBtC,GAAW,CAAC/d,EAASwgB,KAC1GxC,EAAS5zB,KAAKk2B,EAAmB,GAAKC,GAGtC,IAAI/B,EAAM3B,EAAoBtkB,EAAIskB,EAAoBoB,EAAEF,GAEpD9wB,EAAQ,IAAIc,MAgBhB8uB,EAAoB0B,EAAEC,GAfFa,IACnB,GAAGxC,EAAoB/mB,EAAEuqB,EAAiBtC,KAEf,KAD1BuC,EAAqBD,EAAgBtC,MACRsC,EAAgBtC,QAAW7uB,GACrDoxB,GAAoB,CACtB,IAAIG,EAAYpB,IAAyB,SAAfA,EAAMv4B,KAAkB,UAAYu4B,EAAMv4B,MAChE45B,EAAUrB,GAASA,EAAMz4B,QAAUy4B,EAAMz4B,OAAOgX,IACpD3Q,EAAM0zB,QAAU,iBAAmB5C,EAAU,cAAgB0C,EAAY,KAAOC,EAAU,IAC1FzzB,EAAM0I,KAAO,iBACb1I,EAAMnG,KAAO25B,EACbxzB,EAAM2zB,QAAUF,EAChBJ,EAAmB,GAAGrzB,EACvB,CACD,GAEwC,SAAW8wB,EAASA,EAE/D,CACD,EAcF,IAAI8C,EAAuB,CAACC,EAA4BjzB,KACvD,IAGIivB,EAAUiB,GAHTgD,EAAUC,EAAaC,GAAWpzB,EAGhB2I,EAAI,EAC3B,GAAGuqB,EAASnY,MAAMllB,GAAgC,IAAxB28B,EAAgB38B,KAAa,CACtD,IAAIo5B,KAAYkE,EACZnE,EAAoB/mB,EAAEkrB,EAAalE,KACrCD,EAAoBK,EAAEJ,GAAYkE,EAAYlE,IAG7CmE,GAAsBA,EAAQpE,EAClC,CAEA,IADGiE,GAA4BA,EAA2BjzB,GACrD2I,EAAIuqB,EAASx3B,OAAQiN,IACzBunB,EAAUgD,EAASvqB,GAChBqmB,EAAoB/mB,EAAEuqB,EAAiBtC,IAAYsC,EAAgBtC,IACrEsC,EAAgBtC,GAAS,KAE1BsC,EAAgBtC,GAAW,CAC5B,EAIGmD,EAAqBf,KAA0C,oCAAIA,KAA0C,qCAAK,GACtHe,EAAmBvzB,QAAQkzB,EAAqBf,KAAK,KAAM,IAC3DoB,EAAmB92B,KAAOy2B,EAAqBf,KAAK,KAAMoB,EAAmB92B,KAAK01B,KAAKoB,G,KClF7DrE,EAAoB,K", "sources": ["webpack://grafana-lokiexplore-app/webpack/runtime/create fake namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/load script", "webpack://grafana-lokiexplore-app/./services/extensions/exposedComponents.tsx", "webpack://grafana-lokiexplore-app/./module.tsx", "webpack://grafana-lokiexplore-app/./services/extensions/links.ts", "webpack://grafana-lokiexplore-app/./services/fieldsTypes.ts", "webpack://grafana-lokiexplore-app/./services/filterTypes.ts", "webpack://grafana-lokiexplore-app/./services/logger.ts", "webpack://grafana-lokiexplore-app/./services/logqlMatchers.ts", "webpack://grafana-lokiexplore-app/./services/lokiQuery.ts", "webpack://grafana-lokiexplore-app/./services/narrowing.ts", "webpack://grafana-lokiexplore-app/./services/operatorHelpers.ts", "webpack://grafana-lokiexplore-app/./services/getOperatorDescription.ts", "webpack://grafana-lokiexplore-app/./services/operators.ts", "webpack://grafana-lokiexplore-app/./services/extensions/scenesMethods.ts", "webpack://grafana-lokiexplore-app/./services/renderPatternFilters.ts", "webpack://grafana-lokiexplore-app/./services/variables.ts", "webpack://grafana-lokiexplore-app/external amd \"@emotion/css\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/data\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/runtime\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/ui\"", "webpack://grafana-lokiexplore-app/external amd \"lodash\"", "webpack://grafana-lokiexplore-app/external amd \"react\"", "webpack://grafana-lokiexplore-app/external amd \"react-dom\"", "webpack://grafana-lokiexplore-app/external amd \"react-redux\"", "webpack://grafana-lokiexplore-app/external amd \"react-router-dom\"", "webpack://grafana-lokiexplore-app/external amd \"redux\"", "webpack://grafana-lokiexplore-app/external amd \"rxjs\"", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/common/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/lr/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@grafana/lezer-logql/index.es.js", "webpack://grafana-lokiexplore-app/webpack/bootstrap", "webpack://grafana-lokiexplore-app/webpack/runtime/compat get default export", "webpack://grafana-lokiexplore-app/webpack/runtime/define property getters", "webpack://grafana-lokiexplore-app/webpack/runtime/ensure chunk", "webpack://grafana-lokiexplore-app/webpack/runtime/get javascript chunk filename", "webpack://grafana-lokiexplore-app/webpack/runtime/global", "webpack://grafana-lokiexplore-app/webpack/runtime/hasOwnProperty shorthand", "webpack://grafana-lokiexplore-app/webpack/runtime/make namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/publicPath", "webpack://grafana-lokiexplore-app/webpack/runtime/jsonp chunk loading", "webpack://grafana-lokiexplore-app/webpack/startup"], "sourcesContent": ["var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; typeof current == 'object' && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));\n\t}\n\tdef['default'] = () => (value);\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "var inProgress = {};\nvar dataWebpackPrefix = \"grafana-lokiexplore-app:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "import { LinkButton } from '@grafana/ui';\nimport { OpenInExploreLogsButtonProps } from 'Components/OpenInExploreLogsButton/types';\nimport React, { lazy, Suspense } from 'react';\nconst OpenInExploreLogsButton = lazy(() => import('Components/OpenInExploreLogsButton/OpenInExploreLogsButton'));\n\nfunction SuspendedOpenInExploreLogsButton(props: OpenInExploreLogsButtonProps) {\n  return (\n    <Suspense\n      fallback={\n        <LinkButton variant=\"secondary\" disabled>\n          Open in Explore Logs\n        </LinkButton>\n      }\n    >\n      <OpenInExploreLogsButton {...props} />\n    </Suspense>\n  );\n}\n\nexport const exposedComponents = [\n  {\n    id: `grafana-lokiexplore-app/open-in-explore-logs-button/v1`,\n    title: 'Open in Explore Logs button',\n    description: 'A button that opens a logs view in the Explore Logs app.',\n    component: SuspendedOpenInExploreLogsButton,\n  },\n];\n", "import { lazy } from 'react';\nimport { AppPlugin } from '@grafana/data';\nimport { linkConfigs } from 'services/extensions/links';\nimport { exposedComponents } from 'services/extensions/exposedComponents';\n\n// Anything imported in this file is included in the main bundle which is pre-loaded in Grafana\n// Don't add imports to this file without lazy loading\n// Link extensions are the exception as they must be included in the main bundle in order to work in core Grafana\nconst App = lazy(async () => {\n  const { wasmSupported } = await import('services/sorting');\n\n  const { default: initRuntimeDs } = await import('services/datasource');\n  const { default: initChangepoint } = await import('@bsull/augurs/changepoint');\n  const { default: initOutlier } = await import('@bsull/augurs/outlier');\n\n  initRuntimeDs();\n\n  if (wasmSupported()) {\n    await Promise.all([initChangepoint(), initOutlier()]);\n  }\n\n  return import('Components/App');\n});\n\nconst AppConfig = lazy(async () => {\n  return await import('./Components/AppConfig/AppConfig');\n});\n\nexport const plugin = new AppPlugin<{}>().setRootPage(App).addConfigPage({\n  title: 'Configuration',\n  icon: 'cog',\n  body: AppConfig,\n  id: 'configuration',\n});\n\nfor (const linkConfig of linkConfigs) {\n  plugin.addLink(linkConfig);\n}\n\nfor (const exposedComponentConfig of exposedComponents) {\n  plugin.exposeComponent(exposedComponentConfig);\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { PluginExtensionLinkConfig, PluginExtensionPanelContext, PluginExtensionPoints } from '@grafana/data';\n\nimport {\n  addAdHocFilterUserInputPrefix,\n  AdHocFieldValue,\n  AppliedPattern,\n  LEVEL_VARIABLE_VALUE,\n  SERVICE_NAME,\n  stripAdHocFilterUserInputPrefix,\n  VAR_DATASOURCE,\n  VAR_FIELDS,\n  VAR_LABELS,\n  VAR_LEVELS,\n  VAR_LINE_FILTERS,\n  VAR_METADATA,\n  VAR_PATTERNS,\n} from 'services/variables';\nimport pluginJson from '../../plugin.json';\nimport { getMatcherFromQuery } from '../logqlMatchers';\nimport { LokiQuery } from '../lokiQuery';\nimport { LabelType } from '../fieldsTypes';\n\nimport { isOperatorInclusive } from '../operatorHelpers';\nimport { PatternFilterOp } from '../filterTypes';\nimport { renderPatternFilters } from '../renderPatternFilters';\n\nconst PRODUCT_NAME = 'Grafana Logs Drilldown';\nconst title = `Open in ${PRODUCT_NAME}`;\nconst description = `Open current query in the ${PRODUCT_NAME} view`;\nconst icon = 'gf-logs';\n\nexport const ExtensionPoints = {\n  MetricInvestigation: 'grafana-lokiexplore-app/investigation/v1',\n} as const;\n\nexport type LinkConfigs = Array<\n  {\n    targets: string | string[];\n    // eslint-disable-next-line deprecation/deprecation\n  } & Omit<PluginExtensionLinkConfig<PluginExtensionPanelContext>, 'type' | 'extensionPointId'>\n>;\n\n// `plugin.addLink` requires these types; unfortunately, the correct `PluginExtensionAddedLinkConfig` type is not exported with 11.2.x\n// TODO: fix this type when we move to `@grafana/data` 11.3.x\nexport const linkConfigs: LinkConfigs = [\n  {\n    targets: PluginExtensionPoints.DashboardPanelMenu,\n    title,\n    description,\n    icon,\n    path: createAppUrl(),\n    configure: contextToLink,\n  },\n  {\n    targets: PluginExtensionPoints.ExploreToolbarAction,\n    title,\n    description,\n    icon,\n    path: createAppUrl(),\n    configure: contextToLink,\n  },\n];\n\nfunction stringifyValues(value?: string): string {\n  if (!value) {\n    return '\"\"';\n  }\n  return value;\n}\n\n// Why are there twice as many escape chars in the url as expected?\nexport function replaceEscapeChars(value?: string): string | undefined {\n  return value?.replace(/\\\\\\\\/g, '\\\\');\n}\n\nexport function stringifyAdHocValues(value?: string): string {\n  if (!value) {\n    return '\"\"';\n  }\n\n  // All label values from explore are already escaped, so we mark them as custom values to prevent them from getting escaped again when rendering the LogQL\n  return addAdHocFilterUserInputPrefix(replaceEscapeChars(value));\n}\n\nfunction contextToLink<T extends PluginExtensionPanelContext>(context?: T) {\n  if (!context) {\n    return undefined;\n  }\n  const lokiQuery = context.targets.find((target) => target.datasource?.type === 'loki') as LokiQuery | undefined;\n  if (!lokiQuery || !lokiQuery.datasource?.uid) {\n    return undefined;\n  }\n\n  const expr = lokiQuery.expr;\n  const { labelFilters, lineFilters, fields, patternFilters } = getMatcherFromQuery(expr, context, lokiQuery);\n  const labelSelector = labelFilters.find((selector) => isOperatorInclusive(selector.operator));\n\n  // Require at least one inclusive operator to run a valid Loki query\n  if (!labelSelector) {\n    return undefined;\n  }\n\n  // If there are a bunch of values for the same field, the value slug can get really long, let's just use the first one in the URL\n  const urlLabelValue = labelSelector.value.split('|')[0];\n  const labelValue = replaceSlash(urlLabelValue);\n  let labelName = labelSelector.key === SERVICE_NAME ? 'service' : labelSelector.key;\n  // sort `primary label` first\n  labelFilters.sort((a) => (a.key === labelName ? -1 : 1));\n\n  let params = setUrlParameter(UrlParameters.DatasourceId, lokiQuery.datasource?.uid, new URLSearchParams());\n  params = setUrlParameter(UrlParameters.TimeRangeFrom, context.timeRange.from.valueOf().toString(), params);\n  params = setUrlParameter(UrlParameters.TimeRangeTo, context.timeRange.to.valueOf().toString(), params);\n\n  for (const labelFilter of labelFilters) {\n    // skip non-indexed filters for now\n    if (labelFilter.type !== LabelType.Indexed) {\n      continue;\n    }\n\n    const labelsAdHocFilterURLString = `${labelFilter.key}|${labelFilter.operator}|${escapeURLDelimiters(\n      stringifyAdHocValues(labelFilter.value)\n    )},${escapeURLDelimiters(replaceEscapeChars(labelFilter.value))}`;\n\n    params = appendUrlParameter(UrlParameters.Labels, labelsAdHocFilterURLString, params);\n  }\n\n  if (lineFilters) {\n    for (const lineFilter of lineFilters) {\n      params = appendUrlParameter(\n        UrlParameters.LineFilters,\n        `${lineFilter.key}|${escapeURLDelimiters(lineFilter.operator)}|${escapeURLDelimiters(\n          stringifyValues(lineFilter.value)\n        )}`,\n        params\n      );\n    }\n  }\n  if (fields?.length) {\n    for (const field of fields) {\n      if (field.type === LabelType.StructuredMetadata) {\n        if (field.key === LEVEL_VARIABLE_VALUE) {\n          params = appendUrlParameter(\n            UrlParameters.Levels,\n            `${field.key}|${field.operator}|${escapeURLDelimiters(stringifyValues(field.value))}`,\n            params\n          );\n        } else {\n          params = appendUrlParameter(\n            UrlParameters.Metadata,\n            `${field.key}|${field.operator}|${escapeURLDelimiters(\n              stringifyAdHocValues(field.value)\n            )},${escapeURLDelimiters(replaceEscapeChars(field.value))}`,\n            params\n          );\n        }\n      } else {\n        const fieldValue: AdHocFieldValue = {\n          value: field.value,\n          parser: field.parser,\n        };\n\n        const adHocFilterURLString = `${field.key}|${field.operator}|${escapeURLDelimiters(\n          stringifyAdHocValues(JSON.stringify(fieldValue))\n        )},${escapeURLDelimiters(replaceEscapeChars(fieldValue.value))}`;\n\n        params = appendUrlParameter(UrlParameters.Fields, adHocFilterURLString, params);\n      }\n    }\n  }\n  if (patternFilters?.length) {\n    const patterns: AppliedPattern[] = [];\n\n    for (const field of patternFilters) {\n      patterns.push({\n        type: field.operator === PatternFilterOp.match ? 'include' : 'exclude',\n        pattern: stringifyValues(field.value),\n      });\n    }\n\n    let patternsString = renderPatternFilters(patterns);\n\n    params = appendUrlParameter(UrlParameters.Patterns, JSON.stringify(patterns), params);\n    params = appendUrlParameter(UrlParameters.PatternsVariable, patternsString, params);\n  }\n\n  return {\n    path: createAppUrl(`/explore/${labelName}/${labelValue}/logs`, params),\n  };\n}\n\nexport function createAppUrl(path = '/explore', urlParams?: URLSearchParams): string {\n  return `/a/${pluginJson.id}${path}${urlParams ? `?${urlParams.toString()}` : ''}`;\n}\n\nexport const UrlParameters = {\n  DatasourceId: `var-${VAR_DATASOURCE}`,\n  TimeRangeFrom: 'from',\n  TimeRangeTo: 'to',\n  Labels: `var-${VAR_LABELS}`,\n  Fields: `var-${VAR_FIELDS}`,\n  Metadata: `var-${VAR_METADATA}`,\n  Levels: `var-${VAR_LEVELS}`,\n  LineFilters: `var-${VAR_LINE_FILTERS}`,\n  Patterns: VAR_PATTERNS,\n  PatternsVariable: `var-${VAR_PATTERNS}`,\n} as const;\nexport type UrlParameterType = (typeof UrlParameters)[keyof typeof UrlParameters];\n\nexport function setUrlParameter(key: UrlParameterType, value: string, initalParams?: URLSearchParams): URLSearchParams {\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? location.search);\n  searchParams.set(key, value);\n\n  return searchParams;\n}\n\nexport function appendUrlParameter(\n  key: UrlParameterType,\n  value: string,\n  initalParams?: URLSearchParams\n): URLSearchParams {\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? location.search);\n  searchParams.append(key, value);\n\n  return searchParams;\n}\n\nexport function replaceSlash(parameter: string): string {\n  return (\n    stripAdHocFilterUserInputPrefix(parameter)\n      // back-slash is converted to forward-slash in the URL, replace that char\n      .replace(/\\//g, '-')\n      .replace(/\\\\/g, '-')\n  );\n}\n\n// Manually copied over from @grafana/scenes so we don't need to import scenes to build links\nfunction escapeUrlCommaDelimiters(value: string | undefined): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  // Replace the comma due to using it as a value/label separator\n  return /,/g[Symbol.replace](value, '__gfc__');\n}\n\nexport function escapeUrlPipeDelimiters(value: string | undefined): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  // Replace the pipe due to using it as a filter separator\n  return (value = /\\|/g[Symbol.replace](value, '__gfp__'));\n}\n\nexport function escapeURLDelimiters(value: string | undefined): string {\n  return escapeUrlCommaDelimiters(escapeUrlPipeDelimiters(value));\n}\n", "// copied from public/app/plugins/datasource/loki/types.ts\nexport enum LabelType {\n  Indexed = 'I',\n  StructuredMetadata = 'S',\n  Parsed = 'P',\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n\nimport { LabelType } from './fieldsTypes';\nimport { ParserType } from './variables';\n\nexport type FilterOpType = LabelFilterOp | NumericFilterOp;\nexport enum LabelFilterOp {\n  Equal = '=',\n  NotEqual = '!=',\n  RegexEqual = '=~',\n  RegexNotEqual = '!~',\n}\n\nexport enum NumericFilterOp {\n  gt = '>',\n  lt = '<',\n  gte = '>=',\n  lte = '<=',\n}\nexport const FilterOp = { ...LabelFilterOp, ...NumericFilterOp };\n\nexport type IndexedLabelFilter = {\n  key: string;\n  operator: FilterOpType;\n  value: string;\n  type?: LabelType;\n};\n\nexport type FieldFilter = {\n  key: string;\n  operator: FilterOpType;\n  value: string;\n  type?: LabelType;\n  parser?: ParserType;\n};\n\nexport type LineFilterType = {\n  key: string;\n  operator: LineFilterOp;\n  value: string;\n};\n\nexport type PatternFilterType = {\n  operator: PatternFilterOp;\n  value: string;\n};\n\nexport enum LineFilterOp {\n  match = '|=',\n  negativeMatch = `!=`,\n  regex = '|~',\n  negativeRegex = `!~`,\n}\n\nexport enum PatternFilterOp {\n  match = '|>',\n  negativeMatch = '!>',\n}\n\nexport enum LineFilterCaseSensitive {\n  caseSensitive = 'caseSensitive',\n  caseInsensitive = 'caseInsensitive',\n}\n", "import { LogContext } from '@grafana/faro-web-sdk';\nimport { FetchError, logError, logInfo, logWarning } from '@grafana/runtime';\nimport pluginJson from '../plugin.json';\nimport packageJson from '../../package.json';\nimport { isRecord } from './narrowing';\n\nconst defaultContext = {\n  app: pluginJson.id,\n  version: packageJson.version,\n};\n\nexport const logger = {\n  info: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.log(msg, ctx);\n    attemptFaroInfo(msg, ctx);\n  },\n  warn: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.warn(msg, ctx);\n    attemptFaroWarn(msg, ctx);\n  },\n  error: (err: Error | unknown, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.error(err, ctx);\n    attemptFaroErr(err, ctx);\n  },\n};\n\nconst attemptFaroInfo = (msg: string, context?: LogContext) => {\n  try {\n    logInfo(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro event!');\n  }\n};\n\nconst attemptFaroWarn = (msg: string, context?: LogContext) => {\n  try {\n    logWarning(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro warning!', { msg, context });\n  }\n};\n/**\n * Checks unknown error for properties from Records like FetchError and adds them to the context\n * @param err\n * @param context\n */\nfunction populateFetchErrorContext(err: unknown | FetchError, context: LogContext) {\n  if (typeof err === 'object' && err !== null) {\n    if (isRecord(err)) {\n      Object.keys(err).forEach((key: string) => {\n        const value = err[key];\n        if (typeof value === 'string' || typeof value === 'boolean' || typeof value === 'number') {\n          context[key] = value.toString();\n        }\n      });\n    }\n\n    if (hasData(err)) {\n      if (typeof err.data === 'object' && err.data !== null) {\n        try {\n          context.data = JSON.stringify(err.data);\n        } catch (e) {\n          // do nothing\n        }\n      } else if (typeof err.data === 'string' || typeof err.data === 'boolean' || typeof err.data === 'number') {\n        context.data = err.data.toString();\n      }\n    }\n  }\n}\n\nconst attemptFaroErr = (err: Error | FetchError | unknown, context2: LogContext) => {\n  let context = context2;\n  try {\n    populateFetchErrorContext(err, context);\n\n    if (err instanceof Error) {\n      logError(err, context);\n    } else if (typeof err === 'string') {\n      logError(new Error(err), context);\n    } else if (err && typeof err === 'object') {\n      if (context.msg) {\n        logError(new Error(context.msg), context);\n      } else {\n        logError(new Error('error object'), context);\n      }\n    } else {\n      logError(new Error('unknown error'), context);\n    }\n  } catch (e) {\n    console.error('Failed to log faro error!', { err, context });\n  }\n};\n\nconst hasData = (value: object): value is { data: unknown } => {\n  return 'data' in value;\n};\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n\nimport {\n  Bytes,\n  Duration,\n  Eq,\n  FilterOp,\n  Gte,\n  Gtr,\n  Identifier,\n  <PERSON>son,\n  LabelFilter,\n  LineFilter,\n  Logfmt,\n  Lss,\n  Lte,\n  Matcher,\n  Neq,\n  Npa,\n  Nre,\n  Number,\n  OrFilter,\n  parser,\n  PipeExact,\n  PipeMatch,\n  PipePattern,\n  Re,\n  Selector,\n  String,\n} from '@grafana/lezer-logql';\nimport { NodeType, SyntaxNode, Tree } from '@lezer/common';\nimport {\n  FieldFilter,\n  FilterOp as FilterOperator,\n  FilterOpType,\n  IndexedLabelFilter,\n  LineFilterCaseSensitive,\n  LineFilterOp,\n  LineFilterType,\n  PatternFilterOp,\n  PatternFilterType,\n} from './filterTypes';\nimport { PluginExtensionPanelContext } from '@grafana/data';\nimport { getLabelType<PERSON>rom<PERSON><PERSON><PERSON>, LokiQuery } from './lokiQuery';\nimport { LabelType } from './fieldsTypes';\nimport { ParserType } from './variables';\n\nexport class NodePosition {\n  from: number;\n  to: number;\n  type?: NodeType;\n  syntaxNode?: SyntaxNode;\n\n  constructor(from: number, to: number, syntaxNode?: SyntaxNode, type?: NodeType) {\n    this.from = from;\n    this.to = to;\n    this.type = type;\n    this.syntaxNode = syntaxNode;\n  }\n\n  static fromNode(node: SyntaxNode): NodePosition {\n    return new NodePosition(node.from, node.to, node, node.type);\n  }\n\n  contains(position: NodePosition): boolean {\n    return this.from <= position.from && this.to >= position.to;\n  }\n\n  getExpression(query: string): string {\n    return query.substring(this.from, this.to);\n  }\n}\n\nexport function getNodesFromQuery(query: string, nodeTypes?: number[]): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  const tree: Tree = parser.parse(query);\n  tree.iterate({\n    enter: (node): false | void => {\n      if (nodeTypes === undefined || nodeTypes.includes(node.type.id)) {\n        nodes.push(node.node);\n      }\n    },\n  });\n  return nodes;\n}\n\n/**\n * Returns the leaf nodes on the left-hand-side matching nodeTypes\n * @param query\n * @param nodeTypes\n */\nexport function getLHSLeafNodesFromQuery(query: string, nodeTypes: number[]): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  const tree: Tree = parser.parse(query);\n\n  tree.iterate({\n    enter: (node): false | void => {\n      if (nodeTypes.includes(node.type.id)) {\n        let leftChild: SyntaxNode | null;\n        while ((leftChild = node.node.firstChild) !== null) {\n          if (!nodeTypes.includes(leftChild.node.type.id)) {\n            nodes.push(node.node);\n            return false;\n          }\n          node = leftChild;\n        }\n      }\n    },\n  });\n  return nodes;\n}\n\nfunction getAllPositionsInNodeByType(node: SyntaxNode, type: number): NodePosition[] {\n  if (node.type.id === type) {\n    return [NodePosition.fromNode(node)];\n  }\n\n  const positions: NodePosition[] = [];\n  let pos = 0;\n  let child = node.childAfter(pos);\n  while (child) {\n    positions.push(...getAllPositionsInNodeByType(child, type));\n    pos = child.to;\n    child = node.childAfter(pos);\n  }\n  return positions;\n}\n\nfunction parseLabelFilters(query: string, filter: IndexedLabelFilter[]) {\n  // `Matcher` will select field filters as well as indexed label filters\n  const allMatcher = getNodesFromQuery(query, [Matcher]);\n  for (const matcher of allMatcher) {\n    const identifierPosition = getAllPositionsInNodeByType(matcher, Identifier);\n    const valuePosition = getAllPositionsInNodeByType(matcher, String);\n    const operator = query.substring(identifierPosition[0]?.to, valuePosition[0]?.from);\n    const key = identifierPosition[0].getExpression(query);\n    const value = valuePosition.map((position) => query.substring(position.from + 1, position.to - 1))[0];\n\n    if (\n      !key ||\n      !value ||\n      (operator !== FilterOperator.NotEqual &&\n        operator !== FilterOperator.Equal &&\n        operator !== FilterOperator.RegexEqual &&\n        operator !== FilterOperator.RegexNotEqual)\n    ) {\n      continue;\n    }\n\n    filter.push({\n      key,\n      operator,\n      value,\n      type: LabelType.Indexed,\n    });\n  }\n}\n\nfunction parseNonPatternFilters(\n  lineFilterValue: string,\n  quoteString: string,\n  lineFilters: LineFilterType[],\n  index: number,\n  operator: LineFilterOp\n) {\n  const isRegexSelector = operator === LineFilterOp.regex || operator === LineFilterOp.negativeRegex;\n  const isCaseInsensitive = lineFilterValue.includes('(?i)') && isRegexSelector;\n\n  // If quoteString is `, we shouldn't need to un-escape anything\n  // But if the quoteString is \", we'll need to remove double escape chars, as these values are re-escaped when building the query expression (but not stored in the value/url)\n  if (quoteString === '\"' && isRegexSelector) {\n    const replaceDoubleEscape = new RegExp(/\\\\\\\\/, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleEscape, '\\\\');\n  } else if (quoteString === '\"') {\n    const replaceDoubleQuoteEscape = new RegExp(/\\\\\\\\\\\"/, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleQuoteEscape, '\"');\n\n    const replaceDoubleEscape = new RegExp(/\\\\\\\\/, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleEscape, '\\\\');\n  }\n\n  if (isCaseInsensitive) {\n    // If `(?i)` exists in a regex it would need to be escaped to match log lines containing `(?i)`, so it should be safe to replace all instances of `(?i)` in the line filter?\n    lineFilterValue = lineFilterValue.replace('(?i)', '');\n  }\n\n  lineFilters.push({\n    key: isCaseInsensitive\n      ? LineFilterCaseSensitive.caseInsensitive.toString()\n      : LineFilterCaseSensitive.caseSensitive.toString() + ',' + index.toString(),\n    operator: operator,\n    value: lineFilterValue,\n  });\n\n  return lineFilterValue;\n}\n\nfunction parsePatternFilters(lineFilterValue: string, patternFilters: PatternFilterType[], operator: PatternFilterOp) {\n  const replaceDoubleQuoteEscape = new RegExp(/\\\\\"/, 'g');\n  lineFilterValue = lineFilterValue.replace(replaceDoubleQuoteEscape, '\"');\n  patternFilters.push({\n    operator,\n    value: lineFilterValue,\n  });\n}\n\nfunction parseLineFilters(query: string, lineFilters: LineFilterType[], patternFilters: PatternFilterType[]) {\n  const allLineFilters = getNodesFromQuery(query, [LineFilter]);\n  for (const [index, matcher] of allLineFilters.entries()) {\n    const equal = getAllPositionsInNodeByType(matcher, PipeExact);\n    const pipeRegExp = getAllPositionsInNodeByType(matcher, PipeMatch);\n    const notEqual = getAllPositionsInNodeByType(matcher, Neq);\n    const notEqualRegExp = getAllPositionsInNodeByType(matcher, Nre);\n    const patternInclude = getAllPositionsInNodeByType(matcher, PipePattern);\n    const patternExclude = getAllPositionsInNodeByType(matcher, Npa);\n\n    const lineFilterValueNodes = getStringsFromLineFilter(matcher);\n\n    for (const lineFilterValueNode of lineFilterValueNodes) {\n      const quoteString = query.substring(lineFilterValueNode?.from + 1, lineFilterValueNode?.from);\n\n      // Remove quotes\n      let lineFilterValue = query.substring(lineFilterValueNode?.from + 1, lineFilterValueNode?.to - 1);\n\n      if (lineFilterValue.length) {\n        let operator;\n        if (equal.length) {\n          operator = LineFilterOp.match;\n        } else if (notEqual.length) {\n          operator = LineFilterOp.negativeMatch;\n        } else if (notEqualRegExp.length) {\n          operator = LineFilterOp.negativeRegex;\n        } else if (pipeRegExp.length) {\n          operator = LineFilterOp.regex;\n        } else if (patternInclude.length) {\n          operator = PatternFilterOp.match;\n        } else if (patternExclude.length) {\n          operator = PatternFilterOp.negativeMatch;\n        } else {\n          console.warn('unknown line filter', {\n            query: query.substring(matcher.from, matcher.to),\n          });\n\n          continue;\n        }\n\n        if (!(operator === PatternFilterOp.match || operator === PatternFilterOp.negativeMatch)) {\n          parseNonPatternFilters(lineFilterValue, quoteString, lineFilters, index, operator);\n        } else {\n          parsePatternFilters(lineFilterValue, patternFilters, operator);\n        }\n      }\n    }\n  }\n}\n\nfunction getNumericFieldOperator(matcher: SyntaxNode) {\n  if (getAllPositionsInNodeByType(matcher, Lte).length) {\n    return FilterOperator.lte;\n  } else if (getAllPositionsInNodeByType(matcher, Lss).length) {\n    return FilterOperator.lt;\n  } else if (getAllPositionsInNodeByType(matcher, Gte).length) {\n    return FilterOperator.gte;\n  } else if (getAllPositionsInNodeByType(matcher, Gtr).length) {\n    return FilterOperator.gt;\n  }\n\n  console.warn('unknown numeric operator');\n\n  return undefined;\n}\n\nfunction getStringFieldOperator(matcher: SyntaxNode) {\n  if (getAllPositionsInNodeByType(matcher, Eq).length) {\n    return FilterOperator.Equal; // =\n  } else if (getAllPositionsInNodeByType(matcher, Neq).length) {\n    return FilterOperator.NotEqual; // !=\n  } else if (getAllPositionsInNodeByType(matcher, Re).length) {\n    return FilterOperator.RegexEqual; // =~\n  } else if (getAllPositionsInNodeByType(matcher, Nre).length) {\n    return FilterOperator.RegexNotEqual; // !~\n  }\n\n  return undefined;\n}\n\nfunction parseFields(query: string, fields: FieldFilter[], context: PluginExtensionPanelContext, lokiQuery: LokiQuery) {\n  const dataFrame = context.data?.series.find((frame) => frame.refId === lokiQuery.refId);\n  // We do not currently support \"or\" in Grafana Logs Drilldown, so grab the left hand side LabelFilter leaf nodes as this will be the first filter expression in a given pipeline stage\n  const allFields = getLHSLeafNodesFromQuery(query, [LabelFilter]);\n\n  for (const matcher of allFields) {\n    const position = NodePosition.fromNode(matcher);\n    const expression = position.getExpression(query);\n\n    // Skip error expression, it will get added automatically when Grafana Logs Drilldown adds a parser\n    if (expression.substring(0, 9) === `__error__`) {\n      continue;\n    }\n\n    // @todo we need to use detected_fields API to get the \"right\" parser for a specific field\n    // Currently we just check to see if there is a parser before the current node, this means that queries that are placing metadata filters after the parser will query the metadata field as a parsed field, which will lead to degraded performance\n    const logFmtParser = getNodesFromQuery(query.substring(0, matcher.node.to), [Logfmt]);\n    const jsonParser = getNodesFromQuery(query.substring(0, matcher.node.to), [Json]);\n\n    // field filter key\n    const fieldNameNode = getAllPositionsInNodeByType(matcher, Identifier);\n    const fieldName = fieldNameNode[0]?.getExpression(query);\n\n    // field filter value\n    const fieldStringValue = getAllPositionsInNodeByType(matcher, String);\n    const fieldNumberValue = getAllPositionsInNodeByType(matcher, Number);\n    const fieldBytesValue = getAllPositionsInNodeByType(matcher, Bytes);\n    const fieldDurationValue = getAllPositionsInNodeByType(matcher, Duration);\n\n    let fieldValue: string, operator: FilterOpType | undefined;\n    if (fieldStringValue.length) {\n      operator = getStringFieldOperator(matcher);\n      // Strip out quotes\n      fieldValue = query.substring(fieldStringValue[0].from + 1, fieldStringValue[0].to - 1);\n    } else if (fieldNumberValue.length) {\n      fieldValue = fieldNumberValue[0].getExpression(query);\n      operator = getNumericFieldOperator(matcher);\n    } else if (fieldDurationValue.length) {\n      operator = getNumericFieldOperator(matcher);\n      fieldValue = fieldDurationValue[0].getExpression(query);\n    } else if (fieldBytesValue.length) {\n      operator = getNumericFieldOperator(matcher);\n      fieldValue = fieldBytesValue[0].getExpression(query);\n    } else {\n      continue;\n    }\n\n    // Label type\n    let labelType: LabelType | undefined;\n    if (dataFrame) {\n      // @todo if the field label is not in the first line, we'll always add this filter as a field filter\n      // Also negative filters that exclude all values of a field will always fail to get a label type for that exclusion filter?\n      labelType = getLabelTypeFromFrame(fieldName, dataFrame) ?? undefined;\n    }\n\n    if (operator) {\n      let parser: ParserType | undefined;\n      if (logFmtParser.length && jsonParser.length) {\n        parser = 'mixed';\n      } else if (logFmtParser.length) {\n        parser = 'logfmt';\n      } else if (jsonParser.length) {\n        parser = 'json';\n      } else {\n        // If there is no parser in the query, the field would have to be metadata or an invalid query?\n        labelType = LabelType.StructuredMetadata;\n      }\n\n      fields.push({\n        key: fieldName,\n        operator: operator,\n        type: labelType ?? LabelType.Parsed,\n        parser,\n        value: fieldValue,\n      });\n    }\n  }\n}\n\nexport function getMatcherFromQuery(\n  query: string,\n  context: PluginExtensionPanelContext,\n  lokiQuery: LokiQuery\n): {\n  labelFilters: IndexedLabelFilter[];\n  lineFilters?: LineFilterType[];\n  fields?: FieldFilter[];\n  patternFilters?: PatternFilterType[];\n} {\n  const filter: IndexedLabelFilter[] = [];\n  const lineFilters: LineFilterType[] = [];\n  const patternFilters: PatternFilterType[] = [];\n  const fields: FieldFilter[] = [];\n  const selector = getNodesFromQuery(query, [Selector]);\n\n  if (selector.length === 0) {\n    return { labelFilters: filter };\n  }\n\n  // Get the stream selector portion of the query\n  const selectorQuery = getAllPositionsInNodeByType(selector[0], Selector)[0].getExpression(query);\n\n  parseLabelFilters(selectorQuery, filter);\n  parseLineFilters(query, lineFilters, patternFilters);\n  parseFields(query, fields, context, lokiQuery);\n\n  return { labelFilters: filter, lineFilters, fields, patternFilters };\n}\n\nexport function isQueryWithNode(query: string, nodeType: number): boolean {\n  let isQueryWithNode = false;\n  const tree = parser.parse(query);\n  tree.iterate({\n    enter: ({ type }): false | void => {\n      if (type.id === nodeType) {\n        isQueryWithNode = true;\n        return false;\n      }\n    },\n  });\n  return isQueryWithNode;\n}\n\n/**\n * Parses the query and looks for error nodes. If there is at least one, it returns true.\n * Grafana variables are considered errors, so if you need to validate a query\n * with variables you should interpolate it first.\n */\nexport const ErrorId = 0;\nexport function isValidQuery(query: string): boolean {\n  return isQueryWithNode(query, ErrorId) === false;\n}\n\nfunction getStringsFromLineFilter(filter: SyntaxNode): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  let node: SyntaxNode | null = filter;\n  do {\n    const string = node.getChild(String);\n    if (string && !node.getChild(FilterOp)) {\n      nodes.push(string);\n    }\n    node = node.getChild(OrFilter);\n  } while (node != null);\n\n  return nodes;\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { DataSourceRef } from '@grafana/schema';\nimport { DataSourceWithBackend } from '@grafana/runtime';\nimport { DataFrame, DataSourceJsonData, ScopedVars, TimeRange } from '@grafana/data';\nimport { LabelType } from './fieldsTypes';\n\nexport enum LokiQueryDirection {\n  Backward = 'backward',\n  Forward = 'forward',\n  Scan = 'scan',\n}\n\nexport type LokiQuery = {\n  refId: string;\n  queryType?: LokiQueryType;\n  editorMode?: string;\n  supportingQueryType?: string;\n  expr: string;\n  legendFormat?: string;\n  splitDuration?: string;\n  datasource?: DataSourceRef;\n  maxLines?: number;\n  direction?: LokiQueryDirection;\n};\n\nexport type LokiQueryType = 'instant' | 'range' | 'stream' | string;\n\nexport type LokiDatasource = DataSourceWithBackend<LokiQuery, DataSourceJsonData> & {\n  maxLines?: number;\n} & {\n  // @todo delete after min supported grafana is upgraded to >=11.6\n  interpolateString?: (string: string, scopedVars?: ScopedVars) => string;\n  getTimeRangeParams: (timeRange: TimeRange) => { start: number; end: number };\n};\n\nexport function getLabelTypeFromFrame(labelKey: string, frame: DataFrame, index = 0): null | LabelType {\n  const typeField = frame.fields.find((field) => field.name === 'labelTypes')?.values[index];\n  if (!typeField) {\n    return null;\n  }\n  switch (typeField[labelKey]) {\n    case 'I':\n      return LabelType.Indexed;\n    case 'S':\n      return LabelType.StructuredMetadata;\n    case 'P':\n      return LabelType.Parsed;\n    default:\n      return null;\n  }\n}\n", "import { SelectedTableRow } from '../Components/Table/LogLineCellComponent';\nimport { LogsVisualizationType } from './store';\nimport { FieldValue, ParserType } from './variables';\nimport { LogsSortOrder, RawTimeRange } from '@grafana/data';\nimport { LabelFilterOp, NumericFilterOp } from './filterTypes';\n\nconst isObj = (o: unknown): o is object => typeof o === 'object' && o !== null;\n\nfunction hasProp<K extends PropertyKey>(data: object, prop: K): data is Record<K, unknown> {\n  return prop in data;\n}\n\nconst isString = (s: unknown) => (typeof s === 'string' && s) || '';\n\nexport const isRecord = (obj: unknown): obj is Record<string, unknown> => typeof obj === 'object';\n\nexport function unknownToStrings(a: unknown): string[] {\n  let strings: string[] = [];\n  if (Array.isArray(a)) {\n    for (let i = 0; i < a.length; i++) {\n      strings.push(isString(a[i]));\n    }\n  }\n  return strings;\n}\n\nexport function narrowSelectedTableRow(o: unknown): SelectedTableRow | false {\n  const narrowed = isObj(o) && hasProp(o, 'row') && hasProp(o, 'id') && o;\n\n  if (narrowed) {\n    const row = typeof narrowed.row === 'number' && narrowed.row;\n    const id = typeof narrowed.id === 'string' && narrowed.id;\n    if (id !== false && row !== false) {\n      return { row, id };\n    }\n  }\n\n  return false;\n}\n\nexport function narrowLogsVisualizationType(o: unknown): LogsVisualizationType | false {\n  return typeof o === 'string' && (o === 'logs' || o === 'table') && o;\n}\nexport function narrowLogsSortOrder(o: unknown): LogsSortOrder | false {\n  if (typeof o === 'string' && o === LogsSortOrder.Ascending.toString()) {\n    return LogsSortOrder.Ascending;\n  }\n\n  if (typeof o === 'string' && o === LogsSortOrder.Descending.toString()) {\n    return LogsSortOrder.Descending;\n  }\n\n  return false;\n}\n\nexport function narrowFieldValue(o: unknown): FieldValue | false {\n  const narrowed = isObj(o) && hasProp(o, 'value') && hasProp(o, 'parser') && o;\n\n  if (narrowed) {\n    const parser: ParserType | false =\n      typeof narrowed.parser === 'string' &&\n      (narrowed.parser === 'logfmt' ||\n        narrowed.parser === 'json' ||\n        narrowed.parser === 'mixed' ||\n        narrowed.parser === 'structuredMetadata') &&\n      narrowed.parser;\n    const value = typeof narrowed.value === 'string' && narrowed.value;\n\n    if (parser !== false && value !== false) {\n      return { parser, value };\n    }\n  }\n\n  return false;\n}\n\nexport function narrowRecordStringNumber(o: unknown): Record<string, number> | false {\n  const narrowed = isObj(o) && isRecord(o) && o;\n\n  if (narrowed) {\n    const keys = Object.keys(narrowed);\n    const returnRecord: Record<string, number> = {};\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const value = narrowed[keys[i]];\n      if (typeof value === 'number') {\n        returnRecord[key] = value;\n      }\n    }\n\n    return returnRecord;\n  }\n\n  return false;\n}\n\nexport function narrowTimeRange(unknownRange: unknown): RawTimeRange | undefined {\n  const range = isObj(unknownRange) && hasProp(unknownRange, 'to') && hasProp(unknownRange, 'from') && unknownRange;\n  if (range) {\n    const to = isString(range.to);\n    const from = isString(range.from);\n    if (to && from) {\n      return { to, from };\n    }\n  }\n\n  return undefined;\n}\n\nexport function narrowFilterOperator(op: string): LabelFilterOp | NumericFilterOp {\n  switch (op) {\n    case LabelFilterOp.Equal:\n    case LabelFilterOp.NotEqual:\n    case LabelFilterOp.RegexEqual:\n    case LabelFilterOp.RegexNotEqual:\n    case NumericFilterOp.gt:\n    case NumericFilterOp.gte:\n    case NumericFilterOp.lt:\n    case NumericFilterOp.lte:\n      return op;\n    default:\n      throw new NarrowingError('operator is invalid!');\n  }\n}\n\nexport class NarrowingError extends Error {}\n", "import { FilterOp, FilterOpType, NumericFilterOp } from './filterTypes';\nimport { numericOperatorArray } from './operators';\n\nexport const isOperatorInclusive = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.Equal || op === FilterOp.RegexEqual;\n};\nexport const isOperatorExclusive = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.NotEqual || op === FilterOp.RegexNotEqual;\n};\nexport const isOperatorRegex = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.RegexEqual || op === FilterOp.RegexNotEqual;\n};\nexport const isOperatorNumeric = (op: string | NumericFilterOp): boolean => {\n  return numericOperatorArray.includes(op);\n};\n", "import { FilterOp, FilterOpType } from './filterTypes';\nimport { logger } from './logger';\n\nexport function getOperatorDescription(op: FilterOpType): string {\n  if (op === FilterOp.NotEqual) {\n    return 'Not equal';\n  }\n  if (op === FilterOp.RegexNotEqual) {\n    return 'Does not match regex';\n  }\n  if (op === FilterOp.Equal) {\n    return 'Equals';\n  }\n  if (op === FilterOp.RegexEqual) {\n    return 'Matches regex';\n  }\n  if (op === FilterOp.lt) {\n    return 'Less than';\n  }\n  if (op === FilterOp.gt) {\n    return 'Greater than';\n  }\n  if (op === FilterOp.gte) {\n    return 'Greater than or equal to';\n  }\n  if (op === FilterOp.lte) {\n    return 'Less than or equal to';\n  }\n\n  const error = new Error('Invalid operator!');\n  logger.error(error, { msg: 'Invalid operator', operator: op });\n  throw error;\n}\n", "import { FilterOp, LineFilterOp } from './filterTypes';\nimport { SelectableValue } from '@grafana/data';\nimport { getOperatorDescription } from './getOperatorDescription';\n\nexport const operators = [FilterOp.Equal, FilterOp.NotEqual, FilterOp.RegexEqual, FilterOp.RegexNotEqual].map<\n  SelectableValue<string>\n>((value, index, array) => {\n  return {\n    description: getOperatorDescription(value),\n    label: value,\n    value,\n  };\n});\n\nexport const includeOperators = [FilterOp.Equal, FilterOp.RegexEqual].map<SelectableValue<string>>((value) => ({\n  description: getOperatorDescription(value),\n  label: value,\n  value,\n}));\n\nexport const numericOperatorArray = [FilterOp.gt, FilterOp.gte, FilterOp.lt, FilterOp.lte];\n\nexport const numericOperators = numericOperatorArray.map<SelectableValue<string>>((value) => ({\n  description: getOperatorDescription(value),\n  label: value,\n  value,\n}));\n\nexport const lineFilterOperators: SelectableValue[] = [\n  { label: 'match', value: LineFilterOp.match },\n  { label: 'negativeMatch', value: LineFilterOp.negativeMatch },\n  { label: 'regex', value: LineFilterOp.regex },\n  { label: 'negativeRegex', value: LineFilterOp.negativeRegex },\n];\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain any imports to keep that bundle size small. Don't add imports to this file!\n\n/**\n * Methods copied from scenes that we want in the module (to generate links which cannot be lazy loaded), without including all of scenes.\n * See https://github.com/grafana/scenes/issues/1046\n */\n// based on the openmetrics-documentation, the 3 symbols we have to handle are:\n// - \\n ... the newline character\n// - \\  ... the backslash character\n// - \"  ... the double-quote character\nexport function escapeLabelValueInExactSelector(labelValue: string): string {\n  return labelValue.replace(/\\\\/g, '\\\\\\\\').replace(/\\n/g, '\\\\n').replace(/\"/g, '\\\\\"');\n}\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain many imports to keep that bundle size small. Don't add imports to this file!\nimport { AppliedPattern } from './variables';\nimport { escapeLabelValueInExactSelector } from './extensions/scenesMethods';\n\nexport function renderPatternFilters(patterns: AppliedPattern[]) {\n  const excludePatterns = patterns.filter((pattern) => pattern.type === 'exclude');\n  const excludePatternsLine = excludePatterns\n    .map((p) => `!> \"${escapeLabelValueInExactSelector(p.pattern)}\"`)\n    .join(' ')\n    .trim();\n\n  const includePatterns = patterns.filter((pattern) => pattern.type === 'include');\n  let includePatternsLine = '';\n  if (includePatterns.length > 0) {\n    if (includePatterns.length === 1) {\n      includePatternsLine = `|> \"${escapeLabelValueInExactSelector(includePatterns[0].pattern)}\"`;\n    } else {\n      includePatternsLine = `|> ${includePatterns\n        .map((p) => `\"${escapeLabelValueInExactSelector(p.pattern)}\"`)\n        .join(' or ')}`;\n    }\n  }\n  return `${excludePatternsLine} ${includePatternsLine}`.trim();\n}\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain any imports to keep that bundle size small. Don't add imports to this file!\n\nimport { AdHocFilterWithLabels } from '@grafana/scenes';\n\nexport interface FieldValue {\n  value: string;\n  parser: ParserType;\n}\n\nexport interface AdHocFieldValue {\n  value?: string;\n  parser?: ParserType;\n}\nexport interface AppliedPattern {\n  pattern: string;\n  type: 'include' | 'exclude';\n}\n\nexport type ParserType = 'logfmt' | 'json' | 'mixed' | 'structuredMetadata';\nexport type DetectedFieldType = 'int' | 'float' | 'duration' | 'bytes' | 'boolean' | 'string';\nexport type AdHocFilterWithLabelsMeta = { parser?: ParserType; type?: DetectedFieldType };\nexport type AdHocFiltersWithLabelsAndMeta = AdHocFilterWithLabels<AdHocFilterWithLabelsMeta>;\n\nexport type LogsQueryOptions = {\n  labelExpressionToAdd?: string;\n  structuredMetadataToAdd?: string;\n  fieldExpressionToAdd?: string;\n  parser?: ParserType;\n  fieldType?: DetectedFieldType;\n};\n\nexport const VAR_LABELS = 'filters';\nexport const VAR_LABELS_EXPR = '${filters}';\nexport const VAR_LABELS_REPLICA = 'filters_replica';\nexport const VAR_LABELS_REPLICA_EXPR = '${filters_replica}';\nexport const VAR_FIELDS = 'fields';\nexport const VAR_FIELDS_EXPR = '${fields}';\nexport const PENDING_FIELDS_EXPR = '${pendingFields}';\nexport const PENDING_METADATA_EXPR = '${pendingMetadata}';\nexport const VAR_FIELDS_AND_METADATA = 'all-fields';\nexport const VAR_METADATA = 'metadata';\nexport const VAR_METADATA_EXPR = '${metadata}';\nexport const VAR_PATTERNS = 'patterns';\nexport const VAR_PATTERNS_EXPR = '${patterns}';\nexport const VAR_LEVELS = 'levels';\nexport const VAR_LEVELS_EXPR = '${levels}';\nexport const VAR_FIELD_GROUP_BY = 'fieldBy';\nexport const VAR_LABEL_GROUP_BY = 'labelBy';\nexport const VAR_LABEL_GROUP_BY_EXPR = '${labelBy}';\nexport const VAR_PRIMARY_LABEL_SEARCH = 'primary_label_search';\nexport const VAR_PRIMARY_LABEL_SEARCH_EXPR = '${primary_label_search}';\nexport const VAR_PRIMARY_LABEL = 'primary_label';\nexport const VAR_PRIMARY_LABEL_EXPR = '${primary_label}';\nexport const VAR_DATASOURCE = 'ds';\nexport const VAR_DATASOURCE_EXPR = '${ds}';\nexport const MIXED_FORMAT_EXPR = `| json | logfmt | drop __error__, __error_details__`;\nexport const JSON_FORMAT_EXPR = `| json | drop __error__, __error_details__`;\nexport const LOGS_FORMAT_EXPR = `| logfmt`;\n// This variable is hardcoded to the value of MIXED_FORMAT_EXPR. This is a hack to get logs context working, we don't want to use a variable for a value that doesn't change and cannot be updated by the user.\nexport const VAR_LOGS_FORMAT = 'logsFormat';\nexport const VAR_LOGS_FORMAT_EXPR = '${logsFormat}';\n// The deprecated line filter (custom variable)\nexport const VAR_LINE_FILTER_DEPRECATED = 'lineFilter';\n// The new single value line filter (ad-hoc variable), results are added to VAR_LINE_FILTER_AD_HOC when \"submitted\"\nexport const VAR_LINE_FILTER = 'lineFilterV2';\nexport const VAR_LINE_FILTER_EXPR = '${lineFilterV2}';\n// The new multi value line filter (ad-hoc variable)\nexport const VAR_LINE_FILTERS = 'lineFilters';\nexport const VAR_LINE_FILTERS_EXPR = '${lineFilters}';\nexport const LOG_STREAM_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\n// Same as the LOG_STREAM_SELECTOR_EXPR, but without the fields as they will need to be built manually to exclude the current filter value\nexport const DETECTED_FIELD_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${PENDING_FIELDS_EXPR}`;\nexport const DETECTED_FIELD_AND_METADATA_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${PENDING_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${PENDING_FIELDS_EXPR}`;\nexport const DETECTED_METADATA_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${PENDING_FIELDS_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const DETECTED_LEVELS_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${PENDING_FIELDS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const PATTERNS_SAMPLE_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LOGS_FORMAT_EXPR}`;\nexport const PRETTY_LOG_STREAM_SELECTOR_EXPR = `${VAR_LABELS_EXPR} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const EXPLORATION_DS = { uid: VAR_DATASOURCE_EXPR };\nexport const ALL_VARIABLE_VALUE = '$__all';\nexport const LEVEL_VARIABLE_VALUE = 'detected_level';\nexport const SERVICE_NAME = 'service_name';\nexport const SERVICE_UI_LABEL = 'service';\nexport const VAR_AGGREGATED_METRICS = 'var_aggregated_metrics';\nexport const VAR_AGGREGATED_METRICS_EXPR = '${var_aggregated_metrics}';\nexport const EMPTY_VARIABLE_VALUE = '\"\"';\n\n// Delimiter used at the start of a label value to denote user input that should not be escaped\n// @todo we need ad-hoc-filter meta that is persisted in the URL so we can clean this up.\nexport const USER_INPUT_ADHOC_VALUE_PREFIX = '__CVΩ__';\nexport function stripAdHocFilterUserInputPrefix(value = '') {\n  if (value.startsWith(USER_INPUT_ADHOC_VALUE_PREFIX)) {\n    return value.substring(USER_INPUT_ADHOC_VALUE_PREFIX.length);\n  }\n  return value;\n}\nexport function isAdHocFilterValueUserInput(value = '') {\n  return value.startsWith(USER_INPUT_ADHOC_VALUE_PREFIX);\n}\nexport function addAdHocFilterUserInputPrefix(value = '') {\n  return USER_INPUT_ADHOC_VALUE_PREFIX + value;\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__6089__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7781__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8531__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__2007__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3241__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__5959__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8398__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__200__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3806__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7694__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1269__;", "/**\nThe default maximum length of a `TreeBuffer` node.\n*/\nconst DefaultBufferLength = 1024;\nlet nextPropID = 0;\nclass Range {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n    }\n}\n/**\nEach [node type](#common.NodeType) or [individual tree](#common.Tree)\ncan have metadata associated with it in props. Instances of this\nclass represent prop names.\n*/\nclass NodeProp {\n    /**\n    Create a new node prop type.\n    */\n    constructor(config = {}) {\n        this.id = nextPropID++;\n        this.perNode = !!config.perNode;\n        this.deserialize = config.deserialize || (() => {\n            throw new Error(\"This node type doesn't define a deserialize function\");\n        });\n    }\n    /**\n    This is meant to be used with\n    [`NodeSet.extend`](#common.NodeSet.extend) or\n    [`LRParser.configure`](#lr.ParserConfig.props) to compute\n    prop values for each node type in the set. Takes a [match\n    object](#common.NodeType^match) or function that returns undefined\n    if the node type doesn't get this prop, and the prop's value if\n    it does.\n    */\n    add(match) {\n        if (this.perNode)\n            throw new RangeError(\"Can't add per-node props to node types\");\n        if (typeof match != \"function\")\n            match = NodeType.match(match);\n        return (type) => {\n            let result = match(type);\n            return result === undefined ? null : [this, result];\n        };\n    }\n}\n/**\nProp that is used to describe matching delimiters. For opening\ndelimiters, this holds an array of node names (written as a\nspace-separated string when declaring this prop in a grammar)\nfor the node types of closing delimiters that match it.\n*/\nNodeProp.closedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nThe inverse of [`closedBy`](#common.NodeProp^closedBy). This is\nattached to closing delimiters, holding an array of node names\nof types of matching opening delimiters.\n*/\nNodeProp.openedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nUsed to assign node types to groups (for example, all node\ntypes that represent an expression could be tagged with an\n`\"Expression\"` group).\n*/\nNodeProp.group = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nAttached to nodes to indicate these should be\n[displayed](https://codemirror.net/docs/ref/#language.syntaxTree)\nin a bidirectional text isolate, so that direction-neutral\ncharacters on their sides don't incorrectly get associated with\nsurrounding text. You'll generally want to set this for nodes\nthat contain arbitrary text, like strings and comments, and for\nnodes that appear _inside_ arbitrary text, like HTML tags. When\nnot given a value, in a grammar declaration, defaults to\n`\"auto\"`.\n*/\nNodeProp.isolate = new NodeProp({ deserialize: value => {\n        if (value && value != \"rtl\" && value != \"ltr\" && value != \"auto\")\n            throw new RangeError(\"Invalid value for isolate: \" + value);\n        return value || \"auto\";\n    } });\n/**\nThe hash of the [context](#lr.ContextTracker.constructor)\nthat the node was parsed in, if any. Used to limit reuse of\ncontextual nodes.\n*/\nNodeProp.contextHash = new NodeProp({ perNode: true });\n/**\nThe distance beyond the end of the node that the tokenizer\nlooked ahead for any of the tokens inside the node. (The LR\nparser only stores this when it is larger than 25, for\nefficiency reasons.)\n*/\nNodeProp.lookAhead = new NodeProp({ perNode: true });\n/**\nThis per-node prop is used to replace a given node, or part of a\nnode, with another tree. This is useful to include trees from\ndifferent languages in mixed-language parsers.\n*/\nNodeProp.mounted = new NodeProp({ perNode: true });\n/**\nA mounted tree, which can be [stored](#common.NodeProp^mounted) on\na tree node to indicate that parts of its content are\nrepresented by another tree.\n*/\nclass MountedTree {\n    constructor(\n    /**\n    The inner tree.\n    */\n    tree, \n    /**\n    If this is null, this tree replaces the entire node (it will\n    be included in the regular iteration instead of its host\n    node). If not, only the given ranges are considered to be\n    covered by this tree. This is used for trees that are mixed in\n    a way that isn't strictly hierarchical. Such mounted trees are\n    only entered by [`resolveInner`](#common.Tree.resolveInner)\n    and [`enter`](#common.SyntaxNode.enter).\n    */\n    overlay, \n    /**\n    The parser used to create this subtree.\n    */\n    parser) {\n        this.tree = tree;\n        this.overlay = overlay;\n        this.parser = parser;\n    }\n    /**\n    @internal\n    */\n    static get(tree) {\n        return tree && tree.props && tree.props[NodeProp.mounted.id];\n    }\n}\nconst noProps = Object.create(null);\n/**\nEach node in a syntax tree has a node type associated with it.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the node type. Not necessarily unique, but if the\n    grammar was written properly, different node types with the\n    same name within a node set should play the same semantic\n    role.\n    */\n    name, \n    /**\n    @internal\n    */\n    props, \n    /**\n    The id of this node in its set. Corresponds to the term ids\n    used in the parser.\n    */\n    id, \n    /**\n    @internal\n    */\n    flags = 0) {\n        this.name = name;\n        this.props = props;\n        this.id = id;\n        this.flags = flags;\n    }\n    /**\n    Define a node type.\n    */\n    static define(spec) {\n        let props = spec.props && spec.props.length ? Object.create(null) : noProps;\n        let flags = (spec.top ? 1 /* NodeFlag.Top */ : 0) | (spec.skipped ? 2 /* NodeFlag.Skipped */ : 0) |\n            (spec.error ? 4 /* NodeFlag.Error */ : 0) | (spec.name == null ? 8 /* NodeFlag.Anonymous */ : 0);\n        let type = new NodeType(spec.name || \"\", props, spec.id, flags);\n        if (spec.props)\n            for (let src of spec.props) {\n                if (!Array.isArray(src))\n                    src = src(type);\n                if (src) {\n                    if (src[0].perNode)\n                        throw new RangeError(\"Can't store a per-node prop on a node type\");\n                    props[src[0].id] = src[1];\n                }\n            }\n        return type;\n    }\n    /**\n    Retrieves a node prop for this type. Will return `undefined` if\n    the prop isn't present on this node.\n    */\n    prop(prop) { return this.props[prop.id]; }\n    /**\n    True when this is the top node of a grammar.\n    */\n    get isTop() { return (this.flags & 1 /* NodeFlag.Top */) > 0; }\n    /**\n    True when this node is produced by a skip rule.\n    */\n    get isSkipped() { return (this.flags & 2 /* NodeFlag.Skipped */) > 0; }\n    /**\n    Indicates whether this is an error node.\n    */\n    get isError() { return (this.flags & 4 /* NodeFlag.Error */) > 0; }\n    /**\n    When true, this node type doesn't correspond to a user-declared\n    named node, for example because it is used to cache repetition.\n    */\n    get isAnonymous() { return (this.flags & 8 /* NodeFlag.Anonymous */) > 0; }\n    /**\n    Returns true when this node's name or one of its\n    [groups](#common.NodeProp^group) matches the given string.\n    */\n    is(name) {\n        if (typeof name == 'string') {\n            if (this.name == name)\n                return true;\n            let group = this.prop(NodeProp.group);\n            return group ? group.indexOf(name) > -1 : false;\n        }\n        return this.id == name;\n    }\n    /**\n    Create a function from node types to arbitrary values by\n    specifying an object whose property names are node or\n    [group](#common.NodeProp^group) names. Often useful with\n    [`NodeProp.add`](#common.NodeProp.add). You can put multiple\n    names, separated by spaces, in a single property name to map\n    multiple node names to a single value.\n    */\n    static match(map) {\n        let direct = Object.create(null);\n        for (let prop in map)\n            for (let name of prop.split(\" \"))\n                direct[name] = map[prop];\n        return (node) => {\n            for (let groups = node.prop(NodeProp.group), i = -1; i < (groups ? groups.length : 0); i++) {\n                let found = direct[i < 0 ? node.name : groups[i]];\n                if (found)\n                    return found;\n            }\n        };\n    }\n}\n/**\nAn empty dummy node type to use when no actual type is available.\n*/\nNodeType.none = new NodeType(\"\", Object.create(null), 0, 8 /* NodeFlag.Anonymous */);\n/**\nA node set holds a collection of node types. It is used to\ncompactly represent trees by storing their type ids, rather than a\nfull pointer to the type object, in a numeric array. Each parser\n[has](#lr.LRParser.nodeSet) a node set, and [tree\nbuffers](#common.TreeBuffer) can only store collections of nodes\nfrom the same set. A set can have a maximum of 2**16 (65536) node\ntypes in it, so that the ids fit into 16-bit typed array slots.\n*/\nclass NodeSet {\n    /**\n    Create a set with the given types. The `id` property of each\n    type should correspond to its position within the array.\n    */\n    constructor(\n    /**\n    The node types in this set, by id.\n    */\n    types) {\n        this.types = types;\n        for (let i = 0; i < types.length; i++)\n            if (types[i].id != i)\n                throw new RangeError(\"Node type ids should correspond to array positions when creating a node set\");\n    }\n    /**\n    Create a copy of this set with some node properties added. The\n    arguments to this method can be created with\n    [`NodeProp.add`](#common.NodeProp.add).\n    */\n    extend(...props) {\n        let newTypes = [];\n        for (let type of this.types) {\n            let newProps = null;\n            for (let source of props) {\n                let add = source(type);\n                if (add) {\n                    if (!newProps)\n                        newProps = Object.assign({}, type.props);\n                    newProps[add[0].id] = add[1];\n                }\n            }\n            newTypes.push(newProps ? new NodeType(type.name, newProps, type.id, type.flags) : type);\n        }\n        return new NodeSet(newTypes);\n    }\n}\nconst CachedNode = new WeakMap(), CachedInnerNode = new WeakMap();\n/**\nOptions that control iteration. Can be combined with the `|`\noperator to enable multiple ones.\n*/\nvar IterMode;\n(function (IterMode) {\n    /**\n    When enabled, iteration will only visit [`Tree`](#common.Tree)\n    objects, not nodes packed into\n    [`TreeBuffer`](#common.TreeBuffer)s.\n    */\n    IterMode[IterMode[\"ExcludeBuffers\"] = 1] = \"ExcludeBuffers\";\n    /**\n    Enable this to make iteration include anonymous nodes (such as\n    the nodes that wrap repeated grammar constructs into a balanced\n    tree).\n    */\n    IterMode[IterMode[\"IncludeAnonymous\"] = 2] = \"IncludeAnonymous\";\n    /**\n    By default, regular [mounted](#common.NodeProp^mounted) nodes\n    replace their base node in iteration. Enable this to ignore them\n    instead.\n    */\n    IterMode[IterMode[\"IgnoreMounts\"] = 4] = \"IgnoreMounts\";\n    /**\n    This option only applies in\n    [`enter`](#common.SyntaxNode.enter)-style methods. It tells the\n    library to not enter mounted overlays if one covers the given\n    position.\n    */\n    IterMode[IterMode[\"IgnoreOverlays\"] = 8] = \"IgnoreOverlays\";\n})(IterMode || (IterMode = {}));\n/**\nA piece of syntax tree. There are two ways to approach these\ntrees: the way they are actually stored in memory, and the\nconvenient way.\n\nSyntax trees are stored as a tree of `Tree` and `TreeBuffer`\nobjects. By packing detail information into `TreeBuffer` leaf\nnodes, the representation is made a lot more memory-efficient.\n\nHowever, when you want to actually work with tree nodes, this\nrepresentation is very awkward, so most client code will want to\nuse the [`TreeCursor`](#common.TreeCursor) or\n[`SyntaxNode`](#common.SyntaxNode) interface instead, which provides\na view on some part of this data structure, and can be used to\nmove around to adjacent nodes.\n*/\nclass Tree {\n    /**\n    Construct a new tree. See also [`Tree.build`](#common.Tree^build).\n    */\n    constructor(\n    /**\n    The type of the top node.\n    */\n    type, \n    /**\n    This node's child nodes.\n    */\n    children, \n    /**\n    The positions (offsets relative to the start of this tree) of\n    the children.\n    */\n    positions, \n    /**\n    The total length of this tree\n    */\n    length, \n    /**\n    Per-node [node props](#common.NodeProp) to associate with this node.\n    */\n    props) {\n        this.type = type;\n        this.children = children;\n        this.positions = positions;\n        this.length = length;\n        /**\n        @internal\n        */\n        this.props = null;\n        if (props && props.length) {\n            this.props = Object.create(null);\n            for (let [prop, value] of props)\n                this.props[typeof prop == \"number\" ? prop : prop.id] = value;\n        }\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let mounted = MountedTree.get(this);\n        if (mounted && !mounted.overlay)\n            return mounted.tree.toString();\n        let children = \"\";\n        for (let ch of this.children) {\n            let str = ch.toString();\n            if (str) {\n                if (children)\n                    children += \",\";\n                children += str;\n            }\n        }\n        return !this.type.name ? children :\n            (/\\W/.test(this.type.name) && !this.type.isError ? JSON.stringify(this.type.name) : this.type.name) +\n                (children.length ? \"(\" + children + \")\" : \"\");\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) positioned at the top of\n    the tree. Mode can be used to [control](#common.IterMode) which\n    nodes the cursor visits.\n    */\n    cursor(mode = 0) {\n        return new TreeCursor(this.topNode, mode);\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) pointing into this tree\n    at the given position and side (see\n    [`moveTo`](#common.TreeCursor.moveTo).\n    */\n    cursorAt(pos, side = 0, mode = 0) {\n        let scope = CachedNode.get(this) || this.topNode;\n        let cursor = new TreeCursor(scope);\n        cursor.moveTo(pos, side);\n        CachedNode.set(this, cursor._tree);\n        return cursor;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) object for the top of the\n    tree.\n    */\n    get topNode() {\n        return new TreeNode(this, 0, 0, null);\n    }\n    /**\n    Get the [syntax node](#common.SyntaxNode) at the given position.\n    If `side` is -1, this will move into nodes that end at the\n    position. If 1, it'll move into nodes that start at the\n    position. With 0, it'll only enter nodes that cover the position\n    from both sides.\n    \n    Note that this will not enter\n    [overlays](#common.MountedTree.overlay), and you often want\n    [`resolveInner`](#common.Tree.resolveInner) instead.\n    */\n    resolve(pos, side = 0) {\n        let node = resolveNode(CachedNode.get(this) || this.topNode, pos, side, false);\n        CachedNode.set(this, node);\n        return node;\n    }\n    /**\n    Like [`resolve`](#common.Tree.resolve), but will enter\n    [overlaid](#common.MountedTree.overlay) nodes, producing a syntax node\n    pointing into the innermost overlaid tree at the given position\n    (with parent links going through all parent structure, including\n    the host trees).\n    */\n    resolveInner(pos, side = 0) {\n        let node = resolveNode(CachedInnerNode.get(this) || this.topNode, pos, side, true);\n        CachedInnerNode.set(this, node);\n        return node;\n    }\n    /**\n    In some situations, it can be useful to iterate through all\n    nodes around a position, including those in overlays that don't\n    directly cover the position. This method gives you an iterator\n    that will produce all nodes, from small to big, around the given\n    position.\n    */\n    resolveStack(pos, side = 0) {\n        return stackIterator(this, pos, side);\n    }\n    /**\n    Iterate over the tree and its children, calling `enter` for any\n    node that touches the `from`/`to` region (if given) before\n    running over such a node's children, and `leave` (if given) when\n    leaving the node. When `enter` returns `false`, that node will\n    not have its children iterated over (or `leave` called).\n    */\n    iterate(spec) {\n        let { enter, leave, from = 0, to = this.length } = spec;\n        let mode = spec.mode || 0, anon = (mode & IterMode.IncludeAnonymous) > 0;\n        for (let c = this.cursor(mode | IterMode.IncludeAnonymous);;) {\n            let entered = false;\n            if (c.from <= to && c.to >= from && (!anon && c.type.isAnonymous || enter(c) !== false)) {\n                if (c.firstChild())\n                    continue;\n                entered = true;\n            }\n            for (;;) {\n                if (entered && leave && (anon || !c.type.isAnonymous))\n                    leave(c);\n                if (c.nextSibling())\n                    break;\n                if (!c.parent())\n                    return;\n                entered = true;\n            }\n        }\n    }\n    /**\n    Get the value of the given [node prop](#common.NodeProp) for this\n    node. Works with both per-node and per-type props.\n    */\n    prop(prop) {\n        return !prop.perNode ? this.type.prop(prop) : this.props ? this.props[prop.id] : undefined;\n    }\n    /**\n    Returns the node's [per-node props](#common.NodeProp.perNode) in a\n    format that can be passed to the [`Tree`](#common.Tree)\n    constructor.\n    */\n    get propValues() {\n        let result = [];\n        if (this.props)\n            for (let id in this.props)\n                result.push([+id, this.props[id]]);\n        return result;\n    }\n    /**\n    Balance the direct children of this tree, producing a copy of\n    which may have children grouped into subtrees with type\n    [`NodeType.none`](#common.NodeType^none).\n    */\n    balance(config = {}) {\n        return this.children.length <= 8 /* Balance.BranchFactor */ ? this :\n            balanceRange(NodeType.none, this.children, this.positions, 0, this.children.length, 0, this.length, (children, positions, length) => new Tree(this.type, children, positions, length, this.propValues), config.makeTree || ((children, positions, length) => new Tree(NodeType.none, children, positions, length)));\n    }\n    /**\n    Build a tree from a postfix-ordered buffer of node information,\n    or a cursor over such a buffer.\n    */\n    static build(data) { return buildTree(data); }\n}\n/**\nThe empty tree\n*/\nTree.empty = new Tree(NodeType.none, [], [], 0);\nclass FlatBufferCursor {\n    constructor(buffer, index) {\n        this.buffer = buffer;\n        this.index = index;\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    get pos() { return this.index; }\n    next() { this.index -= 4; }\n    fork() { return new FlatBufferCursor(this.buffer, this.index); }\n}\n/**\nTree buffers contain (type, start, end, endIndex) quads for each\nnode. In such a buffer, nodes are stored in prefix order (parents\nbefore children, with the endIndex of the parent indicating which\nchildren belong to it).\n*/\nclass TreeBuffer {\n    /**\n    Create a tree buffer.\n    */\n    constructor(\n    /**\n    The buffer's content.\n    */\n    buffer, \n    /**\n    The total length of the group of nodes in the buffer.\n    */\n    length, \n    /**\n    The node set used in this buffer.\n    */\n    set) {\n        this.buffer = buffer;\n        this.length = length;\n        this.set = set;\n    }\n    /**\n    @internal\n    */\n    get type() { return NodeType.none; }\n    /**\n    @internal\n    */\n    toString() {\n        let result = [];\n        for (let index = 0; index < this.buffer.length;) {\n            result.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result.join(\",\");\n    }\n    /**\n    @internal\n    */\n    childString(index) {\n        let id = this.buffer[index], endIndex = this.buffer[index + 3];\n        let type = this.set.types[id], result = type.name;\n        if (/\\W/.test(result) && !type.isError)\n            result = JSON.stringify(result);\n        index += 4;\n        if (endIndex == index)\n            return result;\n        let children = [];\n        while (index < endIndex) {\n            children.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result + \"(\" + children.join(\",\") + \")\";\n    }\n    /**\n    @internal\n    */\n    findChild(startIndex, endIndex, dir, pos, side) {\n        let { buffer } = this, pick = -1;\n        for (let i = startIndex; i != endIndex; i = buffer[i + 3]) {\n            if (checkSide(side, pos, buffer[i + 1], buffer[i + 2])) {\n                pick = i;\n                if (dir > 0)\n                    break;\n            }\n        }\n        return pick;\n    }\n    /**\n    @internal\n    */\n    slice(startI, endI, from) {\n        let b = this.buffer;\n        let copy = new Uint16Array(endI - startI), len = 0;\n        for (let i = startI, j = 0; i < endI;) {\n            copy[j++] = b[i++];\n            copy[j++] = b[i++] - from;\n            let to = copy[j++] = b[i++] - from;\n            copy[j++] = b[i++] - startI;\n            len = Math.max(len, to);\n        }\n        return new TreeBuffer(copy, len, this.set);\n    }\n}\nfunction checkSide(side, pos, from, to) {\n    switch (side) {\n        case -2 /* Side.Before */: return from < pos;\n        case -1 /* Side.AtOrBefore */: return to >= pos && from < pos;\n        case 0 /* Side.Around */: return from < pos && to > pos;\n        case 1 /* Side.AtOrAfter */: return from <= pos && to > pos;\n        case 2 /* Side.After */: return to > pos;\n        case 4 /* Side.DontCare */: return true;\n    }\n}\nfunction resolveNode(node, pos, side, overlays) {\n    var _a;\n    // Move up to a node that actually holds the position, if possible\n    while (node.from == node.to ||\n        (side < 1 ? node.from >= pos : node.from > pos) ||\n        (side > -1 ? node.to <= pos : node.to < pos)) {\n        let parent = !overlays && node instanceof TreeNode && node.index < 0 ? null : node.parent;\n        if (!parent)\n            return node;\n        node = parent;\n    }\n    let mode = overlays ? 0 : IterMode.IgnoreOverlays;\n    // Must go up out of overlays when those do not overlap with pos\n    if (overlays)\n        for (let scan = node, parent = scan.parent; parent; scan = parent, parent = scan.parent) {\n            if (scan instanceof TreeNode && scan.index < 0 && ((_a = parent.enter(pos, side, mode)) === null || _a === void 0 ? void 0 : _a.from) != scan.from)\n                node = parent;\n        }\n    for (;;) {\n        let inner = node.enter(pos, side, mode);\n        if (!inner)\n            return node;\n        node = inner;\n    }\n}\nclass BaseNode {\n    cursor(mode = 0) { return new TreeCursor(this, mode); }\n    getChild(type, before = null, after = null) {\n        let r = getChildren(this, type, before, after);\n        return r.length ? r[0] : null;\n    }\n    getChildren(type, before = null, after = null) {\n        return getChildren(this, type, before, after);\n    }\n    resolve(pos, side = 0) {\n        return resolveNode(this, pos, side, false);\n    }\n    resolveInner(pos, side = 0) {\n        return resolveNode(this, pos, side, true);\n    }\n    matchContext(context) {\n        return matchNodeContext(this.parent, context);\n    }\n    enterUnfinishedNodesBefore(pos) {\n        let scan = this.childBefore(pos), node = this;\n        while (scan) {\n            let last = scan.lastChild;\n            if (!last || last.to != scan.to)\n                break;\n            if (last.type.isError && last.from == last.to) {\n                node = scan;\n                scan = last.prevSibling;\n            }\n            else {\n                scan = last;\n            }\n        }\n        return node;\n    }\n    get node() { return this; }\n    get next() { return this.parent; }\n}\nclass TreeNode extends BaseNode {\n    constructor(_tree, from, \n    // Index in parent node, set to -1 if the node is not a direct child of _parent.node (overlay)\n    index, _parent) {\n        super();\n        this._tree = _tree;\n        this.from = from;\n        this.index = index;\n        this._parent = _parent;\n    }\n    get type() { return this._tree.type; }\n    get name() { return this._tree.type.name; }\n    get to() { return this.from + this._tree.length; }\n    nextChild(i, dir, pos, side, mode = 0) {\n        for (let parent = this;;) {\n            for (let { children, positions } = parent._tree, e = dir > 0 ? children.length : -1; i != e; i += dir) {\n                let next = children[i], start = positions[i] + parent.from;\n                if (!checkSide(side, pos, start, start + next.length))\n                    continue;\n                if (next instanceof TreeBuffer) {\n                    if (mode & IterMode.ExcludeBuffers)\n                        continue;\n                    let index = next.findChild(0, next.buffer.length, dir, pos - start, side);\n                    if (index > -1)\n                        return new BufferNode(new BufferContext(parent, next, i, start), null, index);\n                }\n                else if ((mode & IterMode.IncludeAnonymous) || (!next.type.isAnonymous || hasChild(next))) {\n                    let mounted;\n                    if (!(mode & IterMode.IgnoreMounts) && (mounted = MountedTree.get(next)) && !mounted.overlay)\n                        return new TreeNode(mounted.tree, start, i, parent);\n                    let inner = new TreeNode(next, start, i, parent);\n                    return (mode & IterMode.IncludeAnonymous) || !inner.type.isAnonymous ? inner\n                        : inner.nextChild(dir < 0 ? next.children.length - 1 : 0, dir, pos, side);\n                }\n            }\n            if ((mode & IterMode.IncludeAnonymous) || !parent.type.isAnonymous)\n                return null;\n            if (parent.index >= 0)\n                i = parent.index + dir;\n            else\n                i = dir < 0 ? -1 : parent._parent._tree.children.length;\n            parent = parent._parent;\n            if (!parent)\n                return null;\n        }\n    }\n    get firstChild() { return this.nextChild(0, 1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.nextChild(this._tree.children.length - 1, -1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.nextChild(0, 1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.nextChild(this._tree.children.length - 1, -1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        let mounted;\n        if (!(mode & IterMode.IgnoreOverlays) && (mounted = MountedTree.get(this._tree)) && mounted.overlay) {\n            let rPos = pos - this.from;\n            for (let { from, to } of mounted.overlay) {\n                if ((side > 0 ? from <= rPos : from < rPos) &&\n                    (side < 0 ? to >= rPos : to > rPos))\n                    return new TreeNode(mounted.tree, mounted.overlay[0].from + this.from, -1, this);\n            }\n        }\n        return this.nextChild(0, 1, pos, side, mode);\n    }\n    nextSignificantParent() {\n        let val = this;\n        while (val.type.isAnonymous && val._parent)\n            val = val._parent;\n        return val;\n    }\n    get parent() {\n        return this._parent ? this._parent.nextSignificantParent() : null;\n    }\n    get nextSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index + 1, 1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get prevSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index - 1, -1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get tree() { return this._tree; }\n    toTree() { return this._tree; }\n    /**\n    @internal\n    */\n    toString() { return this._tree.toString(); }\n}\nfunction getChildren(node, type, before, after) {\n    let cur = node.cursor(), result = [];\n    if (!cur.firstChild())\n        return result;\n    if (before != null)\n        for (let found = false; !found;) {\n            found = cur.type.is(before);\n            if (!cur.nextSibling())\n                return result;\n        }\n    for (;;) {\n        if (after != null && cur.type.is(after))\n            return result;\n        if (cur.type.is(type))\n            result.push(cur.node);\n        if (!cur.nextSibling())\n            return after == null ? result : [];\n    }\n}\nfunction matchNodeContext(node, context, i = context.length - 1) {\n    for (let p = node; i >= 0; p = p.parent) {\n        if (!p)\n            return false;\n        if (!p.type.isAnonymous) {\n            if (context[i] && context[i] != p.name)\n                return false;\n            i--;\n        }\n    }\n    return true;\n}\nclass BufferContext {\n    constructor(parent, buffer, index, start) {\n        this.parent = parent;\n        this.buffer = buffer;\n        this.index = index;\n        this.start = start;\n    }\n}\nclass BufferNode extends BaseNode {\n    get name() { return this.type.name; }\n    get from() { return this.context.start + this.context.buffer.buffer[this.index + 1]; }\n    get to() { return this.context.start + this.context.buffer.buffer[this.index + 2]; }\n    constructor(context, _parent, index) {\n        super();\n        this.context = context;\n        this._parent = _parent;\n        this.index = index;\n        this.type = context.buffer.set.types[context.buffer.buffer[index]];\n    }\n    child(dir, pos, side) {\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get firstChild() { return this.child(1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.child(-1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.child(1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.child(-1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        if (mode & IterMode.ExcludeBuffers)\n            return null;\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], side > 0 ? 1 : -1, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get parent() {\n        return this._parent || this.context.parent.nextSignificantParent();\n    }\n    externalSibling(dir) {\n        return this._parent ? null : this.context.parent.nextChild(this.context.index + dir, dir, 0, 4 /* Side.DontCare */);\n    }\n    get nextSibling() {\n        let { buffer } = this.context;\n        let after = buffer.buffer[this.index + 3];\n        if (after < (this._parent ? buffer.buffer[this._parent.index + 3] : buffer.buffer.length))\n            return new BufferNode(this.context, this._parent, after);\n        return this.externalSibling(1);\n    }\n    get prevSibling() {\n        let { buffer } = this.context;\n        let parentStart = this._parent ? this._parent.index + 4 : 0;\n        if (this.index == parentStart)\n            return this.externalSibling(-1);\n        return new BufferNode(this.context, this._parent, buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n    }\n    get tree() { return null; }\n    toTree() {\n        let children = [], positions = [];\n        let { buffer } = this.context;\n        let startI = this.index + 4, endI = buffer.buffer[this.index + 3];\n        if (endI > startI) {\n            let from = buffer.buffer[this.index + 1];\n            children.push(buffer.slice(startI, endI, from));\n            positions.push(0);\n        }\n        return new Tree(this.type, children, positions, this.to - this.from);\n    }\n    /**\n    @internal\n    */\n    toString() { return this.context.buffer.childString(this.index); }\n}\nfunction iterStack(heads) {\n    if (!heads.length)\n        return null;\n    let pick = 0, picked = heads[0];\n    for (let i = 1; i < heads.length; i++) {\n        let node = heads[i];\n        if (node.from > picked.from || node.to < picked.to) {\n            picked = node;\n            pick = i;\n        }\n    }\n    let next = picked instanceof TreeNode && picked.index < 0 ? null : picked.parent;\n    let newHeads = heads.slice();\n    if (next)\n        newHeads[pick] = next;\n    else\n        newHeads.splice(pick, 1);\n    return new StackIterator(newHeads, picked);\n}\nclass StackIterator {\n    constructor(heads, node) {\n        this.heads = heads;\n        this.node = node;\n    }\n    get next() { return iterStack(this.heads); }\n}\nfunction stackIterator(tree, pos, side) {\n    let inner = tree.resolveInner(pos, side), layers = null;\n    for (let scan = inner instanceof TreeNode ? inner : inner.context.parent; scan; scan = scan.parent) {\n        if (scan.index < 0) { // This is an overlay root\n            let parent = scan.parent;\n            (layers || (layers = [inner])).push(parent.resolve(pos, side));\n            scan = parent;\n        }\n        else {\n            let mount = MountedTree.get(scan.tree);\n            // Relevant overlay branching off\n            if (mount && mount.overlay && mount.overlay[0].from <= pos && mount.overlay[mount.overlay.length - 1].to >= pos) {\n                let root = new TreeNode(mount.tree, mount.overlay[0].from + scan.from, -1, scan);\n                (layers || (layers = [inner])).push(resolveNode(root, pos, side, false));\n            }\n        }\n    }\n    return layers ? iterStack(layers) : inner;\n}\n/**\nA tree cursor object focuses on a given node in a syntax tree, and\nallows you to move to adjacent nodes.\n*/\nclass TreeCursor {\n    /**\n    Shorthand for `.type.name`.\n    */\n    get name() { return this.type.name; }\n    /**\n    @internal\n    */\n    constructor(node, \n    /**\n    @internal\n    */\n    mode = 0) {\n        this.mode = mode;\n        /**\n        @internal\n        */\n        this.buffer = null;\n        this.stack = [];\n        /**\n        @internal\n        */\n        this.index = 0;\n        this.bufferNode = null;\n        if (node instanceof TreeNode) {\n            this.yieldNode(node);\n        }\n        else {\n            this._tree = node.context.parent;\n            this.buffer = node.context;\n            for (let n = node._parent; n; n = n._parent)\n                this.stack.unshift(n.index);\n            this.bufferNode = node;\n            this.yieldBuf(node.index);\n        }\n    }\n    yieldNode(node) {\n        if (!node)\n            return false;\n        this._tree = node;\n        this.type = node.type;\n        this.from = node.from;\n        this.to = node.to;\n        return true;\n    }\n    yieldBuf(index, type) {\n        this.index = index;\n        let { start, buffer } = this.buffer;\n        this.type = type || buffer.set.types[buffer.buffer[index]];\n        this.from = start + buffer.buffer[index + 1];\n        this.to = start + buffer.buffer[index + 2];\n        return true;\n    }\n    /**\n    @internal\n    */\n    yield(node) {\n        if (!node)\n            return false;\n        if (node instanceof TreeNode) {\n            this.buffer = null;\n            return this.yieldNode(node);\n        }\n        this.buffer = node.context;\n        return this.yieldBuf(node.index, node.type);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.buffer ? this.buffer.buffer.childString(this.index) : this._tree.toString();\n    }\n    /**\n    @internal\n    */\n    enterChild(dir, pos, side) {\n        if (!this.buffer)\n            return this.yield(this._tree.nextChild(dir < 0 ? this._tree._tree.children.length - 1 : 0, dir, pos, side, this.mode));\n        let { buffer } = this.buffer;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.buffer.start, side);\n        if (index < 0)\n            return false;\n        this.stack.push(this.index);\n        return this.yieldBuf(index);\n    }\n    /**\n    Move the cursor to this node's first child. When this returns\n    false, the node has no child, and the cursor has not been moved.\n    */\n    firstChild() { return this.enterChild(1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to this node's last child.\n    */\n    lastChild() { return this.enterChild(-1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to the first child that ends after `pos`.\n    */\n    childAfter(pos) { return this.enterChild(1, pos, 2 /* Side.After */); }\n    /**\n    Move to the last child that starts before `pos`.\n    */\n    childBefore(pos) { return this.enterChild(-1, pos, -2 /* Side.Before */); }\n    /**\n    Move the cursor to the child around `pos`. If side is -1 the\n    child may end at that position, when 1 it may start there. This\n    will also enter [overlaid](#common.MountedTree.overlay)\n    [mounted](#common.NodeProp^mounted) trees unless `overlays` is\n    set to false.\n    */\n    enter(pos, side, mode = this.mode) {\n        if (!this.buffer)\n            return this.yield(this._tree.enter(pos, side, mode));\n        return mode & IterMode.ExcludeBuffers ? false : this.enterChild(1, pos, side);\n    }\n    /**\n    Move to the node's parent node, if this isn't the top node.\n    */\n    parent() {\n        if (!this.buffer)\n            return this.yieldNode((this.mode & IterMode.IncludeAnonymous) ? this._tree._parent : this._tree.parent);\n        if (this.stack.length)\n            return this.yieldBuf(this.stack.pop());\n        let parent = (this.mode & IterMode.IncludeAnonymous) ? this.buffer.parent : this.buffer.parent.nextSignificantParent();\n        this.buffer = null;\n        return this.yieldNode(parent);\n    }\n    /**\n    @internal\n    */\n    sibling(dir) {\n        if (!this.buffer)\n            return !this._tree._parent ? false\n                : this.yield(this._tree.index < 0 ? null\n                    : this._tree._parent.nextChild(this._tree.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode));\n        let { buffer } = this.buffer, d = this.stack.length - 1;\n        if (dir < 0) {\n            let parentStart = d < 0 ? 0 : this.stack[d] + 4;\n            if (this.index != parentStart)\n                return this.yieldBuf(buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n        }\n        else {\n            let after = buffer.buffer[this.index + 3];\n            if (after < (d < 0 ? buffer.buffer.length : buffer.buffer[this.stack[d] + 3]))\n                return this.yieldBuf(after);\n        }\n        return d < 0 ? this.yield(this.buffer.parent.nextChild(this.buffer.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode)) : false;\n    }\n    /**\n    Move to this node's next sibling, if any.\n    */\n    nextSibling() { return this.sibling(1); }\n    /**\n    Move to this node's previous sibling, if any.\n    */\n    prevSibling() { return this.sibling(-1); }\n    atLastNode(dir) {\n        let index, parent, { buffer } = this;\n        if (buffer) {\n            if (dir > 0) {\n                if (this.index < buffer.buffer.buffer.length)\n                    return false;\n            }\n            else {\n                for (let i = 0; i < this.index; i++)\n                    if (buffer.buffer.buffer[i + 3] < this.index)\n                        return false;\n            }\n            ({ index, parent } = buffer);\n        }\n        else {\n            ({ index, _parent: parent } = this._tree);\n        }\n        for (; parent; { index, _parent: parent } = parent) {\n            if (index > -1)\n                for (let i = index + dir, e = dir < 0 ? -1 : parent._tree.children.length; i != e; i += dir) {\n                    let child = parent._tree.children[i];\n                    if ((this.mode & IterMode.IncludeAnonymous) ||\n                        child instanceof TreeBuffer ||\n                        !child.type.isAnonymous ||\n                        hasChild(child))\n                        return false;\n                }\n        }\n        return true;\n    }\n    move(dir, enter) {\n        if (enter && this.enterChild(dir, 0, 4 /* Side.DontCare */))\n            return true;\n        for (;;) {\n            if (this.sibling(dir))\n                return true;\n            if (this.atLastNode(dir) || !this.parent())\n                return false;\n        }\n    }\n    /**\n    Move to the next node in a\n    [pre-order](https://en.wikipedia.org/wiki/Tree_traversal#Pre-order,_NLR)\n    traversal, going from a node to its first child or, if the\n    current node is empty or `enter` is false, its next sibling or\n    the next sibling of the first parent node that has one.\n    */\n    next(enter = true) { return this.move(1, enter); }\n    /**\n    Move to the next node in a last-to-first pre-order traversal. A\n    node is followed by its last child or, if it has none, its\n    previous sibling or the previous sibling of the first parent\n    node that has one.\n    */\n    prev(enter = true) { return this.move(-1, enter); }\n    /**\n    Move the cursor to the innermost node that covers `pos`. If\n    `side` is -1, it will enter nodes that end at `pos`. If it is 1,\n    it will enter nodes that start at `pos`.\n    */\n    moveTo(pos, side = 0) {\n        // Move up to a node that actually holds the position, if possible\n        while (this.from == this.to ||\n            (side < 1 ? this.from >= pos : this.from > pos) ||\n            (side > -1 ? this.to <= pos : this.to < pos))\n            if (!this.parent())\n                break;\n        // Then scan down into child nodes as far as possible\n        while (this.enterChild(1, pos, side)) { }\n        return this;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) at the cursor's current\n    position.\n    */\n    get node() {\n        if (!this.buffer)\n            return this._tree;\n        let cache = this.bufferNode, result = null, depth = 0;\n        if (cache && cache.context == this.buffer) {\n            scan: for (let index = this.index, d = this.stack.length; d >= 0;) {\n                for (let c = cache; c; c = c._parent)\n                    if (c.index == index) {\n                        if (index == this.index)\n                            return c;\n                        result = c;\n                        depth = d + 1;\n                        break scan;\n                    }\n                index = this.stack[--d];\n            }\n        }\n        for (let i = depth; i < this.stack.length; i++)\n            result = new BufferNode(this.buffer, result, this.stack[i]);\n        return this.bufferNode = new BufferNode(this.buffer, result, this.index);\n    }\n    /**\n    Get the [tree](#common.Tree) that represents the current node, if\n    any. Will return null when the node is in a [tree\n    buffer](#common.TreeBuffer).\n    */\n    get tree() {\n        return this.buffer ? null : this._tree._tree;\n    }\n    /**\n    Iterate over the current node and all its descendants, calling\n    `enter` when entering a node and `leave`, if given, when leaving\n    one. When `enter` returns `false`, any children of that node are\n    skipped, and `leave` isn't called for it.\n    */\n    iterate(enter, leave) {\n        for (let depth = 0;;) {\n            let mustLeave = false;\n            if (this.type.isAnonymous || enter(this) !== false) {\n                if (this.firstChild()) {\n                    depth++;\n                    continue;\n                }\n                if (!this.type.isAnonymous)\n                    mustLeave = true;\n            }\n            for (;;) {\n                if (mustLeave && leave)\n                    leave(this);\n                mustLeave = this.type.isAnonymous;\n                if (!depth)\n                    return;\n                if (this.nextSibling())\n                    break;\n                this.parent();\n                depth--;\n                mustLeave = true;\n            }\n        }\n    }\n    /**\n    Test whether the current node matches a given context—a sequence\n    of direct parent node names. Empty strings in the context array\n    are treated as wildcards.\n    */\n    matchContext(context) {\n        if (!this.buffer)\n            return matchNodeContext(this.node.parent, context);\n        let { buffer } = this.buffer, { types } = buffer.set;\n        for (let i = context.length - 1, d = this.stack.length - 1; i >= 0; d--) {\n            if (d < 0)\n                return matchNodeContext(this._tree, context, i);\n            let type = types[buffer.buffer[this.stack[d]]];\n            if (!type.isAnonymous) {\n                if (context[i] && context[i] != type.name)\n                    return false;\n                i--;\n            }\n        }\n        return true;\n    }\n}\nfunction hasChild(tree) {\n    return tree.children.some(ch => ch instanceof TreeBuffer || !ch.type.isAnonymous || hasChild(ch));\n}\nfunction buildTree(data) {\n    var _a;\n    let { buffer, nodeSet, maxBufferLength = DefaultBufferLength, reused = [], minRepeatType = nodeSet.types.length } = data;\n    let cursor = Array.isArray(buffer) ? new FlatBufferCursor(buffer, buffer.length) : buffer;\n    let types = nodeSet.types;\n    let contextHash = 0, lookAhead = 0;\n    function takeNode(parentStart, minPos, children, positions, inRepeat, depth) {\n        let { id, start, end, size } = cursor;\n        let lookAheadAtStart = lookAhead, contextAtStart = contextHash;\n        while (size < 0) {\n            cursor.next();\n            if (size == -1 /* SpecialRecord.Reuse */) {\n                let node = reused[id];\n                children.push(node);\n                positions.push(start - parentStart);\n                return;\n            }\n            else if (size == -3 /* SpecialRecord.ContextChange */) { // Context change\n                contextHash = id;\n                return;\n            }\n            else if (size == -4 /* SpecialRecord.LookAhead */) {\n                lookAhead = id;\n                return;\n            }\n            else {\n                throw new RangeError(`Unrecognized record size: ${size}`);\n            }\n        }\n        let type = types[id], node, buffer;\n        let startPos = start - parentStart;\n        if (end - start <= maxBufferLength && (buffer = findBufferSize(cursor.pos - minPos, inRepeat))) {\n            // Small enough for a buffer, and no reused nodes inside\n            let data = new Uint16Array(buffer.size - buffer.skip);\n            let endPos = cursor.pos - buffer.size, index = data.length;\n            while (cursor.pos > endPos)\n                index = copyToBuffer(buffer.start, data, index);\n            node = new TreeBuffer(data, end - buffer.start, nodeSet);\n            startPos = buffer.start - parentStart;\n        }\n        else { // Make it a node\n            let endPos = cursor.pos - size;\n            cursor.next();\n            let localChildren = [], localPositions = [];\n            let localInRepeat = id >= minRepeatType ? id : -1;\n            let lastGroup = 0, lastEnd = end;\n            while (cursor.pos > endPos) {\n                if (localInRepeat >= 0 && cursor.id == localInRepeat && cursor.size >= 0) {\n                    if (cursor.end <= lastEnd - maxBufferLength) {\n                        makeRepeatLeaf(localChildren, localPositions, start, lastGroup, cursor.end, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n                        lastGroup = localChildren.length;\n                        lastEnd = cursor.end;\n                    }\n                    cursor.next();\n                }\n                else if (depth > 2500 /* CutOff.Depth */) {\n                    takeFlatNode(start, endPos, localChildren, localPositions);\n                }\n                else {\n                    takeNode(start, endPos, localChildren, localPositions, localInRepeat, depth + 1);\n                }\n            }\n            if (localInRepeat >= 0 && lastGroup > 0 && lastGroup < localChildren.length)\n                makeRepeatLeaf(localChildren, localPositions, start, lastGroup, start, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n            localChildren.reverse();\n            localPositions.reverse();\n            if (localInRepeat > -1 && lastGroup > 0) {\n                let make = makeBalanced(type, contextAtStart);\n                node = balanceRange(type, localChildren, localPositions, 0, localChildren.length, 0, end - start, make, make);\n            }\n            else {\n                node = makeTree(type, localChildren, localPositions, end - start, lookAheadAtStart - end, contextAtStart);\n            }\n        }\n        children.push(node);\n        positions.push(startPos);\n    }\n    function takeFlatNode(parentStart, minPos, children, positions) {\n        let nodes = []; // Temporary, inverted array of leaf nodes found, with absolute positions\n        let nodeCount = 0, stopAt = -1;\n        while (cursor.pos > minPos) {\n            let { id, start, end, size } = cursor;\n            if (size > 4) { // Not a leaf\n                cursor.next();\n            }\n            else if (stopAt > -1 && start < stopAt) {\n                break;\n            }\n            else {\n                if (stopAt < 0)\n                    stopAt = end - maxBufferLength;\n                nodes.push(id, start, end);\n                nodeCount++;\n                cursor.next();\n            }\n        }\n        if (nodeCount) {\n            let buffer = new Uint16Array(nodeCount * 4);\n            let start = nodes[nodes.length - 2];\n            for (let i = nodes.length - 3, j = 0; i >= 0; i -= 3) {\n                buffer[j++] = nodes[i];\n                buffer[j++] = nodes[i + 1] - start;\n                buffer[j++] = nodes[i + 2] - start;\n                buffer[j++] = j;\n            }\n            children.push(new TreeBuffer(buffer, nodes[2] - start, nodeSet));\n            positions.push(start - parentStart);\n        }\n    }\n    function makeBalanced(type, contextHash) {\n        return (children, positions, length) => {\n            let lookAhead = 0, lastI = children.length - 1, last, lookAheadProp;\n            if (lastI >= 0 && (last = children[lastI]) instanceof Tree) {\n                if (!lastI && last.type == type && last.length == length)\n                    return last;\n                if (lookAheadProp = last.prop(NodeProp.lookAhead))\n                    lookAhead = positions[lastI] + last.length + lookAheadProp;\n            }\n            return makeTree(type, children, positions, length, lookAhead, contextHash);\n        };\n    }\n    function makeRepeatLeaf(children, positions, base, i, from, to, type, lookAhead, contextHash) {\n        let localChildren = [], localPositions = [];\n        while (children.length > i) {\n            localChildren.push(children.pop());\n            localPositions.push(positions.pop() + base - from);\n        }\n        children.push(makeTree(nodeSet.types[type], localChildren, localPositions, to - from, lookAhead - to, contextHash));\n        positions.push(from - base);\n    }\n    function makeTree(type, children, positions, length, lookAhead, contextHash, props) {\n        if (contextHash) {\n            let pair = [NodeProp.contextHash, contextHash];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        if (lookAhead > 25) {\n            let pair = [NodeProp.lookAhead, lookAhead];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        return new Tree(type, children, positions, length, props);\n    }\n    function findBufferSize(maxSize, inRepeat) {\n        // Scan through the buffer to find previous siblings that fit\n        // together in a TreeBuffer, and don't contain any reused nodes\n        // (which can't be stored in a buffer).\n        // If `inRepeat` is > -1, ignore node boundaries of that type for\n        // nesting, but make sure the end falls either at the start\n        // (`maxSize`) or before such a node.\n        let fork = cursor.fork();\n        let size = 0, start = 0, skip = 0, minStart = fork.end - maxBufferLength;\n        let result = { size: 0, start: 0, skip: 0 };\n        scan: for (let minPos = fork.pos - maxSize; fork.pos > minPos;) {\n            let nodeSize = fork.size;\n            // Pretend nested repeat nodes of the same type don't exist\n            if (fork.id == inRepeat && nodeSize >= 0) {\n                // Except that we store the current state as a valid return\n                // value.\n                result.size = size;\n                result.start = start;\n                result.skip = skip;\n                skip += 4;\n                size += 4;\n                fork.next();\n                continue;\n            }\n            let startPos = fork.pos - nodeSize;\n            if (nodeSize < 0 || startPos < minPos || fork.start < minStart)\n                break;\n            let localSkipped = fork.id >= minRepeatType ? 4 : 0;\n            let nodeStart = fork.start;\n            fork.next();\n            while (fork.pos > startPos) {\n                if (fork.size < 0) {\n                    if (fork.size == -3 /* SpecialRecord.ContextChange */)\n                        localSkipped += 4;\n                    else\n                        break scan;\n                }\n                else if (fork.id >= minRepeatType) {\n                    localSkipped += 4;\n                }\n                fork.next();\n            }\n            start = nodeStart;\n            size += nodeSize;\n            skip += localSkipped;\n        }\n        if (inRepeat < 0 || size == maxSize) {\n            result.size = size;\n            result.start = start;\n            result.skip = skip;\n        }\n        return result.size > 4 ? result : undefined;\n    }\n    function copyToBuffer(bufferStart, buffer, index) {\n        let { id, start, end, size } = cursor;\n        cursor.next();\n        if (size >= 0 && id < minRepeatType) {\n            let startIndex = index;\n            if (size > 4) {\n                let endPos = cursor.pos - (size - 4);\n                while (cursor.pos > endPos)\n                    index = copyToBuffer(bufferStart, buffer, index);\n            }\n            buffer[--index] = startIndex;\n            buffer[--index] = end - bufferStart;\n            buffer[--index] = start - bufferStart;\n            buffer[--index] = id;\n        }\n        else if (size == -3 /* SpecialRecord.ContextChange */) {\n            contextHash = id;\n        }\n        else if (size == -4 /* SpecialRecord.LookAhead */) {\n            lookAhead = id;\n        }\n        return index;\n    }\n    let children = [], positions = [];\n    while (cursor.pos > 0)\n        takeNode(data.start || 0, data.bufferStart || 0, children, positions, -1, 0);\n    let length = (_a = data.length) !== null && _a !== void 0 ? _a : (children.length ? positions[0] + children[0].length : 0);\n    return new Tree(types[data.topID], children.reverse(), positions.reverse(), length);\n}\nconst nodeSizeCache = new WeakMap;\nfunction nodeSize(balanceType, node) {\n    if (!balanceType.isAnonymous || node instanceof TreeBuffer || node.type != balanceType)\n        return 1;\n    let size = nodeSizeCache.get(node);\n    if (size == null) {\n        size = 1;\n        for (let child of node.children) {\n            if (child.type != balanceType || !(child instanceof Tree)) {\n                size = 1;\n                break;\n            }\n            size += nodeSize(balanceType, child);\n        }\n        nodeSizeCache.set(node, size);\n    }\n    return size;\n}\nfunction balanceRange(\n// The type the balanced tree's inner nodes.\nbalanceType, \n// The direct children and their positions\nchildren, positions, \n// The index range in children/positions to use\nfrom, to, \n// The start position of the nodes, relative to their parent.\nstart, \n// Length of the outer node\nlength, \n// Function to build the top node of the balanced tree\nmkTop, \n// Function to build internal nodes for the balanced tree\nmkTree) {\n    let total = 0;\n    for (let i = from; i < to; i++)\n        total += nodeSize(balanceType, children[i]);\n    let maxChild = Math.ceil((total * 1.5) / 8 /* Balance.BranchFactor */);\n    let localChildren = [], localPositions = [];\n    function divide(children, positions, from, to, offset) {\n        for (let i = from; i < to;) {\n            let groupFrom = i, groupStart = positions[i], groupSize = nodeSize(balanceType, children[i]);\n            i++;\n            for (; i < to; i++) {\n                let nextSize = nodeSize(balanceType, children[i]);\n                if (groupSize + nextSize >= maxChild)\n                    break;\n                groupSize += nextSize;\n            }\n            if (i == groupFrom + 1) {\n                if (groupSize > maxChild) {\n                    let only = children[groupFrom]; // Only trees can have a size > 1\n                    divide(only.children, only.positions, 0, only.children.length, positions[groupFrom] + offset);\n                    continue;\n                }\n                localChildren.push(children[groupFrom]);\n            }\n            else {\n                let length = positions[i - 1] + children[i - 1].length - groupStart;\n                localChildren.push(balanceRange(balanceType, children, positions, groupFrom, i, groupStart, length, null, mkTree));\n            }\n            localPositions.push(groupStart + offset - start);\n        }\n    }\n    divide(children, positions, from, to, 0);\n    return (mkTop || mkTree)(localChildren, localPositions, length);\n}\n/**\nProvides a way to associate values with pieces of trees. As long\nas that part of the tree is reused, the associated values can be\nretrieved from an updated tree.\n*/\nclass NodeWeakMap {\n    constructor() {\n        this.map = new WeakMap();\n    }\n    setBuffer(buffer, index, value) {\n        let inner = this.map.get(buffer);\n        if (!inner)\n            this.map.set(buffer, inner = new Map);\n        inner.set(index, value);\n    }\n    getBuffer(buffer, index) {\n        let inner = this.map.get(buffer);\n        return inner && inner.get(index);\n    }\n    /**\n    Set the value for this syntax node.\n    */\n    set(node, value) {\n        if (node instanceof BufferNode)\n            this.setBuffer(node.context.buffer, node.index, value);\n        else if (node instanceof TreeNode)\n            this.map.set(node.tree, value);\n    }\n    /**\n    Retrieve value for this syntax node, if it exists in the map.\n    */\n    get(node) {\n        return node instanceof BufferNode ? this.getBuffer(node.context.buffer, node.index)\n            : node instanceof TreeNode ? this.map.get(node.tree) : undefined;\n    }\n    /**\n    Set the value for the node that a cursor currently points to.\n    */\n    cursorSet(cursor, value) {\n        if (cursor.buffer)\n            this.setBuffer(cursor.buffer.buffer, cursor.index, value);\n        else\n            this.map.set(cursor.tree, value);\n    }\n    /**\n    Retrieve the value for the node that a cursor currently points\n    to.\n    */\n    cursorGet(cursor) {\n        return cursor.buffer ? this.getBuffer(cursor.buffer.buffer, cursor.index) : this.map.get(cursor.tree);\n    }\n}\n\n/**\nTree fragments are used during [incremental\nparsing](#common.Parser.startParse) to track parts of old trees\nthat can be reused in a new parse. An array of fragments is used\nto track regions of an old tree whose nodes might be reused in new\nparses. Use the static\n[`applyChanges`](#common.TreeFragment^applyChanges) method to\nupdate fragments for document changes.\n*/\nclass TreeFragment {\n    /**\n    Construct a tree fragment. You'll usually want to use\n    [`addTree`](#common.TreeFragment^addTree) and\n    [`applyChanges`](#common.TreeFragment^applyChanges) instead of\n    calling this directly.\n    */\n    constructor(\n    /**\n    The start of the unchanged range pointed to by this fragment.\n    This refers to an offset in the _updated_ document (as opposed\n    to the original tree).\n    */\n    from, \n    /**\n    The end of the unchanged range.\n    */\n    to, \n    /**\n    The tree that this fragment is based on.\n    */\n    tree, \n    /**\n    The offset between the fragment's tree and the document that\n    this fragment can be used against. Add this when going from\n    document to tree positions, subtract it to go from tree to\n    document positions.\n    */\n    offset, openStart = false, openEnd = false) {\n        this.from = from;\n        this.to = to;\n        this.tree = tree;\n        this.offset = offset;\n        this.open = (openStart ? 1 /* Open.Start */ : 0) | (openEnd ? 2 /* Open.End */ : 0);\n    }\n    /**\n    Whether the start of the fragment represents the start of a\n    parse, or the end of a change. (In the second case, it may not\n    be safe to reuse some nodes at the start, depending on the\n    parsing algorithm.)\n    */\n    get openStart() { return (this.open & 1 /* Open.Start */) > 0; }\n    /**\n    Whether the end of the fragment represents the end of a\n    full-document parse, or the start of a change.\n    */\n    get openEnd() { return (this.open & 2 /* Open.End */) > 0; }\n    /**\n    Create a set of fragments from a freshly parsed tree, or update\n    an existing set of fragments by replacing the ones that overlap\n    with a tree with content from the new tree. When `partial` is\n    true, the parse is treated as incomplete, and the resulting\n    fragment has [`openEnd`](#common.TreeFragment.openEnd) set to\n    true.\n    */\n    static addTree(tree, fragments = [], partial = false) {\n        let result = [new TreeFragment(0, tree.length, tree, 0, false, partial)];\n        for (let f of fragments)\n            if (f.to > tree.length)\n                result.push(f);\n        return result;\n    }\n    /**\n    Apply a set of edits to an array of fragments, removing or\n    splitting fragments as necessary to remove edited ranges, and\n    adjusting offsets for fragments that moved.\n    */\n    static applyChanges(fragments, changes, minGap = 128) {\n        if (!changes.length)\n            return fragments;\n        let result = [];\n        let fI = 1, nextF = fragments.length ? fragments[0] : null;\n        for (let cI = 0, pos = 0, off = 0;; cI++) {\n            let nextC = cI < changes.length ? changes[cI] : null;\n            let nextPos = nextC ? nextC.fromA : 1e9;\n            if (nextPos - pos >= minGap)\n                while (nextF && nextF.from < nextPos) {\n                    let cut = nextF;\n                    if (pos >= cut.from || nextPos <= cut.to || off) {\n                        let fFrom = Math.max(cut.from, pos) - off, fTo = Math.min(cut.to, nextPos) - off;\n                        cut = fFrom >= fTo ? null : new TreeFragment(fFrom, fTo, cut.tree, cut.offset + off, cI > 0, !!nextC);\n                    }\n                    if (cut)\n                        result.push(cut);\n                    if (nextF.to > nextPos)\n                        break;\n                    nextF = fI < fragments.length ? fragments[fI++] : null;\n                }\n            if (!nextC)\n                break;\n            pos = nextC.toA;\n            off = nextC.toA - nextC.toB;\n        }\n        return result;\n    }\n}\n/**\nA superclass that parsers should extend.\n*/\nclass Parser {\n    /**\n    Start a parse, returning a [partial parse](#common.PartialParse)\n    object. [`fragments`](#common.TreeFragment) can be passed in to\n    make the parse incremental.\n    \n    By default, the entire input is parsed. You can pass `ranges`,\n    which should be a sorted array of non-empty, non-overlapping\n    ranges, to parse only those ranges. The tree returned in that\n    case will start at `ranges[0].from`.\n    */\n    startParse(input, fragments, ranges) {\n        if (typeof input == \"string\")\n            input = new StringInput(input);\n        ranges = !ranges ? [new Range(0, input.length)] : ranges.length ? ranges.map(r => new Range(r.from, r.to)) : [new Range(0, 0)];\n        return this.createParse(input, fragments || [], ranges);\n    }\n    /**\n    Run a full parse, returning the resulting tree.\n    */\n    parse(input, fragments, ranges) {\n        let parse = this.startParse(input, fragments, ranges);\n        for (;;) {\n            let done = parse.advance();\n            if (done)\n                return done;\n        }\n    }\n}\nclass StringInput {\n    constructor(string) {\n        this.string = string;\n    }\n    get length() { return this.string.length; }\n    chunk(from) { return this.string.slice(from); }\n    get lineChunks() { return false; }\n    read(from, to) { return this.string.slice(from, to); }\n}\n\n/**\nCreate a parse wrapper that, after the inner parse completes,\nscans its tree for mixed language regions with the `nest`\nfunction, runs the resulting [inner parses](#common.NestedParse),\nand then [mounts](#common.NodeProp^mounted) their results onto the\ntree.\n*/\nfunction parseMixed(nest) {\n    return (parse, input, fragments, ranges) => new MixedParse(parse, nest, input, fragments, ranges);\n}\nclass InnerParse {\n    constructor(parser, parse, overlay, target, from) {\n        this.parser = parser;\n        this.parse = parse;\n        this.overlay = overlay;\n        this.target = target;\n        this.from = from;\n    }\n}\nfunction checkRanges(ranges) {\n    if (!ranges.length || ranges.some(r => r.from >= r.to))\n        throw new RangeError(\"Invalid inner parse ranges given: \" + JSON.stringify(ranges));\n}\nclass ActiveOverlay {\n    constructor(parser, predicate, mounts, index, start, target, prev) {\n        this.parser = parser;\n        this.predicate = predicate;\n        this.mounts = mounts;\n        this.index = index;\n        this.start = start;\n        this.target = target;\n        this.prev = prev;\n        this.depth = 0;\n        this.ranges = [];\n    }\n}\nconst stoppedInner = new NodeProp({ perNode: true });\nclass MixedParse {\n    constructor(base, nest, input, fragments, ranges) {\n        this.nest = nest;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.inner = [];\n        this.innerDone = 0;\n        this.baseTree = null;\n        this.stoppedAt = null;\n        this.baseParse = base;\n    }\n    advance() {\n        if (this.baseParse) {\n            let done = this.baseParse.advance();\n            if (!done)\n                return null;\n            this.baseParse = null;\n            this.baseTree = done;\n            this.startInner();\n            if (this.stoppedAt != null)\n                for (let inner of this.inner)\n                    inner.parse.stopAt(this.stoppedAt);\n        }\n        if (this.innerDone == this.inner.length) {\n            let result = this.baseTree;\n            if (this.stoppedAt != null)\n                result = new Tree(result.type, result.children, result.positions, result.length, result.propValues.concat([[stoppedInner, this.stoppedAt]]));\n            return result;\n        }\n        let inner = this.inner[this.innerDone], done = inner.parse.advance();\n        if (done) {\n            this.innerDone++;\n            // This is a somewhat dodgy but super helpful hack where we\n            // patch up nodes created by the inner parse (and thus\n            // presumably not aliased anywhere else) to hold the information\n            // about the inner parse.\n            let props = Object.assign(Object.create(null), inner.target.props);\n            props[NodeProp.mounted.id] = new MountedTree(done, inner.overlay, inner.parser);\n            inner.target.props = props;\n        }\n        return null;\n    }\n    get parsedPos() {\n        if (this.baseParse)\n            return 0;\n        let pos = this.input.length;\n        for (let i = this.innerDone; i < this.inner.length; i++) {\n            if (this.inner[i].from < pos)\n                pos = Math.min(pos, this.inner[i].parse.parsedPos);\n        }\n        return pos;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n        if (this.baseParse)\n            this.baseParse.stopAt(pos);\n        else\n            for (let i = this.innerDone; i < this.inner.length; i++)\n                this.inner[i].parse.stopAt(pos);\n    }\n    startInner() {\n        let fragmentCursor = new FragmentCursor(this.fragments);\n        let overlay = null;\n        let covered = null;\n        let cursor = new TreeCursor(new TreeNode(this.baseTree, this.ranges[0].from, 0, null), IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n        scan: for (let nest, isCovered;;) {\n            let enter = true, range;\n            if (this.stoppedAt != null && cursor.from >= this.stoppedAt) {\n                enter = false;\n            }\n            else if (fragmentCursor.hasNode(cursor)) {\n                if (overlay) {\n                    let match = overlay.mounts.find(m => m.frag.from <= cursor.from && m.frag.to >= cursor.to && m.mount.overlay);\n                    if (match)\n                        for (let r of match.mount.overlay) {\n                            let from = r.from + match.pos, to = r.to + match.pos;\n                            if (from >= cursor.from && to <= cursor.to && !overlay.ranges.some(r => r.from < to && r.to > from))\n                                overlay.ranges.push({ from, to });\n                        }\n                }\n                enter = false;\n            }\n            else if (covered && (isCovered = checkCover(covered.ranges, cursor.from, cursor.to))) {\n                enter = isCovered != 2 /* Cover.Full */;\n            }\n            else if (!cursor.type.isAnonymous && (nest = this.nest(cursor, this.input)) &&\n                (cursor.from < cursor.to || !nest.overlay)) {\n                if (!cursor.tree)\n                    materialize(cursor);\n                let oldMounts = fragmentCursor.findMounts(cursor.from, nest.parser);\n                if (typeof nest.overlay == \"function\") {\n                    overlay = new ActiveOverlay(nest.parser, nest.overlay, oldMounts, this.inner.length, cursor.from, cursor.tree, overlay);\n                }\n                else {\n                    let ranges = punchRanges(this.ranges, nest.overlay ||\n                        (cursor.from < cursor.to ? [new Range(cursor.from, cursor.to)] : []));\n                    if (ranges.length)\n                        checkRanges(ranges);\n                    if (ranges.length || !nest.overlay)\n                        this.inner.push(new InnerParse(nest.parser, ranges.length ? nest.parser.startParse(this.input, enterFragments(oldMounts, ranges), ranges)\n                            : nest.parser.startParse(\"\"), nest.overlay ? nest.overlay.map(r => new Range(r.from - cursor.from, r.to - cursor.from)) : null, cursor.tree, ranges.length ? ranges[0].from : cursor.from));\n                    if (!nest.overlay)\n                        enter = false;\n                    else if (ranges.length)\n                        covered = { ranges, depth: 0, prev: covered };\n                }\n            }\n            else if (overlay && (range = overlay.predicate(cursor))) {\n                if (range === true)\n                    range = new Range(cursor.from, cursor.to);\n                if (range.from < range.to) {\n                    let last = overlay.ranges.length - 1;\n                    if (last >= 0 && overlay.ranges[last].to == range.from)\n                        overlay.ranges[last] = { from: overlay.ranges[last].from, to: range.to };\n                    else\n                        overlay.ranges.push(range);\n                }\n            }\n            if (enter && cursor.firstChild()) {\n                if (overlay)\n                    overlay.depth++;\n                if (covered)\n                    covered.depth++;\n            }\n            else {\n                for (;;) {\n                    if (cursor.nextSibling())\n                        break;\n                    if (!cursor.parent())\n                        break scan;\n                    if (overlay && !--overlay.depth) {\n                        let ranges = punchRanges(this.ranges, overlay.ranges);\n                        if (ranges.length) {\n                            checkRanges(ranges);\n                            this.inner.splice(overlay.index, 0, new InnerParse(overlay.parser, overlay.parser.startParse(this.input, enterFragments(overlay.mounts, ranges), ranges), overlay.ranges.map(r => new Range(r.from - overlay.start, r.to - overlay.start)), overlay.target, ranges[0].from));\n                        }\n                        overlay = overlay.prev;\n                    }\n                    if (covered && !--covered.depth)\n                        covered = covered.prev;\n                }\n            }\n        }\n    }\n}\nfunction checkCover(covered, from, to) {\n    for (let range of covered) {\n        if (range.from >= to)\n            break;\n        if (range.to > from)\n            return range.from <= from && range.to >= to ? 2 /* Cover.Full */ : 1 /* Cover.Partial */;\n    }\n    return 0 /* Cover.None */;\n}\n// Take a piece of buffer and convert it into a stand-alone\n// TreeBuffer.\nfunction sliceBuf(buf, startI, endI, nodes, positions, off) {\n    if (startI < endI) {\n        let from = buf.buffer[startI + 1];\n        nodes.push(buf.slice(startI, endI, from));\n        positions.push(from - off);\n    }\n}\n// This function takes a node that's in a buffer, and converts it, and\n// its parent buffer nodes, into a Tree. This is again acting on the\n// assumption that the trees and buffers have been constructed by the\n// parse that was ran via the mix parser, and thus aren't shared with\n// any other code, making violations of the immutability safe.\nfunction materialize(cursor) {\n    let { node } = cursor, stack = [];\n    let buffer = node.context.buffer;\n    // Scan up to the nearest tree\n    do {\n        stack.push(cursor.index);\n        cursor.parent();\n    } while (!cursor.tree);\n    // Find the index of the buffer in that tree\n    let base = cursor.tree, i = base.children.indexOf(buffer);\n    let buf = base.children[i], b = buf.buffer, newStack = [i];\n    // Split a level in the buffer, putting the nodes before and after\n    // the child that contains `node` into new buffers.\n    function split(startI, endI, type, innerOffset, length, stackPos) {\n        let targetI = stack[stackPos];\n        let children = [], positions = [];\n        sliceBuf(buf, startI, targetI, children, positions, innerOffset);\n        let from = b[targetI + 1], to = b[targetI + 2];\n        newStack.push(children.length);\n        let child = stackPos\n            ? split(targetI + 4, b[targetI + 3], buf.set.types[b[targetI]], from, to - from, stackPos - 1)\n            : node.toTree();\n        children.push(child);\n        positions.push(from - innerOffset);\n        sliceBuf(buf, b[targetI + 3], endI, children, positions, innerOffset);\n        return new Tree(type, children, positions, length);\n    }\n    base.children[i] = split(0, b.length, NodeType.none, 0, buf.length, stack.length - 1);\n    // Move the cursor back to the target node\n    for (let index of newStack) {\n        let tree = cursor.tree.children[index], pos = cursor.tree.positions[index];\n        cursor.yield(new TreeNode(tree, pos + cursor.from, index, cursor._tree));\n    }\n}\nclass StructureCursor {\n    constructor(root, offset) {\n        this.offset = offset;\n        this.done = false;\n        this.cursor = root.cursor(IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n    }\n    // Move to the first node (in pre-order) that starts at or after `pos`.\n    moveTo(pos) {\n        let { cursor } = this, p = pos - this.offset;\n        while (!this.done && cursor.from < p) {\n            if (cursor.to >= pos && cursor.enter(p, 1, IterMode.IgnoreOverlays | IterMode.ExcludeBuffers)) ;\n            else if (!cursor.next(false))\n                this.done = true;\n        }\n    }\n    hasNode(cursor) {\n        this.moveTo(cursor.from);\n        if (!this.done && this.cursor.from + this.offset == cursor.from && this.cursor.tree) {\n            for (let tree = this.cursor.tree;;) {\n                if (tree == cursor.tree)\n                    return true;\n                if (tree.children.length && tree.positions[0] == 0 && tree.children[0] instanceof Tree)\n                    tree = tree.children[0];\n                else\n                    break;\n            }\n        }\n        return false;\n    }\n}\nclass FragmentCursor {\n    constructor(fragments) {\n        var _a;\n        this.fragments = fragments;\n        this.curTo = 0;\n        this.fragI = 0;\n        if (fragments.length) {\n            let first = this.curFrag = fragments[0];\n            this.curTo = (_a = first.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : first.to;\n            this.inner = new StructureCursor(first.tree, -first.offset);\n        }\n        else {\n            this.curFrag = this.inner = null;\n        }\n    }\n    hasNode(node) {\n        while (this.curFrag && node.from >= this.curTo)\n            this.nextFrag();\n        return this.curFrag && this.curFrag.from <= node.from && this.curTo >= node.to && this.inner.hasNode(node);\n    }\n    nextFrag() {\n        var _a;\n        this.fragI++;\n        if (this.fragI == this.fragments.length) {\n            this.curFrag = this.inner = null;\n        }\n        else {\n            let frag = this.curFrag = this.fragments[this.fragI];\n            this.curTo = (_a = frag.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : frag.to;\n            this.inner = new StructureCursor(frag.tree, -frag.offset);\n        }\n    }\n    findMounts(pos, parser) {\n        var _a;\n        let result = [];\n        if (this.inner) {\n            this.inner.cursor.moveTo(pos, 1);\n            for (let pos = this.inner.cursor.node; pos; pos = pos.parent) {\n                let mount = (_a = pos.tree) === null || _a === void 0 ? void 0 : _a.prop(NodeProp.mounted);\n                if (mount && mount.parser == parser) {\n                    for (let i = this.fragI; i < this.fragments.length; i++) {\n                        let frag = this.fragments[i];\n                        if (frag.from >= pos.to)\n                            break;\n                        if (frag.tree == this.curFrag.tree)\n                            result.push({\n                                frag,\n                                pos: pos.from - frag.offset,\n                                mount\n                            });\n                    }\n                }\n            }\n        }\n        return result;\n    }\n}\nfunction punchRanges(outer, ranges) {\n    let copy = null, current = ranges;\n    for (let i = 1, j = 0; i < outer.length; i++) {\n        let gapFrom = outer[i - 1].to, gapTo = outer[i].from;\n        for (; j < current.length; j++) {\n            let r = current[j];\n            if (r.from >= gapTo)\n                break;\n            if (r.to <= gapFrom)\n                continue;\n            if (!copy)\n                current = copy = ranges.slice();\n            if (r.from < gapFrom) {\n                copy[j] = new Range(r.from, gapFrom);\n                if (r.to > gapTo)\n                    copy.splice(j + 1, 0, new Range(gapTo, r.to));\n            }\n            else if (r.to > gapTo) {\n                copy[j--] = new Range(gapTo, r.to);\n            }\n            else {\n                copy.splice(j--, 1);\n            }\n        }\n    }\n    return current;\n}\nfunction findCoverChanges(a, b, from, to) {\n    let iA = 0, iB = 0, inA = false, inB = false, pos = -1e9;\n    let result = [];\n    for (;;) {\n        let nextA = iA == a.length ? 1e9 : inA ? a[iA].to : a[iA].from;\n        let nextB = iB == b.length ? 1e9 : inB ? b[iB].to : b[iB].from;\n        if (inA != inB) {\n            let start = Math.max(pos, from), end = Math.min(nextA, nextB, to);\n            if (start < end)\n                result.push(new Range(start, end));\n        }\n        pos = Math.min(nextA, nextB);\n        if (pos == 1e9)\n            break;\n        if (nextA == pos) {\n            if (!inA)\n                inA = true;\n            else {\n                inA = false;\n                iA++;\n            }\n        }\n        if (nextB == pos) {\n            if (!inB)\n                inB = true;\n            else {\n                inB = false;\n                iB++;\n            }\n        }\n    }\n    return result;\n}\n// Given a number of fragments for the outer tree, and a set of ranges\n// to parse, find fragments for inner trees mounted around those\n// ranges, if any.\nfunction enterFragments(mounts, ranges) {\n    let result = [];\n    for (let { pos, mount, frag } of mounts) {\n        let startPos = pos + (mount.overlay ? mount.overlay[0].from : 0), endPos = startPos + mount.tree.length;\n        let from = Math.max(frag.from, startPos), to = Math.min(frag.to, endPos);\n        if (mount.overlay) {\n            let overlay = mount.overlay.map(r => new Range(r.from + pos, r.to + pos));\n            let changes = findCoverChanges(ranges, overlay, from, to);\n            for (let i = 0, pos = from;; i++) {\n                let last = i == changes.length, end = last ? to : changes[i].from;\n                if (end > pos)\n                    result.push(new TreeFragment(pos, end, mount.tree, -startPos, frag.from >= pos || frag.openStart, frag.to <= end || frag.openEnd));\n                if (last)\n                    break;\n                pos = changes[i].to;\n            }\n        }\n        else {\n            result.push(new TreeFragment(from, to, mount.tree, -startPos, frag.from >= startPos || frag.openStart, frag.to <= endPos || frag.openEnd));\n        }\n    }\n    return result;\n}\n\nexport { DefaultBufferLength, IterMode, MountedTree, NodeProp, NodeSet, NodeType, NodeWeakMap, Parser, Tree, TreeBuffer, TreeCursor, TreeFragment, parseMixed };\n", "import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, NodeType, <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tree, IterMode } from '@lezer/common';\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let lookaheadRecord = this.reducePos < this.pos - 25 /* Lookahead.Margin */;\n        if (lookaheadRecord)\n            this.setLookAhead(this.pos);\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, lookaheadRecord ? 8 : 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, mustSink = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!mustSink || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */) {\n                let mustMove = false;\n                for (let scan = index; scan > 0 && this.buffer[scan - 2] > end; scan -= 4) {\n                    if (this.buffer[scan - 1] >= 0) {\n                        mustMove = true;\n                        break;\n                    }\n                }\n                if (mustMove)\n                    while (index > 0 && this.buffer[index - 2] > end) {\n                        // Move this record forward\n                        this.buffer[index] = this.buffer[index - 4];\n                        this.buffer[index + 1] = this.buffer[index - 3];\n                        this.buffer[index + 2] = this.buffer[index - 2];\n                        this.buffer[index + 3] = this.buffer[index - 1];\n                        index -= 4;\n                        if (size > 4)\n                            size -= 4;\n                    }\n            }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    /**\n    Accept a token ending at a specific given position.\n    */\n    acceptTokenTo(token, endPos) {\n        this.token.value = token;\n        this.token.end = endPos;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Lookahead.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Lookahead.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Lookahead.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 8400 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n", "import { L<PERSON>arser } from '@lezer/lr';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json$1 = 1,\n  Logfmt$1 = 2,\n  Unpack$1 = 3,\n  Pattern$1 = 4,\n  Regexp$1 = 5,\n  Unwrap$1 = 6,\n  LabelFormat$1 = 7,\n  LineFormat$1 = 8,\n  LabelReplace$1 = 9,\n  Vector$1 = 10,\n  Offset$1 = 11,\n  Bool$1 = 12,\n  On$1 = 13,\n  Ignoring$1 = 14,\n  GroupLeft$1 = 15,\n  GroupRight$1 = 16,\n  Decolorize$1 = 17,\n  Drop$1 = 18,\n  Keep$1 = 19,\n  By$1 = 20,\n  Without$1 = 21,\n  And$1 = 22,\n  Or$1 = 23,\n  Unless$1 = 24,\n  Sum$1 = 25,\n  Avg$1 = 26,\n  Count$1 = 27,\n  Max$1 = 28,\n  Min$1 = 29,\n  Stddev$1 = 30,\n  Stdvar$1 = 31,\n  Bottomk$1 = 32,\n  Topk$1 = 33,\n  Sort$1 = 34,\n  Sort_Desc$1 = 35;\n\nconst keywordTokens = {\n  json: Json$1,\n  logfmt: Logfmt$1,\n  unpack: Unpack$1,\n  pattern: Pattern$1,\n  regexp: Regexp$1,\n  label_format: LabelFormat$1,\n  line_format: LineFormat$1,\n  label_replace: LabelReplace$1,\n  vector: Vector$1,\n  offset: Offset$1,\n  bool: Bool$1,\n  on: On$1,\n  ignoring: Ignoring$1,\n  group_left: GroupLeft$1,\n  group_right: GroupRight$1,\n  unwrap: Unwrap$1,\n  decolorize: Decolorize$1,\n  drop: Drop$1,\n  keep: Keep$1,\n};\n\nconst specializeIdentifier = (value) => {\n  return keywordTokens[value.toLowerCase()] || -1;\n};\n\nconst contextualKeywordTokens = {\n  by: By$1,\n  without: Without$1,\n  and: And$1,\n  or: Or$1,\n  unless: Unless$1,\n  sum: Sum$1,\n  avg: Avg$1,\n  count: Count$1,\n  max: Max$1,\n  min: Min$1,\n  stddev: Stddev$1,\n  stdvar: Stdvar$1,\n  bottomk: Bottomk$1,\n  topk: Topk$1,\n  sort: Sort$1,\n  sort_desc: Sort_Desc$1,\n};\n\nconst extendIdentifier = (value) => {\n  return contextualKeywordTokens[value.toLowerCase()] || -1;\n};\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_Identifier = {__proto__:null,ip:295, count_over_time:301, rate:303, rate_counter:305, bytes_over_time:307, bytes_rate:309, avg_over_time:311, sum_over_time:313, min_over_time:315, max_over_time:317, stddev_over_time:319, stdvar_over_time:321, quantile_over_time:323, first_over_time:325, last_over_time:327, absent_over_time:329, bytes:335, duration:337, duration_seconds:339};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"EtOYQPOOO#cQPO'#DUOOQO'#ER'#ERO#hQPO'#ERO$}QPO'#DTOYQPO'#DTOOQO'#Ed'#EdO%[QPO'#EcOOQO'#FP'#FPO%aQPO'#FOQ%lQPOOO&mQPO'#F]O&rQPO'#F^OOQO'#Eb'#EbOOQO'#DS'#DSOOQO'#Ee'#EeOOQO'#Ef'#EfOOQO'#Eg'#EgOOQO'#Eh'#EhOOQO'#Ei'#EiOOQO'#Ej'#EjOOQO'#Ek'#EkOOQO'#El'#ElOOQO'#Em'#EmOOQO'#En'#EnOOQO'#Eo'#EoOOQO'#Ep'#EpOOQO'#Eq'#EqOOQO'#Er'#ErOOQO'#Es'#EsO&wQPO'#DWOOQO'#DV'#DVO'VQPO,59pOOQO,5:m,5:mOOQO'#Dc'#DcO'_QPO'#DbO'gQPO'#DaO)lQPO'#D`O*{QPO'#D`OOQO'#D_'#D_O+sQPO,59oO-}QPO,59oO.UQPO,5:|O.]QPO,5:}O.hQPO'#E|O0sQPO,5;jO0zQPO,5;jO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lOYQPO,5;wO3cQPO,5;xO3hQPO,59rO#cQPO,59qOOQO1G/[1G/[OOQO'#Dh'#DhO3mQPO,59|O5^QPO,59|OOQO'#Di'#DiO5cQPO,59{OOQO,59{,59{O5kQPO'#DWO6YQPO'#DlO8PQPO'#DoO9sQPO'#DoOOQO'#Do'#DoOOQO'#Dv'#DvOOQO'#Dt'#DtO+kQPO'#DtO9xQPO,59zO;iQPO'#EVO;nQPO'#EWOOQO'#EZ'#EZO;sQPO'#E[O;xQPO'#E_OOQO,59z,59zOOQO,59y,59yOOQO1G/Z1G/ZOOQO1G0h1G0hO;}QPO'#EtO.`QPO'#EtO<XQPO1G0iO<^QPO1G0iO<cQPO,5;hO=oQPO1G1UO=vQPO1G1UO=}QPO1G1UO>UQPO'#FSO@dQPO'#FRO@nQPO'#FROYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO@xQPO1G1cOAPQPO1G1dOOQO1G/^1G/^OOQO1G/]1G/]O5cQPO1G/hOAUQPO1G/hOAZQPO'#DjOBzQPO'#DjOOQO1G/g1G/gOCbQPO,59rOCPQPO,5:cOOQO'#Dm'#DmOClQPO,5:WOEcQPO'#DrOOQO'#Dq'#DqOGVQPO,5:_OHvQPO,5:[OOQO,5:Z,5:ZOJgQPO,5:`O+kQPO,5:`O+kQPO,5:`OOQO,5:q,5:qOJuQPO'#EYOOQO'#EX'#EXOJzQPO,5:rOLkQPO'#E^OOQO'#E^'#E^OOQO'#E]'#E]ONbQPO,5:vO!!RQPO'#EaOOQO'#Ea'#EaOOQO'#E`'#E`O!#xQPO,5:yO!%iQPO'#D`O;}QPO,5;`O!%pQPO'#EuO!%uQPO,5;`O!%}QPO,5;`O!&[QPO,5;`O!&iQPO,5;`O!&nQPO7+&TO.`QPO7+&TOOQO'#E}'#E}O!(OQPO1G1SOOQO1G1S1G1SOYQPO7+&pO!(WQPO7+&pO!)hQPO7+&pO!)oQPO7+&pO!)vQQO'#FTOOQO,5;n,5;nO!,UQPO,5;mO!,]QPO,5;mO!-nQPO7+&rO!-uQPO7+&rOOQO7+&r7+&rO!.SQPO7+&rO!.ZQPO7+&rO!/`QPO7+&rO!/pQPO7+&}OOQO7+'O7+'OOOQO7+%S7+%SO!/uQPO7+%SO5cQPO,5:UO!/zQPO,5:UO!0PQPO1G/{OOQO1G/}1G/}OOQO1G0U1G0UOOQO1G0W1G0WOOQO,5:X,5:XO!0UQPO1G/yO!1uQPO,5:^O!1zQPO,5:]OOQO1G/z1G/zO!2PQPO1G/zO!3pQPO,5:tO;nQPO,5:sO;sQPO,5:wO;xQPO,5:zO!3xQPO,5;cO!%uQPO1G0zO!4WQPO1G0zO!4`QPO,5;aO+kQPO,5;cO!4eQPO1G0zO!4oQPO'#EvO!4tQPO1G0zO!4eQPO1G0zO!4|QPO1G0zO!5ZQPO1G0zO!%xQPO1G0zOOQO1G0z1G0zOOQO<<Io<<IoO!5fQPO<<IoO!5kQPO,5;iOOQO7+&n7+&nO!5pQPO<<J[OOQO<<J[<<J[OYQPO<<J[OOQO'#FV'#FVO!5wQPO,5;oOOQO'#FU'#FUOOQO,5;o,5;oOOQO1G1X1G1XO!6PQPO1G1XO!8YQPO<<JiOOQO<<Hn<<HnOOQO1G/p1G/pO!8_QPO1G/pO!8dQPO7+%gOOQO1G/x1G/xOOQO1G/w1G/wOOQO1G0`1G0`OOQO1G0_1G0_OOQO1G0c1G0cOOQO1G0f1G0fOOQO'#Ex'#ExOOQO1G0}1G0}O!8iQPO1G0}OOQO'#Ey'#EyOOQO'#Ez'#EzOOQO'#E{'#E{OOQO7+&f7+&fOOQO1G0{1G0{O!8nQPO1G0}O!9SQPO7+&fOOQO,5;b,5;bO!9[QPO7+&fO!%xQPO7+&fO!9fQPO7+&fO!9qQPOAN?ZOOQO1G1T1G1TO!;RQPOAN?vO!<cQPOAN?vO!<jQQO1G1ZOOQO1G1Z1G1ZOOQO7+&s7+&sO!<rQPOAN@TOOQO7+%[7+%[O!<wQPO<<IRO!<|QPO7+&iO!=RQPO<<JQO!=ZQPO<<JQO!=cQPO'#EwO!=hQPO<<JQOOQOG24uG24uOOQOG25bG25bOOQO1G1[1G1[OOQO7+&u7+&uO!=pQPOG25oOOQOAN>mAN>mO!=uQPO<<JTOOQOAN?lAN?lO!=zQPOAN?lO!>SQPOLD+ZOOQOAN?oAN?oOOQO,5:r,5:rO!>XQPO!$'NuO!>^QPO!)9DaO!>cQPO!.K9{OOQO!4//g!4//gO;nQPO'#EWO!>hQPO'#D`O!?`QPO,59oO!@fQPO'#DTOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO!AqQPO7+&rO!AxQPO7+&rO!BVQPO7+&rO!C_QPO7+&rO!CfQPO7+&rO!B^QPO'#FQ\",\n  stateData: \"!Cs~O$TOStOS~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!vQO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O{nO~O!vqO~O!OrO!QrO!WrO!XrO!YrO!ZrOfwXgwXhwX!lwX!nwX!owX!pwX!qwX!wwX!xwX#{wX#|wX#}wX$OwX~O!_vO$RwX$ZwX~P#mO$Y{O~Od|Oe|O$Y}O~Of!QOg!POh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{!SO#|!SO#}!SO$O!TO~O$Y!VO~O$Y!WO~O|!XO!O!XO!P!XO!Q!XO~O$V!YO$W!ZO~O}!]O$X!_O~Og!`Of!TXh!TX!O!TX!Q!TX!W!TX!X!TX!Y!TX!Z!TX!_!TX!l!TX!n!TX!o!TX!p!TX!q!TX!w!TX!x!TX#{!TX#|!TX#}!TX$O!TX$R!TX$Z!TX$k!TX$V!TX~O!OrO!QrO!WrO!XrO!YrO!ZrO~Of!SXg!SXh!SX!_!SX!l!SX!n!SX!o!SX!p!SX!q!SX!w!SX!x!SX#{!SX#|!SX#}!SX$O!SX$R!SX$Z!SX$k!SX$V!SX~P)WOP!dOQ!cOR!fOS!eOT!eOV!lOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_vOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Rwa$Zwa~P)WOfvXgvXhvX!OvX!lvX!nvX!ovX!pvX!qvX!wvX!xvX#{vX#|vX#}vX$OvX~O$Z!rO~P,|O$Z!sO~P,|O!v!wO$UPO$Y!uO~O$Y!xO~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O!v!yO~P.mO$Y!{O~O[#OO]!|O^!|OX#uPY#uPi#uPj#uPk#uPl#uPm#uPn#uPo#uPp#uPq#uPr#uPs#uP!v#uP!w#uP!x#uP$U#uP$Y#uP$[#uP$]#uP$^#uP$_#uP$`#uP$a#uP$b#uP$c#uP$d#uP$e#uP$f#uP$g#uP$h#uP$i#uP$j#uP~O!v#WO~O}#XO~Og#ZOf!Uah!Ua!O!Ua!Q!Ua!W!Ua!X!Ua!Y!Ua!Z!Ua!_!Ua!l!Ua!n!Ua!o!Ua!p!Ua!q!Ua!w!Ua!x!Ua#{!Ua#|!Ua#}!Ua$O!Ua$R!Ua$Z!Ua$k!Ua$V!Ua~O$Y#[O~O}#]O$X!_O~O|#`O!O#`O!P!XO!Q!XO!l#aO!n#aO!o#aO!p#aO!q#aO~O{#dO!b#bOf!`Xg!`Xh!`X!O!`X!Q!`X!W!`X!X!`X!Y!`X!Z!`X!_!`X!l!`X!n!`X!o!`X!p!`X!q!`X!w!`X!x!`X#{!`X#|!`X#}!`X$O!`X$R!`X$Z!`X$k!`X$V!`X~O{#dOf!cXg!cXh!cX!O!cX!Q!cX!W!cX!X!cX!Y!cX!Z!cX!_!cX!l!cX!n!cX!o!cX!p!cX!q!cX!w!cX!x!cX#{!cX#|!cX#}!cX$O!cX$R!cX$Z!cX$k!cX$V!cX~O}#hO~Of#jOg#kO$V#jOh!Sa!O!Sa!Q!Sa!W!Sa!X!Sa!Y!Sa!Z!Sa!_!Sa!l!Sa!n!Sa!o!Sa!p!Sa!q!Sa!w!Sa!x!Sa#{!Sa#|!Sa#}!Sa$O!Sa$R!Sa$Z!Sa$k!Sa~O}#lO~O{#mO~O{#pO~O{#tO~O!_#xO$k#zO~P)WO$Z$PO~O$V$QO~O{$RO$Z$TO~Of!uXg!uXh!uX!O!uX!l!uX!n!uX!o!uX!p!uX!q!uX!w!uX!x!uX#{!uX#|!uX#}!uX$O!uX$Z!uX~O$V$UO~P<kO$Z$VO~P,|O!v$WO~P.mO$Y$YO~OX#uXY#uXi#uXj#uXk#uXl#uXm#uXn#uXo#uXp#uXq#uXr#uXs#uX!v#uX!w#uX!x#uX$U#uX$Y#uX$[#uX$]#uX$^#uX$_#uX$`#uX$a#uX$b#uX$c#uX$d#uX$e#uX$f#uX$g#uX$h#uX$i#uX$j#uX~O_$[O`$[O~P>ZO]!|O^!|O~P>ZO$V$dO~P,|O$Z$eO~O}$gO~Og$hOf!^Xh!^X!O!^X!Q!^X!W!^X!X!^X!Y!^X!Z!^X!_!^X!l!^X!n!^X!o!^X!p!^X!q!^X!w!^X!x!^X#{!^X#|!^X#}!^X$O!^X$R!^X$Z!^X$k!^X$V!^X~O$Y$iO~O!m$kO!s$lO!vQO!wRO!xRO~O}#XO$X!_O~PCPO{#dO!b$nOf!`ag!`ah!`a!O!`a!Q!`a!W!`a!X!`a!Y!`a!Z!`a!_!`a!l!`a!n!`a!o!`a!p!`a!q!`a!w!`a!x!`a#{!`a#|!`a#}!`a$O!`a$R!`a$Z!`a$k!`a$V!`a~O|$pOf!fXg!fXh!fX!O!fX!Q!fX!W!fX!X!fX!Y!fX!Z!fX!_!fX!l!fX!n!fX!o!fX!p!fX!q!fX!w!fX!x!fX#{!fX#|!fX#}!fX$O!fX$R!fX$V!fX$Z!fX$k!fX~O$V$qOf!gag!gah!ga!O!ga!Q!ga!W!ga!X!ga!Y!ga!Z!ga!_!ga!l!ga!n!ga!o!ga!p!ga!q!ga!w!ga!x!ga#{!ga#|!ga#}!ga$O!ga$R!ga$Z!ga$k!ga~O$V$qOf!dag!dah!da!O!da!Q!da!W!da!X!da!Y!da!Z!da!_!da!l!da!n!da!o!da!p!da!q!da!w!da!x!da#{!da#|!da#}!da$O!da$R!da$Z!da$k!da~Of#jOg#kO$V#jO$Z$rO~O|$tO~O$V$uOf!zag!zah!za!O!za!Q!za!W!za!X!za!Y!za!Z!za!_!za!l!za!n!za!o!za!p!za!q!za!w!za!x!za#{!za#|!za#}!za$O!za$R!za$Z!za$k!za~O|!XO!O!XO!P!XO!Q!XOf#QXg#QXh#QX!W#QX!X#QX!Y#QX!Z#QX!_#QX!l#QX!n#QX!o#QX!p#QX!q#QX!w#QX!x#QX#{#QX#|#QX#}#QX$O#QX$R#QX$V#QX$Z#QX$k#QX~O$V$vOf#Oag#Oah#Oa!O#Oa!Q#Oa!W#Oa!X#Oa!Y#Oa!Z#Oa!_#Oa!l#Oa!n#Oa!o#Oa!p#Oa!q#Oa!w#Oa!x#Oa#{#Oa#|#Oa#}#Oa$O#Oa$R#Oa$Z#Oa$k#Oa~O|!XO!O!XO!P!XO!Q!XOf#TXg#TXh#TX!W#TX!X#TX!Y#TX!Z#TX!_#TX!l#TX!n#TX!o#TX!p#TX!q#TX!w#TX!x#TX#{#TX#|#TX#}#TX$O#TX$R#TX$V#TX$Z#TX$k#TX~O$V$wOf#Rag#Rah#Ra!O#Ra!Q#Ra!W#Ra!X#Ra!Y#Ra!Z#Ra!_#Ra!l#Ra!n#Ra!o#Ra!p#Ra!q#Ra!w#Ra!x#Ra#{#Ra#|#Ra#}#Ra$O#Ra$R#Ra$Z#Ra$k#Ra~OU$xO~P*{O!m${O~O!_$|O$k#zO~OZ%OO!_#xO$Z#ha~P)WO!_#xO$Z%TO$k#zO~P)WO$Z%UO~Od|Oe|Of#Vqg#Vqh#Vq!O#Vq!l#Vq!n#Vq!o#Vq!p#Vq!q#Vq!w#Vq!x#Vq#{#Vq#|#Vq#}#Vq$O#Vq$R#Vq$Z#Vq$V#Vq~O$V%XO$Z%YO~Od|Oe|Of#rqg#rqh#rq!O#rq!l#rq!n#rq!o#rq!p#rq!q#rq!w#rq!x#rq#{#rq#|#rq#}#rq$O#rq$R#rq$Z#rq$V#rq~O$V%]O~P<kO$Z%[O~P,|O#z%^O$Z%aO~OX#uaY#uai#uaj#uak#ual#uam#uan#uao#uap#uaq#uar#uas#ua!v#ua!w#ua!x#ua$U#ua$[#ua$]#ua$^#ua$_#ua$`#ua$a#ua$b#ua$c#ua$d#ua$e#ua$f#ua$g#ua$h#ua$i#ua$j#ua~O$Y$YO~P!*OO_%cO`%cO$Y#ua~P!*OOf!QOh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{#tq#|#tq#}#tq$O#tq$R#tq$Z#tq~Og#tq~P!,jOf#tqg#tqh#tq~P!,pOg!PO~P!,jO$R#tq$Z#tq~P%lOf#tqg#tqh#tq!O#tq!l#tq!n#tq!o#tq!p#tq!q#tq#{#tq#|#tq#}#tq$O#tq~O!w!RO!x!RO$R#tq$Z#tq~P!.eO}%dO~O$Z%eO~O}%gO~O$Y%hO~O$V$qOf!gig!gih!gi!O!gi!Q!gi!W!gi!X!gi!Y!gi!Z!gi!_!gi!l!gi!n!gi!o!gi!p!gi!q!gi!w!gi!x!gi#{!gi#|!gi#}!gi$O!gi$R!gi$Z!gi$k!gi~O}%iO~O{#dO~Of#jO$V#jOg!hih!hi!O!hi!Q!hi!W!hi!X!hi!Y!hi!Z!hi!_!hi!l!hi!n!hi!o!hi!p!hi!q!hi!w!hi!x!hi#{!hi#|!hi#}!hi$O!hi$R!hi$Z!hi$k!hi~O{%kO}%kO~O{%pO$m%rO$n%sO$o%tO~OZ%OO$Z#hi~O$l%vO~O!_#xO$Z#hi~P)WO!m%yO~O!_$|O$Z#hi~O!_#xO$Z%{O$k#zO~P)WO!_$|O$Z%{O$k#zO~O$Z%}O~O{&OO~O$Z&PO~P,|O$V&RO$Z&SO~O$Y$YOX#uiY#uii#uij#uik#uil#uim#uin#uio#uip#uiq#uir#uis#ui!v#ui!w#ui!x#ui$U#ui$[#ui$]#ui$^#ui$_#ui$`#ui$a#ui$b#ui$c#ui$d#ui$e#ui$f#ui$g#ui$h#ui$i#ui$j#ui~O$V&UO~O$Z&VO~O}&WO~O$Y&XO~Of#jOg#kO$V#jO!_#ki$k#ki$Z#ki~O!_$|O$Z#hq~O!_#xO$Z#hq~P)WOZ%OO!_&[O$Z#hq~Od|Oe|Of#V!Rg#V!Rh#V!R!O#V!R!l#V!R!n#V!R!o#V!R!p#V!R!q#V!R!w#V!R!x#V!R#{#V!R#|#V!R#}#V!R$O#V!R$R#V!R$Z#V!R$V#V!R~Od|Oe|Of#r!Rg#r!Rh#r!R!O#r!R!l#r!R!n#r!R!o#r!R!p#r!R!q#r!R!w#r!R!x#r!R#{#r!R#|#r!R#}#r!R$O#r!R$R#r!R$Z#r!R$V#r!R~O$Z&_O~P,|O#z%^O$Z&aO~O}&bO~O$Z&cO~O{&dO~O!_$|O$Z#hy~OZ%OO$Z#hy~OU$xO~O!_&[O$Z#hy~O$V&gO~O$Z&hO~O!_$|O$Z#h!R~O}&jO~O$V&kO~O}&lO~O$Z&mO~OP!dOQ!cOR!fOS!eOT!eOV&nOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_&oOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Vwa~P)WO!_&oO$VwX~P#mOf&yOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{#tq#|#tq#}#tq$O#tq$V#tq~Og#tq~P!@pOf#tqg#tqh#tq~P!@vOg&xO~P!@pOf&yOg&xOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{&{O#|&{O#}&{O$O&|O~O$V#tq~P!B^O!w&zO!x&zO$V#tq~P!.eO\",\n  goto: \"1l$RPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP$S%R%j&Y&]PPPPPP&t'W'h'v(XPPPP(h(p(yP)S)XP)S)S)[)e)S)m*O*O*XPPPPPP*XP*O*bPPP)S)S*{+R)S)S+Y+])S+c+f+l,_,t-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-p-y.^.j/S/V/V/V/Y/i,_/l,_0R0w1Y1c1fPPPPP,_,_[YOT}!{$U%]Q$^#PQ$_#QS$`#R&tQ$a#SQ$b#TQ$c#UQ'O&rQ'P&sQ'Q&uQ'R&vQ'S&wR'T!Vt^O}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wRyTjSOT}!V!{#P#Q#R#S#T#U$U%]S!t{$QQ#}!u]&q&r&s&t&u&v&wRpPQoP^!hv!i#j#k#x$|&oQ#Y!YS#q!n$vT#u!o$wQxSQ#y!tQ$}#|Q%R#}Q%z%QR&p&q[wS!t#|#}%Q&q]!qx#y$}%R%z&piuSx!t#y#|#}$}%Q%R%z&p&qhtSx!t#y#|#}$}%Q%R%z&p&qR!auksSux!t#y#|#}$}%Q%R%z&p&qQ!^sV#^!`#Z$hW![s!`#Z$hR$j#`Q#_!`Q$f#ZR%f$hV!pv#x&oR#c!cQ#f!cQ#g!dR$o#cU#e!c!d#cR%j$qU!jv#x&oQ#i!iQ$r#jQ$s#kR%w$|_!hv!i#j#k#x$|&o_!gv!i#j#k#x$|&ov]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wT$m#`#aQ#o!lR&i&nS#n!l&nR%l$uR#s!nQ#r!nR%m$vR#w!oQ#v!oR%n$wj^O#P#Q#R#S#T#U&r&s&t&u&v&wQzTQ!z}Q#V!VQ$X!{Q%Z$UR&Q%]w]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwVOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwUOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ!v{Q$O!uR%W$QS#|!t#}W$z#y#{%R%SQ%u$yQ%|%TR&Z%{Q%Q#|Q%u$zQ&]%|R&e&ZQ#{!tS$y#y%RQ%P#|Q%S#}S%x$}%QS&Y%z%|R&f&]R%q$xR%o$xQ!OXQ%V$PQ%[$VQ&^%}R&_&PR$S!xwXOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ#P!PQ#Q!QQ#R!RQ#S!SQ#T!TQ#U!UQ&r&xQ&s&yQ&t&zQ&u&{Q&v&|R&w&}h!}!P!Q!R!S!T!U&x&y&z&{&|&}R$]#OQ$Z!|Q%b$[R&T%cR%_$YQ%`$YR&`&R\",\n  nodeNames: \"⚠ Json Logfmt Unpack Pattern Regexp Unwrap LabelFormat LineFormat LabelReplace Vector Offset Bool On Ignoring GroupLeft GroupRight Decolorize Drop Keep By Without And Or Unless Sum Avg Count Max Min Stddev Stdvar Bottomk Topk Sort Sort_Desc LineComment LogQL Expr LogExpr Selector Matchers Matcher Identifier Eq String Neq Re Nre PipelineExpr PipelineStage LineFilters LineFilter Filter PipeExact PipeMatch PipePattern Npa FilterOp Ip OrFilter Pipe LogfmtParser LogfmtParserFlags ParserFlag LabelParser JsonExpressionParser LabelExtractionExpressionList LabelExtractionExpression LogfmtExpressionParser LabelFilter IpLabelFilter UnitFilter DurationFilter Gtr Duration Gte Lss Lte Eql BytesFilter Bytes NumberFilter LiteralExpr Number Add Sub LineFormatExpr LabelFormatExpr LabelsFormat LabelFormatMatcher DecolorizeExpr DropLabelsExpr DropLabels DropLabel KeepLabelsExpr KeepLabels KeepLabel MetricExpr RangeAggregationExpr RangeOp CountOverTime Rate RateCounter BytesOverTime BytesRate AvgOverTime SumOverTime MinOverTime MaxOverTime StddevOverTime StdvarOverTime QuantileOverTime FirstOverTime LastOverTime AbsentOverTime LogRangeExpr Range OffsetExpr UnwrapExpr ConvOp BytesConv DurationConv DurationSecondsConv Grouping Labels VectorAggregationExpr VectorOp BinOpExpr BinOpModifier OnOrIgnoringModifier GroupingLabels GroupingLabelList GroupingLabel LabelName Mul Div Mod Pow LabelReplaceExpr VectorExpr\",\n  maxTerm: 169,\n  skippedNodes: [0,36],\n  repeatNodeCount: 0,\n  tokenData: \"<n~RvX^#ipq#iqr$^rs$yst%kuv%vxy%{yz&Qz{&V{|&[|}&a}!O&f!O!P2v!P!Q3v!Q!R3{!R![7^![!]9]!^!_9q!_!`:O!`!a:e!c!}:r!}#O;Y#P#Q;_#Q#R;d#R#S:r#S#T;i#T#o:r#o#p;u#p#q;z#q#r<i#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~#nY$T~X^#ipq#i#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~$aR!_!`$j!`!a$o#r#s$t~$oO!O~~$tO!Z~~$yO!Q~~$|UOY$yZr$yrs%`s#O$y#O#P%e#P~$y~%eO}~~%hPO~$y~%pQt~OY%kZ~%k~%{O#}~~&QO$Y~~&VO$Z~~&[O#{~~&aO!w~~&fO$V~~&kQ!x~}!O&q!Q![(w~&tQ#_#`&z#g#h(X~&}P#X#Y'Q~'TP#X#Y'W~'ZP#d#e'^~'aP}!O'd~'gP#X#Y'j~'mP#a#b'p~'sP#d#e'v~'yP#h#i'|~(PP#m#n(S~(XO!b~~([P#h#i(_~(bP#f#g(e~(hP#]#^(k~(nP#V#W(q~(tP#h#i(S~(zZ!O!P)m!Q![(w#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#m#n1{${$|.^~)pP!Q![)s~)vV!Q![)s#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~*bP!m~!Q![*e~*hV!O!P*}!Q![*e#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~+QP!Q![+T~+WU!Q![+T#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~+oQ!m~!Q![+u#g#h-Q~+xV!O!P,_!Q![+u#a#b,z#b#c.R#g#h/X#i#j.^${$|.^~,bP!Q![,e~,hU!Q![,e#a#b,z#b#c.R#g#h/X#i#j.^${$|.^~,}P#g#h-Q~-VP!m~!Q![-Y~-]T!O!P-l!Q![-Y#b#c.R#i#j.^${$|.^~-oP!Q![-r~-uS!Q![-r#b#c.R#i#j.^${$|.^~.UP#g#h.X~.^O!m~~.aP#g#h.d~.iP!m~!Q![.l~.oR!O!P.x!Q![.l#b#c.R~.{P!Q![/O~/RQ!Q![/O#b#c.R~/^P!m~!Q![/a~/dU!O!P/v!Q![/a#a#b,z#b#c.R#i#j.^${$|.^~/yP!Q![/|~0PT!Q![/|#a#b,z#b#c.R#i#j.^${$|.^~0eP!m~!Q![0h~0kW!O!P)m!Q![0h#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~1YP!m~!Q![1]~1`X!O!P)m!Q![1]#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~2QP!m~!Q![2T~2WY!O!P)m!Q![2T#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T${$|.^~2yP!Q![2|~3RR!v~!Q![2|!g!h3[#X#Y3[~3_R{|3h}!O3h!Q![3n~3kP!Q![3n~3sP!v~!Q![3n~3{O#|~~4Qe!v~!O!P5c!Q![7^!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#W#X0`#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#l#m8q#m#n1{${$|.^~5hR!v~!Q![5q!g!h3[#X#Y3[~5v`!v~!Q![5q!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~6}O!s~~7QQ!d!e6x#]#^7W~7ZP!d!e6x~7cd!v~!O!P5c!Q![7^!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#W#X0`#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#m#n1{${$|.^~8tR!Q![8}!c!i8}#T#Z8}~9SR!v~!Q![8}!c!i8}#T#Z8}P9bT{P!Q![9]![!]9]!c!}9]#R#S9]#T#o9]~9vP!o~!_!`9y~:OO!p~~:TQ|~!_!`:Z#r#s:`~:`O!q~~:eO!P~~:jP!l~!_!`:m~:rO!n~R:yT{P#zQ!Q![:r![!]9]!c!}:r#R#S:r#T#o:r~;_O$k~~;dO$l~~;iO$O~~;lRO#S;i#S#T%`#T~;i~;zO$U~~<PR!_~!_!`<Y!`!a<_#r#s<d~<_O!W~~<dO!Y~~<iO!X~~<nO$W~\",\n  tokenizers: [0, 1],\n  topRules: {\"LogQL\":[0,37]},\n  specialized: [{term: 43, get: (value, stack) => (specializeIdentifier(value) << 1)},{term: 43, get: (value, stack) => (extendIdentifier(value) << 1) | 1},{term: 43, get: value => spec_Identifier[value] || -1}],\n  tokenPrec: 0\n});\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json = 1,\n  Logfmt = 2,\n  Unpack = 3,\n  Pattern = 4,\n  Regexp = 5,\n  Unwrap = 6,\n  LabelFormat = 7,\n  LineFormat = 8,\n  LabelReplace = 9,\n  Vector = 10,\n  Offset = 11,\n  Bool = 12,\n  On = 13,\n  Ignoring = 14,\n  GroupLeft = 15,\n  GroupRight = 16,\n  Decolorize = 17,\n  Drop = 18,\n  Keep = 19,\n  By = 20,\n  Without = 21,\n  And = 22,\n  Or = 23,\n  Unless = 24,\n  Sum = 25,\n  Avg = 26,\n  Count = 27,\n  Max = 28,\n  Min = 29,\n  Stddev = 30,\n  Stdvar = 31,\n  Bottomk = 32,\n  Topk = 33,\n  Sort = 34,\n  Sort_Desc = 35,\n  LineComment = 36,\n  LogQL = 37,\n  Expr = 38,\n  LogExpr = 39,\n  Selector = 40,\n  Matchers = 41,\n  Matcher = 42,\n  Identifier = 43,\n  Eq = 44,\n  String = 45,\n  Neq = 46,\n  Re = 47,\n  Nre = 48,\n  PipelineExpr = 49,\n  PipelineStage = 50,\n  LineFilters = 51,\n  LineFilter = 52,\n  Filter = 53,\n  PipeExact = 54,\n  PipeMatch = 55,\n  PipePattern = 56,\n  Npa = 57,\n  FilterOp = 58,\n  Ip = 59,\n  OrFilter = 60,\n  Pipe = 61,\n  LogfmtParser = 62,\n  LogfmtParserFlags = 63,\n  ParserFlag = 64,\n  LabelParser = 65,\n  JsonExpressionParser = 66,\n  LabelExtractionExpressionList = 67,\n  LabelExtractionExpression = 68,\n  LogfmtExpressionParser = 69,\n  LabelFilter = 70,\n  IpLabelFilter = 71,\n  UnitFilter = 72,\n  DurationFilter = 73,\n  Gtr = 74,\n  Duration = 75,\n  Gte = 76,\n  Lss = 77,\n  Lte = 78,\n  Eql = 79,\n  BytesFilter = 80,\n  Bytes = 81,\n  NumberFilter = 82,\n  LiteralExpr = 83,\n  Number = 84,\n  Add = 85,\n  Sub = 86,\n  LineFormatExpr = 87,\n  LabelFormatExpr = 88,\n  LabelsFormat = 89,\n  LabelFormatMatcher = 90,\n  DecolorizeExpr = 91,\n  DropLabelsExpr = 92,\n  DropLabels = 93,\n  DropLabel = 94,\n  KeepLabelsExpr = 95,\n  KeepLabels = 96,\n  KeepLabel = 97,\n  MetricExpr = 98,\n  RangeAggregationExpr = 99,\n  RangeOp = 100,\n  CountOverTime = 101,\n  Rate = 102,\n  RateCounter = 103,\n  BytesOverTime = 104,\n  BytesRate = 105,\n  AvgOverTime = 106,\n  SumOverTime = 107,\n  MinOverTime = 108,\n  MaxOverTime = 109,\n  StddevOverTime = 110,\n  StdvarOverTime = 111,\n  QuantileOverTime = 112,\n  FirstOverTime = 113,\n  LastOverTime = 114,\n  AbsentOverTime = 115,\n  LogRangeExpr = 116,\n  Range = 117,\n  OffsetExpr = 118,\n  UnwrapExpr = 119,\n  ConvOp = 120,\n  BytesConv = 121,\n  DurationConv = 122,\n  DurationSecondsConv = 123,\n  Grouping = 124,\n  Labels = 125,\n  VectorAggregationExpr = 126,\n  VectorOp = 127,\n  BinOpExpr = 128,\n  BinOpModifier = 129,\n  OnOrIgnoringModifier = 130,\n  GroupingLabels = 131,\n  GroupingLabelList = 132,\n  GroupingLabel = 133,\n  LabelName = 134,\n  Mul = 135,\n  Div = 136,\n  Mod = 137,\n  Pow = 138,\n  LabelReplaceExpr = 139,\n  VectorExpr = 140;\n\nexport { AbsentOverTime, Add, And, Avg, AvgOverTime, BinOpExpr, BinOpModifier, Bool, Bottomk, By, Bytes, BytesConv, BytesFilter, BytesOverTime, BytesRate, ConvOp, Count, CountOverTime, Decolorize, DecolorizeExpr, Div, Drop, DropLabel, DropLabels, DropLabelsExpr, Duration, DurationConv, DurationFilter, DurationSecondsConv, Eq, Eql, Expr, Filter, FilterOp, FirstOverTime, GroupLeft, GroupRight, Grouping, GroupingLabel, GroupingLabelList, GroupingLabels, Gte, Gtr, Identifier, Ignoring, Ip, IpLabelFilter, Json, JsonExpressionParser, Keep, KeepLabel, KeepLabels, KeepLabelsExpr, LabelExtractionExpression, LabelExtractionExpressionList, LabelFilter, LabelFormat, LabelFormatExpr, LabelFormatMatcher, LabelName, LabelParser, LabelReplace, LabelReplaceExpr, Labels, LabelsFormat, LastOverTime, LineComment, LineFilter, LineFilters, LineFormat, LineFormatExpr, LiteralExpr, LogExpr, LogQL, LogRangeExpr, Logfmt, LogfmtExpressionParser, LogfmtParser, LogfmtParserFlags, Lss, Lte, Matcher, Matchers, Max, MaxOverTime, MetricExpr, Min, MinOverTime, Mod, Mul, Neq, Npa, Nre, Number, NumberFilter, Offset, OffsetExpr, On, OnOrIgnoringModifier, Or, OrFilter, ParserFlag, Pattern, Pipe, PipeExact, PipeMatch, PipePattern, PipelineExpr, PipelineStage, Pow, QuantileOverTime, Range, RangeAggregationExpr, RangeOp, Rate, RateCounter, Re, Regexp, Selector, Sort, Sort_Desc, Stddev, StddevOverTime, Stdvar, StdvarOverTime, String, Sub, Sum, SumOverTime, Topk, UnitFilter, Unless, Unpack, Unwrap, UnwrapExpr, Vector, VectorAggregationExpr, VectorExpr, VectorOp, Without, parser };\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".js\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"public/plugins/grafana-lokiexplore-app/\";", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkgrafana_lokiexplore_app\"] = self[\"webpackChunkgrafana_lokiexplore_app\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(9077);\n"], "names": ["leafPrototypes", "getProto", "inProgress", "dataWebpackPrefix", "OpenInExploreLogsButton", "lazy", "exposedComponents", "id", "title", "description", "component", "props", "Suspense", "fallback", "LinkButton", "variant", "disabled", "App", "wasmSupported", "default", "initRuntimeDs", "initChangepoint", "initOutlier", "Promise", "all", "AppConfig", "plugin", "AppPlugin", "setRootPage", "addConfigPage", "icon", "body", "linkConfig", "linkConfigs", "addLink", "exposedComponentConfig", "exposeComponent", "PRODUCT_NAME", "ExtensionPoints", "MetricInvestigation", "targets", "PluginExtensionPoints", "DashboardPanelMenu", "path", "createAppUrl", "configure", "contextToLink", "ExploreToolbarAction", "stringifyValues", "value", "replaceEscapeChars", "replace", "stringifyAdHocValues", "addAdHocFilterUserInputPrefix", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "target", "datasource", "type", "uid", "expr", "labelFilters", "lineFilters", "fields", "patternFilters", "getMatcherFromQuery", "labelSelector", "selector", "isOperatorInclusive", "operator", "labelValue", "replaceSlash", "split", "labelName", "key", "SERVICE_NAME", "sort", "a", "params", "setUrlParameter", "UrlParameters", "DatasourceId", "URLSearchParams", "TimeRangeFrom", "timeRange", "from", "valueOf", "toString", "TimeRangeTo", "to", "labelFilter", "LabelType", "Indexed", "labelsAdHocFilterURLString", "escapeURLDelimiters", "appendUrlParameter", "Labels", "lineFilter", "LineFilters", "length", "field", "StructuredMetadata", "LEVEL_VARIABLE_VALUE", "Levels", "<PERSON><PERSON><PERSON>", "fieldValue", "parser", "adHocFilterURLString", "JSON", "stringify", "Fields", "patterns", "push", "PatternFilterOp", "match", "pattern", "patternsString", "renderPatternFilters", "Patterns", "PatternsVariable", "urlParams", "pluginJson", "VAR_DATASOURCE", "VAR_LABELS", "VAR_FIELDS", "VAR_METADATA", "VAR_LEVELS", "VAR_LINE_FILTERS", "VAR_PATTERNS", "initalParams", "searchParams", "location", "search", "set", "append", "parameter", "stripAdHocFilterUserInputPrefix", "Symbol", "escapeUrlCommaDelimiters", "escapeUrlPipeDelimiters", "LabelFilterOp", "NumericFilterOp", "FilterOp", "LineFilterOp", "LineFilterCaseSensitive", "defaultContext", "app", "version", "logger", "info", "msg", "ctx", "console", "log", "attemptFaroInfo", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "err", "attemptFaroErr", "logInfo", "e", "logWarning", "context2", "isRecord", "Object", "keys", "for<PERSON>ach", "hasData", "data", "populateFetchErrorContext", "Error", "logError", "NodePosition", "fromNode", "node", "contains", "position", "this", "getExpression", "query", "substring", "constructor", "syntaxNode", "getNodesFromQuery", "nodeTypes", "nodes", "parse", "iterate", "enter", "undefined", "includes", "getAllPositionsInNodeByType", "positions", "pos", "child", "childAfter", "parseNonPatternFilters", "lineFilterValue", "quoteString", "index", "isRegexSelector", "regex", "negativeRegex", "isCaseInsensitive", "replaceDoubleEscape", "RegExp", "replaceDoubleQuoteEscape", "caseInsensitive", "caseSensitive", "parsePatternFilters", "getNumericFieldOperator", "matcher", "<PERSON><PERSON>", "FilterOperator", "lte", "Lss", "lt", "Gte", "gte", "Gtr", "gt", "getStringFieldOperator", "Eq", "Equal", "Neq", "NotEqual", "Re", "RegexEqual", "Nre", "RegexNotEqual", "filter", "Selector", "allMatcher", "Matcher", "identifierPosition", "valuePosition", "Identifier", "String", "map", "parseLabelFilters", "allLineFilters", "LineFilter", "entries", "equal", "PipeExact", "pipeRegExp", "PipeMatch", "notEqual", "notEqualRegExp", "patternInclude", "PipePattern", "patternExclude", "Npa", "lineFilterValueNodes", "getStringsFromLineFilter", "lineFilterValueNode", "negativeMatch", "parseLineFilters", "dataFrame", "series", "frame", "refId", "allFields", "leftChild", "<PERSON><PERSON><PERSON><PERSON>", "getLHSLeafNodesFromQuery", "LabelFilter", "fieldNameNode", "logFmtParser", "Logfmt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Json", "fieldName", "fieldStringValue", "fieldNumberValue", "Number", "fieldBytesValue", "Bytes", "fieldDurationValue", "Duration", "labelType", "getLabelTypeFromFrame", "Parsed", "parseFields", "ErrorId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "isQueryWithNode", "string", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "LokiQueryDirection", "labelKey", "typeField", "name", "values", "isObj", "o", "hasProp", "prop", "isString", "s", "obj", "unknownToStrings", "strings", "Array", "isArray", "i", "narrowSelectedTableRow", "narrowed", "row", "narrowLogsVisualizationType", "narrowLogsSortOrder", "LogsSortOrder", "Ascending", "Descending", "narrowFieldValue", "narrowRecordStringNumber", "returnRecord", "narrowTimeRange", "<PERSON><PERSON><PERSON><PERSON>", "range", "narrowFilterOperator", "op", "NarrowingError", "isOperatorExclusive", "isOperatorRegex", "isOperatorNumeric", "numericOperatorArray", "getOperatorDescription", "operators", "array", "label", "includeOperators", "numericOperators", "lineFilterOperators", "escapeLabelValueInExactSelector", "excludePatternsLine", "p", "join", "trim", "includePatterns", "includePatternsLine", "VAR_LABELS_EXPR", "VAR_LABELS_REPLICA", "VAR_LABELS_REPLICA_EXPR", "VAR_FIELDS_EXPR", "PENDING_FIELDS_EXPR", "PENDING_METADATA_EXPR", "VAR_FIELDS_AND_METADATA", "VAR_METADATA_EXPR", "VAR_PATTERNS_EXPR", "VAR_LEVELS_EXPR", "VAR_FIELD_GROUP_BY", "VAR_LABEL_GROUP_BY", "VAR_LABEL_GROUP_BY_EXPR", "VAR_PRIMARY_LABEL_SEARCH", "VAR_PRIMARY_LABEL", "VAR_PRIMARY_LABEL_EXPR", "VAR_DATASOURCE_EXPR", "MIXED_FORMAT_EXPR", "JSON_FORMAT_EXPR", "LOGS_FORMAT_EXPR", "VAR_LOGS_FORMAT", "VAR_LOGS_FORMAT_EXPR", "VAR_LINE_FILTER", "VAR_LINE_FILTERS_EXPR", "LOG_STREAM_SELECTOR_EXPR", "DETECTED_FIELD_AND_METADATA_VALUES_EXPR", "DETECTED_LEVELS_VALUES_EXPR", "PATTERNS_SAMPLE_SELECTOR_EXPR", "PRETTY_LOG_STREAM_SELECTOR_EXPR", "EXPLORATION_DS", "ALL_VARIABLE_VALUE", "SERVICE_UI_LABEL", "VAR_AGGREGATED_METRICS", "EMPTY_VARIABLE_VALUE", "USER_INPUT_ADHOC_VALUE_PREFIX", "startsWith", "isAdHocFilterValueUserInput", "module", "exports", "__WEBPACK_EXTERNAL_MODULE__6089__", "__WEBPACK_EXTERNAL_MODULE__7781__", "__WEBPACK_EXTERNAL_MODULE__8531__", "__WEBPACK_EXTERNAL_MODULE__2007__", "__WEBPACK_EXTERNAL_MODULE__3241__", "__WEBPACK_EXTERNAL_MODULE__5959__", "__WEBPACK_EXTERNAL_MODULE__8398__", "__WEBPACK_EXTERNAL_MODULE__200__", "__WEBPACK_EXTERNAL_MODULE__3806__", "__WEBPACK_EXTERNAL_MODULE__7694__", "__WEBPACK_EXTERNAL_MODULE__1269__", "De<PERSON>ult<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "nextPropID", "Range", "NodeProp", "config", "perNode", "deserialize", "add", "RangeError", "NodeType", "result", "closedBy", "str", "openedBy", "group", "isolate", "contextHash", "lookAhead", "mounted", "MountedTree", "tree", "overlay", "get", "noProps", "create", "flags", "define", "spec", "top", "skipped", "src", "isTop", "isSkipped", "isError", "isAnonymous", "is", "indexOf", "direct", "groups", "found", "none", "NodeSet", "types", "extend", "newTypes", "newProps", "source", "assign", "CachedNode", "WeakMap", "CachedInnerNode", "IterMode", "Tree", "children", "ch", "test", "cursor", "mode", "TreeCursor", "topNode", "cursorAt", "side", "scope", "moveTo", "_tree", "TreeNode", "resolve", "resolveNode", "resolveInner", "resolveStack", "inner", "layers", "scan", "parent", "mount", "root", "iterStack", "stackIterator", "leave", "anon", "IncludeAnonymous", "c", "entered", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "balance", "balanceRange", "makeTree", "build", "_a", "buffer", "nodeSet", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reused", "minRepeatType", "FlatBufferCursor", "takeNode", "parentStart", "minPos", "inRepeat", "depth", "start", "end", "size", "lookAheadAtStart", "contextAtStart", "next", "startPos", "maxSize", "fork", "skip", "minStart", "nodeSize", "localSkipped", "nodeStart", "findBufferSize", "Uint16Array", "endPos", "copyToBuffer", "<PERSON><PERSON><PERSON><PERSON>", "localChildren", "localPositions", "localInRepeat", "lastGroup", "lastEnd", "makeRepeatLeaf", "takeFlatNode", "reverse", "make", "last", "lookAheadProp", "lastI", "makeBalanced", "nodeCount", "stopAt", "j", "base", "pop", "pair", "concat", "bufferStart", "startIndex", "topID", "buildTree", "empty", "childString", "endIndex", "<PERSON><PERSON><PERSON><PERSON>", "dir", "pick", "checkSide", "slice", "startI", "endI", "b", "copy", "len", "Math", "max", "overlays", "IgnoreOverlays", "BaseNode", "before", "after", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchContext", "matchNodeContext", "enterUnfinishedNodesBefore", "childBefore", "<PERSON><PERSON><PERSON><PERSON>", "prevSibling", "_parent", "super", "<PERSON><PERSON><PERSON><PERSON>", "ExcludeBuffers", "BufferNode", "BufferContext", "<PERSON><PERSON><PERSON><PERSON>", "IgnoreMounts", "rPos", "nextSignificantParent", "val", "toTree", "cur", "externalSibling", "heads", "picked", "newHeads", "splice", "StackIterator", "stack", "bufferNode", "yieldNode", "n", "unshift", "yieldBuf", "yield", "enterChild", "sibling", "d", "atLastNode", "move", "prev", "cache", "mustLeave", "some", "nodeSizeCache", "balanceType", "mkTop", "mkTree", "total", "max<PERSON><PERSON><PERSON>", "ceil", "divide", "offset", "groupFrom", "groupStart", "groupSize", "nextSize", "only", "<PERSON><PERSON><PERSON>", "startParse", "input", "fragments", "ranges", "StringInput", "createParse", "done", "advance", "chunk", "lineChunks", "read", "<PERSON><PERSON>", "state", "reducePos", "score", "bufferBase", "cur<PERSON><PERSON><PERSON><PERSON>", "_", "cx", "StackContext", "pushState", "reduce", "action", "lookaheadRecord", "setLookAhead", "dPrec", "dynamicPrecedence", "getGoto", "minRepeatTerm", "storeNode", "reduceContext", "lastBigReductionStart", "bigReductionCount", "lastBigReductionSize", "count", "stateFlag", "baseStateID", "term", "mustSink", "mustMove", "shift", "shiftContext", "maxNode", "nextState", "apply", "nextStart", "nextEnd", "useNode", "updateContext", "tracker", "reuse", "stream", "reset", "off", "recoverByDelete", "isNode", "canShift", "sim", "SimulatedStack", "stateSlot", "hasAction", "recoverByInsert", "nextStates", "best", "v", "forceReduce", "validAction", "backup", "findForcedReduction", "seen", "explore", "allActions", "r<PERSON><PERSON><PERSON>", "forceAll", "deadEnd", "restart", "sameState", "other", "dialectEnabled", "dialectID", "dialect", "emitContext", "hash", "emitLookAhead", "newCx", "close", "strict", "goto", "StackBufferCursor", "maybeNext", "decodeArray", "Type", "out", "charCodeAt", "stop", "digit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extended", "mask", "nullToken", "InputStream", "chunkOff", "chunk2", "chunk2Pos", "token", "rangeIndex", "chunkPos", "readNext", "resolveOffset", "assoc", "clipPos", "peek", "idx", "resolved", "acceptToken", "endOffset", "acceptTokenTo", "getChunk", "nextChunk", "setDone", "min", "TokenGroup", "precTable", "precOffset", "groupMask", "accEnd", "allows", "overrides", "low", "high", "mid", "readToken", "tokenPrecTable", "findOffset", "tableData", "tableOffset", "iPrev", "prototype", "contextual", "verbose", "process", "env", "LOG", "stackIDs", "cutAt", "fragment", "safeFrom", "safeTo", "trees", "nextFragment", "fr", "openStart", "openEnd", "nodeAt", "TokenCache", "tokens", "mainToken", "actions", "tokenizers", "getActions", "actionIndex", "main", "tokenizer", "updateCachedToken", "addActions", "eofTerm", "getMainToken", "specialized", "specializers", "putAction", "Parse", "recovering", "nextStackID", "minStackPos", "stoppedAt", "topTerm", "stacks", "bufferLength", "parsedPos", "stopped", "stoppedTokens", "newStacks", "advanceStack", "tok", "finished", "findFinished", "stackID", "stackToTree", "getName", "SyntaxError", "runRecovery", "maxRemaining", "outer", "strictCx", "cxHash", "cached", "defaultReduce", "localStack", "advanceFully", "pushStackDedup", "restarted", "tokenEnd", "force", "forceBase", "insert", "fromCodePoint", "Dialect", "<PERSON><PERSON><PERSON><PERSON>", "wrappers", "nodeNames", "repeatNodeCount", "topTerms", "topRules", "nodeProps", "setProp", "nodeID", "propSpec", "skippedNodes", "propSources", "tokenArray", "tokenData", "specializerSpecs", "getSpecializer", "states", "Uint32Array", "stateData", "maxTerm", "dialects", "dynamicPrecedences", "tokenPrec", "termNames", "parseDialect", "w", "loose", "table", "groupTag", "terminal", "slot", "flag", "deflt", "t", "external", "contextTracker", "wrap", "hasWrappers", "prec", "part", "Uint8Array", "keywordTokens", "json", "logfmt", "unpack", "regexp", "label_format", "line_format", "label_replace", "vector", "bool", "on", "ignoring", "group_left", "group_right", "unwrap", "decolorize", "drop", "keep", "contextualKeywordTokens", "by", "without", "and", "or", "unless", "sum", "avg", "stddev", "stdvar", "bottomk", "topk", "sort_desc", "spec_Identifier", "__proto__", "ip", "count_over_time", "rate", "rate_counter", "bytes_over_time", "bytes_rate", "avg_over_time", "sum_over_time", "min_over_time", "max_over_time", "stddev_over_time", "stdvar_over_time", "quantile_over_time", "first_over_time", "last_over_time", "absent_over_time", "bytes", "duration", "duration_seconds", "toLowerCase", "specializeIdentifier", "extendIdentifier", "MetricExpr", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "call", "m", "getter", "__esModule", "getPrototypeOf", "then", "ns", "def", "current", "getOwnPropertyNames", "definition", "defineProperty", "enumerable", "f", "chunkId", "promises", "u", "g", "globalThis", "Function", "window", "hasOwnProperty", "l", "url", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "onScriptComplete", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "fn", "setTimeout", "bind", "head", "append<PERSON><PERSON><PERSON>", "toStringTag", "baseURI", "self", "href", "installedChunks", "installedChunkData", "promise", "reject", "errorType", "realSrc", "message", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "chunkIds", "moreModules", "runtime", "chunkLoadingGlobal"], "sourceRoot": ""}