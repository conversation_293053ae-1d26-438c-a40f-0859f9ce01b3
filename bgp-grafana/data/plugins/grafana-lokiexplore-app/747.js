"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[747],{5218:(e,t,n)=>{n.d(t,{F:()=>o});var r=n(6089),a=n(2007),i=n(5959),s=n.n(i),l=n(1220);const o=e=>{const{isExcluded:t,isIncluded:n,onInclude:r,onExclude:i,onClear:o,titles:u,buttonFill:d}=e,p=(0,a.useStyles2)(c,n,t);return s().createElement("div",{className:p.container},s().createElement(a.<PERSON>,{variant:n?"primary":"secondary",fill:d,size:"sm","aria-selected":n,className:p.includeButton,onClick:n?o:r,"data-testid":l.b.exploreServiceDetails.buttonFilterInclude,title:null==u?void 0:u.include},"Include"),s().createElement(a.<PERSON><PERSON>,{variant:t?"primary":"secondary",fill:d,size:"sm","aria-selected":t,className:p.excludeButton,onClick:t?o:i,title:null==u?void 0:u.exclude,"data-testid":l.b.exploreServiceDetails.buttonFilterExclude},"Exclude"))},c=(e,t,n)=>({container:(0,r.css)({display:"flex",justifyContent:"center"}),includeButton:(0,r.css)({borderRadius:0,borderRight:t?void 0:"none"}),excludeButton:(0,r.css)({borderRadius:`0 ${e.shape.radius.default} ${e.shape.radius.default} 0`,borderLeft:n?void 0:"none"})})},4482:(e,t,n)=>{n.d(t,{R:()=>c});var r=n(5959),a=n.n(r),i=n(6089),s=n(1575),l=n(2007);const o=e=>({graphicContainer:(0,i.css)({display:"flex",justifyContent:"center",margin:"0 auto"}),graphic:(0,i.css)({width:"200px",height:"120px",padding:e.spacing(1)}),text:(0,i.css)({display:"flex",justifyContent:"center",alignItems:"center"}),wrap:(0,i.css)({margin:"0 auto"})}),c=({children:e})=>{const t=(0,l.useStyles2)(o),n=(0,l.useTheme2)();return a().createElement("div",{className:t.wrap},a().createElement("div",{className:t.graphicContainer},a().createElement(s.A,{className:t.graphic,src:n.isDark?"/public/plugins/grafana-lokiexplore-app/img/grot_err.svg":"/public/plugins/grafana-lokiexplore-app/img/grot_err_light.svg"})),a().createElement("div",{className:t.text},a().createElement(l.Text,{textAlignment:"center",color:"primary",element:"span"},e||"An error occurred")))}},8538:(e,t,n)=>{n.d(t,{P:()=>Xe,y:()=>Ye});var r=n(5959),a=n.n(r),i=n(7781),s=n(2672),l=n(3143),o=n(227),c=n(2254),u=n(2007),d=n(6089);function p(e){const t=(0,u.useStyles2)(g);return a().createElement(a().Fragment,null,a().createElement(u.Alert,{className:t.alert,severity:"info",title:"Welcome to Grafana Logs Drilldown!",onRemove:e.onRemove},a().createElement("div",null,"Check out our"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/logs/",rel:"noreferrer"},"Get started doc"),", or see"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://github.com/grafana/explore-logs/releases",rel:"noreferrer"},"recent changes"),".",a().createElement("br",null),"Help us shape the future of the app."," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://forms.gle/1sYWCTPvD72T1dPH9",rel:"noreferrer"},"Send us feedback")," ","or engage with us on"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://github.com/grafana/explore-logs/?tab=readme-ov-file#explore-logs",rel:"noreferrer"},"GitHub"),".")))}function g(e){return{alert:(0,d.css)({flex:"none"})}}var h=n(8831),v=n(2871),m=n(4916);const f=()=>{const e=(0,u.useStyles2)(b);return a().createElement("div",{className:e.wrapper},a().createElement("a",{href:"https://forms.gle/1sYWCTPvD72T1dPH9",className:e.feedback,title:"Share your thoughts about Logs in Grafana.",target:"_blank",rel:"noreferrer noopener"},a().createElement(u.Icon,{name:"comment-alt-message"})," Give feedback"))},b=e=>({wrapper:(0,d.css)({display:"flex",marginLeft:"auto",gap:e.spacing(1),position:"relative",top:e.spacing(-1)}),feedback:(0,d.css)({alignSelf:"center",color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize,"&:hover":{color:e.colors.text.link}})});class y extends s.Bs{}var S,w,O;O=function({model:e}){var t,n;const r=s.jh.getVariables(e).useState();let i=r.variables;return(null===(t=e.state.include)||void 0===t?void 0:t.length)&&(i=r.variables.filter((t=>{var n,r;return null===(n=e.state.include)||void 0===n?void 0:n.includes(null!==(r=t.state.name)&&void 0!==r?r:"")}))),(null===(n=e.state.exclude)||void 0===n?void 0:n.length)&&(i=r.variables.filter((t=>{var n,r;return!(null===(n=e.state.exclude)||void 0===n?void 0:n.includes(null!==(r=t.state.name)&&void 0!==r?r:""))}))),a().createElement(a().Fragment,null,i.map((t=>a().createElement(s.Lp,{key:t.state.key,variable:t,layout:e.state.layout}))))},(w="Component")in(S=y)?Object.defineProperty(S,w,{value:O,enumerable:!0,configurable:!0,writable:!0}):S[w]=O;var x=n(1220);const E=({onRemove:e,pattern:t,size:n="lg"})=>{const i=(0,u.useStyles2)(F),[s,l]=(0,r.useState)(!1);return a().createElement("div",{className:i.pattern,onClick:()=>l(!s),onMouseLeave:()=>l(!1)},a().createElement(u.Tag,{title:t,key:t,name:s?t:k(t,n),className:i.tag}),a().createElement(u.Button,{"aria-label":"Remove pattern","data-testid":x.b.exploreServiceDetails.buttonRemovePattern,variant:"secondary",size:"sm",className:i.removeButton,onClick:e},a().createElement(u.Icon,{name:"times"})))},C={sm:50,lg:Math.round(window.innerWidth/8)};function k(e,t){const n=e.length;if(n<C[t])return e;const r=Math.round(.4*C[t]);return`${e.substring(0,r)} … ${e.substring(n-r)}`}const F=e=>({pattern:(0,d.css)({display:"flex",fontFamily:"monospace",gap:e.spacing(.25),cursor:"pointer",overflow:"hidden"}),tag:(0,d.css)({borderTopRightRadius:0,borderBottomRightRadius:0,backgroundColor:e.colors.secondary.main,border:`solid 1px ${e.colors.secondary.border}`,color:e.colors.secondary.text,boxSizing:"border-box",padding:e.spacing(.25,.75),overflow:"hidden",textOverflow:"ellipsis"}),removeButton:(0,d.css)({paddingLeft:2.5,paddingRight:2.5})});var L=n(2718);const P=({patterns:e,onRemove:t})=>{const n=(0,u.useStyles2)(j);if(!e||0===e.length)return null;const r=e.filter((e=>"include"===e.type)),i=e.filter((e=>"include"!==e.type)),s=n=>{t(e.filter((e=>e!==n))),(0,L.EE)(L.NO.service_details,L.ir.service_details.pattern_removed,{includePatternsLength:r.length-("include"===(null==n?void 0:n.type)?1:0),excludePatternsLength:i.length-("include"!==(null==n?void 0:n.type)?1:0),type:n.type})};return a().createElement("div",null,r.length>0&&a().createElement("div",{className:n.patternsContainer},a().createElement(u.Text,{variant:"bodySmall",weight:"bold","data-testid":x.b.patterns.buttonIncludedPattern},"Included pattern",e.length>1?"s":""),a().createElement("div",{className:n.patterns},r.map((e=>a().createElement(E,{key:e.pattern,pattern:e.pattern,size:"lg",onRemove:()=>s(e)}))))),i.length>0&&a().createElement("div",{className:n.patternsContainer},a().createElement(u.Text,{variant:"bodySmall",weight:"bold","data-testid":x.b.patterns.buttonExcludedPattern},"Excluded pattern",i.length>1?"s":"",":"),a().createElement("div",{className:n.patterns},i.map((e=>a().createElement(E,{key:e.pattern,pattern:e.pattern,size:i.length>1?"sm":"lg",onRemove:()=>s(e)}))))))};function j(e){return{patternsContainer:(0,d.css)({overflow:"hidden"}),patterns:(0,d.css)({display:"flex",gap:e.spacing(1),alignItems:"center",flexWrap:"wrap"})}}class _ extends s.Bs{}function T(e){return{firstRowWrapper:(0,d.css)({"& > div > div":{gap:"16px",label:"first-row-wrapper",[e.breakpoints.down("lg")]:{flexDirection:"column"}}}),bodyContainer:(0,d.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),container:(0,d.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column",padding:e.spacing(2),maxWidth:"100vw"}),body:(0,d.css)({flexGrow:1,display:"flex",flexDirection:"column",gap:e.spacing(1)}),controlsFirstRowContainer:(0,d.css)({label:"controls-first-row",display:"flex",gap:e.spacing(2),justifyContent:"space-between",alignItems:"flex-start"}),controlsRowContainer:(0,d.css)({"&:empty":{display:"none"},label:"controls-row",display:"flex",gap:e.spacing(1),alignItems:"flex-start",paddingLeft:e.spacing(2)}),controlsContainer:(0,d.css)({label:"controlsContainer",display:"flex",flexDirection:"column",gap:e.spacing(1)}),filters:(0,d.css)({label:"filters",display:"flex"}),filtersWrap:(0,d.css)({label:"filtersWrap",display:"flex",gap:e.spacing(2),width:"calc(100% - 450)",flexWrap:"wrap",alignItems:"flex-end"}),controlsWrapper:(0,d.css)({label:"controlsWrapper",display:"flex",flexDirection:"column",marginTop:e.spacing(.375)}),timeRangeDatasource:(0,d.css)({label:"timeRangeDatasource",display:"flex",gap:e.spacing(1),flexWrap:"wrap",justifyContent:"flex-end"}),timeRange:(0,d.css)({label:"timeRange",display:"flex",flexDirection:"row",gap:e.spacing(1)}),controls:(0,d.css)({display:"flex",gap:e.spacing(1)}),feedback:(0,d.css)({textAlign:"end"}),rotateIcon:(0,d.css)({svg:{transform:"rotate(180deg)"}})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(_,"Component",(({model:e})=>{const t=s.jh.getAncestor(e,Xe),{controls:n,patterns:r}=t.useState(),i=s.jh.getAncestor(e,M),{lineFilterRenderer:l,levelsRenderer:o}=i.useState(),c=(0,u.useStyles2)(T);return a().createElement("div",{className:c.controlsContainer},a().createElement(a().Fragment,null,n&&a().createElement("div",{className:c.controlsFirstRowContainer},a().createElement("div",{className:c.filtersWrap},a().createElement("div",{className:(0,d.cx)(c.filters,c.firstRowWrapper)},n.map((e=>e instanceof s.G1?a().createElement(e.Component,{key:e.state.key,model:e}):null)))),a().createElement("div",{className:c.controlsWrapper},a().createElement(f,null),a().createElement("div",{className:c.timeRangeDatasource},n.map((e=>e.state.key===A?a().createElement(e.Component,{key:e.state.key,model:e}):null)),a().createElement("div",{className:c.timeRange},n.map((e=>e instanceof y||e instanceof s.G1?null:a().createElement(e.Component,{key:e.state.key,model:e}))))))),a().createElement("div",{className:c.controlsRowContainer},o&&a().createElement(o.Component,{model:o})),a().createElement("div",{className:c.controlsRowContainer},n&&a().createElement("div",{className:c.filtersWrap},a().createElement("div",{className:c.filters},n.map((e=>e instanceof y&&e.state.key===$?a().createElement(e.Component,{key:e.state.key,model:e}):null))))),a().createElement("div",{className:c.controlsRowContainer},a().createElement(P,{patterns:r,onRemove:e=>t.setState({patterns:e})})),a().createElement("div",{className:c.controlsRowContainer},l&&a().createElement(l.Component,{model:l}))))}));var D=n(3626);function N(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const I=`${h.s_}.interceptBannerStorageKey`,$="vars-fields-metadata",A="vars-ds";class M extends s.Bs{onActivate(){this.setState({lineFilterRenderer:new m.Y({}),levelsRenderer:new D.q({}),variableLayout:new _({})})}dismiss(){this.setState({interceptDismissed:!0}),localStorage.setItem(I,"true")}constructor(e){var t,n;super((t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){N(e,t,n[t])}))}return e}({},e),n=null!=(n={interceptDismissed:!!localStorage.getItem(I)})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)),this.addActivationHandler(this.onActivate.bind(this))}}function B(e){return{bodyContainer:(0,d.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),container:(0,d.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column",padding:e.spacing(2),maxWidth:"100vw"}),body:(0,d.css)({label:"body-wrapper",flexGrow:1,display:"flex",flexDirection:"column",gap:e.spacing(1)}),controlsContainer:(0,d.css)({label:"controlsContainer",display:"flex",flexDirection:"column",gap:e.spacing(1)})}}N(M,"Component",(({model:e})=>{const t=s.jh.getAncestor(e,Xe),{contentScene:n}=t.useState(),{interceptDismissed:r,variableLayout:i}=e.useState();if(!n)return v.v.warn("content scene not defined"),null;const l=(0,u.useStyles2)(B);return a().createElement("div",{className:l.bodyContainer},a().createElement("div",{className:l.container},!r&&a().createElement(p,{onRemove:()=>{e.dismiss()}}),i&&a().createElement(i.Component,{model:i}),a().createElement("div",{className:l.body},n&&a().createElement(n.Component,{model:n}))))}));var R=n(892),V=n(1105),W=n(8531),z=n(7918),H=n(5435),G=n(5431),q=n(4750),K=n(4002),U=n(4106),Q=n(9077),J=n(9829),Y=n(3241),X=n(4793),Z=n(7063),ee=n(5111);function te(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}function ne(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){te(i,r,a,s,l,"next",e)}function l(e){te(i,r,a,s,l,"throw",e)}s(void 0)}))}}const re=function(){var e=ne((function*(e,t,n,r,a,i){const s=yield(0,W.getDataSourceSrv)().get((0,J.U4)(r));if(!(s instanceof W.DataSourceWithBackend))throw v.v.error(new Error("getTagValuesProvider: Invalid datasource!")),new Error("Invalid datasource!");const o=s.languageProvider;let c=[];if(o&&o.fetchDetectedLabelValues){const r={expr:n,limit:1e3,timeRange:a,throwError:!0},s={showErrorAlert:!1};try{let n=yield o.fetchDetectedLabelValues(e.key,r,s);if(n&&(0,Y.isArray)(n)){var u;if(i===l._Y)return{replace:!0,values:n.map((e=>({text:e})))};const r=t.state.filters;let a=[];r.forEach((e=>{var t,n;const r=null!==(n=null===(t=e.valueLabels)||void 0===t?void 0:t[0])&&void 0!==n?n:e.value;(0,ee.SM)(e.operator)?r.split("|").forEach((e=>a.push(e))):a.push(r)}));const s=n.filter((e=>!a.includes(e)));if("structuredMetadata"!==(null===(u=e.meta)||void 0===u?void 0:u.parser)){if(e.value){const t=(0,q.bu)(e,i);return{replace:!0,values:s.map((e=>({text:e,value:JSON.stringify({value:e,parser:t.parser})})))}}return{replace:!0,values:s.map((t=>{var n,r;return{text:t,value:JSON.stringify({value:t,parser:null!==(r=null===(n=e.meta)||void 0===n?void 0:n.parser)&&void 0!==r?r:"mixed"})}}))}}c=s.map((e=>({text:e})))}else c=[],v.v.error(n,{msg:"fetchDetectedLabelValues error!"})}catch(e){v.v.error(e,{msg:"getDetectedFieldValuesTagValuesProvider: loki missing detected_field/.../values endpoint. Upgrade to Loki 3.3.0 or higher."}),c=[]}}else v.v.warn("getDetectedFieldValuesTagValuesProvider: fetchDetectedLabelValues is not defined in Loki datasource. Upgrade to Grafana 11.4 or higher."),c=[];return{replace:!0,values:c}}));return function(t,n,r,a,i,s){return e.apply(this,arguments)}}();function ae(e,t){return ie.apply(this,arguments)}function ie(){return ie=ne((function*(e,t){const n=yield(0,W.getDataSourceSrv)().get((0,J.U4)(e));if(!(n instanceof W.DataSourceWithBackend))throw v.v.error(new Error("getTagValuesProvider: Invalid datasource!")),new Error("Invalid datasource!");const r=n;if(r&&r.getTagValues){const n=function(e,t){let n=e.filter((e=>!((0,ee.BG)(t.operator)&&e.key===t.key)));return n.some((e=>(0,ee.BG)(e.operator)))||(n=[]),n}(new Z.K(e.state.filters).getJoinedLabelsFilters(),t),i={key:t.key,filters:n};let s=yield r.getTagValues(i);if((0,Y.isArray)(s)){var a;s=s.filter((n=>!e.state.filters.filter((e=>e.key===t.key)).some((e=>(0,ee.SM)(e.operator)?e.value.split("|").some((e=>e===n.text)):e.operator===X.w7.Equal&&e.value===n.text))));const n=(0,o.eT)(null===(a=(0,q.S9)(e).getValue())||void 0===a?void 0:a.toString(),t.key),r=new Set(n);n.length&&s.sort(((e,t)=>(r.has(t.text)?1:-1)-(r.has(e.text)?1:-1)))}return{replace:!0,values:s}}return v.v.error(new Error("getTagValuesProvider: missing or invalid datasource!")),{replace:!0,values:[]}})),ie.apply(this,arguments)}var se=n(6001);function le(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}function oe(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){le(i,r,a,s,l,"next",e)}function l(e){le(i,r,a,s,l,"throw",e)}s(void 0)}))}}function ce(e){return ue.apply(this,arguments)}function ue(){return ue=oe((function*(e){const t=yield(0,W.getDataSourceSrv)().get((0,J.U4)(e));if(!(t instanceof W.DataSourceWithBackend))throw v.v.error(new Error("getTagKeysProvider: Invalid datasource!")),new Error("Invalid datasource!");const n=t;if(n&&n.getTagKeys){const t={filters:new Z.K(e.state.filters).getJoinedLabelsFilters()},r=yield n.getTagKeys(t),a=(Array.isArray(r)?r:[]).filter((e=>!se.rm.includes(e.text)));return{replace:!0,values:a}}return v.v.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{replace:!0,values:[]}})),ue.apply(this,arguments)}function de(e){return pe.apply(this,arguments)}function pe(){return pe=oe((function*({limit:e,timeRange:t,scopedVars:n,expr:r,sceneRef:a,variableType:i}){const s=yield(0,W.getDataSourceSrv)().get((0,J.U4)(a));if(!(s instanceof W.DataSourceWithBackend))throw v.v.error(new Error("getTagKeysProvider: Invalid datasource!")),new Error("Invalid datasource!");const o=s,c=o.languageProvider,u={expr:r,timeRange:t,scopedVars:n,variableType:i,sceneRef:a,limit:e},d=o&&"function"==typeof c.fetchDetectedFields&&c.fetchDetectedFields.bind(c)||function(e){return function(e,t,n){return ge.apply(this,arguments)}(o,e)};if(d&&"function"==typeof d){const e=yield d(u);if(e instanceof Error)throw v.v.error(e,{msg:"Failed to fetch detected fields"}),e;const t=e.filter((e=>i===l._Y?e.label===l.e4:i===l.sL&&e.label!==l.e4||null!==e.parsers)).map((e=>{if(i===l.sL){var t;let n=1===(null===(t=e.parsers)||void 0===t?void 0:t.length)?e.parsers[0]:"mixed";null===e.parsers&&(n="structuredMetadata");const r=e.type;return{text:e.label,value:e.label,group:n,meta:{parser:n,type:r}}}return{text:e.label,value:e.label}}));return t.sort(((e,t)=>"structuredMetadata"===e.group&&"structuredMetadata"!==t.group?-1:"structuredMetadata"!==e.group&&"structuredMetadata"===t.group?1:0)),{replace:!0,values:t}}return v.v.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{replace:!0,values:[]}})),pe.apply(this,arguments)}function ge(){return ge=oe((function*(e,t,n){if(!("interpolateString"in e)||"function"!=typeof(null==e?void 0:e.interpolateString))throw new Error("Datasource missing interpolateString method");const r=t.expr&&"{}"!==t.expr?e.interpolateString(t.expr,t.scopedVars):void 0;if(!r)throw new Error("fetchDetectedFields requires query expression");var a;const s=null!==(a=null==t?void 0:t.timeRange)&&void 0!==a?a:(0,i.getDefaultTimeRange)(),l=e.getTimeRangeParams(s),{start:o,end:c}=l;var u;const d={start:o,end:c,limit:null!==(u=null==t?void 0:t.limit)&&void 0!==u?u:1e3};return d.query=r,new Promise(function(){var t=oe((function*(t,r){try{t((yield e.getResource("detected_fields",d,n)).fields)}catch(e){console.error("error",e),r(e)}}));return function(e,n){return t.apply(this,arguments)}}())})),ge.apply(this,arguments)}var he=n(6059);function ve(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let me={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},fe={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},be={"~":"`","!":"1","@":"2","#":"3",$:"4","%":"5","^":"6","&":"7","*":"8","(":"9",")":"0",_:"-","+":"=",":":";",'"':"'","<":",",">":".","?":"/","|":"\\"},ye={option:"alt",command:"meta",return:"enter",escape:"esc",plus:"+",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl"},Se=null;for(let e=1;e<20;++e)me[111+e]="f"+e;for(let e=0;e<=9;++e)me[e+96]=e.toString();function we(e){if("keypress"===e.type){let t=String.fromCharCode(e.which);return e.shiftKey||(t=t.toLowerCase()),t}return me[e.which]?me[e.which]:fe[e.which]?fe[e.which]:String.fromCharCode(e.which).toLowerCase()}function Oe(e){return"shift"===e||"ctrl"===e||"alt"===e||"meta"===e}function xe(e,t,n){return n||(n=function(){if(!Se){Se={};for(let e in me){const t=parseInt(e,10);t>95&&t<112||me.hasOwnProperty(e)&&(Se[me[e]]=e)}}return Se}()[e]?"keydown":"keypress"),"keypress"===n&&t.length&&(n="keydown"),n}function Ee(e,t){let n,r,a,i=[];for(n=function(e){return"+"===e?["+"]:(e=e.replace(/\+{2}/g,"+plus")).split("+")}(e),a=0;a<n.length;++a)r=n[a],ye[r]&&(r=ye[r]),t&&"keypress"!==t&&be[r]&&(r=be[r],i.push("shift")),Oe(r)&&i.push(r);if(!r)throw new Error("Unable to get key");return{key:r,modifiers:i,action:t=xe(r,i,t)}}function Ce(e,t){return null!==e&&e!==document&&(e===t||Ce(e.parentNode,t))}const ke=new class{constructor(e){ve(this,"target",void 0),ve(this,"_callbacks",{}),ve(this,"_directMap",{}),ve(this,"_sequenceLevels",{}),ve(this,"_resetTimer",void 0),ve(this,"_ignoreNextKeyup",!1),ve(this,"_ignoreNextKeypress",!1),ve(this,"_nextExpectedAction",!1),ve(this,"_globalCallbacks",{}),ve(this,"_resetSequences",(e=>{e=e||{};let t,n=!1;for(t in this._sequenceLevels)e[t]?n=!0:this._sequenceLevels[t]=0;n||(this._nextExpectedAction=!1)})),ve(this,"_getMatches",((e,t,n,r,a,i)=>{let s,l,o=[],c=n.type;if(!this._callbacks[e])return[];for("keyup"===c&&Oe(e)&&(t=[e]),s=0;s<this._callbacks[e].length;++s)if(l=this._callbacks[e][s],(r||!l.seq||this._sequenceLevels[l.seq]===l.level)&&c===l.action&&("keypress"===c&&!n.metaKey&&!n.ctrlKey||(u=t,d=l.modifiers,u.sort().join(",")===d.sort().join(",")))){let t=!r&&l.combo===a,n=r&&l.seq===r&&l.level===i;(t||n)&&this._callbacks[e].splice(s,1),o.push(l)}var u,d;return o})),ve(this,"_fireCallback",((e,t,n,r)=>{const a=t.target||t.srcElement;var i;a&&a instanceof HTMLElement&&this.stopCallback(t,a,n,r)||!1===e(t,n)&&((i=t).preventDefault?i.preventDefault():i.returnValue=!1,function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}(t))})),ve(this,"_handleKey",((e,t,n)=>{let r,a=this._getMatches(e,t,n),i={},s=0,l=!1;for(r=0;r<a.length;++r){var o;a[r].seq&&(s=Math.max(s,null!==(o=a[r].level)&&void 0!==o?o:0))}for(r=0;r<a.length;++r){const t=a[r].seq;if(t){if(a[r].level!==s)continue;l=!0,i[t]=1,this._fireCallback(a[r].callback,n,a[r].combo,t);const o=t.lastIndexOf(e),c=t.slice(0,o);for(const[e,t]of Object.entries(this._sequenceLevels))t>0&&e.startsWith(c)&&(i[e]=1)}else l||this._fireCallback(a[r].callback,n,a[r].combo)}var c;for(const t of null!==(c=this._callbacks[e])&&void 0!==c?c:[])t.action===n.type&&t.seq&&0===t.level&&(i[t.seq]=1);let u="keypress"===n.type&&this._ignoreNextKeypress;n.type!==this._nextExpectedAction||Oe(e)||u||this._resetSequences(i),this._ignoreNextKeypress=l&&"keydown"===n.type})),ve(this,"_handleKeyEvent",(e=>{if(!(e instanceof KeyboardEvent))throw new Error("Didn't get a KeyboardEvent");const t=e;if(t.repeat)return;"number"!=typeof t.which&&(t.which=t.keyCode);let n=we(t);n&&("keyup"!==t.type||this._ignoreNextKeyup!==n?this.handleKey(n,function(e){let t=[];return e.shiftKey&&t.push("shift"),e.altKey&&t.push("alt"),e.ctrlKey&&t.push("ctrl"),e.metaKey&&t.push("meta"),t}(t),t):this._ignoreNextKeyup=!1)})),ve(this,"_resetSequenceTimer",(()=>{clearTimeout(this._resetTimer),this._resetTimer=setTimeout(this._resetSequences,1e3)})),ve(this,"_bindSequence",((e,t,n,r)=>{this._sequenceLevels[e]=0;const a=t=>()=>{this._nextExpectedAction=t,++this._sequenceLevels[e],this._resetSequenceTimer()},i=t=>{this._fireCallback(n,t,e),"keyup"!==r&&(this._ignoreNextKeyup=we(t)),this._resetSequenceTimer()};for(let n=0;n<t.length;++n){let s=n+1===t.length?i:a(r||Ee(t[n+1]).action);this._bindSingle(t[n],s,r,e,n)}})),ve(this,"_bindSingle",((e,t,n,r,a)=>{this._directMap[e+":"+n]=t;let i,s=(e=e.replace(/\s+/g," ")).split(" ");if(s.length>1)return void this._bindSequence(e,s,t,n);i=Ee(e,n),this._callbacks[i.key]=this._callbacks[i.key]||[];const l={type:i.action,metaKey:!1,ctrlKey:!1};this._getMatches(i.key,i.modifiers,l,r,e,a);const o={callback:t,modifiers:i.modifiers,action:i.action,seq:r,level:a,combo:e};this._callbacks[i.key][r?"unshift":"push"](o)})),ve(this,"_bindMultiple",((e,t,n)=>{for(let r=0;r<e.length;++r)this._bindSingle(e[r],t,n)})),ve(this,"bind",((e,t,n)=>(e=e instanceof Array?e:[e],this._bindMultiple(e,t,n),self))),ve(this,"unbind",((e,t)=>this.bind(e,(function(){}),t))),ve(this,"bindGlobal",((e,t,n)=>{if(this.bind(e,t,n),e instanceof Array)for(let t=0;t<e.length;t++)this._globalCallbacks[e[t]]=!0;else this._globalCallbacks[e]=!0})),ve(this,"unbindGlobal",((e,t)=>{if(this.unbind(e,t),e instanceof Array)for(let t=0;t<e.length;t++)this._globalCallbacks[e[t]]=!1;else this._globalCallbacks[e]=!1})),ve(this,"trigger",((e,t)=>{let n=this;return n._directMap[e+":"+t]&&n._directMap[e+":"+t]({},e),n})),ve(this,"reset",(()=>(this._callbacks={},this._directMap={},this))),ve(this,"stopCallback",((e,t,n,r)=>{if(this._globalCallbacks[n]||r&&this._globalCallbacks[r])return!1;if((" "+t.className+" ").indexOf(" mousetrap ")>-1)return!1;if(Ce(t,this.target))return!1;if("composedPath"in e&&"function"==typeof e.composedPath){let n=e.composedPath()[0];n!==e.target&&n instanceof HTMLElement&&(t=n)}return Boolean("INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||"isContentEditable"in t&&t.isContentEditable)})),ve(this,"handleKey",((...e)=>this._handleKey(...e))),ve(this,"addKeycodes",(e=>{for(let t in e)e.hasOwnProperty(t)&&(me[t]=e[t]);Se=null})),this.target=e,this.target.addEventListener("keypress",(e=>{e instanceof KeyboardEvent&&this._handleKeyEvent(e)})),this.target.addEventListener("keydown",(e=>{e instanceof KeyboardEvent&&this._handleKeyEvent(e)})),this.target.addEventListener("keyup",(e=>{e instanceof KeyboardEvent&&this._handleKeyEvent(e)}))}}(document);class Fe{addBinding(e){ke.bind(e.key,(t=>{t.preventDefault(),t.stopPropagation(),t.returnValue=!1,e.onTrigger()}),"keydown"),this._binds.push(e)}removeAll(){this._binds.forEach((e=>{ke.unbind(e.key,e.type)})),this._binds=[]}constructor(){!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(this,"_binds",[])}}var Le=n(7085),Pe=n(4011);function je(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}function _e(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){je(i,r,a,s,l,"next",e)}function l(e){je(i,r,a,s,l,"throw",e)}s(void 0)}))}}function Te(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const De=(0,W.getAppEvents)();function Ne(e){const t=(0,J.m0)(e);null==t||t.onZoom()}function Ie(e,t){const n=(0,J.m0)(e);n&&("left"===t&&n.onMoveBackward(),"right"===t&&n.onMoveForward())}function $e(e){const t=e.state.options;var n;null!=(n=t)&&"object"==typeof n&&"legend"in n&&"boolean"==typeof t.legend.showLegend&&e.onOptionsChange({legend:{showLegend:!t.legend.showLegend}})}class Ae extends i.BusEventBase{}Te(Ae,"type","copy-time");class Me extends i.BusEventWithPayload{}function Be(){return(Be=_e((function*(){const e=yield navigator.clipboard.readText();let t;try{t=JSON.parse(e);const n=(0,Pe.OK)(t);if(n)return{isError:!1,range:n}}catch(e){}return{range:e,isError:!0}}))).apply(this,arguments)}Te(Me,"type","paste-time");var Re=n(5548),Ve=n(1863),We=n(833),ze=n(1293),He=n(541),Ge=n(1575);const qe=()=>{const e=(0,u.useStyles2)(Ke),t=(0,u.useTheme2)();return a().createElement("div",{className:e.wrap},a().createElement("div",{className:e.graphicContainer},a().createElement(Ge.A,{src:(t.isDark,"/public/plugins/grafana-lokiexplore-app/img/grot_loki.svg")})),a().createElement("div",{className:e.text},a().createElement("h3",{className:e.title},"Welcome to Grafana Logs Drilldown"),a().createElement("p",null,"We noticed there is no Loki datasource configured.",a().createElement("br",null),"Add a"," ",a().createElement("a",{className:"external-link",href:i.locationUtil.assureBaseUrl("/connections/datasources/new")},"Loki datasource")," ","to view logs."),a().createElement("br",null),a().createElement("p",null,"Click"," ",a().createElement("a",{href:"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/logs/",target:"_blank",className:"external-link",rel:"noreferrer"},"here")," ","to learn more...")))},Ke=e=>({graphicContainer:(0,d.css)({display:"flex",justifyContent:"center",margin:"0 auto",width:"200px",height:"250px",padding:e.spacing(1),[e.breakpoints.up("md")]:{alignSelf:"flex-end",width:"300px",height:"auto",padding:e.spacing(1)},[e.breakpoints.up("lg")]:{alignSelf:"flex-end",width:"400px",height:"auto",padding:e.spacing(1)}}),text:(0,d.css)({display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column"}),title:(0,d.css)({marginBottom:"1.5rem"}),wrap:(0,d.css)({[e.breakpoints.up("md")]:{margin:"4rem auto auto auto",flexDirection:"row"},padding:"2rem",margin:"0 auto auto auto",display:"flex",alignItems:"center",flexDirection:"column",textAlign:"center"})});function Ue(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}function Qe(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){Ue(i,r,a,s,l,"next",e)}function l(e){Ue(i,r,a,s,l,"throw",e)}s(void 0)}))}}function Je(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Ye="showLogsButtonScene";class Xe extends s.Bs{onActivate(){const e={};var t,n;this.setVariableProviders(),s.jh.findByKeyAndType(this,Ye,he.H).setState({hidden:!1}),this.state.contentScene||(e.contentScene=(n=null===(t=this.state.routeMatch)||void 0===t?void 0:t.params.breakdownLabel,(0,R.FT)()===R.G3.explore?new V.y({}):new c.Mn({drillDownLabel:n}))),this.setTagProviders(),this.setState(e),this.updatePatterns(this.state,(0,q.Ku)(this)),this.resetVariablesIfNotInUrl((0,q.ir)(this),(0,q.n5)(l.mB)),this.resetVariablesIfNotInUrl((0,q.iw)(this),(0,q.n5)(l._Y)),this._subs.add(this.subscribeToState((e=>{this.updatePatterns(e,(0,q.Ku)(this))})));const r=s.jh.getTimeRange(this);this._subs.add(r.subscribeToState(this.limitMaxInterval(r))),this._subs.add(this.subscribeToEvent(Me,this.subscribeToPasteTimeEvent));const a=(0,q.ir)(this).state.filters,o=(0,q.oY)(this).state.filters,u=(0,q.YS)(this);u.updateFilters([...o,...a]),this._subs.add(u.subscribeToState(this.subscribeToCombinedFieldsVariable));const d=function(e){const t=new Fe;let n=null;const r=De.subscribe(i.SetPanelAttentionEvent,(e=>{"string"==typeof e.payload.panelId&&(n=e.payload.panelId)}));function a(e,t){return()=>{const r=s.jh.findObject(e,(e=>e.state.key===n&&e.isActive));r&&r instanceof s.Eb&&t(r)}}return t.addBinding({key:"p l",onTrigger:a(e,$e)}),t.addBinding({key:"a l",onTrigger:function(e,t){return()=>{s.jh.findAllObjects(e,(e=>e instanceof s.Eb&&e.isActive)).forEach((e=>{e&&e instanceof s.Eb&&t(e)}))}}(e,$e)}),t.addBinding({key:"p x",onTrigger:a(e,function(){var e=_e((function*(e){const t=(0,Le.iD)(e);t&&W.locationService.push(t)}));return function(t){return e.apply(this,arguments)}}())}),t.addBinding({key:"t c",onTrigger:()=>{var t;t=s.jh.getTimeRange(e),window.__grafanaSceneContext,window.__grafanaSceneContext=t,De.publish(new Ae)}}),t.addBinding({key:"t v",onTrigger:()=>{const t=new Me({updateUrl:!1});e.publishEvent(t),De.publish(t)}}),t.addBinding({key:"d r",onTrigger:()=>s.jh.getTimeRange(e).onRefresh()}),t.addBinding({key:"t z",onTrigger:()=>{Ne(e)}}),t.addBinding({key:"ctrl+z",onTrigger:()=>{Ne(e)}}),t.addBinding({key:"t a",onTrigger:()=>{const t=(0,J.m0)(e);null==t||t.toAbsolute()}}),t.addBinding({key:"t left",onTrigger:()=>{Ie(e,"left")}}),t.addBinding({key:"t right",onTrigger:()=>{Ie(e,"right")}}),()=>{t.removeAll(),r.unsubscribe()}}(this);return()=>{d()}}setTagProviders(){this.setLabelsProviders()}setLabelsProviders(){const e=(0,q.cR)(this);e._getOperators=()=>(0,Ve.Ht)(e),e.setState({getTagKeysProvider:ce,getTagValuesProvider:ae})}limitMaxInterval(e){return(t,n)=>{const{jsonData:r}=Q.plugin.meta;if(null==r?void 0:r.interval)try{var a;const l=i.rangeUtil.intervalToSeconds(null!==(a=null==r?void 0:r.interval)&&void 0!==a?a:"");if(!l)return;const o=t.value.to.diff(t.value.from,"seconds");if(o>l){if(o<=n.value.to.diff(n.value.from,"seconds"))e.setState({value:n.value,from:n.from,to:n.to});else{const t=new s.JZ(U.sp);e.setState({value:t.state.value,from:t.state.from,to:t.state.to})}(0,W.getAppEvents)().publish({type:i.AppEvents.alertWarning.name,payload:["Time range interval exceeds maximum interval configured by the administrator."]}),(0,L.EE)("all","interval_too_long",{attempted_duration_seconds:o,configured_max_interval:l})}}catch(e){console.error(e)}}}setVariableProviders(){const e=(0,q.iw)(this),t=(0,q.YS)(this);t._getOperators=()=>(0,Ve.Ht)(t),e.setState({getTagValuesProvider:this.getLevelsTagValuesProvider(),getTagKeysProvider:this.getLevelsTagKeysProvider()}),t.setState({getTagKeysProvider:this.getCombinedFieldsTagKeysProvider(),getTagValuesProvider:this.getCombinedFieldsTagValuesProvider()})}getCombinedFieldsTagKeysProvider(){return(e,t)=>{const n=(0,q.oY)(this),r=(0,q.ir)(this),a=(0,ze.O)(l.sL),i=n.state.filters.filter((e=>e.key!==t)),o=r.state.filters.filter((e=>e.key!==t)),c=this.renderVariableFilters(l.mB,o),u=this.renderVariableFilters(l._P,i),d=a.replace(l.Gd,c).replace(l.w0,u);return de({expr:s.jh.interpolate(this,d),sceneRef:this,timeRange:s.jh.getTimeRange(this).state.value,variableType:l.sL})}}getCombinedFieldsTagValuesProvider(){return(e,t)=>{const n=(0,ze.O)(l.sL),r=(0,q.oY)(this),a=(0,q.ir)(this),i=r.state.filters.filter((e=>e.key!==t.key&&(0,ee.BG)(e.operator))),o=a.state.filters.filter((e=>e.key!==t.key&&(0,ee.BG)(e.operator))),c=this.renderVariableFilters(l.mB,o),u=this.renderVariableFilters(l._P,i),d=n.replace(l.Gd,c).replace(l.w0,u),p=s.jh.interpolate(this,d);return re(t,e,p,this,s.jh.getTimeRange(this).state.value,l.sL)}}getLevelsTagKeysProvider(){return(e,t)=>{const n=e.state.filters.filter((e=>e.key!==t)),r=this.renderVariableFilters(l._Y,n),a=(0,ze.O)(l._Y).replace(l.Gd,r);return de({expr:s.jh.interpolate(this,a),sceneRef:this,timeRange:s.jh.getTimeRange(this).state.value,variableType:l._Y})}}getLevelsTagValuesProvider(){return(e,t)=>{const n=e.state.filters.filter((e=>e.key!==t.key&&e.operator===X.w7.Equal)),r=this.renderVariableFilters(l._Y,n),a=(0,ze.O)(l._Y).replace(l.Gd,r),i=s.jh.interpolate(this,a);return re(t,e,i,this,s.jh.getTimeRange(this).state.value,l._Y)}}renderVariableFilters(e,t){if(e===l.mB)return(0,z.ZX)(t);if(e===l._P)return(0,z.E3)(t);if(e===l._Y)return(0,z.E3)(t);{const e=new Error("getFieldsTagValuesProvider only supports fields, metadata, and levels");throw v.v.error(e),e}}resetVariablesIfNotInUrl(e,t){const n=W.locationService.getLocation();null===new URLSearchParams(n.search).get(t)&&e.setState({filters:[]})}updatePatterns(e,t){var n;const r=(0,He.M)(null!==(n=e.patterns)&&void 0!==n?n:[]);t.changeValueTo(r)}getUrlState(){return{patterns:JSON.stringify(this.state.patterns)}}updateFromUrl(e){const t={};e.patterns&&"string"==typeof e.patterns&&(t.patterns=JSON.parse(e.patterns)),this.setState(t)}constructor(e){var t,n;const{variablesScene:r,unsub:a}=function(e,t){const n=new s.H9({name:l.MB,datasource:l.eL,layout:"combobox",label:"Labels",allowCustomValue:!0,filters:null!=t?t:[],expressionBuilder:z.VW,hide:H.zL.dontHide,key:"adhoc_service_filter",onAddCustomValue:z.c0});n._getOperators=function(){return Re.II};const r=new s.H9({name:l.mB,label:"Detected fields",applyMode:"manual",layout:"combobox",expressionBuilder:z.ZX,hide:H.zL.hideVariable,allowCustomValue:!0});r._getOperators=()=>Re.II;const a=new s.H9({name:l._P,label:"Metadata",applyMode:"manual",layout:"combobox",expressionBuilder:e=>(0,z.E3)(e),hide:H.zL.hideVariable,allowCustomValue:!0});a._getOperators=()=>Re.II;const i=new s.H9({name:l.sL,label:"Fields",applyMode:"manual",layout:"combobox",hide:H.zL.hideVariable,allowCustomValue:!0,onAddCustomValue:z.PP,skipUrlSync:!0}),c=new s.H9({name:l._Y,label:"Error levels",applyMode:"manual",layout:"vertical",expressionBuilder:z._q,hide:H.zL.hideVariable,supportsMultiValueOperators:!0}),u=new s.H9({name:l.NW,hide:H.zL.hideVariable,getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),expressionBuilder:z.CY,layout:"horizontal"});u._getOperators=()=>Re.eb;const d=new s.mI({name:l.EY,label:"Data source",value:e,pluginId:"loki"}),p=d.subscribeToState((e=>{const t=`${e.value}`;e.value&&(0,o.ke)(t)}));return{variablesScene:new s.Pj({variables:[d,n,r,c,a,i,new s.yP({name:l.uw,value:"",hide:H.zL.hideVariable}),new s.H9({name:l.WM,hide:H.zL.hideVariable,expressionBuilder:z.CY}),u,new G.m({name:l.QE,value:l.YN,skipUrlSync:!0,hide:H.zL.hideVariable,options:[{value:l.YN,label:l.YN}]})]}),unsub:p}}(null!==(n=(0,o.QB)())&&void 0!==n?n:"grafanacloud-logs",e.initialFilters),c=[new s.G1({key:"vars-row__datasource-labels-timepicker-button",direction:"row",children:[new s.vA({body:new y({key:"vars-labels",layout:"vertical",include:[l.MB]})}),new he.H({key:Ye,disabled:!0})]}),new y({key:"vars-metadata",layout:"vertical",include:[l._P]}),new y({key:"vars-fields",layout:"vertical",include:[l.mB]}),new y({key:A,layout:"horizontal",include:[l.EY]}),new y({key:$,layout:"vertical",include:[l.sL]}),new s.KE({key:"vars-timepicker"}),new s.WM({key:"vars-refresh"})];var u,d,p,g,h;"explore"===(0,R.FT)()&&W.config.featureToggles.exploreLogsAggregatedMetrics&&c.push(new K.s({key:"vars-toolbar",isOpen:!1})),super((g=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Je(e,t,n[t])}))}return e}({$timeRange:null!==(u=e.$timeRange)&&void 0!==u?u:new s.JZ({}),$variables:null!==(d=e.$variables)&&void 0!==d?d:r,controls:null!==(p=e.controls)&&void 0!==p?p:c,patterns:[]},e),h=null!=(h={body:new M({})})?h:{},Object.getOwnPropertyDescriptors?Object.defineProperties(g,Object.getOwnPropertyDescriptors(h)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(h)).forEach((function(e){Object.defineProperty(g,e,Object.getOwnPropertyDescriptor(h,e))})),g)),t=this,Je(this,"_urlSync",new s.So(this,{keys:["patterns"]})),Je(this,"subscribeToCombinedFieldsVariable",((e,t)=>{if(!(0,We.B)(e.filters,null==t?void 0:t.filters)){const t=e.filters.filter((e=>(0,se.OH)(e))),n=e.filters.filter((e=>!(0,se.OH)(e)));(0,q.ir)(this).updateFilters(n),(0,q.oY)(this).updateFilters(t)}})),Je(this,"subscribeToPasteTimeEvent",Qe((function*(){const e=yield function(){return Be.apply(this,arguments)}();if(e.isError)return;const n=s.jh.getTimeRange(t),r="string"==typeof e.range.to?e.range.to:void 0,a="string"==typeof e.range.from?e.range.from:void 0,l=i.rangeUtil.convertRawToRange(e.range);n&&l?n.setState({value:l,to:r,from:a}):v.v.error(new Error("Invalid time range from clipboard"),{msg:"Invalid time range from clipboard",sceneTimeRange:typeof n,to:null!=r?r:"",from:null!=a?a:""})}))),this._subs.add(a),this.addActivationHandler(this.onActivate.bind(this)),(0,J.hJ)(this).then((e=>{this.setState({ds:e})}))}}Je(Xe,"Component",(({model:e})=>{const{body:t}=e.useState();return(0,q.S9)(e).state.options.length?t?a().createElement(t.Component,{model:t}):a().createElement(u.LoadingPlaceholder,{text:"Loading..."}):a().createElement(qe,null)}))},3626:(e,t,n)=>{n.d(t,{k:()=>v,q:()=>m});var r=n(2672),a=n(5959),i=n.n(a),s=n(4750),l=n(6089),o=n(2007),c=n(3143),u=n(4793),d=n(1220);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){p(e,t,n[t])}))}return e}function h(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const v="levels-var-custom-renderer";class m extends r.Bs{onActivate(){this.onFilterChange(),(0,s.iw)(this).subscribeToEvent(r.oh,(()=>this.onFilterChange()))}onFilterChange(){const e=(0,s.iw)(this);e.state.filters.length&&this.setState({options:e.state.filters.map((e=>{var t,n;return{text:null!==(n=null===(t=e.valueLabels)||void 0===t?void 0:t[0])&&void 0!==n?n:e.value,selected:!0,value:e.value}}))})}constructor(e){super(h(g({},e),{isLoading:!1,visible:!1,key:v,isOpen:!1})),p(this,"getTagValues",(()=>{var e,t;this.setState({isLoading:!0});const n=(0,s.iw)(this);var r;const a=null==n||null===(t=n.state)||void 0===t||null===(e=t.getTagValuesProvider)||void 0===e?void 0:e.call(t,n,null!==(r=n.state.filters[0])&&void 0!==r?r:{key:c.e4});null==a||a.then((e=>{Array.isArray(e.values)&&this.setState({isLoading:!1,options:e.values.map((e=>{var t;return{text:e.text,value:null!==(t=e.value)&&void 0!==t?t:e.text,selected:n.state.filters.some((t=>t.value===e.text))}}))})}))})),p(this,"updateFilters",((e,t)=>{var n;const r=(0,s.iw)(this),a=null===(n=this.state.options)||void 0===n?void 0:n.filter((e=>e.selected));var i;r.updateFilters(null!==(i=null==a?void 0:a.map((e=>({key:c.e4,operator:u.w7.Equal,value:e.text}))))&&void 0!==i?i:[],{skipPublish:e,forcePublish:t})})),p(this,"onChangeOptions",(e=>{var t;this.setState({options:null===(t=this.state.options)||void 0===t?void 0:t.map((t=>e.some((e=>e.value===t.value))?h(g({},t),{selected:!0}):h(g({},t),{selected:!1})))}),this.state.isOpen?this.updateFilters(!0):this.updateFilters(!1)})),p(this,"openSelect",(e=>{this.setState({isOpen:e})})),p(this,"onCloseMenu",(()=>{this.openSelect(!1),this.updateFilters(!1,!0)})),this.addActivationHandler(this.onActivate.bind(this))}}p(m,"Component",(({model:e})=>{const{options:t,isLoading:n,visible:a,isOpen:s}=e.useState(),l=(0,o.useStyles2)(f);return a?i().createElement("div",{"data-testid":d.b.variables.levels.inputWrap},i().createElement(r.Zx,{layout:"vertical",label:"Log levels"}),i().createElement(o.MultiSelect,{"aria-label":"Log level filters",prefix:i().createElement(o.Icon,{size:"lg",name:"filter"}),placeholder:"All levels",className:l.flex,onChange:e.onChangeOptions,onCloseMenu:()=>e.onCloseMenu(),onOpenMenu:e.getTagValues,onFocus:()=>e.openSelect(!0),menuShouldPortal:!0,isOpen:s,isLoading:n,isClearable:!0,blurInputOnSelect:!1,closeMenuOnSelect:!1,openMenuOnFocus:!0,showAllSelectedWhenOpen:!0,hideSelectedOptions:!1,value:null==t?void 0:t.filter((e=>e.selected)),options:null==t?void 0:t.map((e=>({value:e.text,label:e.text})))})):null}));const f=e=>({flex:(0,l.css)({flex:"1"}),removeButton:(0,l.css)({marginInline:e.spacing(.5),cursor:"pointer","&:hover":{color:e.colors.text.primary}}),pillText:(0,l.css)({maxWidth:"200px",width:"100%",textOverflow:"ellipsis",overflow:"hidden"}),tooltipText:(0,l.css)({textAlign:"center"}),comboboxWrapper:(0,l.css)({display:"flex",flexWrap:"nowrap",alignItems:"center",columnGap:e.spacing(1),rowGap:e.spacing(.5),minHeight:e.spacing(4),backgroundColor:e.components.input.background,border:`1px solid ${e.colors.border.strong}`,borderRadius:e.shape.radius.default,paddingInline:e.spacing(1),paddingBlock:e.spacing(.5),flexGrow:1}),comboboxFocusOutline:(0,l.css)({"&:focus-within":{outline:"2px dotted transparent",outlineOffset:"2px",boxShadow:`0 0 0 2px ${e.colors.background.canvas}, 0 0 0px 4px ${e.colors.primary.main}`,transitionTimingFunction:"cubic-bezier(0.19, 1, 0.22, 1)",transitionDuration:"0.2s",transitionProperty:"outline, outline-offset, box-shadow",zIndex:2}}),filterIcon:(0,l.css)({color:e.colors.text.secondary,alignSelf:"center"})})},4916:(e,t,n)=>{n.d(t,{F:()=>f,Y:()=>m});var r=n(2672),a=n(5959),i=n.n(a),s=n(4750),l=n(4793),o=n(2718),c=n(3241),u=n(6089),d=n(2007),p=n(6261);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}function v(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class m extends r.Bs{isFilterExclusive({operator:e}){return e===l.cK.negativeMatch||e===l.cK.negativeRegex}updateFilter(e,t,n=!0){n?(this.updateVariableLineFilter(e,t,!0),this.updateVariableDebounced(e,t,!1,!0)):this.updateVariableLineFilter(e,t)}constructor(...e){super(...e),g(this,"handleEnter",((e,t,n)=>{"Enter"===e.key&&this.updateVariableLineFilter(n,v(h({},n),{value:t}))})),g(this,"onRegexToggle",(e=>{let t;switch(e.operator){case l.cK.match:t=l.cK.regex;break;case l.cK.negativeMatch:t=l.cK.negativeRegex;break;case l.cK.regex:t=l.cK.match;break;case l.cK.negativeRegex:t=l.cK.negativeMatch;break;default:throw new Error("Invalid operator!")}this.updateFilter(e,v(h({},e),{operator:t}),!1)})),g(this,"onToggleExclusive",(e=>{let t;switch(e.operator){case l.cK.match:t=l.cK.negativeMatch;break;case l.cK.negativeMatch:t=l.cK.match;break;case l.cK.regex:t=l.cK.negativeRegex;break;case l.cK.negativeRegex:t=l.cK.regex;break;default:throw new Error("Invalid operator!")}this.updateFilter(e,v(h({},e),{operator:t}),!1)})),g(this,"onCaseSensitiveToggle",(e=>{const t=e.key===l.ld.caseSensitive?l.ld.caseInsensitive:l.ld.caseSensitive;this.updateFilter(e,v(h({},e),{key:t}),!1)})),g(this,"onInputChange",((e,t)=>{this.updateFilter(t,v(h({},t),{value:e.target.value}),!0)})),g(this,"removeFilter",(e=>{const t=(0,s.Gk)(this),n=t.state.filters.filter((t=>void 0!==t.keyLabel&&t.keyLabel!==e.keyLabel));t.setState({filters:n})})),g(this,"updateVariableLineFilter",((e,t,n=!1,r=!1)=>{const a=(0,s.Gk)(this),i=a.state.filters.filter((t=>void 0!==t.keyLabel&&t.keyLabel!==e.keyLabel));a.updateFilters([{keyLabel:e.keyLabel,key:t.key,operator:t.operator,value:t.value},...i],{skipPublish:n,forcePublish:r}),(0,o.EE)(o.NO.service_details,o.ir.service_details.search_string_in_variables_changed,{searchQueryLength:e.value.length,containsLevel:e.value.toLowerCase().includes("level"),operator:t.operator,caseSensitive:t.key})})),g(this,"updateVariableDebounced",(0,c.debounce)(((e,t,n=!1,r=!1)=>{this.updateVariableLineFilter(e,t,n,r)}),1e3))}}function f(e){e.sort(((e,t)=>{var n,r;return parseInt(null!==(n=e.keyLabel)&&void 0!==n?n:"0",10)-parseInt(null!==(r=t.keyLabel)&&void 0!==r?r:"0",10)}))}function b(e){return{lineFiltersWrap:(0,u.css)({label:"lineFiltersWrap",display:"flex",flexWrap:"wrap",gap:`${e.spacing(.25)} ${e.spacing(2)}`}),wrapper:(0,u.css)({maxWidth:"300px"}),titleWrap:(0,u.css)({display:"flex",fontSize:e.typography.bodySmall.fontSize,marginBottom:e.spacing(.5),gap:e.spacing(1)})}}g(m,"Component",(({model:e})=>{const t=(0,s.Gk)(e),{filters:n}=t.useState(),r=(0,d.useStyles2)(b);return f(n),n.length?i().createElement("div",{className:r.lineFiltersWrap},n.map((t=>{const n={lineFilter:t.value,regex:t.operator===l.cK.regex||t.operator===l.cK.negativeRegex,caseSensitive:t.key===l.ld.caseSensitive,exclusive:e.isFilterExclusive(t),handleEnter:(n,r)=>e.handleEnter(n,t.value,t),setExclusive:()=>e.onToggleExclusive(t),updateFilter:(n,r)=>e.updateFilter(t,v(h({},t),{value:n}),r),onRegexToggle:()=>e.onRegexToggle(t),onInputChange:n=>e.onInputChange(n,t),onCaseSensitiveToggle:()=>e.onCaseSensitiveToggle(t)};return i().createElement("span",{key:t.keyLabel,className:r.wrapper},i().createElement("div",{className:r.titleWrap},i().createElement("span",null,"Line filter"),i().createElement(d.IconButton,{onClick:()=>e.removeFilter(t),name:"times",size:"xs","aria-label":"Line filter variable"})," "),i().createElement(p._,n))}))):null}))},6059:(e,t,n)=>{n.d(t,{H:()=>g});var r=n(2672),a=n(2007),i=n(5959),s=n.n(i),l=n(6089),o=n(8835),c=n(4750),u=n(1220),d=n(5111);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends r.Bs{onActivate(){const e=(0,c.cR)(this),t=e.state.filters.some((e=>(0,d.BG)(e.operator)));this.setState({disabled:!t}),e.subscribeToState((e=>{const t=e.filters.some((e=>(0,d.BG)(e.operator)));this.setState({disabled:!t})}))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){p(e,t,n[t])}))}return e}({},e)),p(this,"onClick",(()=>{const e=(0,c.cR)(this).state.filters.find((e=>(0,d.BG)(e.operator)));e&&(0,o.jY)(e.key,e.value)})),this.addActivationHandler(this.onActivate.bind(this))}}function h(e){return{button:(0,l.css)({[e.breakpoints.down("lg")]:{alignSelf:"flex-end"},[e.breakpoints.down("md")]:{marginTop:e.spacing(1),alignSelf:"flex-start"},alignSelf:"flex-start",marginTop:"22px"})}}p(g,"Component",(({model:e})=>{const{disabled:t,hidden:n}=e.useState(),r=(0,a.useStyles2)(h);return!0===n?null:s().createElement(a.Button,{"data-testid":u.b.index.header.showLogsButton,disabled:t,fill:"outline",className:r.button,onClick:e.onClick},"Show logs")}))},4002:(e,t,n)=>{n.d(t,{s:()=>v});var r=n(2672),a=n(2007),i=n(5959),s=n.n(i),l=n(6089),o=n(8531),c=n(2718),u=n(1105),d=n(2533),p=n(1220);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const h=`${d.id}.serviceSelection.aggregatedMetrics`;class v extends r.Bs{constructor(e){const t=localStorage.getItem(h),n=o.config.featureToggles.exploreLogsAggregatedMetrics&&"false"!==t;var r;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}({isOpen:!1,options:{aggregatedMetrics:{active:null!=n&&n,userOverride:null!==(r="true"===t)&&void 0!==r&&r,disabled:!1}}},e)),g(this,"toggleAggregatedMetricsOverride",(()=>{const e=!this.state.options.aggregatedMetrics.active;(0,c.EE)(c.NO.service_selection,c.ir.service_selection.aggregated_metrics_toggled,{enabled:e}),localStorage.setItem(h,e.toString()),this.setState({options:{aggregatedMetrics:{active:e,disabled:this.state.options.aggregatedMetrics.disabled,userOverride:e}}})})),g(this,"onToggleOpen",(e=>{this.setState({isOpen:e})}))}}function m(e){return{popover:(0,l.css)({display:"flex",padding:e.spacing(2),flexDirection:"column",background:e.colors.background.primary,boxShadow:e.shadows.z3,borderRadius:e.shape.radius.default,border:`1px solid ${e.colors.border.weak}`,zIndex:1,marginRight:e.spacing(2)}),heading:(0,l.css)({fontWeight:e.typography.fontWeightMedium,paddingBottom:e.spacing(2)}),options:(0,l.css)({display:"grid",gridTemplateColumns:"1fr 50px",rowGap:e.spacing(1),columnGap:e.spacing(2),alignItems:"center"})}}g(v,"Component",(({model:e})=>{const{isOpen:t,options:n}=e.useState(),r=(0,a.useStyles2)(m);return n.aggregatedMetrics?s().createElement(a.Dropdown,{overlay:()=>s().createElement("div",{className:r.popover,onClick:e=>e.stopPropagation()},s().createElement("div",{className:r.heading},"Query options"),s().createElement("div",{className:r.options},s().createElement("div",{title:"Aggregated metrics will return service queries results much more quickly, but with lower resolution"},"Aggregated metrics"),s().createElement("span",{title:n.aggregatedMetrics.disabled?`Aggregated metrics can only be enabled for queries starting after ${u.X.toLocaleString()}`:""},s().createElement(a.Switch,{label:"Toggle aggregated metrics","data-testid":p.b.index.aggregatedMetricsToggle,value:n.aggregatedMetrics.active,disabled:n.aggregatedMetrics.disabled,onChange:e.toggleAggregatedMetricsOverride})))),placement:"bottom",onVisibleChange:e.onToggleOpen},s().createElement(a.ToolbarButton,{icon:"cog",variant:"canvas",isOpen:t,"data-testid":p.b.index.aggregatedMetricsMenu})):s().createElement(s().Fragment,null)}))},4106:(e,t,n)=>{n.d(t,{Oo:()=>h,c:()=>g,sp:()=>d});var r=n(2672),a=n(892),i=n(7781),s=n(8538),l=n(8835),o=n(2871),c=n(8315),u=n(8831);const d={from:"now-15m",to:"now"};function p(e){return new r.P1({body:new s.P({$timeRange:new r.JZ(d),routeMatch:e})})}function g(){return new r.jD({title:"Grafana Logs Drilldown",url:(0,u._F)(a.G3.explore),layout:i.PageLayoutType.Custom,preserveUrlKeys:a.Zt,routePath:(0,u._F)(a.G3.explore),getScene:e=>p(e),drilldowns:[{routePath:a.HU.logs,getPage:(e,t)=>v(e,t,a.G3.logs),defaultRoute:!0},{routePath:a.HU.labels,getPage:(e,t)=>v(e,t,a.G3.labels)},{routePath:a.HU.patterns,getPage:(e,t)=>v(e,t,a.G3.patterns)},{routePath:a.HU.fields,getPage:(e,t)=>v(e,t,a.G3.fields)},{routePath:a.KL.label,getPage:(e,t)=>m(e,t,a._J.label)},{routePath:a.KL.field,getPage:(e,t)=>m(e,t,a._J.field)},{routePath:"*",getPage:()=>h()}]})}function h(){return new r.jD({title:"",url:u.Gy,getScene:()=>new r.P1({body:new r.G1({direction:"column",children:[]})}),hideFromBreadcrumbs:!0,routePath:"*",$behaviors:[()=>{(0,l.Ns)()}]})}function v(e,t,n){const{labelName:s,labelValue:l}=(0,a.XJ)(e);return new r.jD({title:(0,c.Zr)(n),layout:i.PageLayoutType.Custom,url:a.bw[n](l,s),preserveUrlKeys:a.tm,getParentPage:()=>t,getScene:e=>p(e)})}function m(e,t,n){const{labelName:s,labelValue:l,breakdownLabel:u}=(0,a.XJ)(e);if(!u){const e=new Error("Breakdown value missing!");throw o.v.error(e,{msg:"makeBreakdownValuePage: Breakdown value missing!",labelName:s,labelValue:l,breakdownLabel:null!=u?u:""}),e}return new r.jD({title:(0,c.Zr)(u),layout:i.PageLayoutType.Custom,url:a.mC[n](l,s,u),preserveUrlKeys:a.tm,getParentPage:()=>t,getScene:e=>p(e)})}},7085:(e,t,n)=>{n.d(t,{ls:()=>T,Ci:()=>D,GD:()=>N,iD:()=>I,K_:()=>R});var r=n(2672),a=n(5959),i=n.n(a),s=n(7781),l=n(8531),o=n(2007),c=n(9829),u=(n(1220),n(2718)),d=n(227),p=n(4011),g=n(9186);var h=n(8538),v=n(2871),m=n(7608);function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){f(e,t,n[t])}))}return e}function y(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class S extends r.Bs{constructor(e){super(y(b({},e),{queries:[]})),f(this,"onActivate",(()=>{(0,c.hJ)(this).then((e=>{this.setState({ds:e})})),this._subs.add(this.subscribeToState(((e,t)=>{this.state.queries.length||this.getQueries(),!this.state.context&&this.state.queries.length&&this.getContext()})))})),f(this,"getQueries",(()=>{const e=r.jh.getData(this),t=(0,c.UX)(e,(e=>e instanceof r.dt),r.dt);if(t){const e=this.state.frame?w(this.state.frame):null,n=t.state.queries.map((n=>{var a;return y(b({},n),{expr:r.jh.interpolate(t,n.expr),legendFormat:(null==e?void 0:e.name)?`{{ ${e.name} }}`:r.jh.interpolate(t,n.legendFormat),datasource:null!==(a=n.datasource)&&void 0!==a?a:void 0})}));JSON.stringify(n)!==JSON.stringify(this.state.queries)&&this.setState({queries:n})}})),f(this,"getContext",(()=>{const{queries:e,ds:t,labelName:n,fieldName:a,type:i}=this.state,s=r.jh.getTimeRange(this);if(!s||!e||!(null==t?void 0:t.uid))return;const l={origin:"Grafana Logs Drilldown",type:null!=i?i:"timeseries",queries:e,timeRange:b({},s.state.value),datasource:{uid:t.uid},url:window.location.href,id:`${JSON.stringify(e)}${n}${a}`,title:`${n}${a?` > ${a}`:""}`,logoPath:"public/plugins/grafana-lokiexplore-app/img/3d96a93cfcb32df74eef.svg",drillDownLabel:a};JSON.stringify(l)!==JSON.stringify(this.state.context)&&this.setState({context:l})})),this.addActivationHandler(this.onActivate)}}f(S,"Component",(({model:e})=>{const{context:t}=e.useState(),{links:n}=(0,l.usePluginLinks)({extensionPointId:m.R6.MetricInvestigation,context:t});return i().createElement(i().Fragment,null,n.filter((e=>"grafana-investigations-app"===e.pluginId&&e.onClick)).map((e=>{var t;return i().createElement(o.IconButton,{tooltip:e.description,"aria-label":"extension-link-to-open-exploration",key:e.id,name:null!==(t=e.icon)&&void 0!==t?t:"panel-add",onClick:t=>{e.onClick&&e.onClick(t)}})})))}));const w=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{};if(1!==Object.keys(r).length)return;const a=Object.keys(r)[0];return{name:a,value:r[a]}};var O=n(5183),x=n(3102),E=n(8516),C=n(4836),k=n(3630),F=n(6089);function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const P="Add to investigation",j="investigations_divider",_="Investigations";var T=function(e){return e.timeseries="timeseries",e.histogram="histogram",e}({}),D=function(e){return e.collapsed="Collapse",e.expanded="Expand",e}({});class N extends r.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){var t,n,a;super((n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){L(e,t,n[t])}))}return e}({},e),a=null!=(a={addInvestigationsLink:null===(t=e.addInvestigationsLink)||void 0===t||t})?a:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(a)).forEach((function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(a,e))})),n)),this.addActivationHandler((()=>{var e,t,n,a,i,s,l;const o=[{text:"Navigation",type:"group"},{text:"Explore",iconClassName:"compass",href:I(this),onClick:()=>$(),shortcut:"p x"}];let u;try{u=r.jh.getAncestor(this,r.Eb)}catch(e){return void this.setState({body:new r.Lw({items:o})})}var p;this.setState({investigationsButton:new S({labelName:(null===(e=this.state.investigationOptions)||void 0===e?void 0:e.getLabelName)?null===(t=this.state.investigationOptions)||void 0===t?void 0:t.getLabelName():null===(n=this.state.investigationOptions)||void 0===n?void 0:n.labelName,fieldName:null===(a=this.state.investigationOptions)||void 0===a?void 0:a.fieldName,frame:null===(i=this.state.investigationOptions)||void 0===i?void 0:i.frame,type:null===(s=this.state.investigationOptions)||void 0===s?void 0:s.type})}),this.state.addInvestigationsLink&&(null===(p=this.state.investigationsButton)||void 0===p||p.activate()),(this.state.panelType||(null==u?void 0:u.state.collapsible))&&function(e){e.push({text:"",type:"divider"}),e.push({text:"Visualization",type:"group"})}(o),(null==u?void 0:u.state.collapsible)&&function(e,t){const n=r.jh.getAncestor(t,r.Eb);e.push({text:n.state.collapsed?"Expand":"Collapse",iconClassName:n.state.collapsed?"table-collapse-all":"table-expand-all",onClick:()=>{const e=n.state.collapsed?"Expand":"Collapse",a=r.jh.getAncestor(t,r.G1);(0,E.Zb)(a,e),n.setState({collapsed:!n.state.collapsed}),(0,d.IW)("collapsed",e)}})}(o,this),this.state.panelType&&function(e,t){e.push({text:"histogram"!==t.state.panelType?"Histogram":"Time series",iconClassName:"histogram"!==t.state.panelType?"graph-bar":"chart-line",onClick:()=>{const e=r.jh.getAncestor(t,r.xK),n=r.jh.getAncestor(t,r.Eb).clone(),a=r.jh.getData(t).clone(),i=t.clone(),s=Array.isArray(n.state.headerActions)?n.state.headerActions.map((e=>e.clone())):n.state.headerActions;let l;l="histogram"!==t.state.panelType?r.d0.timeseries().setOverrides(O.jC):r.d0.histogram(),e.setState({body:l.setMenu(i).setTitle(n.state.title).setHeaderActions(s).setData(a).build()});const o="timeseries"!==t.state.panelType?"timeseries":"histogram";(0,d.IW)("panelType",o),i.setState({panelType:o});const u=(0,c.UX)(e,(e=>e instanceof x.E),x.E);u&&u.rebuildAvgFields(),A(o)}})}(o,this),this.setState({body:new r.Lw({items:o})}),this._subs.add(null===(l=this.state.investigationsButton)||void 0===l?void 0:l.subscribeToState((()=>{!function(e){const t=e.state.investigationsButton;if(t){var n;const o=M(t);var r;const c=null!==(r=null===(n=e.state.body)||void 0===n?void 0:n.state.items)&&void 0!==r?r:[],u=c.find((e=>e.text===P));var a,i,s,l;o&&(u?u&&(null===(a=e.state.body)||void 0===a||a.setItems(c.filter((e=>!1===[j,_,P].includes(e.text))))):(null===(i=e.state.body)||void 0===i||i.addItem({text:j,type:"divider"}),null===(s=e.state.body)||void 0===s||s.addItem({text:_,type:"group"}),null===(l=e.state.body)||void 0===l||l.addItem({text:P,iconClassName:"plus-square",onClick:e=>B(e,t)})))}}(this)})))}))}}L(N,"Component",(({model:e})=>{const{body:t}=e.useState();return t?i().createElement(t.Component,{model:t}):i().createElement(i().Fragment,null)}));const I=e=>{const t=r.jh.getAncestor(e,h.P),n=r.jh.getData(e);let a=n instanceof r.dt?n:(0,c.oh)(n)[0];if(!a){const t=r.jh.findObject(e,(e=>e instanceof C.u||e instanceof k.u));if(t){const e=r.jh.getData(t);a=e instanceof r.dt?e:(0,c.oh)(e)[0]}else v.v.error(new Error("Unable to locate query runner!"),{msg:"PanelMenu - getExploreLink: Unable to locate query runner!"})}const i=a.state.queries[0].expr;return((e,t,n=!1)=>{t||(t=(0,c.u9)(e)),t=t.replace(/\s+/g," ").trimEnd();const a=(0,c.U4)(e),i=r.jh.getTimeRange(e).state.value,o=(0,d.N$)(e),u=(0,d.k5)(),h=function(){const e=new URLSearchParams(window.location.search).get("urlColumns");if(e)try{const t=(0,p.aJ)(JSON.parse(e));let n={};for(const e in t)n[e]=t[e];return n}catch(e){console.error(e)}}(),v=JSON.stringify({"loki-explore":{range:(0,s.toURLRange)(i.raw),queries:[{refId:"logs",expr:t,datasource:a}],panelsState:{logs:{displayedFields:o,visualisationType:u,columns:h,labelFieldName:"table"===u?g.bz:void 0}},datasource:a}});var m;const f=null!==(m=l.config.appSubUrl)&&void 0!==m?m:"",b=s.urlUtil.renderUrl(`${f}/explore`,{panes:v,schemaVersion:1});return n&&window.open(b,"_blank"),b})(t,r.jh.interpolate(e,i))},$=()=>{(0,u.EE)(u.NO.all,u.ir.all.open_in_explore_menu_clicked)},A=e=>{(0,u.EE)(u.NO.service_details,u.ir.service_details.change_viz_type,{newVizType:e})},M=e=>(0,l.getPluginLinkExtensions)({extensionPointId:m.R6.MetricInvestigation,context:e.state.context}).extensions[0],B=(e,t)=>{const n=M(t);n&&n.onClick&&n.onClick(e)},R=e=>({panelWrapper:(0,F.css)({width:"100%",height:"100%",label:"panel-wrapper",position:"absolute",display:"flex","button.show-on-hover":{opacity:1,visibility:"visible",background:"none","&:hover":{background:e.colors.secondary.shade}}})})},558:(e,t,n)=>{n.d(t,{Of:()=>f,PT:()=>C,Qt:()=>E,VT:()=>O,XI:()=>y,hi:()=>S,oR:()=>L,ts:()=>k,vn:()=>x});var r=n(5959),a=n.n(r),i=n(7781),s=n(2672),l=n(5435),o=n(2718),c=n(3143),u=n(5218),d=n(2254),p=n(7097),g=n(4750),h=n(4793),v=n(9055);function m(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class f extends i.BusEventBase{constructor(e,t,n){super(),m(this,"operator",void 0),m(this,"key",void 0),m(this,"value",void 0),this.operator=e,this.key=t,this.value=n}}m(f,"type","add-filter");class b extends i.BusEventBase{constructor(e,t,n){super(),m(this,"key",void 0),m(this,"value",void 0),m(this,"operator",void 0),this.key=e,this.value=t,this.operator=n}}function y(e,t,n){const r="="===e.operator?"include":"exclude";E(e.key,e.value,r,t,n)}function S(e,t,n,r,a){n||(n=F(e,t));const i=j(n,e,t);let s=i.state.filters.filter((t=>{const n=(0,g.z2)(i,t);return r&&a?!(t.key===e&&n.value===r&&t.operator===a):r?!(t.key===e&&n.value===r):a?!(t.key===e&&t.operator===a):!(t.key===e)}));t.publishEvent(new b(e,r,a),!0),i.setState({filters:s})}m(b,"type","add-filter");const w=e=>e===h.w7.gt||e===h.w7.gte?"greater":e===h.w7.lt||e===h.w7.lte?"lesser":void 0;function O(e,t,n,r){r||(r=F(e,t));const a=j(r,e,t),i=n?w(n):void 0;let s=a.state.filters.filter((t=>!(t.key===e&&(w(t.operator)===i||t.operator===h.w7.NotEqual))));a.setState({filters:s})}function x(e,t,n,r,a){const i=w(n);a||(a=F(e,r));const s=j(a,e,r);let l;a===c.mB&&(l=JSON.stringify({value:t,parser:(0,p.Ri)(e,r)}));let o=s.state.filters.filter((t=>!(t.key===e&&(w(t.operator)===i||t.operator===h.w7.NotEqual))));o=[...o,{key:e,operator:n,value:l||t,valueLabels:[t]}],r.publishEvent(new f(n,e,t),!0),s.setState({filters:o})}function E(e,t,n,r,a){a||(a=F(e,r)),a===c.MB&&(0,v._J)(e,t,r);const i=j(a,e,r);let s;a===c.mB&&(s=JSON.stringify({value:t,parser:(0,p.Ri)(e,r)}));let l=i.state.filters.filter((r=>{const a=(0,g.z2)(i,r);return"include"===n?!(r.key===e&&r.operator!==h.w7.Equal):!(r.key===e&&a.value===t)}));const o=l.length!==i.state.filters.length;("include"===n||"exclude"===n||!o&&"toggle"===n)&&(l=[...l,{key:e,operator:"exclude"===n?h.w7.NotEqual:h.w7.Equal,value:s||t,valueLabels:[t]}]),r.publishEvent(new f(n,e,t),!0),i.setState({filters:l})}function C(e,t,n,r,a){j(a,e,r).setState({filters:[{key:e,operator:"exclude"===n?h.w7.NotEqual:h.w7.Equal,value:t}],hide:l.zL.hideLabel})}function k(e,t){return e===c.e4?c._Y:t}function F(e,t){var n,r;return(null===(r=(0,d.TG)(t))||void 0===r||null===(n=r.fields)||void 0===n?void 0:n.find((t=>t.name===e)))?c.MB:c.mB}class L extends s.Bs{constructor(...e){super(...e),m(this,"onClick",(e=>{const t=P(this.state.frame);if(!t)return;E(t.name,t.value,e,this,this.state.variableName);const n=j(this.state.variableName,t.name,this);(0,o.EE)(o.NO.service_details,o.ir.service_details.add_to_filters_in_breakdown_clicked,{filterType:this.state.variableName,key:t.name,action:e,filtersLength:(null==n?void 0:n.state.filters.length)||0})})),m(this,"isSelected",(()=>{const e=P(this.state.frame);if(!e)return{isIncluded:!1,isExcluded:!1};const t=j(this.state.variableName,e.name,this),n=t.state.filters.find((n=>{const r=(0,g.z2)(t,n);return n.key===e.name&&r.value===e.value}));return n?{isIncluded:n.operator===h.w7.Equal,isExcluded:n.operator===h.w7.NotEqual}:{isIncluded:!1,isExcluded:!1}}))}}m(L,"Component",(({model:e})=>{const{isIncluded:t,isExcluded:n}=e.isSelected();return a().createElement(u.F,{buttonFill:"outline",isIncluded:t,isExcluded:n,onInclude:()=>e.onClick("include"),onClear:()=>e.onClick("clear"),onExclude:()=>e.onClick("exclude")})}));const P=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{};if(1!==Object.keys(r).length)return;const a=Object.keys(r)[0];return{name:a,value:r[a]}},j=(e,t,n)=>e===c.mB||e===c._P?(0,g.YS)(n):(0,g.bY)(k(t,e),n)},1022:(e,t,n)=>{n.d(t,{G:()=>v,x:()=>g});var r=n(2672),a=n(5959),i=n.n(a),s=n(5631),l=n(8642),o=n(8810),c=n(4105),u=n(7781),d=n(2871);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends u.BusEventBase{}p(g,"type","breakdown-search-reset");const h={};class v extends r.Bs{filterValues(e){const t=r.jh.findObject(this,(e=>e instanceof o.O||e instanceof c.J6));if(t instanceof o.O||t instanceof c.J6){h[this.cacheKey]=e;const n=r.jh.findDescendents(t,s.h);null==n||n.forEach((t=>{t.state.body.isActive&&t.filterByString(e)}))}else d.v.warn("unable to find Breakdown scene",{typeofBody:typeof t,filter:e})}constructor(e){var t;super({filter:null!==(t=h[e])&&void 0!==t?t:""}),p(this,"cacheKey",void 0),p(this,"onValueFilterChange",(e=>{this.setState({filter:e.target.value}),this.filterValues(e.target.value)})),p(this,"clearValueFilter",(()=>{this.setState({filter:""}),this.filterValues("")})),p(this,"reset",(()=>{this.setState({filter:""}),h[this.cacheKey]=""})),this.cacheKey=e}}p(v,"Component",(({model:e})=>{const{filter:t}=e.useState();return i().createElement(l.D,{value:t,onChange:e.onValueFilterChange,onClear:e.clearValueFilter,placeholder:"Search for value"})}))},5631:(e,t,n)=>{n.d(t,{h:()=>b});var r=n(5959),a=n.n(r),i=n(7781),s=n(2672),l=n(5722),o=n(4932),c=n(4144),u=n(2007),d=n(6089),p=n(1022),g=n(1269),h=n(738),v=n(8516),m=n(2871);function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class b extends s.Bs{performRepeat(e){const t=[],n=(0,l.sortSeries)(e.series,this.sortBy,this.direction);for(let e=0;e<n.length;e++){const r=this.state.getLayoutChild(n[e],e);t.push(r)}this.sortedSeries=n,this.unfilteredChildren=t,this.getFilter()?(this.state.body.setState({children:[]}),this.filterByString(this.getFilter())):this.state.body.setState({children:t})}filterSummaryChart(e){const t=s.jh.getAncestor(this,h.U);if(t){const n=s.jh.findAllObjects(t,(e=>e.isActive&&e.state.key===v.s$));if(n[0]instanceof s.G1){const t=s.jh.findDescendents(n[0],s.Eb)[0];t instanceof s.Eb?t.setState({$data:new s.Es({transformations:[()=>{return t=e[0],e=>e.pipe((0,g.map)((e=>{if(!t||!t.length)return e;let n=[];return e.forEach((e=>{const r=(0,c.ee)(e);t.includes(r)&&n.push(e)})),n})));var t}]})}):m.v.warn("filterSummaryChart: VizPanel not found",{typeofPanel:typeof t})}else m.v.warn("filterSummaryChart: SceneFlexItem not found",{typeofGraphParent:typeof n})}}constructor(e){var{sortBy:t,direction:n,getFilter:r}=e;super(function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(e,["sortBy","direction","getFilter"])),f(this,"unfilteredChildren",[]),f(this,"sortBy",void 0),f(this,"direction",void 0),f(this,"sortedSeries",[]),f(this,"getFilter",void 0),f(this,"sort",((e,t)=>{const n=s.jh.getData(this);this.sortBy=e,this.direction=t,n.state.data&&this.performRepeat(n.state.data)})),f(this,"iterateFrames",(e=>{if(s.jh.getData(this).state.data)for(let t=0;t<this.sortedSeries.length;t++)e(this.sortedSeries,t)})),f(this,"filterByString",(e=>{let t=[];this.iterateFrames(((e,n)=>{const r=(0,c.ee)(e[n]);t.push(r)})),(0,o.X)(t,e,(e=>{e&&e[0]?this.filterFrames((t=>{const n=(0,c.ee)(t);return e[0].includes(n)})):this.filterFrames((()=>!0)),this.filterSummaryChart(e)}))})),f(this,"filterFrames",(e=>{const t=[];if(this.iterateFrames(((n,r)=>{e(n[r])&&t.push(this.unfilteredChildren[r])})),0===t.length){const e=this.getFilter();this.state.body.setState({children:[y(e,this.clearFilter)]})}else this.state.body.setState({children:t})})),f(this,"clearFilter",(()=>{this.publishEvent(new p.x,!0)})),this.sortBy=t,this.direction=n,this.getFilter=r,this.addActivationHandler((()=>{const e=s.jh.getData(this);this._subs.add(e.subscribeToState(((e,t)=>{var n,r,a,s;((null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done||(null===(r=e.data)||void 0===r?void 0:r.state)===i.LoadingState.Streaming&&e.data.series.length>(null!==(s=null===(a=t.data)||void 0===a?void 0:a.series.length)&&void 0!==s?s:0))&&this.performRepeat(e.data)}))),e.state.data&&this.performRepeat(e.state.data)}))}}function y(e,t){return new s.G1({direction:"row",children:[new s.vA({body:new s.dM({reactNode:a().createElement("div",{className:S.alertContainer},a().createElement(u.Alert,{title:"",severity:"info",className:S.noResultsAlert},"No values found matching “",e,"”",a().createElement(u.Button,{className:S.clearButton,onClick:t},"Clear filter")))})})]})}f(b,"Component",(({model:e})=>{const{body:t}=e.useState();return a().createElement(t.Component,{model:t})}));const S={alertContainer:(0,d.css)({flexGrow:1,display:"flex",justifyContent:"center",alignItems:"center"}),noResultsAlert:(0,d.css)({minWidth:"30vw",flexGrow:0}),clearButton:(0,d.css)({marginLeft:"1.5rem"})}},3782:(e,t,n)=>{n.d(t,{a:()=>c});var r=n(2672),a=n(4482),i=n(2007),s=n(5959),l=n.n(s),o=n(4105);class c extends r.Bs{static Component({model:e}){const{type:t}=e.useState();return l().createElement(a.R,null,l().createElement(i.Alert,{title:"",severity:"warning"},"We did not find any ",t," for the given timerange. Please"," ",l().createElement("a",{className:o.ZI.link,href:"https://forms.gle/1sYWCTPvD72T1dPH9",target:"_blank",rel:"noopener noreferrer"},"let us know")," ","if you think this is a mistake."))}}},4462:(e,t,n)=>{n.d(t,{f:()=>d,u:()=>u});var r=n(6089),a=n(5959),i=n.n(a),s=n(2007),l=n(1220),o=n(7918);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u({options:e,value:t,onChange:n,label:r}){const l=(0,s.useStyles2)(p),[o,c]=(0,a.useState)(!1),u=e.map((e=>({label:e.label,value:e.value})));return i().createElement(s.InlineField,{className:l.selectWrapper,label:r},i().createElement(s.Select,{options:u,value:t,onOpenMenu:()=>c(!0),onCloseMenu:()=>c(!1),onChange:e=>n(e.value),className:l.select,prefix:o?void 0:i().createElement(s.Icon,{name:"search"})}))}function d({options:e,value:t,onChange:n,label:r,selectOption:u,isLoading:d,initialFilter:g}){var h;const v=(0,s.useStyles2)(p),[m,f]=(0,a.useState)(!1),[b,y]=(0,a.useState)(g),S=e.map((e=>({label:e.label,value:e.value}))),w=b&&t&&(null===(h=b.value)||void 0===h?void 0:h.includes(t))?[b,...S]:S,O=null==w?void 0:w.find((e=>e.value===t));return i().createElement(s.InlineField,{className:v.serviceSceneSelectWrapper,label:r},i().createElement(s.Select,{isLoading:d,"data-testid":l.b.exploreServiceSearch.search,placeholder:"Search values",options:w,isClearable:!0,value:t,onOpenMenu:()=>f(!0),onCloseMenu:()=>f(!1),allowCustomValue:!0,prefix:m||(null==O?void 0:O.__isNew__)?void 0:i().createElement(s.Icon,{name:"search"}),onChange:(e,t)=>{return(null==e?void 0:e.__isNew__)||(null==e?void 0:e.icon)?(y((r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){c(e,t,n[t])}))}return e}({},e),a=null!=(a={icon:"filter"})?a:{},Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(a)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(a)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(a,e))})),r)),n(e.value)):"clear"===t.action?n(""):void("select-option"===t.action&&e.value&&!e.__isNew__&&u(e.value));var r,a},onInputChange:(e,t)=>{const r=t;return"input-change"===r.action?n(e):"menu-close"===r.action&&r.prevInputValue?(y({value:(0,o.vC)(r.prevInputValue),label:r.prevInputValue,icon:"filter",__isNew__:!0}),n(r.prevInputValue)):void 0}}))}function p(e){return{input:(0,r.css)({marginBottom:0}),select:(0,r.css)({maxWidth:e.spacing(64),minWidth:e.spacing(20)}),selectWrapper:(0,r.css)({label:"field-selector-select-wrapper",maxWidth:e.spacing(62.5),minWidth:e.spacing(20),marginRight:e.spacing.x1,marginBottom:0}),serviceSceneSelectWrapper:(0,r.css)({label:"service-select-wrapper",maxWidth:e.spacing(62.5),minWidth:e.spacing(20),marginRight:e.spacing.x1,marginBottom:0})}}},4836:(e,t,n)=>{n.d(t,{u:()=>L});var r,a,i,s=n(2672),l=n(7918),o=n(227),c=n(7781),u=n(738),d=n(5183),p=n(5631),g=n(2007),h=n(7097),v=n(4144),m=n(3143),f=n(5959),b=n.n(f),y=n(4105),S=n(558),w=n(8835),O=n(892),x=n(2254),E=n(5722),C=n(4750),k=n(7085),F=n(8516);class L extends s.Bs{static Selector({model:e}){const{body:t}=e.useState();return t instanceof u.U?b().createElement(b().Fragment,null,t&&b().createElement(u.U.Selector,{model:t})):b().createElement(b().Fragment,null)}onActivate(){var e;const t=(0,C.Hj)(this),n=String(t.state.value),r=(0,C.ir)(this),a=(0,x.rD)(this),i=(0,h.Jl)(n,r,a),o=(0,l.l)(i,{legendFormat:`{{${n}}}`,refId:n});this.setState({body:this.build(o),$data:new s.Es({$data:(0,d.rS)([o]),transformations:[]})}),this._subs.add(this.subscribeToEvent(S.Of,(e=>{this.setState({lastFilterEvent:e})}))),this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState((e=>{this.onValuesDataQueryChange(e,o)})))}onValuesDataQueryChange(e,t){var n,r;if((null===(n=e.data)||void 0===n?void 0:n.state)===c.LoadingState.Done){var a;const n=this.state.lastFilterEvent;(null===(a=e.data)||void 0===a?void 0:a.state)===c.LoadingState.Done&&n&&("exclude"===n.operator&&e.data.series.length<1&&this.navigateToFields(),"include"===n.operator&&e.data.series.length<=1&&this.navigateToFields()),this.state.body instanceof s.dM&&this.setState({body:this.build(t)})}(null===(r=e.data)||void 0===r?void 0:r.state)===c.LoadingState.Error&&this.setErrorState(e.data.errors)}setErrorState(e){this.setState({body:new s.dM({reactNode:b().createElement(g.Alert,{title:"Something went wrong with your request",severity:"error"},null==e?void 0:e.map(((e,t)=>b().createElement("div",{key:t},e.status&&b().createElement(b().Fragment,null,b().createElement("strong",null,"Status"),": ",e.status," ",b().createElement("br",null)),e.message&&b().createElement(b().Fragment,null,b().createElement("strong",null,"Message"),": ",e.message," ",b().createElement("br",null)),e.traceId&&b().createElement(b().Fragment,null,b().createElement("strong",null,"TraceId"),": ",e.traceId)))))})})}navigateToFields(){this.setState({lastFilterEvent:void 0}),(0,w.Vt)(O.G3.fields,s.jh.getAncestor(this,x.Mn))}build(e){const t=(0,C.Hj)(this),n=String(t.state.value),{sortBy:r,direction:a}=(0,o.vs)("fields",E.DEFAULT_SORT_BY,"desc"),i=s.jh.getAncestor(this,y.J6),l=()=>{var e;return null!==(e=i.state.search.state.filter)&&void 0!==e?e:""},c=(0,h.Ri)(n,this);return new u.U({options:[{value:"single",label:"Single"},{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new s.G1({direction:"column",children:[new s.dM({reactNode:b().createElement(y.J6.LabelsMenu,{model:i})}),new s.vA({minHeight:300,body:s.d0.timeseries().setTitle(n).setMenu(new k.GD({})).build()})]}),new s.G1({direction:"column",children:[new s.dM({reactNode:b().createElement(y.J6.LabelsMenu,{model:i})}),new F.s7({title:n}),new s.dM({reactNode:b().createElement(y.J6.ValuesMenu,{model:i})}),new p.h({body:new s.gF({templateColumns:y.OK,autoRows:"200px",children:[new s.vA({body:new s.dM({reactNode:b().createElement(g.LoadingPlaceholder,{text:"Loading..."})})})],isLazy:!0}),getLayoutChild:(0,h.Zp)(v.ee,(null==e?void 0:e.expr.includes("count_over_time"))?g.DrawStyle.Bars:g.DrawStyle.Line,"structuredMetadata"===c?m._P:m.mB,s.jh.getAncestor(this,y.J6).state.sort,n),sortBy:r,direction:a,getFilter:l})]}),new s.G1({direction:"column",children:[new s.dM({reactNode:b().createElement(y.J6.LabelsMenu,{model:i})}),new F.s7({title:n}),new s.dM({reactNode:b().createElement(y.J6.ValuesMenu,{model:i})}),new p.h({body:new s.gF({templateColumns:"1fr",autoRows:"200px",children:[new s.vA({body:new s.dM({reactNode:b().createElement(g.LoadingPlaceholder,{text:"Loading..."})})})],isLazy:!0}),getLayoutChild:(0,h.Zp)(v.ee,(null==e?void 0:e.expr.includes("count_over_time"))?g.DrawStyle.Bars:g.DrawStyle.Line,"structuredMetadata"===c?m._P:m.mB,s.jh.getAncestor(this,y.J6).state.sort,n),sortBy:r,direction:a,getFilter:l})]})]})}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}i=({model:e})=>{const{body:t}=e.useState(),n=(0,g.useStyles2)(k.K_);return t?b().createElement("span",{className:n.panelWrapper},t&&b().createElement(t.Component,{model:t})):b().createElement(g.LoadingPlaceholder,{text:"Loading..."})},(a="Component")in(r=L)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},3102:(e,t,n)=>{n.d(t,{E:()=>x});var r=n(2672),a=n(3143),i=n(7918),s=n(5183),l=n(2007),o=n(738),c=n(4105),u=n(2254),d=n(5959),p=n.n(d),g=n(9016),h=n(892),v=n(7781),m=n(7097),f=n(4750),b=n(7085),y=n(2871),S=n(227),w=n(1752);function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class x extends r.Bs{updateChildren(e,t=void 0){var n;const a=(0,u.UO)(e),i=(0,u.nU)(e),s=(0,u.dB)(e),l=this.calculateCardinalityMap(e);null===(n=this.state.body)||void 0===n||n.state.layouts.forEach((e=>{if(e instanceof r.gF){const n=new Set(null==i?void 0:i.values),o=e.state.children;for(let l=0;l<o.length;l++){const c=e.state.children[l];if(c instanceof r.xK){const e=c.state.body;if(e instanceof r.Eb){if(t){const n=null==i?void 0:i.values.indexOf(e.state.title);if((n&&-1!==n?null==s?void 0:s.values[n]:void 0)!==t){const t=(0,m.ph)(e.state.title,a),n=this.getQueryRunnerForPanel(e.state.title,a,t);e.setState({$data:n})}}n.has(e.state.title)?n.delete(e.state.title):(o.splice(l,1),l--)}else y.v.warn("panel is not VizPanel!")}else y.v.warn("gridItem is not SceneCSSGridItem")}const c=Array.from(n).map((e=>e));o.push(...this.buildChildren(c)),o.sort(this.sortChildren(l)),o.map((e=>{this.subscribeToPanel(e)})),e.setState({children:o})}else y.v.warn("Layout is not SceneCSSGridLayout")}))}sortChildren(e){return(t,n)=>{const r=t.state.body,a=n.state.body;var i;const s=null!==(i=e.get(r.state.title))&&void 0!==i?i:0;var l;return(null!==(l=e.get(a.state.title))&&void 0!==l?l:0)-s}}calculateCardinalityMap(e){const t=(0,u.UO)(e),n=new Map;if(null==t?void 0:t.length)for(let e=0;e<(null==t?void 0:t.length);e++){const r=t.fields[0].values[e],a=t.fields[1].values[e];n.set(r,a)}return n}onActivate(){var e;this.setState({body:this.build()});const t=r.jh.getAncestor(this,u.Mn);void 0===t.state.fieldsCount&&this.updateFieldCount(),this._subs.add(null===(e=t.state.$detectedFieldsData)||void 0===e?void 0:e.subscribeToState(this.onDetectedFieldsChange)),this._subs.add(this.subscribeToFieldsVar())}subscribeToFieldsVar(){return(0,f.ir)(this).subscribeToState(((e,t)=>{const n=r.jh.getAncestor(this,u.Mn),a=e.filters.map((e=>(0,f.bu)(e).parser)),i=t.filters.map((e=>(0,f.bu)(e).parser)),s=(0,m.Qg)(a);if(s!==(0,m.Qg)(i)){var l;const e=null===(l=n.state.$detectedFieldsData)||void 0===l?void 0:l.state;e&&this.updateChildren(e,s)}}))}build(){var e;const t=(0,f.Hj)(this).state.options.map((e=>String(e.value)));r.jh.getAncestor(this,c.J6).state.search.reset();const n=this.buildChildren(t),a=r.jh.getAncestor(this,u.Mn),i=this.calculateCardinalityMap(null===(e=a.state.$detectedFieldsData)||void 0===e?void 0:e.state);n.sort(this.sortChildren(i));const s=n.map((e=>e.clone()));return[...n,...s].map((e=>{this.subscribeToPanel(e)})),new o.U({options:[{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new r.gF({templateColumns:c.OK,autoRows:"200px",children:n,isLazy:!0}),new r.gF({templateColumns:"1fr",autoRows:"200px",children:s,isLazy:!0})]})}subscribeToPanel(e){const t=e.state.body;var n;t&&this._subs.add(null==t||null===(n=t.state.$data)||void 0===n?void 0:n.getResultsStream().subscribe((t=>{t.data.errors&&t.data.errors.length>0&&(e.setState({isHidden:!0}),this.updateFieldCount())})))}rebuildAvgFields(){const e=(0,u.rD)(this),t=this.getActiveGridLayouts(),n=[];var a;const i=null!==(a=(0,S.ex)("panelType",[b.ls.histogram,b.ls.timeseries]))&&void 0!==a?a:b.ls.timeseries;null==t||t.state.children.forEach((t=>{if(t instanceof r.xK&&!t.state.isHidden){const a=r.jh.findDescendents(t,r.Eb);if(a.length){const r=a[0].state.title,s=(0,m.ph)(r,e);if((0,m.JI)(s)){const t=this.buildChild(r,e,i);t&&n.push(t)}else n.push(t)}}})),n.length&&(null==t||t.setState({children:n}))}buildChildren(e){const t=[],n=(0,u.rD)(this);var r;const i=null!==(r=(0,S.ex)("panelType",[b.ls.timeseries,b.ls.histogram]))&&void 0!==r?r:b.ls.timeseries;for(const r of e){if(r===a.To||!r)continue;const e=this.buildChild(r,n,i);e&&t.push(e)}return t}buildChild(e,t,n){if(e===a.To||!e)return;const i=(0,m.ph)(e,t),o=this.getQueryRunnerForPanel(e,t,i);let c;const u=[];(0,m.JI)(i)?(c="histogram"===n?r.d0.histogram():r.d0.timeseries(),c.setTitle(e).setData(o).setMenu(new b.GD({investigationOptions:{labelName:e},panelType:n})),u.push(new g.X({labelName:String(e),hideValueDrilldown:!0,fieldType:h._J.field}))):(c=r.d0.timeseries().setTitle(e).setData(o).setMenu(new b.GD({investigationOptions:{labelName:e}})).setCustomFieldConfig("stacking",{mode:l.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",l.DrawStyle.Bars).setOverrides(s.jC),u.push(new g.X({labelName:String(e),fieldType:h._J.field}))),c.setHeaderActions(u),c.setSeriesLimit(w.l);const d=c.build();return new r.xK({body:d})}getQueryRunnerForPanel(e,t,n){const r=(0,f.ir)(this),a=(0,m.Jl)(e,r,t),l=(0,i.l)(a,{legendFormat:(0,m.JI)(n)?e:`{{${e}}}`,refId:e});return(0,s.rS)([l])}getActiveGridLayouts(){var e,t,n;return null!==(n=null===(e=this.state.body)||void 0===e?void 0:e.state.layouts.find((e=>e.isActive)))&&void 0!==n?n:null===(t=this.state.body)||void 0===t?void 0:t.state.layouts[0]}updateFieldCount(){var e,t;const n=this.getActiveGridLayouts(),a=null==n?void 0:n.state.children,i=null==a?void 0:a.filter((e=>!e.state.isHidden));var s;null===(e=(t=r.jh.getAncestor(this,c.J6).state).changeFieldCount)||void 0===e||e.call(t,null!==(s=null==i?void 0:i.length)&&void 0!==s?s:0)}static Selector({model:e}){const{body:t}=e.useState();return p().createElement(p().Fragment,null,t&&p().createElement(o.U.Selector,{model:t}))}constructor(e){super(e),O(this,"onDetectedFieldsChange",(e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===v.LoadingState.Done&&this.updateChildren(e)})),this.addActivationHandler(this.onActivate.bind(this))}}O(x,"Component",(({model:e})=>{const{body:t}=e.useState(),n=(0,l.useStyles2)(b.K_);return t?p().createElement("span",{className:n.panelWrapper},t&&p().createElement(t.Component,{model:t})):p().createElement(l.LoadingPlaceholder,{text:"Loading..."})}))},4105:(e,t,n)=>{n.d(t,{J6:()=>N,OK:()=>D,ZI:()=>I});var r=n(6089),a=n(5959),i=n.n(a),s=n(7781),l=n(2672),o=n(2007),c=n(2718),u=n(227),d=n(3143),p=n(833),g=n(5431),h=n(8835),v=n(892),m=n(5722),f=n(8538),b=n(2254),y=n(1022),S=n(5631),w=n(3102),O=n(4462),x=n(4836),E=n(738),C=n(4144),k=n(9570),F=n(6001),L=n(3782),P=n(4750),j=n(9558),_=n(1863);function T(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const D="repeat(auto-fit, minmax(400px, 1fr))";class N extends l.Bs{onActivate(){var e,t,n;const r=(0,P.Hj)(this),a=l.jh.getAncestor(this,b.Mn);this.setState({loading:(null===(t=a.state.$detectedLabelsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)!==s.LoadingState.Done}),this._subs.add(this.subscribeToEvent(y.x,(()=>{this.state.search.clearValueFilter()}))),this._subs.add(this.subscribeToEvent(C.gf,this.handleSortByChange)),this._subs.add(r.subscribeToState(this.variableChanged)),this._subs.add((0,P.cR)(this).subscribeToState(((e,t)=>{const n=(0,P.Hj)(this);let{labelName:r}=(0,v.W6)();const a=e.filters.find((e=>e.key===r)),i=t.filters.find((e=>e.key===r));n.state.value===d.To&&a!==i&&this.setState({loading:!0,body:void 0})}))),this._subs.add(null===(n=a.state.$detectedFieldsData)||void 0===n?void 0:n.subscribeToState(((e,t)=>{var n,r,a;(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&(null===(r=e.data.series)||void 0===r?void 0:r[0])&&this.updateOptions(null===(a=e.data.series)||void 0===a?void 0:a[0])})));const i=(0,b.rD)(this);i&&this.updateOptions(i),(0,v.NX)(this)}updateOptions(e){if(!e||!e.length){const e=l.jh.getAncestor(this,f.P);let r;var t,n;return(0,_.mE)(e).length>1?(null===(t=(n=this.state).changeFieldCount)||void 0===t||t.call(n,0),r=new j.W({clearCallback:()=>(0,_.rA)(this)})):r=new L.a({type:"fields"}),void this.setState({loading:!1,body:r})}const r=l.jh.getAncestor(this,b.Mn);var a;(0,P.Hj)(this).setState({options:(0,F.rd)(e.fields[0].values.map((e=>String(e)))),loading:!1,value:null!==(a=r.state.drillDownLabel)&&void 0!==a?a:d.To}),this.setState({loading:!1})}updateBody(e){const t=(0,P.Hj)(this);if(!t.state.options||!t.state.options.length)return;const n={};if(t.state.options&&t.state.options.length<=1){const e=l.jh.getAncestor(this,f.P);var r,a;(0,_.mE)(e).length>1?(null===(r=(a=this.state).changeFieldCount)||void 0===r||r.call(a,0),n.body=new j.W({clearCallback:()=>(0,_.rA)(this)})):n.body=new L.a({type:"fields"})}else e.value===d.To&&this.state.body instanceof x.u?n.body=new w.E({}):e.value!==d.To&&this.state.body instanceof w.E?n.body=new x.u({}):(void 0===this.state.body||this.state.body instanceof L.a||this.state.body instanceof j.W)&&(n.body=e.value===d.To?new w.E({}):new x.u({}));this.setState(n)}constructor(e){var t,n,r,a;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){T(e,t,n[t])}))}return e}({$variables:null!==(r=e.$variables)&&void 0!==r?r:new l.Pj({variables:[new g.m({name:d.LI,defaultToAll:!1,includeAll:!0,value:null!==(t=e.value)&&void 0!==t?t:d.To,options:null!==(n=e.options)&&void 0!==n?n:[]})]}),loading:!0,sort:new C.wd({target:"fields"}),search:new y.G("fields"),value:null!==(a=e.value)&&void 0!==a?a:d.To},e)),T(this,"_variableDependency",new l.Sh(this,{variableNames:[d.MB]})),T(this,"variableChanged",((e,t)=>{(e.value!==t.value||!(0,p.B)(e.options,t.options)||void 0===this.state.body||this.state.body instanceof L.a||this.state.body instanceof j.W)&&this.updateBody(e)})),T(this,"handleSortByChange",(e=>{if("fields"!==e.target)return;const t=this.state.body;var n;t instanceof x.u&&t.state.body instanceof E.U&&(null===(n=t.state.body)||void 0===n||n.state.layouts.forEach((n=>{l.jh.findDescendents(t,S.h).forEach((t=>t.sort(e.sortBy,e.direction)))}))),(0,c.EE)(c.NO.service_details,c.ir.service_details.value_breakdown_sort_change,{target:"fields",criteria:e.sortBy,direction:e.direction})})),T(this,"onFieldSelectorChange",(e=>{if(!e)return;const t=(0,P.Hj)(this),{sortBy:n,direction:r}=(0,u.vs)("fields",m.DEFAULT_SORT_BY,"desc");(0,c.EE)(c.NO.service_details,c.ir.service_details.select_field_in_breakdown_clicked,{field:e,previousField:t.getValueText(),view:"fields",sortBy:n,sortByDirection:r});const a=l.jh.getAncestor(this,b.Mn);(0,h.fg)(v._J.field,e,a)})),this.addActivationHandler(this.onActivate.bind(this))}}T(N,"LabelsMenu",(({model:e})=>{const{body:t,loading:n,search:r}=e.useState(),a=(0,o.useStyles2)($),s=(0,P.Hj)(e),{options:l,value:c}=s.useState();return i().createElement("div",{className:a.labelsMenuWrapper},t instanceof w.E&&i().createElement(w.E.Selector,{model:t}),t instanceof x.u&&i().createElement(x.u.Selector,{model:t}),t instanceof x.u&&i().createElement(r.Component,{model:r}),!n&&l.length>1&&i().createElement(O.u,{label:"Field",options:l,value:String(c),onChange:e.onFieldSelectorChange}))})),T(N,"ValuesMenu",(({model:e})=>{const{loading:t,sort:n}=e.useState(),r=(0,o.useStyles2)($),a=(0,P.Hj)(e),{value:s}=a.useState();return i().createElement("div",{className:r.valuesMenuWrapper},!t&&s!==d.To&&i().createElement(i().Fragment,null,i().createElement(n.Component,{model:n})))})),T(N,"Component",(({model:e})=>{const{body:t,loading:n,blockingMessage:r}=e.useState(),a=(0,o.useStyles2)($);return i().createElement("div",{className:a.container},i().createElement(k.O,{isLoading:n,blockingMessage:r},t instanceof w.E&&e&&i().createElement(N.LabelsMenu,{model:e}),i().createElement("div",{className:a.content},t&&i().createElement(t.Component,{model:t}))))}));const I={link:(0,r.css)({textDecoration:"underline"}),button:(0,r.css)({marginLeft:"1.5rem"})};function $(e){return{container:(0,r.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column",gap:e.spacing(1)}),content:(0,r.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),labelsMenuWrapper:(0,r.css)({flexGrow:0,display:"flex",alignItems:"top",justifyContent:"space-between",flexDirection:"row-reverse",gap:e.spacing(2)}),valuesMenuWrapper:(0,r.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2),flexDirection:"row"})}}},8810:(e,t,n)=>{n.d(t,{O:()=>A});var r=n(6089),a=n(5959),i=n.n(a),s=n(7781),l=n(2672),o=n(2007),c=n(2718),u=n(892),d=n(3143),p=n(5631),g=n(4462),h=n(9570),v=n(6001),m=n(1022),f=n(227),b=n(4144),y=n(2254),S=n(5431),w=n(8835),O=n(833),x=n(3630),E=n(738),C=n(5183),k=n(9016),F=n(4119),L=n(4750),P=n(7085),j=n(1752);function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class T extends l.Bs{onActivate(){var e;const t=(0,L.ir)(this),n=l.jh.getAncestor(this,y.Mn).state.$detectedLabelsData;this.state.body?(null==n||null===(e=n.state.data)||void 0===e?void 0:e.state)===s.LoadingState.Done&&this.update(null==n?void 0:n.state.data.series[0]):this.setState({body:this.build()}),this._subs.add(null==n?void 0:n.subscribeToState(((e,t)=>{var n;(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&this.update(e.data.series[0])}))),this._subs.add(t.subscribeToState((()=>{this.updateQueriesOnFieldsVariableChange()})))}getPanelByIndex(e,t){const n=e.state.children[t].state.body;return{panel:n,title:n.state.title}}update(e){var t;const n=(0,L.P4)(this).state.options.filter((e=>e.value!==d.To)).map((e=>e.label));null===(t=this.state.body)||void 0===t||t.state.layouts.forEach((t=>{let r=[];const a=t,i=new Set(n),s=a.state.children;for(let e=0;e<s.length;e++){const{title:t}=this.getPanelByIndex(a,e);i.has(t)?i.delete(t):(s.splice(e,1),e--),r.push(t)}const l=Array.from(i).map((e=>({label:e,value:e})));s.push(...this.buildChildren(l));const o=this.calculateCardinalityMap(e);s.sort(this.sortChildren(o)),a.setState({children:s})}))}calculateCardinalityMap(e){const t=new Map;if(null==e?void 0:e.length)for(let n=0;n<(null==e?void 0:e.fields.length);n++){const r=e.fields[n].name,a=e.fields[n].values[0];t.set(r,a)}return t}build(){var e;const t=(0,L.P4)(this);l.jh.getAncestor(this,A).state.search.reset();const n=this.buildChildren(t.state.options),r=l.jh.getAncestor(this,y.Mn).state.$detectedLabelsData;if((null==r||null===(e=r.state.data)||void 0===e?void 0:e.state)===s.LoadingState.Done){const e=this.calculateCardinalityMap(null==r?void 0:r.state.data.series[0]);n.sort(this.sortChildren(e))}const a=n.map((e=>e.clone()));return new E.U({options:[{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new l.gF({isLazy:!0,templateColumns:F.d,autoRows:"200px",children:n}),new l.gF({isLazy:!0,templateColumns:"1fr",autoRows:"200px",children:a})]})}buildChildren(e){const t=[];for(const n of e){const{value:e}=n,r=String(e);if(e===d.To||!e)continue;const a=(0,F.o)(this,String(n.value),String(n.value)),i=(0,C.rS)([a]);t.push(new l.xK({body:l.d0.timeseries().setTitle(r).setData(i).setHeaderActions([new k.X({labelName:r,fieldType:u._J.label})]).setCustomFieldConfig("stacking",{mode:o.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",o.DrawStyle.Bars).setHoverHeader(!1).setOverrides(C.jC).setMenu(new P.GD({investigationOptions:{labelName:r}})).setSeriesLimit(j.l).build()}))}return t}sortChildren(e){return(t,n)=>{const r=t.state.body,a=n.state.body;if(r.state.title===d.e4)return-1;if(a.state.title===d.e4)return 1;var i;const s=null!==(i=e.get(r.state.title))&&void 0!==i?i:0;var l;return(null!==(l=e.get(a.state.title))&&void 0!==l?l:0)-s}}static Selector({model:e}){const{body:t}=e.useState();return i().createElement(i().Fragment,null,t&&i().createElement(E.U.Selector,{model:t}))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){_(e,t,n[t])}))}return e}({},e)),_(this,"updateQueriesOnFieldsVariableChange",(()=>{var e;null===(e=this.state.body)||void 0===e||e.state.layouts.forEach((e=>{const t=e;for(let e=0;e<t.state.children.length;e++){const{panel:a,title:i}=this.getPanelByIndex(t,e),s=a.state.$data,o=(0,F.o)(this,i,i);var n,r;if(s instanceof l.dt&&o.expr===(null==s||null===(r=s.state.queries)||void 0===r||null===(n=r[0])||void 0===n?void 0:n.expr))break;a.setState({$data:(0,C.rS)([o])})}}))})),this.addActivationHandler(this.onActivate.bind(this))}}_(T,"Component",(({model:e})=>{const{body:t}=e.useState(),n=(0,o.useStyles2)(P.K_);return t?i().createElement("span",{className:n.panelWrapper},t&&i().createElement(t.Component,{model:t})):i().createElement(o.LoadingPlaceholder,{text:"Loading..."})}));var D=n(5722),N=n(3782);function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){I(e,t,n[t])}))}return e}class A extends l.Bs{onActivate(){var e,t,n,r,a;const i=l.jh.getAncestor(this,y.Mn),o=(0,L.P4)(this);this.setState({loading:(null===(t=i.state.$detectedLabelsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)!==s.LoadingState.Done,error:(null===(r=i.state.$detectedLabelsData)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.state)===s.LoadingState.Error}),this._subs.add(this.subscribeToEvent(m.x,(()=>{this.state.search.clearValueFilter()}))),this._subs.add(this.subscribeToEvent(b.gf,this.handleSortByChange)),this._subs.add(null===(a=i.state.$detectedLabelsData)||void 0===a?void 0:a.subscribeToState(this.onDetectedLabelsDataChange)),this._subs.add((0,L.cR)(this).subscribeToState(((e,t)=>{this.onLabelsVariableChange(e,t)}))),this._subs.add(o.subscribeToState(((e,t)=>{this.onGroupByVariableChange(e,t)})));const c=(0,y.TG)(this);c&&this.updateOptions(c),(0,u.NX)(this)}onGroupByVariableChange(e,t){(e.value!==t.value||!(0,O.B)(e.options,t.options)||void 0===this.state.body||this.state.body instanceof N.a)&&this.updateBody()}onLabelsVariableChange(e,t){let{labelName:n}=(0,u.W6)();n===d.ky&&(n=d.OX);const r=(0,L.P4)(this),a=e.filters.find((e=>e.key===n)),i=t.filters.find((e=>e.key===n));r.state.value===d.To&&a!==i&&this.setState({loading:!0,body:void 0,error:void 0})}updateOptions(e){if(!e||!e.length)return void this.setState({loading:!1,body:new N.a({type:"labels"})});const t=(0,L.P4)(this),n=(0,v.dD)(e.fields.map((e=>e.name)));var r;t.setState({loading:!1,options:n,value:null!==(r=this.state.value)&&void 0!==r?r:d.To})}updateBody(){const e=(0,L.P4)(this);if(!e.state.options||!e.state.options.length)return;const t={loading:!1,blockingMessage:void 0,error:!1};e.hasAllValue()&&this.state.body instanceof x.u?t.body=new T({}):!e.hasAllValue()&&this.state.body instanceof T?t.body=new x.u({}):void 0===this.state.body?e.state.options.length>0?t.body=e.hasAllValue()?new T({}):new x.u({}):t.body=new N.a({type:"labels"}):this.state.body instanceof N.a&&e.state.options.length>0&&(t.body=e.hasAllValue()?new T({}):new x.u({})),this.setState($({},t))}constructor(e){var t,n,r,a,i;super((a=$({},e),i=null!=(i={$variables:null!==(r=e.$variables)&&void 0!==r?r:new l.Pj({variables:[new S.m({name:d.Jg,defaultToAll:!1,includeAll:!0,value:null!==(t=e.value)&&void 0!==t?t:d.To,options:null!==(n=e.options)&&void 0!==n?n:[]})]}),loading:!0,sort:new b.wd({target:"labels"}),search:new m.G("labels"),value:e.value})?i:{},Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(i)).forEach((function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(i,e))})),a)),I(this,"_variableDependency",new l.Sh(this,{variableNames:[d.MB]})),I(this,"onDetectedLabelsDataChange",((e,t)=>{var n,r,a,i,l,o,c,u,d;(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&(null===(r=e.data.series)||void 0===r?void 0:r[0])&&!(0,O.B)(null===(i=e.data.series)||void 0===i||null===(a=i[0])||void 0===a?void 0:a.fields,null===(c=t.data)||void 0===c||null===(o=c.series)||void 0===o||null===(l=o[0])||void 0===l?void 0:l.fields)?this.updateOptions(null===(d=e.data.series)||void 0===d?void 0:d[0]):(null===(u=e.data)||void 0===u?void 0:u.state)===s.LoadingState.Done&&(0,L.P4)(this).setState({loading:!1})})),I(this,"handleSortByChange",(e=>{if("labels"!==e.target)return;const t=this.state.body;t instanceof x.u&&l.jh.findDescendents(t,p.h).forEach((t=>{t.sort(e.sortBy,e.direction)})),(0,c.EE)(c.NO.service_details,c.ir.service_details.value_breakdown_sort_change,{target:"labels",criteria:e.sortBy,direction:e.direction})})),I(this,"onChange",(e=>{if(!e)return;const t=(0,L.P4)(this);t.changeValueTo(e);const{sortBy:n,direction:r}=(0,f.vs)("labels",D.DEFAULT_SORT_BY,"desc");(0,c.EE)(c.NO.service_details,c.ir.service_details.select_field_in_breakdown_clicked,{label:e,previousLabel:t.getValueText(),view:"labels",sortBy:n,sortByDirection:r});const a=l.jh.getAncestor(this,y.Mn);(0,w.fg)(u._J.label,e,a)})),this.addActivationHandler(this.onActivate.bind(this))}}function M(e){return{container:(0,r.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column",gap:e.spacing(1)}),content:(0,r.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),labelsMenuWrapper:(0,r.css)({flexGrow:0,display:"flex",alignItems:"top",justifyContent:"space-between",flexDirection:"row-reverse",gap:e.spacing(2)}),valuesMenuWrapper:(0,r.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2),flexDirection:"row"})}}I(A,"LabelsMenu",(({model:e})=>{const{body:t,loading:n,search:r}=e.useState(),a=(0,L.P4)(e),{options:s,value:l}=a.useState(),c=(0,o.useStyles2)(M);return i().createElement("div",{className:c.labelsMenuWrapper},t instanceof x.u&&i().createElement(x.u.Selector,{model:t}),t instanceof T&&i().createElement(T.Selector,{model:t}),t instanceof x.u&&i().createElement(r.Component,{model:r}),!n&&s.length>0&&i().createElement(g.u,{label:"Label",options:s,value:String(l),onChange:e.onChange}))})),I(A,"ValuesMenu",(({model:e})=>{const{loading:t,sort:n}=e.useState(),r=(0,L.P4)(e),{value:a}=r.useState(),s=(0,o.useStyles2)(M);return i().createElement("div",{className:s.valuesMenuWrapper},!t&&a!==d.To&&i().createElement(i().Fragment,null,i().createElement(n.Component,{model:n})))})),I(A,"Component",(({model:e})=>{const{body:t,loading:n,blockingMessage:r,error:a}=e.useState(),s=(0,o.useStyles2)(M);return i().createElement("div",{className:s.container},i().createElement(h.O,{isLoading:n,blockingMessage:r},a&&i().createElement(o.Alert,{title:"",severity:"warning"},"The labels are not available at this moment. Try using a different time range or check again later."),t instanceof T&&e&&i().createElement(A.LabelsMenu,{model:e}),i().createElement("div",{className:s.content},t&&i().createElement(t.Component,{model:t}))))}))},3630:(e,t,n)=>{n.d(t,{u:()=>D});var r=n(2672),a=n(738),i=n(4144),s=n(2007),l=n(5183),o=n(227),c=n(7781),u=n(5631),d=n(7097),p=n(3143),g=n(5959),h=n.n(g),v=n(8810),m=n(8835),f=n(892),b=n(2254),y=n(558),S=n(5722),w=n(4119),O=n(8531),x=n(4750),E=n(7085),C=n(9558),k=n(3782),F=n(8538),L=n(1863),P=n(8516);function j(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){j(e,t,n[t])}))}return e}function T(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class D extends r.Bs{onActivate(){var e;this.setState({$data:(0,l.rS)([(0,w.o)(this,p.zp,String((0,x.P4)(this).state.value))]),body:this.build()});const t=(0,x.P4)(this);this._subs.add(t.subscribeToState((e=>{e.value===p.To&&this.setState({$data:void 0,body:void 0})}))),this.subscribeToEvent(y.Of,(e=>{this.setState({lastFilterEvent:e})})),this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState(((e,t)=>{this.onValuesDataQueryChange(e,t)})))}onValuesDataQueryChange(e,t){this.setEmptyStates(e),this.setErrorStates(e),this.navigateOnLastFilter(e)}navigateOnLastFilter(e){var t,n;if((null===(t=e.data)||void 0===t?void 0:t.state)===c.LoadingState.Done||(null===(n=e.data)||void 0===n?void 0:n.state)===c.LoadingState.Streaming){const t=this.state.lastFilterEvent;t&&("exclude"===t.operator&&e.data.series.length<1&&this.navigateToLabels(),"include"===t.operator&&e.data.series.length<=1&&this.navigateToLabels())}}setErrorStates(e){var t,n;if((null==e||null===(t=e.data)||void 0===t?void 0:t.errors)&&(null===(n=e.data)||void 0===n?void 0:n.state)!==c.LoadingState.Done){var r;const t=this.state.errors;null==e||null===(r=e.data)||void 0===r||r.errors.forEach((e=>{const n=`${e.status}_${e.traceId}_${e.message}`;void 0===t[n]&&(t[n]=T(_({},e),{displayed:!1}))})),this.setState({errors:t}),this.showErrorToast(this.state.errors)}}setEmptyStates(e){var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===c.LoadingState.Done)if(e.data.series.length>0&&!(this.state.body instanceof a.U))this.setState({body:this.build()});else if(0===e.data.series.length){const e=r.jh.getAncestor(this,F.P);(0,L.mE)(e).length>1?this.setState({body:new C.W({clearCallback:()=>(0,L.rA)(this)})}):this.setState({body:new k.a({type:"fields"})})}}getActiveLayout(){const e=this.state.body;if(e instanceof a.U){const t=null==e?void 0:e.state.layouts.find((e=>e.isActive));if(t instanceof r.G1)return t}}activeLayoutContainsNoPanels(){const e=this.getActiveLayout();return!!e&&r.jh.findDescendents(e,u.h).some((e=>{const t=e.state.body.state.children[0];return t instanceof r.vA||t instanceof r.dM}))}navigateToLabels(){this.setState({lastFilterEvent:void 0}),(0,m.Vt)(f.G3.labels,r.jh.getAncestor(this,b.Mn))}build(){const e=(0,x.P4)(this).state,t=r.jh.getAncestor(this,v.O),n=String(null==e?void 0:e.value);let c=r.d0.timeseries();c=c.setCustomFieldConfig("stacking",{mode:s.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",s.DrawStyle.Bars).setOverrides(l.jC).setMenu(new E.GD({})).setTitle(n);const g=c.build(),{sortBy:m,direction:f}=(0,o.vs)("labels",S.DEFAULT_SORT_BY,"desc"),b=()=>{var e;return null!==(e=t.state.search.state.filter)&&void 0!==e?e:""};return new a.U({options:[{value:"single",label:"Single"},{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new r.G1({direction:"column",children:[new r.dM({reactNode:h().createElement(v.O.LabelsMenu,{model:t})}),new r.vA({minHeight:300,body:g})]}),new r.G1({direction:"column",children:[new r.dM({reactNode:h().createElement(v.O.LabelsMenu,{model:t})}),new P.s7({title:n,levelColor:!0}),new r.dM({reactNode:h().createElement(v.O.ValuesMenu,{model:t})}),new u.h({body:new r.gF({isLazy:!0,templateColumns:w.d,autoRows:"200px",children:[new r.vA({body:new r.dM({reactNode:h().createElement(s.LoadingPlaceholder,{text:"Loading..."})})})]}),getLayoutChild:(0,d.Zp)(i.ee,s.DrawStyle.Bars,p.MB,r.jh.getAncestor(this,v.O).state.sort,n),sortBy:m,direction:f,getFilter:b})]}),new r.G1({direction:"column",children:[new r.dM({reactNode:h().createElement(v.O.LabelsMenu,{model:t})}),new P.s7({title:n,levelColor:!0}),new r.dM({reactNode:h().createElement(v.O.ValuesMenu,{model:t})}),new u.h({body:new r.gF({templateColumns:"1fr",autoRows:"200px",children:[new r.vA({body:new r.dM({reactNode:h().createElement(s.LoadingPlaceholder,{text:"Loading..."})})})]}),getLayoutChild:(0,d.Zp)(i.ee,s.DrawStyle.Bars,p.MB,r.jh.getAncestor(this,v.O).state.sort,n),sortBy:m,direction:f,getFilter:b})]})]})}showErrorToast(e){const t=(0,O.getAppEvents)();let n=[];for(const t in e){const r=e[t];r.displayed||(n.push(r),r.displayed=!0)}n.length&&(this.activeLayoutContainsNoPanels()||t.publish({type:c.AppEvents.alertError.name,payload:null==n?void 0:n.map(((e,t)=>this.renderError(t,e)))}),this.setState({errors:e}))}renderError(e,t){return h().createElement("div",{key:e},t.status&&h().createElement(h().Fragment,null,h().createElement("strong",null,"Status"),": ",t.status," ",h().createElement("br",null)),t.message&&h().createElement(h().Fragment,null,h().createElement("strong",null,"Message"),": ",t.message," ",h().createElement("br",null)),t.traceId&&h().createElement(h().Fragment,null,h().createElement("strong",null,"TraceId"),": ",t.traceId))}static Selector({model:e}){const{body:t}=e.useState();return h().createElement(h().Fragment,null,t&&t instanceof a.U&&h().createElement(a.U.Selector,{model:t}))}constructor(e){super(T(_({},e),{errors:{}})),this.addActivationHandler(this.onActivate.bind(this))}}j(D,"Component",(({model:e})=>{const{body:t}=e.useState(),n=(0,s.useStyles2)(E.K_);return t?h().createElement("span",{className:n.panelWrapper},t&&h().createElement(t.Component,{model:t})):h().createElement(s.LoadingPlaceholder,{text:"Loading..."})}))},738:(e,t,n)=>{n.d(t,{U:()=>d});var r=n(5959),a=n.n(r),i=n(2672),s=n(2007),l=n(2718),o=n(892),c=n(6089);function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class d extends i.Bs{constructor(...e){super(...e),u(this,"onLayoutChange",(e=>{(0,l.EE)(l.NO.service_details,l.ir.service_details.layout_type_changed,{layout:e,view:(0,o.FT)()}),this.setState({active:e})}))}}u(d,"Selector",(function({model:e}){const{active:t,options:n}=e.useState(),r=(0,s.useStyles2)(p);return a().createElement(s.Field,{className:r.field},a().createElement(s.RadioButtonGroup,{options:n,value:t,onChange:e.onLayoutChange}))})),u(d,"Component",(({model:e})=>{const{layouts:t,options:n,active:r}=e.useState(),i=n.findIndex((e=>e.value===r));if(-1===i)return null;const s=t[i];return a().createElement(s.Component,{model:s})}));const p=e=>({field:(0,c.css)({marginBottom:0})})},9558:(e,t,n)=>{n.d(t,{W:()=>p});var r,a,i,s=n(2672),l=n(4482),o=n(2007),c=n(5959),u=n.n(c),d=n(4105);class p extends s.Bs{}i=({model:e})=>{const{clearCallback:t}=e.useState();return u().createElement(l.R,null,u().createElement(o.Alert,{title:"",severity:"info"},"No labels match these filters."," ",u().createElement(o.Button,{className:d.ZI.button,onClick:()=>t()},"Clear filters")," "))},(a="Component")in(r=p)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},8516:(e,t,n)=>{n.d(t,{Zb:()=>h,s$:()=>m,s7:()=>g});var r,a,i,s=n(2672),l=n(7085),o=n(2007),c=n(5183),u=n(227),d=n(5959),p=n.n(d);class g extends s.Bs{onActivate(){var e;const t=null!==(e=(0,u.ex)("collapsed",[l.Ci.collapsed,l.Ci.expanded]))&&void 0!==e?e:l.Ci.expanded,n=function(e,t){var n;const r=null!==(n=(0,u.ex)("collapsed",[l.Ci.collapsed,l.Ci.expanded]))&&void 0!==n?n:l.Ci.expanded,a=s.d0.timeseries().setTitle(e).setMenu(new l.GD({})).setCollapsible(!0).setCollapsed(r===l.Ci.collapsed).setCustomFieldConfig("stacking",{mode:o.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setSeriesLimit(100).setCustomFieldConfig("drawStyle",o.DrawStyle.Bars);return(null==t?void 0:t.levelColor)&&a.setOverrides(c.jC),a.build()}(this.state.title,{levelColor:this.state.levelColor}),r=v(t);this.setState({body:new s.G1({key:m,minHeight:r,height:r,maxHeight:r,wrap:"nowrap",children:[new s.vA({body:n})]})}),this._subs.add(n.subscribeToState(((e,t)=>{e.collapsed!==t.collapsed&&(h(s.jh.getAncestor(n,s.G1),e.collapsed?l.Ci.collapsed:l.Ci.expanded),(0,u.IW)("collapsed",e.collapsed?l.Ci.collapsed:l.Ci.expanded))})))}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}function h(e,t){const n=v(t);e.setState({minHeight:n,height:n,maxHeight:n})}function v(e){return e===l.Ci.collapsed?35:300}i=({model:e})=>{const{body:t}=e.useState();return t?p().createElement("div",null,p().createElement(t.Component,{model:t})):null},(a="Component")in(r=g)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i;const m="value_summary_panel"},8642:(e,t,n)=>{n.d(t,{D:()=>o});var r=n(6089),a=n(2007),i=n(5959),s=n.n(i);function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const o=e=>{var{value:t,onChange:n,placeholder:r,onClear:i,suffix:o}=e,u=function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(e,["value","onChange","placeholder","onClear","suffix"]);const d=(0,a.useStyles2)(c);return s().createElement(a.Input,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){l(e,t,n[t])}))}return e}({value:t,onChange:n,suffix:s().createElement("span",{className:d.suffixWrapper},i&&t?s().createElement(a.IconButton,{"aria-label":"Clear search",tooltip:"Clear search",onClick:i,name:"times",className:d.clearIcon}):void 0,o&&o),prefix:s().createElement(a.Icon,{name:"search"}),placeholder:r},u))},c=e=>({suffixWrapper:(0,r.css)({gap:e.spacing(.5),display:"inline-flex"}),clearIcon:(0,r.css)({cursor:"pointer"})})},9016:(e,t,n)=>{n.d(t,{X:()=>N});var r=n(2672),a=n(2254),i=n(8835),s=n(892),l=n(2007),o=n(5959),c=n.n(o),u=n(558),d=n(3143),p=n(7781),g=n(4750),h=n(4793),v=n(6089),m=n(3241),f=n(2871),b=n(1220);function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var S=function(e){return e.ns="ns",e.us="µs",e.ms="ms",e.s="s",e.m="m",e.h="h",e}(S||{}),w=function(e){return e.B="B",e.KB="KB",e.MB="MB",e.GB="GB",e.TB="TB",e}(w||{});class O extends r.Bs{onActivate(){const e=(0,g.bY)((0,u.ts)(this.state.labelName,this.state.variableType),this).state.filters.filter((e=>e.key===this.state.labelName)),t=e.find((e=>e.operator===h.w7.gte||e.operator===h.w7.gt)),n=e.find((e=>e.operator===h.w7.lte||e.operator===h.w7.lt));let r={};if("duration"===this.state.fieldType||"bytes"===this.state.fieldType){if(t){const e=x((0,g.bu)(t).value,this.state.fieldType);e&&(r.gt=e.value,r.gtu=e.unit,r.gte=t.operator===h.w7.gte)}if(n){const e=x((0,g.bu)(n).value,this.state.fieldType);e&&(r.lt=e.value,r.ltu=e.unit,r.lte=n.operator===h.w7.lte)}}else{if(t){const e=(0,g.bu)(t).value;r.gt=Number(e),r.gtu="",r.gte=t.operator===h.w7.gte}if(n){const e=(0,g.bu)(n).value;r.lt=Number(e),r.ltu="",r.lte=n.operator===h.w7.lte}}0!==Object.keys(r).length&&(r.hasExistingFilter=!0),this.setState(r)}onSubmit(){this.state.gt?(0,u.vn)(this.state.labelName,this.state.gt.toString()+this.state.gtu,this.state.gte?h.w7.gte:h.w7.gt,this,this.state.variableType):(0,u.VT)(this.state.labelName,this,this.state.gte?h.w7.gte:h.w7.gt,this.state.variableType),this.state.lt?(0,u.vn)(this.state.labelName,this.state.lt.toString()+this.state.ltu,this.state.lte?h.w7.lte:h.w7.lt,this,this.state.variableType):(0,u.VT)(this.state.labelName,this,this.state.lte?h.w7.lte:h.w7.lt,this.state.variableType),r.jh.getAncestor(this,N).togglePopover()}constructor(e){let t;const n=e.fieldType;if("bytes"===n)t={ltu:"B",gtu:"B"};else if("duration"===n)t={ltu:"s",gtu:"s"};else{if("float"!==n)throw new Error(`field type incorrectly defined: ${n}`);t={ltu:"",gtu:""}}super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){y(e,t,n[t])}))}return e}({},e,t)),y(this,"onInputKeydown",(e=>{const t=void 0===this.state.gt&&void 0===this.state.lt;"Enter"!==e.key||t||this.onSubmit()})),this.addActivationHandler(this.onActivate.bind(this))}}function x(e,t){if("duration"===t){const t=Object.values(S).find((t=>{const n=t.length;return e.slice(-1*n)===t}));if(t){const n=Number(e.replace(t,""));if(!isNaN(n))return{unit:t,value:n}}}if("bytes"===t){const t=Object.values(w).sort(((e,t)=>t.length-e.length)).find((t=>{const n=t.length;return e.slice(-1*n)===t}));if(t){const n=Number(e.replace(t,""));if(!isNaN(n))return{unit:t,value:n}}}}function E(e){if("duration"===e)return Object.keys(S).map((e=>({text:e,value:S[e],label:e})));if("bytes"===e)return Object.keys(w).map((e=>({text:e,value:w[e],label:e})));const t=new Error(`invalid field type: ${e}`);throw f.v.error(t,{msg:"getUnitOptions, invalid field type"}),t}y(O,"Component",(({model:e})=>{const t=(0,l.useStyles2)(C),{labelName:n,gt:a,lt:i,gte:s,lte:o,gtu:u,ltu:d,fieldType:p,hasExistingFilter:g}=e.useState(),h="float"!==p&&p!==n?`(${p})`:void 0,m=r.jh.getAncestor(e,N),f=void 0===a&&void 0===i;return c().createElement(l.ClickOutsideWrapper,{useCapture:!0,onClick:()=>m.togglePopover()},c().createElement(l.Stack,{direction:"column",gap:0,role:"tooltip"},c().createElement("div",{className:t.card.body},c().createElement("div",{className:t.card.title},n," ",h),c().createElement("div",{className:t.card.fieldWrap},c().createElement(l.FieldSet,{className:t.card.fieldset},c().createElement(l.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputGreaterThanInclusive,horizontal:!0,className:(0,v.cx)(t.card.field,t.card.inclusiveField)},c().createElement(l.Select,{className:t.card.inclusiveInput,menuShouldPortal:!1,value:void 0!==s?s.toString():"false",options:[{label:"Greater than",value:"false"},{label:"Greater than or equal",value:"true"}],onChange:t=>e.setState({gte:"true"===t.value})})),c().createElement(l.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputGreaterThan,horizontal:!0,className:t.card.field},c().createElement(l.Input,{onKeyDownCapture:e.onInputKeydown,autoFocus:!0,onChange:t=>{e.setState({gt:""!==t.currentTarget.value?Number(t.currentTarget.value):void 0})},className:t.card.numberInput,value:a,type:"number"})),"float"!==p&&c().createElement(l.Label,null,c().createElement(l.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputGreaterThanUnit,horizontal:!0,className:t.card.field,label:c().createElement("span",{className:t.card.unitFieldLabel},"Unit")},c().createElement(l.Select,{onChange:t=>{e.setState({gtu:t.value})},menuShouldPortal:!1,options:E(p),className:t.card.selectInput,value:u})))),c().createElement(l.FieldSet,{className:t.card.fieldset},c().createElement(l.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputLessThanInclusive,horizontal:!0,className:(0,v.cx)(t.card.field,t.card.inclusiveField)},c().createElement(l.Select,{className:t.card.inclusiveInput,menuShouldPortal:!1,value:void 0!==o?o.toString():"false",options:[{label:"Less than",value:"false"},{label:"Less than or equal",value:"true"}],onChange:t=>e.setState({lte:"true"===t.value})})),c().createElement(l.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputLessThan,horizontal:!0,className:t.card.field},c().createElement(l.Input,{onKeyDownCapture:e.onInputKeydown,onChange:t=>e.setState({lt:""!==t.currentTarget.value?Number(t.currentTarget.value):void 0}),className:t.card.numberInput,value:i,type:"number"})),"float"!==p&&c().createElement(l.Label,null,c().createElement(l.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputLessThanUnit,horizontal:!0,className:t.card.field,label:c().createElement("span",{className:t.card.unitFieldLabel},"Unit")},c().createElement(l.Select,{onChange:t=>{e.setState({ltu:t.value})},menuShouldPortal:!1,options:E(p),className:t.card.selectInput,value:d}))))),c().createElement("div",{className:t.card.buttons},g&&c().createElement(l.Button,{"data-testid":b.b.breakdowns.common.filterNumericPopover.removeButton,disabled:!g,onClick:()=>{e.setState({gt:void 0,lt:void 0}),e.onSubmit()},size:"sm",variant:"destructive",fill:"outline"},"Remove"),c().createElement(l.Button,{"data-testid":b.b.breakdowns.common.filterNumericPopover.submitButton,disabled:f,onClick:()=>e.onSubmit(),size:"sm",variant:"primary",fill:"outline",type:"submit"},"Add"),c().createElement(l.Button,{"data-testid":b.b.breakdowns.common.filterNumericPopover.cancelButton,onClick:()=>m.togglePopover(),size:"sm",variant:"secondary",fill:"outline"},"Cancel")))))}));const C=e=>({card:{buttons:(0,v.css)({display:"flex",flexWrap:"wrap",justifyContent:"flex-end",gap:e.spacing(1.5),marginTop:e.spacing(1)}),inclusiveInput:(0,v.css)({minWidth:"185px"}),selectInput:(0,v.css)({minWidth:"65px"}),numberInput:(0,v.css)({width:"75px"}),fieldWrap:(0,v.css)({display:"flex",flexDirection:"column",paddingTop:e.spacing(2),paddingBottom:0}),field:(0,v.css)({display:"flex",alignItems:"center",marginBottom:e.spacing(1)}),inclusiveField:(0,v.css)({marginRight:e.spacing(1)}),unitFieldLabel:(0,v.css)({marginLeft:e.spacing(2),marginRight:e.spacing(1.5)}),numberFieldLabel:(0,v.css)({width:"100px"}),switchFieldLabel:(0,v.css)({marginLeft:e.spacing(2),marginRight:e.spacing(1)}),fieldset:(0,v.css)({display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",marginBottom:0}),title:(0,v.css)({}),body:(0,v.css)({padding:e.spacing(2)}),p:(0,v.css)({maxWidth:300})}});var k=n(7097),F=n(9829);function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){L(e,t,n[t])}))}return e}function j(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const _="Include",T="Exclude",D="Add to filter";class N extends r.Bs{onChange(e){const t=this.getVariable(),n=t.state.name,r=this.getExistingFilter(t),a=(0,g.z2)(t,r);(null==r?void 0:r.operator)===h.w7.NotEqual&&a.value===d.ZO&&e.value===_?this.clearFilter(n):e.value===_?this.onClickExcludeEmpty(n):e.value===T?this.onClickIncludeEmpty(n):e.value===D&&this.onClickNumericFilter(n),this.setState({selectedValue:e})}getExistingFilter(e){let{labelName:t}=(0,s.W6)();if(this.state.labelName!==t)return null==e?void 0:e.state.filters.find((e=>e.key===this.state.labelName))}onActivate(){var e,t;const n=r.jh.getAncestor(this,a.Mn);(null===(t=n.state.$data)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)===p.LoadingState.Done&&this.calculateSparsity(),this._subs.add(r.jh.getData(this).subscribeToState((e=>{var t,r,a,i;(null===(t=e.data)||void 0===t?void 0:t.state)===p.LoadingState.Done&&((null===(a=n.state.$data)||void 0===a||null===(r=a.state.data)||void 0===r?void 0:r.state)===p.LoadingState.Done&&this.calculateSparsity(),this._subs.add(null===(i=n.state.$data)||void 0===i?void 0:i.subscribeToState((e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===p.LoadingState.Done&&this.calculateSparsity()}))))})))}togglePopover(){this.setState({showPopover:!this.state.showPopover})}calculateSparsity(){var e;const t=r.jh.getAncestor(this,a.Mn),n=(0,a.tn)(null===(e=t.state.$data)||void 0===e?void 0:e.state.data),i=null==n?void 0:n.fields.find((e=>"labels"===e.name)),s=r.jh.getData(this),l=(0,F.UX)(s,(e=>e instanceof r.dt),r.dt);if(l){const e=l.state.queries[0];(null==e?void 0:e.expr.includes("avg_over_time"))&&this.setState({hasNumericFilters:!0})}if(!i||!n)return void this.setState({hasSparseFilters:!1});const o=this.getVariable(),c=i.values.reduce(((e,t)=>((null==t?void 0:t[this.state.labelName])&&e++,e)),0),u=r.jh.getAncestor(this,r.Eb);if(void 0!==c&&n.length>0){const e=(c/n.length*100).toLocaleString(),t=`${this.state.labelName} exists on ${e}% of ${n.length} sampled log lines`;u.setState({description:t})}else u.setState({description:void 0});const p=this.getExistingFilter(o),h=p&&o.state.name===d.mB?(0,g.bu)(p):void 0;c<n.length||(null==h?void 0:h.value)===d.ZO?this.setState({hasSparseFilters:!0}):this.setState({hasSparseFilters:!1})}getVariable(){return this.state.fieldType===s._J.field?(0,g.ir)(this):this.state.labelName===d.e4?(0,g.iw)(this):(0,g.cR)(this)}constructor(e){super(j(P({},e),{showPopover:!1})),L(this,"onClickNumericFilter",(e=>{const t=(0,a.rD)(this),n=(0,k.ph)(this.state.labelName,t);if(!n||"string"===n||"boolean"===n||"int"===n){const e=new Error(`Incorrect field type: ${n}`);throw f.v.error(e,{msg:`onClickNumericFilter invalid field type ${n}`}),e}this.setState({popover:new O({labelName:this.state.labelName,variableType:e,fieldType:n})}),this.togglePopover()})),L(this,"onClickViewValues",(()=>{const e=r.jh.getAncestor(this,a.Mn);(0,i.fg)(this.state.fieldType,this.state.labelName,e)})),L(this,"onClickExcludeEmpty",(e=>{(0,u.Qt)(this.state.labelName,d.ZO,"exclude",this,e)})),L(this,"onClickIncludeEmpty",(e=>{(0,u.Qt)(this.state.labelName,d.ZO,"include",this,e)})),L(this,"clearFilter",(e=>{(0,u.Qt)(this.state.labelName,d.ZO,"clear",this,e)})),L(this,"clearFilters",(e=>{(0,u.hi)(this.state.labelName,this,e)})),this.addActivationHandler(this.onActivate.bind(this))}}function I(e){const t=(0,l.useStyles2)($);return c().createElement("span",{className:t.description},e.selected&&c().createElement("span",{className:t.selected}),e.text)}L(N,"Component",(({model:e})=>{const{hideValueDrilldown:t,labelName:n,hasSparseFilters:r,hasNumericFilters:a,selectedValue:i,popover:u,showPopover:p,fieldType:v}=e.useState(),f=e.getVariable(),y=f.useState().name,S=e.getExistingFilter(f),w=(0,g.z2)(f,S),O=(0,l.useStyles2)(A),x=(0,o.useRef)(null),E=v===s._J.label&&0===f.state.filters.filter((e=>e.key!==n&&e.operator===h.w7.Equal)).length,C=(null==S?void 0:S.operator)===h.w7.NotEqual&&w.value===d.ZO,k=!!S;var F;const L=null!==(F=null==i?void 0:i.value)&&void 0!==F?F:C?_:a?D:_,N=!!(null==S?void 0:S.operator)&&[h.w7.gte,h.w7.gt,h.w7.lte,h.w7.lt].includes(S.operator),$=L===D||N,M=L===_&&!$,B={value:_,component:()=>c().createElement(I,{selected:M,text:`Include all log lines with ${n}`})},R={value:T,component:()=>c().createElement(I,{selected:!1,text:`Exclude all log lines with ${n}`})},V={value:D,component:()=>c().createElement(I,{selected:$,text:`Add an expression, i.e. ${n} > 30`})},W=[];a&&W.push(V),r&&(N||W.push(B),W.push(R));const z=C?B:a?V:B;var H;return c().createElement(c().Fragment,null,k&&c().createElement(l.IconButton,{disabled:E,name:"filter",tooltip:`Clear ${n} filters`,onClick:()=>e.clearFilters(y)}),(a||r)&&c().createElement(c().Fragment,null,c().createElement(l.ButtonGroup,{"data-testid":b.b.breakdowns.common.filterButtonGroup},c().createElement(l.Button,{"data-testid":b.b.breakdowns.common.filterButton,ref:x,onClick:()=>e.onChange(null!=i?i:z),size:"sm",fill:"outline",variant:"secondary"},null!==(H=null==i?void 0:i.value)&&void 0!==H?H:z.value),c().createElement(l.ButtonSelect,{"data-testid":b.b.breakdowns.common.filterSelect,className:O.buttonSelect,variant:"default",options:W,onChange:t=>{e.onChange(t)}}))),!0!==t&&c().createElement(l.Button,{title:`View breakdown of values for ${n}`,variant:"primary",fill:"outline",size:"sm",onClick:e.onClickViewValues,"aria-label":`Select ${n}`},"Select"),u&&c().createElement(l.PopoverController,{content:c().createElement(u.Component,{model:u})},((e,t,n)=>{const r={onBlur:t,onFocus:e};return c().createElement(c().Fragment,null,x.current&&c().createElement(c().Fragment,null,c().createElement(l.Popover,P(j(P({},n,m.rest),{show:p,wrapperClassName:O.popover,referenceElement:x.current,renderArrow:!0}),r))))})))}));const $=e=>({selected:(0,v.css)({label:"selectable-value-selected","&:before":{content:'""',position:"absolute",left:0,top:"4px",height:"calc(100% - 8px)",width:"2px",backgroundColor:e.colors.warning.main}}),description:(0,v.css)({textAlign:"left",fontSize:e.typography.pxToRem(12)})}),A=e=>({popover:(0,v.css)({borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3,background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`}),description:(0,v.css)({textAlign:"left",fontSize:e.typography.pxToRem(12)}),buttonSelect:(0,v.css)({border:`1px solid ${e.colors.border.strong}`,borderLeft:"none",borderTopLeftRadius:0,borderBottomLeftRadius:0,padding:1,height:"24px"})})},4144:(e,t,n)=>{n.d(t,{ee:()=>f,gf:()=>g,wd:()=>h});var r=n(2672),a=n(5959),i=n.n(a),s=n(7781),l=n(1383),o=n(2007),c=n(227),u=n(1220),d=n(5722);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends s.BusEventBase{constructor(e,t,n){super(),p(this,"target",void 0),p(this,"sortBy",void 0),p(this,"direction",void 0),this.target=e,this.sortBy=t,this.direction=n}}p(g,"type","sort-criteria-changed");class h extends r.Bs{constructor(e){const{sortBy:t,direction:n}=(0,c.vs)(e.target,d.DEFAULT_SORT_BY,"desc");super({target:e.target,sortBy:t,direction:n}),p(this,"sortingOptions",[{label:"",options:[{value:"changepoint",label:"Most relevant",description:"Smart ordering of graphs based on the most significant spikes in the data"},{value:"outliers",label:"Outlying values",description:"Order by the amount of outlying values in the data"},{value:s.ReducerID.stdDev,label:"Widest spread",description:"Sort graphs by deviation from the average value"},{value:"alphabetical",label:"Name",description:"Alphabetical order"},{value:s.ReducerID.sum,label:"Count",description:"Sort graphs by total number of logs"},{value:s.ReducerID.max,label:"Highest spike",description:"Sort graphs by the highest values (max)"},{value:s.ReducerID.min,label:"Lowest dip",description:"Sort graphs by the smallest values (min)"}]},{label:"Percentiles",options:[...s.fieldReducers.selectOptions([],m).options]}]),p(this,"onCriteriaChange",(e=>{e.value&&(this.setState({sortBy:e.value}),(0,c.fq)(this.state.target,e.value,this.state.direction),this.publishEvent(new g(this.state.target,e.value,this.state.direction),!0))})),p(this,"onDirectionChange",(e=>{e.value&&(this.setState({direction:e.value}),(0,c.fq)(this.state.target,this.state.sortBy,e.value),this.publishEvent(new g(this.state.target,this.state.sortBy,e.value),!0))}))}}p(h,"Component",(({model:e})=>{const{sortBy:t,direction:n}=e.useState(),r=e.sortingOptions.find((e=>e.options.find((e=>e.value===t)))),a=null==r?void 0:r.options.find((e=>e.value===t));return i().createElement(i().Fragment,null,i().createElement(o.InlineField,{label:"Sort by",htmlFor:"sort-by-criteria",tooltip:"Calculate a derived quantity from the values in your time series and sort by this criteria. Defaults to standard deviation."},i().createElement(o.Select,{"data-testid":u.b.breakdowns.common.sortByFunction,value:a,width:20,isSearchable:!0,options:e.sortingOptions,placeholder:"Choose criteria",onChange:e.onCriteriaChange,inputId:"sort-by-criteria"})),i().createElement(o.InlineField,null,i().createElement(o.Select,{"data-testid":u.b.breakdowns.common.sortByDirection,onChange:e.onDirectionChange,"aria-label":"Sort direction",placeholder:"",value:n,options:[{label:"Asc",value:"asc"},{label:"Desc",value:"desc"}]})))}));const v=["p10","p25","p75","p90","p99"];function m(e){return e.id>="p1"&&e.id<="p99"&&v.includes(e.id)}function f(e){var t;return null!==(t=(0,l.H7)(e))&&void 0!==t?t:"No labels"}},9570:(e,t,n)=>{n.d(t,{O:()=>l});var r=n(6089),a=n(5959),i=n.n(a),s=n(2007);function l({blockingMessage:e,isLoading:t,children:n}){const r=(0,s.useStyles2)(o);return t&&!e&&(e="Loading..."),t?i().createElement(s.LoadingPlaceholder,{className:r.statusMessage,text:e}):e?i().createElement("div",{className:r.statusMessage},e):i().createElement(i().Fragment,null,n)}function o(e){return{statusMessage:(0,r.css)({fontStyle:"italic",marginTop:e.spacing(7),textAlign:"center"})}}},1752:(e,t,n)=>{n.d(t,{l:()=>r});const r=20},6261:(e,t,n)=>{n.d(t,{_:()=>w});var r=n(5959),a=n.n(r),i=n(2007),s=n(7781),l=n(6089);function o(e){return{outline:"2px dotted transparent",outlineOffset:"2px",boxShadow:`0 0 0 2px ${e.colors.background.canvas}, 0 0 0px 4px ${e.colors.primary.main}`,transitionTimingFunction:"cubic-bezier(0.19, 1, 0.22, 1)",transitionDuration:"0.2s",transitionProperty:"outline, outline-offset, box-shadow"}}function c(e,t){return{zIndex:"-1",position:"absolute",opacity:"0",width:`${e}px`,height:`${e}px`,borderRadius:t.shape.radius.default,content:'""',[t.transitions.handleMotion("no-preference","reduce")]:{transitionDuration:"0.2s",transitionTimingFunction:"cubic-bezier(0.4, 0, 0.2, 1)",transitionProperty:"opacity"}}}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){u(e,t,n[t])}))}return e}const p=e=>{const t=(0,i.useTheme2)(),n=e.regex?t.colors.text.maxContrast:t.colors.text.disabled,r=g(t),s=(e.regex?"Disable":"Enable")+" regex";return a().createElement(i.Tooltip,{content:s},a().createElement("button",{onClick:()=>e.onRegexToggle(e.regex?"match":"regex"),className:(0,l.cx)(r.button,e.regex?r.active:null),"aria-label":s},a().createElement("svg",{fill:n,width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},a().createElement("text",{fontSize:"13",width:"16",height:"16",x:"50%",y:"50%",dominantBaseline:"central",textAnchor:"middle"},".*"))))},g=(e,t="secondary")=>{const n=16+e.spacing.gridSize;return{button:(0,l.css)({zIndex:0,position:"relative",margin:`0 ${e.spacing.x0_5} 0 ${e.spacing.x0_5}`,boxShadow:"none",border:"none",display:"inline-flex",background:"transparent",justifyContent:"center",alignItems:"center",padding:0,color:e.colors.text.primary,"&:before":(r=d({},c(n,e)),a={position:"absolute"},a=null!=a?a:{},Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(a)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(a)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(a,e))})),r),"&:hover":{"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:s.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1}},"&:focus, &:focus-visible":o(e),"&:focus:not(:focus-visible)":{outline:"none",boxShadow:"none"}}),active:(0,l.css)({"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:s.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1},"&:hover":{"&:before":{backgroundColor:"none",opacity:0}}})};var r,a};var h=n(8642),v=n(1220),m=n(4793);function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){f(e,t,n[t])}))}return e}const y=e=>{const t=(0,i.useTheme2)(),n=e.caseSensitive?t.colors.text.maxContrast:t.colors.text.disabled,r=S(t),s=(e.caseSensitive?"Disable":"Enable")+" case match";return a().createElement(i.Tooltip,{content:s},a().createElement("button",{onClick:()=>e.onCaseSensitiveToggle(e.caseSensitive?m.ld.caseInsensitive:m.ld.caseSensitive),className:(0,l.cx)(r.button,e.caseSensitive?r.active:null),"aria-label":s},a().createElement("svg",{fill:n,width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},a().createElement("text",{fontSize:"13",width:"16",height:"16",x:"50%",y:"50%",dominantBaseline:"central",textAnchor:"middle"},"Aa"))))},S=(e,t="secondary")=>{const n=16+e.spacing.gridSize;return{button:(0,l.css)({zIndex:0,position:"relative",margin:`0 ${e.spacing.x0_5} 0 ${e.spacing.x0_5}`,boxShadow:"none",border:"none",display:"inline-flex",background:"transparent",justifyContent:"center",alignItems:"center",padding:0,color:e.colors.text.primary,"&:before":(r=b({},c(n,e)),a={position:"absolute"},a=null!=a?a:{},Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(a)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(a)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(a,e))})),r),"&:hover":{"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:s.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1}},"&:focus, &:focus-visible":o(e),"&:focus:not(:focus-visible)":{outline:"none",boxShadow:"none"}}),active:(0,l.css)({"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:s.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1},"&:hover":{"&:before":{backgroundColor:"none",opacity:0}}})};var r,a};function w({exclusive:e,lineFilter:t,caseSensitive:n,setExclusive:r,regex:s,onInputChange:o,onCaseSensitiveToggle:c,onRegexToggle:u,handleEnter:d,onSubmitLineFilter:g,onClearLineFilter:m}){const f=(0,i.useStyles2)(O);return a().createElement("div",{className:f.wrapper},!g&&a().createElement(i.Select,{prefix:null,className:f.select,value:e?"exclusive":"inclusive",options:[{value:"exclusive",label:"Exclude"},{value:"inclusive",label:"Include"}],onChange:()=>r(!e)}),a().createElement(i.Field,{className:f.field},a().createElement(h.D,{"data-testid":v.b.exploreServiceDetails.searchLogs,value:t,className:(0,l.cx)(g?f.inputNoBorderRight:void 0,f.input),onChange:o,suffix:a().createElement("span",{className:`${f.suffix} input-suffix`},a().createElement(y,{caseSensitive:n,onCaseSensitiveToggle:c}),a().createElement(p,{regex:s,onRegexToggle:u})),prefix:null,placeholder:"Search in log lines",onClear:m,onKeyUp:e=>d(e,t)})),g&&a().createElement("span",{className:f.buttonWrap},a().createElement(i.Button,{onClick:()=>{r(!1),g()},className:f.includeButton,variant:"secondary",fill:"outline",disabled:!t},"Include"),a().createElement(i.Button,{onClick:()=>{r(!0),g()},className:f.excludeButton,variant:"secondary",fill:"outline",disabled:!t},"Exclude")))}const O=e=>({inputNoBorderRight:(0,l.css)({input:{borderTopRightRadius:0,borderBottomRightRadius:0}}),suffix:(0,l.css)({display:"inline-flex",gap:e.spacing(.5)}),removeBtn:(0,l.css)({borderTopLeftRadius:0,borderBottomLeftRadius:0}),buttonWrap:(0,l.css)({display:"flex",justifyContent:"center"}),includeButton:(0,l.css)({borderLeft:"none",borderRadius:0,borderRight:"none","&[disabled]":{borderRight:"none"}}),excludeButton:(0,l.css)({borderRadius:`0 ${e.shape.radius.default} ${e.shape.radius.default} 0`,borderLeft:"none","&[disabled]":{borderLeft:"none"}}),submit:(0,l.css)({borderTopLeftRadius:0,borderBottomLeftRadius:0}),select:(0,l.css)({label:"line-filter-exclusion",marginLeft:0,paddingLeft:0,height:"auto",borderBottomRightRadius:"0",borderTopRightRadius:"0",borderRight:"none",minHeight:"30px",width:"100px",maxWidth:"95px",outline:"none"}),wrapper:(0,l.css)({display:"flex",width:"100%",maxWidth:"600px"}),input:(0,l.css)({label:"line-filter-input-wrapper",width:"100%",input:{borderTopLeftRadius:0,borderBottomLeftRadius:0}}),exclusiveBtn:(0,l.css)({marginRight:"1rem"}),field:(0,l.css)({label:"field",flex:"0 1 auto",width:"100%",marginBottom:0})})},9254:(e,t,n)=>{n.d(t,{PY:()=>S,ZB:()=>b,zQ:()=>y});var r=n(6089),a=n(2672),i=n(2007),s=n(5959),l=n.n(s),o=n(227),c=n(1254),u=n(2718),d=n(8760),p=n(7781),g=n(9153),h=n(8531),v=n(4011),m=n(2871);function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class b extends a.Bs{constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){f(e,t,n[t])}))}return e}({},e)),f(this,"handleWrapLinesChange",(e=>{this.getLogsPanelScene().setState({wrapLogMessage:e}),(0,o.YK)("wrapLogMessage",e),this.getLogsListScene().setLogsVizOption({wrapLogMessage:e}),this.getLogsListScene().setLogsVizOption({prettifyLogMessage:e})})),f(this,"onChangeLogsSortOrder",(e=>{this.getLogsPanelScene().setState({sortOrder:e}),(0,o.YK)("sortOrder",e),this.getLogsListScene().setLogsVizOption({sortOrder:e})})),f(this,"getLogsListScene",(()=>a.jh.getAncestor(this,c.i))),f(this,"getLogsPanelScene",(()=>a.jh.getAncestor(this,g.o))),f(this,"clearDisplayedFields",(()=>{this.getLogsListScene().clearDisplayedFields(),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_clear_displayed_fields)}))}}function y(){return(0,o.YM)("sortOrder",p.LogsSortOrder.Descending)}function S(){const e=h.locationService.getLocation(),t=new URLSearchParams(e.search).get("sortOrder");try{if("string"==typeof t){const e=(0,v.FH)(JSON.parse(t));if(e)return e}}catch(e){m.v.error(e,{msg:"LogOptionsScene(getLogsPanelSortOrderFromURL): unable to parse sortOrder"})}return!1}f(b,"Component",(function({model:e}){const{onChangeVisualizationType:t,visualizationType:n}=e.useState(),{wrapLogMessage:r,sortOrder:a}=e.getLogsPanelScene().useState(),{displayedFields:s}=e.getLogsListScene().useState(),o=(0,i.useStyles2)(w),c=null!=r&&r;return l().createElement("div",{className:o.container},s.length>0&&l().createElement(i.Tooltip,{content:`Clear displayed fields: ${s.join(", ")}`},l().createElement(i.Button,{size:"sm",variant:"secondary",fill:"outline",onClick:e.clearDisplayedFields},"Show original log line")),l().createElement(i.InlineField,{className:o.buttonGroupWrapper,transparent:!0},l().createElement(i.RadioButtonGroup,{size:"sm",options:[{label:"Newest first",value:p.LogsSortOrder.Descending,description:"Show results newest to oldest"},{label:"Oldest first",value:p.LogsSortOrder.Ascending,description:"Show results oldest to newest"}],value:a,onChange:e.onChangeLogsSortOrder})),l().createElement(i.InlineField,{className:o.buttonGroupWrapper,transparent:!0},l().createElement(i.RadioButtonGroup,{size:"sm",value:c,onChange:e.handleWrapLinesChange,options:[{label:"Wrap",value:!0,description:"Enable wrapping of long log lines"},{label:"No wrap",value:!1,description:"Disable wrapping of long log lines"}]})),l().createElement(d.C,{vizType:n,onChange:t}))}));const w=e=>({container:(0,r.css)({display:"flex",alignItems:"center",gap:e.spacing(1),marginTop:e.spacing(.5)}),buttonGroupWrapper:(0,r.css)({margin:0,alignItems:"center"})})},1254:(e,t,n)=>{n.d(t,{i:()=>ut});var r=n(5959),a=n.n(r),i=n(2672),s=n(6089),l=n(7781),o=n(9186),c=n(2871),u=n(8831),d=n(4011);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){p(e,t,n[t])}))}return e}const h=`${u.s_}.tableColumnWidths`;var v=function(e){return e.text="text",e.labels="labels",e.auto="auto",e}({});const m=(0,r.createContext)({columnWidthMap:{},setColumnWidthMap:()=>{},columns:{},filteredColumns:{},setColumns:()=>{},setFilteredColumns:()=>{},setVisible:()=>!1,visible:!1,bodyState:"auto",setBodyState:()=>{},clearSelectedLine:()=>{}}),f=({children:e,initialColumns:t,logsFrame:n,setUrlColumns:i,clearSelectedLine:s,setUrlTableBodyState:l,urlTableBodyState:u,showColumnManagementDrawer:p,isColumnManagementActive:v})=>{const[f,y]=(0,r.useState)(b(t)),[S,w]=(0,r.useState)(null!=u?u:"auto"),[O,x]=(0,r.useState)(void 0),E=function(){let e={};const t=localStorage.getItem(h);if(t)try{return e=(0,d.Zt)(JSON.parse(t)),!1===e&&c.v.error(new d.QX("getColumnWidthsFromLocalStorage: unable to validate values in local storage"),{msg:"NarrowingError: error parsing table column widths from local storage"}),e}catch(e){c.v.error(e,{msg:"error parsing table column widths from local storage"})}return e}(),[C,k]=(0,r.useState)(E),F=(0,r.useCallback)((e=>{if(e){const t=b(e);y(t),i((e=>{let t=[];return Object.keys(e).forEach((n=>{e[n].active&&void 0!==e[n].index&&t.push(n)})),t.sort(((t,n)=>{const r=e[t],a=e[n];return r.index-a.index})),t})(t))}}),[i]),L=(0,r.useCallback)((e=>{w(e),l(e)}),[l]),P=(0,r.useCallback)((e=>{p(e)}),[p]);return(0,r.useEffect)((()=>{t&&F(t)}),[t,F]),(0,r.useEffect)((()=>{const e=function(e,t){if(!t)return void c.v.warn("missing dataframe, cannot set url state");const n=Object.keys(e).filter((t=>{var n;return null===(n=e[t])||void 0===n?void 0:n.active})).sort(((t,n)=>{const r=e[t],a=e[n];return void 0!==r.index&&void 0!==a.index?r.index-a.index:0})),r=t.timeField,a=t.bodyField;if(r&&a||n.length){const e=[];return(null==r?void 0:r.name)&&e.push(r.name),(null==a?void 0:a.name)&&e.push(a.name),n.length?n:e}return[]}(f,n);(null==e?void 0:e.length)&&(0===Object.keys(f).filter((e=>f[e].active)).length&&function(e,t,n){const r=g({},e);r[(0,o.fF)(n)]={index:0,active:!0,type:"TIME_FIELD",percentOfLinesWithLabel:100,cardinality:1/0},r[(0,o.Il)(n)]={index:1,active:!0,type:"BODY_FIELD",percentOfLinesWithLabel:100,cardinality:1/0},t(r)}(f,F,n),x(void 0))}),[f,n,x,F]),a().createElement(m.Provider,{value:{setColumnWidthMap:e=>{localStorage.setItem(h,JSON.stringify(e)),k(e)},columnWidthMap:C,bodyState:S,setBodyState:L,setFilteredColumns:x,filteredColumns:O,columns:f,setColumns:F,visible:v,setVisible:P,clearSelectedLine:()=>{s()}}},e)},b=e=>{if("labelTypes"in e){const t=g({},e),{labelTypes:n}=t;return function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(t,["labelTypes"])}return e},y=()=>(0,r.useContext)(m);var S=n(3367),w=n(1269),O=n(8531),x=n(3321),E=n(2007);const C=(0,r.createContext)({cellIndex:{index:null,numberOfMenuItems:3},setActiveCellIndex:e=>!1}),k=({children:e})=>{const[t,n]=(0,r.useState)({index:null}),i=(0,r.useCallback)((e=>{n(e)}),[]);return a().createElement(C.Provider,{value:{cellIndex:t,setActiveCellIndex:i}},e)},F=()=>(0,r.useContext)(C),L=(0,r.createContext)({isHeaderMenuActive:!1,setHeaderMenuActive:e=>!1}),P=({children:e})=>{const[t,n]=(0,r.useState)(!1),i=(0,r.useCallback)((e=>{n(e)}),[]);return a().createElement(L.Provider,{value:{isHeaderMenuActive:t,setHeaderMenuActive:i}},e)},j=()=>(0,r.useContext)(L);var _=n(4932);function T({searchValue:e,setSearchValue:t}){const{columns:n,setFilteredColumns:r}=y(),i=e=>{const t=e[0];let a={},i=0;var s;t.forEach((e=>{e in n&&(a[e]=n[e],i++)})),r(a),s=i,(0,O.reportInteraction)("grafana_logs_app_table_text_search_result_count",{resultCount:s})},l=(o=(0,E.useTheme2)(),{searchWrap:(0,s.css)({padding:`${o.spacing(.4)} 0 ${o.spacing(.4)} ${o.spacing(.4)}`})});var o;return a().createElement(E.Field,{className:l.searchWrap},a().createElement(E.Input,{value:e,type:"text",placeholder:"Search fields by name",onChange:e=>{var a;const s=null===(a=e.currentTarget)||void 0===a?void 0:a.value;var l;t(s),s?(l=s,(0,_.E)(Object.keys(n),l,i)):r(void 0)}}))}var D=n(5755),N=n(5786);function I(){const e=(t=(0,E.useTheme2)(),{empty:(0,s.css)({marginBottom:t.spacing(2),marginLeft:t.spacing(1.75),fontSize:t.typography.fontSize})});var t;return a().createElement("div",{className:e.empty},"No fields")}function $(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function A(e){var t=function(e){if("object"!==M(e)||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!==M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===M(t)?t:String(t)}function M(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}function B(e){const t=(n=(0,E.useTheme2)(),{dragIcon:(0,s.css)({cursor:"drag",marginLeft:n.spacing(1),opacity:.4}),labelCount:(0,s.css)({marginLeft:n.spacing(.5),marginRight:n.spacing(.5),appearance:"none",background:"none",border:"none",fontSize:n.typography.pxToRem(11),opacity:.6,display:"flex",flexDirection:"column",alignItems:"self-end"}),contentWrap:(0,s.css)({display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%"}),customWidthWrap:(0,s.css)({fontSize:n.typography.bodySmall.fontSize,cursor:"pointer"}),checkboxLabel:(0,s.css)({"> span":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block",maxWidth:"100%"}})});var n,r,i,l,o,c,u;return e.labels[e.label]?a().createElement(a().Fragment,null,a().createElement("div",{className:t.contentWrap},a().createElement(E.Checkbox,{className:t.checkboxLabel,label:e.label,onChange:e.onChange,checked:null!==(u=null===(r=e.labels[e.label])||void 0===r?void 0:r.active)&&void 0!==u&&u}),e.showCount&&a().createElement("div",{className:t.labelCount},a().createElement("div",null,null===(i=e.labels[e.label])||void 0===i?void 0:i.percentOfLinesWithLabel,"%"),a().createElement("div",null,null===(l=e.labels[e.label])||void 0===l?void 0:l.cardinality," ",1===(null===(o=e.labels[e.label])||void 0===o?void 0:o.cardinality)?"value":"values")),e.columnWidthMap&&e.setColumnWidthMap&&void 0!==(null===(c=e.columnWidthMap)||void 0===c?void 0:c[e.label])&&a().createElement("div",{onClick:()=>{var t;const n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){$(e,t,n[t])}))}return e}({},e.columnWidthMap),r=e.label,{[r]:a}=n,i=function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(n,[r].map(A));null===(t=e.setColumnWidthMap)||void 0===t||t.call(e,i)},title:"Clear column width override",className:t.customWidthWrap},"Reset column width",a().createElement(E.Icon,{name:"x"}))),e.draggable&&a().createElement(E.Icon,{"aria-label":"Drag and drop icon",title:"Drag and drop to reorder",name:"draggabledots",size:"lg",className:t.dragIcon})):null}function R(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function V(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){R(e,t,n[t])}))}return e}function W(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const z=e=>{const{columnWidthMap:t,setColumnWidthMap:n}=y(),{reorderColumn:r,labels:i,valueFilter:l,toggleColumn:o}=e,c=(0,N.$j)(),{columns:u}=y(),d=function(e){return{wrap:(0,s.css)({marginTop:e.spacing(1),marginBottom:e.spacing(1),display:"flex",background:e.colors.background.primary}),dragging:(0,s.css)({background:e.colors.background.secondary}),columnWrapper:(0,s.css)({marginBottom:e.spacing(1.5),paddingLeft:e.spacing(.5)})}}(c),p=Object.keys(i).filter((e=>l(e))),g=e=>{const t=i[e];if(t)return`${e} appears in ${null==t?void 0:t.percentOfLinesWithLabel}% of log lines`};return p.length?a().createElement(D.JY,{onDragEnd:e=>{e.destination&&r(u,e.source.index,e.destination.index)}},a().createElement(D.gL,{droppableId:"order-fields",direction:"vertical"},(e=>a().createElement("div",W(V({className:d.columnWrapper},e.droppableProps),{ref:e.innerRef}),p.sort(function(e){return(t,n)=>{const r=e[t],a=e[n];return null!=r.index&&null!=a.index?r.index-a.index:0}}(i)).map(((e,r)=>a().createElement(D.sx,{draggableId:e,key:e,index:r},((r,l)=>a().createElement("div",W(V({className:(0,s.cx)(d.wrap,l.isDragging?d.dragging:void 0),ref:r.innerRef},r.draggableProps,r.dragHandleProps),{title:g(e)}),a().createElement(B,{setColumnWidthMap:n,columnWidthMap:t,label:e,onChange:()=>o(e),labels:i,draggable:!0})))))),e.placeholder)))):a().createElement(I,null)},H=new Intl.Collator(void 0,{sensitivity:"base"}),G=e=>{const{labels:t,valueFilter:n,toggleColumn:r}=e,i=(o=(0,E.useTheme2)(),{wrap:(0,s.css)({marginTop:o.spacing(.25),marginBottom:o.spacing(.25),display:"flex",background:o.colors.background.primary,borderBottom:`1px solid ${o.colors.background.canvas}`}),dragging:(0,s.css)({background:o.colors.background.secondary}),columnWrapper:(0,s.css)({marginBottom:o.spacing(1.5),paddingLeft:o.spacing(.5)})}),l=Object.keys(t).filter((e=>n(e)));var o;return l.length?a().createElement("div",{className:i.columnWrapper},l.sort(function(e){return(t,n)=>{const r=e[t],a=e[n];return null!=r&&null!=a?Number("TIME_FIELD"===a.type)-Number("TIME_FIELD"===r.type)||Number("BODY_FIELD"===a.type)-Number("BODY_FIELD"===r.type)||H.compare(t,n):0}}(t)).map((e=>{var n;return a().createElement("div",{key:e,className:i.wrap,title:`${e} appears in ${null===(n=t[e])||void 0===n?void 0:n.percentOfLinesWithLabel}% of log lines`},a().createElement(B,{showCount:!0,label:e,onChange:()=>r(e),labels:t}))}))):a().createElement(I,null)},q=e=>{const t=(n=(0,E.useTheme2)(),{sidebarWrap:(0,s.css)({overflowY:"scroll",height:"calc(100% - 50px)","&::-webkit-scrollbar":{display:"none"},scrollbarWidth:"none"}),columnHeaderButton:(0,s.css)({appearance:"none",background:"none",border:"none",fontSize:n.typography.pxToRem(11)}),columnHeader:(0,s.css)({display:"flex",justifyContent:"space-between",fontSize:n.typography.h6.fontSize,background:n.colors.background.secondary,position:"sticky",top:0,left:0,paddingTop:n.spacing(.75),paddingRight:n.spacing(.75),paddingBottom:n.spacing(.75),paddingLeft:n.spacing(1.5),zIndex:3,marginBottom:n.spacing(2)})});var n,r,i;return a().createElement("div",{className:t.sidebarWrap},a().createElement(a().Fragment,null,a().createElement("div",{className:t.columnHeader},"Selected fields",a().createElement("button",{onClick:e.clear,className:t.columnHeaderButton},"Reset")),a().createElement(z,{reorderColumn:e.reorderColumn,toggleColumn:e.toggleColumn,labels:null!==(r=e.filteredColumnsWithMeta)&&void 0!==r?r:e.columnsWithMeta,valueFilter:t=>{var n,r;return null!==(r=null===(n=e.columnsWithMeta[t])||void 0===n?void 0:n.active)&&void 0!==r&&r},id:"selected-fields"}),a().createElement("div",{className:t.columnHeader},"Fields"),a().createElement(G,{toggleColumn:e.toggleColumn,labels:null!==(i=e.filteredColumnsWithMeta)&&void 0!==i?i:e.columnsWithMeta,valueFilter:t=>{var n;return!(null===(n=e.columnsWithMeta[t])||void 0===n?void 0:n.active)}})))};function K(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function U(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){K(e,t,n[t])}))}return e}function Q(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function J(e){return(t,n,r)=>{if(n===r)return;const a=U({},t),i=Object.keys(a).filter((e=>a[e].active)).map((e=>{var t;return{fieldName:e,index:null!==(t=a[e].index)&&void 0!==t?t:0}})).sort(((e,t)=>e.index-t.index)),[s]=i.splice(n,1);i.splice(r,0,s),i.filter((e=>void 0!==e)).forEach(((e,t)=>{a[e.fieldName].index=t})),e(a)}}function Y(){const{columns:e,setColumns:t,setVisible:n,filteredColumns:i,setFilteredColumns:s}=y(),[l,o]=(0,r.useState)(""),u=J(t);return a().createElement(E.ClickOutsideWrapper,{onClick:()=>{n(!1),s(e),o("")},useCapture:!0},a().createElement(T,{searchValue:l,setSearchValue:o}),a().createElement(q,{toggleColumn:n=>{if(!e||!(n in e))return void function(e,t){let n;try{n={columns:JSON.stringify(t),columnName:e}}catch(t){n={msg:"Table: ColumnSelectionDrawerWrap failed to encode context",columnName:e}}c.v.warn("failed to get column",n)}(n,e);const r=Object.keys(e).filter((t=>e[t].active)).length,a=!e[n].active||void 0;let l;if(l=Q(U({},e),a?{[n]:Q(U({},e[n]),{active:a,index:r})}:{[n]:Q(U({},e[n]),{active:!1,index:void 0})}),function(t){if(e){var n,r;const a=!(null===(n=e[t])||void 0===n?void 0:n.active),i=null===(r=Object.keys(e).filter((t=>{var n;return null===(n=e[t])||void 0===n?void 0:n.active})))||void 0===r?void 0:r.length,s={columnAction:a?"add":"remove",columnCount:a?i+1:i-1};(0,O.reportInteraction)("grafana_logs_app_table_column_filter_clicked",s)}}(n),t(l),i){var u;const e=!(null===(u=i[n])||void 0===u?void 0:u.active);let t;t=Q(U({},i),e?{[n]:Q(U({},i[n]),{active:e,index:r})}:{[n]:Q(U({},i[n]),{active:!1,index:void 0})}),s(t),o("")}},filteredColumnsWithMeta:i,columnsWithMeta:e,clear:()=>{const n=U({},e);let r=0;Object.keys(n).forEach((e=>{const t="BODY_FIELD"===n[e].type||"TIME_FIELD"===n[e].type;n[e].active=t,n[e].index=t?r++:void 0})),t(n),s(n),o("")},reorderColumn:u}))}const X=e=>a().createElement(Z,{onMouseOut:e.onMouseOut,onMouseIn:e.onMouseIn,onClick:e.onClick,field:e.field,rowIndex:e.rowIndex},e.children),Z=e=>{var t;const n=(0,E.useTheme2)(),r=F(),i=(e=>({active:(0,s.css)({height:"calc(100% + 36px)",zIndex:e.zIndex.tooltip,background:"transparent"}),wrap:(0,s.css)({position:"absolute",overflowX:"hidden",whiteSpace:"nowrap",width:"100%",height:"100%",left:0,top:0,margin:"auto",background:"transparent"})}))(n,0,null===(t=r.cellIndex)||void 0===t||t.numberOfMenuItems);return a().createElement("div",{onMouseLeave:e.onMouseOut,onMouseEnter:e.onMouseIn,onClick:e.onClick,className:r.cellIndex.index===e.rowIndex&&r.cellIndex.fieldName===e.field.name?(0,s.cx)(i.wrap,i.active):i.wrap},e.children)},ee={logsFrame:null,addFilter:e=>{},timeRange:void 0,selectedLine:void 0},te=(0,r.createContext)(ee),ne=({children:e,logsFrame:t,addFilter:n,selectedLine:r,timeRange:i})=>a().createElement(te.Provider,{value:{logsFrame:t,addFilter:n,selectedLine:r,timeRange:i}},e),re=()=>(0,r.useContext)(te);var ae=n(4793);const ie=e=>{const t=(r=(0,E.useTheme2)(),i=e.pillType,{menu:(0,s.css)({position:"relative",paddingRight:"5px",display:"flex",minWidth:"60px",justifyContent:"flex-start"}),menuItemsWrap:(0,s.css)({boxShadow:r.shadows.z3,display:"flex",background:r.colors.background.secondary,padding:"5px 0",marginLeft:"column"===i?"5px":void 0}),menuItem:(0,s.css)({overflow:"auto",textOverflow:"ellipsis",cursor:"pointer",paddingLeft:"5px",paddingRight:"5px",display:"flex",alignItems:"center"})}),{addFilter:n}=re();var r,i;return a().createElement("span",{className:t.menu},a().createElement("span",{className:t.menuItemsWrap},"derived"!==e.fieldType&&a().createElement(a().Fragment,null,a().createElement("div",{className:t.menuItem,onClick:()=>{n({key:e.label,value:e.value,operator:ae.w7.Equal})}},a().createElement(E.Icon,{title:"Add to search",size:"md",name:"plus-circle"})),a().createElement("div",{className:t.menuItem,onClick:()=>{n({key:e.label,value:e.value,operator:ae.w7.NotEqual})}},a().createElement(E.Icon,{title:"Exclude from search",size:"md",name:"minus-circle"}))),e.showColumn&&a().createElement("div",{title:"Add column",onClick:e.showColumn,className:t.menuItem},a().createElement("svg",{width:"18",height:"16",viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},a().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.38725 1.33301H13.3872C13.5641 1.33301 13.7336 1.40325 13.8587 1.52827C13.9837 1.65329 14.0539 1.82286 14.0539 1.99967V2.33333C14.0539 2.70152 13.7554 3 13.3872 3H13.0542C12.87 3 12.7206 2.85062 12.7206 2.66634H8.05391V13.333H12.7206C12.7206 13.1491 12.8697 13 13.0536 13H13.3872C13.7554 13 14.0539 13.2985 14.0539 13.6667V13.9997C14.0539 14.1765 13.9837 14.3461 13.8587 14.4711C13.7336 14.5961 13.5641 14.6663 13.3872 14.6663H1.38725C1.21044 14.6663 1.04087 14.5961 0.915843 14.4711C0.790819 14.3461 0.720581 14.1765 0.720581 13.9997V1.99967C0.720581 1.82286 0.790819 1.65329 0.915843 1.52827C1.04087 1.40325 1.21044 1.33301 1.38725 1.33301ZM2.05391 13.333H6.72058V2.66634H2.05391V13.333Z",fill:"#CCCCDC",fillOpacity:"1"}),a().createElement("path",{d:"M13.8538 7.19999H16.2538C16.466 7.19999 16.6695 7.28429 16.8195 7.4343C16.9696 7.58432 17.0538 7.78783 17.0538 7.99999C17.0538 8.21214 16.9696 8.41566 16.8195 8.56567C16.6695 8.71569 16.466 8.79999 16.2538 8.79999H13.8538V11.2C13.8538 11.4121 13.7696 11.6156 13.6195 11.7657C13.4695 11.9157 13.266 12 13.0538 12C12.8416 12 12.6382 11.9157 12.4881 11.7657C12.3381 11.6156 12.2538 11.4121 12.2538 11.2V8.79999H9.85384C9.64165 8.79999 9.43819 8.71569 9.28815 8.56567C9.13811 8.41566 9.05383 8.21214 9.05383 7.99999C9.05383 7.78783 9.13811 7.58432 9.28815 7.4343C9.43819 7.28429 9.64165 7.19999 9.85384 7.19999H12.2538V4.8C12.2538 4.58784 12.3381 4.38433 12.4881 4.23431C12.6382 4.0843 12.8416 4 13.0538 4C13.266 4 13.4695 4.0843 13.6195 4.23431C13.7696 4.38433 13.8538 4.58784 13.8538 4.8V7.19999Z",fill:"#CCCCDC",fillOpacity:"1"}))),e.links&&e.links.map((e=>{var n;return a().createElement("div",{className:t.menuItem,onClick:()=>{window.open(e.href,"_blank")},key:e.href},a().createElement(E.Icon,{title:null!==(n=e.title)&&void 0!==n?n:"Link",key:e.href,size:"md",name:"link"}))}))))},se="detected_level",le=e=>{const{label:t,value:n}=e,r=(0,E.useTheme2)(),{cellIndex:i}=F();let o;if(t===se){const e=$e().options;"string"==typeof n&&n in e&&(o=e[n].color)}const c=i.index===e.rowIndex&&e.field.name===i.fieldName,u=((e,t)=>({activePillWrap:(0,s.css)({}),pillWrap:(0,s.css)({width:"100%"}),pill:(0,s.css)({border:`1px solid ${e.colors.border.weak}`,"&:hover":{border:`1px solid ${e.colors.border.strong}`},marginRight:"5px",marginTop:"4px",marginLeft:"5px",padding:"2px 5px",position:"relative",display:"inline-flex",flexDirection:"row-reverse",backgroundColor:"transparent",paddingLeft:t?`${e.spacing(.75)}`:"2px","&:before":{content:'""',position:"absolute",left:0,top:0,height:"100%",width:`${e.spacing(.25)}`,backgroundColor:t}}),menu:(0,s.css)({width:"100%"}),menuItem:(0,s.css)({overflow:"auto",textOverflow:"ellipsis"}),menuItemText:(0,s.css)({width:"65px",display:"inline-block"})}))(r,o);return a().createElement("div",{className:(0,s.cx)(u.pillWrap,c?u.activePillWrap:void 0)},!!n&&a().createElement(a().Fragment,null,a().createElement("span",{className:u.pill},a().createElement(a().Fragment,null,n)),c&&"string"==typeof n&&e.field.type!==l.FieldType.time&&a().createElement(ie,{label:e.label,value:n,pillType:"column"})))};var oe=n(1220),ce=n(8315);function ue(e){var t;const n=(g=(0,E.useTheme2)(),{clipboardButton:(0,s.css)({padding:0,height:"100%",lineHeight:"1",width:"20px"}),inspectButton:(0,s.css)({display:"inline-flex",verticalAlign:"middle",margin:0,overflow:"hidden",borderRadius:"5px"}),iconWrapper:(0,s.css)({height:"35px",position:"sticky",left:0,display:"flex",background:g.colors.background.secondary,padding:`0 ${g.spacing(.5)}`,zIndex:1,boxShadow:g.shadows.z2}),inspect:(0,s.css)({padding:"5px 3px","&:hover":{color:g.colors.text.link,cursor:"pointer"}})}),{logsFrame:i,timeRange:l}=re(),o=null==i||null===(t=i.idField)||void 0===t?void 0:t.values[e.rowIndex],c=null==i?void 0:i.bodyField.values[e.rowIndex],[u,d]=(0,r.useState)(!1),p=(0,r.useCallback)((()=>l?(0,ce.gW)("selectedLine",{id:o,row:e.rowIndex},l):""),[o,e.rowIndex,l]);var g;return a().createElement(a().Fragment,null,a().createElement("div",{className:n.iconWrapper},a().createElement("div",{className:n.inspect},a().createElement(E.IconButton,{"data-testid":oe.b.table.inspectLine,className:n.inspectButton,tooltip:"View log line",variant:"secondary","aria-label":"View log line",tooltipPlacement:"top",size:"md",name:"eye",onClick:()=>d(!0),tabIndex:0})),a().createElement("div",{className:n.inspect},a().createElement(E.ClipboardButton,{className:n.clipboardButton,icon:"share-alt",variant:"secondary",fill:"text",size:"md",tooltip:"Copy link to log line",tooltipPlacement:"top",tabIndex:0,getText:p}))),a().createElement(a().Fragment,null,u&&a().createElement(E.Modal,{onDismiss:()=>d(!1),isOpen:!0,title:"Inspect value"},a().createElement("pre",null,c),a().createElement(E.Modal.ButtonRow,null,a().createElement(E.ClipboardButton,{icon:"copy",getText:()=>e.value},"Copy to Clipboard")))))}const de=e=>{var t;let n=e.value;const r=e.field,i=r.display(n),o=(h=(0,E.useTheme2)(),v=e.field.type,{flexWrap:(0,s.css)({display:"flex",alignItems:"flex-start",flexDirection:v===l.FieldType.number?"row-reverse":"row",textAlign:v===l.FieldType.number?"right":"left"}),content:(0,s.css)({position:"relative",overflow:"hidden",display:"flex",height:"100%"}),linkWrapper:(0,s.css)({color:h.colors.text.link,marginTop:"7px",marginLeft:"7px","&:hover":{textDecoration:"underline"}})}),{setVisible:c}=y(),{cellIndex:u,setActiveCellIndex:d}=F(),p={index:e.rowIndex},g=Boolean(null===(t=(0,E.getCellLinks)(e.field,p))||void 0===t?void 0:t.length);var h,v;return null===n?a().createElement(a().Fragment,null):(n=a().isValidElement(e.value)?e.value:"object"==typeof n?JSON.stringify(e.value):(0,l.formattedValueToString)(i),a().createElement(X,{onClick:()=>e.rowIndex===u.index&&e.field.name===u.fieldName?d({index:null}):d({index:e.rowIndex,fieldName:e.field.name,numberOfMenuItems:3}),field:e.field,rowIndex:e.rowIndex},a().createElement("div",{className:o.content},0===e.fieldIndex&&a().createElement(ue,{value:n,rowIndex:e.rowIndex}),a().createElement("div",{className:o.flexWrap}),!g&&((t,n)=>a().createElement(le,{field:e.field,rowIndex:e.rowIndex,showColumns:()=>c(!0),label:n,value:t}))(n,r.name),g&&r.getLinks&&a().createElement(E.DataLinksContextMenu,{links:()=>{var e;return null!==(e=(0,E.getCellLinks)(r,p))&&void 0!==e?e:[]}},(e=>e.openMenu?a().createElement("div",{className:o.linkWrapper,onClick:e.openMenu},a().createElement(a().Fragment,null,n)):a().createElement("div",{className:o.linkWrapper},a().createElement(a().Fragment,null,n)))))))};function pe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ge(e){const t=(0,E.useTheme2)();let n;if(e.label===se){const t=$e().options;e.value in t&&(n=t[e.value].color)}const r=((e,t)=>({pill:(0,s.css)({flex:"0 1 auto",marginLeft:e.spacing(.5),marginRight:e.spacing(.5),padding:`${e.spacing(.25)} ${e.spacing(.25)}`,position:"relative",display:"inline-flex",flexDirection:"column",marginTop:e.spacing(.5)}),activePill:(0,s.css)({}),valueWrap:(0,s.css)({border:`1px solid ${e.colors.background.secondary}`,boxShadow:`-2px 2px 5px 0px ${e.colors.background.secondary}`,backgroundColor:"transparent",cursor:"pointer",position:"relative",paddingRight:`${e.spacing(.5)}`,paddingLeft:t?`${e.spacing(.75)}`:`${e.spacing(.5)}`,"&:before":{content:'""',position:"absolute",left:0,top:0,height:"100%",width:`${e.spacing(.25)}`,backgroundColor:t},"&:hover":{border:`1px solid ${e.colors.border.strong}`}})}))(t,n);return a().createElement("span",{className:(0,s.cx)(r.pill,e.menuActive?r.activePill:void 0),onClick:e.onClick},a().createElement("span",{className:r.valueWrap},e.label,"=",e.value),e.menuActive&&a().createElement(ie,{pillType:"logPill",fieldType:e.fieldType,links:e.links,label:e.label,value:e.value,showColumn:e.onClickAdd}))}const he=e=>{const{label:t}=e,{cellIndex:n,setActiveCellIndex:i}=F(),{columns:s,setColumns:o}=y(),c=e.value,u=(0,O.getTemplateSrv)(),d=(0,r.useMemo)((()=>u.replace.bind(u)),[u]),p=e.field;if(!p||(null==p?void 0:p.type)===l.FieldType.other)return null;const g={index:e.rowIndex};e.originalField&&e.isDerivedField&&e.originalFrame&&(e.originalField.getLinks=(0,l.getLinksSupplier)(e.originalFrame,e.originalField,{},d));const h=e.originalField&&(0,E.getCellLinks)(e.originalField,g);return a().createElement(ge,{onClick:()=>e.rowIndex===n.index&&p.name===n.fieldName&&t===n.subFieldName?i({index:null}):i({index:e.rowIndex,fieldName:p.name,subFieldName:t,numberOfMenuItems:e.isDerivedField?2:3}),menuActive:n.index===e.rowIndex&&n.fieldName===p.name&&n.subFieldName===t,fieldType:e.isDerivedField?"derived":void 0,label:t,value:c,onClickAdd:()=>(e=>{const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){pe(e,t,n[t])}))}return e}({},s),n=Object.keys(s).filter((e=>s[e].active)).length;t[e].active?(t[e].active=!1,t[e].index=void 0):(t[e].active=!0,t[e].index=n),o(t)})(t),links:h})},ve=e=>{var t,n;null==e||null===(n=e.current)||void 0===n||n.scrollTo({left:null===(t=e.current)||void 0===t?void 0:t.scrollLeft})};function me({scrollerRef:e}){const t=(n=(0,E.useTheme2)(),{scroller:s.css`
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 20px;
    top: 32px;
    margin-top: -24px;
    // For some reason clicking on this button causes text to be selected in the following row
    user-select: none;
  `,scrollLeft:s.css`
    cursor: pointer;
    background: ${n.colors.background.primary};

    &:hover {
      background: ${n.colors.background.secondary};
    }
  `,scrollRight:s.css`
    cursor: pointer;
    background: ${n.colors.background.primary};

    &:hover {
      background: ${n.colors.background.secondary};
    }
  `});var n;return a().createElement("div",{className:t.scroller},a().createElement("span",{onPointerDown:()=>{var t,n;null==(t=e)||null===(n=t.current)||void 0===n||n.scrollTo({top:0,left:0,behavior:"smooth"})},onPointerUp:()=>ve(e),className:t.scrollLeft},a().createElement(E.Icon,{name:"arrow-left"})),a().createElement("span",{onPointerDown:()=>{var t,n;null==(t=e)||null===(n=t.current)||void 0===n||n.scrollTo({top:0,left:t.current.scrollWidth,behavior:"smooth"})},onPointerUp:()=>ve(e),className:t.scrollRight},a().createElement(E.Icon,{name:"arrow-right"})))}function fe(e){const t=(0,E.useTheme2)(),n=be(t);return a().createElement("div",{"data-testid":oe.b.table.rawLogLine,className:n.rawLogLine},a().createElement(a().Fragment,null,e.value))}const be=(e,t)=>({rawLogLine:(0,s.css)({fontFamily:e.typography.fontFamilyMonospace,height:"35px",lineHeight:"35px",paddingRight:e.spacing(1.5),paddingLeft:e.spacing(1),fontSize:e.typography.bodySmall.fontSize})}),ye=e=>{let t=e.value;const n=e.field,i=n.display(t),s=(0,E.useTheme2)(),c=Se(s),{columns:u,setVisible:d,bodyState:p}=y(),{logsFrame:g}=re(),[h,m]=(0,r.useState)(!1),f=(0,r.useRef)(null);t=a().isValidElement(e.value)?e.value:"object"==typeof t?JSON.stringify(e.value):(0,l.formattedValueToString)(i);const b=(t=>Object.keys(u).filter((e=>e!==(0,o.Il)(g))).sort(((e,t)=>e===se?-1:t===se?1:"LINK_FIELD"===u[e].type?-1:"LINK_FIELD"===u[t].type?1:u[e].cardinality>u[t].cardinality?-1:1)).filter((e=>!u[e].active&&u[e].cardinality>1)).map((r=>{var i;const s=t[r],o=null==g||null===(i=g.raw)||void 0===i?void 0:i.fields.find((e=>e.name===r)),c=null==n?void 0:n.values[e.rowIndex],p=!s&&!!c;if(s)return a().createElement(he,{originalFrame:void 0,field:n,columns:u,rowIndex:e.rowIndex,frame:e.frame,showColumns:()=>d(!0),key:r,label:r,isDerivedField:!1,value:s});if(p&&(null==o?void 0:o.name)){const t=null==o?void 0:o.values[e.rowIndex];if((null==o?void 0:o.type)===l.FieldType.string&&t)return a().createElement(he,{originalFrame:null==g?void 0:g.raw,originalField:o,field:n,value:t,columns:u,rowIndex:e.rowIndex,frame:e.frame,showColumns:()=>d(!0),key:o.name,label:o.name,isDerivedField:!0})}return null})).filter((e=>e)))(e.labels),w=p===v.auto,O=b.length>0;return a().createElement(X,{onMouseIn:()=>{m(!0)},onMouseOut:()=>{m(!1)},rowIndex:e.rowIndex,field:e.field},a().createElement(S.ScrollSyncPane,{innerRef:f,group:"horizontal"},a().createElement("div",{className:c.content},0===e.fieldIndex&&a().createElement(ue,{rowIndex:e.rowIndex,value:t}),w&&O&&a().createElement(a().Fragment,null,b),p===v.labels&&O&&a().createElement(a().Fragment,null,b),p===v.labels&&!O&&a().createElement(fe,{value:t}),w&&!O&&a().createElement(fe,{value:t}),p===v.text&&a().createElement(fe,{value:t}),h&&a().createElement(me,{scrollerRef:f}))))},Se=e=>({content:s.css`
    white-space: nowrap;
    overflow-x: auto;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    padding-right: 30px;
    display: flex;
    align-items: flex-start;
    height: 100%;
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
    }

    &:after {
      pointer-events: none;
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      // Fade out text in last 10px to background color to add affordance to horiziontal scroll
      background: linear-gradient(to right, transparent calc(100% - 10px), ${e.colors.background.primary});
    }
  `});function we(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Oe(e){var t=function(e){if("object"!==xe(e)||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!==xe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===xe(t)?t:String(t)}function xe(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}const Ee=e=>{const{setHeaderMenuActive:t,isHeaderMenuActive:n}=j(),{logsFrame:i}=re(),l=(0,r.useRef)(null),c=(f=(0,E.useTheme2)(),b=0===e.fieldIndex,S=e.field.name===(0,o.Il)(i),{logLineButton:(0,s.css)({marginLeft:"5px"}),tableHeaderMenu:(0,s.css)({label:"tableHeaderMenu",width:"100%",minWidth:"250px",height:"100%",maxHeight:"400px",backgroundColor:f.colors.background.primary,border:`1px solid ${f.colors.border.weak}`,padding:f.spacing(2),margin:f.spacing(1,0),boxShadow:f.shadows.z3,borderRadius:f.shape.radius.default}),leftAlign:(0,s.css)({label:"left-align",display:"flex",width:"calc(100% - 20px)"}),clearButton:(0,s.css)({marginLeft:"5px"}),rightAlign:(0,s.css)({label:"right-align",display:"flex",marginRight:"5px"}),wrapper:(0,s.css)({label:"wrapper",display:"flex",marginLeft:b?"56px":"6px",width:S?"calc(100% + 6px)":"100%",borderRight:`1px solid ${f.colors.border.weak}`,marginRight:"-6px"}),defaultContentWrapper:(0,s.css)({borderLeft:b?`1px solid ${f.colors.border.weak}`:"none",marginLeft:b?"-6px":0,paddingLeft:b?"12px":0,display:"flex"})}),{columnWidthMap:u,setColumnWidthMap:d,setBodyState:p,bodyState:g}=y(),h=e.field.name===(0,o.Il)(i),m=()=>{p(g===v.text?v.labels:v.text)};var f,b,S;return a().createElement("span",{className:c.wrapper},a().createElement("span",{className:c.leftAlign},a().createElement("span",{className:c.defaultContentWrapper},e.defaultContent),u&&d&&void 0!==(null==u?void 0:u[e.field.name])&&a().createElement(E.IconButton,{tooltip:"Reset column width",tooltipPlacement:"top",className:c.clearButton,"aria-label":"Reset column width",name:"x",onClick:()=>{const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){we(e,t,n[t])}))}return e}({},u),n=e.field.name,{[n]:r}=t,a=function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(t,[n].map(Oe));null==d||d(a)}}),h&&a().createElement(a().Fragment,null,g===v.text?a().createElement(E.IconButton,{tooltipPlacement:"top",tooltip:"Show log labels","aria-label":"Show log labels",onClick:m,className:c.logLineButton,name:"brackets-curly",size:"md"}):a().createElement(E.IconButton,{tooltipPlacement:"top",tooltip:"Show log text","aria-label":"Show log text",onClick:m,className:c.logLineButton,name:"text-fields",size:"md"}))),a().createElement("span",{className:c.rightAlign},a().createElement(E.IconButton,{tooltip:`Show ${e.field.name} menu`,tooltipPlacement:"top",ref:l,"aria-label":`Show ${e.field.name} menu`,onClick:e=>{t(!n)},name:"ellipsis-v"})),l.current&&a().createElement(E.Popover,{show:n,content:a().createElement(E.ClickOutsideWrapper,{onClick:()=>t(!1),useCapture:!0},a().createElement("div",{className:c.tableHeaderMenu},e.children)),referenceElement:l.current}))};function Ce(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ke(e){const{setHeaderMenuActive:t}=j(),{columns:n,setColumns:i,bodyState:l,setBodyState:c}=y(),{logsFrame:u}=re(),d=Fe(),p=(0,r.useCallback)((e=>{const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ce(e,t,n[t])}))}return e}({},n);Object.keys(t).filter((n=>{const r=t[n].index,a=t[e.name].index;return t[n].active&&a&&r&&r>a})).map((e=>t[e])).forEach((e=>{void 0!==e.index&&e.index--})),t[e.name].active=!1,t[e.name].index=void 0,i(t)}),[n,i]),g=e.headerProps.field.name===(0,o.Il)(u);return a().createElement(Ee,e.headerProps,a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{e.openColumnManagementDrawer(),t(!1)}},a().createElement(E.Icon,{className:d.icon,name:"columns",size:"md"}),"Manage columns")),a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>p(e.headerProps.field)},a().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 17 16",width:"17",height:"16",className:"css-q2u0ig-Icon"},a().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.73446 1.33301H12.2345C12.3892 1.33301 12.5375 1.40325 12.6469 1.52827C12.7563 1.65329 12.8178 1.82286 12.8178 1.99967V4.74967C12.8178 5.07184 12.5566 5.33301 12.2345 5.33301C11.9123 5.33301 11.6511 5.07184 11.6511 4.74967V2.66634H7.56779V13.333H11.6511V10.9163C11.6511 10.5942 11.9123 10.333 12.2345 10.333C12.5566 10.333 12.8178 10.5942 12.8178 10.9163V13.9997C12.8178 14.1765 12.7563 14.3461 12.6469 14.4711C12.5375 14.5961 12.3892 14.6663 12.2345 14.6663H1.73446C1.57975 14.6663 1.43137 14.5961 1.32198 14.4711C1.21258 14.3461 1.15112 14.1765 1.15112 13.9997V1.99967C1.15112 1.82286 1.21258 1.65329 1.32198 1.52827C1.43137 1.40325 1.57975 1.33301 1.73446 1.33301ZM2.31779 13.333H6.40112V2.66634H2.31779V13.333Z",fill:"#CCCCDC",fillOpacity:"1"}),a().createElement("path",{d:"M15.9893 10.6315C15.9498 10.7263 15.8919 10.8123 15.819 10.8846C15.7467 10.9575 15.6607 11.0154 15.5659 11.0549C15.4712 11.0943 15.3695 11.1147 15.2668 11.1147C15.1641 11.1147 15.0625 11.0943 14.9677 11.0549C14.8729 11.0154 14.7869 10.9575 14.7146 10.8846L12.9335 9.09573L11.1524 10.8846C11.0801 10.9575 10.9941 11.0154 10.8993 11.0549C10.8045 11.0943 10.7028 11.1147 10.6002 11.1147C10.4975 11.1147 10.3958 11.0943 10.301 11.0549C10.2063 11.0154 10.1202 10.9575 10.0479 10.8846C9.97504 10.8123 9.91717 10.7263 9.87769 10.6315C9.8382 10.5367 9.81787 10.4351 9.81787 10.3324C9.81787 10.2297 9.8382 10.1281 9.87769 10.0333C9.91717 9.9385 9.97504 9.85248 10.0479 9.78017L11.8368 7.99906L10.0479 6.21795C9.90148 6.07149 9.8192 5.87285 9.8192 5.66573C9.8192 5.4586 9.90148 5.25996 10.0479 5.1135C10.1944 4.96705 10.393 4.88477 10.6002 4.88477C10.8073 4.88477 11.0059 4.96705 11.1524 5.1135L12.9335 6.90239L14.7146 5.1135C14.8611 4.96705 15.0597 4.88477 15.2668 4.88477C15.4739 4.88477 15.6726 4.96705 15.819 5.1135C15.9655 5.25996 16.0478 5.4586 16.0478 5.66573C16.0478 5.87285 15.9655 6.07149 15.819 6.21795L14.0302 7.99906L15.819 9.78017C15.8919 9.85248 15.9498 9.9385 15.9893 10.0333C16.0288 10.1281 16.0491 10.2297 16.0491 10.3324C16.0491 10.4351 16.0288 10.5367 15.9893 10.6315Z",fill:"#CCCCDC",fillOpacity:"1"})),"Remove column")),e.slideLeft&&a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{var t;return null===(t=e.slideLeft)||void 0===t?void 0:t.call(e,n)}},a().createElement(E.Icon,{className:(0,s.cx)(d.icon,d.reverse),name:"arrow-from-right",size:"md"}),"Move left")),e.slideRight&&a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{var t;return null===(t=e.slideRight)||void 0===t?void 0:t.call(e,n)}},a().createElement(E.Icon,{className:d.icon,name:"arrow-from-right",size:"md"}),"Move right")),g&&a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{l===v.text?c(v.labels):c(v.text)}},l===v.text?a().createElement(E.Icon,{className:d.icon,name:"brackets-curly",size:"md"}):a().createElement(E.Icon,{className:d.icon,name:"text-fields",size:"md"}),l===v.text?"Show labels":"Show log text")),e.autoColumnWidths&&a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{var t;return null===(t=e.autoColumnWidths)||void 0===t?void 0:t.call(e)}},a().createElement(E.Icon,{className:d.icon,name:"arrows-h",size:"md"}),"Reset column widths")))}const Fe=()=>({reverse:(0,s.css)({transform:"scaleX(-1)"}),link:(0,s.css)({paddingTop:"5px",paddingBottom:"5px"}),icon:(0,s.css)({marginRight:"10px"}),linkWrap:(0,s.css)({})});var Le=n(3241);function Pe(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}function je(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _e(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){je(e,t,n[t])}))}return e}function Te(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function De(e){return a().createElement(E.Table,{onColumnResize:e.onResize,initialSortBy:[{displayName:(0,o.fF)(e.logsFrame),desc:!0}],initialRowIndex:e.selectedLine,cellHeight:x.qM.Sm,data:e.data,height:e.height,width:e.width,footerOptions:{show:!0,reducer:["count"],countRows:!0}})}const Ne=e=>{const{height:t,timeZone:n,logsFrame:i,width:c,labels:u}=e,d=(0,E.useTheme2)(),p={section:(0,s.css)({position:"relative"}),tableWrap:(0,s.css)({".cellActions":{display:"none !important"}})},[g,h]=(0,r.useState)(void 0),{columns:v,visible:m,setVisible:f,setFilteredColumns:b,setColumns:x,clearSelectedLine:C,columnWidthMap:F,setColumnWidthMap:L}=y(),{selectedLine:j}=re(),[_]=(0,r.useState)(j),T=J(x),D=(0,O.getTemplateSrv)(),N=(0,r.useMemo)((()=>D.replace.bind(D)),[D]),I=(0,r.useCallback)((e=>{if(!e.length)return e;const[t]=(0,l.applyFieldOverrides)({data:[e],timeZone:n,theme:d,replaceVariables:N,fieldConfig:{defaults:{custom:{}},overrides:[]}});for(const[n,o]of t.fields.entries()){var r,s;o.type=o.type===l.FieldType.string?null!==(r=Ie(o))&&void 0!==r?r:l.FieldType.string:o.type,o.config=Te(_e({},o.config),{custom:_e({inspect:!0,filterable:!0,headerComponent:t=>a().createElement(P,null,a().createElement(ke,{headerProps:Te(_e({},t),{fieldIndex:n}),openColumnManagementDrawer:()=>f(!0),slideLeft:0!==n?e=>T(e,n,n-1):void 0,slideRight:n!==e.fields.length-1?e=>T(e,n,n+1):void 0,autoColumnWidths:Object.keys(F).length>0?()=>{L({})}:void 0})),width:null!==(s=F[o.name])&&void 0!==s?s:Me(o,n,v,c,t.fields.length,i),cellOptions:Ae(o,n,u,i)},o.config.custom),filterable:!0})}return t}),[n,d,u,c,N,f,F]);(0,r.useEffect)((()=>{const e=function(){var e,t=(e=function*(){const e=(t=i.raw).fields.filter((e=>{var n,r,a;const i="json.RawMessage"===(null===(n=e.typeInfo)||void 0===n?void 0:n.frame)&&"labels"===e.name&&(null==t||null===(r=t.meta)||void 0===r?void 0:r.type)!==l.DataFrameType.LogLines,s="labels"===e.name&&e.type===l.FieldType.other&&(null==t||null===(a=t.meta)||void 0===a?void 0:a.type)===l.DataFrameType.LogLines;return i||s})).flatMap((e=>[{id:"extractFields",options:{format:"json",keepTime:!1,replace:!1,source:e.name}}]));var t;const n=function(e){let t={};for(const n in e)t[n]=!0;return Object.keys(e).length>0?{id:"organize",options:{indexByName:e,includeByName:t}}:null}(function(e){let t={};return Object.keys(e).filter((t=>e[t].active)).forEach((n=>{const r=e[n].index;void 0!==r&&(t[n]=r)})),t}(v));if(n)e.push(n);else{const t={time:i.timeField,body:i.bodyField,extraFields:i.extraFields};t&&void 0!==t.body&&void 0!==t.time&&e.push(function(e){return{id:"organize",options:{indexByName:{[e.time.name]:0,[e.body.name]:1},includeByName:{[e.body.name]:!0,[e.time.name]:!0}}}}(t))}if(e.length>0){const t=yield(0,w.lastValueFrom)((0,l.transformDataFrame)(e,[i.raw])),n=I(t[0]);h(n)}else h(I(i.raw))},function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){Pe(i,r,a,s,l,"next",e)}function l(e){Pe(i,r,a,s,l,"throw",e)}s(void 0)}))});return function(){return t.apply(this,arguments)}}();e()}),[i.raw,i.bodyField,i.timeField,i.extraFields,I,v]),(0,r.useEffect)((()=>{_&&j&&C()}),[_,C,j]);const $=i.raw.fields.find((e=>e.name===(0,o.po)(i))),A=null==$?void 0:$.values.findIndex((e=>e===(null==_?void 0:_.id))),M=A&&-1!==A?A:void 0;return g?a().createElement("div",{"data-testid":oe.b.table.wrapper,className:p.section},m&&a().createElement(E.Drawer,{size:"sm",onClose:()=>{f(!1),b(v)}},a().createElement(Y,null)),a().createElement("div",{className:p.tableWrap},a().createElement(k,null,a().createElement(S.ScrollSync,{horizontal:!0,vertical:!1,proportional:!1},a().createElement(De,{logsFrame:i,selectedLine:M,data:g,height:t,width:c,onResize:(0,Le.debounce)(((e,t)=>{const n=Object.keys(v).filter((e=>v[e].active)).find((t=>t===e));if(n&&t>0){const e=_e({},F);e[n]=t,L(e)}}),100)}))))):a().createElement(a().Fragment,null)};function Ie(e){if(e.name){const t=e.name.toLowerCase();if("date"===t||"time"===t)return l.FieldType.time}for(let t=0;t<e.values.length;t++){const n=e.values[t];if(null!=n)return ze(n)}}const $e=()=>({options:{critical:{color:"#705da0",index:0},crit:{color:"#705da0",index:1},error:{color:"#e24d42",index:2},err:{color:"#e24d42",index:3},eror:{color:"#e24d42",index:4},warning:{color:"#FF9900",index:5},warn:{color:"#FF9900",index:6},info:{color:"#7eb26d",index:7},debug:{color:"#1f78c1",index:8},trace:{color:"#6ed0e0",index:9}},type:l.MappingType.ValueToText});function Ae(e,t,n,r){return e.name===(0,o.Il)(r)?{cellComponent:e=>a().createElement(ye,Te(_e({},e),{fieldIndex:t,labels:n[e.rowIndex]})),type:E.TableCellDisplayMode.Custom}:{cellComponent:e=>a().createElement(de,Te(_e({},e),{fieldIndex:t})),type:E.TableCellDisplayMode.Custom}}function Me(e,t,n,r,a,i){var s,c;const u=a<=2?r:Math.min(r/2),d=0===t?50:0;if(e.type===l.FieldType.time)return 200+d;const p=n[e.name];if(void 0===p)return;var g;const h=Math.max(null!==(g=p.maxLength)&&void 0!==g?g:0,e.name.length);return p.maxLength?Math.min(Math.max(6.5*h+95+d,90+d),u):e.name!==(0,o.Il)(i)?Math.min(Math.max(6.5*(null!==(v=null===(c=e.values)||void 0===c||null===(s=c[0])||void 0===s?void 0:s.length)&&void 0!==v?v:80)+95+d,90+d),u):void 0;var v}var Be=n(5540);const Re=/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3,})?(?:Z|[-+]\d{2}:?\d{2})$/,Ve=e=>{const{logsFrame:t}=re(),[n,i]=(0,r.useState)({width:0,height:0});(0,Be.w)({ref:e.panelWrap,onResize:()=>{const t=e.panelWrap.current;t&&(n.width===t.clientWidth&&n.height===t.clientHeight||i({width:t.clientWidth,height:t.clientHeight}))}});const o={section:(0,s.css)({position:"relative"})},c=(0,l.getTimeZone)(),u=(0,r.useCallback)((t=>{const n=e.urlColumns;return(null==n?void 0:n.length)&&Object.values(n).forEach(((e,n)=>{t[e]&&(t[e].active=!0,t[e].index=n)})),t}),[e.urlColumns]);if(!t||!t.raw.length)return null;var d;const p=null!==(d=t.getLogFrameLabelsAsLabels())&&void 0!==d?d:[],g=t?t.raw.length:0;let h=function(e,t){let n={};const r=new Map,a=function(e){const t=new Map;return e.forEach((e=>{Object.keys(e).forEach((n=>{if(t.has(n)){const r=t.get(n),a=null==r?void 0:r.valueSet,i=null==r?void 0:r.maxLength;a&&!(null==a?void 0:a.has(e[n]))&&(null==a||a.add(e[n]),i&&e[n].length>i&&t.set(n,{maxLength:e[n].length,valueSet:a}))}else t.set(n,{maxLength:e[n].length,valueSet:new Set([e[n]])})}))})),t}(t),i=e?e.length:0;return(null==t?void 0:t.length)&&i&&(t.forEach((e=>{Object.keys(e).forEach((e=>{var t;const n=a.get(e);var i;const s=null!==(i=null==n||null===(t=n.valueSet)||void 0===t?void 0:t.size)&&void 0!==i?i:0;if(r.has(e)){const t=r.get(e);t&&((null==t?void 0:t.active)?r.set(e,{percentOfLinesWithLabel:t.percentOfLinesWithLabel+1,active:!0,index:t.index,cardinality:s,maxLength:null==n?void 0:n.maxLength}):r.set(e,{percentOfLinesWithLabel:t.percentOfLinesWithLabel+1,active:!1,index:void 0,cardinality:s,maxLength:null==n?void 0:n.maxLength}))}else r.set(e,{percentOfLinesWithLabel:1,active:!1,index:void 0,cardinality:s,maxLength:null==n?void 0:n.maxLength})}))})),n=Object.fromEntries(r),Object.keys(n).forEach((e=>{n[e].percentOfLinesWithLabel=We(n[e].percentOfLinesWithLabel,i)}))),n}(t.raw,p);const v={time:t.timeField,body:t.bodyField,extraFields:t.extraFields};return v&&(function(e,t,n){e.forEach((e=>{var r,a;if(!e)return;const i=null===(r=t[e.name])||void 0===r?void 0:r.active,s=null===(a=t[e.name])||void 0===a?void 0:a.index;t[e.name]=i&&void 0!==s?{percentOfLinesWithLabel:We(e.values.filter((e=>null!=e)).length,n),active:!0,index:s,cardinality:n}:{percentOfLinesWithLabel:We(e.values.filter((e=>null!=e)).length,n),active:!1,index:void 0,cardinality:n}}))}([v.time,v.body,...v.extraFields],h,g),h=u(h),function(e,t,n){var r,a,i,s,l,o,c,u,d,p;0===e.length&&((null===(i=t.body)||void 0===i?void 0:i.name)&&(n[null===(l=t.body)||void 0===l?void 0:l.name].active=!0,n[null===(o=t.body)||void 0===o?void 0:o.name].index=1),(null===(s=t.time)||void 0===s?void 0:s.name)&&(n[null===(c=t.time)||void 0===c?void 0:c.name].active=!0,n[null===(u=t.time)||void 0===u?void 0:u.name].index=0));(null===(r=t.time)||void 0===r?void 0:r.name)&&(null===(a=t.body)||void 0===a?void 0:a.name)&&(n[null===(d=t.body)||void 0===d?void 0:d.name].type="BODY_FIELD",n[null===(p=t.time)||void 0===p?void 0:p.name].type="TIME_FIELD");t.extraFields.length&&t.extraFields.forEach((e=>{var t;(null===(t=e.config.links)||void 0===t?void 0:t.length)&&(n[e.name].type="LINK_FIELD")}))}(Object.keys(h).filter((e=>h[e].active)),v,h)),a().createElement("section",{className:o.section},a().createElement(f,{setUrlTableBodyState:e.setUrlTableBodyState,logsFrame:t,initialColumns:h,setUrlColumns:e.setUrlColumns,clearSelectedLine:e.clearSelectedLine,urlTableBodyState:e.urlTableBodyState,showColumnManagementDrawer:e.showColumnManagementDrawer,isColumnManagementActive:e.isColumnManagementActive},a().createElement(Ne,{logsFrame:t,timeZone:c,height:n.height-50,width:n.width-25,labels:p})))},We=(e,t)=>Math.ceil(100*e/t);function ze(e){let t=(0,l.guessFieldTypeFromValue)(e);return"string"===t&&Re.test(e)&&(t=l.FieldType.time),t}const He=({dataFrame:e,setUrlColumns:t,urlColumns:n,addFilter:r,selectedLine:i,timeRange:s,panelWrap:l,clearSelectedLine:c,setUrlTableBodyState:u,urlTableBodyState:d,showColumnManagementDrawer:p,isColumnManagementActive:g})=>{if(!e)return null;const h=(0,o.Os)(e);return h?a().createElement(ne,{addFilter:r,selectedLine:i,timeRange:s,logsFrame:h},a().createElement(Ve,{urlTableBodyState:d,setUrlColumns:t,setUrlTableBodyState:u,urlColumns:n,panelWrap:l,clearSelectedLine:c,showColumnManagementDrawer:p,isColumnManagementActive:g})):null};var Ge=n(8760),qe=n(558),Ke=n(833),Ue=n(2254),Qe=n(7097),Je=n(7085);function Ye(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Xe extends i.Bs{onActivate(){this.setState({menu:new Je.GD({addInvestigationsLink:!1})})}constructor(e){var t,n;super((t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ye(e,t,n[t])}))}return e}({},e),n=null!=(n={isColumnManagementActive:!1})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)),Ye(this,"showColumnManagementDrawer",(e=>{this.setState({isColumnManagementActive:e})})),this.addActivationHandler(this.onActivate.bind(this))}}Ye(Xe,"Component",(({model:e})=>{const t=(0,E.useStyles2)(Ze),n=i.jh.getAncestor(e,ut),{data:s}=i.jh.getData(e).useState(),{selectedLine:l,urlColumns:o,visualizationType:c,tableLogLineState:u}=n.useState(),{menu:d,isColumnManagementActive:p}=e.useState(),g=i.jh.getTimeRange(e),{value:h}=g.useState(),v=(0,Ue.tn)(s),m=(0,r.useRef)(null);return a().createElement("div",{className:t.panelWrapper,ref:m},a().createElement(E.PanelChrome,{loadingState:null==s?void 0:s.state,title:"Logs",menu:d?a().createElement(d.Component,{model:d}):void 0,actions:a().createElement(a().Fragment,null,a().createElement(E.Button,{onClick:()=>e.showColumnManagementDrawer(!0),variant:"secondary",size:"sm"},"Manage columns"),a().createElement(Ge.C,{vizType:c,onChange:n.setVisualizationType}))},v&&a().createElement(He,{panelWrap:m,addFilter:t=>{const r=(0,Qe.OE)(v,t.key,e);(0,qe.XI)(t,n,r)},timeRange:h,selectedLine:l,urlColumns:null!=o?o:[],setUrlColumns:e=>{(0,Ke.n)(e,n.state.urlColumns)||n.setState({urlColumns:e})},dataFrame:v,clearSelectedLine:()=>{n.state.selectedLine&&n.clearSelectedLine()},setUrlTableBodyState:e=>{n.setState({tableLogLineState:e})},urlTableBodyState:u,showColumnManagementDrawer:e.showColumnManagementDrawer,isColumnManagementActive:p})))}));const Ze=e=>({panelWrapper:(0,s.css)({width:"100%",height:"100%",label:"panel-wrapper-table","button.show-on-hover":{opacity:1,visibility:"visible",background:"none","&:hover":{background:e.colors.secondary.shade}}})});var et=n(2718),tt=n(9153),nt=n(227),rt=n(6177),at=n.n(rt),it=n(4750),st=n(6261);function lt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ot extends i.Bs{clearVariable(){(0,it.Rr)(this).updateFilters([],{skipPublish:!0}),this.setState({lineFilter:""})}getOperator(){if(this.state.regex&&this.state.exclusive)return ae.cK.negativeRegex;if(this.state.regex&&!this.state.exclusive)return ae.cK.regex;if(!this.state.regex&&this.state.exclusive)return ae.cK.negativeMatch;if(!this.state.regex&&!this.state.exclusive)return ae.cK.match;throw new Error("getOperator: failed to determine operation")}getFilterKey(){return this.state.caseSensitive?ae.ld.caseSensitive:ae.ld.caseInsensitive}getFilter(){return(0,it.Rr)(this).state.filters[0]}updateFilter(e,t=!0){this.updateInputState(e),t?this.updateVariableDebounced(e):this.updateVariable(e)}updateInputState(e){this.setState({lineFilter:e})}constructor(e){var t,n,r;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){lt(e,t,n[t])}))}return e}({lineFilter:(null==e?void 0:e.lineFilter)||"",caseSensitive:null!==(t=null==e?void 0:e.caseSensitive)&&void 0!==t?t:(0,nt.hp)(!1),regex:null!==(n=null==e?void 0:e.regex)&&void 0!==n?n:(0,nt.og)(!1),exclusive:null!==(r=null==e?void 0:e.exclusive)&&void 0!==r?r:(0,nt.Zs)(!1)},e)),lt(this,"onActivate",(()=>{const e=this.getFilter();if(e)return this.setState({lineFilter:e.value,regex:e.operator===ae.cK.regex||e.operator===ae.cK.negativeRegex,caseSensitive:e.key===ae.ld.caseSensitive,exclusive:e.operator===ae.cK.negativeMatch||e.operator===ae.cK.negativeRegex}),()=>{this.clearFilter()}})),lt(this,"clearFilter",(()=>{this.updateVariableDebounced.cancel(),this.updateFilter("",!1)})),lt(this,"onToggleExclusive",(e=>{(0,nt.Bq)(e),this.setState({exclusive:e}),this.updateFilter(this.state.lineFilter,!1)})),lt(this,"onSubmitLineFilter",(()=>{this.updateFilter(this.state.lineFilter,!1),this.updateVariableDebounced.flush();const e=(0,it.Gk)(this),t=e.state.filters,n=this.getFilter();e.updateFilters([...t,n]),this.clearVariable()})),lt(this,"handleChange",(e=>{this.updateInputState(e.target.value)})),lt(this,"handleEnter",(e=>{"Enter"===e.key&&this.state.lineFilter&&this.onSubmitLineFilter()})),lt(this,"onCaseSensitiveToggle",(e=>{const t=e===ae.ld.caseSensitive;this.setState({caseSensitive:t}),(0,nt.Xo)(t),this.updateFilter(this.state.lineFilter,!1)})),lt(this,"onRegexToggle",(e=>{const t="regex"===e;this.setState({regex:t}),(0,nt.GL)(t),this.updateFilter(this.state.lineFilter,!1)})),lt(this,"updateVariableDebounced",at()((e=>{this.updateVariable(e)}),1e3)),lt(this,"updateVariable",(e=>{this.updateVariableDebounced.flush();const t=(0,it.Rr)(this),n=(0,it.Gk)(this),r={key:this.getFilterKey(),keyLabel:n.state.filters.length.toString(),operator:this.getOperator(),value:e};t.updateFilters([r]),(0,et.EE)(et.NO.service_details,et.ir.service_details.search_string_in_logs_changed,{searchQueryLength:e.length,containsLevel:e.toLowerCase().includes("level"),operator:r.operator,caseSensitive:r.key})})),this.addActivationHandler(this.onActivate)}}function ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}lt(ot,"Component",(function({model:e}){const{lineFilter:t,caseSensitive:n,regex:r,exclusive:a}=e.useState();return(0,st._)({exclusive:a,lineFilter:t,caseSensitive:n,regex:r,onSubmitLineFilter:e.onSubmitLineFilter,handleEnter:e.handleEnter,onInputChange:e.handleChange,updateFilter:e.updateFilter,onCaseSensitiveToggle:e.onCaseSensitiveToggle,onRegexToggle:e.onRegexToggle,setExclusive:e.onToggleExclusive,onClearLineFilter:e.clearFilter})}));class ut extends i.Bs{getUrlState(){var e;const t=null!==(e=this.state.urlColumns)&&void 0!==e?e:[],n=this.state.selectedLine,r=this.state.visualizationType;var a,i;const s=null!==(i=null!==(a=this.state.displayedFields)&&void 0!==a?a:(0,nt.N$)(this))&&void 0!==i?i:[];return{urlColumns:JSON.stringify(t),selectedLine:JSON.stringify(n),visualizationType:JSON.stringify(r),displayedFields:JSON.stringify(s),tableLogLineState:JSON.stringify(this.state.tableLogLineState)}}updateFromUrl(e){const t={};try{if("string"==typeof e.urlColumns){const n=(0,d.aJ)(JSON.parse(e.urlColumns));n!==this.state.urlColumns&&(t.urlColumns=n)}if("string"==typeof e.selectedLine){const n=(0,d.lb)(JSON.parse(e.selectedLine));if(n){const e=n;e!==this.state.selectedLine&&(t.selectedLine=e)}}if("string"==typeof e.visualizationType){const n=(0,d.v_)(JSON.parse(e.visualizationType));n&&n!==this.state.visualizationType&&(t.visualizationType=n)}if("string"==typeof e.displayedFields){const n=(0,d.aJ)(JSON.parse(e.displayedFields));n&&n.length&&(t.displayedFields=n)}if("string"==typeof e.tableLogLineState){const n=JSON.parse(e.tableLogLineState);n!==v.labels&&n!==v.text||(t.tableLogLineState=n)}}catch(e){c.v.error(e,{msg:"LogsListScene: updateFromUrl unexpected error"})}Object.keys(t).length&&this.setState(t)}clearSelectedLine(){this.setState({selectedLine:void 0})}onActivate(){const e=new URLSearchParams(O.locationService.getLocation().search);this.setStateFromUrl(e),this.state.panel||this.updateLogsPanel(),this._subs.add(this.subscribeToState(((e,t)=>{e.visualizationType!==t.visualizationType&&this.updateLogsPanel()})))}setStateFromUrl(e){const t=e.get("selectedLine"),n=e.get("urlColumns"),r=e.get("visualizationType");var a;const i=null!==(a=e.get("displayedFields"))&&void 0!==a?a:JSON.stringify((0,nt.N$)(this)),s=e.get("tableLogLineState");this.updateFromUrl({selectedLine:t,urlColumns:n,vizType:r,displayedFields:i,tableLogLineState:s})}getVizPanel(){return this.logsPanelScene=new tt.o({}),new i.G1({direction:"column",children:"logs"===this.state.visualizationType?[new i.G1({children:[new i.vA({body:new ot({lineFilter:this.state.lineFilter}),xSizing:"fill"})]}),new i.vA({height:"calc(100vh - 220px)",body:this.logsPanelScene})]:[new i.vA({body:new ot({lineFilter:this.state.lineFilter}),xSizing:"fill"}),new i.vA({height:"calc(100vh - 220px)",body:new Xe({})})]})}constructor(e){var t,n;super((t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ct(e,t,n[t])}))}return e}({},e),n=null!=(n={visualizationType:(0,nt.k5)(),displayedFields:[]})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)),ct(this,"_urlSync",new i.So(this,{keys:["urlColumns","selectedLine","visualizationType","displayedFields","tableLogLineState"]})),ct(this,"logsPanelScene",void 0),ct(this,"clearDisplayedFields",(()=>{this.setState({displayedFields:[]}),this.logsPanelScene&&this.logsPanelScene.clearDisplayedFields()})),ct(this,"setLogsVizOption",((e={})=>{this.logsPanelScene&&this.logsPanelScene.setLogsVizOption(e)})),ct(this,"updateLogsPanel",(()=>{if(this.setState({panel:this.getVizPanel()}),this.state.panel){const e=i.jh.findDescendents(this.state.panel,ot);if(e.length){const t=e[0];this._subs.add(t.subscribeToState(((e,t)=>{e.lineFilter!==t.lineFilter&&this.setState({lineFilter:e.lineFilter})})))}}})),ct(this,"setVisualizationType",(e=>{this.setState({visualizationType:e}),(0,et.EE)(et.NO.service_details,et.ir.service_details.logs_visualization_toggle,{visualisationType:e}),(0,nt.o5)(e)})),this.addActivationHandler(this.onActivate.bind(this))}}ct(ut,"Component",(({model:e})=>{const{panel:t}=e.useState();if(t)return a().createElement("div",{className:dt.panelWrapper},a().createElement(t.Component,{model:t}))}));const dt={panelWrapper:(0,s.css)({'section > div[class$="panel-content"]':(0,s.css)({contain:"none",overflow:"auto"})})}},9153:(e,t,n)=>{n.d(t,{o:()=>T});var r=n(2672),a=n(7781),i=n(227),s=n(5959),l=n.n(s),o=n(1254),c=n(2007),u=n(558),d=n(7097),p=n(3143),g=n(2718),h=n(4750),v=n(8315);const m=({onClick:e})=>{const[t,n]=(0,s.useState)(!1);(0,s.useEffect)((()=>{let e;return t&&(e=setTimeout((()=>{n(!1)}),2e3)),()=>{clearTimeout(e)}}),[t]);const r=(0,s.useCallback)(((t,r)=>{e(t,r),n(!0)}),[e]);return l().createElement(c.IconButton,{"aria-label":t?"Copied":"Copy link to log line",tooltip:t?"Copied":"Copy link to log line",tooltipPlacement:"top",variant:t?"primary":"secondary",size:"md",name:t?"check":"share-alt",onClick:r})};var f=n(9254),b=n(3690),y=n(7085),S=n(2254),w=n(4793),O=n(8531),x=n(4011),E=n(2871),C=n(9829),k=n(4482);const F=({clearFilters:e,error:t})=>l().createElement(k.R,null,l().createElement("div",null,l().createElement("p",null,l().createElement("strong",null,"No logs found.")),l().createElement("p",null,function(e){return e.includes("parse error")?"Logs could not be retrieved due to invalid filter parameters. Please review your filters and try again.":"Logs could not be retrieved. Please review your filters or try a different time range."}(t)),l().createElement(c.Button,{variant:"secondary",onClick:e},"Clear filters")));var L=n(1863);function P(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){P(e,t,n[t])}))}return e}function _(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class T extends r.Bs{setStateFromUrl(){const e=new URLSearchParams(O.locationService.getLocation().search);this.updateFromUrl({sortOrder:e.get("sortOrder"),wrapLogMessage:e.get("wrapLogMessage")})}getUrlState(){return{sortOrder:JSON.stringify(this.state.sortOrder),wrapLogMessage:JSON.stringify(this.state.wrapLogMessage)}}updateFromUrl(e){const t={};try{if("string"==typeof e.sortOrder&&e.sortOrder){const n=(0,x.FH)(JSON.parse(e.sortOrder));n&&(t.sortOrder=n,this.setLogsVizOption({sortOrder:n}))}if("string"==typeof e.wrapLogMessage&&e.wrapLogMessage){const n=JSON.parse(e.wrapLogMessage);"boolean"==typeof n&&(t.wrapLogMessage=n,this.setLogsVizOption({wrapLogMessage:n}),this.setLogsVizOption({prettifyLogMessage:n}))}}catch(e){E.v.error(e,{msg:"LogOptionsScene: updateFromUrl unexpected error"})}Object.keys(t).length&&this.setState(j({},t))}onActivate(){this.setStateFromUrl(),this.state.body||this.setState({body:this.getLogsPanel({wrapLogMessage:this.state.wrapLogMessage,prettifyLogMessage:this.state.wrapLogMessage,sortOrder:this.state.sortOrder})});const e=r.jh.getAncestor(this,S.Mn);this._subs.add(e.subscribeToState(((e,t)=>{var n,r,i;(null===(r=e.$data)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.state)===a.LoadingState.Error?this.handleLogsError(null===(i=e.$data)||void 0===i?void 0:i.state.data):this.state.error&&this.clearLogsError(),e.logsCount!==t.logsCount&&(this.state.body?this.state.body.setState({title:this.getTitle(e.logsCount)}):this.setState({body:this.getLogsPanel({wrapLogMessage:this.state.wrapLogMessage,prettifyLogMessage:this.state.wrapLogMessage,sortOrder:this.state.sortOrder})}))})))}handleLogsError(e){var t,n,a;const s=null!==(a=this.state.logsVolumeCollapsedByError)&&void 0!==a?a:!(0,i.Rf)("collapsed"),l=(null===(t=e.errors)||void 0===t?void 0:t.length)?e.errors[0].message:null===(n=e.error)||void 0===n?void 0:n.message;var o;(this.setState({error:l,logsVolumeCollapsedByError:s}),s)&&(null===(o=r.jh.findByKeyAndType(this,b.b,b._).state.panel)||void 0===o||o.setState({collapsed:!0}))}clearLogsError(){var e;this.state.logsVolumeCollapsedByError&&(null===(e=r.jh.findByKeyAndType(this,b.b,b._).state.panel)||void 0===e||e.setState({collapsed:!1}));this.setState({error:void 0,logsVolumeCollapsedByError:void 0})}setLogsVizOption(e={}){if(this.state.body){if("sortOrder"in e&&e.sortOrder!==this.state.body.state.options.sortOrder){const e=r.jh.getData(this),t=e instanceof r.dt?e:r.jh.findDescendents(e,r.dt)[0];t&&t.runQueries()}this.state.body.onOptionsChange(e)}}getParentScene(){return r.jh.getAncestor(this,o.i)}getTitle(e){var t;const n=(0,a.getValueFormat)("short"),r=void 0!==e?n(e,0):void 0;return void 0!==r?`Logs (${r.text}${null===(t=r.suffix)||void 0===t?void 0:t.trim()})`:"Logs"}getLogsPanel(e){const t=this.getParentScene(),n=t.state.visualizationType,a=r.jh.getAncestor(this,S.Mn);var s,o,c;return r.d0.logs().setTitle(this.getTitle(a.state.logsCount)).setOption("showTime",!0).setOption("onClickFilterLabel",this.handleLabelFilterClick).setOption("onClickFilterOutLabel",this.handleLabelFilterOutClick).setOption("isFilterLabelActive",this.handleIsFilterLabelActive).setOption("onClickFilterString",this.handleFilterStringClick).setOption("onClickFilterOutString",this.handleFilterOutStringClick).setOption("onClickShowField",this.onClickShowField).setOption("onClickHideField",this.onClickHideField).setOption("displayedFields",t.state.displayedFields).setOption("sortOrder",null!==(s=e.sortOrder)&&void 0!==s?s:(0,f.zQ)()).setOption("wrapLogMessage",null!==(o=e.wrapLogMessage)&&void 0!==o?o:Boolean((0,i.YM)("wrapLogMessage",!1))).setOption("prettifyLogMessage",null!==(c=e.prettifyLogMessage)&&void 0!==c?c:Boolean((0,i.YM)("wrapLogMessage",!1))).setMenu(new y.GD({investigationOptions:{type:"logs",getLabelName:()=>`Logs: ${(0,C.Mq)(a)}`}})).setOption("showLogContextToggle",!0).setOption("enableInfiniteScrolling",!0).setOption("onNewLogsReceived",this.updateVisibleRange).setOption("logRowMenuIconsAfter",[l().createElement(m,{onClick:this.handleShareLogLineClick,key:0})]).setHeaderActions(new f.ZB({visualizationType:n,onChangeVisualizationType:t.setVisualizationType})).build()}handleLabelFilter(e,t,n,r){const a=(0,d.OE)(n,e,this);(0,u.Qt)(e,t,r,this,a),(0,g.EE)(g.NO.service_details,g.ir.service_details.logs_detail_filter_applied,{filterType:a,key:e,action:r})}constructor(e){super(j({sortOrder:(0,f.zQ)(),wrapLogMessage:Boolean((0,i.YM)("wrapLogMessage",!1)),error:void 0},e)),P(this,"_urlSync",new r.So(this,{keys:["sortOrder","wrapLogMessage"]})),P(this,"onClickShowField",(e=>{const t=this.getParentScene();if(-1===t.state.displayedFields.indexOf(e)&&this.state.body){const n=[...t.state.displayedFields,e];this.setLogsVizOption({displayedFields:n}),t.setState({displayedFields:n}),(0,i.ZF)(this,t.state.displayedFields),(0,g.EE)(g.NO.service_details,g.ir.service_details.logs_toggle_displayed_field)}})),P(this,"onClickHideField",(e=>{const t=this.getParentScene();if(t.state.displayedFields.indexOf(e)>=0&&this.state.body){const n=t.state.displayedFields.filter((t=>e!==t));this.setLogsVizOption({displayedFields:n}),t.setState({displayedFields:n}),(0,i.ZF)(this,t.state.displayedFields),(0,g.EE)(g.NO.service_details,g.ir.service_details.logs_toggle_displayed_field)}})),P(this,"clearDisplayedFields",(()=>{this.state.body&&(this.setLogsVizOption({displayedFields:[]}),(0,i.ZF)(this,[]))})),P(this,"updateVisibleRange",(e=>{var t,n;const a=r.jh.getAncestor(this,S.Mn);a.setState({logsCount:e[0].length}),(null===(n=a.state.$data)||void 0===n||null===(t=n.state.data)||void 0===t?void 0:t.series)&&a.state.$data.setState(_(j({},a.state.$data.state),{data:_(j({},a.state.$data.state.data),{series:e})})),r.jh.findByKeyAndType(this,b.b,b._).updateVisibleRange(e)})),P(this,"handleShareLogLineClick",((e,t)=>{if((null==t?void 0:t.rowId)&&this.state.body){const e=this.getParentScene(),n=(0,v.Ki)(t);(0,v.Dk)((0,v.gW)("panelState",{logs:{id:t.uid,displayedFields:e.state.displayedFields}},n))}})),P(this,"handleLabelFilterClick",((e,t,n)=>{this.handleLabelFilter(e,t,n,"toggle")})),P(this,"handleLabelFilterOutClick",((e,t,n)=>{this.handleLabelFilter(e,t,n,"exclude")})),P(this,"handleIsFilterLabelActive",((e,t)=>{const n=(0,h.bY)(p.MB,this),r=(0,h.bY)(p.mB,this),a=(0,h.bY)(p._Y,this),i=(0,h.bY)(p._P,this),s=n=>n&&n.state.filters.findIndex((n=>"="===n.operator&&n.key===e&&n.value===t))>=0;return s(n)||(n=>{if(n){const r=n.state.filters.find((t=>"="===t.operator&&t.key===e));if(r)return(0,h.bu)(r,e).value===t}return!1})(r)||s(a)||s(i)})),P(this,"handleFilterOutStringClick",(e=>{const t=(0,h.Gk)(this);t&&(t.setState({filters:[...t.state.filters,{operator:w.cK.negativeMatch,value:e,key:w.ld.caseSensitive,keyLabel:t.state.filters.length.toString()}]}),(0,g.EE)(g.NO.service_details,g.ir.service_details.logs_popover_line_filter,{selectionLength:e.length}))})),P(this,"handleFilterStringClick",(e=>{const t=(0,h.Gk)(this);t&&(t.setState({filters:[...t.state.filters,{operator:w.cK.match,value:e,key:w.ld.caseSensitive,keyLabel:t.state.filters.length.toString()}]}),(0,g.EE)(g.NO.service_details,g.ir.service_details.logs_popover_line_filter,{selectionLength:e.length}))})),this.addActivationHandler(this.onActivate.bind(this))}}P(T,"Component",(({model:e})=>{const{body:t,error:n}=e.useState(),r=(0,c.useStyles2)(y.K_);return t?l().createElement("span",{className:r.panelWrapper},!n&&l().createElement(t.Component,{model:t}),n&&l().createElement(F,{error:n,clearFilters:()=>(0,L.rA)(t)})):l().createElement(c.LoadingPlaceholder,{text:"Loading..."})}))},3690:(e,t,n)=>{n.d(t,{_:()=>_,b:()=>j});var r,a,i,s=n(5959),l=n.n(s),o=n(2672),c=n(2007),u=n(5183),d=n(7918),p=n(3143),g=n(2718),h=n(1293),v=n(1383),m=n(7781),f=n(4750),b=n(833),y=n(7085),S=n(2254),w=n(9186),O=n(227),x=n(8538),E=n(8531),C=n(9829);class k extends o.Bs{}function F(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function L(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){F(e,t,n[t])}))}return e}function P(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}i=function({model:e}){const{component:t,isLoading:n}=(0,E.usePluginComponent)("grafana-adaptivelogs-app/temporary-exemptions/v1"),r=(0,f.bY)(p.MB,e),{filters:a}=r.useState(),i=a.map((({key:e,operator:t,value:n})=>({key:e,operator:t,value:n}))),s=(0,C.U4)(e);return n||!t?null:l().createElement(t,{dataSourceUid:s,streamSelector:i,contextHints:["explorelogs","logvolumepanel","headeraction"]})},(a="Component")in(r=k)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i;const j="logs-volume-panel";class _ extends o.Bs{onActivate(){if(!this.state.panel){const e=this.getVizPanel();this.setState({panel:e}),this.updateContainerHeight(e)}const e=(0,f.cR)(this),t=(0,f.ir)(this);e.subscribeToState(((e,t)=>{(0,b.B)(e.filters,t.filters)||this.setState({panel:this.getVizPanel()})})),t.subscribeToState(((e,t)=>{(0,b.B)(e.filters,t.filters)||this.setState({panel:this.getVizPanel()})}))}getTitle(e,t){var n,r,a;const i=null!==(a=null===(n=o.jh.getAncestor(this,x.P).state.ds)||void 0===n?void 0:n.maxLines)&&void 0!==a?a:d.by,s=(0,m.getValueFormat)("short"),l=void 0!==e?s(e,0):void 0;if(void 0===e&&void 0!==t&&t<i){var c;const e=s(t,0);return void 0!==e?`Log volume (${e.text}${null===(c=e.suffix)||void 0===c?void 0:c.trim()})`:"Log volume"}return void 0!==l?`Log volume (${l.text}${null===(r=l.suffix)||void 0===r?void 0:r.trim()})`:"Log volume"}getVizPanel(){var e,t;const n=o.jh.getAncestor(this,S.Mn),r=o.d0.timeseries().setTitle(this.getTitle(n.state.totalLogsCount,n.state.logsCount)).setOption("legend",{showLegend:!0,calcs:["sum"],displayMode:c.LegendDisplayMode.List}).setUnit("short").setMenu(new y.GD({investigationOptions:{labelName:"level"}})).setCollapsible(!0).setCollapsed((0,O.Rf)("collapsed")).setHeaderActions(new k({})).setData((0,u.rS)([(0,d.l)((0,h.m)(this,p.e4,!1),{legendFormat:`{{${p.e4}}}`})]));(0,u.ZC)(r);const a=r.build();return a.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(t)}),this._subs.add(a.subscribeToState(((e,t)=>{e.collapsed!==t.collapsed&&(this.updateContainerHeight(a),(0,O.RN)("collapsed",e.collapsed?"true":void 0))}))),this._subs.add(null===(e=a.state.$data)||void 0===e?void 0:e.subscribeToState((e=>{var t,r,i,s,l,o;(null===(t=e.data)||void 0===t?void 0:t.state)===m.LoadingState.Done&&((null===(i=n.state.$data)||void 0===i||null===(r=i.state.data)||void 0===r?void 0:r.state)!==m.LoadingState.Done||(null===(s=e.data.annotations)||void 0===s?void 0:s.length)?this.displayVisibleRange():this.updateVisibleRange(null===(o=n.state.$data)||void 0===o||null===(l=o.state.data)||void 0===l?void 0:l.series),(0,u.Cw)(a,e.data.series,this))}))),this._subs.add(null===(t=n.state.$data)||void 0===t?void 0:t.subscribeToState((e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===m.LoadingState.Done&&this.updateVisibleRange(e.data.series)}))),this._subs.add(n.subscribeToState(((e,t)=>{e.totalLogsCount===t.totalLogsCount&&void 0===e.logsCount||(this.state.panel?this.state.panel.setState({title:this.getTitle(e.totalLogsCount,e.logsCount)}):this.setState({panel:this.getVizPanel()}))}))),a}updateContainerHeight(e){const t=o.jh.getAncestor(e,o.G1),n=e.state.collapsed?35:Math.max(Math.round(.2*window.innerHeight),100);t.setState({minHeight:n,height:n,maxHeight:n})}updateVisibleRange(e=[]){this.updatedLogSeries=e,this.displayVisibleRange()}displayVisibleRange(){var e,t;const n=this.state.panel;if(!n||!(null===(e=n.state.$data)||void 0===e?void 0:e.state.data)||(null===(t=n.state.$data)||void 0===t?void 0:t.state.data.state)!==m.LoadingState.Done||!this.updatedLogSeries)return;const r=(0,w.z5)(this.updatedLogSeries);this.updatedLogSeries=null,n.state.$data.setState({data:P(L({},n.state.$data.state.data),{annotations:[(0,w.hy)(r.start,r.end)]})})}constructor(e){super(P(L({},e),{key:j})),F(this,"updatedLogSeries",null),F(this,"extendTimeSeriesLegendBus",(e=>{const t=(0,f.iw)(this);this._subs.add(null==t?void 0:t.subscribeToState((()=>{var e,t,n,r;const a=this.state.panel;(null==a||null===(t=a.state.$data)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.series)&&(0,u.Cw)(a,null==a||null===(r=a.state.$data)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.series,this)}))),e.onToggleSeriesVisibility=(e,t)=>{if(t===c.SeriesVisibilityChangeMode.AppendToSelection)return;const n=(0,v.PE)(e,this);(0,g.EE)(g.NO.service_details,g.ir.service_details.level_in_logs_volume_clicked,{level:e,action:n})}})),this.addActivationHandler(this.onActivate.bind(this))}}F(_,"Component",(({model:e})=>{const{panel:t}=e.useState();if(!t)return;const n=(0,c.useStyles2)(y.K_);return l().createElement("span",{className:n.panelWrapper},l().createElement(t.Component,{model:t}))}))},2254:(e,t,n)=>{n.d(t,{AA:()=>qe,DS:()=>Ge,Mn:()=>Ze,rD:()=>Qe,UO:()=>Je,nU:()=>Ye,dB:()=>Xe,TG:()=>Ue,tn:()=>Ke});var r=n(5959),a=n.n(r),i=n(7781),s=n(2672),l=n(2007),o=n(5183),c=n(7918),u=n(3143),d=n(6949),p=n(8835),g=n(833),h=n(892),v=n(2718),m=n(6089),f=n(1254),b=n(1220),y=n(8810),S=n(4105),w=n(9570),O=n(8538),x=n(2871);function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function C(e){var t,n;const{indexScene:r,pattern:a,type:i}=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){E(e,t,n[t])}))}return e}({},e),l=s.jh.getAncestor(r,O.P);if(!l)return void x.v.warn("logs exploration scene not found");const{patterns:o=[]}=l.state,c=o.filter((e=>e.pattern!==a));var u;const d=null!==(u=null===(t=c.filter((e=>"include"===e.type)))||void 0===t?void 0:t.length)&&void 0!==u?u:0;var p;const g=null!==(p=null===(n=c.filter((e=>"exclude"===e.type)))||void 0===n?void 0:n.length)&&void 0!==p?p:0;(0,v.EE)(v.NO.service_details,v.ir.service_details.pattern_selected,{type:i,includePatternsLength:d+("include"===i?1:0),excludePatternsLength:g+("exclude"===i?1:0)}),"undo"===i?l.setState({patterns:c}):l.setState({patterns:[...c,{pattern:a,type:i}]})}var k=n(5218),F=n(8531),L=n(9829);const P=e=>({logsStatsRow:(0,m.css)({margin:`${e.spacing(1.15)}px 0`}),logsStatsRowActive:(0,m.css)({color:e.colors.primary.text,position:"relative"}),logsStatsRowLabel:(0,m.css)({display:"flex",marginBottom:"1px"}),logsStatsRowValue:(0,m.css)({flex:1,textOverflow:"ellipsis",overflow:"hidden"}),logsStatsRowCount:(0,m.css)({textAlign:"right",marginLeft:e.spacing(.75)}),logsStatsRowPercent:(0,m.css)({textAlign:"right",marginLeft:e.spacing(.75),width:e.spacing(4.5)}),logsStatsRowBar:(0,m.css)({height:e.spacing(.5),overflow:"hidden",background:e.colors.text.disabled}),logsStatsRowInnerBar:(0,m.css)({height:e.spacing(.5),overflow:"hidden",background:e.colors.primary.main})}),j=({active:e,count:t,proportion:n,value:r})=>{const i=(0,l.useStyles2)(P),s=`${Math.round(100*n)}%`,o={width:s};return a().createElement("div",{className:e?`${i.logsStatsRow} ${i.logsStatsRowActive}`:i.logsStatsRow},a().createElement("div",{className:i.logsStatsRowLabel},a().createElement("div",{className:i.logsStatsRowValue,title:r},r),a().createElement("div",{className:i.logsStatsRowCount},t),a().createElement("div",{className:i.logsStatsRowPercent},s)),a().createElement("div",{className:i.logsStatsRowBar},a().createElement("div",{className:i.logsStatsRowInnerBar,style:o})))};function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const T=e=>({logsStats:(0,m.css)({background:"inherit",color:e.colors.text.primary,wordBreak:"break-all",width:"fit-content",maxHeight:"40vh",overflowY:"auto",marginTop:e.spacing(1)}),logsStatsHeader:(0,m.css)({borderBottom:`1px solid ${e.colors.border.medium}`,display:"flex"}),logsStatsTitle:(0,m.css)({fontWeight:e.typography.fontWeightMedium,paddingRight:e.spacing(2),display:"inline-block",whiteSpace:"nowrap",textOverflow:"ellipsis",flexGrow:1}),logsStatsClose:(0,m.css)({cursor:"pointer"}),logsStatsBody:(0,m.css)({padding:"5px 0px"})}),D=e=>{const t=(0,l.useStyles2)(T),{stats:n,value:r}=e,i=n.slice(0,10);let s=i.find((e=>e.value===r)),o=n.slice(10);!s&&(s=o.find((e=>e.value===r)),o=o.filter((e=>e.value!==r)));const c=o.reduce(((e,t)=>e+t.count),0),u=i.reduce(((e,t)=>e+t.count),0)+c;let d=[...i];return c>0&&d.push({value:"Other",count:c,proportion:c/u}),d.sort(((e,t)=>t.count-e.count)),a().createElement("div",{className:t.logsStats},a().createElement("div",{className:t.logsStatsHeader},a().createElement("div",{className:t.logsStatsTitle},"From a sample of ",u," rows found")),a().createElement("div",{className:t.logsStatsBody},d.map((e=>{return a().createElement(j,(t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){_(e,t,n[t])}))}return e}({key:e.value},e),n=null!=(n={active:e.value===r})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t));var t,n}))))};var N=n(4750);function I(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}const $=({exploration:e,pattern:t,maxLines:n})=>{const o=function(e){const t=[];let n=e.indexOf("<_>");for(;-1!==n;)t.push(n),n=e.indexOf("<_>",n+1);return t}(t),[u,d]=(0,r.useState)(void 0),[p,g]=(0,r.useState)(!1),h=(0,l.useStyles2)(A),m=(0,r.useRef)(null),f=(0,r.useRef)(null),b=function(){var r,a=(r=function*(){(0,v.EE)(v.NO.service_details,v.ir.service_details.pattern_field_clicked);const r=function(e,t,n){let r=1;const a=e.replace(/<_>/g,(()=>`<field_${r++}>`));return`{${n.state.filterExpression}} |> \`${e}\` | pattern \`${a}\` | keep ${t.map(((e,t)=>`field_${t+1}`)).join(" ,")} | line_format ""`}(t,o,(0,N.cR)(e)),a=yield(0,L.hJ)(e),l=s.jh.getTimeRange(e).state.value;u&&r===m.current&&l===f.current||(m.current=r,f.current=l,null==a||a.query({requestId:"1",interval:"",intervalMs:0,scopedVars:{},range:l,targets:[(0,c.l)(r,{maxLines:n})],timezone:"",app:"",startTime:0}).forEach((e=>{var t,r;e.state!==i.LoadingState.Done||(null===(t=e.errors)||void 0===t?void 0:t.length)?(e.state===i.LoadingState.Error||(null===(r=e.errors)||void 0===r?void 0:r.length))&&(d(void 0),g(!0)):(d(function(e,t,n){const r=new Map;e.data[0].fields[0].values.toArray().forEach((e=>{Object.keys(e).forEach((t=>{var n,a;r.has(t)||r.set(t,new Map),null===(a=r.get(t))||void 0===a||a.set(e[t],((null===(n=r.get(t))||void 0===n?void 0:n.get(e[t]))||0)+1)}))}));const a=[];for(let e=0;e<=t;e++){var i;const t=[];null===(i=r.get(`field_${e+1}`))||void 0===i||i.forEach(((e,r)=>{t.push({value:r,count:e,proportion:e/n})})),t.sort(((e,t)=>t.count-e.count)),a.push(t)}return a}(e,o.length,n)),g(!1))})))},function(){var e=this,t=arguments;return new Promise((function(n,a){var i=r.apply(e,t);function s(e){I(i,n,a,s,l,"next",e)}function l(e){I(i,n,a,s,l,"throw",e)}s(void 0)}))});return function(){return a.apply(this,arguments)}}(),y=(0,r.useMemo)((()=>t.split("<_>")),[t]);return a().createElement("div",null,y.map(((e,t)=>a().createElement("span",{key:t},e,t!==o.length&&a().createElement(l.Toggletip,{onOpen:b,content:a().createElement(a().Fragment,null,u&&u[t].length>0&&a().createElement(D,{stats:u[t],value:""}),u&&0===u[t].length&&a().createElement("div",null,"No available stats for this field in the current timestamp."),!u&&p&&a().createElement("div",null,"Could not load stats for this pattern."),!u&&!p&&a().createElement("div",{style:{padding:"10px"}},a().createElement(l.Spinner,{size:"xl"})))},a().createElement("span",{className:h.pattern},"<_>"))))))};function A(e){return{pattern:(0,m.css)({cursor:"pointer",backgroundColor:e.colors.emphasize(e.colors.background.primary,.1),margin:"0 2px","&:hover":{backgroundColor:e.colors.emphasize(e.colors.background.primary,.2)}})}}var M=n(541);function B(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class R extends s.Bs{onActivate(){if(this.state.body)return;const e=(0,c.l)(u.SA);this.replacePatternsInQuery(e);const t=(0,o.rS)([e]);t.getResultsStream().subscribe((e=>{this.onQueryWithFiltersResult(e)})),this.setState({body:new s.G1({direction:"column",children:[new s.vA({body:void 0,width:"100%",height:0}),new s.vA({height:300,width:"100%",body:s.d0.logs().setHoverHeader(!0).setOption("showLogContextToggle",!0).setOption("showTime",!0).setData(t).build()})]})})}replacePatternsInQuery(e){const t={pattern:this.state.pattern,type:"include"},n=(0,M.M)([t]);e.expr=e.expr.replace(u.sC,n)}removePatternFromFilterExclusion(){const e=s.jh.getAncestor(this,z);var t;const n=null!==(t=e.state.patternsNotMatchingFilters)&&void 0!==t?t:[],r=n.findIndex((e=>e===this.state.pattern));-1!==r&&(n.splice(r,1),e.setState({patternsNotMatchingFilters:n}))}setWarningMessage(e){const t=this.getNoticeFlexItem(),n=this.getVizFlexItem();return t instanceof s.vA&&t.setState({isHidden:!1,height:"auto",body:new s.dM({reactNode:e})}),n}getNoticeFlexItem(){const e=this.getFlexItemChildren();return null==e?void 0:e[0]}getVizFlexItem(){const e=this.getFlexItemChildren();return null==e?void 0:e[1]}getFlexItemChildren(){var e;return null===(e=this.state.body)||void 0===e?void 0:e.state.children}excludeThisPatternFromFiltering(){const e=s.jh.getAncestor(this,z);var t;const n=null!==(t=e.state.patternsNotMatchingFilters)&&void 0!==t?t:[];e.setState({patternsNotMatchingFilters:[...n,this.state.pattern]})}static Component({model:e}){const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):null}constructor(e){super(e),B(this,"clearFilters",(()=>{const e=(0,N.ir)(this),t=(0,N.Gk)(this),n=(0,N.iw)(this);if(e.setState({filters:[]}),n.setState({filters:[]}),t.state.filters.length){t.setState({filters:[]});const e=this.getNoticeFlexItem();null==e||e.setState({isHidden:!0}),this.removePatternFromFilterExclusion()}})),B(this,"onQueryError",(e=>{if(e.data.state===i.LoadingState.Done&&(0===e.data.series.length||e.data.series.every((e=>0===e.length)))||e.data.state===i.LoadingState.Error){let t;try{t={pattern:this.state.pattern,traceIds:JSON.stringify(e.data.traceIds),request:JSON.stringify(e.data.request),msg:"onQueryError"}}catch(e){t={pattern:this.state.pattern,msg:"Failed to encode context"}}x.v.error(new Error("Pattern sample query returns no results"),t),this.setWarningMessage(a().createElement(l.Alert,{severity:"error",title:""},"This pattern returns no logs."));const n=this.getVizFlexItem();n instanceof s.vA&&n.setState({isHidden:!0})}})),B(this,"onQueryWithFiltersResult",(e=>{const t=(0,c.l)(u.pT);this.replacePatternsInQuery(t);const n=(0,o.rS)([t]);if(n.getResultsStream().subscribe(this.onQueryError),e.data.state===i.LoadingState.Done&&(0===e.data.series.length||e.data.series.every((e=>0===e.length)))){const e=this.getNoticeFlexItem(),t=this.getVizFlexItem();if(e instanceof s.vA&&e.setState({isHidden:!1,height:"auto",body:new s.dM({reactNode:a().createElement(l.Alert,{severity:"warning",title:""},"The logs returned by this pattern do not match the current query filters.",a().createElement(l.Button,{className:S.ZI.button,onClick:()=>this.clearFilters()},"Clear filters"))})}),t instanceof s.vA){const e=t.state.body;e instanceof s.Eb&&(null==e||e.setState({$data:n}))}this.excludeThisPatternFromFiltering()}e.data.state===i.LoadingState.Error&&this.onQueryError(e)})),this.addActivationHandler(this.onActivate.bind(this))}}function V({tableViz:e,row:t}){const{expandedRows:n}=e.useState(),i=null==n?void 0:n.find((e=>e.state.key===t.pattern));return(0,r.useEffect)((()=>{if(!i){const a=(r=t.pattern,new R({pattern:r,key:r}));var n;e.setState({expandedRows:[...null!==(n=e.state.expandedRows)&&void 0!==n?n:[],a]})}var r}),[t,e,i]),i?a().createElement(i.Component,{model:i}):null}const W=[""," K"," Mil"," Bil"," Tri"," Quadr"," Quint"," Sext"," Sept"];class z extends s.Bs{onActivate(){var e;const t=null===(e=s.jh.getAncestor(this,O.P).state.ds)||void 0===e?void 0:e.maxLines;this.setState({maxLines:t})}buildColumns(e,t,n,r,o){const c=J(n),u=s.jh.getTimeRange(this).state.value;return[{id:"volume-samples",header:"",cell:e=>{const t={timeRange:u,series:[e.cell.row.original.dataFrame],state:i.LoadingState.Done},n=new s.Zv({data:t}),r=s.d0.timeseries().setData(n).setHoverHeader(!0).setOption("tooltip",{mode:l.TooltipDisplayMode.None}).setCustomFieldConfig("hideFrom",{legend:!0,tooltip:!0}).setCustomFieldConfig("axisPlacement",l.AxisPlacement.Hidden).setDisplayMode("transparent").build();return a().createElement("div",{className:c.tableTimeSeriesWrap},a().createElement("div",{className:c.tableTimeSeries},a().createElement(r.Component,{model:r})))}},{id:"count",header:"Count",sortType:"number",cell:e=>{const t=(0,i.scaledUnits)(1e3,W)(e.cell.row.original.sum);var n,r;return a().createElement("div",{className:c.countTextWrap},a().createElement("div",null,null!==(n=t.prefix)&&void 0!==n?n:"",t.text,null!==(r=t.suffix)&&void 0!==r?r:""))}},{id:"percent",header:"%",sortType:"number",cell:t=>a().createElement("div",{className:c.countTextWrap},a().createElement("div",null,(100*t.cell.row.original.sum/e).toFixed(0),"%"))},{id:"pattern",header:"Pattern",cell:e=>a().createElement("div",{className:(0,m.cx)(U(),c.tablePatternTextDefault)},a().createElement($,{exploration:(0,L.Ti)(this),pattern:e.cell.row.original.pattern,maxLines:r}))},{id:"include",header:void 0,disableGrow:!0,cell:e=>{if(null==o?void 0:o.includes(e.cell.row.original.pattern))return;const n=null==t?void 0:t.find((t=>t.pattern===e.cell.row.original.pattern)),r="include"===(null==n?void 0:n.type),i="exclude"===(null==n?void 0:n.type);return a().createElement(k.F,{isExcluded:i,isIncluded:r,onInclude:()=>e.cell.row.original.includeLink(),onExclude:()=>e.cell.row.original.excludeLink(),onClear:()=>e.cell.row.original.undoLink(),buttonFill:"outline"})}}]}buildTableData(e,t){const n=s.jh.getAncestor(this,O.P);return e.filter((e=>!t.size||t.has(e.pattern))).map((e=>({dataFrame:e.dataFrame,pattern:e.pattern,sum:e.sum,includeLink:()=>C({pattern:e.pattern,type:"include",indexScene:n}),excludeLink:()=>C({pattern:e.pattern,type:"exclude",indexScene:n}),undoLink:()=>C({pattern:e.pattern,type:"undo",indexScene:n})})))}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}var H,G,q;q=function({model:e}){const t=s.jh.getAncestor(e,O.P),{patterns:n}=t.useState(),r=(0,l.useTheme2)(),i=Q(r),o=s.jh.getAncestor(e,te),{legendSyncPatterns:u}=o.useState(),{patternFrames:d,patternsNotMatchingFilters:p}=e.useState(),g=null!=d?d:[],h=g.reduce(((e,t)=>e+t.sum),0),v=e.buildTableData(g,u);var m;const f=e.buildColumns(h,n,r,null!==(m=e.state.maxLines)&&void 0!==m?m:c.by,p);return a().createElement("div",{"data-testid":b.b.patterns.tableWrapper,className:i.tableWrap},a().createElement(l.InteractiveTable,{columns:f,data:v,getRowId:e=>e.pattern,renderExpandedRow:t=>a().createElement(V,{tableViz:e,row:t})}))},(G="Component")in(H=z)?Object.defineProperty(H,G,{value:q,enumerable:!0,configurable:!0,writable:!0}):H[G]=q;const K=F.config.theme2,U=()=>(0,m.css)({minWidth:"200px",fontFamily:K.typography.fontFamilyMonospace,overflow:"hidden",overflowWrap:"break-word"}),Q=e=>({link:(0,m.css)({textDecoration:"underline"}),tableWrap:(0,m.css)({"> div":{height:"calc(100vh - 450px)",minHeight:"470px"},th:{top:0,position:"sticky",backgroundColor:e.colors.background.canvas,zIndex:e.zIndex.navbarFixed}})}),J=e=>({tablePatternTextDefault:(0,m.css)({fontFamily:e.typography.fontFamilyMonospace,minWidth:"200px",maxWidth:"100%",overflow:"hidden",overflowWrap:"break-word",fontSize:e.typography.bodySmall.fontSize,wordBreak:"break-word"}),countTextWrap:(0,m.css)({textAlign:"right",fontSize:e.typography.bodySmall.fontSize}),tableTimeSeriesWrap:(0,m.css)({width:"230px",pointerEvents:"none"}),tableTimeSeries:(0,m.css)({height:"30px",overflow:"hidden"})});function Y(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}function X(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){Y(i,r,a,s,l,"next",e)}function l(e){Y(i,r,a,s,l,"throw",e)}s(void 0)}))}}function Z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const ee=F.config.theme2.visualization.palette;class te extends s.Bs{onActivate(){this.updateBody(),this._subs.add(s.jh.getAncestor(this,Ze).subscribeToState(((e,t)=>{var n,r,a,i,l,o;const c=null==e||null===(a=e.$patternsData)||void 0===a||null===(r=a.state)||void 0===r||null===(n=r.data)||void 0===n?void 0:n.series,u=null==t||null===(o=t.$patternsData)||void 0===o||null===(l=o.state)||void 0===l||null===(i=l.data)||void 0===i?void 0:i.series;if(!(0,g.B)(c,u)){const e=s.jh.getAncestor(this,ge);this.updatePatterns(e.state.patternFrames),e.setState({filteredPatterns:void 0})}}))),this._subs.add(s.jh.getAncestor(this,ge).subscribeToState(((e,t)=>{const n=s.jh.getAncestor(this,ge);e.filteredPatterns&&!(0,g.B)(e.filteredPatterns,t.filteredPatterns)?this.updatePatterns(n.state.filteredPatterns):n.state.patternFilter||this.updatePatterns(n.state.patternFrames)})))}updatePatterns(e=[]){var t=this;return X((function*(){var n;null===(n=t.state.body)||void 0===n||n.forEachChild((n=>{n instanceof s.Eb&&n.setState({$data:t.getTimeseriesDataNode(e)}),n instanceof z&&n.setState({patternFrames:e})}))}))()}updateBody(){var e=this;return X((function*(){var t,n;const r=s.jh.getAncestor(e,ge).state.patternFrames;(null===(n=s.jh.getAncestor(e,Ze).state.$patternsData)||void 0===n||null===(t=n.state.data)||void 0===t?void 0:t.series)&&r?e.setState({body:e.getSingleViewLayout(),legendSyncPatterns:new Set,loading:!1}):x.v.warn("Failed to update PatternsFrameScene body")}))()}extendTimeSeriesLegendBus(e,t){const n=t.onToggleSeriesVisibility;t.onToggleSeriesVisibility=(t,r)=>{var a;null==n||n(t,r);const i=null===(a=e.state.fieldConfig.overrides)||void 0===a?void 0:a[0],s=null==i?void 0:i.matcher.options.names,l=new Set;s&&s.forEach(l.add,l),this.setState({legendSyncPatterns:l})}}getSingleViewLayout(){const e=s.jh.getAncestor(this,ge).state.patternFrames;if(!e)return void x.v.warn("Failed to set getSingleViewLayout");const t=this.getTimeSeries(e);return new s.gF({templateColumns:"100%",autoRows:"200px",isLazy:!0,children:[t,new z({patternFrames:e})]})}getTimeSeries(e){const t=s.jh.getAncestor(this,O.P),n=s.d0.timeseries().setData(this.getTimeseriesDataNode(e)).setOption("legend",{asTable:!0,showLegend:!0,displayMode:l.LegendDisplayMode.Table,placement:"right",width:200}).setHoverHeader(!0).setUnit("short").setLinks([{url:"#",targetBlank:!1,onClick:e=>{C({pattern:e.origin.labels.name,type:"include",indexScene:t})},title:"Include"},{url:"#",targetBlank:!1,onClick:e=>{C({pattern:e.origin.labels.name,type:"exclude",indexScene:t})},title:"Exclude"}]).build();return n.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(e,t)}),n}getTimeseriesDataNode(e){const t=s.jh.getTimeRange(this).state.value;return new s.Zv({data:{series:e.map(((e,t)=>{const n=e.dataFrame;return n.fields[1].config.color=function(e){return{mode:"fixed",fixedColor:ee[e]}}(t),n.fields[1].name="",n})),state:i.LoadingState.Done,timeRange:t}})}constructor(e){var t,n;super((t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Z(e,t,n[t])}))}return e}({loading:!0},e),n=null!=(n={legendSyncPatterns:new Set})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)),this.addActivationHandler(this.onActivate.bind(this))}}Z(te,"Component",(({model:e})=>{var t;const{body:n,loading:r}=e.useState(),i=s.jh.getAncestor(e,Ze),{$patternsData:l}=i.useState(),o=null==l||null===(t=l.state.data)||void 0===t?void 0:t.series;return a().createElement("div",{className:ne.container},!r&&o&&o.length>0&&a().createElement(a().Fragment,null,n&&a().createElement(n.Component,{model:n})))}));const ne={container:(0,m.css)({width:"100%",".show-on-hover":{display:"none"}})};var re=n(4932),ae=n(8642);function ie(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class se extends s.Bs{onActivate(){const e=s.jh.getAncestor(this,ge);this._subs.add(e.subscribeToState(((e,t)=>{if(e.patternFilter!==t.patternFilter){const e=s.jh.getAncestor(this,ge);e.state.patternFrames&&(0,re.E)(e.state.patternFrames.map((e=>e.pattern)),e.state.patternFilter,this.onSearchResult)}}))),this._subs.add(e.subscribeToState(((e,t)=>{e.patternFilter&&!e.filteredPatterns&&e.patternFrames&&!(0,g.B)(e.filteredPatterns,t.filteredPatterns)&&(0,re.X)(e.patternFrames.map((e=>e.pattern)),e.patternFilter,this.onSearchResult)})))}setFilteredPatterns(e,t){const n=s.jh.getAncestor(this,ge),r=null!=t?t:n.state.patternFrames;if(r){const t=r.filter((t=>!(!n.state.patternFilter||!(null==r?void 0:r.length))&&e.find((e=>e===t.pattern))));n.setState({filteredPatterns:t})}}setEmptySearch(){s.jh.getAncestor(this,ge).setState({filteredPatterns:void 0})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ie(e,t,n[t])}))}return e}({},e)),ie(this,"clearSearch",(()=>{s.jh.getAncestor(this,ge).setState({patternFilter:""})})),ie(this,"handleSearchChange",(e=>{s.jh.getAncestor(this,ge).setState({patternFilter:e.target.value})})),ie(this,"onSearchResult",(e=>{const t=s.jh.getAncestor(this,ge);t.state.patternFilter?this.setFilteredPatterns(e[0]):t.state.filteredPatterns&&!t.state.patternFilter&&this.setEmptySearch()})),this.addActivationHandler(this.onActivate.bind(this))}}ie(se,"Component",(function({model:e}){const t=s.jh.getAncestor(e,ge),{patternFilter:n}=t.useState();return a().createElement(l.Field,{className:le.field},a().createElement(ae.D,{onChange:e.handleSearchChange,onClear:e.clearSearch,value:n,placeholder:"Search patterns"}))}));const le={field:(0,m.css)({label:"field",marginBottom:0}),icon:(0,m.css)({cursor:"pointer"})};var oe=n(4482);function ce(){return a().createElement(oe.R,null,a().createElement("div",null,a().createElement("p",null,a().createElement("strong",null,"Sorry, we could not detect any patterns.")),a().createElement("p",null,"Check back later or reach out to the team in the"," ",a().createElement(l.TextLink,{href:"https://slack.grafana.com/",external:!0},"Grafana Labs community Slack channel")),a().createElement("p",null,"Patterns let you detect similar log lines to include or exclude from your search.")))}function ue(){return a().createElement(oe.R,null,a().createElement("div",null,a().createElement("p",null,a().createElement("strong",null,"Patterns are only available for the most recent ",pe," hours of data.")),a().createElement("p",null,"See the"," ",a().createElement(l.TextLink,{href:"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/logs/patterns/",external:!0},"patterns docs")," ","for more info.")))}function de(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const pe=3;class ge extends s.Bs{onActivate(){var e,t;const n=s.jh.getAncestor(this,Ze);var r;this.setBody(),(null===(e=n.state.$patternsData)||void 0===e?void 0:e.state)&&this.onDataChange(null===(r=n.state.$patternsData)||void 0===r?void 0:r.state),this._subs.add(null===(t=n.state.$patternsData)||void 0===t?void 0:t.subscribeToState(this.onDataChange))}setBody(){this.setState({body:new s.G1({direction:"column",children:[new s.vA({ySizing:"content",body:new se}),new s.vA({body:new te})]})})}updatePatternFrames(e){if(!e)return;const t=this.dataFrameToPatternFrame(e);this.setState({patternFrames:t})}dataFrameToPatternFrame(e){const t=s.jh.getAncestor(this,Ze),n=s.jh.getAncestor(t,O.P).state.patterns;return e.map((e=>{var t,r;const a=null==n?void 0:n.find((t=>t.pattern===e.name)),i=null===(r=e.meta)||void 0===r||null===(t=r.custom)||void 0===t?void 0:t.sum;var s;return{dataFrame:e,pattern:null!==(s=e.name)&&void 0!==s?s:"",sum:i,status:null==a?void 0:a.type}}))}constructor(e){var t;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){de(e,t,n[t])}))}return e}({$variables:null!==(t=e.$variables)&&void 0!==t?t:new s.Pj({variables:[new s.yP({name:u.Jg,defaultToAll:!0,includeAll:!0})]}),loading:!0,patternFilter:""},e)),de(this,"onDataChange",((e,t)=>{var n,r,a,s,l;const o=null===(n=e.data)||void 0===n?void 0:n.series,c=null==t||null===(r=t.data)||void 0===r?void 0:r.series;(null===(a=e.data)||void 0===a?void 0:a.state)===i.LoadingState.Done?(this.setState({loading:!1,error:!1}),(0,g.B)(o,c)||this.updatePatternFrames(o)):(null===(s=e.data)||void 0===s?void 0:s.state)===i.LoadingState.Loading?this.setState({loading:!0,error:!1}):(null===(l=e.data)||void 0===l?void 0:l.state)===i.LoadingState.Error&&this.setState({loading:!1,error:!0})})),this.addActivationHandler(this.onActivate.bind(this))}}function he(e){return{container:(0,m.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,m.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,m.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2)}),controlsRight:(0,m.css)({flexGrow:0,display:"flex",justifyContent:"flex-end"}),controlsLeft:(0,m.css)({display:"flex",justifyContent:"flex-left",justifyItems:"left",width:"100%",flexDirection:"column"}),patternMissingText:(0,m.css)({padding:e.spacing(2)})}}de(ge,"Component",(({model:e})=>{const{body:t,loading:n,blockingMessage:r,patternFrames:o,error:c}=e.useState(),{value:u}=s.jh.getTimeRange(e).useState(),d=(0,l.useStyles2)(he),p=(0,i.dateTime)().diff(u.to,"hours")>=pe;return a().createElement("div",{className:d.container},a().createElement(w.O,{isLoading:n,blockingMessage:r},!n&&c&&a().createElement("div",{className:d.patternMissingText},a().createElement(l.Text,{textAlignment:"center",color:"primary"},a().createElement("p",null,"There are no pattern matches."),a().createElement("p",null,"Pattern matching has not been configured."),a().createElement("p",null,"Patterns let you detect similar log lines and add or exclude them from your search."),a().createElement("p",null,"To see them in action, add the following to your Loki configuration"),a().createElement("p",null,a().createElement("code",null,"--pattern-ingester.enabled=true")))),!c&&!n&&0===(null==o?void 0:o.length)&&p&&a().createElement(ue,null),!c&&!n&&0===(null==o?void 0:o.length)&&!p&&a().createElement(ce,null),!c&&!n&&o&&o.length>0&&a().createElement("div",{className:d.content},t&&a().createElement(t.Component,{model:t}))))}));var ve=n(3690),me=n(3633),fe=function(e){return e.logs="Logs",e.labels="Labels",e.fields="Fields",e.patterns="Patterns",e}({});const be=[{displayName:"Logs",value:h.G3.logs,getScene:()=>new s.G1({direction:"column",children:[new s.vA({body:new ve._({})}),new s.vA({minHeight:"470px",height:"calc(100vh - 500px)",body:new f.i({})})]}),testId:b.b.exploreServiceDetails.tabLogs},{displayName:"Labels",value:h.G3.labels,getScene:()=>new s.G1({$behaviors:[new s.Gg.K2({key:"sync",sync:me.yV.Crosshair})],children:[new s.vA({body:new y.O({})})]}),testId:b.b.exploreServiceDetails.tabLabels},{displayName:"Fields",value:h.G3.fields,getScene:e=>{return t=e,new s.G1({$behaviors:[new s.Gg.K2({key:"sync",sync:me.yV.Crosshair})],children:[new s.vA({body:new S.J6({changeFieldCount:t})})]});var t},testId:b.b.exploreServiceDetails.tabFields},{displayName:"Patterns",value:h.G3.patterns,getScene:()=>new s.G1({children:[new s.vA({body:new ge({})})]}),testId:b.b.exploreServiceDetails.tabPatterns}],ye=[{displayName:"Label",value:h._J.label,getScene:e=>function(e){return new s.G1({$behaviors:[new s.Gg.K2({key:"sync",sync:me.yV.Crosshair})],children:[new s.vA({body:new y.O({value:e})})]})}(e),testId:b.b.exploreServiceDetails.tabLabels},{displayName:"Field",value:h._J.field,getScene:e=>function(e){return new s.G1({$behaviors:[new s.Gg.K2({key:"sync",sync:me.yV.Crosshair})],children:[new s.vA({body:new S.J6({value:e})})]})}(e),testId:b.b.exploreServiceDetails.tabFields}];var Se=n(8315);function we(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}function Oe(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){we(i,r,a,s,l,"next",e)}function l(e){we(i,r,a,s,l,"throw",e)}s(void 0)}))}}function xe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ee extends s.Bs{setIsOpen(e){this.setState({isOpen:e})}onCopyLink(e,t,r){e?(Le(r||n.g.location.href),(0,F.reportInteraction)("grafana_explore_shortened_link_clicked",{isAbsoluteTime:t})):((0,Se.Dk)(void 0!==r?`${window.location.protocol}//${window.location.host}${F.config.appSubUrl}${r}`:n.g.location.href),this.state.onCopyLink&&this.state.onCopyLink(e,t,r))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){xe(e,t,n[t])}))}return e}({isOpen:!1,lastSelected:Ce},e))}}xe(Ee,"MenuActions",(({model:e})=>{const t=[{key:"normal",label:"Normal URL links",items:[{key:"copy-shortened-link",icon:"link",label:"Copy shortened URL",getUrl:()=>{},shorten:!0,absTime:!1},{key:"copy-link",icon:"link",label:"Copy URL",getUrl:()=>{},shorten:!1,absTime:!1}]},{key:"timesync",label:"Time-sync URL links (share with time range intact)",items:[{key:"copy-short-link-abs-time",icon:"clock-nine",label:"Copy absolute shortened URL",shorten:!0,getUrl:()=>Pe(void 0!==e.state.getSceneTimeRange?e.state.getSceneTimeRange():s.jh.getTimeRange(e)),absTime:!0},{key:"copy-link-abs-time",icon:"clock-nine",label:"Copy absolute URL",shorten:!1,getUrl:()=>Pe(void 0!==e.state.getSceneTimeRange?e.state.getSceneTimeRange():s.jh.getTimeRange(e)),absTime:!0}]}];return a().createElement(l.Menu,null,t.map((t=>a().createElement(l.MenuGroup,{key:t.key,label:t.label},t.items.map((t=>a().createElement(l.Menu.Item,{key:t.key,label:t.label,icon:t.icon,onClick:()=>{const n=t.getUrl();e.onCopyLink(t.shorten,t.absTime,n),e.setState({lastSelected:t})}})))))))})),xe(Ee,"Component",(({model:e})=>{const{lastSelected:t,isOpen:n}=e.useState();return a().createElement(l.ButtonGroup,null,a().createElement(l.ToolbarButton,{tooltip:t.label,icon:t.icon,variant:"canvas",narrow:!0,onClick:()=>{const n=t.getUrl();e.onCopyLink(t.shorten,t.absTime,n)},"aria-label":"Copy shortened URL"},a().createElement("span",null,"Share")),a().createElement(l.Dropdown,{overlay:a().createElement(Ee.MenuActions,{model:e}),placement:"bottom-end",onVisibleChange:e.setIsOpen.bind(e)},a().createElement(l.ToolbarButton,{narrow:!0,variant:"canvas",isOpen:n,"aria-label":"Open copy link options"})))}));const Ce={key:"copy-link",label:"Copy shortened URL",icon:"share-alt",getUrl:()=>{},shorten:!0,absTime:!1};function ke(e){let t=e.replace(`${window.location.protocol}//${window.location.host}${F.config.appSubUrl}`,"");return t.startsWith("/")?t.substring(1,t.length):t}const Fe=function(){var e=Oe((function*(e){const t=(0,F.getAppEvents)();try{return(yield(0,F.getBackendSrv)().post("/api/short-urls",{path:ke(e)})).url}catch(e){console.error("Error when creating shortened link: ",e),t.publish({type:i.AppEvents.alertError.name,payload:["Error generating shortened link"]})}}));return function(t){return e.apply(this,arguments)}}(),Le=function(){var e=Oe((function*(e){const t=(0,F.getAppEvents)(),n=yield Fe(e);n?((0,Se.Dk)(n),t.publish({type:i.AppEvents.alertSuccess.name,payload:["Shortened link copied to clipboard"]})):t.publish({type:i.AppEvents.alertError.name,payload:["Error generating shortened link"]})}));return function(t){return e.apply(this,arguments)}}(),Pe=e=>{const t=(0,i.toUtc)(e.state.value.from),n=(0,i.toUtc)(e.state.value.to),r=F.locationService.getLocation(),a=i.urlUtil.getUrlSearchParams();return a.from=t.toISOString(),a.to=n.toISOString(),i.urlUtil.renderUrl(r.pathname,a)};class je extends s.Bs{onActivate(){const e=s.jh.getAncestor(this,O.P).state.ds;void 0!==(null==e?void 0:e.maxLines)&&this.setState({maxLines:e.maxLines}),this.state.shareButtonScene||this.setState({shareButtonScene:new Ee({})})}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(je,"Component",(({model:e})=>{const t=(0,l.useStyles2)(Te);let n=(0,h.FT)(),r=!1;if(!Object.values(h.G3).includes(n)){const e=(0,h.er)();r=!0,e===h._J.field&&(n=h.G3.fields),e===h._J.label&&(n=h.G3.labels)}const o=s.jh.getAncestor(e,Ze),u=o.useState(),{loading:d,$data:g,logsCount:f,totalLogsCount:b}=u,y=function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(u,["loading","$data","logsCount","totalLogsCount"]),{maxLines:S}=e.useState(),w=y.loadingStates;return a().createElement(l.Box,{paddingY:0},a().createElement("div",{className:t.actions},a().createElement(l.Stack,{gap:1},F.config.featureToggles.appSidecar&&a().createElement(De,{serviceScene:o}),e.state.shareButtonScene&&a().createElement(e.state.shareButtonScene.Component,{model:e.state.shareButtonScene}))),a().createElement(l.TabsBar,null,be.map(((t,o)=>a().createElement(l.Tab,{"data-testid":t.testId,key:o,label:t.displayName,active:n===t.value,counter:w[t.displayName]?void 0:_e(t,y),suffix:t.displayName===fe.logs?({className:e})=>function(e,t,n,r){const s=(0,l.useStyles2)(Ne),o=(0,i.getValueFormat)("short");if(void 0===t&&void 0!==n&&n<r){var c;const t=o(n,0);return a().createElement("span",{className:(0,m.cx)(e,s.logsCountStyles)},t.text,null===(c=t.suffix)||void 0===c?void 0:c.trim())}if(void 0!==t){var u;const n=o(t,0);return a().createElement("span",{className:(0,m.cx)(e,s.logsCountStyles)},n.text,null===(u=n.suffix)||void 0===u?void 0:u.trim())}return a().createElement("span",{className:(0,m.cx)(e,s.emptyCountStyles)})}(e,b,f,null!=S?S:c.by):void 0,icon:w[t.displayName]?"spinner":void 0,onChangeTab:()=>{if(t.value&&t.value!==n||r){(0,v.EE)(v.NO.service_details,v.ir.service_details.action_view_changed,{newActionView:t.value,previousActionView:n});const r=s.jh.getAncestor(e,Ze);(0,p.Vt)(t.value,r)}}})))))}));const _e=(e,t)=>{switch(e.value){case"fields":return t.fieldsCount;case"patterns":return t.patternsCount;case"labels":return t.labelsCount;default:return}};function Te(e){return{actions:(0,m.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,zIndex:2}})}}function De(e){const[t,n]=(0,r.useState)((0,N.cR)(e.serviceScene).state.filters);(0,r.useEffect)((()=>{const t=(0,N.cR)(e.serviceScene).subscribeToState((e=>{n(e.filters)}));return()=>{t.unsubscribe()}}),[e.serviceScene]);const[i,s]=(0,r.useState)(!1),o=(0,F.usePluginLinks)({extensionPointId:"grafana-lokiexplore-app/toolbar-open-related/v1",limitPerPlugin:3,context:{filters:t}});if(o.isLoading||0===o.links.length)return null;if(1===o.links.length){const e=o.links[0];return a().createElement("div",null,a().createElement(l.ToolbarButton,{variant:"canvas",key:e.id,onClick:t=>{var n;return null===(n=e.onClick)||void 0===n?void 0:n.call(e,t)},icon:e.icon},"Related ",e.title))}const c=a().createElement(l.Menu,null,o.links.map((e=>a().createElement(l.Menu.Item,{ariaLabel:e.title,icon:(null==e?void 0:e.icon)||"plug",key:e.id,label:e.title,onClick:t=>{var n;null===(n=e.onClick)||void 0===n||n.call(e,t)}}))));return a().createElement(l.Dropdown,{onVisibleChange:s,placement:"bottom-start",overlay:c},a().createElement(l.ToolbarButton,{"aria-label":"Open related",variant:"canvas",isOpen:i},"Open related"))}function Ne(e){return{emptyCountStyles:(0,m.css)({display:"inline-block",fontSize:e.typography.bodySmall.fontSize,minWidth:"1em",marginLeft:e.spacing(1),padding:e.spacing(.25,1)}),logsCountStyles:(0,m.css)({fontSize:e.typography.bodySmall.fontSize,label:"counter",marginLeft:e.spacing(1),borderRadius:e.spacing(3),backgroundColor:e.colors.action.hover,padding:e.spacing(.25,1),color:e.colors.text.secondary,fontWeight:e.typography.fontWeightMedium})}}var Ie=n(7608),$e=n(6059),Ae=n(4793);function Me(e,t){const n=["^","$",".","*","+","?","(",")","[","]","{","}","|"];return t||n.push("\\"),e.split("").filter(((e,t,r)=>{const a=r[t+1],i=n.includes(a);return!("\\"===e&&i)})).join("")}var Be=n(5435),Re=n(3626),Ve=n(5111);function We(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ze(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){We(e,t,n[t])}))}return e}function He(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Ge="logsPanelQuery",qe="logsCountQuery";function Ke(e){return null==e?void 0:e.series.find((e=>e.refId===Ge))}function Ue(e){var t,n,r;return null===(r=s.jh.getAncestor(e,Ze).state.$detectedLabelsData)||void 0===r||null===(n=r.state.data)||void 0===n||null===(t=n.series)||void 0===t?void 0:t[0]}function Qe(e){var t;const n=s.jh.getAncestor(e,Ze);return Je(null===(t=n.state.$detectedFieldsData)||void 0===t?void 0:t.state)}const Je=e=>{var t,n;return null==e||null===(n=e.data)||void 0===n||null===(t=n.series)||void 0===t?void 0:t[0]},Ye=e=>{var t,n,r,a;return null===(a=e.data)||void 0===a||null===(r=a.series)||void 0===r||null===(n=r[0])||void 0===n||null===(t=n.fields)||void 0===t?void 0:t[0]},Xe=e=>{var t,n,r,a;return null===(a=e.data)||void 0===a||null===(r=a.series)||void 0===r||null===(n=r[0])||void 0===n||null===(t=n.fields)||void 0===t?void 0:t[2]};class Ze extends s.Bs{setSubscribeToLabelsVariable(){const e=(0,N.cR)(this);0!==e.state.filters.length?this._subs.add(e.subscribeToState(((e,t)=>{0===e.filters.length&&this.redirectToStart();let{labelName:n,labelValue:r,breakdownLabel:a}=(0,h.W6)();n===u.ky&&(n=u.OX);const i=s.jh.getAncestor(this,O.P),l=i.state.routeMatch;if(e.filters.some((e=>e.key===n&&(0,Ve.BG)(e.operator)&&(0,Ie.uu)(e.value)===r))){if(!(0,g.B)(e.filters,t.filters)){var o,c,d,v;null===(o=this.state.$patternsData)||void 0===o||o.runQueries(),null===(c=this.state.$detectedLabelsData)||void 0===c||c.runQueries(),null===(d=this.state.$detectedFieldsData)||void 0===d||d.runQueries(),null===(v=this.state.$logsCount)||void 0===v||v.runQueries()}}else{const t=e.filters.find((e=>(0,Ve.BG)(e.operator)&&e.value!==u.ZO));if(t){const e=(0,u.zE)(t.value)?(0,Ie.uu)((0,u.Dx)(t.value)):(0,Ie.uu)(t.value);var m,f,b;i.setState({routeMatch:He(ze({},l),{params:He(ze({},null==l?void 0:l.params),{labelName:t.key===u.OX?u.ky:t.key,labelValue:e.split("|")[0]}),url:null!==(m=null==l?void 0:l.url)&&void 0!==m?m:"",path:null!==(f=null==l?void 0:l.path)&&void 0!==f?f:"",isExact:null===(b=null==l?void 0:l.isExact)||void 0===b||b})}),this.resetTabCount(),a?(0,p.fg)((0,h.er)(),a,this):(0,p.Vt)((0,h.FT)(),this)}else this.redirectToStart()}}))):this.redirectToStart()}redirectToStart(){this.setState({$data:void 0,$logsCount:void 0,body:void 0,$patternsData:void 0,$detectedLabelsData:void 0,$detectedFieldsData:void 0,patternsCount:void 0,labelsCount:void 0,fieldsCount:void 0,logsCount:void 0,totalLogsCount:void 0}),(0,d.JO)().setServiceSceneState(this.state),this._subs.unsubscribe(),this.clearAdHocVariables(),(0,p.Ns)()}showVariables(){s.jh.findByKeyAndType(this,Re.k,Re.q).setState({visible:!0}),(0,N.YS)(this).setState({hide:Be.zL.dontHide})}getMetadata(){const e=(0,d.JO)().getServiceSceneState();e&&this.setState(ze({},e))}onActivate(){s.jh.findByKeyAndType(this,O.y,$e.H).setState({hidden:!0}),this.showVariables(),this.getMetadata(),this.resetBodyAndData(),this.setBreakdownView(),this.runQueries(),this._subs.add(this.subscribeToPatternsQuery()),this._subs.add(this.subscribeToDetectedLabelsQuery()),this._subs.add(this.subscribeToDetectedFieldsQuery((0,h.FT)()!==h.G3.fields)),this._subs.add(this.subscribeToLogsQuery()),this._subs.add(this.subscribeToLogsCountQuery()),this.setSubscribeToLabelsVariable(),this._subs.add(this.subscribeToFieldsVariable()),this._subs.add(this.subscribeToMetadataVariable()),this._subs.add(this.subscribeToLevelsVariable()),this._subs.add(this.subscribeToDataSourceVariable()),this._subs.add(this.subscribeToPatternsVariable()),this._subs.add(this.subscribeToLineFiltersVariable()),this._subs.add(this.subscribeToTimeRange()),function(e){const t=i.urlUtil.getUrlSearchParams(),n=t["var-lineFilter"];if(!Array.isArray(n)||!n.length)return;const r=n[0];if("string"!=typeof r||!r)return;const a=s.jh.getAncestor(e,O.P),l=(0,N.Gk)(e),o=null==r?void 0:r.match(/\|=.`(.+?)`/);var c,u;o&&2===o.length&&(null===(u=a.state.body)||void 0===u||null===(c=u.state.lineFilterRenderer)||void 0===c||c.addActivationHandler((()=>{l.setState({filters:[{key:Ae.ld.caseSensitive,operator:Ae.cK.match,value:Me(o[1],!0),keyLabel:"0"}]})})));const d=null==r?void 0:r.match(/`\(\?i\)(.+)`/);var p,g;d&&2===d.length&&(null===(g=a.state.body)||void 0===g||null===(p=g.state.lineFilterRenderer)||void 0===p||p.addActivationHandler((()=>{l.updateFilters([{key:Ae.ld.caseInsensitive,operator:Ae.cK.match,value:Me(d[1],!1),keyLabel:"0"}])}))),delete t["var-lineFilter"],F.locationService.replace(i.urlUtil.renderUrl(location.pathname,t))}(this)}subscribeToPatternsVariable(){return(0,N.Ku)(this).subscribeToState(((e,t)=>{var n,r;e.value!==t.value&&(null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries())}))}subscribeToLineFiltersVariable(){return(0,N.Gk)(this).subscribeToEvent(s.oh,(()=>{var e,t;null===(e=this.state.$logsCount)||void 0===e||e.runQueries(),null===(t=this.state.$detectedFieldsData)||void 0===t||t.runQueries()}))}subscribeToDataSourceVariable(){return(0,N.S9)(this).subscribeToState((()=>{this.redirectToStart()}))}resetTabCount(){this.setState({fieldsCount:void 0,labelsCount:void 0,patternsCount:void 0}),(0,d.JO)().setServiceSceneState(this.state)}subscribeToFieldsVariable(){return(0,N.ir)(this).subscribeToState(((e,t)=>{var n,r;(0,g.B)(e.filters,t.filters)||(null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries())}))}subscribeToMetadataVariable(){return(0,N.oY)(this).subscribeToState(((e,t)=>{var n,r;(0,g.B)(e.filters,t.filters)||(null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries())}))}subscribeToLevelsVariable(){return(0,N.iw)(this).subscribeToEvent(s.oh,(()=>{var e,t;null===(e=this.state.$detectedFieldsData)||void 0===e||e.runQueries(),null===(t=this.state.$logsCount)||void 0===t||t.runQueries()}))}runQueries(){const e=(0,h.FT)(),t=(0,h.er)();var n,r,a,i;e!==h.G3.patterns&&void 0!==this.state.patternsCount||null===(n=this.state.$patternsData)||void 0===n||n.runQueries(),e!==h.G3.labels&&t!==h._J.label&&void 0!==this.state.labelsCount||null===(r=this.state.$detectedLabelsData)||void 0===r||r.runQueries(),e!==h.G3.fields&&t!==h._J.field&&void 0!==this.state.fieldsCount||null===(a=this.state.$detectedFieldsData)||void 0===a||a.runQueries(),void 0===this.state.logsCount&&(null===(i=this.state.$logsCount)||void 0===i||i.runQueries())}subscribeToPatternsQuery(){var e;return null===(e=this.state.$patternsData)||void 0===e?void 0:e.subscribeToState((e=>{var t;if(this.updateLoadingState(e,fe.patterns),(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){const t=e.data.series;void 0!==(null==t?void 0:t.length)&&(this.setState({patternsCount:t.length}),(0,d.JO)().setPatternsCount(t.length))}}))}subscribeToDetectedLabelsQuery(){var e;return null===(e=this.state.$detectedLabelsData)||void 0===e?void 0:e.subscribeToState((e=>{var t;if(this.updateLoadingState(e,fe.labels),(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){const t=e.data,n=t.series[0].fields;if(void 0!==t.series.length&&void 0!==n.length){const e=t.series[0].fields.filter((e=>u.e4!==e.name));this.setState({labelsCount:e.length+1}),(0,d.JO)().setLabelsCount(n.length)}}}))}updateLoadingState(e,t){var n;const r=this.state.loadingStates;r[t]=(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Loading;const a=Object.values(r).some((e=>e));this.setState({loading:a,loadingStates:r})}subscribeToLogsQuery(){var e;return null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState(((e,t)=>{var n,r;if(this.updateLoadingState(e,fe.logs),(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done||(null===(r=e.data)||void 0===r?void 0:r.state)===i.LoadingState.Streaming){var a,s;const t=null!==(s=null===(a=e.data.series[0])||void 0===a?void 0:a.length)&&void 0!==s?s:0;t!==this.state.logsCount&&this.setState({logsCount:t})}}))}subscribeToLogsCountQuery(){var e;return null===(e=this.state.$logsCount)||void 0===e?void 0:e.subscribeToState((e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){var n,r,a,s;const t=null===(s=e.data.series[0])||void 0===s||null===(a=s.fields)||void 0===a||null===(r=a[1])||void 0===r||null===(n=r.values)||void 0===n?void 0:n[0];this.setState({totalLogsCount:t})}}))}subscribeToDetectedFieldsQuery(e){var t;return null===(t=this.state.$detectedFieldsData)||void 0===t?void 0:t.subscribeToState((t=>{var n;if(this.updateLoadingState(t,fe.fields),e&&(null===(n=t.data)||void 0===n?void 0:n.state)===i.LoadingState.Done){const e=t.data.series[0];void 0!==e&&e.length!==this.state.fieldsCount&&(this.setState({fieldsCount:e.length}),(0,d.JO)().setFieldsCount(e.length))}}))}subscribeToTimeRange(){return s.jh.getTimeRange(this).subscribeToState((()=>{var e,t,n,r;null===(e=this.state.$patternsData)||void 0===e||e.runQueries(),null===(t=this.state.$detectedLabelsData)||void 0===t||t.runQueries(),null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries()}))}resetBodyAndData(){let e={};this.state.$data||(e.$data=at()),this.state.$patternsData||(e.$patternsData=tt()),this.state.$detectedLabelsData||(e.$detectedLabelsData=nt()),this.state.$detectedFieldsData||(e.$detectedFieldsData=rt()),this.state.$logsCount||(e.$logsCount=it()),this.state.body||(e.body=et()),Object.keys(e).length&&this.setState(e)}setBreakdownView(){const{body:e}=this.state,t=(0,h.FT)(),n=be.find((e=>e.value===t));if(!e){const e=new Error("body is not defined in setBreakdownView!");throw x.v.error(e,{msg:"ServiceScene setBreakdownView error"}),e}if(n)e.setState({children:[...e.state.children.slice(0,1),n.getScene((e=>{"fields"===n.value&&this.setState({fieldsCount:e})}))]});else{const t=(0,h.er)(),n=ye.find((e=>e.value===t));n&&this.state.drillDownLabel?e.setState({children:[...e.state.children.slice(0,1),n.getScene(this.state.drillDownLabel)]}):x.v.error(new Error("not setting breakdown view"),{msg:"setBreakdownView error"})}}constructor(e){var t;super(ze({loadingStates:{[fe.patterns]:!1,[fe.labels]:!1,[fe.fields]:!1,[fe.logs]:!1},loading:!0,body:null!==(t=e.body)&&void 0!==t?t:et(),$data:at(),$patternsData:tt(),$detectedLabelsData:nt(),$detectedFieldsData:rt(),$logsCount:it()},e)),We(this,"_variableDependency",new s.Sh(this,{variableNames:[u.EY,u.MB,u.mB,u.uw,u._Y]})),We(this,"clearAdHocVariables",(()=>{[(0,N.cR)(this),(0,N.ir)(this),(0,N.iw)(this)].forEach((e=>{e.setState({filters:[]})}))})),this.addActivationHandler(this.onActivate.bind(this))}}function et(){return new s.G1({direction:"column",children:[new s.vA({ySizing:"content",body:new je({})})]})}function tt(){return(0,o.FH)([(0,c.BM)(`{${u.S1}}`,"patterns",{refId:"patterns"})])}function nt(){return(0,o.FH)([(0,c.BM)(`{${u.S1}}`,"detected_labels",{refId:"detectedLabels"})])}function rt(){return(0,o.FH)([(0,c.BM)(u.SA,"detected_fields",{refId:"detectedFields"})])}function at(){return(0,o.rS)([(0,c.l)(u.SA,{refId:Ge})])}function it(){const e=(0,o.rS)([(0,c.l)(`sum(count_over_time(${u.SA}[$__auto]))`,{refId:qe,queryType:"instant"})],{runQueriesMode:"manual"});if(e instanceof s.dt)return e;const t=new Error("log count query provider is not query runner!");throw x.v.error(t,{msg:"getLogCountQueryRunner: invalid return type"}),t}We(Ze,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):a().createElement(l.LoadingPlaceholder,{text:"Loading..."})}))},866:(e,t,n)=>{n.d(t,{p:()=>g});var r,a,i,s=n(2672),l=n(5959),o=n.n(l),c=n(2007),u=n(6089),d=n(227),p=n(9055);class g extends s.Bs{setHover(e){this.setState({hover:e})}onClick(e){e?(0,p.wy)(this.state.labelName,this.state.labelValue,this):(0,p._J)(this.state.labelName,this.state.labelValue,this)}}i=({model:e})=>{const{ds:t,labelValue:n,labelName:r,hover:a}=e.useState(),i=(0,d.eT)(t,r).includes(n),s=(0,c.useStyles2)((e=>({wrapper:(0,u.css)({display:"flex",flexDirection:"column",justifyContent:"center",alignSelf:"center"})}))),l=i?`Remove  ${n} from favorites`:`Add ${n} to favorites`;return o().createElement("span",{className:s.wrapper},o().createElement(c.ToolbarButton,{onMouseOver:()=>{e.setHover(!0)},onMouseOut:()=>{e.setHover(!1)},icon:o().createElement(c.Icon,{name:i?"favorite":"star",size:"lg",type:i?"mono":"default"}),color:i?"rgb(235, 123, 24)":"#ccc",onClick:()=>e.onClick(i),name:"star","aria-label":l,tooltip:l}))},(a="Component")in(r=g)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},1105:(e,t,n)=>{n.d(t,{X:()=>se,y:()=>ce});var r=n(6089),a=n(3241),i=n(5959),s=n.n(i),l=n(7781),o=n(2672),c=n(2007),u=n(227),d=n(3143),p=n(2718),g=n(8835),h=n(4750),v=n(4793),m=n(1220),f=n(9055);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e,t,n){const r=(0,h.cR)(n);(0,p.EE)(p.NO.service_selection,p.ir.service_selection.service_selected,{value:t,label:e});const a=[...r.state.filters.filter((n=>!(n.key===e&&n.value===t))),{key:e,operator:v.w7.Equal,value:t}];r.setState({filters:a}),(0,f._J)(e,t,n),e===d.OX&&(e=d.ky),(0,g.jY)(e,t)}class S extends o.Bs{constructor(...e){super(...e),b(this,"onClick",(()=>{this.state.labelValue&&y(this.state.labelName,this.state.labelValue,this)}))}}function w(e){return{button:(0,r.css)({alignSelf:"center"})}}b(S,"Component",(({model:e})=>{const t=(0,c.useStyles2)(w);return s().createElement(c.Button,{"data-testid":m.b.index.showLogsButton,tooltip:`View logs for ${e.state.labelValue}`,className:t.button,variant:"secondary",size:"sm",onClick:e.onClick},"Show logs")}));var O=n(7918),x=n(5183),E=n(4482);const C=()=>s().createElement(E.R,null,s().createElement("p",null,"Log volume has not been configured."),s().createElement("p",null,s().createElement(c.TextLink,{href:"https://grafana.com/docs/loki/latest/reference/api/#query-log-volume",external:!0},"Instructions to enable volume in the Loki config:")),s().createElement(c.Text,{textAlignment:"left"},s().createElement("pre",null,s().createElement("code",null,"limits_config:",s().createElement("br",null),"  volume_enabled: true")))),k=()=>s().createElement(E.R,null,s().createElement("p",null,"No service matched your search."));var F=n(1383),L=n(4462),P=n(5431),j=n(833),_=n(8531),T=n(5435),D=n(4002),N=n(8538),I=n(8315);function $(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class A extends o.Bs{}$(A,"Component",(({model:e})=>{const t=o.jh.getAncestor(e,ce),n=o.jh.getAncestor(e,V),{tabOptions:r,showPopover:a}=n.useState(),i=(0,c.useStyles2)(M),l=r.map((e=>{return t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){$(e,t,n[t])}))}return e}({},e),n=null!=(n={icon:e.saved?"save":void 0,label:`${e.label}`})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t;var t,n}));return s().createElement(c.Stack,{direction:"column",gap:0,role:"tooltip"},s().createElement("div",{className:i.card.body},s().createElement(c.Select,{menuShouldPortal:!1,width:50,onBlur:()=>{n.toggleShowPopover()},autoFocus:!0,isOpen:a,placeholder:"Search labels",options:l,isSearchable:!0,openMenuOnFocus:!0,onChange:e=>{e.value&&(n.toggleShowPopover(),t.setSelectedTab(e.value))}})))}));const M=e=>({card:{body:(0,r.css)({padding:e.spacing(1)}),p:(0,r.css)({maxWidth:300})}});function B(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){B(e,t,n[t])}))}return e}class V extends o.Bs{getLabelsFromQueryRunnerState(e=(()=>{var e;return null===(e=this.state.$labelsData)||void 0===e?void 0:e.state})()){var t;return null===(t=e.data)||void 0===t?void 0:t.series[0].fields.map((e=>({label:e.name,cardinality:e.values[0]})))}populatePrimaryLabelsVariableOptions(e){const t=o.jh.getAncestor(this,ce).getSelectedTab(),n=(0,u.sj)((0,h.S9)(this).getValue().toString()),r=e.map((e=>{const r=n.indexOf(e.label);return{label:e.label===d.OX?d.ky:e.label,value:e.label,active:t===e.label,saved:-1!==r,savedIndex:r}})).sort(((e,t)=>e.value===d.OX||t.value===d.OX?e.value===d.OX?-1:1:e.label<t.label?-1:e.label>t.label?1:0));this.setState({tabOptions:r})}runDetectedLabels(){this.state.$labelsData.runQueries()}runDetectedLabelsSubs(){this._subs.add(o.jh.getTimeRange(this).subscribeToState((()=>{this.runDetectedLabels()}))),this._subs.add((0,h.S9)(this).subscribeToState((()=>{this.runDetectedLabels()})))}onActivate(){this.runDetectedLabels(),this.setState({popover:new A({})}),this.runDetectedLabelsSubs(),this._subs.add((0,h.S9)(this).subscribeToState((()=>{this.state.$labelsData.runQueries()}))),this._subs.add((0,h.El)(this).subscribeToState((()=>{var e;const t=this.getLabelsFromQueryRunnerState(null===(e=this.state.$labelsData)||void 0===e?void 0:e.state);t&&this.populatePrimaryLabelsVariableOptions(t)}))),this._subs.add(this.state.$labelsData.subscribeToState((e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===l.LoadingState.Done){const t=this.getLabelsFromQueryRunnerState(e),n=o.jh.getAncestor(this,ce);t&&this.populatePrimaryLabelsVariableOptions(t);const r=n.getSelectedTab();(null==t?void 0:t.some((e=>e.label===r)))||n.selectDefaultLabelTab()}})))}constructor(e){super(R({showPopover:!1,$labelsData:(0,x.HF)({queries:[(0,O.BM)("","detected_labels")],runQueriesMode:"manual"}),tabOptions:[{label:d.ky,value:d.OX,saved:!0}]},e)),B(this,"removeSavedTab",(e=>{(0,u.Gg)((0,h.S9)(this).getValue().toString(),e);const t=this.getLabelsFromQueryRunnerState();t&&this.populatePrimaryLabelsVariableOptions(t);const n=o.jh.getAncestor(this,ce);n.getSelectedTab()===e&&n.selectDefaultLabelTab()})),B(this,"toggleShowPopover",(()=>{this.setState({showPopover:!this.state.showPopover})})),this.addActivationHandler(this.onActivate.bind(this))}}B(V,"Component",(({model:e})=>{const{tabOptions:t,showPopover:n,popover:u,$labelsData:p}=e.useState(),{data:g}=p.useState(),v=o.jh.getAncestor(e,ce);(0,h.El)(e).useState();const m=(0,c.useStyles2)(W),f=(0,i.useRef)(null);return s().createElement(c.TabsBar,null,t.filter((e=>e.saved||e.active||e.value===d.OX)).sort(((e,t)=>{return e.value===d.OX||t.value===d.OX?e.value===d.OX?-1:1:(null!==(n=e.savedIndex)&&void 0!==n?n:0)-(null!==(r=t.savedIndex)&&void 0!==r?r:0);var n,r})).map((t=>{const n=s().createElement(c.Tab,{key:t.value,onChangeTab:()=>{v.setSelectedTab(t.value)},label:(0,I.EJ)(t.label,15,!0),active:t.active,suffix:t.value!==d.OX?n=>s().createElement(s().Fragment,null,s().createElement(c.Tooltip,{content:"Remove tab"},s().createElement(c.Icon,{onKeyDownCapture:n=>{"Enter"===n.key&&e.removeSavedTab(t.value)},onClick:n=>{n.stopPropagation(),e.removeSavedTab(t.value)},name:"times",className:(0,r.cx)(n.className)}))):void 0});return t.label.length>15?s().createElement(c.Tooltip,{key:t.value,content:t.label},n):n})),(null==g?void 0:g.state)===l.LoadingState.Loading&&s().createElement(c.Tab,{label:"Loading tabs",icon:"spinner"}),(null==g?void 0:g.state)===l.LoadingState.Done&&s().createElement("span",{className:m.addTab},s().createElement(c.Tab,{onChangeTab:e.toggleShowPopover,label:"Add label",ref:f,icon:"plus-circle"})),u&&s().createElement(c.PopoverController,{content:s().createElement(u.Component,{model:u})},((e,t,r)=>{const i={onBlur:t,onFocus:e};return s().createElement(s().Fragment,null,f.current&&s().createElement(s().Fragment,null,s().createElement(c.Popover,R((l=R({},r,a.rest),o=null!=(o={show:n,wrapperClassName:m.popover,referenceElement:f.current,renderArrow:!0})?o:{},Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(o)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(o)).forEach((function(e){Object.defineProperty(l,e,Object.getOwnPropertyDescriptor(o,e))})),l),i))));var l,o})))}));const W=e=>({addTab:(0,r.css)({label:"add-label-tab",color:e.colors.primary.text,"& button":{color:e.colors.primary.text}}),popover:(0,r.css)({borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3,background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`})});var z=n(866);const H=e=>s().createElement(E.R,null,s().createElement("p",null,"No logs found in ",s().createElement("strong",null,e.labelName),".",s().createElement("br",null),"Please adjust time range or select another label."));var G=n(9829),q=n(558);function K(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function U(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){K(e,t,n[t])}))}return e}class Q extends o.Bs{onActivate(){this.setState(U({},this.isSelected())),this._subs.add((0,h.cR)(this).subscribeToState((()=>{const e=this.isSelected();this.state.included!==e.included&&this.setState(U({},e))})))}getFilter(){return{name:this.state.name,value:this.state.value}}constructor(e){var t,n;super((t=U({},e),n=null!=(n={included:null})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)),K(this,"isSelected",(()=>{const e=(0,h.cR)(this),t=e.state.filters.find((t=>{const n=(0,h.z2)(e,t);return t.key===this.state.name&&n.value===this.state.value}));return t?{included:t.operator===v.w7.Equal}:{included:!1}})),K(this,"onClick",(e=>{const t=this.getFilter();(0,q.Qt)(t.name,t.value,e,this,d.MB);const n=(0,h.cR)(this);(0,p.EE)(p.NO.service_selection,p.ir.service_selection.add_to_filters,{filterType:"index-filters",key:t.name,action:e,filtersLength:(null==n?void 0:n.state.filters.length)||0}),this.setState(U({},this.isSelected()))})),this.addActivationHandler(this.onActivate.bind(this))}}K(Q,"Component",(({model:e})=>{const{value:t,hidden:n,included:r}=e.useState();if(n)return s().createElement(s().Fragment,null);const a=(0,c.useStyles2)(J);return s().createElement("span",{className:a.wrapper},s().createElement(c.Button,{tooltip:!0===r?`Remove ${t} from filters`:`Add ${t} to filters`,variant:"secondary",fill:"outline",icon:!0===r?"minus":"plus",size:"sm","aria-selected":!0===r,className:a.includeButton,onClick:()=>!0===r?e.onClick("clear"):e.onClick("include"),"data-testid":m.b.exploreServiceDetails.buttonFilterInclude}))}));const J=()=>({container:(0,r.css)({display:"flex",justifyContent:"center"}),includeButton:(0,r.css)({borderRadius:0}),wrapper:(0,r.css)({display:"flex",flexDirection:"column",justifyContent:"center",alignSelf:"center"})});var Y=n(6059);function X(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Z extends o.Bs{}function ee(e){return{icon:(0,r.css)({color:e.colors.text.disabled,marginLeft:e.spacing.x1}),searchPageCountWrap:(0,r.css)({display:"flex",alignItems:"center"}),select:(0,r.css)({maxWidth:"65px",marginLeft:e.spacing(1),marginRight:e.spacing(1)}),searchFieldPlaceholderText:(0,r.css)({fontSize:e.typography.bodySmall.fontSize,color:e.colors.text.disabled,alignItems:"center",display:"flex",flex:"1 0 auto",textWrapMode:"nowrap"})}}function te(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){te(e,t,n[t])}))}return e}function re(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}X(Z,"PageCount",(({model:e,totalCount:t})=>{const n=(0,c.useStyles2)(ee),r=o.jh.getAncestor(e,ce),{countPerPage:a}=r.useState(),l=function(e){const t=20*Math.ceil(e/20),n=[];for(let r=20;r<=60&&r<=t;r+=20){let t=r.toString();r<20?t=r.toString():r>e&&(t=e.toString()),n.push({value:r.toString(),label:t})}return n}(t);return(0,i.useEffect)((()=>{var e,t;const n=null!==(t=null===(e=l[l.length-1])||void 0===e?void 0:e.value)&&void 0!==t?t:a.toString();a.toString()>n&&r.setState({countPerPage:parseInt(n,10)})}),[a,l,r]),s().createElement("span",{className:n.searchPageCountWrap},s().createElement("span",{className:n.searchFieldPlaceholderText},"Showing"," ",s().createElement(c.Select,{className:n.select,onChange:e=>{if(e.value){const t=parseInt(e.value,10);r.setState({countPerPage:t,currentPage:1}),r.updateBody(),(0,u.uF)(t)}},options:l,value:a.toString()})," ","of ",t," ",s().createElement(c.IconButton,{className:n.icon,"aria-label":"Count info",name:"info-circle",tooltip:`${t} labels have values for the selected time range. Total label count may differ`})))})),X(Z,"Component",(({model:e,totalCount:t})=>{const n=o.jh.getAncestor(e,ce),{countPerPage:a,currentPage:i}=n.useState(),l=(0,c.useStyles2)((e=>({pagination:(0,r.css)({float:"none"}),paginationWrap:(0,r.css)({[e.breakpoints.up("lg")]:{display:"none"},[e.breakpoints.down("lg")]:{display:"flex",justifyContent:"flex-end",flex:"1 0 auto"}}),paginationWrapMd:(0,r.css)({[e.breakpoints.down("lg")]:{display:"none"},[e.breakpoints.up("lg")]:{display:"flex",justifyContent:"flex-end",flex:"1 0 auto"}})})));return t>a?s().createElement(s().Fragment,null,s().createElement("span",{className:l.paginationWrapMd},s().createElement(c.Pagination,{className:l.pagination,currentPage:i,numberOfPages:Math.ceil(t/a),onNavigate:e=>{n.setState({currentPage:e}),n.updateBody()}})),s().createElement("span",{className:l.paginationWrap},s().createElement(c.Pagination,{showSmallVersion:!0,className:l.pagination,currentPage:i,numberOfPages:Math.ceil(t/a),onNavigate:e=>{n.setState({currentPage:e}),n.updateBody()}}))):null}));const ae=_.config.featureToggles.exploreLogsAggregatedMetrics,ie="__aggregated_metric__",se=(0,l.dateTime)("2024-08-30","YYYY-MM-DD"),le="var-primary_label",oe="var-ds";class ce extends o.Bs{getUrlState(){const{key:e}=ue(),t=(0,h.El)(this).state.filters[0];return t.key&&t.key!==e&&(0,h.El)(this).setState({filters:[re(ne({},t),{key:null!=e?e:t.key})]}),{}}updateFromUrl(e){}addDatasourceChangeToBrowserHistory(e){const t=_.locationService.getLocation(),n=new URLSearchParams(t.search),r=n.get(oe);if(r&&e!==r){const r=t.pathname+t.search;n.set(oe,e);const a=t.pathname+"?"+n.toString();r!==a&&(0,g.ad)(a)}}addLabelChangeToBrowserHistory(e,t=!1){const{key:n,search:r,location:a}=ue();if(n){const i=null==n?void 0:n.split("|");if((null==i?void 0:i[0])!==e){i[0]=e,r.set(le,i.join("|"));const n=a.pathname+a.search,s=a.pathname+"?"+r.toString();n!==s&&(t?_.locationService.replace(s):(0,g.ad)(s))}}}getSelectedTab(){var e;return null===(e=(0,h.El)(this).state.filters[0])||void 0===e?void 0:e.key}selectDefaultLabelTab(){this.addLabelChangeToBrowserHistory(d.OX,!0),this.setSelectedTab(d.OX)}setSelectedTab(e){(0,u.cO)((0,h.S9)(this).getValue().toString(),e),(0,h.h)(this),(0,h.BL)(e,this)}buildServiceLayout(e,t,n,r,a,i){var s;let u;n.to.diff(n.from,"hours")>=4&&n.to.diff(n.from,"hours")<=26&&(u="2h");const p=o.d0.timeseries().setTitle(t).setData((0,x.rS)([(0,O.l)(this.getMetricExpression(t,r,a),{legendFormat:`{{${d.e4}}}`,splitDuration:u,refId:`ts-${t}`})],{runQueriesMode:"manual"})).setCustomFieldConfig("stacking",{mode:c.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",c.DrawStyle.Bars).setUnit("short").setOverrides(x.jC).setOption("legend",{showLegend:!0,calcs:["sum"],placement:"right",displayMode:c.LegendDisplayMode.Table}).setHeaderActions([new z.p({ds:null===(s=i.getValue())||void 0===s?void 0:s.toString(),labelName:e,labelValue:t}),new Q({name:e,value:t,hidden:this.isAggregatedMetricsActive()}),new S({labelValue:t,labelName:e})]).build();p.setState({extendPanelContext:(n,r)=>this.extendTimeSeriesLegendBus(e,t,r,p)});const g=new o.xK({$behaviors:[new o.Gg.K2({key:"serviceCrosshairSync",sync:l.DashboardCursorSync.Crosshair})],body:p});return g.addActivationHandler((()=>{var e;(null===(e=(0,G.oh)(g)[0].state.data)||void 0===e?void 0:e.state)!==l.LoadingState.Done&&this.runPanelQuery(g)})),g}isAggregatedMetricsActive(){const e=this.getQueryOptionsToolbar();return!(null==e?void 0:e.state.options.aggregatedMetrics.disabled)&&(null==e?void 0:e.state.options.aggregatedMetrics.active)}formatPrimaryLabelForUI(){const e=this.getSelectedTab();return e===d.OX?d.ky:e}setVolumeQueryRunner(){this.setState({$data:(0,x.HF)({queries:[(0,O.$k)(`{${d.kl}, ${d.ll}}`,"volume",this.getSelectedTab())],runQueriesMode:"manual"})}),this.subscribeToVolume()}doVariablesNeedSync(){const e=(0,h.cR)(this),t=(0,h.aW)(this),n=this.getSelectedTab(),r=e.state.filters.filter((e=>e.key!==n));return{filters:r,needsSync:!(0,j.B)(r,t.state.filters)}}syncVariables(){const e=(0,h.aW)(this),{filters:t,needsSync:n}=this.doVariablesNeedSync();n&&e.setState({filters:t})}onActivate(){var e;this.fixRequiredUrlParams(),this.syncVariables(),this.setVolumeQueryRunner(),this.subscribeToPrimaryLabelsVariable(),this.subscribeToLabelFilterChanges(),this.subscribeToActiveTabVariable((0,h.El)(this)),(null===(e=this.state.$data.state.data)||void 0===e?void 0:e.state)!==l.LoadingState.Done&&this.runVolumeOnActivate(),this.subscribeToTimeRange(),this.subscribeToDatasource(),this.subscribeToAggregatedMetricToggle(),this.subscribeToAggregatedMetricVariable()}runVolumeOnActivate(){var e,t;this.isTimeRangeTooEarlyForAggMetrics()?(this.onUnsupportedAggregatedMetricTimeRange(),(null===(e=this.state.$data.state.data)||void 0===e?void 0:e.state)!==l.LoadingState.Done&&this.runVolumeQuery()):(this.onSupportedAggregatedMetricTimeRange(),(null===(t=this.state.$data.state.data)||void 0===t?void 0:t.state)!==l.LoadingState.Done&&this.runVolumeQuery())}subscribeToAggregatedMetricToggle(){var e;this._subs.add(null===(e=this.getQueryOptionsToolbar())||void 0===e?void 0:e.subscribeToState(((e,t)=>{e.options.aggregatedMetrics.userOverride!==t.options.aggregatedMetrics.userOverride&&this.runVolumeQuery(!0)})))}subscribeToDatasource(){this._subs.add((0,h.S9)(this).subscribeToState((e=>{this.addDatasourceChangeToBrowserHistory(e.value.toString()),this.runVolumeQuery()})))}subscribeToActiveTabVariable(e){this._subs.add(e.subscribeToState(((e,t)=>{if(e.filterExpression!==t.filterExpression){const t=e.filters[0].key;this.addLabelChangeToBrowserHistory(t);const{needsSync:n}=this.doVariablesNeedSync();n?this.syncVariables():this.runVolumeQuery(!0)}})))}subscribeToAggregatedMetricVariable(){this._subs.add((0,h.vm)(this).subscribeToState(((e,t)=>{e.value!==t.value&&(this.setState({body:new o.gF({children:[]})}),this.updateBody(!0))})))}subscribeToPrimaryLabelsVariable(){const e=(0,h.cR)(this);this._subs.add(e.subscribeToState(((e,t)=>{(0,j.B)(e.filters,t.filters)||this.syncVariables()})))}subscribeToLabelFilterChanges(){const e=(0,h.aW)(this);this._subs.add(e.subscribeToState(((e,t)=>{(0,j.B)(e.filters,t.filters)||this.runVolumeQuery(!0)})))}subscribeToVolume(){this._subs.add(this.state.$data.subscribeToState(((e,t)=>{var n,r,a;(null===(n=e.data)||void 0===n?void 0:n.state)!==l.LoadingState.Done||(0,j.B)(null==t||null===(r=t.data)||void 0===r?void 0:r.series,null==e||null===(a=e.data)||void 0===a?void 0:a.series)||this.updateBody(!0)})))}subscribeToTimeRange(){this._subs.add(o.jh.getTimeRange(this).subscribeToState((()=>{this.isTimeRangeTooEarlyForAggMetrics()?this.onUnsupportedAggregatedMetricTimeRange():this.onSupportedAggregatedMetricTimeRange(),this.runVolumeQuery()})))}fixRequiredUrlParams(){const{key:e}=ue();e||this.selectDefaultLabelTab()}isTimeRangeTooEarlyForAggMetrics(){return o.jh.getTimeRange(this).state.value.from.isBefore((0,l.dateTime)(se))}onUnsupportedAggregatedMetricTimeRange(){const e=this.getQueryOptionsToolbar();null==e||e.setState({options:{aggregatedMetrics:re(ne({},null==e?void 0:e.state.options.aggregatedMetrics),{disabled:!0})}})}getQueryOptionsToolbar(){return o.jh.getAncestor(this,N.P).state.controls.find((e=>e instanceof D.s))}onSupportedAggregatedMetricTimeRange(){const e=this.getQueryOptionsToolbar();null==e||e.setState({options:{aggregatedMetrics:re(ne({},null==e?void 0:e.state.options.aggregatedMetrics),{disabled:!1})}})}runVolumeQuery(e=!1){e&&this.setVolumeQueryRunner(),this.updateAggregatedMetricVariable(),this.state.$data.runQueries()}updateAggregatedMetricVariable(){const e=(0,h.vm)(this),t=(0,h.cR)(this);this.isTimeRangeTooEarlyForAggMetrics()&&ae||!this.isAggregatedMetricsActive()?(e.changeValueTo(d.OX),t.setState({hide:T.zL.dontHide}),e.changeValueTo(d.OX),o.jh.findByKeyAndType(this,N.y,Y.H).setState({hidden:!1})):(e.changeValueTo(ie),t.setState({hide:T.zL.hideVariable,filters:[]}),o.jh.findByKeyAndType(this,N.y,Y.H).setState({hidden:!0}))}updateTabs(){if(!this.state.tabs){const e=new V({});this.setState({tabs:e})}}getGridItems(){return this.state.body.state.children}getVizPanel(e){return e.state.body instanceof o.Eb?e.state.body:void 0}runPanelQuery(e){if(e.isActive){const n=(0,G.oh)(e);if(1===n.length){var t;const e=n[0],r=e.state.queries[0],a=null===(t=e.state.data)||void 0===t?void 0:t.timeRange,i=o.jh.getTimeRange(this),s=a?Math.abs(i.state.value.from.diff(null==a?void 0:a.from,"s")):1/0,l=a?Math.abs(i.state.value.to.diff(null==a?void 0:a.to,"s")):1/0,c=o.jh.interpolate(this,r.expr);(e.state.key!==c||s>0||l>0)&&(e.setState({key:c}),e.runQueries())}}}updateBody(e=!1){var t;const{labelsToQuery:n}=this.getLabels(null===(t=this.state.$data.state.data)||void 0===t?void 0:t.series),r=this.getSelectedTab();if(this.updateTabs(),this.state.paginationScene||this.setState({paginationScene:new Z({})}),n&&0!==n.length){const t=[],a=this.getGridItems(),i=o.jh.getTimeRange(this).state.value,s=(0,h.vm)(this),l=(0,h.El)(this),c=(0,h.S9)(this),u=(this.state.currentPage-1)*this.state.countPerPage,d=u+this.state.countPerPage;for(const o of n.slice(u,d)){const n=a.filter((e=>{const t=this.getVizPanel(e);return(null==t?void 0:t.state.title)===o}));if(2===n.length)t.push(n[0],n[1]),n[0].isActive&&e&&this.runPanelQuery(n[0]),n[1].isActive&&e&&this.runPanelQuery(n[1]);else{const e=this.buildServiceLayout(r,o,i,s,l,c),n=this.buildServiceLogsLayout(r,o);t.push(e,n)}}this.state.body.setState({children:t,isLazy:!0,templateColumns:"repeat(auto-fit, minmax(500px, 1fr) minmax(300px, 70vw))",autoRows:"200px",md:{templateColumns:"1fr",rowGap:1,columnGap:1}})}else this.state.body.setState({children:[]})}updateServiceLogs(e,t){var n;if(!this.state.body)return void this.updateBody();const{labelsToQuery:r}=this.getLabels(null===(n=this.state.$data.state.data)||void 0===n?void 0:n.series),a=null==r?void 0:r.indexOf(t);if(void 0===a||a<0)return;let i=[...this.getGridItems()];i.splice(2*a+1,1,this.buildServiceLogsLayout(e,t)),this.state.body.setState({children:i})}getLogExpression(e,t,n){return`{${e}=\`${t}\` , ${d.ll} }${n}`}getMetricExpression(e,t,n){const r=n.state.filters[0];return t.state.value===ie?r.key===d.OX?`sum by (${d.e4}) (sum_over_time({${ie}=\`${e}\` } | logfmt | unwrap count [$__auto]))`:`sum by (${d.e4}) (sum_over_time({${ie}=~\`.+\` } | logfmt | ${r.key}=\`${e}\` | unwrap count [$__auto]))`:`sum by (${d.e4}) (count_over_time({ ${r.key}=\`${e}\`, ${d.ll} } [$__auto]))`}getLabels(e){var t,n,r;const a=null!==(r=null==e||null===(t=e[0])||void 0===t?void 0:t.fields[0].values)&&void 0!==r?r:[],i=null===(n=(0,h.S9)(this).getValue())||void 0===n?void 0:n.toString(),s=(0,h.eY)(this).getValue(),l=this.getSelectedTab(),o=function(e,t,n,r){if(!(null==e?void 0:e.length))return[];".+"===n&&(n="");const a=(0,u.eT)(t,r).filter((t=>t.toLowerCase().includes(n.toLowerCase())&&e.includes(t)));return Array.from(new Set([...a,...e]))}(a,i,String(s),l);return{labelsByVolume:a,labelsToQuery:o}}constructor(e){var t,n;super(ne({body:new o.gF({children:[]}),$variables:new o.Pj({variables:[new P.m({name:d.Du,label:"Service",hide:T.zL.hideVariable,skipUrlSync:!0,value:".+"}),new P.m({name:d.Wi,label:"",hide:T.zL.hideLabel,value:d.OX,skipUrlSync:!0,options:[{value:d.OX,label:d.OX},{value:ie,label:ie}]}),new o.H9({name:d.Gb,hide:T.zL.hideLabel,expressionBuilder:e=>function(e){if(e.length){const t=e[0];return`${t.key}${t.operator}\`${t.value}\``}return""}(e),filters:[{key:null!==(t=ue().key)&&void 0!==t?t:d.OX,value:".+",operator:"=~"}]}),new o.H9({name:d.fi,datasource:d.eL,layout:"vertical",filters:[],expressionBuilder:O.VW,hide:T.zL.hideVariable,key:"adhoc_service_filter_replica",skipUrlSync:!0})]}),$data:(0,x.HF)({queries:[],runQueriesMode:"manual"}),serviceLevel:new Map,countPerPage:null!==(n=(0,u.KH)())&&void 0!==n?n:20,currentPage:1,showPopover:!1,tabOptions:[{label:d.ky,value:d.OX}]},e)),te(this,"_urlSync",new o.So(this,{keys:[le]})),te(this,"onSearchServicesChange",(0,a.debounce)((e=>{const t=(0,h.eY)(this);(e?(0,O.vC)(e):".+")!==t.state.value&&t.setState({value:e?(0,O.vC)(e):".+",label:null!=e?e:""});const n=(0,h.El)(this),r=n.state.filters[0];(0,O.vC)(t.state.value.toString())!==r.value&&n.setState({filters:[re(ne({},r),{value:(0,O.vC)(t.state.value.toString())})]}),this.setState({currentPage:1}),(0,p.EE)(p.NO.service_selection,p.ir.service_selection.search_services_changed,{searchQuery:e})}),500)),te(this,"getLevelFilterForService",(e=>{let t=this.state.serviceLevel.get(e)||[];return 0===t.length?"":` | ${t.map((e=>("logs"===e&&(e=""),`${d.e4}=\`${e}\``))).join(" or ")} `})),te(this,"buildServiceLogsLayout",((e,t)=>{const n=this.getLevelFilterForService(t),r=new o.xK({$behaviors:[new o.Gg.K2({sync:l.DashboardCursorSync.Off})],body:o.d0.logs().setHoverHeader(!0).setData((0,x.rS)([(0,O.l)(this.getLogExpression(e,t,n),{maxLines:100,refId:`logs-${t}`})],{runQueriesMode:"manual"})).setTitle(t).setOption("showTime",!0).setOption("enableLogDetails",!1).build()});return r.addActivationHandler((()=>{var e;(null===(e=(0,G.oh)(r)[0].state.data)||void 0===e?void 0:e.state)!==l.LoadingState.Done&&this.runPanelQuery(r)})),r})),te(this,"extendTimeSeriesLegendBus",((e,t,n,r)=>{const a=n.onToggleSeriesVisibility;n.onToggleSeriesVisibility=(n,i)=>{var s,l,o;null==a||a(n,i);const c=(0,F.de)(null!==(o=null===(l=r.state.$data)||void 0===l||null===(s=l.state.data)||void 0===s?void 0:s.series)&&void 0!==o?o:[]),u=(0,F.pC)(n,this.state.serviceLevel.get(t),i,c);this.state.serviceLevel.set(t,u),this.updateServiceLogs(e,t)}})),this.addActivationHandler(this.onActivate.bind(this))}}function ue(){const e=_.locationService.getLocation(),t=new URLSearchParams(e.search),n=t.get(le),r=null==n?void 0:n.split("|");return{key:null==r?void 0:r[0],search:t,location:e}}function de(e){return{container:(0,r.css)({display:"flex",flexDirection:"column",flexGrow:1,position:"relative"}),headingWrapper:(0,r.css)({marginTop:e.spacing(1)}),loadingText:(0,r.css)({margin:0}),header:(0,r.css)({position:"absolute",right:0,top:"4px",zIndex:2}),bodyWrapper:(0,r.css)({flexGrow:1,display:"flex",flexDirection:"column"}),body:(0,r.css)({flexGrow:1,display:"flex",flexDirection:"column"}),searchPaginationWrap:(0,r.css)({label:"search-pagination-wrap",display:"flex",alignItems:"center",flexWrap:"wrap",flex:"1 0 auto",[e.breakpoints.down("md")]:{marginTop:e.spacing(1),width:"100%"}}),searchWrapper:(0,r.css)({label:"search-wrapper",display:"flex",alignItems:"center",flexWrap:"wrap",[e.breakpoints.down("md")]:{flexDirection:"column",alignItems:"flex-start"}}),searchField:(0,r.css)({marginTop:e.spacing(1),position:"relative"})}}te(ce,"Component",(({model:e})=>{var t;const n=(0,c.useStyles2)(de),{body:r,$data:a,tabs:i,paginationScene:o}=e.useState(),{data:u}=a.useState(),d=e.getSelectedTab(),p=(0,h.eY)(e),{label:g,value:v}=p.useState(),m=v&&".+"!==v,{labelsByVolume:f,labelsToQuery:b}=e.getLabels(null==u?void 0:u.series),S=(null==u?void 0:u.state)===l.LoadingState.Loading||(null==u?void 0:u.state)===l.LoadingState.Streaming||void 0===u,w=(null===(t=a.state.data)||void 0===t?void 0:t.state)===l.LoadingState.Error,x=e.formatPrimaryLabelForUI();let E=p.getValue().toString();".+"===E&&(E="");const F=(0,O.sT)(E);var P;return s().createElement("div",{className:n.container},s().createElement("div",{className:n.bodyWrapper},i&&s().createElement(i.Component,{model:i}),s().createElement(c.Field,{className:n.searchField},s().createElement("div",{className:n.searchWrapper},s().createElement(L.f,{initialFilter:{label:F,value:E,icon:"filter"},isLoading:S,value:E||g,onChange:t=>(t=>{e.onSearchServicesChange(t)})(t),selectOption:t=>{y(d,t,e)},label:x,options:null!==(P=null==b?void 0:b.map((e=>({value:e,label:e}))))&&void 0!==P?P:[]}),!S&&s().createElement("span",{className:n.searchPaginationWrap},o&&s().createElement(Z.PageCount,{model:o,totalCount:b.length}),o&&s().createElement(Z.Component,{model:o,totalCount:b.length})))),!S&&w&&s().createElement(C,null),!S&&!w&&m&&!(null==f?void 0:f.length)&&s().createElement(k,null),!S&&!w&&!m&&!(null==f?void 0:f.length)&&s().createElement(H,{labelName:d}),!(!S&&w)&&s().createElement("div",{className:n.body},s().createElement(r.Component,{model:r}),s().createElement("div",{className:n.headingWrapper},o&&s().createElement(Z.Component,{totalCount:b.length,model:o})))))}))},8760:(e,t,n)=>{n.d(t,{C:()=>s});var r=n(5959),a=n.n(r),i=n(2007);function s(e){return a().createElement(i.RadioButtonGroup,{options:[{label:"Logs",value:"logs",description:"Show results in logs visualisation"},{label:"Table",value:"table",description:"Show results in table visualisation"}],size:"sm",value:e.vizType,onChange:e.onChange})}},5431:(e,t,n)=>{n.d(t,{m:()=>s});var r=n(1269),a=n(2672);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class s extends a.n8{getValueOptions(e){return(0,r.of)(this.state.options)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){i(e,t,n[t])}))}return e}({type:"custom",value:"",text:"",options:[],name:""},e))}}i(s,"Component",(({model:e})=>(0,a.yC)(e)))},7063:(e,t,n)=>{n.d(t,{K:()=>p});var r=n(2672),a=n(4793),i=n(3241),s=n(3143),l=n(4750),o=n(5111),c=n(4011),u=n(227);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class p{getJoinedLabelsFilters(){let{equalsFilters:e,notEqualsFilters:t,regexEqualFilters:n,regexNotEqualFilters:r}=this.getCombinedLabelFilters();const a=[];return[e,t,n,r].filter((e=>e)).forEach((e=>{const t=this.joinCombinedFiltersValues(e,"|");for(const n in e){const r=e[n].operator;a.push({key:n,operator:r,value:t[n]})}})),a}getLabelsExpr(){let{equalsFilters:e,notEqualsFilters:t,regexEqualFilters:n,regexNotEqualFilters:r,ltFilters:a,lteFilters:i,gtFilters:s,gteFilters:l}=this.getCombinedLabelFilters();this.options.debug&&console.info("combined filters after merge",{equalsFilters:e,notEqualsFilters:t,regexEqualFilters:n,regexNotEqualFilters:r,ltFilters:a,lteFilters:i,gtFilters:s,gteFilters:l});const o=this.buildLabelsLogQLFromFilters({equalsFilters:e,notEqualsFilters:t,regexEqualFilters:n,regexNotEqualFilters:r,ltFilters:a,lteFilters:i,gtFilters:s,gteFilters:l});var c;return o?(null!==(c=this.options.prefix)&&void 0!==c?c:"")+o:""}getMetadataExpr(e={filterSeparator:" |",prefix:"| ",joinMatchFilters:!1,decodeFilters:!1}){return this.options=e,this.getLabelsExpr()}getLevelsExpr(e={filterSeparator:" |",prefix:"| ",joinMatchFilters:!1,decodeFilters:!1}){return this.options=e,this.getLabelsExpr()}getFieldsExpr(e={filterSeparator:" |",prefix:"| ",joinMatchFilters:!1,decodeFilters:!0}){return this.options=e,this.getLabelsExpr()}buildLabelsLogQLFromFilters({equalsFilters:e,notEqualsFilters:t,regexEqualFilters:n,regexNotEqualFilters:r,ltFilters:s,lteFilters:l,gtFilters:o,gteFilters:c}){let u,d,p,g,h,v,m,f;const b=[];var y;this.options.joinMatchFilters?(u=this.joinCombinedFiltersValues(e,"|"),d=this.joinCombinedFiltersValues(t,"|"),p=this.joinCombinedFiltersValues(n,"|"),g=this.joinCombinedFiltersValues(r,"|"),b.push(...this.buildJoinedFilters(u,a.KQ.Equal)),b.push(...this.buildJoinedFilters(d,a.KQ.NotEqual)),b.push(...this.buildJoinedFilters(p,a.KQ.RegexEqual)),b.push(...this.buildJoinedFilters(g,a.KQ.RegexNotEqual))):(u=this.getFilterValues(e),d=this.getFilterValues(t),p=this.getFilterValues(n),g=this.getFilterValues(r),b.push(...this.buildFilter(u,a.KQ.Equal)),b.push(...this.buildFilter(d,a.KQ.NotEqual)),b.push(...this.buildFilter(p,a.KQ.RegexEqual)),b.push(...this.buildFilter(g,a.KQ.RegexNotEqual))),h=this.getFilterValues(s),v=this.getFilterValues(l),m=this.getFilterValues(o),f=this.getFilterValues(c),b.push(...this.buildFilter(h,a.Rk.lt)),b.push(...this.buildFilter(v,a.Rk.lte)),b.push(...this.buildFilter(m,a.Rk.gt)),b.push(...this.buildFilter(f,a.Rk.gte)),this.options.debug&&console.info("combined filters after stringify",{equalFiltersStrings:u,notEqualsFiltersStrings:d,regexEqualFiltersStrings:p,regexNotEqualFiltersStrings:g,ltFiltersStrings:h,lteFiltersStrings:v,gtFiltersStrings:m,gteFiltersStrings:f,allFilters:b});const S=(0,i.trim)(this.combineValues(b,`${null!==(y=this.options.filterSeparator)&&void 0!==y?y:","} `));return this.options.debug&&console.info("labels expr",{allFiltersString:S}),S}getCombinedLabelFilters(){const{[a.KQ.Equal]:e,[a.KQ.NotEqual]:t,[a.KQ.RegexEqual]:n,[a.KQ.RegexNotEqual]:r,[a.Rk.lt]:i,[a.Rk.lte]:s,[a.Rk.gt]:l,[a.Rk.gte]:o}=this.groupFiltersByKey(this.filters);let c,u,d,p,g,h,v,m;return this.options.joinMatchFilters?(c=this.combineFiltersValues(e,a.KQ.RegexEqual),u=this.combineFiltersValues(t,a.KQ.RegexNotEqual),d=this.combineFiltersValues(n),p=this.combineFiltersValues(r)):(c=this.combineFiltersValues(e),u=this.combineFiltersValues(t),d=this.combineFiltersValues(n),p=this.combineFiltersValues(r)),g=this.combineFiltersValues(i),h=this.combineFiltersValues(s),v=this.combineFiltersValues(l),m=this.combineFiltersValues(o),this.options.debug&&console.info("combined filters",{equalsFilters:c,notEqualsFilters:u,regexEqualFilters:d,regexNotEqualFilters:p}),this.options.joinMatchFilters&&(c&&(d=this.mergeFilters(a.KQ.RegexEqual,c,d),c=this.removeStaleOperators(c,a.KQ.Equal)),u&&(p=this.mergeFilters(a.KQ.RegexNotEqual,u,p),u=this.removeStaleOperators(u,a.KQ.NotEqual))),{equalsFilters:c,notEqualsFilters:u,regexEqualFilters:d,regexNotEqualFilters:p,ltFilters:g,lteFilters:h,gtFilters:v,gteFilters:m}}buildFilter(e,t){const n=[];for(const r in e){const a=[],i=e[r];(0,o.iu)(t)?i.forEach((e=>a.push(this.buildFilterString(r,t,e,"")))):i.forEach((e=>a.push(this.buildFilterString(r,t,e)))),n.push(a.join(` ${this.valueSeparator} `))}return n}buildJoinedFilters(e,t){const n=[];for(const r in e)n.push(this.buildFilterString(r,t,e[r]));return n}removeStaleOperators(e,t){const n={};return Object.keys(e).forEach((r=>{e[r].operator===t&&(n[r]=e[r])})),n}mergeFilters(e,t,n){return Object.keys(t).filter((n=>t[n].operator===e)).map((e=>({values:t[e].values,key:e}))).forEach((r=>{void 0===n&&(n={[r.key]:{values:[],operator:e}}),void 0===n[r.key]&&(n[r.key]={values:[],operator:e}),n[r.key].values.push(...this.mergeCombinedFiltersValues(t[r.key],e))})),n}mergeCombinedFiltersValues(e,t){var n;const r=[];return e.operator===t&&(null===(n=e.values)||void 0===n?void 0:n.length)&&r.push(...e.values),r}joinCombinedFiltersValues(e,t){const n={};for(const r in e)e[r].values.length&&(n[r]=this.combineValues(e[r].values,t));return n}getFilterValues(e){const t={};for(const n in e)e[n].values.length&&(t[n]=e[n].values);return t}combineValues(e,t){return e.join(`${t}`)}combineFiltersValues(e,t){let n={};for(const i in e){if(!e[i].length)continue;const s=(0,c.kR)(e[i][0].operator),l=null!=t?t:s,o=e[i][0];if(n[i]={values:[],operator:l},1===e[i].length){var r;const e=this.escapeFieldValue(o.operator,o.value,null!==(r=o.valueLabels)&&void 0!==r?r:[]);n[i]={operator:s,values:[e]},this.options.debug&&console.info("single value filter",{filter:o,filterString:e})}else{const t=this.escapeFieldValues(i,e,l);var a;void 0===n[i].operator?n[i]={operator:l,values:t}:null===(a=n[i].values)||void 0===a||a.push(...t)}}return n}escapeFieldValues(e,t,n){return t[e].map((e=>{var t;return this.escapeFieldValue(n,e.value,null!==(t=e.valueLabels)&&void 0!==t?t:[])}))}escapeFieldValue(e,t,n){const a=(0,s.zE)(t);return this.options.decodeFilters&&(t=(0,l.bu)({value:t,valueLabels:n}).value),t===s.ZO?(this.options.debug&&console.info("empty variable value, do not escape"),t):a?(this.options.debug&&console.info("ESCAPE: user input - exact selector",{operator:e,value:t,result:r.Go.escapeLabelValueInExactSelector((0,s.Dx)(t))}),r.Go.escapeLabelValueInExactSelector((0,s.Dx)(t))):(0,o.SM)(e)?(this.options.debug&&console.info("ESCAPE: regex selector",{operator:e,value:t}),r.Go.escapeLabelValueInRegexSelector(t)):(this.options.debug&&console.info("ESCAPE: exact selector",{operator:e,value:t}),r.Go.escapeLabelValueInExactSelector(t))}buildFilterString(e,t,n,r='"'){if(n===s.ZO)return`${e}${t}${n}`;const a=`${e}${t}${r}${n}${r}`;return this.options.debug&&console.info("buildDoubleQuotedFilter",{filter:{key:e,operator:t,value:n},filterString:a}),a}groupFiltersByKey(e){const t=e.filter((e=>(0,o.BG)(e.operator)&&!(0,o.SM)(e.operator))),n=e.filter((e=>(0,o.BG)(e.operator)&&(0,o.SM)(e.operator))),r=e.filter((e=>(0,o.Lw)(e.operator)&&!(0,o.SM)(e.operator))),s=e.filter((e=>(0,o.Lw)(e.operator)&&(0,o.SM)(e.operator))),l=e.filter((e=>e.operator===a.w7.gt)),c=e.filter((e=>e.operator===a.w7.gte)),u=e.filter((e=>e.operator===a.w7.lt)),d=e.filter((e=>e.operator===a.w7.lte)),p=(0,i.groupBy)(t,(e=>e.key)),g=(0,i.groupBy)(n,(e=>e.key)),h=(0,i.groupBy)(r,(e=>e.key)),v=(0,i.groupBy)(s,(e=>e.key)),m=(0,i.groupBy)(l,(e=>e.key)),f=(0,i.groupBy)(c,(e=>e.key)),b=(0,i.groupBy)(u,(e=>e.key)),y=(0,i.groupBy)(d,(e=>e.key));return{[a.w7.Equal]:p,[a.w7.RegexEqual]:g,[a.w7.NotEqual]:h,[a.w7.RegexNotEqual]:v,[a.w7.gt]:m,[a.w7.gte]:f,[a.w7.lt]:b,[a.w7.lte]:y}}constructor(e,t={joinMatchFilters:!0,decodeFilters:!1}){d(this,"filters",void 0),d(this,"options",void 0),d(this,"valueSeparator","or"),this.filters=e,this.options=t,this.options.debug||(this.options.debug=(0,u.Rb)())}}},2718:(e,t,n)=>{n.d(t,{EE:()=>i,NO:()=>s,ir:()=>l});var r=n(8531),a=n(2533);const i=(e,t,n)=>{(0,r.reportInteraction)(((e,t)=>`${a.id.replace(/-/g,"_")}_${e}_${t}`)(e,t),n)},s={service_selection:"service_selection",service_details:"service_details",all:"all"},l={[s.service_selection]:{search_services_changed:"search_services_changed",service_selected:"service_selected",aggregated_metrics_toggled:"aggregated_metrics_toggled",add_to_filters:"add_to_filters"},[s.service_details]:{open_in_explore_clicked:"open_in_explore_clicked",action_view_changed:"action_view_changed",add_to_filters_in_breakdown_clicked:"add_to_filters_in_breakdown_clicked",select_field_in_breakdown_clicked:"select_field_in_breakdown_clicked",level_in_logs_volume_clicked:"level_in_logs_volume_clicked",layout_type_changed:"layout_type_changed",search_string_in_logs_changed:"search_string_in_logs_changed",search_string_in_variables_changed:"search_string_in_variables_changed",pattern_removed:"pattern_removed",pattern_selected:"pattern_selected",pattern_field_clicked:"pattern_field_clicked",logs_visualization_toggle:"logs_visualization_toggle",logs_detail_filter_applied:"logs_detail_filter_applied",logs_popover_line_filter:"logs_popover_line_filter",logs_toggle_displayed_field:"logs_toggle_displayed_field",logs_clear_displayed_fields:"logs_clear_displayed_fields",value_breakdown_sort_change:"value_breakdown_sort_change",wasm_not_supported:"wasm_not_supported",change_viz_type:"change_viz_type"},[s.all]:{interval_too_long:"interval_too_long",open_in_explore_menu_clicked:"open_in_explore_menu_clicked"}}},833:(e,t,n)=>{n.d(t,{B:()=>i,n:()=>s});var r=n(3241),a=n.n(r);const i=(e,t)=>{if(typeof e!=typeof t)return!1;const n=new Set(e),r=new Set(t);return n.size===r.size&&a().isEqual(n,r)},s=(e,t)=>typeof e==typeof t&&a().isEqual(e,t)},2854:(e,t,n)=>{n.r(t),n.d(t,{DETECTED_FIELDS_CARDINALITY_NAME:()=>q,DETECTED_FIELDS_NAME_FIELD:()=>G,DETECTED_FIELDS_PARSER_NAME:()=>K,DETECTED_FIELDS_TYPE_NAME:()=>U,WRAPPED_LOKI_DS_UID:()=>H,WrappedLokiDatasource:()=>Q,default:()=>J});var r=n(7781),a=n(8531),i=n(2672),s=n(1269),l=n(9829),o=n(6001),c=n(3143),u=n(2533),d=n(5745),p=n(2344),g=n(8682);function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){h(e,t,n[t])}))}return e}function m(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function f(e){if(function(e){return void 0!==e.targets.find((e=>function(e){return e.trim().length>2&&!function(e,t){let n=!1;return p.K3.parse(e).iterate({enter:({type:e})=>{if(e.id===t)return n=!0,!1}}),n}(e,p.Yw)}(e.expr)))}(e))return!1;if(function(e){return e.targets.find((e=>"instant"===e.queryType))}(e))return!1;for(let n=0;n<e.targets.length;n++){var t;if(null===(t=e.targets[n].expr)||void 0===t?void 0:t.includes("avg_over_time"))return!1}return!0}const b="__stream_shard_number__",y=e=>e.replace("}",`, __stream_shard__=~"${b}"}`),S=(e,t)=>{if(void 0===t||0===t.length)return e.map((e=>m(v({},e),{expr:e.expr.replace(`, __stream_shard__=~"${b}"}`,"}")})));let n=t.join("|");return"-1"===n||1===t.length?(n="-1"===n?"":n,e.map((e=>m(v({},e),{expr:e.expr.replace(`, __stream_shard__=~"${b}"}`,`, __stream_shard__="${n}"}`)})))):e.map((e=>m(v({},e),{expr:e.expr.replace(new RegExp(`${b}`,"g"),n)})))};var w=n(2871);function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){O(e,t,n[t])}))}return e}function E(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function C(e,t,n){const a=(0,r.closestIdx)(t.values[n],e.values);return a<0?0:t.values[n]===e.values[a]&&null!=t.nanos&&null!=e.nanos?t.nanos[n]>e.nanos[a]?a+1:a:t.values[n]>e.values[a]?a+1:a}function k(e,t,n,r,a,i){const s=function(e,t,n,r){return e.nanos&&n.nanos?void 0!==e.values[t]&&e.values[t]===n.values[r]&&void 0!==e.nanos[t]&&e.nanos[t]===n.nanos[r]:void 0!==e.values[t]&&e.values[t]===n.values[r]}(e,n,r,i);return!!s&&(null==t||null==a||void 0!==t.values[n]&&t.values[n]===a.values[i])}function F(e,t,n){const r=t.filter((t=>t.name===e.name));return 1===r.length?r[0]:t[n]}const L="Summary: total bytes processed";function P(e,t){const n=e.find((e=>e.displayName===L)),r=t.find((e=>e.displayName===L));if(null!=r&&null!=n)return[{value:r.value+n.value,displayName:L,unit:n.unit}];const a=null!=r?r:n;return null!=a?[a]:[]}function j(e){return E(x({},e),{fields:e.fields.map((e=>E(x({},e),{values:e.values})))})}function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){_(e,t,n[t])}))}return e}function D(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function N(e,t){const n=e.interpolateVariablesInQueries(t.targets,t.scopedVars).filter((e=>e.expr)).map((e=>D(T({},e),{expr:y(e.expr)})));return function(e,t,n){let a=!1,i={data:[],state:r.LoadingState.Streaming,key:(0,d.A)()},l=null,o=new Map,c=null;const u=(s,d,p,g)=>{let h=g,v=!1;null!=l&&(l.unsubscribe(),l=null);const m=()=>{i.state=a?r.LoadingState.Error:r.LoadingState.Done,s.next(i),s.complete()};if(a)return void m();const f=()=>{const e=Math.min(d+g,p.length);d<p.length&&e<=p.length?u(s,e,p,h):m()},b=e=>{try{if(e&&!function(e){var t,n,r;const a=e.errors?(null!==(n=e.errors[0].message)&&void 0!==n?n:"").toLowerCase():null!==(r=null===(t=e.error)||void 0===t?void 0:t.message)&&void 0!==r?r:"";if(a.includes("timeout"))return!0;if(a.includes("parse error"))throw new Error(a);return!1}(e))return!1}catch(s){var t,n,r,i,l,h;return w.v.error(s,{msg:"sharding retry error",error:null!==(i=null==e||null===(t=e.error)||void 0===t?void 0:t.message)&&void 0!==i?i:"",errors:null!==(l=null==e||null===(n=e.errors)||void 0===n?void 0:n.map((e=>e.message)).join(" | "))&&void 0!==l?l:"",traces:null!==(h=null==e||null===(r=e.traceIds)||void 0===r?void 0:r.join("|"))&&void 0!==h?h:""}),a=!0,!1}if(g>1)return $(`Possible time out, new group size ${g=Math.floor(Math.sqrt(g))}`),v=!0,u(s,d,p,g),!0;var m;const f=null!==(m=o.get(d))&&void 0!==m?m:0;return f>3?(a=!0,!1):(o.set(d,f+1),c=setTimeout((()=>{w.v.info(`Retrying ${d} (${f+1})`),u(s,d,p,g),c=null}),1500*Math.pow(2,f)),v=!0,!0)},y=function(e,t,n){return t===e.length?[-1]:e.slice(t,t+n)}(p,d,g);$(`Querying ${y.join(", ")}`);const O=D(T({},t),{targets:S(n,y)});t.requestId&&(O.requestId=`${t.requestId}_shard_${d}_${g}`),l=e.runQuery(O).subscribe({next:e=>{var t;((null!==(t=e.errors)&&void 0!==t?t:[]).length>0||null!=e.error)&&b(e)||(h=function(e,t,n){return Math.min(t,Math.max(Math.floor(.7*(n-e)),1))}(d+g,function(e,t){var n,r;if(!e.data.length)return t+1;const a=null===(r=e.data[0].meta)||void 0===r||null===(n=r.stats)||void 0===n?void 0:n.find((e=>"Summary: exec time"===e.displayName));if(a){const e=Math.round(a.value);return $(`${a.value}`),e<=1?Math.floor(1.5*t):e<6?Math.ceil(1.1*t):1===t?t:e<20?Math.ceil(.9*t):Math.floor(t/2)}return t}(e,g),p.length),h!==g&&$(`New group size ${h}`),i=function(e,t){if(!e)return E(x({},n=t),{data:n.data.map(j)});var n,a,i;t.data.forEach((t=>{const n=e.data.find((e=>function(e,t){var n,a,i,s,l,o;if(e.refId!==t.refId)return!1;if(null!=e.name&&null!=t.name&&e.name!==t.name)return!1;const c=null===(n=e.meta)||void 0===n?void 0:n.type;if(c!==(null===(a=t.meta)||void 0===a?void 0:a.type))return!1;if(c===r.DataFrameType.TimeSeriesMulti)return function(e,t){const n=e.fields.find((e=>e.type===r.FieldType.number)),a=t.fields.find((e=>e.type===r.FieldType.number));return void 0!==n&&void 0!==a&&(null==e.name&&(e.name=JSON.stringify(n.labels)),null==t.name&&(t.name=JSON.stringify(a.labels)),e.name===t.name)}(e,t);const u=null===(s=e.meta)||void 0===s||null===(i=s.custom)||void 0===i?void 0:i.frameType,d=null===(o=t.meta)||void 0===o||null===(l=o.custom)||void 0===l?void 0:l.frameType;return"LabeledTimeValues"===u&&"LabeledTimeValues"===d||u===d}(e,t)));n?function(e,t){var n,a;const i=e.fields.find((e=>e.type===r.FieldType.time)),s=e.fields.find((e=>e.type===r.FieldType.string&&"id"===e.name)),l=t.fields.find((e=>e.type===r.FieldType.time)),o=t.fields.find((e=>e.type===r.FieldType.string&&"id"===e.name));if(!i||!l)return void w.v.error(new Error("Time fields not found in the data frames"));var c;const u=null!==(c=null==l?void 0:l.values.slice(0))&&void 0!==c?c:[],d=Math.max(e.fields.length,t.fields.length);for(let n=0;n<u.length;n++){const a=C(i,l,n),c=k(i,s,a,l,o,n);for(let i=0;i<d;i++){if(!e.fields[i])continue;const s=F(e.fields[i],t.fields,i);if(s)if(c){if(e.fields[i].type===r.FieldType.time)continue;var p;e.fields[i].type===r.FieldType.number?e.fields[i].values[a]=(null!==(p=e.fields[i].values[a])&&void 0!==p?p:0)+s.values[n]:e.fields[i].type===r.FieldType.other?"object"==typeof s.values[n]?e.fields[i].values[a]=x({},e.fields[i].values[a],s.values[n]):null!=s.values[n]&&(e.fields[i].values[a]=s.values[n]):e.fields[i].values[a]=s.values[n]}else if(void 0!==s.values[n]){var g,h;e.fields[i].values.splice(a,0,s.values[n]),s.nanos&&(e.fields[i].nanos=null!==(h=e.fields[i].nanos)&&void 0!==h?h:new Array(e.fields[i].values.length-1).fill(0),null===(g=e.fields[i].nanos)||void 0===g||g.splice(a,0,s.nanos[n]))}}}var v,m;e.length=e.fields[0].values.length,e.meta=E(x({},e.meta),{stats:P(null!==(v=null===(n=e.meta)||void 0===n?void 0:n.stats)&&void 0!==v?v:[],null!==(m=null===(a=t.meta)||void 0===a?void 0:a.stats)&&void 0!==m?m:[])})}(n,t):e.data.push(j(t))}));const s=[...null!==(a=e.errors)&&void 0!==a?a:[],...null!==(i=t.errors)&&void 0!==i?i:[]];var l;s.length>0&&(e.errors=s);const o=null!==(l=e.error)&&void 0!==l?l:t.error;var c,u;null!=o&&(e.error=o);const d=[...null!==(c=e.traceIds)&&void 0!==c?c:[],...null!==(u=t.traceIds)&&void 0!==u?u:[]];return d.length>0&&(e.traceIds=d),e}(i,e))},complete:()=>{v||(i.data.length&&s.next(i),f())},error:e=>{w.v.error(e,{msg:"failed to shard"}),s.next(i),b()||f()}})},h=n=>{l=e.query(t).subscribe({next:e=>{i=e},complete:()=>{n.next(i)},error:e=>{w.v.error(e,{msg:"runNonSplitRequest subscription error"}),n.error(i)}})},v=new s.Observable((r=>{const i=(e=>{const t=(0,g.QH)(e,[p.MD]);return t.length>0?e.substring(t[0].from,t[0].to).replace(`, __stream_shard__=~"${b}"}`,"}"):""})(n[0].expr);return(0,g.T0)(i)?(e.languageProvider.fetchLabelValues("__stream_shard__",{timeRange:t.range,streamSelector:i||void 0}).then((e=>{const t=e.map((e=>parseInt(e,10)));t&&t.length?(t.sort(((e,t)=>t-e)),$(`Querying ${t.join(", ")} shards`),u(r,0,t,function(e){return Math.floor(Math.sqrt(e.length))}(t))):(w.v.warn("Shard splitting not supported. Issuing a regular query."),h(r))})).catch((e=>{w.v.error(e,{msg:"failed to fetch label values for __stream_shard__"}),h(r)})),()=>{a=!0,c&&clearTimeout(c),null!=l&&(l.unsubscribe(),l=null)}):($(`Skipping invalid selector: ${i}`),void r.complete())}));return v}(e,t,n)}const I=Boolean(localStorage.getItem(`${u.id}.sharding_debug_enabled`));function $(e){I&&console.log(e)}var A=n(8831),M=n(7918);function B(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}function R(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){B(i,r,a,s,l,"next",e)}function l(e){B(i,r,a,s,l,"throw",e)}s(void 0)}))}}function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function W(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){V(e,t,n[t])}))}return e}function z(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const H="wrapped-loki-ds-uid",G="name",q="cardinality",K="parser",U="type";class Q extends i.UU{query(e){return new s.Observable((t=>{var n;if(!(null===(n=e.scopedVars)||void 0===n?void 0:n.__sceneObject))throw new Error("Scene object not found in request");var r=this;(0,a.getDataSourceSrv)().get((0,l.U4)(e.scopedVars.__sceneObject.valueOf())).then(function(){var n=R((function*(n){var i;if(!(n instanceof a.DataSourceWithBackend&&"interpolateString"in n&&"getTimeRangeParams"in n))throw new Error("Invalid datasource!");e.targets=null===(i=e.targets)||void 0===i?void 0:i.map((e=>(e.datasource=n,e)));const s=new Set;if(e.targets.forEach((e=>{var t;s.add(null!==(t=e.resource)&&void 0!==t?t:"")})),1!==s.size)throw new Error("A request cannot contain queries to multiple endpoints");switch(e.targets[0].resource){case"volume":yield r.getVolume(e,n,t);break;case"patterns":yield r.getPatterns(e,n,t);break;case"detected_labels":yield r.getDetectedLabels(e,n,t);break;case"detected_fields":yield r.getDetectedFields(e,n,t);break;case"labels":yield r.getLabels(e,n,t);break;default:r.getData(e,n,t)}}));return function(e){return n.apply(this,arguments)}}())}))}getData(e,t,n){const r=a.config.featureToggles.exploreLogsShardSplitting,i=z(W({},e),{targets:t.interpolateVariablesInQueries(e.targets,e.scopedVars).map((e=>z(W({},e),{resource:void 0,expr:(0,M.VT)(e.expr)})))});return(!1!==f(i)&&r?N(t,i):t.query(i)).subscribe(n),n}getPatterns(e,t,n){var a=this;return R((function*(){const i=e.targets.filter((e=>"patterns"===e.resource));if(1!==i.length)throw new Error("Patterns query can only have a single target!");const{interpolatedTarget:s,expression:l}=a.interpolate(t,i,e);n.next({data:[],state:r.LoadingState.Loading});try{var o;const a=t.getResource("patterns",{query:l,start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString(),step:e.interval},{requestId:null!==(o=e.requestId)&&void 0!==o?o:"patterns",headers:{"X-Query-Tags":`Source=${A.s_}`}}),i=yield a,u=null==i?void 0:i.data;let d=-1/0,p=0;var c;const g=null!==(c=null==u?void 0:u.map((e=>{const t=[],n=[];let a=0;return e.samples.forEach((([e,r])=>{t.push(1e3*e),n.push(r),r>d&&(d=r),r<p&&(p=r),r>d&&(d=r),r<p&&(p=r),a+=r})),(0,r.createDataFrame)({refId:s.refId,name:e.pattern,fields:[{name:"time",type:r.FieldType.time,values:t,config:{}},{name:e.pattern,type:r.FieldType.number,values:n,config:{}}],meta:{preferredVisualisationType:"graph",custom:{sum:a}}})})))&&void 0!==c?c:[];g.sort(((e,t)=>{var n,r,a,i;return(null===(r=t.meta)||void 0===r||null===(n=r.custom)||void 0===n?void 0:n.sum)-(null===(i=e.meta)||void 0===i||null===(a=i.custom)||void 0===a?void 0:a.sum)})),n.next({data:g,state:r.LoadingState.Done})}catch(e){n.next({data:[],state:r.LoadingState.Error})}return n}))()}interpolate(e,t,n){const r=e.interpolateVariablesInQueries(t,n.scopedVars);if(!r.length)throw new Error("Datasource failed to interpolate query!");const a=r[0];return{interpolatedTarget:a,expression:(0,M.VT)(a.expr)}}getDetectedLabels(e,t,n){var a=this;return R((function*(){const i=e.targets.filter((e=>"detected_labels"===e.resource));if(1!==i.length)throw new Error("Detected labels query can only have a single target!");let{interpolatedTarget:s,expression:l}=a.interpolate(t,i,e);"{}"===l&&(l=""),n.next({data:[],state:r.LoadingState.Loading});try{var c,u,d;const a=null===(u=(yield t.getResource("detected_labels",{query:l,start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString()},{requestId:null!==(d=e.requestId)&&void 0!==d?d:"detected_labels",headers:{"X-Query-Tags":`Source=${A.s_}`}})).detectedLabels)||void 0===u||null===(c=u.filter((e=>!o.rm.includes(e.label))))||void 0===c?void 0:c.sort(((e,t)=>(0,o.p_)(e,t))),i=null==a?void 0:a.map((e=>({name:e.label,values:[e.cardinality]}))),p=(0,r.createDataFrame)({refId:s.refId,fields:null!=i?i:[]});n.next({data:[p],state:r.LoadingState.Done})}catch(e){n.next({data:[],state:r.LoadingState.Error})}return n}))()}getDetectedFields(e,t,n){var a=this;return R((function*(){const i=e.targets.filter((e=>"detected_fields"===e.resource));if(1!==i.length)throw new Error("Detected fields query can only have a single target!");n.next({data:[],state:r.LoadingState.Loading});const{interpolatedTarget:s,expression:l}=a.interpolate(t,i,e);try{var c,u;const a=yield t.getResource("detected_fields",{query:l,start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString()},{requestId:null!==(u=e.requestId)&&void 0!==u?u:"detected_fields",headers:{"X-Query-Tags":`Source=${A.s_}`}}),i={name:G,type:r.FieldType.string,values:[],config:{}},d={name:q,type:r.FieldType.number,values:[],config:{}},p={name:K,type:r.FieldType.string,values:[],config:{}},g={name:U,type:r.FieldType.string,values:[],config:{}};null===(c=a.fields)||void 0===c||c.forEach((e=>{var t;o.$R.includes(e.label)||(i.values.push(e.label),d.values.push(e.cardinality),p.values.push((null===(t=e.parsers)||void 0===t?void 0:t.length)?e.parsers.join(", "):"structuredMetadata"),g.values.push(e.type))}));const h=(0,r.createDataFrame)({refId:s.refId,fields:[i,d,p,g]});n.next({data:[h],state:r.LoadingState.Done})}catch(e){w.v.error(e,{msg:"Detected fields error"}),n.next({data:[],state:r.LoadingState.Error})}return n}))()}getVolume(e,t,n){return R((function*(){if(1!==e.targets.length)throw new Error("Volume query can only have a single target!");const a=e.targets[0],i=a.primaryLabel;if(!i)throw new Error("Primary label is required for volume queries!");const s=t.interpolateVariablesInQueries([a],e.scopedVars),l=(0,M.VT)(s[0].expr.replace(".*.*",".+"));n.next({data:[],state:r.LoadingState.Loading});try{var o,u,d;const a=yield t.getResource("index/volume",{query:l,start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString(),limit:5e3},{requestId:null!==(d=e.requestId)&&void 0!==d?d:"volume",headers:{"X-Query-Tags":`Source=${A.s_}`}});null==a||a.data.result.sort(((e,t)=>{const n=e.value[1],r=t.value[1];return Number(r)-Number(n)}));const s=(0,r.createDataFrame)({fields:[{name:c.OX,values:null==a||null===(o=a.data.result)||void 0===o?void 0:o.map((e=>e.metric[i]))},{name:"volume",values:null==a||null===(u=a.data.result)||void 0===u?void 0:u.map((e=>Number(e.value[1])))}]});n.next({data:[s]})}catch(e){w.v.error(e),n.next({data:[],state:r.LoadingState.Error})}return n.complete(),n}))()}getLabels(e,t,n){return R((function*(){if(1!==e.targets.length)throw new Error("Volume query can only have a single target!");try{var a;const i=yield t.getResource("labels",{start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString()},{requestId:null!==(a=e.requestId)&&void 0!==a?a:"labels",headers:{"X-Query-Tags":`Source=${A.s_}`}}),s=(0,r.createDataFrame)({fields:[{name:"labels",values:null==i?void 0:i.data}]});n.next({data:[s],state:r.LoadingState.Done})}catch(e){n.next({data:[],state:r.LoadingState.Error})}return n.complete(),n}))()}testDatasource(){return Promise.resolve({status:"success",message:"Data source is working",title:"Success"})}constructor(e,t){super(e,t)}}const J=function(){i.Go.registerRuntimeDataSource({dataSource:new Q("wrapped-loki-ds",H)})}},1293:(e,t,n)=>{n.d(t,{O:()=>o,m:()=>l});var r=n(3143),a=n(7097),i=n(4750),s=n(2871);function l(e,t,n=!0){const s=(0,i.ir)(e);let l="";n&&t===r.e4&&(l=`| ${r.e4} != ""`);const o=s.state.filters,c=(0,a.k$)(s);if(o.length){if("mixed"===c)return`sum(count_over_time({${r.S1}} ${l} ${r.S6} ${r.sC} ${r.rl} ${r.YN} ${r.Oc} [$__auto])) by (${t})`;if("json"===c)return`sum(count_over_time({${r.S1}} ${l} ${r.S6} ${r.sC} ${r.rl} ${r.VL} ${r.Oc} [$__auto])) by (${t})`;if("logfmt"===c)return`sum(count_over_time({${r.S1}} ${l} ${r.S6} ${r.sC} ${r.rl} ${r.mF} ${r.Oc} [$__auto])) by (${t})`}return`sum(count_over_time({${r.S1}} ${l} ${r.S6} ${r.sC} ${r.rl} ${r.Oc} [$__auto])) by (${t})`}function o(e){switch(e){case r._Y:return r.Sy;case r.sL:return r.fJ;default:const t=new Error(`Unknown variable type: ${e}`);throw s.v.error(t,{variableType:e,msg:`getFieldsTagValuesExpression: Unknown variable type: ${e}`}),t}}},9055:(e,t,n)=>{n.d(t,{_J:()=>u,wy:()=>d});var r=n(2672),a=n(8538),i=n(866),s=n(1105),l=n(4750),o=n(227);function c(e){const t=r.jh.getAncestor(e,a.P);r.jh.findAllObjects(t,(e=>e instanceof i.p)).forEach((e=>e.forceRender())),r.jh.findDescendents(t,s.y).forEach((e=>e.forceRender()))}function u(e,t,n){const r=(0,l.S9)(n).getValue();(0,o.OB)(r,e,t),c(n)}function d(e,t,n){const r=(0,l.S9)(n).getValue();(0,o.cC)(r,e,t),c(n)}},7097:(e,t,n)=>{n.d(t,{JI:()=>E,Jl:()=>k,OE:()=>O,Qg:()=>y,Ri:()=>S,Zp:()=>w,k$:()=>x,ph:()=>C});var r=n(7781),a=n(2007),i=n(2672),s=n(558),l=n(3143),o=n(5183),c=n(1269),u=n(2254),d=n(4750),p=n(2871),g=n(7085),h=n(581),v=n(7232);const m=e=>{if(e&&Object.values(r.ReducerID).includes(e))return e};function f(e){switch(e){case"json":return"json";case"logfmt":return"logfmt";case"":case"structuredMetadata":return"structuredMetadata";default:return"mixed"}}function b(e){switch(e){case"int":case"float":case"duration":case"boolean":case"bytes":return e;default:return"string"}}function y(e){var t;const n=new Set(null!==(t=null==e?void 0:e.map((e=>e.toString())))&&void 0!==t?t:[]);n.delete("structuredMetadata");const r=Array.from(n);return 1===r.length?f(r[0]):0===n.size?"structuredMetadata":"mixed"}function S(e,t){var n;const r=(0,u.rD)(t),a=null==r?void 0:r.fields[2],i=null==r?void 0:r.fields[0],s=null==i?void 0:i.values.indexOf(e);var l;const o=void 0!==s&&-1!==s?f(null!==(l=null==a||null===(n=a.values)||void 0===n?void 0:n[s])&&void 0!==l?l:""):void 0;return void 0===o?(p.v.warn("missing parser, using mixed format for",{fieldName:e}),"mixed"):o}function w(e,t,n,r,l){return(u,d)=>{const p=m(r.state.sortBy),h=i.d0.timeseries().setOption("legend",{showLegend:!1}).setCustomFieldConfig("fillOpacity",9).setTitle(e(u)).setData(new i.Es({transformations:[()=>function(e){return t=>t.pipe((0,c.map)((()=>[e])))}(u)]})).setOverrides(o.jC).setMenu(new g.GD({investigationOptions:{frame:u,fieldName:e(u),labelName:l}})).setHeaderActions([new s.oR({frame:u,variableName:n})]);return t===a.DrawStyle.Bars&&h.setCustomFieldConfig("stacking",{mode:a.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setOverrides(o.jC).setCustomFieldConfig("drawStyle",a.DrawStyle.Bars),p&&(h.setOption("legend",{showLegend:!0,calcs:[p]}),h.setDisplayName(" ")),new i.xK({body:h.build()})}}function O(e,t,n){const r=e?(0,h.E)(t,e):v.H.Parsed;if(r)return function(e,t){switch(e){case v.H.Indexed:return l.MB;case v.H.Parsed:return l.mB;case v.H.StructuredMetadata:return t===l.e4?l._Y:l._P;default:{const n=new Error(`Invalid label type for ${t}`);throw p.v.error(n,{type:e,msg:`Invalid label type for ${t}`}),n}}}(r,t);const a=S(t,n);return"structuredMetadata"===a?l._P:(p.v.warn("unable to determine label variable, falling back to parsed field",{key:t,parserForThisField:null!=a?a:""}),l.mB)}function x(e){return y(e.state.filters.map((e=>(0,d.bu)(e).parser)))}function E(e){return"duration"===e||"bytes"===e||"float"===e}function C(e,t){var n;const r=null==t?void 0:t.fields[0],a=null==t?void 0:t.fields[3],i=null==r?void 0:r.values.indexOf(e);return void 0!==i&&-1!==i?b(null==a||null===(n=a.values)||void 0===n?void 0:n[i]):void 0}function k(e,t,n){var r,a;const i=null==n?void 0:n.fields[2],s=null==n?void 0:n.fields[0],l=null==n?void 0:n.fields[3],o=null==s?void 0:s.values.indexOf(e),c=void 0!==o&&-1!==o?f(null==i||null===(r=i.values)||void 0===r?void 0:r[o]):"mixed",u=void 0!==o&&-1!==o?b(null==l||null===(a=l.values)||void 0===a?void 0:a[o]):void 0,p=t.state.filters.map((e=>{var t;const n=null==s?void 0:s.values.indexOf(e.key),r=(0,d.bu)(e);if(r.parser)return r.parser;var a;const l=void 0!==n&&-1!==n?f(null!==(a=null==i||null===(t=i.values)||void 0===t?void 0:t[n])&&void 0!==a?a:"mixed"):void 0;return null!=l?l:"mixed"}));let g="",h="";return"structuredMetadata"===c?h=`| ${e}!=""`:g=`| ${e}!=""`,function(e,t){return t.fieldType&&["bytes","duration"].includes(t.fieldType)?`avg_over_time(${(0,d.DX)(t)} | unwrap `+t.fieldType+`(${e}) | __error__="" [$__auto]) by ()`:t.fieldType&&"float"===t.fieldType?`avg_over_time(${(0,d.DX)(t)} | unwrap `+e+' | __error__="" [$__auto]) by ()':`sum by (${e}) (count_over_time(${(0,d.DX)(t)} [$__auto]))`}(e,{structuredMetadataToAdd:h,fieldExpressionToAdd:g,parser:y([...p,c]),fieldType:u})}},6001:(e,t,n)=>{n.d(t,{$R:()=>s,OH:()=>c,dD:()=>i,p_:()=>a,rd:()=>o,rm:()=>l});var r=n(3143);function a(e,t){return 1===e.cardinality?1:1===t.cardinality?-1:e.cardinality-t.cardinality}function i(e){const t=[...e];e.includes(r.e4)||t.unshift(r.e4);const n=t.map((e=>({label:e,value:String(e)})));return[{label:"All",value:r.To},...n]}const s=["level_extracted",r.e4,"level"],l=["__aggregated_metric__","__stream_shard__"];function o(e){const t=[...e].map((e=>({label:e,value:String(e)})));return[{label:"All",value:r.To},...t]}function c(e){var t;return((0,r.zE)(e.value)?(0,r.Dx)(e.value):e.value)===(null===(t=e.valueLabels)||void 0===t?void 0:t[0])}},4119:(e,t,n)=>{n.d(t,{d:()=>l,o:()=>o});var r=n(3143),a=n(7097),i=n(7918),s=n(4750);const l="repeat(auto-fit, minmax(400px, 1fr))";function o(e,t,n){let l="",o="";const c=(0,s.ir)(e),u=(0,a.k$)(c);return n&&n!==r.e4?l=` ,${n} != ""`:n&&n===r.e4&&(o=` | ${n} != ""`),(0,i.l)(`sum(count_over_time(${(0,s.DX)({labelExpressionToAdd:l,structuredMetadataToAdd:o,parser:u})} [$__auto])) by (${t})`,{legendFormat:`{{${t}}}`,refId:"LABEL_BREAKDOWN_VALUES"})}},1383:(e,t,n)=>{n.d(t,{Ex:()=>d,H7:()=>u,PE:()=>g,de:()=>c,pC:()=>o});var r=n(2007),a=n(3143),i=n(558),s=n(4750),l=n(5111);function o(e,t,n,a){if(n===r.SeriesVisibilityChangeMode.ToggleSelection){const n=null!=t?t:[];return 1===n.length&&n.includes(e)?[]:[e]}let i=(null==t?void 0:t.length)?t:a;return i.includes(e)?i.filter((t=>t!==e)):[...i,e]}function c(e){return e.map((e=>{var t;return null!==(t=u(e))&&void 0!==t?t:"logs"}))}function u(e){var t;const n=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!n)return null;const r=Object.keys(n);return 0===r.length?null:n[r[0]]}function d(e,t){const n=(0,s.iw)(t),r=n.state.filters.filter((e=>(0,l.BG)(e.operator))).map((e=>e.value.split("|").map(p))).join("|"),a=n.state.filters.filter((e=>(0,l.Lw)(e.operator))).map((e=>e.value.split("|").map(p))).join("|");return e.filter((e=>!a.includes(e)&&(0===r.length||r.includes(e))))}function p(e){return'""'===e?"logs":e}function g(e,t){const n=(0,s.iw)(t),r=0===n.state.filters.length,o=n.state.filters.find((t=>t.value.split("|").includes(e)&&(0,l.BG)(t.operator)));let c;return"logs"===e&&(e='""'),r||!o?((0,i.PT)(a.e4,e,"include",t,a._Y),c="add"):((0,i.Qt)(a.e4,e,"toggle",t,a._Y),c="remove"),c}},9186:(e,t,n)=>{n.d(t,{Il:()=>g,Os:()=>d,bz:()=>c,fF:()=>p,hy:()=>f,po:()=>h,z5:()=>v});var r=n(7781);function a(e,t,n){const r=e.getFieldByName(t);if(void 0!==r)return r.type===n?r:void 0}const i="timestamp",s="body",l="severity",o="id",c="labels";function u(e){const t={};return Object.entries(e).forEach((([e,n])=>{t[e]="string"==typeof n?n:JSON.stringify(n)})),t}function d(e){var t;return(null===(t=e.meta)||void 0===t?void 0:t.type)===r.DataFrameType.LogLines?function(e){const t=new r.FieldCache(e),n=a(t,i,r.FieldType.time),d=a(t,s,r.FieldType.string);if(void 0===n||void 0===d)return null;var p;const g=null!==(p=a(t,l,r.FieldType.string))&&void 0!==p?p:null;var h;const v=null!==(h=a(t,o,r.FieldType.string))&&void 0!==h?h:null;var m;const f=null!==(m=a(t,c,r.FieldType.other))&&void 0!==m?m:null,b=null===f?null:f.values,y=t.fields.filter(((e,t)=>t!==n.index&&t!==d.index&&t!==(null==g?void 0:g.index)&&t!==(null==v?void 0:v.index)&&t!==(null==f?void 0:f.index)));return{raw:e,timeField:n,bodyField:d,severityField:g,idField:v,getLogFrameLabels:()=>b,timeNanosecondField:null,getLogFrameLabelsAsLabels:()=>null!==b?b.map(u):null,getLabelFieldName:()=>null!==f?f.name:null,extraFields:y}}(e):function(e){const t=new r.FieldCache(e),n=t.getFirstFieldOfType(r.FieldType.time),a=t.getFirstFieldOfType(r.FieldType.string);if(void 0===n||void 0===a)return null;var i;const s=null!==(i=t.getFieldByName("tsNs"))&&void 0!==i?i:null;var l;const o=null!==(l=t.getFieldByName("level"))&&void 0!==l?l:null;var c;const d=null!==(c=t.getFieldByName("id"))&&void 0!==c?c:null,[p,g]=function(e,t,n){const a=e.getFieldByName("labels");if(void 0!==a&&a.type===r.FieldType.other){const e=a.values.map(u);return[a,()=>e]}return[null,()=>function(e,t){const n=e.labels;if(void 0!==n){const e=new Array(t);return e.fill(n),e}return null}(t,n.length)]}(t,a,e),h=t.fields.filter(((e,t)=>t!==n.index&&t!==a.index&&t!==(null==s?void 0:s.index)&&t!==(null==o?void 0:o.index)&&t!==(null==d?void 0:d.index)&&t!==(null==p?void 0:p.index)));return{timeField:n,bodyField:a,timeNanosecondField:s,severityField:o,idField:d,getLogFrameLabels:g,getLogFrameLabelsAsLabels:g,getLabelFieldName:()=>{var e;return null!==(e=null==p?void 0:p.name)&&void 0!==e?e:null},extraFields:h,raw:e}}(e)}function p(e){var t;return null!==(t=null==e?void 0:e.timeField.name)&&void 0!==t?t:i}function g(e){var t;return null!==(t=null==e?void 0:e.bodyField.name)&&void 0!==t?t:s}function h(e){var t,n;return null!==(n=null==e||null===(t=e.idField)||void 0===t?void 0:t.name)&&void 0!==n?n:o}function v(e){var t;let n=0,a=0;const i=null===(t=e[0])||void 0===t?void 0:t.fields.find((e=>e.type===r.FieldType.time));if(i){const e=[...i.values].sort(),t=e[0]<e[e.length-1];n=t?e[0]:e[e.length-1],a=t?e[e.length-1]:e[0]}return{start:n,end:a}}const m="Visible range";function f(e,t){const n=(0,r.arrayToDataFrame)([{time:e,timeEnd:t,isRegion:!0,text:"Range from oldest to newest logs in display",color:"rgba(58, 113, 255, 0.3)"}]);return n.name=m,n.meta={dataTopic:r.DataTopic.Annotations},n}},6949:(e,t,n)=>{let r;function a(){r||(r=new i)}n.d(t,{JO:()=>s,rX:()=>a});class i{getServiceSceneState(){return this.serviceSceneState}setPatternsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.patternsCount=e}setLabelsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.labelsCount=e}setFieldsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.fieldsCount=e}setServiceSceneState(e){this.serviceSceneState={patternsCount:e.patternsCount,labelsCount:e.labelsCount,fieldsCount:e.fieldsCount,loading:e.loading,logsCount:e.logsCount,totalLogsCount:e.totalLogsCount}}constructor(){var e,t;t=void 0,(e="serviceSceneState")in this?Object.defineProperty(this,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):this[e]=t}}function s(){return r}},8835:(e,t,n)=>{n.d(t,{Ns:()=>S,Vt:()=>b,ad:()=>y,fg:()=>m,jY:()=>f});var r=n(8538),a=n(3143),i=n(6949),s=n(8531),l=n(892),o=n(2672),c=n(7781),u=n(7608),d=n(2871),p=n(8831);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let h;function v(e,t){return c.urlUtil.renderUrl(e,function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}({},Object.entries(c.urlUtil.getUrlSearchParams()).reduce(((e,[t,n])=>(l.tm.includes(t)&&(e[t]=n),e)),{}),e)}(t))}function m(e,t,n){const s=o.jh.getAncestor(n,r.P);if(s){var c,g;const r=null===(c=s.state.routeMatch)||void 0===c?void 0:c.params.labelName,o=null===(g=s.state.routeMatch)||void 0===g?void 0:g.params.labelValue;if(r&&o){let s=function(e,t,n,r="service"){return e===a.To&&t===l._J.label?(0,p._F)(`${l.G3.explore}/${r}/${(0,u.uu)(n)}/${l.G3.labels}`):e===a.To&&t===l._J.field?(0,p._F)(`${l.G3.explore}/${r}/${(0,u.uu)(n)}/${l.G3.fields}`):(0,p._F)(`${l.G3.explore}/${r}/${(0,u.uu)(n)}/${t}/${(0,u.uu)(e)}`)}(t,e,o,r);const c=v(s);return n&&(0,i.JO)().setServiceSceneState(n.state),void y(c)}d.v.warn("missing url params",{urlLabelName:null!=r?r:"",urlLabelValue:null!=o?o:""})}}function f(e,t){y(v(l.bw.logs(t,e)))}function b(e,t,n){var a,s;const c=o.jh.getAncestor(t,r.P),d=null===(a=c.state.routeMatch)||void 0===a?void 0:a.params.labelValue,g=null===(s=c.state.routeMatch)||void 0===s?void 0:s.params.labelName;if(d){const r=v((0,p._F)(`${l.G3.explore}/${g}/${(0,u.uu)(d)}/${e}`),n);t&&(0,i.JO)().setServiceSceneState(t.state),y(r)}}function y(e){h=e,s.locationService.push(e)}function S(){const e=s.locationService.getLocation(),t=(0,l.qe)(l.bw.explore()),n=e.pathname+e.search,r=s.locationService.getSearch();t===n||n.includes(t)||(r.get("var-filters")?y(t):(h&&s.locationService.replace(h),s.locationService.push(t)))}},5183:(e,t,n)=>{n.d(t,{rS:()=>C,FH:()=>E,HF:()=>k,jC:()=>b,ZC:()=>y,Cw:()=>w});var r=n(7781),a=n(2672),i=n(1269),s=n(3321),l=n(2854);class o extends a.dt{runQueries(){const e=a.jh.getTimeRange(this);this.runWithTimeRange(e)}constructor(e){super(e)}}var c=n(2007),u=n(1383),d=n(581),p=n(2254),g=n(9254);function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){h(e,t,n[t])}))}return e}function m(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const f="logs";function b(e){e.matchFieldsWithName("info").overrideColor({mode:"fixed",fixedColor:"semi-dark-green"}),e.matchFieldsWithName("debug").overrideColor({mode:"fixed",fixedColor:"semi-dark-blue"}),e.matchFieldsWithName("error").overrideColor({mode:"fixed",fixedColor:"semi-dark-red"}),e.matchFieldsWithName("warn").overrideColor({mode:"fixed",fixedColor:"semi-dark-orange"}),e.matchFieldsWithName("logs").overrideColor({mode:"fixed",fixedColor:"darkgray"})}function y(e){return e.setCustomFieldConfig("stacking",{mode:c.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("axisSoftMin",0).setCustomFieldConfig("drawStyle",c.DrawStyle.Bars).setOverrides(b)}function S(e,t){t.match({id:r.FieldMatcherID.byNames,options:{mode:"exclude",names:e,prefix:"All except:",readOnly:!0}}).overrideCustomFieldConfig("hideFrom",{legend:!1,tooltip:!1,viz:!0});const n=t.build();n[n.length-1].__systemRef="hideSeriesFrom"}function w(e,t,n){const r=(0,u.Ex)((0,u.de)(t),n);if(null==r?void 0:r.length){const t=y(a.No.timeseries()).setOverrides(S.bind(null,r));t instanceof a.OS&&e.onFieldConfigChange(t.build(),!0)}}function O(){return e=>e.pipe((0,i.map)((e=>e.map(((t,n)=>m(v({},t),{fields:t.fields.map(((n,a)=>{if(n.type===r.FieldType.time)return n;const i=(0,r.getFieldDisplayName)(n,t,e);return m(v({},n),{config:m(v({},n.config),{displayName:i,color:{mode:r.FieldColorModeId.PaletteClassicByName}})})}))}))))))}function x(){return e=>e.pipe((0,i.map)((e=>e.map((e=>(e.fields.length<2||e.fields[1].config.displayNameFromDS||(e.fields[1].config.displayNameFromDS=f),e))).sort(((e,t)=>{if(e.fields.length<2||t.fields.length<2)return 0;const n=e.fields[1].config.displayNameFromDS,r=(null==n?void 0:n.includes("error"))?4:(null==n?void 0:n.includes("warn"))?3:(null==n?void 0:n.includes("info"))?2:1,a=t.fields[1].config.displayNameFromDS;return r-((null==a?void 0:a.includes("error"))?4:(null==a?void 0:a.includes("warn"))?3:(null==a?void 0:a.includes("info"))?2:1)})))))}function E(e){return new o({datasource:{uid:l.WRAPPED_LOKI_DS_UID},queries:e})}function C(e,t){const n=e.find((e=>{var t;return null===(t=e.legendFormat)||void 0===t?void 0:t.toLowerCase().includes("level")})),r=e.find((e=>e.refId===p.DS||e.refId===p.AA));return n?new a.Es({$data:k(v({datasource:{uid:l.WRAPPED_LOKI_DS_UID},queries:e},t)),transformations:[x]}):r?(e=e.map((e=>m(v({},e),{get direction(){return((0,g.PY)()||(0,g.zQ)())===s.uH.Descending?d.t.Backward:d.t.Forward}}))),k(v({datasource:{uid:l.WRAPPED_LOKI_DS_UID},queries:e},t))):new a.Es({$data:k(v({datasource:{uid:l.WRAPPED_LOKI_DS_UID},queries:e},t)),transformations:[O]})}function k(e){return new a.dt(v({datasource:{uid:l.WRAPPED_LOKI_DS_UID},queries:[]},e))}},8831:(e,t,n)=>{n.d(t,{Gy:()=>a,_F:()=>i,s_:()=>r});const r=n(2533).id,a=`/a/${r}`;function i(e){return`${a}/${e}`}},7918:(e,t,n)=>{n.d(t,{$k:()=>v,BM:()=>p,CY:()=>O,E3:()=>S,PP:()=>b,VT:()=>C,VW:()=>m,ZX:()=>w,_q:()=>y,by:()=>k,c0:()=>f,l:()=>g,sT:()=>E,vC:()=>x});var r=n(3143),a=n(8831),i=n(2672),s=n(4793),l=n(4916),o=n(7063);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){c(e,t,n[t])}))}return e}function d(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const p=(e,t,n,a)=>d(u(d(u({},h),{resource:t,refId:t}),n),{datasource:{uid:r.gR},expr:e,primaryLabel:a}),g=(e,t)=>d(u({},h,t),{expr:e}),h={refId:"A",queryType:"range",editorMode:"code",supportingQueryType:a.s_},v=(e,t,n,r)=>p(e,t,u({},r),n);function m(e){return new o.K(e).getLabelsExpr()}function f(e){var t,n,a;return e.value?{value:(0,r.OQ)(e.value),valueLabels:[null!==(t=e.label)&&void 0!==t?t:e.value]}:{value:e.value,valueLabels:[null!==(a=null!==(n=e.label)&&void 0!==n?n:e.value)&&void 0!==a?a:""]}}function b(e,t){var n,a,i;const s={value:null!==(a=e.value)&&void 0!==a?a:"",parser:null!==(i=null==t||null===(n=t.meta)||void 0===n?void 0:n.parser)&&void 0!==i?i:"mixed"};var l,o;return"structuredMetadata"===s.parser?{value:(0,r.OQ)(s.value),valueLabels:[null!==(l=e.label)&&void 0!==l?l:s.value]}:{value:(0,r.OQ)(JSON.stringify(s)),valueLabels:[null!==(o=e.label)&&void 0!==o?o:s.value]}}function y(e){return new o.K(e).getLevelsExpr()}function S(e){return new o.K(e).getMetadataExpr()}function w(e){return new o.K(e).getFieldsExpr()}function O(e){return(0,l.F)(e),e.map((e=>{if(""===e.value)return"";const t=function(e){return e.operator!==s.cK.match&&e.operator!==s.cK.negativeMatch||e.key!==s.ld.caseInsensitive?i.Go.escapeLabelValueInExactSelector(e.value):i.Go.escapeLabelValueInRegexSelector(e.value)}(e);return function(e,t){return e.key===s.ld.caseInsensitive?e.operator===s.cK.negativeRegex||e.operator===s.cK.negativeMatch?`${s.cK.negativeRegex} "(?i)${t}"`:`${s.cK.regex} "(?i)${t}"`:`${e.operator} "${t}"`}(e,t)})).join(" ")}function x(e){return".+"===e?e:"(?i).*"!==e.substring(0,6)?`(?i).*${e}.*`:e}function E(e){return"(?i).*"===e.substring(0,6)&&".*"===e.slice(-2)?e.slice(6).slice(0,-2):e}function C(e){return e.replace(/\s*,\s*}/,"}")}const k=1e3},892:(e,t,n)=>{n.d(t,{FT:()=>y,G3:()=>d,HU:()=>v,KL:()=>m,NX:()=>E,W6:()=>S,XJ:()=>x,Zt:()=>f,_J:()=>p,bw:()=>g,er:()=>w,mC:()=>h,qe:()=>O,tm:()=>b});var r=n(7781),a=n(3143),i=n(8531),s=n(7608),l=n(4750),o=n(2871),c=n(8831);function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var d=function(e){return e.explore="explore",e.logs="logs",e.labels="labels",e.patterns="patterns",e.fields="fields",e}({}),p=function(e){return e.field="field",e.label="label",e}({});const g={explore:()=>(0,c._F)("explore"),logs:(e,t="service")=>(0,c._F)(`explore/${t}/${(0,s.uu)(e)}/logs`),fields:(e,t="service")=>(0,c._F)(`explore/${t}/${(0,s.uu)(e)}/fields`),patterns:(e,t="service")=>(0,c._F)(`explore/${t}/${(0,s.uu)(e)}/patterns`),labels:(e,t="service")=>(0,c._F)(`explore/${t}/${(0,s.uu)(e)}/labels`)},h={label:(e,t="service",n)=>(0,c._F)(`explore/${t}/${(0,s.uu)(e)}/label/${n}`),field:(e,t="service",n)=>(0,c._F)(`explore/${t}/${(0,s.uu)(e)}/field/${n}`)},v={explore:(0,c._F)("explore"),logs:(0,c._F)("explore/:labelName/:labelValue/logs"),fields:(0,c._F)("explore/:labelName/:labelValue/fields"),patterns:(0,c._F)("explore/:labelName/:labelValue/patterns"),labels:(0,c._F)("explore/:labelName/:labelValue/labels")},m={field:(0,c._F)("explore/:labelName/:labelValue/field/:breakdownLabel"),label:(0,c._F)("explore/:labelName/:labelValue/label/:breakdownLabel")},f=["from","to",`var-${a.EY}`,`var-${a.MB}`],b=["from","to","mode","urlColumns","visualizationType","selectedLine","displayedFields","panelState",a.uw,`var-${a.uw}`,`var-${a.EY}`,`var-${a.MB}`,`var-${a.mB}`,`var-${a._Y}`,`var-${a.LI}`,`var-${a.Jg}`,`var-${a.EY}`,`var-${a.WM}`,`var-${a._P}`,`var-${a.NW}`];function y(){const e=i.locationService.getLocation();return e.pathname.slice(e.pathname.lastIndexOf("/")+1,e.pathname.length)}function S(){const e=i.locationService.getLocation(),t=e.pathname.slice(e.pathname.indexOf("/a/grafana-lokiexplore-app/explore")+34+1).split("/");let n=t[0];const r=t[1],s=t[3];return n===a.OX&&(n=a.ky),{labelName:n,labelValue:r,breakdownLabel:s}}function w(){const e=i.locationService.getLocation().pathname.split("/");return e[e.length-2]}function O(e,t){return r.urlUtil.renderUrl(e,function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){u(e,t,n[t])}))}return e}({},Object.entries(r.urlUtil.getUrlSearchParams()).reduce(((e,[t,n])=>(f.includes(t)&&(e[t]=n),e)),{}),e)}(t))}function x(e){return{labelName:e.params.labelName,labelValue:e.params.labelValue,breakdownLabel:e.params.breakdownLabel}}function E(e){const t=(0,l.cR)(e);let{labelName:n,labelValue:r}=S();if(n===a.ky&&(n=a.OX),!t.state.filters.find((e=>e.key===n))){const e=i.locationService.getLocation();o.v.info("invalid primary label name in url",{labelName:n,url:`${e.pathname}${e.search}`})}if(!t.state.filters.find((e=>(0,s.uu)(e.value)===r))){const e=i.locationService.getLocation();o.v.info("invalid primary label value in url",{labelValue:r,url:`${e.pathname}${e.search}`})}}},9829:(e,t,n)=>{n.d(t,{Mq:()=>p,Ti:()=>c,U4:()=>u,UX:()=>m,hJ:()=>g,m0:()=>f,oh:()=>v,u9:()=>d}),n(7781);var r=n(8531),a=n(2672),i=n(3143),s=(n(892),n(8538)),l=n(2871);function o(e,t,n,r,a,i,s){try{var l=e[i](s),o=l.value}catch(e){return void n(e)}l.done?t(o):Promise.resolve(o).then(r,a)}function c(e){return a.jh.getAncestor(e,s.P)}function u(e){return a.jh.interpolate(e,i.gR)}function d(e){return a.jh.interpolate(e,i.SA).replace(/\s+/g," ")}function p(e){return a.jh.interpolate(e,i.FX).replace(/\s+/g," ")}function g(e){return h.apply(this,arguments)}function h(){var e;return e=function*(e){return yield(0,r.getDataSourceSrv)().get(i.gR,{__sceneObject:{value:e}})},h=function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){o(i,r,a,s,l,"next",e)}function l(e){o(i,r,a,s,l,"throw",e)}s(void 0)}))},h.apply(this,arguments)}function v(e){return a.jh.findDescendents(e,a.dt)}function m(e,t,n){const r=a.jh.findObject(e,t);return r instanceof n?r:(null!==r&&l.v.warn(`invalid return type: ${n.toString()}`),null)}function f(e){var t;return null===(t=e.state.controls)||void 0===t?void 0:t.find((e=>e instanceof a.KE))}},4932:(e,t,n)=>{n.d(t,{E:()=>l,X:()=>s});var r=n(7928),a=n(3241);const i=new r.A({intraMode:1,intraIns:1,intraSub:1,intraTrn:1,intraDel:1});function s(e,t,n){const[a,s,l]=i.search(e,t,0,1e5);let o=[],c=new Set;if(a&&l){const t=(e,t)=>{t&&c.add(e)};for(let n=0;n<l.length;n++){let a=l[n];r.A.highlight(e[s.idx[a]],s.ranges[a],t),o.push(e[s.idx[a]])}n([o,[...c]])}else t||n([])}const l=(0,a.debounce)(s,300)},5722:(e,t,n)=>{n.r(t),n.d(t,{DEFAULT_SORT_BY:()=>u,calculateDataFrameChangepoints:()=>p,calculateOutlierValue:()=>m,sortSeries:()=>d,sortSeriesByName:()=>g,wasmSupported:()=>f});var r=n(1854),a=n(6944),i=n(7781),s=n(1383),l=n(3241),o=n(2718),c=n(2871);const u="changepoint",d=(0,l.memoize)(((e,t,n)=>{if("alphabetical"===t)return g(e,n);"outliers"===t&&h(e);const r=n=>{var r;try{if("changepoint"===t)return p(n);if("outliers"===t)return m(e,n)}catch(e){c.v.error(e,{msg:"failed to sort"}),t=i.ReducerID.stdDev}const a=i.fieldReducers.get(t);var s,l;return null!==(l=(null!==(s=null===(r=a.reduce)||void 0===r?void 0:r.call(a,n.fields[1],!0,!0))&&void 0!==s?s:(0,i.doStandardCalcs)(n.fields[1],!0,!0))[t])&&void 0!==l?l:0},a=e.map((e=>({value:r(e),dataFrame:e})));return a.sort(((e,t)=>void 0!==e.value&&void 0!==t.value?t.value-e.value:0)),"asc"===n&&a.reverse(),a.map((({dataFrame:e})=>e))}),((e,t,n)=>{const r=e.length>0?e[0].fields[0].values[0]:0,a=e.length>0?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0,i=e.length>0?(0,s.H7)(e[0]):"",l=e.length>0?(0,s.H7)(e[e.length-1]):"",o=e.map((e=>e.length+"_"+e.fields.map((e=>e.name+"_"+e.values[0]+"_"+e.values[e.values.length-1]))));return`${i}_${l}_${r}_${a}_${e.length}_${o}_${t}_${n}`})),p=e=>{if(!f())throw new Error("WASM not supported, fall back to stdDev");const t=e.fields.filter((e=>e.type===i.FieldType.number)),n=t[0].values.length;let a=Math.floor(n/100)||1;a>1&&(a=Math.ceil(a/2));const s=t[0].values.filter(((e,t)=>t%a==0)),l=new Float64Array(s);return r.ChangepointDetector.defaultArgpcp().detectChangepoints(l).indices.length},g=(e,t)=>{const n=[...e];return n.sort(((e,t)=>{const n=(0,s.H7)(e),r=(0,s.H7)(t);return n&&r&&null!==(a=null==n?void 0:n.localeCompare(r))&&void 0!==a?a:0;var a})),"desc"===t&&n.reverse(),n},h=e=>{if(!f())return;const t=(0,i.outerJoinDataFrames)({frames:e});if(!t)return;const n=t.fields.filter((e=>e.type===i.FieldType.number)).flatMap((e=>new Float64Array(e.values)));try{const e=a.OutlierDetector.dbscan({sensitivity:.4}).preprocess(n);v=e.detect()}catch(e){c.v.error(e,{msg:"initOutlierDetector: OutlierDetector error"})}};let v;const m=(e,t)=>{if(!f())throw new Error("WASM not supported, fall back to stdDev");if(!v)throw new Error("Initialize outlier detector first");const n=e.indexOf(t);return v.seriesResults[n].isOutlier?v.seriesResults[n].outlierIntervals.length:0},f=()=>{const e="object"==typeof WebAssembly;return e||(0,o.EE)(o.NO.service_details,o.ir.service_details.wasm_not_supported),e}},227:(e,t,n)=>{n.d(t,{Bq:()=>B,GL:()=>M,Gg:()=>v,IW:()=>G,KH:()=>Q,N$:()=>C,OB:()=>p,QB:()=>y,RN:()=>_,Rb:()=>K,Rf:()=>T,Xo:()=>A,YK:()=>P,YM:()=>L,ZF:()=>k,Zs:()=>W,cC:()=>g,cO:()=>h,eT:()=>d,ex:()=>H,fq:()=>x,hp:()=>R,k5:()=>N,ke:()=>S,o5:()=>I,og:()=>V,sj:()=>m,uF:()=>J,vs:()=>O});var r=n(2533),a=n(4750),i=n(2871),s=n(3143),l=n(4011);const o=`${r.id}.services.favorite`,c=`${r.id}.primarylabels.tabs.favorite`,u=`${r.id}.datasource`;function d(e,t){if(!e||"string"!=typeof e)return[];const n=f(e,t);let r=[];try{r=(0,l.aJ)(JSON.parse(localStorage.getItem(n)||"[]"))}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}return Array.isArray(r)||(r=[]),r}function p(e,t,n){if(!e||"string"!=typeof e)return;const r=f(e,t);let a=[];try{a=(0,l.aJ)(JSON.parse(localStorage.getItem(r)||"[]"))}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(a)||(a=[]);const s=a.filter((e=>e!==n));s.unshift(n),localStorage.setItem(r,JSON.stringify(s))}function g(e,t,n){if(!e||!t||!n||"string"!=typeof e)return;const r=f(e,t);let a=[];try{a=(0,l.aJ)(JSON.parse(localStorage.getItem(r)||"[]"))}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(a)||(a=[]);const s=a.filter((e=>e!==n));localStorage.setItem(r,JSON.stringify(s))}function h(e,t){if(!e||!t)return;const n=b(e);let r=[];try{r=(0,l.aJ)(JSON.parse(localStorage.getItem(n)||"[]"))}catch(e){i.v.error(e,{msg:"Error parsing saved tabs from local storage"})}if(Array.isArray(r)||(r=[]),-1===r.indexOf(t)){const e=r.filter((e=>e!==t));e.unshift(t),localStorage.setItem(n,JSON.stringify(e))}}function v(e,t){if(!e||!t)return;const n=b(e);let r=[];try{r=(0,l.aJ)(JSON.parse(localStorage.getItem(n)||"[]"))}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(r)||(r=[]);const a=r.filter((e=>e!==t));localStorage.setItem(n,JSON.stringify(a))}function m(e){if(!e||"string"!=typeof e)return[];const t=b(e);let n=[];try{n=(0,l.aJ)(JSON.parse(localStorage.getItem(t)||"[]"))}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}return Array.isArray(n)||(n=[]),n}function f(e,t){return t=t===s.OX?"":`_${t}`,`${o}_${e}${t}`}function b(e){return`${c}_${e}`}function y(){var e;return null!==(e=localStorage.getItem(u))&&void 0!==e?e:void 0}function S(e){localStorage.setItem(u,e)}const w=`${r.id}.values.sort`;function O(e,t,n){var r;const a=(null!==(r=localStorage.getItem(`${w}.${e}.by`))&&void 0!==r?r:"").split(".");return a[0]&&a[1]?{sortBy:a[0],direction:a[1]}:{sortBy:t,direction:n}}function x(e,t,n){t&&n&&localStorage.setItem(`${w}.${e}.by`,`${t}.${n}`)}function E(e){return`${(0,a.nH)(e)}.${(0,a.p_)(e)}`}function C(e){const t=E(e),n=localStorage.getItem(`${r.id}.${t}.logs.fields`);return n?JSON.parse(n):[]}function k(e,t){const n=E(e);localStorage.setItem(`${r.id}.${n}.logs.fields`,JSON.stringify(t))}const F=`${r.id}.logs.option`;function L(e,t){return localStorage.getItem(`${F}.${e}`)||t}function P(e,t){let n=t.toString();"boolean"!=typeof t||t||(n=""),localStorage.setItem(`${F}.${e}`,n)}const j="grafana.explore.logs.logsVolume";function _(e,t){const n=`${j}.${e}`;void 0!==t?localStorage.setItem(n,t):localStorage.removeItem(n)}function T(e){return Boolean(localStorage.getItem(`${j}.${e}`))}const D="grafana.explore.logs.visualisationType";function N(){var e;const t=null!==(e=localStorage.getItem(D))&&void 0!==e?e:"";switch(t){case"table":case"logs":return t;default:return"logs"}}function I(e){localStorage.setItem(D,e)}const $=`${r.id}.linefilter.option`;function A(e){let t=e.toString();e||(t=""),localStorage.setItem(`${$}.caseSensitive`,t)}function M(e){let t=e.toString();e||(t=""),localStorage.setItem(`${$}.regex`,t)}function B(e){let t=e.toString();e||(t=""),localStorage.setItem(`${$}.exclusive`,t)}function R(e){return"true"===localStorage.getItem(`${$}.caseSensitive`)||e}function V(e){return"true"===localStorage.getItem(`${$}.regex`)||e}function W(e){return"true"===localStorage.getItem(`${$}.exclusive`)||e}const z=`${r.id}.panel.option`;function H(e,t){const n=localStorage.getItem(`${z}.${e}`);var r;return null!==n&&null!==(r=t.find((e=>n===e)))&&void 0!==r?r:null}function G(e,t){localStorage.setItem(`${z}.${e}`,t)}const q=`${r.id}.expressionBuilder.debug`;function K(){return!!localStorage.getItem(q)}const U=`${r.id}.serviceSelection.pageCount`;function Q(){const e=localStorage.getItem(U);return e?parseInt(e,10):void 0}function J(e){localStorage.setItem(U,e.toString(10))}},1220:(e,t,n)=>{n.d(t,{b:()=>r});const r={appConfig:{container:"data-testid ac-container",apiKey:"data-testid ac-api-key",apiUrl:"data-testid ac-api-url",submit:"data-testid ac-submit-form"},exploreServiceSearch:{search:"data-testid search-services"},header:{refreshPicker:"data-testid RefreshPicker run button"},variables:{datasource:{label:"data-testid Dashboard template variables submenu Label Data source"},combobox:{},serviceName:{label:"data-testid Dashboard template variables submenu Label Labels"},levels:{inputWrap:"data-testid detected_level filter variable"}},breakdowns:{labels:{},fields:{},common:{sortByFunction:"data-testid SortBy function",sortByDirection:"data-testid SortBy direction",filterButtonGroup:"data-testid filter-button-group",filterButton:"data-testid filter-button",filterSelect:"data-testid filter-select",filterNumericPopover:{removeButton:"data-testid filter-numeric-remove",submitButton:"data-testid filter-numeric-submit",cancelButton:"data-testid filter-numeric-cancel",inputGreaterThan:"data-testid filter-numeric-gt",inputGreaterThanUnit:"data-testid filter-numeric-gtu",inputGreaterThanInclusive:"data-testid filter-numeric-gte",inputLessThan:"data-testid filter-numeric-lt",inputLessThanUnit:"data-testid filter-numeric-ltu",inputLessThanInclusive:"data-testid filter-numeric-lte"}}},index:{showLogsButton:"data-testid Show logs",addNewLabelTab:"data-testid Tab Add label",searchLabelValueInput:"data-testid search-services-input",aggregatedMetricsMenu:"data-testid aggregated-metrics-menu",aggregatedMetricsToggle:"data-testid aggregated-metrics-toggle",header:{showLogsButton:"data-testid Show logs header"}},exploreServiceDetails:{searchLogs:"data-testid search-logs",openExplore:"data-testid open-explore",tabPatterns:"data-testid tab-patterns",tabLogs:"data-testid tab-logs",tabFields:"data-testid tab-fields",tabLabels:"data-testid tab-labels",buttonRemovePattern:"data-testid button-remove-pattern",buttonFilterInclude:"data-testid button-filter-include",buttonFilterExclude:"data-testid button-filter-exclude"},patterns:{tableWrapper:"data-testid table-wrapper",buttonIncludedPattern:"data-testid button-included-pattern",buttonExcludedPattern:"data-testid button-excluded-pattern"},logsPanelHeader:{header:"data-testid Panel header Logs",radio:"data-testid radio-button"},table:{wrapper:"data-testid table-wrapper",inspectLine:"data-testid inspect",rawLogLine:"data-testid raw-log-line"}}},8315:(e,t,n)=>{n.d(t,{Dk:()=>s,EJ:()=>c,Ki:()=>u,Zr:()=>o,gW:()=>l});var r=n(8531),a=n(2871),i=n(7781);const s=e=>{if(navigator.clipboard&&window.isSecureContext)navigator.clipboard.writeText(e);else{const t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t)}},l=(e,t,n)=>{const a=r.locationService.getLocation(),i=new URLSearchParams(a.search);i.set("from",n.from.toISOString()),i.set("to",n.to.toISOString()),i.set(e,JSON.stringify(t));const s=i.toString().replace(/\+/g,"%20");return window.location.origin+a.pathname+"?"+s};function o(e){return e.length?(null==e?void 0:e.charAt(0).toUpperCase())+e.slice(1):(a.v.warn("invalid string argument"),e)}function c(e,t,n){return e.substring(0,t)+(n&&e.length>t?"…":"")}function u(e){const t=(0,i.dateTime)(e.timeEpochMs-1),n=(0,i.dateTime)(e.timeEpochMs+1);return{from:t,to:n,raw:{from:t,to:n}}}},4750:(e,t,n)=>{n.d(t,{BL:()=>F,DX:()=>c,El:()=>k,Gk:()=>O,Hj:()=>S,Ku:()=>u,P4:()=>b,Rr:()=>f,S9:()=>w,YS:()=>h,aW:()=>p,bY:()=>x,bu:()=>P,cR:()=>d,eY:()=>E,h:()=>C,ir:()=>v,iw:()=>m,n5:()=>L,nH:()=>T,oY:()=>g,p_:()=>_,vm:()=>y,z2:()=>j});var r=n(2672),a=n(5431),i=n(3143),s=n(2871),l=n(4011),o=n(6001);function c(e){const{labelExpressionToAdd:t="",structuredMetadataToAdd:n="",fieldExpressionToAdd:r="",parser:a}=e;switch(a){case"structuredMetadata":return`{${i.S1}${t}} ${n} ${i.qZ} ${i.S6} ${i.sC} ${i.rl} ${r} ${i.Oc}`;case"json":return`{${i.S1}${t}} ${n} ${i.qZ} ${i.S6} ${i.sC} ${i.rl} ${i.VL} ${r} ${i.Oc}`;case"logfmt":return`{${i.S1}${t}} ${n} ${i.qZ} ${i.S6} ${i.sC} ${i.rl} ${i.mF} ${r} ${i.Oc}`;default:return`{${i.S1}${t}} ${n} ${i.qZ} ${i.S6} ${i.sC} ${i.rl} ${i.YN} ${r} ${i.Oc}`}}function u(e){const t=r.jh.lookupVariable(i.uw,e);if(!(t instanceof r.yP))throw new Error("VAR_PATTERNS not found");return t}function d(e){return x(i.MB,e)}function p(e){return x(i.fi,e)}function g(e){return x(i._P,e)}function h(e){return x(i.sL,e)}function v(e){return x(i.mB,e)}function m(e){return x(i._Y,e)}function f(e){const t=r.jh.lookupVariable(i.WM,e);if(!(t instanceof r.H9))throw new Error("VAR_LINE_FILTER not found");return t}function b(e){const t=r.jh.lookupVariable(i.Jg,e);if(!(t instanceof a.m))throw new Error("VAR_LABEL_GROUP_BY not found");return t}function y(e){const t=r.jh.lookupVariable(i.Wi,e);if(!(t instanceof a.m))throw new Error("SERVICE_LABEL_VAR not found");return t}function S(e){const t=r.jh.lookupVariable(i.LI,e);if(!(t instanceof a.m))throw new Error("VAR_FIELD_GROUP_BY not found");return t}function w(e){const t=r.jh.lookupVariable(i.EY,e);if(!(t instanceof r.mI))throw new Error("VAR_DATASOURCE not found");return t}function O(e){const t=r.jh.lookupVariable(i.NW,e);if(!(t instanceof r.H9))throw new Error("VAR_LINE_FILTERS not found");return t}function x(e,t){const n=r.jh.lookupVariable(e,t);if(!(n instanceof r.H9))throw new Error(`Could not get AdHocFiltersVariable ${e}. Variable not found.`);return n}function E(e){const t=r.jh.lookupVariable(i.Du,e);if(!(t instanceof a.m))throw new Error("VAR_PRIMARY_LABEL_SEARCH not found");return t}function C(e){E(e).setState({value:".+",label:""})}function k(e){const t=r.jh.lookupVariable(i.Gb,e);if(!(t instanceof r.H9))throw new Error("VAR_PRIMARY_LABEL not found");return t}function F(e,t){k(t).setState({filters:[{value:".+",operator:"=~",key:e}]})}function L(e){return`var-${e}`}function P(e,t=i.mB){if((0,o.OH)(e))return{value:e.value,parser:"structuredMetadata"};try{const t=(0,i.zE)(e.value)?(0,i.Dx)(e.value):e.value,n=(0,l.fS)(JSON.parse(t));if(!1!==n)return n;throw new l.QX("getValueFromFieldsFilter: invalid filter value!")}catch(n){if(n instanceof l.QX?s.v.error(n,{msg:`getValueFromFieldsFilter: Failed to validate ${t}`,value:e.value}):s.v.error(n,{msg:`getValueFromFieldsFilter: Failed to parse ${t}`,value:e.value}),e.value)return{value:e.value,parser:"mixed"};throw n}}function j(e,t){return e.state.name===i.mB&&t?P(t):{value:null==t?void 0:t.value}}function _(e){return function(e){const t=e.filters.filter((e=>e.key===i.OX)).map((e=>e.value));if(!t)throw new Error("Service present in filters selected");return t[0]}(d(e).state)}function T(e){return w(e).getValue()}},1863:(e,t,n)=>{n.d(t,{Ht:()=>g,mE:()=>d,rA:()=>p});var r=n(2672),a=n(5431),i=n(3143),s=n(8538),l=n(892),o=n(4793),c=n(5548),u=n(5111);function d(e){const t=r.jh.getVariables(e);let n=[];for(const e of t.state.variables)e instanceof r.H9&&e.state.filters.length&&n.push(e),e instanceof a.m&&e.state.value&&"logsFormat"!==e.state.name&&n.push(e);return n}function p(e){const t=r.jh.getAncestor(e,s.P);t.setState({patterns:[]}),d(t).forEach((e=>{if(e instanceof r.H9&&"adhoc_service_filter"===e.state.key){let{labelName:t}=(0,l.W6)();t===i.ky&&(t=i.OX),e.setState({filters:e.state.filters.filter((e=>e.key===t))})}else e instanceof r.H9?e.setState({filters:[]}):e instanceof a.m&&e.setState({value:"",text:""})}))}const g=function(e){const t=e.state._wip;if(t&&e.state.filters.some((e=>e.key===t.key&&e.operator===o.w7.Equal)))return c._i;const n=e.state.name===i.MB,r=e.state.filters.filter((e=>(0,u.BG)(e.operator))).length,a=!(null==t?void 0:t.key)&&1===r,s=(null==t?void 0:t.key)&&r<1;if(n&&(a||s))return c._i;if(null==t?void 0:t.meta){const e=t.meta.type;if("float"===e||"bytes"===e||"duration"===e)return c.hI}return c.II}}}]);
//# sourceMappingURL=747.js.map