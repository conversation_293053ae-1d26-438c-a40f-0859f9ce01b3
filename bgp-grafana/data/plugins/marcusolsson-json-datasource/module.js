define(["@grafana/data","@grafana/ui","react","@emotion/css","@grafana/runtime","lodash","moment"],((e,t,r,n,a,i,o)=>(()=>{var s={7693:function(e){e.exports=function(){"use strict";var e=6e4,t=36e5,r="millisecond",n="second",a="minute",i="hour",o="day",s="week",l="month",c="quarter",u="year",p="date",d="Invalid Date",f=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,h=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,m={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||t[0])+"]"}},g=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},v={s:g,z:function(e){var t=-e.utcOffset(),r=Math.abs(t),n=Math.floor(r/60),a=r%60;return(t<=0?"+":"-")+g(n,2,"0")+":"+g(a,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),a=t.clone().add(n,l),i=r-a<0,o=t.clone().add(n+(i?-1:1),l);return+(-(n+(r-a)/(i?a-o:o-a))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:l,y:u,w:s,d:o,D:p,h:i,m:a,s:n,ms:r,Q:c}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}},y="en",b={};b[y]=m;var w="$isDayjsObject",x=function(e){return e instanceof j||!(!e||!e[w])},O=function e(t,r,n){var a;if(!t)return y;if("string"==typeof t){var i=t.toLowerCase();b[i]&&(a=i),r&&(b[i]=r,a=i);var o=t.split("-");if(!a&&o.length>1)return e(o[0])}else{var s=t.name;b[s]=t,a=s}return!n&&a&&(y=a),a||!n&&y},k=function(e,t){if(x(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new j(r)},E=v;E.l=O,E.i=x,E.w=function(e,t){return k(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var j=function(){function m(e){this.$L=O(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[w]=!0}var g=m.prototype;return g.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(E.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(f);if(n){var a=n[2]-1||0,i=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],a,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)):new Date(n[1],a,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)}}return new Date(t)}(e),this.init()},g.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},g.$utils=function(){return E},g.isValid=function(){return!(this.$d.toString()===d)},g.isSame=function(e,t){var r=k(e);return this.startOf(t)<=r&&r<=this.endOf(t)},g.isAfter=function(e,t){return k(e)<this.startOf(t)},g.isBefore=function(e,t){return this.endOf(t)<k(e)},g.$g=function(e,t,r){return E.u(e)?this[t]:this.set(r,e)},g.unix=function(){return Math.floor(this.valueOf()/1e3)},g.valueOf=function(){return this.$d.getTime()},g.startOf=function(e,t){var r=this,c=!!E.u(t)||t,d=E.p(e),f=function(e,t){var n=E.w(r.$u?Date.UTC(r.$y,t,e):new Date(r.$y,t,e),r);return c?n:n.endOf(o)},h=function(e,t){return E.w(r.toDate()[e].apply(r.toDate("s"),(c?[0,0,0,0]:[23,59,59,999]).slice(t)),r)},m=this.$W,g=this.$M,v=this.$D,y="set"+(this.$u?"UTC":"");switch(d){case u:return c?f(1,0):f(31,11);case l:return c?f(1,g):f(0,g+1);case s:var b=this.$locale().weekStart||0,w=(m<b?m+7:m)-b;return f(c?v-w:v+(6-w),g);case o:case p:return h(y+"Hours",0);case i:return h(y+"Minutes",1);case a:return h(y+"Seconds",2);case n:return h(y+"Milliseconds",3);default:return this.clone()}},g.endOf=function(e){return this.startOf(e,!1)},g.$set=function(e,t){var s,c=E.p(e),d="set"+(this.$u?"UTC":""),f=(s={},s[o]=d+"Date",s[p]=d+"Date",s[l]=d+"Month",s[u]=d+"FullYear",s[i]=d+"Hours",s[a]=d+"Minutes",s[n]=d+"Seconds",s[r]=d+"Milliseconds",s)[c],h=c===o?this.$D+(t-this.$W):t;if(c===l||c===u){var m=this.clone().set(p,1);m.$d[f](h),m.init(),this.$d=m.set(p,Math.min(this.$D,m.daysInMonth())).$d}else f&&this.$d[f](h);return this.init(),this},g.set=function(e,t){return this.clone().$set(e,t)},g.get=function(e){return this[E.p(e)]()},g.add=function(r,c){var p,d=this;r=Number(r);var f=E.p(c),h=function(e){var t=k(d);return E.w(t.date(t.date()+Math.round(e*r)),d)};if(f===l)return this.set(l,this.$M+r);if(f===u)return this.set(u,this.$y+r);if(f===o)return h(1);if(f===s)return h(7);var m=(p={},p[a]=e,p[i]=t,p[n]=1e3,p)[f]||1,g=this.$d.getTime()+r*m;return E.w(g,this)},g.subtract=function(e,t){return this.add(-1*e,t)},g.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||d;var n=e||"YYYY-MM-DDTHH:mm:ssZ",a=E.z(this),i=this.$H,o=this.$m,s=this.$M,l=r.weekdays,c=r.months,u=r.meridiem,p=function(e,r,a,i){return e&&(e[r]||e(t,n))||a[r].slice(0,i)},f=function(e){return E.s(i%12||12,e,"0")},m=u||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(h,(function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return E.s(t.$y,4,"0");case"M":return s+1;case"MM":return E.s(s+1,2,"0");case"MMM":return p(r.monthsShort,s,c,3);case"MMMM":return p(c,s);case"D":return t.$D;case"DD":return E.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(r.weekdaysMin,t.$W,l,2);case"ddd":return p(r.weekdaysShort,t.$W,l,3);case"dddd":return l[t.$W];case"H":return String(i);case"HH":return E.s(i,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return m(i,o,!0);case"A":return m(i,o,!1);case"m":return String(o);case"mm":return E.s(o,2,"0");case"s":return String(t.$s);case"ss":return E.s(t.$s,2,"0");case"SSS":return E.s(t.$ms,3,"0");case"Z":return a}return null}(e)||a.replace(":","")}))},g.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},g.diff=function(r,p,d){var f,h=this,m=E.p(p),g=k(r),v=(g.utcOffset()-this.utcOffset())*e,y=this-g,b=function(){return E.m(h,g)};switch(m){case u:f=b()/12;break;case l:f=b();break;case c:f=b()/3;break;case s:f=(y-v)/6048e5;break;case o:f=(y-v)/864e5;break;case i:f=y/t;break;case a:f=y/e;break;case n:f=y/1e3;break;default:f=y}return d?f:E.a(f)},g.daysInMonth=function(){return this.endOf(l).$D},g.$locale=function(){return b[this.$L]},g.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=O(e,t,!0);return n&&(r.$L=n),r},g.clone=function(){return E.w(this.$d,this)},g.toDate=function(){return new Date(this.valueOf())},g.toJSON=function(){return this.isValid()?this.toISOString():null},g.toISOString=function(){return this.$d.toISOString()},g.toString=function(){return this.$d.toUTCString()},m}(),S=j.prototype;return k.prototype=S,[["$ms",r],["$s",n],["$m",a],["$H",i],["$W",o],["$M",l],["$y",u],["$D",p]].forEach((function(e){S[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),k.extend=function(e,t){return e.$i||(e(t,j,k),e.$i=!0),k},k.locale=O,k.isDayjs=x,k.unix=function(e){return k(1e3*e)},k.en=b[y],k.Ls=b,k.p={},k}()},9952:(e,t,r)=>{e.exports=function e(t,r,n){function a(o,s){if(!r[o]){if(!t[o]){if(i)return i(o,!0);var l=new Error("Cannot find module '"+o+"'");throw l.code="MODULE_NOT_FOUND",l}var c=r[o]={exports:{}};t[o][0].call(c.exports,(function(e){return a(t[o][1][e]||e)}),c,c.exports,e,t,r,n)}return r[o].exports}for(var i=void 0,o=0;o<n.length;o++)a(n[o]);return a}({1:[function(e,t,r){const n=e("./utils"),a=function(){"use strict";const e=n.stringToArray,t=["Zero","One","Two","Three","Four","Five","Six","Seven","Eight","Nine","Ten","Eleven","Twelve","Thirteen","Fourteen","Fifteen","Sixteen","Seventeen","Eighteen","Nineteen"],r=["Zeroth","First","Second","Third","Fourth","Fifth","Sixth","Seventh","Eighth","Ninth","Tenth","Eleventh","Twelfth","Thirteenth","Fourteenth","Fifteenth","Sixteenth","Seventeenth","Eighteenth","Nineteenth"],a=["Twenty","Thirty","Forty","Fifty","Sixty","Seventy","Eighty","Ninety","Hundred"],i=["Thousand","Million","Billion","Trillion"];const o={};t.forEach((function(e,t){o[e.toLowerCase()]=t})),r.forEach((function(e,t){o[e.toLowerCase()]=t})),a.forEach((function(e,t){const r=e.toLowerCase();o[r]=10*(t+2),o[r.substring(0,e.length-1)+"ieth"]=o[r]})),o.hundredth=100,i.forEach((function(e,t){const r=e.toLowerCase(),n=Math.pow(10,3*(t+1));o[r]=n,o[r+"th"]=n}));const s=[[1e3,"m"],[900,"cm"],[500,"d"],[400,"cd"],[100,"c"],[90,"xc"],[50,"l"],[40,"xl"],[10,"x"],[9,"ix"],[5,"v"],[4,"iv"],[1,"i"]],l={M:1e3,D:500,C:100,L:50,X:10,V:5,I:1};function c(e){for(var t=0;t<s.length;t++){const r=s[t];if(e>=r[0])return r[1]+c(e-r[0])}return""}function u(e,t){if(void 0!==e)return f(e=Math.floor(e),m(t))}const p={DECIMAL:"decimal",LETTERS:"letters",ROMAN:"roman",WORDS:"words",SEQUENCE:"sequence"},d={UPPER:"upper",LOWER:"lower",TITLE:"title"};function f(n,o){let s;const l=n<0;switch(n=Math.abs(n),o.primary){case p.LETTERS:s=function(e,t){for(var r=[],n=t.charCodeAt(0);e>0;)r.unshift(String.fromCharCode((e-1)%26+n)),e=Math.floor((e-1)/26);return r.join("")}(n,o.case===d.UPPER?"A":"a");break;case p.ROMAN:s=c(n),o.case===d.UPPER&&(s=s.toUpperCase());break;case p.WORDS:s=function(e,n){var o=function(e,n,s){var l="";if(e<=19)l=(n?" and ":"")+(s?r[e]:t[e]);else if(e<100){const t=Math.floor(e/10),r=e%10;l=(n?" and ":"")+a[t-2],r>0?l+="-"+o(r,!1,s):s&&(l=l.substring(0,l.length-1)+"ieth")}else if(e<1e3){const r=Math.floor(e/100),a=e%100;l=(n?", ":"")+t[r]+" Hundred",a>0?l+=o(a,!0,s):s&&(l+="th")}else{var c=Math.floor(Math.log10(e)/3);c>i.length&&(c=i.length);const t=Math.pow(10,3*c),r=Math.floor(e/t),a=e-r*t;l=(n?", ":"")+o(r,!1,!1)+" "+i[c-1],a>0?l+=o(a,!0,s):s&&(l+="th")}return l};return o(e,!1,n)}(n,o.ordinal),o.case===d.UPPER?s=s.toUpperCase():o.case===d.LOWER&&(s=s.toLowerCase());break;case p.DECIMAL:s=""+n;var u=o.mandatoryDigits-s.length;if(u>0){var f=new Array(u+1).join("0");s=f+s}if(48!==o.zeroCode&&(s=e(s).map((e=>String.fromCodePoint(e.codePointAt(0)+o.zeroCode-48))).join("")),o.regular)for(let e=Math.floor((s.length-1)/o.groupingSeparators.position);e>0;e--){const t=s.length-e*o.groupingSeparators.position;s=s.substr(0,t)+o.groupingSeparators.character+s.substr(t)}else o.groupingSeparators.reverse().forEach((e=>{const t=s.length-e.position;s=s.substr(0,t)+e.character+s.substr(t)}));if(o.ordinal){var h={1:"st",2:"nd",3:"rd"}[s[s.length-1]];(!h||s.length>1&&"1"===s[s.length-2])&&(h="th"),s+=h}break;case p.SEQUENCE:throw{code:"D3130",value:o.token}}return l&&(s="-"+s),s}const h=[48,1632,1776,1984,2406,2534,2662,2790,2918,3046,3174,3302,3430,3558,3664,3792,3872,4160,4240,6112,6160,6470,6608,6784,6800,6992,7088,7232,7248,42528,43216,43264,43472,43504,43600,44016,65296];function m(t){const r={type:"integer",primary:p.DECIMAL,case:d.LOWER,ordinal:!1};let n,a;const i=t.lastIndexOf(";");switch(-1===i?n=t:(n=t.substring(0,i),a=t.substring(i+1),"o"===a[0]&&(r.ordinal=!0)),n){case"A":r.case=d.UPPER;case"a":r.primary=p.LETTERS;break;case"I":r.case=d.UPPER;case"i":r.primary=p.ROMAN;break;case"W":r.case=d.UPPER,r.primary=p.WORDS;break;case"Ww":r.case=d.TITLE,r.primary=p.WORDS;break;case"w":r.primary=p.WORDS;break;default:{let t=null,a=0,i=0,o=[],s=0;if(e(n).map((e=>e.codePointAt(0))).reverse().forEach((e=>{let r=!1;for(let n=0;n<h.length;n++){const i=h[n];if(e>=i&&e<=i+9){if(r=!0,a++,s++,null===t)t=i;else if(i!==t)throw{code:"D3131"};break}}r||(35===e?(s++,i++):o.push({position:s,character:String.fromCodePoint(e)}))})),a>0){r.primary=p.DECIMAL,r.zeroCode=t,r.mandatoryDigits=a,r.optionalDigits=i;const e=function(e){if(0===e.length)return 0;const t=e[0].character;for(let r=1;r<e.length;r++)if(e[r].character!==t)return 0;const r=e.map((e=>e.position)),n=function(e,t){return 0===t?e:n(t,e%t)},a=r.reduce(n);for(let e=1;e<=r.length;e++)if(-1===r.indexOf(e*a))return 0;return a}(o);e>0?(r.regular=!0,r.groupingSeparators={position:e,character:o[0].character}):(r.regular=!1,r.groupingSeparators=o)}else r.primary=p.SEQUENCE,r.token=n}}return r}const g={Y:"1",M:"1",D:"1",d:"1",F:"n",W:"1",w:"1",X:"1",x:"1",H:"1",h:"1",P:"n",m:"01",s:"01",f:"1",Z:"01:01",z:"01:01",C:"n",E:"n"};function v(e){var t=[];const r={type:"datetime",parts:t},n=function(r,n){if(n>r){let a=e.substring(r,n);a=a.split("]]").join("]"),t.push({type:"literal",value:a})}};for(var a=0,i=0;i<e.length;){if("["===e.charAt(i)){if("["===e.charAt(i+1)){n(a,i),t.push({type:"literal",value:"["}),a=i+=2;continue}if(n(a,i),a=i,-1===(i=e.indexOf("]",a)))throw{code:"D3135"};let r=e.substring(a+1,i);r=r.split(/\s+/).join("");var o,s={type:"marker",component:r.charAt(0)},l=r.lastIndexOf(",");if(-1!==l){const e=r.substring(l+1),t=e.indexOf("-");let n,a;const i=function(e){return void 0===e||"*"===e?void 0:parseInt(e)};-1===t?n=e:(n=e.substring(0,t),a=e.substring(t+1));const c={min:i(n),max:i(a)};s.width=c,o=r.substring(1,l)}else o=r.substring(1);if(1===o.length)s.presentation1=o;else if(o.length>1){var c=o.charAt(o.length-1);-1!=="atco".indexOf(c)?(s.presentation2=c,"o"===c&&(s.ordinal=!0),s.presentation1=o.substring(0,o.length-1)):s.presentation1=o}else s.presentation1=g[s.component];if(void 0===s.presentation1)throw{code:"D3132",value:s.component};if("n"===s.presentation1[0])s.names=d.LOWER;else if("N"===s.presentation1[0])"n"===s.presentation1[1]?s.names=d.TITLE:s.names=d.UPPER;else if(-1!=="YMDdFWwXxHhmsf".indexOf(s.component)){var u=s.presentation1;if(s.presentation2&&(u+=";"+s.presentation2),s.integerFormat=m(u),s.width&&void 0!==s.width.min&&s.integerFormat.mandatoryDigits<s.width.min&&(s.integerFormat.mandatoryDigits=s.width.min),-1!=="YMD".indexOf(s.component))if(s.n=-1,s.width&&void 0!==s.width.max)s.n=s.width.max,s.integerFormat.mandatoryDigits=s.n;else{var p=s.integerFormat.mandatoryDigits+s.integerFormat.optionalDigits;p>=2&&(s.n=p)}}"Z"!==s.component&&"z"!==s.component||(s.integerFormat=m(s.presentation1)),t.push(s),a=i+1}i++}return n(a,i),r}const y=["","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],b=["January","February","March","April","May","June","July","August","September","October","November","December"],w=864e5,x=function(e){const t=Date.UTC(e.year,e.month);var r=new Date(t).getUTCDay();return 0===r&&(r=7),r>4?t+(8-r)*w:t-(r-1)*w},O=function(e,t){return{year:e,month:t,nextMonth:function(){return 11===t?O(e+1,0):O(e,t+1)},previousMonth:function(){return 0===t?O(e-1,11):O(e,t-1)},nextYear:function(){return O(e+1,t)},previousYear:function(){return O(e-1,t)}}},k=function(e,t){return(t-e)/6048e5+1},E=(e,t)=>{let r;switch(t){case"Y":r=e.getUTCFullYear();break;case"M":r=e.getUTCMonth()+1;break;case"D":r=e.getUTCDate();break;case"d":r=(Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate())-Date.UTC(e.getUTCFullYear(),0))/w+1;break;case"F":r=e.getUTCDay(),0===r&&(r=7);break;case"W":{const t=O(e.getUTCFullYear(),0),n=x(t),a=Date.UTC(t.year,e.getUTCMonth(),e.getUTCDate());let i=k(n,a);if(i>52)a>=x(t.nextYear())&&(i=1);else if(i<1){const e=x(t.previousYear());i=k(e,a)}r=Math.floor(i);break}case"w":{const t=O(e.getUTCFullYear(),e.getUTCMonth()),n=x(t),a=Date.UTC(t.year,t.month,e.getUTCDate());let i=k(n,a);if(i>4)a>=x(t.nextMonth())&&(i=1);else if(i<1){const e=x(t.previousMonth());i=k(e,a)}r=Math.floor(i);break}case"X":{const t=O(e.getUTCFullYear(),0),n=x(t),a=x(t.nextYear()),i=e.getTime();r=i<n?t.year-1:i>=a?t.year+1:t.year;break}case"x":{const t=O(e.getUTCFullYear(),e.getUTCMonth()),n=x(t),a=t.nextMonth(),i=x(a),o=e.getTime();r=o<n?t.previousMonth().month+1:o>=i?a.month+1:t.month+1;break}case"H":r=e.getUTCHours();break;case"h":r=e.getUTCHours(),r%=12,0===r&&(r=12);break;case"P":r=e.getUTCHours()>=12?"pm":"am";break;case"m":r=e.getUTCMinutes();break;case"s":r=e.getUTCSeconds();break;case"f":r=e.getUTCMilliseconds();break;case"Z":case"z":break;case"C":case"E":r="ISO"}return r};let j=null;function S(e,t,r){var n=0,a=0;if(void 0!==r){const e=parseInt(r);n=Math.floor(e/100),a=e%100}let i;void 0===t?(null===j&&(j=v("[Y0001]-[M01]-[D01]T[H01]:[m01]:[s01].[f001][Z01:01t]")),i=j):i=v(t);const o=new Date(e+60*(60*n+a)*1e3);let s="";return i.parts.forEach((function(e){"literal"===e.type?s+=e.value:s+=function(e,t){var r=E(e,t.component);if(-1!=="YMDdFWwXxHhms".indexOf(t.component))if("Y"===t.component&&-1!==t.n&&(r%=Math.pow(10,t.n)),t.names){if("M"===t.component||"x"===t.component)r=b[r-1];else{if("F"!==t.component)throw{code:"D3133",value:t.component};r=y[r]}t.names===d.UPPER?r=r.toUpperCase():t.names===d.LOWER&&(r=r.toLowerCase()),t.width&&r.length>t.width.max&&(r=r.substring(0,t.width.max))}else r=f(r,t.integerFormat);else if("f"===t.component)r=f(r,t.integerFormat);else if("Z"===t.component||"z"===t.component){const e=100*n+a;if(t.integerFormat.regular)r=f(e,t.integerFormat);else{const i=t.integerFormat.mandatoryDigits;if(1===i||2===i)r=f(n,t.integerFormat),0!==a&&(r+=":"+u(a,"00"));else{if(3!==i&&4!==i)throw{code:"D3134",value:i};r=f(e,t.integerFormat)}}e>=0&&(r="+"+r),"z"===t.component&&(r="GMT"+r),0===e&&"t"===t.presentation2&&(r="Z")}return r}(o,e)})),s}function P(e){var t={};if("datetime"===e.type)t.type="datetime",t.parts=e.parts.map((function(e){var t={};if("literal"===e.type)t.regex=e.value.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");else if("Z"===e.component||"z"===e.component){let r;Array.isArray(e.integerFormat.groupingSeparators)||(r=e.integerFormat.groupingSeparators),t.regex="","z"===e.component&&(t.regex="GMT"),t.regex+="[-+][0-9]+",r&&(t.regex+=r.character+"[0-9]+"),t.parse=function(t){"z"===e.component&&(t=t.substring(3));let n=0,a=0;return r?(n=Number.parseInt(t.substring(0,t.indexOf(r.character))),a=Number.parseInt(t.substring(t.indexOf(r.character)+1))):t.length-1<=2?n=Number.parseInt(t):(n=Number.parseInt(t.substring(0,3)),a=Number.parseInt(t.substring(3))),60*n+a}}else if(e.integerFormat)e.integerFormat.n=e.n,t=P(e.integerFormat);else{t.regex="[a-zA-Z]+";var r={};if("M"===e.component||"x"===e.component)b.forEach((function(t,n){e.width&&e.width.max?r[t.substring(0,e.width.max)]=n+1:r[t]=n+1}));else if("F"===e.component)y.forEach((function(t,n){n>0&&(e.width&&e.width.max?r[t.substring(0,e.width.max)]=n:r[t]=n)}));else{if("P"!==e.component)throw{code:"D3133",value:e.component};r={am:0,AM:0,pm:1,PM:1}}t.parse=function(e){return r[e]}}return t.component=e.component,t}));else{t.type="integer";const r=e.case===d.UPPER;let n;switch(n=e.n&&e.n>0?0===e.optionalDigits?`{${e.n}}`:`{${e.n-e.optionalDigits},${e.n}}`:"+",e.primary){case p.LETTERS:t.regex=r?"[A-Z]+":"[a-z]+",t.parse=function(e){return function(e,t){for(var r=t.charCodeAt(0),n=0,a=0;a<e.length;a++)n+=(e.charCodeAt(e.length-a-1)-r+1)*Math.pow(26,a);return n}(e,r?"A":"a")};break;case p.ROMAN:t.regex=r?"[MDCLXVI]+":"[mdclxvi]+",t.parse=function(e){return function(e){for(var t=0,r=1,n=e.length-1;n>=0;n--){const a=e[n],i=l[a];i<r?t-=i:(r=i,t+=i)}return t}(r?e:e.toUpperCase())};break;case p.WORDS:t.regex="(?:"+Object.keys(o).concat("and","[\\-, ]").join("|")+")+",t.parse=function(e){return function(e){const t=e.split(/,\s|\sand\s|[\s\\-]/).map((e=>o[e]));let r=[0];return t.forEach((e=>{if(e<100){let t=r.pop();t>=1e3&&(r.push(t),t=0),r.push(t+e)}else r.push(r.pop()*e)})),r.reduce(((e,t)=>e+t),0)}(e.toLowerCase())};break;case p.DECIMAL:t.regex=`[0-9]${n}`,e.ordinal&&(t.regex+="(?:th|st|nd|rd)"),t.parse=function(t){let r=t;return e.ordinal&&(r=t.substring(0,t.length-2)),e.regular?r=r.split(",").join(""):e.groupingSeparators.forEach((e=>{r=r.split(e.character).join("")})),48!==e.zeroCode&&(r=r.split("").map((t=>String.fromCodePoint(t.codePointAt(0)-e.zeroCode+48))).join("")),parseInt(r)};break;case p.SEQUENCE:throw{code:"D3130",value:e.token}}}return t}function D(e,t){const r=P(v(t)),n="^"+r.parts.map((e=>"("+e.regex+")")).join("")+"$";var a=new RegExp(n,"i").exec(e);if(null!==a){const e=161,t=130,n=84,o=72,s=23,l=47,c={};for(let e=1;e<a.length;e++){const t=r.parts[e-1];t.parse&&(c[t.component]=t.parse(a[e]))}if(0===Object.getOwnPropertyNames(c).length)return;let u=0;const p=e=>{u<<=1,u+=e?1:0},d=e=>!(~e&u||!(e&u));"YXMxWwdD".split("").forEach((e=>p(c[e])));const f=!d(e)&&d(t),h=d(n),m=!h&&d(o);u=0,"PHhmsf".split("").forEach((e=>p(c[e])));const g=!d(s)&&d(l),v=(f?"YD":h?"XxwF":m?"XWF":"YMD")+(g?"Phmsf":"Hmsf"),y=this.environment.timestamp;let b=!1,w=!1;if(v.split("").forEach((e=>{if(void 0===c[e])b?(c[e]=-1!=="MDd".indexOf(e)?1:0,w=!0):c[e]=E(y,e);else if(b=!0,w)throw{code:"D3136"}})),c.M>0?c.M-=1:c.M=0,f){const e=Date.UTC(c.Y,0),t=1e3*(c.d-1)*60*60*24,r=new Date(e+t);c.M=r.getUTCMonth(),c.D=r.getUTCDate()}if(h)throw{code:"D3136"};if(m)throw{code:"D3136"};g&&(c.H=12===c.h?0:c.h,1===c.P&&(c.H+=12));var i=Date.UTC(c.Y,c.M,c.D,c.H,c.m,c.s,c.f);return(c.Z||c.z)&&(i-=60*(c.Z||c.z)*1e3),i}}var T=new RegExp("^\\d{4}(-[01]\\d)*(-[0-3]\\d)*(T[0-2]\\d:[0-5]\\d:[0-5]\\d)*(\\.\\d+)?([+-][0-2]\\d:?[0-5]\\d|Z)?$");return{formatInteger:u,parseInteger:function(e,t){if(void 0!==e)return P(m(t)).parse(e)},fromMillis:function(e,t,r){if(void 0!==e)return S.call(this,e,t,r)},toMillis:function(e,t){if(void 0!==e){if(void 0===t){if(!T.test(e))throw{stack:(new Error).stack,code:"D3110",value:e};return Date.parse(e)}return D.call(this,e,t)}}}}();t.exports=a},{"./utils":6}],2:[function(e,t,n){(function(r){(function(){var n=e("./utils");const a=(()=>{"use strict";var e=n.isNumeric,t=n.isArrayOfStrings,a=n.isArrayOfNumbers,i=n.createSequence,o=n.isSequence,s=n.isFunction,l=n.isLambda,c=n.isIterable,u=n.getFunctionArity,p=n.isDeepEqual,d=n.stringToArray;function f(e,t,r){if(void 0!==e){var n=d(e),a=n.length;if(a+t<0&&(t=0),void 0!==r){if(r<=0)return"";var i=t>=0?t+r:a+t+r;return n.slice(t,i).join("")}return n.slice(t).join("")}}function h(e){if(void 0!==e)return d(e).length}function*m(e,t){var r=e.apply(this,[t]);if(c(r)&&(r=yield*r),r&&"number"!=typeof r.start&&"number"!==r.end&&!Array.isArray(r.groups)&&!s(r.next))throw{code:"T1010",stack:(new Error).stack};return r}function g(e,t){var r;if(void 0!==e){if(t){var n=e.toString().split("e");e=+(n[0]+"e"+(n[1]?+n[1]+t:t))}var a=(r=Math.round(e))-e;return.5===Math.abs(a)&&1===Math.abs(r%2)&&(r-=1),t&&(n=r.toString().split("e"),r=+(n[0]+"e"+(n[1]?+n[1]-t:-t))),Object.is(r,-0)&&(r=0),r}}function v(t){if(void 0!==t){var r=!1;return Array.isArray(t)?1===t.length?r=v(t[0]):t.length>1&&(r=t.filter((function(e){return v(e)})).length>0):"string"==typeof t?t.length>0&&(r=!0):e(t)?0!==t&&(r=!0):null!==t&&"object"==typeof t?Object.keys(t).length>0&&(r=!0):"boolean"==typeof t&&!0===t&&(r=!0),r}}function y(e,t,r,n){var a=[t],i=u(e);return i>=2&&a.push(r),i>=3&&a.push(n),a}function b(e,t){return void 0===e?t:void 0===t?e:(Array.isArray(e)||(e=i(e)),Array.isArray(t)||(t=[t]),e.concat(t))}return{sum:function(e){if(void 0!==e){var t=0;return e.forEach((function(e){t+=e})),t}},count:function(e){return void 0===e?0:e.length},max:function(e){if(void 0!==e&&0!==e.length)return Math.max.apply(Math,e)},min:function(e){if(void 0!==e&&0!==e.length)return Math.min.apply(Math,e)},average:function(e){if(void 0!==e&&0!==e.length){var t=0;return e.forEach((function(e){t+=e})),t/e.length}},string:function(t,r=!1){if(void 0!==t){var n;if("string"==typeof t)n=t;else if(s(t))n="";else{if("number"==typeof t&&!isFinite(t))throw{code:"D3001",value:t,stack:(new Error).stack};var a=r?2:0;Array.isArray(t)&&t.outerWrapper&&(t=t[0]),n=JSON.stringify(t,(function(t,r){return null!=r&&r.toPrecision&&e(r)?Number(r.toPrecision(15)):r&&s(r)?"":r}),a)}return n}},substring:f,substringBefore:function(e,t){if(void 0!==e){var r=e.indexOf(t);return r>-1?e.substr(0,r):e}},substringAfter:function(e,t){if(void 0!==e){var r=e.indexOf(t);return r>-1?e.substr(r+t.length):e}},lowercase:function(e){if(void 0!==e)return e.toLowerCase()},uppercase:function(e){if(void 0!==e)return e.toUpperCase()},length:h,trim:function(e){if(void 0!==e){var t=e.replace(/[ \t\n\r]+/gm," ");return" "===t.charAt(0)&&(t=t.substring(1))," "===t.charAt(t.length-1)&&(t=t.substring(0,t.length-1)),t}},pad:function(e,t,r){if(void 0!==e){var n;void 0!==r&&0!==r.length||(r=" ");var a=Math.abs(t)-h(e);if(a>0){var i=new Array(a+1).join(r);r.length>1&&(i=f(i,0,a)),n=t>0?e+i:i+e}else n=e;return n}},match:function*(e,t,r){if(void 0!==e){if(r<0)throw{stack:(new Error).stack,value:r,code:"D3040",index:3};var n=i();if(void 0===r||r>0){var a=0,o=yield*m(t,e);if(void 0!==o)for(;void 0!==o&&(void 0===r||a<r);)n.push({match:o.match,index:o.start,groups:o.groups}),o=yield*m(o.next),a++}return n}},contains:function*(e,t){if(void 0!==e)return"string"==typeof t?-1!==e.indexOf(t):void 0!==(yield*m(t,e))},replace:function*(e,t,r,n){if(void 0!==e){var a;if(""===t)throw{code:"D3010",stack:(new Error).stack,value:t,index:2};if(n<0)throw{code:"D3011",stack:(new Error).stack,value:n,index:4};a="string"==typeof r?function(e){for(var t="",n=0,a=r.indexOf("$",n);-1!==a&&n<r.length;){t+=r.substring(n,a),n=a+1;var i=r.charAt(n);if("$"===i)t+="$",n++;else if("0"===i)t+=e.match,n++;else{var o;if(o=0===e.groups.length?1:Math.floor(Math.log(e.groups.length)*Math.LOG10E)+1,a=parseInt(r.substring(n,n+o),10),o>1&&a>e.groups.length&&(a=parseInt(r.substring(n,n+o-1),10)),isNaN(a))t+="$";else{if(e.groups.length>0){var s=e.groups[a-1];void 0!==s&&(t+=s)}n+=a.toString().length}}a=r.indexOf("$",n)}return t+r.substring(n)}:r;var i="",o=0;if(void 0===n||n>0){var s=0;if("string"==typeof t){for(var l=e.indexOf(t,o);-1!==l&&(void 0===n||s<n);)i+=e.substring(o,l),i+=r,o=l+t.length,s++,l=e.indexOf(t,o);i+=e.substring(o)}else{var u=yield*m(t,e);if(void 0!==u){for(;void 0!==u&&(void 0===n||s<n);){i+=e.substring(o,u.start);var p=a.apply(this,[u]);if(c(p)&&(p=yield*p),"string"!=typeof p)throw{code:"D3012",stack:(new Error).stack,value:p};i+=p,o=u.start+u.match.length,s++,u=yield*m(u.next)}i+=e.substring(o)}else i=e}}else i=e;return i}},split:function*(e,t,r){if(void 0!==e){if(r<0)throw{code:"D3020",stack:(new Error).stack,value:r,index:3};var n=[];if(void 0===r||r>0)if("string"==typeof t)n=e.split(t,r);else{var a=0,i=yield*m(t,e);if(void 0!==i){for(var o=0;void 0!==i&&(void 0===r||a<r);)n.push(e.substring(o,i.start)),o=i.end,i=yield*m(i.next),a++;(void 0===r||a<r)&&n.push(e.substring(o))}else n.push(e)}return n}},join:function(e,t){if(void 0!==e)return void 0===t&&(t=""),e.join(t)},formatNumber:function(e,t,r){if(void 0!==e){var n={"decimal-separator":".","grouping-separator":",","exponent-separator":"e",infinity:"Infinity","minus-sign":"-",NaN:"NaN",percent:"%","per-mille":"‰","zero-digit":"0",digit:"#","pattern-separator":";"};void 0!==r&&Object.keys(r).forEach((function(e){n[e]=r[e]}));for(var a=[],i=n["zero-digit"].charCodeAt(0),o=i;o<i+10;o++)a.push(String.fromCharCode(o));var s=a.concat([n["decimal-separator"],n["exponent-separator"],n["grouping-separator"],n.digit,n["pattern-separator"]]),l=t.split(n["pattern-separator"]);if(l.length>2)throw{code:"D3080",stack:(new Error).stack};var c=l.map((function(e){var t,r,a,i,o=function(){for(var t,r=0;r<e.length;r++)if(t=e.charAt(r),-1!==s.indexOf(t)&&t!==n["exponent-separator"])return e.substring(0,r)}(),l=function(){for(var t,r=e.length-1;r>=0;r--)if(t=e.charAt(r),-1!==s.indexOf(t)&&t!==n["exponent-separator"])return e.substring(r+1)}(),c=e.substring(o.length,e.length-l.length),u=e.indexOf(n["exponent-separator"],o.length);-1===u||u>e.length-l.length?(t=c,r=void 0):(t=c.substring(0,u),r=c.substring(u+1));var p=t.indexOf(n["decimal-separator"]);return-1===p?(a=t,i=l):(a=t.substring(0,p),i=t.substring(p+1)),{prefix:o,suffix:l,activePart:c,mantissaPart:t,exponentPart:r,integerPart:a,fractionalPart:i,subpicture:e}}));c.forEach((function(e){var t,r,i=e.subpicture,o=i.indexOf(n["decimal-separator"]);o!==i.lastIndexOf(n["decimal-separator"])&&(t="D3081"),i.indexOf(n.percent)!==i.lastIndexOf(n.percent)&&(t="D3082"),i.indexOf(n["per-mille"])!==i.lastIndexOf(n["per-mille"])&&(t="D3083"),-1!==i.indexOf(n.percent)&&-1!==i.indexOf(n["per-mille"])&&(t="D3084");var l=!1;for(r=0;r<e.mantissaPart.length;r++){var c=e.mantissaPart.charAt(r);if(-1!==a.indexOf(c)||c===n.digit){l=!0;break}}l||(t="D3085"),-1!==e.activePart.split("").map((function(e){return-1===s.indexOf(e)?"p":"a"})).join("").indexOf("p")&&(t="D3086"),-1!==o?i.charAt(o-1)!==n["grouping-separator"]&&i.charAt(o+1)!==n["grouping-separator"]||(t="D3087"):e.integerPart.charAt(e.integerPart.length-1)===n["grouping-separator"]&&(t="D3088"),-1!==i.indexOf(n["grouping-separator"]+n["grouping-separator"])&&(t="D3089");var u=e.integerPart.indexOf(n.digit);-1!==u&&e.integerPart.substring(0,u).split("").filter((function(e){return a.indexOf(e)>-1})).length>0&&(t="D3090"),-1!==(u=e.fractionalPart.lastIndexOf(n.digit))&&e.fractionalPart.substring(u).split("").filter((function(e){return a.indexOf(e)>-1})).length>0&&(t="D3091");var p="string"==typeof e.exponentPart;if(p&&e.exponentPart.length>0&&(-1!==i.indexOf(n.percent)||-1!==i.indexOf(n["per-mille"]))&&(t="D3092"),p&&(0===e.exponentPart.length||e.exponentPart.split("").filter((function(e){return-1===a.indexOf(e)})).length>0)&&(t="D3093"),t)throw{code:t,stack:(new Error).stack}}));var u,p,d,f,h=c.map((function(e){var t=function(t,r){for(var i=[],o=t.indexOf(n["grouping-separator"]);-1!==o;){var s=(r?t.substring(0,o):t.substring(o)).split("").filter((function(e){return-1!==a.indexOf(e)||e===n.digit})).length;i.push(s),o=e.integerPart.indexOf(n["grouping-separator"],o+1)}return i},r=t(e.integerPart),i=function(e){if(0===e.length)return 0;for(var t=function(e,r){return 0===r?e:t(r,e%r)},r=e.reduce(t),n=1;n<=e.length;n++)if(-1===e.indexOf(n*r))return 0;return r}(r),o=t(e.fractionalPart,!0),s=e.integerPart.split("").filter((function(e){return-1!==a.indexOf(e)})).length,l=s,c=e.fractionalPart.split(""),u=c.filter((function(e){return-1!==a.indexOf(e)})).length,p=c.filter((function(e){return-1!==a.indexOf(e)||e===n.digit})).length,d="string"==typeof e.exponentPart;0===s&&0===p&&(d?(u=1,p=1):s=1),d&&0===s&&-1!==e.integerPart.indexOf(n.digit)&&(s=1),0===s&&0===u&&(u=1);var f=0;return d&&(f=e.exponentPart.split("").filter((function(e){return-1!==a.indexOf(e)})).length),{integerPartGroupingPositions:r,regularGrouping:i,minimumIntegerPartSize:s,scalingFactor:l,prefix:e.prefix,fractionalPartGroupingPositions:o,minimumFactionalPartSize:u,maximumFactionalPartSize:p,minimumExponentSize:f,suffix:e.suffix,picture:e.subpicture}})),m=n["minus-sign"],v=n["zero-digit"],y=n["decimal-separator"],b=n["grouping-separator"];if(1===h.length&&(h.push(JSON.parse(JSON.stringify(h[0]))),h[1].prefix=m+h[1].prefix),p=-1!==(u=e>=0?h[0]:h[1]).picture.indexOf(n.percent)?100*e:-1!==u.picture.indexOf(n["per-mille"])?1e3*e:e,0===u.minimumExponentSize)d=p;else{var w=Math.pow(10,u.scalingFactor),x=Math.pow(10,u.scalingFactor-1);for(d=p,f=0;d<x;)d*=10,f-=1;for(;d>w;)d/=10,f+=1}var O=function(e,t){var r=Math.abs(e).toFixed(t);return"0"!==v&&(r=r.split("").map((function(e){return e>="0"&&e<="9"?a[e.charCodeAt(0)-48]:e})).join("")),r},k=O(g(d,u.maximumFactionalPartSize),u.maximumFactionalPartSize),E=k.indexOf(".");for(-1===E?k+=y:k=k.replace(".",y);k.charAt(0)===v;)k=k.substring(1);for(;k.charAt(k.length-1)===v;)k=k.substring(0,k.length-1);E=k.indexOf(y);var j=u.minimumIntegerPartSize-E,S=u.minimumFactionalPartSize-(k.length-E-1);if(k=(j>0?new Array(j+1).join(v):"")+k,k+=S>0?new Array(S+1).join(v):"",E=k.indexOf(y),u.regularGrouping>0)for(var P=Math.floor((E-1)/u.regularGrouping),D=1;D<=P;D++)k=[k.slice(0,E-D*u.regularGrouping),b,k.slice(E-D*u.regularGrouping)].join("");else u.integerPartGroupingPositions.forEach((function(e){k=[k.slice(0,E-e),b,k.slice(E-e)].join(""),E++}));if(E=k.indexOf(y),u.fractionalPartGroupingPositions.forEach((function(e){k=[k.slice(0,e+E+1),b,k.slice(e+E+1)].join("")})),E=k.indexOf(y),-1!==u.picture.indexOf(y)&&E!==k.length-1||(k=k.substring(0,k.length-1)),void 0!==f){var T=O(f,0);(j=u.minimumExponentSize-T.length)>0&&(T=new Array(j+1).join(v)+T),k=k+n["exponent-separator"]+(f<0?m:"")+T}return k=u.prefix+k+u.suffix}},formatBase:function(e,t){if(void 0!==e){if(e=g(e),(t=void 0===t?10:g(t))<2||t>36)throw{code:"D3100",stack:(new Error).stack,value:t};return e.toString(t)}},number:function(e){var t;if(void 0!==e){if("number"==typeof e)t=e;else if("string"==typeof e&&/^-?[0-9]+(\.[0-9]+)?([Ee][-+]?[0-9]+)?$/.test(e)&&!isNaN(parseFloat(e))&&isFinite(e))t=parseFloat(e);else if(!0===e)t=1;else{if(!1!==e)throw{code:"D3030",value:e,stack:(new Error).stack,index:1};t=0}return t}},floor:function(e){if(void 0!==e)return Math.floor(e)},ceil:function(e){if(void 0!==e)return Math.ceil(e)},round:g,abs:function(e){if(void 0!==e)return Math.abs(e)},sqrt:function(e){if(void 0!==e){if(e<0)throw{stack:(new Error).stack,code:"D3060",index:1,value:e};return Math.sqrt(e)}},power:function(e,t){var r;if(void 0!==e){if(r=Math.pow(e,t),!isFinite(r))throw{stack:(new Error).stack,code:"D3061",index:1,value:e,exp:t};return r}},random:function(){return Math.random()},boolean:v,not:function(e){if(void 0!==e)return!v(e)},map:function*(e,t){if(void 0!==e){for(var r=i(),n=0;n<e.length;n++){var a=y(t,e[n],n,e),o=yield*t.apply(this,a);void 0!==o&&r.push(o)}return r}},zip:function(){for(var e=[],t=Array.prototype.slice.call(arguments),r=Math.min.apply(Math,t.map((function(e){return Array.isArray(e)?e.length:0}))),n=0;n<r;n++){var a=t.map((e=>e[n]));e.push(a)}return e},filter:function*(e,t){if(void 0!==e){for(var r=i(),n=0;n<e.length;n++){var a=e[n],o=y(t,a,n,e);v(yield*t.apply(this,o))&&r.push(a)}return r}},single:function*(e,t){if(void 0!==e){for(var r,n=!1,a=0;a<e.length;a++){var i=e[a],o=!0;if(void 0!==t){var s=y(t,i,a,e);o=v(yield*t.apply(this,s))}if(o){if(n)throw{stack:(new Error).stack,code:"D3138",index:a};r=i,n=!0}}if(!n)throw{stack:(new Error).stack,code:"D3139"};return r}},foldLeft:function*(e,t,r){if(void 0!==e){var n,a,i=u(t);if(i<2)throw{stack:(new Error).stack,code:"D3050",index:1};for(void 0===r&&e.length>0?(n=e[0],a=1):(n=r,a=0);a<e.length;){var o=[n,e[a]];i>=3&&o.push(a),i>=4&&o.push(e),n=yield*t.apply(this,o),a++}return n}},sift:function*(e,t){var r={};for(var n in e){var a=e[n],i=y(t,a,n,e);v(yield*t.apply(this,i))&&(r[n]=a)}return 0===Object.keys(r).length&&(r=void 0),r},keys:function e(t){var r=i();if(Array.isArray(t)){var n={};t.forEach((function(t){e(t).forEach((function(e){n[e]=!0}))})),r=e(n)}else null===t||"object"!=typeof t||l(t)||Object.keys(t).forEach((e=>r.push(e)));return r},lookup:function e(t,r){var n;if(Array.isArray(t)){n=i();for(var a=0;a<t.length;a++){var o=e(t[a],r);void 0!==o&&(Array.isArray(o)?o.forEach((e=>n.push(e))):n.push(o))}}else null!==t&&"object"==typeof t&&(n=t[r]);return n},append:b,exists:function(e){return void 0!==e},spread:function e(t){var r=i();if(Array.isArray(t))t.forEach((function(t){r=b(r,e(t))}));else if(null===t||"object"!=typeof t||l(t))r=t;else for(var n in t){var a={};a[n]=t[n],r.push(a)}return r},merge:function(e){if(void 0!==e){var t={};return e.forEach((function(e){for(var r in e)t[r]=e[r]})),t}},reverse:function(e){if(void 0!==e){if(e.length<=1)return e;for(var t=e.length,r=new Array(t),n=0;n<t;n++)r[t-n-1]=e[n];return r}},each:function*(e,t){var r=i();for(var n in e){var a=y(t,e[n],n,e),o=yield*t.apply(this,a);void 0!==o&&r.push(o)}return r},error:function(e){throw{code:"D3137",stack:(new Error).stack,message:e||"$error() function evaluated"}},assert:function(e,t){if(!e)throw{code:"D3141",stack:(new Error).stack,message:t||"$assert() statement failed"}},type:function(t){if(void 0!==t)return null===t?"null":e(t)?"number":"string"==typeof t?"string":"boolean"==typeof t?"boolean":Array.isArray(t)?"array":s(t)?"function":"object"},sort:function*(e,r){if(void 0!==e){if(e.length<=1)return e;var n;if(void 0===r){if(!a(e)&&!t(e))throw{stack:(new Error).stack,code:"D3070",index:1};n=function*(e,t){return e>t}}else n=r;var i=function*(e){if(!Array.isArray(e)||e.length<=1)return e;var t=Math.floor(e.length/2),r=e.slice(0,t),a=e.slice(t);return r=yield*i(r),a=yield*i(a),yield*function*(e,t){var r=function*(e,t,a){0===t.length?Array.prototype.push.apply(e,a):0===a.length?Array.prototype.push.apply(e,t):(yield*n(t[0],a[0]))?(e.push(a[0]),yield*r(e,t,a.slice(1))):(e.push(t[0]),yield*r(e,t.slice(1),a))},a=[];return yield*r(a,e,t),a}(r,a)};return yield*i(e)}},shuffle:function(e){if(void 0!==e){if(e.length<=1)return e;for(var t=new Array(e.length),r=0;r<e.length;r++){var n=Math.floor(Math.random()*(r+1));r!==n&&(t[r]=t[n]),t[n]=e[r]}return t}},distinct:function(e){if(void 0!==e){if(!Array.isArray(e)||e.length<=1)return e;for(var t=o(e)?i():[],r=0;r<e.length;r++){for(var n=e[r],a=!1,s=0;s<t.length;s++)if(p(n,t[s])){a=!0;break}a||t.push(n)}return t}},base64encode:function(e){if(void 0!==e){var t="undefined"!=typeof window?window.btoa:function(e){return new r.Buffer.from(e,"binary").toString("base64")};return t(e)}},base64decode:function(e){if(void 0!==e){var t="undefined"!=typeof window?window.atob:function(e){return new r.Buffer.from(e,"base64").toString("binary")};return t(e)}},encodeUrlComponent:function(e){if(void 0!==e){var t;try{t=encodeURIComponent(e)}catch(t){throw{code:"D3140",stack:(new Error).stack,value:e,functionName:"encodeUrlComponent"}}return t}},encodeUrl:function(e){if(void 0!==e){var t;try{t=encodeURI(e)}catch(t){throw{code:"D3140",stack:(new Error).stack,value:e,functionName:"encodeUrl"}}return t}},decodeUrlComponent:function(e){if(void 0!==e){var t;try{t=decodeURIComponent(e)}catch(t){throw{code:"D3140",stack:(new Error).stack,value:e,functionName:"decodeUrlComponent"}}return t}},decodeUrl:function(e){if(void 0!==e){var t;try{t=decodeURI(e)}catch(t){throw{code:"D3140",stack:(new Error).stack,value:e,functionName:"decodeUrl"}}return t}}}})();t.exports=a}).call(this)}).call(this,void 0!==r.g?r.g:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./utils":6}],3:[function(e,t,r){var n=e("./datetime"),a=e("./functions"),i=e("./utils"),o=e("./parser"),s=e("./signature"),l=function(){"use strict";var e=i.isNumeric,t=i.isArrayOfStrings,r=i.isArrayOfNumbers,l=i.createSequence,c=i.isSequence,u=i.isFunction,p=i.isLambda,d=i.isIterable,f=i.getFunctionArity,h=i.isDeepEqual,m=I(null);function*g(r,n,i){var o,s=i.lookup("__evaluate_entry");switch(s&&s(r,n,i),r.type){case"path":o=yield*function*(e,t,r){var n,a;n=Array.isArray(t)&&"variable"!==e.steps[0].type?t:l(t);for(var i=!1,o=void 0,s=0;s<e.steps.length;s++){var c=e.steps[s];if(c.tuple&&(i=!0),0===s&&c.consarray?a=yield*g(c,n,r):i?o=yield*w(c,n,o,r):a=yield*y(c,n,r,s===e.steps.length-1),!i&&(void 0===a||0===a.length))break;void 0===c.focus&&(n=a)}if(i)if(e.tuple)a=o;else for(a=l(),s=0;s<o.length;s++)a.push(o[s]["@"]);return e.keepSingletonArray&&(Array.isArray(a)&&a.cons&&!a.sequence&&(a=l(a)),a.keepSingleton=!0),e.hasOwnProperty("group")&&(a=yield*j(e.group,i?o:a,r)),a}(r,n,i);break;case"binary":o=yield*function*(t,r,n){var i,o=yield*g(t.lhs,r,n),s=t.value,l=function*(){return yield*g(t.rhs,r,n)};if("and"===s||"or"===s)try{return yield*function*(e,t,r){var n,a=E(e);switch(r){case"and":n=a&&E(yield*t());break;case"or":n=a||E(yield*t())}return n}(o,l,s)}catch(e){throw e.position=t.position,e.token=s,e}var c=yield*l();try{switch(s){case"+":case"-":case"*":case"/":case"%":i=function(t,r,n){var a;if(void 0!==t&&!e(t))throw{code:"T2001",stack:(new Error).stack,value:t};if(void 0!==r&&!e(r))throw{code:"T2002",stack:(new Error).stack,value:r};if(void 0===t||void 0===r)return a;switch(n){case"+":a=t+r;break;case"-":a=t-r;break;case"*":a=t*r;break;case"/":a=t/r;break;case"%":a=t%r}return a}(o,c,s);break;case"=":case"!=":i=function(e,t,r){var n;if(void 0===e||void 0===t)return!1;switch(r){case"=":n=h(e,t);break;case"!=":n=!h(e,t)}return n}(o,c,s);break;case"<":case"<=":case">":case">=":i=function(e,t,r){var n,a=typeof e,i=typeof t;if("undefined"!==a&&"string"!==a&&"number"!==a||"undefined"!==i&&"string"!==i&&"number"!==i)throw{code:"T2010",stack:(new Error).stack,value:"string"!==a&&"number"!==a?e:t};if("undefined"!==a&&"undefined"!==i){if(a!==i)throw{code:"T2009",stack:(new Error).stack,value:e,value2:t};switch(r){case"<":n=e<t;break;case"<=":n=e<=t;break;case">":n=e>t;break;case">=":n=e>=t}return n}}(o,c,s);break;case"&":i=function(e,t){var r="",n="";return void 0!==e&&(r=a.string(e)),void 0!==t&&(n=a.string(t)),r.concat(n)}(o,c);break;case"..":i=function(e,t){var r;if(void 0!==e&&!Number.isInteger(e))throw{code:"T2003",stack:(new Error).stack,value:e};if(void 0!==t&&!Number.isInteger(t))throw{code:"T2004",stack:(new Error).stack,value:t};if(void 0===e||void 0===t)return r;if(e>t)return r;var n=t-e+1;if(n>1e7)throw{code:"D2014",stack:(new Error).stack,value:n};r=new Array(n);for(var a=e,i=0;a<=t;a++,i++)r[i]=a;return r.sequence=!0,r}(o,c);break;case"in":i=function(e,t){var r=!1;if(void 0===e||void 0===t)return!1;Array.isArray(t)||(t=[t]);for(var n=0;n<t.length;n++)if(t[n]===e){r=!0;break}return r}(o,c)}}catch(e){throw e.position=t.position,e.token=s,e}return i}(r,n,i);break;case"unary":o=yield*function*(t,r,n){var i;switch(t.value){case"-":if(void 0===(i=yield*g(t.expression,r,n)))i=void 0;else{if(!e(i))throw{code:"D1002",stack:(new Error).stack,position:t.position,token:t.value,value:i};i=-i}break;case"[":i=[];for(var o=0;o<t.expressions.length;o++){var s=t.expressions[o],l=yield*g(s,r,n);void 0!==l&&("["===s.value?i.push(l):i=a.append(i,l))}t.consarray&&Object.defineProperty(i,"cons",{enumerable:!1,configurable:!1,value:!0});break;case"{":i=yield*j(t,r,n)}return i}(r,n,i);break;case"name":o=function(e,t,r){return a.lookup(t,e.value)}(r,n);break;case"string":case"number":case"value":o=function(e){return e.value}(r);break;case"wildcard":o=function(e,t){var r=l();return Array.isArray(t)&&t.outerWrapper&&t.length>0&&(t=t[0]),null!==t&&"object"==typeof t&&Object.keys(t).forEach((function(e){var n=t[e];Array.isArray(n)?(n=O(n),r=a.append(r,n)):r.push(n)})),r}(0,n);break;case"descendant":o=function(e,t){var r,n=l();return void 0!==t&&(k(t,n),r=1===n.length?n[0]:n),r}(0,n);break;case"parent":o=i.lookup(r.slot.label);break;case"condition":o=yield*function*(e,t,r){var n,i=yield*g(e.condition,t,r);return a.boolean(i)?n=yield*g(e.then,t,r):void 0!==e.else&&(n=yield*g(e.else,t,r)),n}(r,n,i);break;case"block":o=yield*function*(e,t,r){for(var n,a=I(r),i=0;i<e.expressions.length;i++)n=yield*g(e.expressions[i],t,a);return n}(r,n,i);break;case"bind":o=yield*function*(e,t,r){var n=yield*g(e.rhs,t,r);return r.bind(e.lhs.value,n),n}(r,n,i);break;case"regex":o=function(e){var t=new z.RegexEngine(e.value),r=function(n,a){var i;t.lastIndex=a||0;var o=t.exec(n);if(null!==o){if(i={match:o[0],start:o.index,end:o.index+o[0].length,groups:[]},o.length>1)for(var s=1;s<o.length;s++)i.groups.push(o[s]);i.next=function(){if(!(t.lastIndex>=n.length)){var a=r(n,t.lastIndex);if(a&&""===a.match)throw{code:"D1004",stack:(new Error).stack,position:e.position,value:e.value.source};return a}}}return i};return r}(r);break;case"function":o=yield*T(r,n,i);break;case"variable":o=function(e,t,r){return""===e.value?t&&t.outerWrapper?t[0]:t:r.lookup(e.value)}(r,n,i);break;case"lambda":o=function(e,t,r){var n={_jsonata_lambda:!0,input:t,environment:r,arguments:e.arguments,signature:e.signature,body:e.body};return!0===e.thunk&&(n.thunk=!0),n.apply=function*(e,r){return yield*A(n,r,t,e.environment)},n}(r,n,i);break;case"partial":o=yield*function*(e,t,r){for(var n,a=[],i=0;i<e.arguments.length;i++){var o=e.arguments[i];"operator"===o.type&&"?"===o.value?a.push(o):a.push(yield*g(o,t,r))}var s=yield*g(e.procedure,t,r);if(void 0===s&&"path"===e.procedure.type&&r.lookup(e.procedure.steps[0].value))throw{code:"T1007",stack:(new Error).stack,position:e.position,token:e.procedure.steps[0].value};if(p(s))n=C(s,a);else if(s&&!0===s._jsonata_function)n=_(s.implementation,a);else{if("function"!=typeof s)throw{code:"T1008",stack:(new Error).stack,position:e.position,token:"path"===e.procedure.type?e.procedure.steps[0].value:e.procedure.value};n=_(s,a)}return n}(r,n,i);break;case"apply":o=yield*function*(e,t,r){var n,a=yield*g(e.lhs,t,r);if("function"===e.rhs.type)n=yield*T(e.rhs,t,r,{context:a});else{var i=yield*g(e.rhs,t,r);if(!u(i))throw{code:"T2006",stack:(new Error).stack,position:e.position,value:i};if(u(a)){var o=yield*g(D,null,r);n=yield*A(o,[a,i],null,r)}else n=yield*A(i,[a],null,r)}return n}(r,n,i);break;case"transform":o=function(e,r,n){return $((function*(r){if(void 0!==r){var a=n.lookup("clone");if(!u(a))throw{code:"T2013",stack:(new Error).stack,position:e.position};var i=yield*A(a,[r],null,n),o=yield*g(e.pattern,i,n);if(void 0!==o){Array.isArray(o)||(o=[o]);for(var s=0;s<o.length;s++){var l=o[s];if(l&&(l.isPrototypeOf(i)||l instanceof Object.constructor))throw{code:"D1010",stack:(new Error).stack,position:e.position};var c=yield*g(e.update,l,n),p=typeof c;if("undefined"!==p){if("object"!==p||null===c||Array.isArray(c))throw{code:"T2011",stack:(new Error).stack,position:e.update.position,value:c};for(var d in c)l[d]=c[d]}if(void 0!==e.delete){var f=yield*g(e.delete,l,n);if(void 0!==f){var h=f;if(Array.isArray(f)||(f=[f]),!t(f))throw{code:"T2012",stack:(new Error).stack,position:e.delete.position,value:h};for(var m=0;m<f.length;m++)"object"==typeof l&&null!==l&&delete l[f[m]]}}}}return i}}),"<(oa):o>")}(r,0,i)}if(!i.async||null!=o&&"function"==typeof o.then||(o=Promise.resolve(o)),i.async&&"function"==typeof o.then&&r.nextFunction&&"function"==typeof o[r.nextFunction]||(o=yield o),Object.prototype.hasOwnProperty.call(r,"predicate"))for(var d=0;d<r.predicate.length;d++)o=yield*x(r.predicate[d].expr,o,i);"path"!==r.type&&Object.prototype.hasOwnProperty.call(r,"group")&&(o=yield*j(r.group,o,i));var f=i.lookup("__evaluate_exit");return f&&f(r,n,i,o),o&&c(o)&&!o.tupleStream&&(r.keepArray&&(o.keepSingleton=!0),0===o.length?o=void 0:1===o.length&&(o=o.keepSingleton?o:o[0])),o}function v(e,t){var r=I(e);for(const e in t)r.bind(e,t[e]);return r}function*y(e,t,r,n){var a;if("sort"===e.type)return a=yield*P(e,t,r),e.stages&&(a=yield*b(e.stages,a,r)),a;a=l();for(var i=0;i<t.length;i++){var o=yield*g(e,t[i],r);if(e.stages)for(var s=0;s<e.stages.length;s++)o=yield*x(e.stages[s].expr,o,r);void 0!==o&&a.push(o)}var u=l();return n&&1===a.length&&Array.isArray(a[0])&&!c(a[0])?u=a[0]:a.forEach((function(e){!Array.isArray(e)||e.cons?u.push(e):e.forEach((e=>u.push(e)))})),u}function*b(e,t,r){for(var n=t,a=0;a<e.length;a++){var i=e[a];switch(i.type){case"filter":n=yield*x(i.expr,n,r);break;case"index":for(var o=0;o<n.length;o++)n[o][i.value]=o}}return n}function*w(e,t,r,n){var a;if("sort"===e.type){if(r)a=yield*P(e,r,n);else{var i=yield*P(e,t,n);(a=l()).tupleStream=!0;for(var o=0;o<i.length;o++){var s={"@":i[o]};s[e.index]=o,a.push(s)}}return e.stages&&(a=yield*b(e.stages,a,n)),a}(a=l()).tupleStream=!0;var c=n;void 0===r&&(r=t.map((e=>({"@":e}))));for(var u=0;u<r.length;u++){c=v(n,r[u]);var p=yield*g(e,r[u]["@"],c);if(void 0!==p){Array.isArray(p)||(p=[p]);for(var d=0;d<p.length;d++)s={},Object.assign(s,r[u]),p.tupleStream?Object.assign(s,p[d]):(e.focus?(s[e.focus]=p[d],s["@"]=r[u]["@"]):s["@"]=p[d],e.index&&(s[e.index]=d),e.ancestor&&(s[e.ancestor.label]=r[u]["@"])),a.push(s)}}return e.stages&&(a=yield*b(e.stages,a,n)),a}function*x(t,n,i){var o=l();if(n&&n.tupleStream&&(o.tupleStream=!0),Array.isArray(n)||(n=l(n)),"number"===t.type){var s=Math.floor(t.value);s<0&&(s=n.length+s),void 0!==(c=n[s])&&(Array.isArray(c)?o=c:o.push(c))}else for(s=0;s<n.length;s++){var c,u=c=n[s],p=i;n.tupleStream&&(u=c["@"],p=v(i,c));var d=yield*g(t,u,p);e(d)&&(d=[d]),r(d)?d.forEach((function(e){var t=Math.floor(e);t<0&&(t=n.length+t),t===s&&o.push(c)})):a.boolean(d)&&o.push(c)}return o}function O(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((function(e){O(e,t)})):t.push(e),t}function k(e,t){Array.isArray(e)||t.push(e),Array.isArray(e)?e.forEach((function(e){k(e,t)})):null!==e&&"object"==typeof e&&Object.keys(e).forEach((function(r){k(e[r],t)}))}function E(e){var t=a.boolean(e);return void 0!==t&&t}function*j(e,t,r){var n={},i={},o=!(!t||!t.tupleStream);Array.isArray(t)||(t=l(t)),0===t.length&&t.push(void 0);for(var s=0;s<t.length;s++)for(var c=t[s],u=o?v(r,c):r,p=0;p<e.lhs.length;p++){var d=e.lhs[p],f=yield*g(d[0],o?c["@"]:c,u);if("string"!=typeof f&&void 0!==f)throw{code:"T1003",stack:(new Error).stack,position:e.position,value:f};if(void 0!==f){var h={data:c,exprIndex:p};if(i.hasOwnProperty(f)){if(i[f].exprIndex!==p)throw{code:"D1009",stack:(new Error).stack,position:e.position,value:f};i[f].data=a.append(i[f].data,c)}else i[f]=h}}for(f in i){var m=(h=i[f]).data;if(u=r,o){var y=S(h.data);m=y["@"],delete y["@"],u=v(r,y)}var b=yield*g(e.lhs[h.exprIndex][1],m,u);void 0!==b&&(n[f]=b)}return n}function S(e){if(!Array.isArray(e))return e;var t={};Object.assign(t,e[0]);for(var r=1;r<e.length;r++)for(const n in e[r])t[n]=a.append(t[n],e[r][n]);return t}function*P(e,t,r){var n=t,i=!!t.tupleStream,o={environment:r,input:t};return yield*a.sort.apply(o,[n,function*(t,n){for(var a=0,o=0;0===a&&o<e.terms.length;o++){var s=e.terms[o],l=t,c=r;i&&(l=t["@"],c=v(r,t));var u=yield*g(s.expression,l,c);l=n,c=r,i&&(l=n["@"],c=v(r,n));var p=yield*g(s.expression,l,c),d=typeof u,f=typeof p;if("undefined"!==d)if("undefined"!==f){if("string"!==d&&"number"!==d||"string"!==f&&"number"!==f)throw{code:"T2008",stack:(new Error).stack,position:e.position,value:"string"!==d&&"number"!==d?u:p};if(d!==f)throw{code:"T2007",stack:(new Error).stack,position:e.position,value:u,value2:p};u!==p&&(a=u<p?-1:1,!0===s.descending&&(a=-a))}else a=-1;else a="undefined"===f?0:1}return 1===a}])}var D=o("function($f, $g) { function($x){ $g($f($x)) } }");function*T(e,t,r,n){var a,i=yield*g(e.procedure,t,r);if(void 0===i&&"path"===e.procedure.type&&r.lookup(e.procedure.steps[0].value))throw{code:"T1005",stack:(new Error).stack,position:e.position,token:e.procedure.steps[0].value};var o=[];void 0!==n&&o.push(n.context);for(var s=0;s<e.arguments.length;s++){const n=yield*g(e.arguments[s],t,r);if(u(n)){const e=function*(...e){return yield*A(n,e,null,r)};e.arity=f(n),o.push(e)}else o.push(n)}var l="path"===e.procedure.type?e.procedure.steps[0].value:e.procedure.value;try{"object"==typeof i&&(i.token=l,i.position=e.position),a=yield*A(i,o,t,r)}catch(t){throw t.position||(t.position=e.position),t.token||(t.token=l),t}return a}function*A(e,t,r,n){var a;for(a=yield*F(e,t,r,n);p(a)&&!0===a.thunk;){var i=yield*g(a.body.procedure,a.input,a.environment);"variable"===a.body.procedure.type&&(i.token=a.body.procedure.value),i.position=a.body.procedure.position;for(var o=[],s=0;s<a.body.arguments.length;s++)o.push(yield*g(a.body.arguments[s],a.input,a.environment));a=yield*F(i,o,r,n)}return a}function*F(e,t,r,n){var a;try{var i=t;if(e&&(i=function(e,t,r){return void 0===e?t:e.validate(t,r)}(e.signature,t,r)),p(e))a=yield*function*(e,t){var r=I(e.environment);return e.arguments.forEach((function(e,n){r.bind(e.value,t[n])})),"function"==typeof e.body?yield*function*(e,t){var r=N(e).map((function(e){return t.lookup(e.trim())})),n={environment:t},a=e.apply(n,r);return d(a)&&(a=yield*a),a}(e.body,r):yield*g(e.body,e.input,r)}(e,i);else if(e&&!0===e._jsonata_function){var o={environment:n,input:r};a=e.implementation.apply(o,i),d(a)&&(a=yield*a)}else{if("function"!=typeof e)throw{code:"T1006",stack:(new Error).stack};a=e.apply(r,i),d(a)&&(a=yield*a)}}catch(t){throw e&&(void 0===t.token&&void 0!==e.token&&(t.token=e.token),t.position=e.position||t.position),t}return a}function C(e,t){var r=I(e.environment),n=[];return e.arguments.forEach((function(e,a){var i=t[a];i&&"operator"===i.type&&"?"===i.value?n.push(e):r.bind(e.value,i)})),{_jsonata_lambda:!0,input:e.input,environment:r,arguments:n,body:e.body}}function _(e,t){var r=N(e),n="function("+(r=r.map((function(e){return"$"+e.trim()}))).join(", ")+"){ _ }",a=o(n);return a.body=e,C(a,t)}function N(e){var t=e.toString();return/\(([^)]*)\)/.exec(t)[1].split(",")}function $(e,t){var r={_jsonata_function:!0,implementation:e};return void 0!==t&&(r.signature=s(t)),r}function I(e){var t={};return{bind:function(e,r){t[e]=r},lookup:function(r){var n;return t.hasOwnProperty(r)?n=t[r]:e&&(n=e.lookup(r)),n},timestamp:e?e.timestamp:null,async:!!e&&e.async,global:e?e.global:{ancestry:[null]}}}m.bind("sum",$(a.sum,"<a<n>:n>")),m.bind("count",$(a.count,"<a:n>")),m.bind("max",$(a.max,"<a<n>:n>")),m.bind("min",$(a.min,"<a<n>:n>")),m.bind("average",$(a.average,"<a<n>:n>")),m.bind("string",$(a.string,"<x-b?:s>")),m.bind("substring",$(a.substring,"<s-nn?:s>")),m.bind("substringBefore",$(a.substringBefore,"<s-s:s>")),m.bind("substringAfter",$(a.substringAfter,"<s-s:s>")),m.bind("lowercase",$(a.lowercase,"<s-:s>")),m.bind("uppercase",$(a.uppercase,"<s-:s>")),m.bind("length",$(a.length,"<s-:n>")),m.bind("trim",$(a.trim,"<s-:s>")),m.bind("pad",$(a.pad,"<s-ns?:s>")),m.bind("match",$(a.match,"<s-f<s:o>n?:a<o>>")),m.bind("contains",$(a.contains,"<s-(sf):b>")),m.bind("replace",$(a.replace,"<s-(sf)(sf)n?:s>")),m.bind("split",$(a.split,"<s-(sf)n?:a<s>>")),m.bind("join",$(a.join,"<a<s>s?:s>")),m.bind("formatNumber",$(a.formatNumber,"<n-so?:s>")),m.bind("formatBase",$(a.formatBase,"<n-n?:s>")),m.bind("formatInteger",$(n.formatInteger,"<n-s:s>")),m.bind("parseInteger",$(n.parseInteger,"<s-s:n>")),m.bind("number",$(a.number,"<(nsb)-:n>")),m.bind("floor",$(a.floor,"<n-:n>")),m.bind("ceil",$(a.ceil,"<n-:n>")),m.bind("round",$(a.round,"<n-n?:n>")),m.bind("abs",$(a.abs,"<n-:n>")),m.bind("sqrt",$(a.sqrt,"<n-:n>")),m.bind("power",$(a.power,"<n-n:n>")),m.bind("random",$(a.random,"<:n>")),m.bind("boolean",$(a.boolean,"<x-:b>")),m.bind("not",$(a.not,"<x-:b>")),m.bind("map",$(a.map,"<af>")),m.bind("zip",$(a.zip,"<a+>")),m.bind("filter",$(a.filter,"<af>")),m.bind("single",$(a.single,"<af?>")),m.bind("reduce",$(a.foldLeft,"<afj?:j>")),m.bind("sift",$(a.sift,"<o-f?:o>")),m.bind("keys",$(a.keys,"<x-:a<s>>")),m.bind("lookup",$(a.lookup,"<x-s:x>")),m.bind("append",$(a.append,"<xx:a>")),m.bind("exists",$(a.exists,"<x:b>")),m.bind("spread",$(a.spread,"<x-:a<o>>")),m.bind("merge",$(a.merge,"<a<o>:o>")),m.bind("reverse",$(a.reverse,"<a:a>")),m.bind("each",$(a.each,"<o-f:a>")),m.bind("error",$(a.error,"<s?:x>")),m.bind("assert",$(a.assert,"<bs?:x>")),m.bind("type",$(a.type,"<x:s>")),m.bind("sort",$(a.sort,"<af?:a>")),m.bind("shuffle",$(a.shuffle,"<a:a>")),m.bind("distinct",$(a.distinct,"<x:x>")),m.bind("base64encode",$(a.base64encode,"<s-:s>")),m.bind("base64decode",$(a.base64decode,"<s-:s>")),m.bind("encodeUrlComponent",$(a.encodeUrlComponent,"<s-:s>")),m.bind("encodeUrl",$(a.encodeUrl,"<s-:s>")),m.bind("decodeUrlComponent",$(a.decodeUrlComponent,"<s-:s>")),m.bind("decodeUrl",$(a.decodeUrl,"<s-:s>")),m.bind("eval",$((function*(e,t){if(void 0!==e){var r=this.input;void 0!==t&&(r=t,Array.isArray(r)&&!c(r)&&((r=l(r)).outerWrapper=!0));try{var n=o(e,!1)}catch(e){throw R(e),{stack:(new Error).stack,code:"D3120",value:e.message,error:e}}try{var a=yield*g(n,r,this.environment)}catch(e){throw R(e),{stack:(new Error).stack,code:"D3121",value:e.message,error:e}}return a}}),"<sx?:x>")),m.bind("toMillis",$(n.toMillis,"<s-s?:n>")),m.bind("fromMillis",$(n.fromMillis,"<n-s?s?:s>")),m.bind("clone",$((function(e){if(void 0!==e)return JSON.parse(a.string(e))}),"<(oa)-:o>"));var M={S0101:"String literal must be terminated by a matching quote",S0102:"Number out of range: {{token}}",S0103:"Unsupported escape sequence: \\{{token}}",S0104:"The escape sequence \\u must be followed by 4 hex digits",S0105:"Quoted property name must be terminated with a backquote (`)",S0106:"Comment has no closing tag",S0201:"Syntax error: {{token}}",S0202:"Expected {{value}}, got {{token}}",S0203:"Expected {{value}} before end of expression",S0204:"Unknown operator: {{token}}",S0205:"Unexpected token: {{token}}",S0206:"Unknown expression type: {{token}}",S0207:"Unexpected end of expression",S0208:"Parameter {{value}} of function definition must be a variable name (start with $)",S0209:"A predicate cannot follow a grouping expression in a step",S0210:"Each step can only have one grouping expression",S0211:"The symbol {{token}} cannot be used as a unary operator",S0212:"The left side of := must be a variable name (start with $)",S0213:"The literal value {{value}} cannot be used as a step within a path expression",S0214:"The right side of {{token}} must be a variable name (start with $)",S0215:"A context variable binding must precede any predicates on a step",S0216:"A context variable binding must precede the 'order-by' clause on a step",S0217:"The object representing the 'parent' cannot be derived from this expression",S0301:"Empty regular expressions are not allowed",S0302:"No terminating / in regular expression",S0402:"Choice groups containing parameterized types are not supported",S0401:"Type parameters can only be applied to functions and arrays",S0500:"Attempted to evaluate an expression containing syntax error(s)",T0410:"Argument {{index}} of function {{token}} does not match function signature",T0411:"Context value is not a compatible type with argument {{index}} of function {{token}}",T0412:"Argument {{index}} of function {{token}} must be an array of {{type}}",D1001:"Number out of range: {{value}}",D1002:"Cannot negate a non-numeric value: {{value}}",T1003:"Key in object structure must evaluate to a string; got: {{value}}",D1004:"Regular expression matches zero length string",T1005:"Attempted to invoke a non-function. Did you mean ${{{token}}}?",T1006:"Attempted to invoke a non-function",T1007:"Attempted to partially apply a non-function. Did you mean ${{{token}}}?",T1008:"Attempted to partially apply a non-function",D1009:"Multiple key definitions evaluate to same key: {{value}}",D1010:"Attempted to access the Javascript object prototype",T1010:"The matcher function argument passed to function {{token}} does not return the correct object structure",T2001:"The left side of the {{token}} operator must evaluate to a number",T2002:"The right side of the {{token}} operator must evaluate to a number",T2003:"The left side of the range operator (..) must evaluate to an integer",T2004:"The right side of the range operator (..) must evaluate to an integer",D2005:"The left side of := must be a variable name (start with $)",T2006:"The right side of the function application operator ~> must be a function",T2007:"Type mismatch when comparing values {{value}} and {{value2}} in order-by clause",T2008:"The expressions within an order-by clause must evaluate to numeric or string values",T2009:"The values {{value}} and {{value2}} either side of operator {{token}} must be of the same data type",T2010:"The expressions either side of operator {{token}} must evaluate to numeric or string values",T2011:"The insert/update clause of the transform expression must evaluate to an object: {{value}}",T2012:"The delete clause of the transform expression must evaluate to a string or array of strings: {{value}}",T2013:"The transform expression clones the input object using the $clone() function.  This has been overridden in the current scope by a non-function.",D2014:"The size of the sequence allocated by the range operator (..) must not exceed 1e6.  Attempted to allocate {{value}}.",D3001:"Attempting to invoke string function on Infinity or NaN",D3010:"Second argument of replace function cannot be an empty string",D3011:"Fourth argument of replace function must evaluate to a positive number",D3012:"Attempted to replace a matched string with a non-string value",D3020:"Third argument of split function must evaluate to a positive number",D3030:"Unable to cast value to a number: {{value}}",D3040:"Third argument of match function must evaluate to a positive number",D3050:"The second argument of reduce function must be a function with at least two arguments",D3060:"The sqrt function cannot be applied to a negative number: {{value}}",D3061:"The power function has resulted in a value that cannot be represented as a JSON number: base={{value}}, exponent={{exp}}",D3070:"The single argument form of the sort function can only be applied to an array of strings or an array of numbers.  Use the second argument to specify a comparison function",D3080:"The picture string must only contain a maximum of two sub-pictures",D3081:"The sub-picture must not contain more than one instance of the 'decimal-separator' character",D3082:"The sub-picture must not contain more than one instance of the 'percent' character",D3083:"The sub-picture must not contain more than one instance of the 'per-mille' character",D3084:"The sub-picture must not contain both a 'percent' and a 'per-mille' character",D3085:"The mantissa part of a sub-picture must contain at least one character that is either an 'optional digit character' or a member of the 'decimal digit family'",D3086:"The sub-picture must not contain a passive character that is preceded by an active character and that is followed by another active character",D3087:"The sub-picture must not contain a 'grouping-separator' character that appears adjacent to a 'decimal-separator' character",D3088:"The sub-picture must not contain a 'grouping-separator' at the end of the integer part",D3089:"The sub-picture must not contain two adjacent instances of the 'grouping-separator' character",D3090:"The integer part of the sub-picture must not contain a member of the 'decimal digit family' that is followed by an instance of the 'optional digit character'",D3091:"The fractional part of the sub-picture must not contain an instance of the 'optional digit character' that is followed by a member of the 'decimal digit family'",D3092:"A sub-picture that contains a 'percent' or 'per-mille' character must not contain a character treated as an 'exponent-separator'",D3093:"The exponent part of the sub-picture must comprise only of one or more characters that are members of the 'decimal digit family'",D3100:"The radix of the formatBase function must be between 2 and 36.  It was given {{value}}",D3110:"The argument of the toMillis function must be an ISO 8601 formatted timestamp. Given {{value}}",D3120:"Syntax error in expression passed to function eval: {{value}}",D3121:"Dynamic error evaluating the expression passed to function eval: {{value}}",D3130:"Formatting or parsing an integer as a sequence starting with {{value}} is not supported by this implementation",D3131:"In a decimal digit pattern, all digits must be from the same decimal group",D3132:"Unknown component specifier {{value}} in date/time picture string",D3133:"The 'name' modifier can only be applied to months and days in the date/time picture string, not {{value}}",D3134:"The timezone integer format specifier cannot have more than four digits",D3135:"No matching closing bracket ']' in date/time picture string",D3136:"The date/time picture string is missing specifiers required to parse the timestamp",D3137:"{{{message}}}",D3138:"The $single() function expected exactly 1 matching result.  Instead it matched more.",D3139:"The $single() function expected exactly 1 matching result.  Instead it matched 0.",D3140:"Malformed URL passed to ${{{functionName}}}(): {{value}}",D3141:"{{{message}}}"};function R(e){var t=M[e.code];if(void 0!==t){var r=t.replace(/\{\{\{([^}]+)}}}/g,(function(){return e[arguments[1]]}));r=r.replace(/\{\{([^}]+)}}/g,(function(){return JSON.stringify(e[arguments[1]])})),e.message=r}}function z(e,t){var r,a;try{r=o(e,t&&t.recover),a=r.errors,delete r.errors}catch(e){throw R(e),e}var i=I(m),s=new Date;return i.bind("now",$((function(e,t){return n.fromMillis(s.getTime(),e,t)}),"<s?s?:s>")),i.bind("millis",$((function(){return s.getTime()}),"<:n>")),t&&t.RegexEngine?z.RegexEngine=t.RegexEngine:z.RegexEngine=RegExp,{evaluate:function(e,t,n){if(void 0!==a){var o={code:"S0500",position:0};throw R(o),o}var u,p,d;if(void 0!==t)for(var f in u=I(i),t)u.bind(f,t[f]);else u=i;if(u.bind("$",e),s=new Date,u.timestamp=s,Array.isArray(e)&&!c(e)&&((e=l(e)).outerWrapper=!0),"function"==typeof n){u.async=!0;var h=function(e){R(e),n(e,null)},m=function(e){(p=d.next(e)).done?n(null,p.value):p.value.then(m).catch(h)};d=g(r,e,u),(p=d.next()).value.then(m).catch(h)}else try{for(d=g(r,e,u),p=d.next();!p.done;)p=d.next(p.value);return p.value}catch(o){throw R(o),o}},assign:function(e,t){i.bind(e,t)},registerFunction:function(e,t,r){var n=$(t,r);i.bind(e,n)},ast:function(){return r},errors:function(){return a}}}return z.parser=o,z}();t.exports=l},{"./datetime":1,"./functions":2,"./parser":4,"./signature":5,"./utils":6}],4:[function(e,t,r){var n=e("./signature");const a=(()=>{"use strict";var e={".":75,"[":80,"]":0,"{":70,"}":0,"(":80,")":0,",":0,"@":80,"#":80,";":80,":":80,"?":20,"+":50,"-":50,"*":60,"/":60,"%":60,"|":20,"=":40,"<":40,">":40,"^":40,"**":60,"..":20,":=":10,"!=":40,"<=":40,">=":40,"~>":40,and:30,or:25,in:40,"&":50,"!":0,"~":0},t={'"':'"',"\\":"\\","/":"/",b:"\b",f:"\f",n:"\n",r:"\r",t:"\t"},r=function(r){var n=0,a=r.length,i=function(e,t){return{type:e,value:t,position:n}},o=function(s){if(n>=a)return null;for(var l=r.charAt(n);n<a&&" \t\n\r\v".indexOf(l)>-1;)n++,l=r.charAt(n);if("/"===l&&"*"===r.charAt(n+1)){var c=n;for(n+=2,l=r.charAt(n);"*"!==l||"/"!==r.charAt(n+1);)if(l=r.charAt(++n),n>=a)throw{code:"S0106",stack:(new Error).stack,position:c};return n+=2,l=r.charAt(n),o(s)}if(!0!==s&&"/"===l)return n++,i("regex",function(){for(var e,t,i=n,o=0;n<a;){var s=r.charAt(n);if("/"===s&&"\\"!==r.charAt(n-1)&&0===o){if(""===(e=r.substring(i,n)))throw{code:"S0301",stack:(new Error).stack,position:n};for(n++,s=r.charAt(n),i=n;"i"===s||"m"===s;)n++,s=r.charAt(n);return t=r.substring(i,n)+"g",new RegExp(e,t)}"("!==s&&"["!==s&&"{"!==s||"\\"===r.charAt(n-1)||o++,")"!==s&&"]"!==s&&"}"!==s||"\\"===r.charAt(n-1)||o--,n++}throw{code:"S0302",stack:(new Error).stack,position:n}}());if("."===l&&"."===r.charAt(n+1))return n+=2,i("operator","..");if(":"===l&&"="===r.charAt(n+1))return n+=2,i("operator",":=");if("!"===l&&"="===r.charAt(n+1))return n+=2,i("operator","!=");if(">"===l&&"="===r.charAt(n+1))return n+=2,i("operator",">=");if("<"===l&&"="===r.charAt(n+1))return n+=2,i("operator","<=");if("*"===l&&"*"===r.charAt(n+1))return n+=2,i("operator","**");if("~"===l&&">"===r.charAt(n+1))return n+=2,i("operator","~>");if(Object.prototype.hasOwnProperty.call(e,l))return n++,i("operator",l);if('"'===l||"'"===l){var u=l;n++;for(var p="";n<a;){if("\\"===(l=r.charAt(n)))if(n++,l=r.charAt(n),Object.prototype.hasOwnProperty.call(t,l))p+=t[l];else{if("u"!==l)throw{code:"S0103",stack:(new Error).stack,position:n,token:l};var d=r.substr(n+1,4);if(!/^[0-9a-fA-F]+$/.test(d))throw{code:"S0104",stack:(new Error).stack,position:n};var f=parseInt(d,16);p+=String.fromCharCode(f),n+=4}else{if(l===u)return n++,i("string",p);p+=l}n++}throw{code:"S0101",stack:(new Error).stack,position:n}}var h,m=/^-?(0|([1-9][0-9]*))(\.[0-9]+)?([Ee][-+]?[0-9]+)?/.exec(r.substring(n));if(null!==m){var g=parseFloat(m[0]);if(!isNaN(g)&&isFinite(g))return n+=m[0].length,i("number",g);throw{code:"S0102",stack:(new Error).stack,position:n,token:m[0]}}if("`"===l){n++;var v=r.indexOf("`",n);if(-1!==v)return h=r.substring(n,v),n=v+1,i("name",h);throw n=a,{code:"S0105",stack:(new Error).stack,position:n}}for(var y,b=n;;)if(y=r.charAt(b),b===a||" \t\n\r\v".indexOf(y)>-1||Object.prototype.hasOwnProperty.call(e,y)){if("$"===r.charAt(n))return h=r.substring(n+1,b),n=b,i("variable",h);switch(h=r.substring(n,b),n=b,h){case"or":case"in":case"and":return i("operator",h);case"true":return i("value",!0);case"false":return i("value",!1);case"null":return i("value",null);default:return n===a&&""===h?null:i("name",h)}}else b++};return o};return function(t,a){var i,o,s={},l=[],c=function(){var e=[];"(end)"!==i.id&&e.push({type:i.type,value:i.value,position:i.position});for(var t=o();null!==t;)e.push(t),t=o();return e},u={nud:function(){var e={code:"S0211",token:this.value,position:this.position};if(a)return e.remaining=c(),e.type="error",l.push(e),e;throw e.stack=(new Error).stack,e}},p=function(e,t){var r=s[e];return t=t||0,r?t>=r.lbp&&(r.lbp=t):((r=Object.create(u)).id=r.value=e,r.lbp=t,s[e]=r),r},d=function(e){if(a){e.remaining=c(),l.push(e);var t=s["(error)"];return(i=Object.create(t)).error=e,i.type="(error)",i}throw e.stack=(new Error).stack,e},f=function(e,r){if(e&&i.id!==e){var n={code:"(end)"===i.id?"S0203":"S0202",position:i.position,token:i.value,value:e};return d(n)}var a=o(r);if(null===a)return(i=s["(end)"]).position=t.length,i;var l,c=a.value,u=a.type;switch(u){case"name":case"variable":l=s["(name)"];break;case"operator":if(!(l=s[c]))return d({code:"S0204",stack:(new Error).stack,position:a.position,token:c});break;case"string":case"number":case"value":l=s["(literal)"];break;case"regex":u="regex",l=s["(regex)"];break;default:return d({code:"S0205",stack:(new Error).stack,position:a.position,token:c})}return(i=Object.create(l)).value=c,i.type=u,i.position=a.position,i},h=function(e){var t,r=i;for(f(null,!0),t=r.nud();e<i.lbp;)r=i,f(),t=r.led(t);return t},m=function(e){p(e,0).nud=function(){return this}},g=function(t,r,n){var a=r||e[t],i=p(t,a);return i.led=n||function(e){return this.lhs=e,this.rhs=h(a),this.type="binary",this},i},v=function(e,t,r){var n=p(e,t);return n.led=r,n},y=function(e,t){var r=p(e);return r.nud=t||function(){return this.expression=h(70),this.type="unary",this},r};m("(end)"),m("(name)"),m("(literal)"),m("(regex)"),p(":"),p(";"),p(","),p(")"),p("]"),p("}"),p(".."),g("."),g("+"),g("-"),g("*"),g("/"),g("%"),g("="),g("<"),g(">"),g("!="),g("<="),g(">="),g("&"),g("and"),g("or"),g("in"),m("and"),m("or"),m("in"),y("-"),g("~>"),v("(error)",10,(function(e){return this.lhs=e,this.error=i.error,this.remaining=c(),this.type="error",this})),y("*",(function(){return this.type="wildcard",this})),y("**",(function(){return this.type="descendant",this})),y("%",(function(){return this.type="parent",this})),g("(",e["("],(function(e){if(this.procedure=e,this.type="function",this.arguments=[],")"!==i.id)for(;"operator"===i.type&&"?"===i.id?(this.type="partial",this.arguments.push(i),f("?")):this.arguments.push(h(0)),","===i.id;)f(",");if(f(")",!0),"name"===e.type&&("function"===e.value||"λ"===e.value)){if(this.arguments.forEach((function(e,t){if("variable"!==e.type)return d({code:"S0208",stack:(new Error).stack,position:e.position,token:e.value,value:t+1})})),this.type="lambda","<"===i.id){for(var t=i.position,r=1,a="<";r>0&&"{"!==i.id&&"(end)"!==i.id;){var o=f();">"===o.id?r--:"<"===o.id&&r++,a+=o.value}f(">");try{this.signature=n(a)}catch(e){return e.position=t+e.offset,d(e)}}f("{"),this.body=h(0),f("}")}return this})),y("(",(function(){for(var e=[];")"!==i.id&&(e.push(h(0)),";"===i.id);)f(";");return f(")",!0),this.type="block",this.expressions=e,this})),y("[",(function(){var e=[];if("]"!==i.id)for(;;){var t=h(0);if(".."===i.id){var r={type:"binary",value:"..",position:i.position,lhs:t};f(".."),r.rhs=h(0),t=r}if(e.push(t),","!==i.id)break;f(",")}return f("]",!0),this.expressions=e,this.type="unary",this})),g("[",e["["],(function(t){if("]"===i.id){for(var r=t;r&&"binary"===r.type&&"["===r.value;)r=r.lhs;return r.keepArray=!0,f("]"),t}return this.lhs=t,this.rhs=h(e["]"]),this.type="binary",f("]",!0),this})),g("^",e["^"],(function(e){f("(");for(var t=[];;){var r={descending:!1};if("<"===i.id?f("<"):">"===i.id&&(r.descending=!0,f(">")),r.expression=h(0),t.push(r),","!==i.id)break;f(",")}return f(")"),this.lhs=e,this.rhs=t,this.type="binary",this}));var b=function(e){var t=[];if("}"!==i.id)for(;;){var r=h(0);f(":");var n=h(0);if(t.push([r,n]),","!==i.id)break;f(",")}return f("}",!0),void 0===e?(this.lhs=t,this.type="unary"):(this.lhs=e,this.rhs=t,this.type="binary"),this};y("{",b),g("{",e["{"],b),v(":=",e[":="],(function(t){return"variable"!==t.type?d({code:"S0212",stack:(new Error).stack,position:t.position,token:t.value}):(this.lhs=t,this.rhs=h(e[":="]-1),this.type="binary",this)})),g("@",e["@"],(function(t){return this.lhs=t,this.rhs=h(e["@"]),"variable"!==this.rhs.type?d({code:"S0214",stack:(new Error).stack,position:this.rhs.position,token:"@"}):(this.type="binary",this)})),g("#",e["#"],(function(t){return this.lhs=t,this.rhs=h(e["#"]),"variable"!==this.rhs.type?d({code:"S0214",stack:(new Error).stack,position:this.rhs.position,token:"#"}):(this.type="binary",this)})),g("?",e["?"],(function(e){return this.type="condition",this.condition=e,this.then=h(0),":"===i.id&&(f(":"),this.else=h(0)),this})),y("|",(function(){return this.type="transform",this.pattern=h(0),f("|"),this.update=h(0),","===i.id&&(f(","),this.delete=h(0)),f("|"),this}));var w=function(e){var t;if("function"!==e.type||e.predicate)if("condition"===e.type)e.then=w(e.then),void 0!==e.else&&(e.else=w(e.else)),t=e;else if("block"===e.type){var r=e.expressions.length;r>0&&(e.expressions[r-1]=w(e.expressions[r-1])),t=e}else t=e;else{var n={type:"lambda",thunk:!0,arguments:[],position:e.position};n.body=e,t=n}return t},x=0,O=0,k=[],E=function(e,t){switch(e.type){case"name":case"wildcard":t.level--,0===t.level&&(void 0===e.ancestor||(k[t.index].slot.label=e.ancestor.label),e.ancestor=t,e.tuple=!0);break;case"parent":t.level++;break;case"block":e.expressions.length>0&&(e.tuple=!0,t=E(e.expressions[e.expressions.length-1],t));break;case"path":e.tuple=!0;var r=e.steps.length-1;for(t=E(e.steps[r--],t);t.level>0&&r>=0;)t=E(e.steps[r--],t);break;default:throw{code:"S0217",token:e.type,position:e.position}}return t},j=function(e,t){if(void 0!==t.seekingParent||"parent"===t.type){var r=void 0!==t.seekingParent?t.seekingParent:[];"parent"===t.type&&r.push(t.slot),void 0===e.seekingParent?e.seekingParent=r:Array.prototype.push.apply(e.seekingParent,r)}},S=function(e){var t=e.steps.length-1,r=e.steps[t],n=void 0!==r.seekingParent?r.seekingParent:[];"parent"===r.type&&n.push(r.slot);for(var a=0;a<n.length;a++){var i=n[a];for(t=e.steps.length-2;i.level>0;){if(t<0){void 0===e.seekingParent?e.seekingParent=[i]:e.seekingParent.push(i);break}for(var o=e.steps[t--];t>=0&&o.focus&&e.steps[t].focus;)o=e.steps[t--];i=E(o,i)}}},P=function(e){var t;switch(e.type){case"binary":switch(e.value){case".":var r=P(e.lhs);t="path"===r.type?r:{type:"path",steps:[r]},"parent"===r.type&&(t.seekingParent=[r.slot]);var n=P(e.rhs);"function"===n.type&&"path"===n.procedure.type&&1===n.procedure.steps.length&&"name"===n.procedure.steps[0].type&&"function"===t.steps[t.steps.length-1].type&&(t.steps[t.steps.length-1].nextFunction=n.procedure.steps[0].value),"path"===n.type?Array.prototype.push.apply(t.steps,n.steps):(void 0!==n.predicate&&(n.stages=n.predicate,delete n.predicate),t.steps.push(n)),t.steps.filter((function(e){if("number"===e.type||"value"===e.type)throw{code:"S0213",stack:(new Error).stack,position:e.position,value:e.value};return"string"===e.type})).forEach((function(e){e.type="name"})),t.steps.filter((function(e){return!0===e.keepArray})).length>0&&(t.keepSingletonArray=!0);var i=t.steps[0];"unary"===i.type&&"["===i.value&&(i.consarray=!0);var o=t.steps[t.steps.length-1];"unary"===o.type&&"["===o.value&&(o.consarray=!0),S(t);break;case"[":var s=t=P(e.lhs),c="predicate";if("path"===t.type&&(s=t.steps[t.steps.length-1],c="stages"),void 0!==s.group)throw{code:"S0209",stack:(new Error).stack,position:e.position};void 0===s[c]&&(s[c]=[]);var u=P(e.rhs);void 0!==u.seekingParent&&(u.seekingParent.forEach((e=>{1===e.level?E(s,e):e.level--})),j(s,u)),s[c].push({type:"filter",expr:u,position:e.position});break;case"{":if(void 0!==(t=P(e.lhs)).group)throw{code:"S0210",stack:(new Error).stack,position:e.position};t.group={lhs:e.rhs.map((function(e){return[P(e[0]),P(e[1])]})),position:e.position};break;case"^":"path"!==(t=P(e.lhs)).type&&(t={type:"path",steps:[t]});var p={type:"sort",position:e.position};p.terms=e.rhs.map((function(e){var t=P(e.expression);return j(p,t),{descending:e.descending,expression:t}})),t.steps.push(p),S(t);break;case":=":(t={type:"bind",value:e.value,position:e.position}).lhs=P(e.lhs),t.rhs=P(e.rhs),j(t,t.rhs);break;case"@":if(t=P(e.lhs),s=t,"path"===t.type&&(s=t.steps[t.steps.length-1]),void 0!==s.stages||void 0!==s.predicate)throw{code:"S0215",stack:(new Error).stack,position:e.position};if("sort"===s.type)throw{code:"S0216",stack:(new Error).stack,position:e.position};e.keepArray&&(s.keepArray=!0),s.focus=e.rhs.value,s.tuple=!0;break;case"#":t=P(e.lhs),s=t,"path"===t.type?s=t.steps[t.steps.length-1]:(t={type:"path",steps:[t]},void 0!==s.predicate&&(s.stages=s.predicate,delete s.predicate)),void 0===s.stages?s.index=e.rhs.value:s.stages.push({type:"index",value:e.rhs.value,position:e.position}),s.tuple=!0;break;case"~>":(t={type:"apply",value:e.value,position:e.position}).lhs=P(e.lhs),t.rhs=P(e.rhs);break;default:(t={type:e.type,value:e.value,position:e.position}).lhs=P(e.lhs),t.rhs=P(e.rhs),j(t,t.lhs),j(t,t.rhs)}break;case"unary":t={type:e.type,value:e.value,position:e.position},"["===e.value?t.expressions=e.expressions.map((function(e){var r=P(e);return j(t,r),r})):"{"===e.value?t.lhs=e.lhs.map((function(e){var r=P(e[0]);j(t,r);var n=P(e[1]);return j(t,n),[r,n]})):(t.expression=P(e.expression),"-"===e.value&&"number"===t.expression.type?(t=t.expression).value=-t.value:j(t,t.expression));break;case"function":case"partial":(t={type:e.type,name:e.name,value:e.value,position:e.position}).arguments=e.arguments.map((function(e){var r=P(e);return j(t,r),r})),t.procedure=P(e.procedure);break;case"lambda":t={type:e.type,arguments:e.arguments,signature:e.signature,position:e.position};var d=P(e.body);t.body=w(d);break;case"condition":(t={type:e.type,position:e.position}).condition=P(e.condition),j(t,t.condition),t.then=P(e.then),j(t,t.then),void 0!==e.else&&(t.else=P(e.else),j(t,t.else));break;case"transform":(t={type:e.type,position:e.position}).pattern=P(e.pattern),t.update=P(e.update),void 0!==e.delete&&(t.delete=P(e.delete));break;case"block":(t={type:e.type,position:e.position}).expressions=e.expressions.map((function(e){var r=P(e);return j(t,r),(r.consarray||"path"===r.type&&r.steps[0].consarray)&&(t.consarray=!0),r}));break;case"name":t={type:"path",steps:[e]},e.keepArray&&(t.keepSingletonArray=!0);break;case"parent":t={type:"parent",slot:{label:"!"+x++,level:1,index:O++}},k.push(t);break;case"string":case"number":case"value":case"wildcard":case"descendant":case"variable":case"regex":t=e;break;case"operator":if("and"===e.value||"or"===e.value||"in"===e.value)e.type="name",t=P(e);else{if("?"!==e.value)throw{code:"S0201",stack:(new Error).stack,position:e.position,token:e.value};t=e}break;case"error":t=e,e.lhs&&(t=P(e.lhs));break;default:var f="S0206";"(end)"===e.id&&(f="S0207");var h={code:f,position:e.position,token:e.value};if(a)return l.push(h),{type:"error",error:h};throw h.stack=(new Error).stack,h}return e.keepArray&&(t.keepArray=!0),t};o=r(t),f();var D=h(0);if("(end)"!==i.id){var T={code:"S0201",position:i.position,token:i.value};d(T)}if("parent"===(D=P(D)).type||void 0!==D.seekingParent)throw{code:"S0217",token:D.type,position:D.position};return l.length>0&&(D.errors=l),D}})();t.exports=a},{"./signature":5}],5:[function(e,t,r){var n=e("./utils");const a=(()=>{"use strict";var e={a:"arrays",b:"booleans",f:"functions",n:"numbers",o:"objects",s:"strings"};return function(t){for(var r=1,a=[],i={},o=i;r<t.length;){var s=t.charAt(r);if(":"===s)break;var l=function(){a.push(i),o=i,i={}},c=function(e,t,r,n){for(var a=1,i=t;i<e.length;)if(i++,(s=e.charAt(i))===n){if(0==--a)break}else s===r&&a++;return i};switch(s){case"s":case"n":case"b":case"l":case"o":i.regex="["+s+"m]",i.type=s,l();break;case"a":i.regex="[asnblfom]",i.type=s,i.array=!0,l();break;case"f":i.regex="f",i.type=s,l();break;case"j":i.regex="[asnblom]",i.type=s,l();break;case"x":i.regex="[asnblfom]",i.type=s,l();break;case"-":o.context=!0,o.contextRegex=new RegExp(o.regex),o.regex+="?";break;case"?":case"+":o.regex+=s;break;case"(":var u=c(t,r,"(",")"),p=t.substring(r+1,u);if(-1!==p.indexOf("<"))throw{code:"S0402",stack:(new Error).stack,value:p,offset:r};i.regex="["+p+"m]",i.type="("+p+")",r=u,l();break;case"<":if("a"!==o.type&&"f"!==o.type)throw{code:"S0401",stack:(new Error).stack,value:o.type,offset:r};var d=c(t,r,"<",">");o.subtype=t.substring(r+1,d),r=d}r++}var f="^"+a.map((function(e){return"("+e.regex+")"})).join("")+"$",h=new RegExp(f),m=function(e){var t;if(n.isFunction(e))t="f";else switch(typeof e){case"string":t="s";break;case"number":t="n";break;case"boolean":t="b";break;case"object":t=null===e?"l":Array.isArray(e)?"a":"o";break;default:t="m"}return t};return{definition:t,validate:function(t,r){var n="";t.forEach((function(e){n+=m(e)}));var i=h.exec(n);if(i){var o=[],s=0;return a.forEach((function(n,a){var l=t[s],c=i[a+1];if(""===c)if(n.context&&n.contextRegex){var u=m(r);if(!n.contextRegex.test(u))throw{code:"T0411",stack:(new Error).stack,value:r,index:s+1};o.push(r)}else o.push(l),s++;else c.split("").forEach((function(r){if("a"===n.type){if("m"===r)l=void 0;else{l=t[s];var a=!0;if(void 0!==n.subtype)if("a"!==r&&c!==n.subtype)a=!1;else if("a"===r&&l.length>0){var i=m(l[0]);a=i===n.subtype.charAt(0)&&0===l.filter((function(e){return m(e)!==i})).length}if(!a)throw{code:"T0412",stack:(new Error).stack,value:l,index:s+1,type:e[n.subtype]};"a"!==r&&(l=[l])}o.push(l),s++}else o.push(l),s++}))})),o}!function(e,t){for(var r="^",n=0,i=0;i<a.length;i++){r+=a[i].regex;var o=t.match(r);if(null===o)throw{code:"T0410",stack:(new Error).stack,value:e[n],index:n+1};n=o[0].length}throw{code:"T0410",stack:(new Error).stack,value:e[n],index:n+1}}(t,n)}}}})();t.exports=a},{"./utils":6}],6:[function(e,t,r){const n=(()=>{"use strict";function e(e){var t=!1;if("number"==typeof e&&(t=!isNaN(e))&&!isFinite(e))throw{code:"D1001",value:e,stack:(new Error).stack};return t}var t=("function"==typeof Symbol?Symbol:{}).iterator||"@@iterator";return{isNumeric:e,isArrayOfStrings:function(e){var t=!1;return Array.isArray(e)&&(t=0===e.filter((function(e){return"string"!=typeof e})).length),t},isArrayOfNumbers:function(t){var r=!1;return Array.isArray(t)&&(r=0===t.filter((function(t){return!e(t)})).length),r},createSequence:function(){var e=[];return e.sequence=!0,1===arguments.length&&e.push(arguments[0]),e},isSequence:function(e){return!0===e.sequence&&Array.isArray(e)},isFunction:function(e){return e&&(!0===e._jsonata_function||!0===e._jsonata_lambda)||"function"==typeof e},isLambda:function(e){return e&&!0===e._jsonata_lambda},isIterable:function(e){return"object"==typeof e&&null!==e&&t in e&&"next"in e&&"function"==typeof e.next},getFunctionArity:function(e){return"number"==typeof e.arity?e.arity:"function"==typeof e.implementation?e.implementation.length:"number"==typeof e.length?e.length:e.arguments.length},isDeepEqual:function e(t,r){if(t===r)return!0;if("object"==typeof t&&"object"==typeof r&&null!==t&&null!==r){if(Array.isArray(t)&&Array.isArray(r)){if(t.length!==r.length)return!1;for(var n=0;n<t.length;n++)if(!e(t[n],r[n]))return!1;return!0}var a=Object.getOwnPropertyNames(t),i=Object.getOwnPropertyNames(r);if(a.length!==i.length)return!1;for(a=a.sort(),i=i.sort(),n=0;n<a.length;n++)if(a[n]!==i[n])return!1;for(n=0;n<a.length;n++){var o=a[n];if(!e(t[o],r[o]))return!1}return!0}return!1},stringToArray:function(e){var t=[];for(let r of e)t.push(r);return t}}})();t.exports=n},{}]},{},[3])(3)},6539:(e,t,r)=>{var n=r(7400).Symbol;e.exports=n},9349:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},8213:(e,t,r)=>{var n=r(4701),a=r(2900),i=r(9785),o=r(3854),s=r(2383),l=r(8519),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),u=!r&&a(e),p=!r&&!u&&o(e),d=!r&&!u&&!p&&l(e),f=r||u||p||d,h=f?n(e.length,String):[],m=h.length;for(var g in e)!t&&!c.call(e,g)||f&&("length"==g||p&&("offset"==g||"parent"==g)||d&&("buffer"==g||"byteLength"==g||"byteOffset"==g)||s(g,m))||h.push(g);return h}},9736:(e,t,r)=>{var n=r(6539),a=r(4840),i=r(1258),o=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":o&&o in Object(e)?a(e):i(e)}},5829:(e,t,r)=>{var n=r(9736),a=r(2360);e.exports=function(e){return a(e)&&"[object Arguments]"==n(e)}},6729:(e,t,r)=>{var n=r(8338),a=r(9678),i=r(1611),o=r(6532),s=/^\[object .+?Constructor\]$/,l=Function.prototype,c=Object.prototype,u=l.toString,p=c.hasOwnProperty,d=RegExp("^"+u.call(p).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||a(e))&&(n(e)?d:s).test(o(e))}},6972:(e,t,r)=>{var n=r(9736),a=r(4194),i=r(2360),o={};o["[object Float32Array]"]=o["[object Float64Array]"]=o["[object Int8Array]"]=o["[object Int16Array]"]=o["[object Int32Array]"]=o["[object Uint8Array]"]=o["[object Uint8ClampedArray]"]=o["[object Uint16Array]"]=o["[object Uint32Array]"]=!0,o["[object Arguments]"]=o["[object Array]"]=o["[object ArrayBuffer]"]=o["[object Boolean]"]=o["[object DataView]"]=o["[object Date]"]=o["[object Error]"]=o["[object Function]"]=o["[object Map]"]=o["[object Number]"]=o["[object Object]"]=o["[object RegExp]"]=o["[object Set]"]=o["[object String]"]=o["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&a(e.length)&&!!o[n(e)]}},9464:(e,t,r)=>{var n=r(1611),a=r(6016),i=r(1586),o=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return i(e);var t=a(e),r=[];for(var s in e)("constructor"!=s||!t&&o.call(e,s))&&r.push(s);return r}},1197:(e,t,r)=>{var n=r(1137),a=r(1871),i=r(3132);e.exports=function(e,t){return i(a(e,t,n),e+"")}},4459:(e,t,r)=>{var n=r(551),a=r(2630),i=r(1137),o=a?function(e,t){return a(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:i;e.exports=o},4701:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},9334:e=>{e.exports=function(e){return function(t){return e(t)}}},4937:(e,t,r)=>{var n=r(7400)["__core-js_shared__"];e.exports=n},2630:(e,t,r)=>{var n=r(1822),a=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=a},9120:(e,t,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=n},1822:(e,t,r)=>{var n=r(6729),a=r(5371);e.exports=function(e,t){var r=a(e,t);return n(r)?r:void 0}},4840:(e,t,r)=>{var n=r(6539),a=Object.prototype,i=a.hasOwnProperty,o=a.toString,s=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,s),r=e[s];try{e[s]=void 0;var n=!0}catch(e){}var a=o.call(e);return n&&(t?e[s]=r:delete e[s]),a}},5371:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},2383:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},7535:(e,t,r)=>{var n=r(5638),a=r(68),i=r(2383),o=r(1611);e.exports=function(e,t,r){if(!o(r))return!1;var s=typeof t;return!!("number"==s?a(r)&&i(t,r.length):"string"==s&&t in r)&&n(r[t],e)}},9678:(e,t,r)=>{var n,a=r(4937),i=(n=/[^.]+$/.exec(a&&a.keys&&a.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";e.exports=function(e){return!!i&&i in e}},6016:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},1586:e=>{e.exports=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}},8125:(e,t,r)=>{e=r.nmd(e);var n=r(9120),a=t&&!t.nodeType&&t,i=a&&e&&!e.nodeType&&e,o=i&&i.exports===a&&n.process,s=function(){try{return i&&i.require&&i.require("util").types||o&&o.binding&&o.binding("util")}catch(e){}}();e.exports=s},1258:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},1871:(e,t,r)=>{var n=r(9349),a=Math.max;e.exports=function(e,t,r){return t=a(void 0===t?e.length-1:t,0),function(){for(var i=arguments,o=-1,s=a(i.length-t,0),l=Array(s);++o<s;)l[o]=i[t+o];o=-1;for(var c=Array(t+1);++o<t;)c[o]=i[o];return c[t]=r(l),n(e,this,c)}}},7400:(e,t,r)=>{var n=r(9120),a="object"==typeof self&&self&&self.Object===Object&&self,i=n||a||Function("return this")();e.exports=i},3132:(e,t,r)=>{var n=r(4459),a=r(9591)(n);e.exports=a},9591:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var a=t(),i=16-(a-n);if(n=a,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},6532:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},551:e=>{e.exports=function(e){return function(){return e}}},236:(e,t,r)=>{var n=r(1197),a=r(5638),i=r(7535),o=r(3893),s=Object.prototype,l=s.hasOwnProperty,c=n((function(e,t){e=Object(e);var r=-1,n=t.length,c=n>2?t[2]:void 0;for(c&&i(t[0],t[1],c)&&(n=1);++r<n;)for(var u=t[r],p=o(u),d=-1,f=p.length;++d<f;){var h=p[d],m=e[h];(void 0===m||a(m,s[h])&&!l.call(e,h))&&(e[h]=u[h])}return e}));e.exports=c},5638:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},1137:e=>{e.exports=function(e){return e}},2900:(e,t,r)=>{var n=r(5829),a=r(2360),i=Object.prototype,o=i.hasOwnProperty,s=i.propertyIsEnumerable,l=n(function(){return arguments}())?n:function(e){return a(e)&&o.call(e,"callee")&&!s.call(e,"callee")};e.exports=l},9785:e=>{var t=Array.isArray;e.exports=t},68:(e,t,r)=>{var n=r(8338),a=r(4194);e.exports=function(e){return null!=e&&a(e.length)&&!n(e)}},3854:(e,t,r)=>{e=r.nmd(e);var n=r(7400),a=r(7714),i=t&&!t.nodeType&&t,o=i&&e&&!e.nodeType&&e,s=o&&o.exports===i?n.Buffer:void 0,l=(s?s.isBuffer:void 0)||a;e.exports=l},8338:(e,t,r)=>{var n=r(9736),a=r(1611);e.exports=function(e){if(!a(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},4194:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},1611:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},2360:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},8519:(e,t,r)=>{var n=r(6972),a=r(9334),i=r(8125),o=i&&i.isTypedArray,s=o?a(o):n;e.exports=s},3893:(e,t,r)=>{var n=r(8213),a=r(9464),i=r(68);e.exports=function(e){return i(e)?n(e,!0):a(e)}},7714:e=>{e.exports=function(){return!1}},4195:e=>{"use strict";function t(){var e=Object.create(null),t=0,r=0,n=0,a=!1;function i(t){n--,delete e[t]}this.put=function(t,r,o,s){if(a&&console.log("caching: %s = %j (@%s)",t,r,o),void 0!==o&&("number"!=typeof o||isNaN(o)||o<=0))throw new Error("Cache timeout must be a positive number");if(void 0!==s&&"function"!=typeof s)throw new Error("Cache timeout callback must be a function");var l=e[t];l?clearTimeout(l.timeout):n++;var c={value:r,expire:o+Date.now()};return isNaN(c.expire)||(c.timeout=setTimeout(function(){i(t),s&&s(t,r)}.bind(this),o)),e[t]=c,r},this.del=function(t){var r=!0,n=e[t];return n?(clearTimeout(n.timeout),!isNaN(n.expire)&&n.expire<Date.now()&&(r=!1)):r=!1,r&&i(t),r},this.clear=function(){for(var i in e)clearTimeout(e[i].timeout);n=0,e=Object.create(null),a&&(t=0,r=0)},this.get=function(i){var o=e[i];if(void 0!==o){if(isNaN(o.expire)||o.expire>=Date.now())return a&&t++,o.value;a&&r++,n--,delete e[i]}else a&&r++;return null},this.size=function(){return n},this.memsize=function(){var t,r=0;for(t in e)r++;return r},this.debug=function(e){a=e},this.hits=function(){return t},this.misses=function(){return r},this.keys=function(){return Object.keys(e)},this.exportJson=function(){var t={};for(var r in e){var n=e[r];t[r]={value:n.value,expire:n.expire||"NaN"}}return JSON.stringify(t)},this.importJson=function(t,r){var n=JSON.parse(t),i=Date.now(),o=r&&r.skipDuplicates;for(var s in n)if(n.hasOwnProperty(s)){if(o&&e[s]){a&&console.log("Skipping duplicate imported key '%s'",s);continue}var l=n[s],c=l.expire-i;if(c<=0){this.del(s);continue}c=c>0?c:void 0,this.put(s,l.value,c)}return this.size()}}e.exports=new t,e.exports.Cache=t},7644:e=>{"use strict";e.exports=n},3305:t=>{"use strict";t.exports=e},3545:e=>{"use strict";e.exports=a},7388:e=>{"use strict";e.exports=t},5980:e=>{"use strict";e.exports=i},8283:e=>{"use strict";e.exports=o},2650:e=>{"use strict";e.exports=r}},l={};function c(e){var t=l[e];if(void 0!==t)return t.exports;var r=l[e]={id:e,loaded:!1,exports:{}};return s[e].call(r.exports,r,r.exports,c),r.loaded=!0,r.exports}c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},c.d=(e,t)=>{for(var r in t)c.o(t,r)&&!c.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.nmd=e=>(e.paths=[],e.children||(e.children=[]),e);var u={};return(()=>{"use strict";c.r(u),c.d(u,{plugin:()=>Pn});var e=c(3305),t=c(7388),r=c(2650),n=c.n(r),a=c(7644),i=Object.defineProperty,o=Object.defineProperties,s=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,p=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,f=(e,t,r)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,h=(e,t)=>{for(var r in t||(t={}))p.call(t,r)&&f(e,r,t[r]);if(l)for(var r of l(t))d.call(t,r)&&f(e,r,t[r]);return e};const m=({dataSourceName:e,docsLink:r,hasRequiredFields:i=!0,className:l})=>{const c=(0,t.useTheme2)(),u={container:(0,a.css)({p:{margin:0},"p + p":{marginTop:c.spacing(2)}}),text:(0,a.css)((p=h({},c.typography.body),d={color:c.colors.text.secondary,a:(0,a.css)({color:c.colors.text.link,textDecoration:"underline","&:hover":{textDecoration:"none"}})},o(p,s(d))))};var p,d;return n().createElement("div",{className:(0,a.cx)(u.container,l)},n().createElement("p",{className:u.text},"Before you can use the ",e," data source, you must configure it below or in the config file. For detailed instructions,"," ",n().createElement("a",{href:r,target:"_blank",rel:"noreferrer"},"view the documentation"),"."),i&&n().createElement("p",{className:u.text},n().createElement("i",null,"Fields marked with * are required")))};var g=Object.defineProperty,v=Object.defineProperties,y=Object.getOwnPropertyDescriptors,b=Object.getOwnPropertySymbols,w=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable,O=(e,t,r)=>t in e?g(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,k=(e,t)=>{for(var r in t||(t={}))w.call(t,r)&&O(e,r,t[r]);if(b)for(var r of b(t))x.call(t,r)&&O(e,r,t[r]);return e};const E=({children:e,title:i,description:o,isCollapsible:s=!1,isInitiallyOpen:l=!0,kind:c="section",className:u})=>{const{colors:p,typography:d,spacing:f}=(0,t.useTheme2)(),[h,m]=(0,r.useState)(!s||l),g=h?"angle-up":"angle-down",b="sub-section"===c,w=`${h?"Collapse":"Expand"} section ${i}`,x={header:(0,a.css)({display:"flex",justifyContent:"space-between",alignItems:"center"}),title:(0,a.css)({margin:0}),subtitle:(0,a.css)({margin:0,fontWeight:d.fontWeightRegular}),descriptionText:(0,a.css)((O=k({marginTop:f(b?.25:.5),marginBottom:0},d.bodySmall),E={color:p.text.secondary},v(O,y(E)))),content:(0,a.css)({marginTop:f(2)})};var O,E;return n().createElement("div",{className:u},n().createElement("div",{className:x.header},"section"===c?n().createElement("h3",{className:x.title},i):n().createElement("h6",{className:x.subtitle},i),s&&n().createElement(t.IconButton,{name:g,onClick:()=>m(!h),type:"button",size:"xl","aria-label":w})),o&&n().createElement("p",{className:x.descriptionText},o),h&&n().createElement("div",{className:x.content},e))};var j=Object.defineProperty,S=Object.defineProperties,P=Object.getOwnPropertyDescriptors,D=Object.getOwnPropertySymbols,T=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable,F=(e,t,r)=>t in e?j(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;const C=e=>{var t,r=e,{children:a}=r,i=((e,t)=>{var r={};for(var n in e)T.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&D)for(var n of D(e))t.indexOf(n)<0&&A.call(e,n)&&(r[n]=e[n]);return r})(r,["children"]);return n().createElement(E,(t=((e,t)=>{for(var r in t||(t={}))T.call(t,r)&&F(e,r,t[r]);if(D)for(var r of D(t))A.call(t,r)&&F(e,r,t[r]);return e})({},i),S(t,P({kind:"section"}))),a)};var _=Object.defineProperty,N=Object.defineProperties,$=Object.getOwnPropertyDescriptors,I=Object.getOwnPropertySymbols,M=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable,z=(e,t,r)=>t in e?_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;const U=({config:e,onChange:r,description:i,urlPlaceholder:o,urlTooltip:s,urlLabel:l,className:c})=>{const u=/^(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?$/.test(e.url),p={container:(0,a.css)({maxWidth:578})};return n().createElement(n().Fragment,null,n().createElement(C,{title:"Connection",description:i,className:(0,a.cx)(p.container,c)},n().createElement(t.InlineField,{htmlFor:"connection-url",label:l||"URL",labelWidth:24,tooltip:s||n().createElement(n().Fragment,null,"Specify a complete HTTP URL",n().createElement("br",null),"(for example https://example.com:8080)"),grow:!0,disabled:e.readOnly,required:!0,invalid:!u&&!e.readOnly,error:u?"":"Please enter a valid URL",interactive:!0},n().createElement(t.Input,{id:"connection-url","aria-label":"Data source connection URL",onChange:t=>{return r((n=((e,t)=>{for(var r in t||(t={}))M.call(t,r)&&z(e,r,t[r]);if(I)for(var r of I(t))R.call(t,r)&&z(e,r,t[r]);return e})({},e),a={url:t.currentTarget.value},N(n,$(a))));var n,a},value:e.url||"",placeholder:o||"URL"}))))};var L=Object.defineProperty,B=Object.defineProperties,q=Object.getOwnPropertyDescriptors,W=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable,Y=(e,t,r)=>t in e?L(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,G=(e,t)=>{for(var r in t||(t={}))J.call(t,r)&&Y(e,r,t[r]);if(W)for(var r of W(t))H.call(t,r)&&Y(e,r,t[r]);return e},V=(e,t)=>B(e,q(t));const Q=e=>{var n=e,{isConfigured:a,onReset:i}=n,o=((e,t)=>{var r={};for(var n in e)J.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&W)for(var n of W(e))t.indexOf(n)<0&&H.call(e,n)&&(r[n]=e[n]);return r})(n,["isConfigured","onReset"]);return r.createElement(t.HorizontalGroup,null,!a&&r.createElement(t.Input,V(G({},o),{type:"password"})),a&&r.createElement(t.Input,V(G({},o),{type:"text",disabled:!0,value:"configured"})),a&&r.createElement(t.Button,{onClick:i,variant:"secondary"},"Reset"))},Z=()=>({inlineFieldNoMarginRight:(0,a.css)({marginRight:0}),inlineFieldWithSecret:(0,a.css)({'[class$="layoutChildrenWrapper"]:first-child':{flexGrow:1}})}),K=({user:e,passwordConfigured:r,userTooltip:i="The username of the data source account",passwordTooltip:o="The password of the data source account",onUserChange:s,onPasswordChange:l,onPasswordReset:c,readOnly:u})=>{const p=Z(),d={lastInlineField:(0,a.css)({marginBottom:0})};return n().createElement(n().Fragment,null,n().createElement(t.InlineField,{className:p.inlineFieldNoMarginRight,label:"User",labelWidth:24,tooltip:i,required:!0,htmlFor:"basic-auth-user-input",interactive:!0,grow:!0,disabled:u},n().createElement(t.Input,{id:"basic-auth-user-input",placeholder:"User",value:e,onChange:e=>s(e.currentTarget.value),required:!0})),n().createElement(t.InlineField,{className:(0,a.cx)(p.inlineFieldNoMarginRight,p.inlineFieldWithSecret,d.lastInlineField),label:"Password",labelWidth:24,tooltip:o,required:!0,htmlFor:"basic-auth-password-input",interactive:!0,grow:!0,disabled:u},n().createElement(Q,{id:"basic-auth-password-input",isConfigured:r,onReset:u?()=>{}:c,placeholder:"Password",onChange:e=>l(e.currentTarget.value),required:!0})))};var X=Object.defineProperty,ee=Object.defineProperties,te=Object.getOwnPropertyDescriptors,re=Object.getOwnPropertySymbols,ne=Object.prototype.hasOwnProperty,ae=Object.prototype.propertyIsEnumerable,ie=(e,t,r)=>t in e?X(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;const oe=e=>{var t,r=e,{children:a}=r,i=((e,t)=>{var r={};for(var n in e)ne.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&re)for(var n of re(e))t.indexOf(n)<0&&ae.call(e,n)&&(r[n]=e[n]);return r})(r,["children"]);return n().createElement(E,(t=((e,t)=>{for(var r in t||(t={}))ne.call(t,r)&&ie(e,r,t[r]);if(re)for(var r of re(t))ae.call(t,r)&&ie(e,r,t[r]);return e})({},i),ee(t,te({kind:"sub-section"}))),a)};var se=(e=>(e.NoAuth="NoAuth",e.BasicAuth="BasicAuth",e.OAuthForward="OAuthForward",e.CrossSiteCredentials="CrossSiteCredentials",e))(se||{}),le=Object.defineProperty,ce=Object.defineProperties,ue=Object.getOwnPropertyDescriptors,pe=Object.getOwnPropertySymbols,de=Object.prototype.hasOwnProperty,fe=Object.prototype.propertyIsEnumerable,he=(e,t,r)=>t in e?le(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,me=(e,t)=>{for(var r in t||(t={}))de.call(t,r)&&he(e,r,t[r]);if(pe)for(var r of pe(t))fe.call(t,r)&&he(e,r,t[r]);return e},ge=(e,t)=>ce(e,ue(t));const ve={[se.BasicAuth]:{label:"Basic authentication",value:se.BasicAuth,description:"Authenticate with your data source username and password"},[se.CrossSiteCredentials]:{label:"Enable cross-site access control requests",value:se.CrossSiteCredentials,description:"Allow cross-site Access-Control requests with your existing credentials and cookies. This enables the server to authenticate the user and perform authorized requests on their behalf on other domains."},[se.OAuthForward]:{label:"Forward OAuth Identity",value:se.OAuthForward,description:"Forward the OAuth access token (and if available: the OIDC ID token) of the user querying to the data source"},[se.NoAuth]:{label:"No Authentication",value:se.NoAuth,description:"Data source is available without authentication"}},ye=({selectedMethod:e,mostCommonMethod:i,visibleMethods:o,extendedDefaultOptions:s,customMethods:l,onAuthMethodSelect:c,basicAuth:u,readOnly:p})=>{var d,f,h,m;const[g,v]=(0,r.useState)(!1),{colors:y,spacing:b}=(0,t.useTheme2)(),w=(0,r.useMemo)((()=>{var e;return null!=o?o:[se.BasicAuth,se.OAuthForward,se.NoAuth,...null!=(e=null==l?void 0:l.map((e=>e.id)))?e:[]]}),[l,o]),x=w.length>1,O=(0,r.useMemo)((()=>{var e;const t=null!=(e=null==l?void 0:l.reduce(((e,t)=>(e[t.id]={label:t.label,value:t.id,description:t.description},e)),{}))?e:{},r={};let n;for(n in ve)s&&s[n]?r[n]=me(me({},ve[n]),s[n]):r[n]=ve[n];const a=me(me({},t),r);return w.filter((e=>Boolean(a[e]))).map((e=>{const t=a[e];return e===i&&x?ge(me({},t),{label:`${t.label} (most common)`}):t}))}),[w,l,s,i,x]);let k=e;x?e===se.NoAuth&&i&&!g&&(k=i):k=w[0];let E=null;k===se.BasicAuth&&u?E=n().createElement(K,ge(me({},u),{readOnly:p})):k.startsWith("custom-")&&(E=null!=(f=null==(d=null==l?void 0:l.find((e=>e.id===k)))?void 0:d.component)?f:null);const j=x?"Authentication methods":null!=(h=O[0].label)?h:"",S=x?"Choose an authentication method to access the data source":null!=(m=O[0].description)?m:"",P={authMethods:(0,a.css)(me({marginTop:b(2.5)},x&&{padding:b(2),border:`1px solid ${y.border.weak}`})),selectedMethodFields:(0,a.css)({marginTop:b(1.5)})};return n().createElement(oe,{title:j,description:S},n().createElement("div",{className:P.authMethods},x&&n().createElement(t.Select,{options:O,value:k,onChange:e=>{v(!0),c(e.value)},disabled:p}),E&&n().createElement("div",{className:P.selectedMethodFields},E)))};var be=Object.defineProperty,we=Object.defineProperties,xe=Object.getOwnPropertyDescriptors,Oe=Object.getOwnPropertySymbols,ke=Object.prototype.hasOwnProperty,Ee=Object.prototype.propertyIsEnumerable,je=(e,t,r)=>t in e?be(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Se=(e,t)=>{for(var r in t||(t={}))ke.call(t,r)&&je(e,r,t[r]);if(Oe)for(var r of Oe(t))Ee.call(t,r)&&je(e,r,t[r]);return e};const Pe=e=>({configuredStyle:a.css`
      min-height: ${e.spacing(e.components.height.md)};
      padding-top: ${e.spacing(.5)};
      resize: none;
    `}),De=e=>{var n=e,{isConfigured:i,onReset:o}=n,s=((e,t)=>{var r={};for(var n in e)ke.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&Oe)for(var n of Oe(e))t.indexOf(n)<0&&Ee.call(e,n)&&(r[n]=e[n]);return r})(n,["isConfigured","onReset"]);const l=(0,t.useStyles2)(Pe);return r.createElement(t.HorizontalGroup,null,!i&&r.createElement(t.TextArea,Se({},s)),i&&r.createElement(t.TextArea,(c=Se({},s),u={rows:1,disabled:!0,value:"configured",className:(0,a.cx)(l.configuredStyle)},we(c,xe(u)))),i&&r.createElement(t.Button,{onClick:o,variant:"secondary"},"Reset"));var c,u},Te=({children:e,enabled:r,label:i,tooltipText:o,onToggle:s,readOnly:l})=>{const{colors:c,spacing:u}=(0,t.useTheme2)(),p={container:(0,a.css)({marginTop:3}),checkboxContainer:(0,a.css)({display:"flex",alignItems:"center"}),infoIcon:(0,a.css)({marginTop:-2,marginLeft:5,color:c.text.secondary}),content:(0,a.css)({margin:u(1,0,2,3)})};return n().createElement("div",{className:p.container},n().createElement("div",{className:p.checkboxContainer},n().createElement(t.Checkbox,{value:r,label:i,onChange:()=>s(!r),disabled:l}),n().createElement(t.Tooltip,{placement:"top",content:o,interactive:!0},n().createElement(t.Icon,{name:"info-circle",className:p.infoIcon,size:"sm"}))),r&&e&&n().createElement("div",{className:p.content},e))},Ae=({enabled:e,certificateConfigured:r,onToggle:i,onCertificateChange:o,onCertificateReset:s,tooltips:l,readOnly:c})=>{var u;const p=Z();return n().createElement(Te,{enabled:e,label:"Add self-signed certificate",tooltipText:"Add your own Certificate Authority (CA) certificate on top of one generated by the certificate authorities for additional security measures",onToggle:e=>i(e),readOnly:c},n().createElement(t.InlineField,{label:"CA Certificate",labelWidth:24,tooltip:null!=(u=null==l?void 0:l.certificateLabel)?u:"Your self-signed certificate",required:!0,htmlFor:"self-signed-certificate-input",interactive:!0,grow:!0,className:(0,a.cx)(p.inlineFieldNoMarginRight,p.inlineFieldWithSecret),disabled:c},n().createElement(De,{id:"self-signed-certificate-input",isConfigured:r,onChange:e=>o(e.currentTarget.value),onReset:c?()=>{}:s,placeholder:"Begins with --- BEGIN CERTIFICATE ---",rows:6,required:!0})))},Fe=({enabled:e,serverName:r,clientCertificateConfigured:i,clientKeyConfigured:o,onToggle:s,onServerNameChange:l,onClientCertificateChange:c,onClientKeyChange:u,onClientCertificateReset:p,onClientKeyReset:d,tooltips:f,readOnly:h})=>{var m,g,v;const y=Z();return n().createElement(Te,{enabled:e,label:"TLS Client Authentication",tooltipText:"Validate using TLS client authentication, in which the server authenticates the client",onToggle:e=>s(e),readOnly:h},n().createElement(t.InlineField,{label:"ServerName",labelWidth:24,tooltip:null!=(m=null==f?void 0:f.serverNameLabel)?m:"A Servername is used to verify the hostname on the returned certificate",required:!0,htmlFor:"client-auth-servername-input",interactive:!0,grow:!0,className:y.inlineFieldNoMarginRight,disabled:h},n().createElement(t.Input,{id:"client-auth-servername-input",placeholder:"domain.example.com",value:r,onChange:e=>l(e.currentTarget.value),required:!0})),n().createElement(t.InlineField,{label:"Client Certificate",labelWidth:24,tooltip:null!=(g=null==f?void 0:f.certificateLabel)?g:"The client certificate can be generated from a Certificate Authority or be self-signed",required:!0,htmlFor:"client-auth-client-certificate-input",interactive:!0,grow:!0,className:(0,a.cx)(y.inlineFieldNoMarginRight,y.inlineFieldWithSecret),disabled:h},n().createElement(De,{id:"client-auth-client-certificate-input",isConfigured:i,onChange:e=>c(e.currentTarget.value),onReset:h?()=>{}:p,placeholder:"Begins with --- BEGIN CERTIFICATE ---",rows:6,required:!0})),n().createElement(t.InlineField,{label:"Client Key",labelWidth:24,tooltip:null!=(v=null==f?void 0:f.keyLabel)?v:"The client key can be generated from a Certificate Authority or be self-signed",required:!0,htmlFor:"client-auth-client-key-input",interactive:!0,grow:!0,className:(0,a.cx)(y.inlineFieldNoMarginRight,y.inlineFieldWithSecret),disabled:h},n().createElement(De,{id:"client-auth-client-key-input",isConfigured:o,onChange:e=>u(e.currentTarget.value),onReset:h?()=>{}:d,placeholder:"Begins with --- RSA PRIVATE KEY CERTIFICATE ---",rows:6,required:!0})))},Ce=({enabled:e,onToggle:t,readOnly:r})=>n().createElement(Te,{enabled:e,label:"Skip TLS certificate validation",tooltipText:"Skipping TLS certificate validation is not recommended unless absolutely necessary or for testing",onToggle:e=>t(e),readOnly:r});var _e=Object.defineProperty,Ne=Object.defineProperties,$e=Object.getOwnPropertyDescriptors,Ie=Object.getOwnPropertySymbols,Me=Object.prototype.hasOwnProperty,Re=Object.prototype.propertyIsEnumerable,ze=(e,t,r)=>t in e?_e(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Ue=(e,t)=>{for(var r in t||(t={}))Me.call(t,r)&&ze(e,r,t[r]);if(Ie)for(var r of Ie(t))Re.call(t,r)&&ze(e,r,t[r]);return e},Le=(e,t)=>Ne(e,$e(t));const Be=({selfSignedCertificate:e,TLSClientAuth:r,skipTLSVerification:i,readOnly:o})=>{const{spacing:s}=(0,t.useTheme2)(),l={container:(0,a.css)({marginTop:s(3)})};return n().createElement(oe,{className:l.container,title:"TLS settings",description:"Additional security measures that can be applied on top of authentication"},n().createElement(Ae,Le(Ue({},e),{readOnly:o})),n().createElement(Fe,Le(Ue({},r),{readOnly:o})),n().createElement(Ce,Le(Ue({},i),{readOnly:o})))};var qe=Object.defineProperty,We=Object.defineProperties,Je=Object.getOwnPropertyDescriptors,He=Object.getOwnPropertySymbols,Ye=Object.prototype.hasOwnProperty,Ge=Object.prototype.propertyIsEnumerable,Ve=(e,t,r)=>t in e?qe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Qe=(e,t)=>{for(var r in t||(t={}))Ye.call(t,r)&&Ve(e,r,t[r]);if(He)for(var r of He(t))Ge.call(t,r)&&Ve(e,r,t[r]);return e},Ze=(e,t)=>We(e,Je(t));const Ke=({header:e,onChange:r,onBlur:i,onDelete:o,readOnly:s})=>{const{spacing:l}=(0,t.useTheme2)(),c=Z(),u={container:(0,a.css)({alignItems:"center"}),input:(0,a.css)({minWidth:"100%"}),headerNameField:(0,a.css)({width:"40%",marginRight:0,paddingRight:l(1)}),headerValueField:(0,a.css)({width:"45%",marginRight:0}),removeHeaderBtn:(0,a.css)({margin:"0 0 3px 10px"})};return n().createElement(n().Fragment,null,n().createElement(t.InlineFieldRow,{className:u.container},n().createElement(t.InlineField,{label:"Header",labelWidth:9,grow:!0,className:u.headerNameField,htmlFor:`custom-header-${e.id}-name-input`,disabled:s},n().createElement(t.Input,{id:`custom-header-${e.id}-name-input`,placeholder:"X-Custom-Header",value:e.name,width:12,onChange:t=>r(Ze(Qe({},e),{name:t.currentTarget.value})),onBlur:i,className:u.input})),n().createElement(t.InlineField,{label:"Value",labelWidth:9,grow:!0,className:(0,a.cx)(c.inlineFieldWithSecret,u.headerValueField),htmlFor:`custom-header-${e.id}-value-input`,disabled:s},n().createElement(Q,{id:`custom-header-${e.id}-value-input`,isConfigured:e.configured,placeholder:"Header value",value:e.value,width:12,onChange:t=>r(Ze(Qe({},e),{value:t.currentTarget.value})),onReset:s?()=>{}:()=>r(Ze(Qe({},e),{configured:!1,value:""})),onBlur:i,className:u.input})),n().createElement(t.IconButton,{name:"trash-alt",tooltip:"Remove header",tooltipPlacement:"top",className:u.removeHeaderBtn,onClick:o,type:"button",disabled:s})))};var Xe=Object.defineProperty,et=Object.defineProperties,tt=Object.getOwnPropertyDescriptors,rt=Object.getOwnPropertySymbols,nt=Object.prototype.hasOwnProperty,at=Object.prototype.propertyIsEnumerable,it=(e,t,r)=>t in e?Xe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ot=(e,t)=>{for(var r in t||(t={}))nt.call(t,r)&&it(e,r,t[r]);if(rt)for(var r of rt(t))at.call(t,r)&&it(e,r,t[r]);return e},st=(e,t)=>et(e,tt(t));const lt=({headers:e,onChange:i,readOnly:o})=>{const{spacing:s}=(0,t.useTheme2)(),[l,c]=(0,r.useState)(e.map((e=>st(ot({},e),{id:ct(),value:""}))));(0,r.useEffect)((()=>{c((t=>{let r=!1;const n=t.map((t=>{var n;const a=null==(n=e.find((e=>e.name===t.name)))?void 0:n.configured;return void 0!==a&&t.configured!==a?(r=!0,st(ot({},t),{configured:a})):t}));return r?n:t}))}),[e]);const u=()=>{i(l.map((({name:e,value:t,configured:r})=>({name:e,value:t,configured:r}))))},p={container:(0,a.css)({marginTop:s(3)}),addHeaderButton:(0,a.css)({marginTop:s(1.5)})};return n().createElement("div",{className:p.container},n().createElement(oe,{title:"HTTP headers",description:"Pass along additional context and metadata about the request/response",isCollapsible:!0,isInitiallyOpen:l.length>0},n().createElement("div",null,l.map((e=>n().createElement(Ke,{key:e.id,header:e,onChange:e=>((e,t)=>{c(l.map((r=>r.id===e?ot({},t):r)))})(e.id,e),onDelete:()=>(e=>{const t=l.findIndex((t=>t.id===e));if(-1===t)return;const r=[...l];r.splice(t,1),c(r),i(r.map((({name:e,value:t,configured:r})=>({name:e,value:t,configured:r}))))})(e.id),onBlur:u,readOnly:o})))),n().createElement("div",{className:p.addHeaderButton},n().createElement(t.Button,{icon:"plus",variant:"secondary",fill:"outline",onClick:()=>{c([...l,{id:ct(),name:"",value:"",configured:!1}])},disabled:o},0===l.length?"Add header":"Add another header"))))};function ct(){return Math.random().toString(16).slice(2)}var ut=Object.defineProperty,pt=Object.defineProperties,dt=Object.getOwnPropertyDescriptors,ft=Object.getOwnPropertySymbols,ht=Object.prototype.hasOwnProperty,mt=Object.prototype.propertyIsEnumerable,gt=(e,t,r)=>t in e?ut(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,vt=(e,t)=>{for(var r in t||(t={}))ht.call(t,r)&&gt(e,r,t[r]);if(ft)for(var r of ft(t))mt.call(t,r)&&gt(e,r,t[r]);return e},yt=(e,t)=>pt(e,dt(t));const bt=({selectedMethod:e,mostCommonMethod:t,visibleMethods:r,extendedDefaultOptions:i,customMethods:o,onAuthMethodSelect:s,basicAuth:l,TLS:c,customHeaders:u,readOnly:p=!1})=>{const d={container:(0,a.css)({maxWidth:578})};return n().createElement("div",{className:d.container},n().createElement(C,{title:"Authentication"},n().createElement(ye,{selectedMethod:e,mostCommonMethod:t,customMethods:o,visibleMethods:r,extendedDefaultOptions:i,onAuthMethodSelect:s,basicAuth:l,readOnly:p}),c&&n().createElement(Be,yt(vt({},c),{readOnly:p})),u&&n().createElement(lt,yt(vt({},u),{readOnly:p}))))};var wt=Object.defineProperty,xt=Object.defineProperties,Ot=Object.getOwnPropertyDescriptors,kt=Object.getOwnPropertySymbols,Et=Object.prototype.hasOwnProperty,jt=Object.prototype.propertyIsEnumerable,St=(e,t,r)=>t in e?wt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Pt=(e,t)=>{for(var r in t||(t={}))Et.call(t,r)&&St(e,r,t[r]);if(kt)for(var r of kt(t))jt.call(t,r)&&St(e,r,t[r]);return e},Dt=(e,t)=>xt(e,Ot(t));const Tt="httpHeaderName",At="httpHeaderValue";function Ft(e){return e.basicAuth?se.BasicAuth:e.withCredentials?se.CrossSiteCredentials:e.jsonData.oauthPassThru?se.OAuthForward:se.NoAuth}function Ct(e,t){return r=>{t(Dt(Pt({},e),{basicAuth:r===se.BasicAuth,withCredentials:r===se.CrossSiteCredentials,jsonData:Dt(Pt({},e.jsonData),{oauthPassThru:r===se.OAuthForward})}))}}function _t(e,t){return{user:e.basicAuthUser,passwordConfigured:e.secureJsonFields.basicAuthPassword,onUserChange:r=>t(Dt(Pt({},e),{basicAuthUser:r})),onPasswordChange:r=>t(Dt(Pt({},e),{secureJsonData:Dt(Pt({},e.secureJsonData),{basicAuthPassword:r})})),onPasswordReset:()=>t(Dt(Pt({},e),{secureJsonData:Dt(Pt({},e.secureJsonData),{basicAuthPassword:""}),secureJsonFields:Dt(Pt({},e.secureJsonFields),{basicAuthPassword:!1})}))}}function Nt(e,t){return{selfSignedCertificate:{enabled:Boolean(e.jsonData.tlsAuthWithCACert),certificateConfigured:e.secureJsonFields.tlsCACert,onToggle:r=>t(Dt(Pt({},e),{jsonData:Dt(Pt({},e.jsonData),{tlsAuthWithCACert:r})})),onCertificateChange:r=>t(Dt(Pt({},e),{secureJsonData:Dt(Pt({},e.secureJsonData),{tlsCACert:r})})),onCertificateReset:()=>t(Dt(Pt({},e),{secureJsonData:Dt(Pt({},e.secureJsonData),{tlsCACert:""}),secureJsonFields:Dt(Pt({},e.secureJsonFields),{tlsCACert:!1})}))},TLSClientAuth:{enabled:e.jsonData.tlsAuth,serverName:e.jsonData.serverName,clientCertificateConfigured:e.secureJsonFields.tlsClientCert,clientKeyConfigured:e.secureJsonFields.tlsClientKey,onToggle:r=>t(Dt(Pt({},e),{jsonData:Dt(Pt({},e.jsonData),{tlsAuth:r})})),onServerNameChange:r=>t(Dt(Pt({},e),{jsonData:Dt(Pt({},e.jsonData),{serverName:r})})),onClientCertificateChange:r=>t(Dt(Pt({},e),{secureJsonData:Dt(Pt({},e.secureJsonData),{tlsClientCert:r})})),onClientCertificateReset:()=>t(Dt(Pt({},e),{secureJsonData:Dt(Pt({},e.secureJsonData),{tlsClientCert:""}),secureJsonFields:Dt(Pt({},e.secureJsonFields),{tlsClientCert:!1})})),onClientKeyChange:r=>t(Dt(Pt({},e),{secureJsonData:Dt(Pt({},e.secureJsonData),{tlsClientKey:r})})),onClientKeyReset:()=>t(Dt(Pt({},e),{secureJsonData:Dt(Pt({},e.secureJsonData),{tlsClientKey:""}),secureJsonFields:Dt(Pt({},e.secureJsonFields),{tlsClientKey:!1})}))},skipTLSVerification:{enabled:e.jsonData.tlsSkipVerify,onToggle:r=>t(Dt(Pt({},e),{jsonData:Dt(Pt({},e.jsonData),{tlsSkipVerify:r})}))}}}function $t(e,t){return{headers:Object.keys(e.jsonData).filter((e=>e.startsWith(Tt))).sort().map((t=>{var r;const n=t.slice(14);return{name:e.jsonData[t],configured:null!=(r=e.secureJsonFields[`${At}${n}`])&&r}})),onChange:r=>{const n=Object.fromEntries(Object.entries(e.jsonData).filter((([e])=>!e.startsWith(Tt)))),a=Object.fromEntries(Object.entries(e.secureJsonData||{}).filter((([e])=>!e.startsWith(At)))),i=Object.fromEntries(Object.entries(e.secureJsonFields).filter((([e])=>!e.startsWith(At))));r.forEach(((e,t)=>{n[`${Tt}${t+1}`]=e.name,e.configured?i[`${At}${t+1}`]=!0:a[`${At}${t+1}`]=e.value})),t(Dt(Pt({},e),{jsonData:n,secureJsonData:a,secureJsonFields:i}))}}}var It=Object.defineProperty,Mt=Object.defineProperties,Rt=Object.getOwnPropertyDescriptors,zt=Object.getOwnPropertySymbols,Ut=Object.prototype.hasOwnProperty,Lt=Object.prototype.propertyIsEnumerable,Bt=(e,t,r)=>t in e?It(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,qt=(e,t)=>{for(var r in t||(t={}))Ut.call(t,r)&&Bt(e,r,t[r]);if(zt)for(var r of zt(t))Lt.call(t,r)&&Bt(e,r,t[r]);return e},Wt=(e,t)=>Mt(e,Rt(t));const Jt=({config:e,onChange:r,className:i})=>{const o={container:(0,a.css)({maxWidth:578})};return n().createElement(oe,{title:"Advanced HTTP settings",className:(0,a.cx)(o.container,i)},n().createElement(t.InlineField,{htmlFor:"advanced-http-cookies",label:"Allowed cookies",labelWidth:24,tooltip:"Grafana proxy deletes forwarded cookies by default. Specify cookies by name that should be forwarded to the data source.",disabled:e.readOnly,grow:!0},n().createElement(t.TagsInput,{id:"advanced-http-cookies",placeholder:"New cookie (hit enter to add)",tags:e.jsonData.keepCookies,onChange:t=>{r(Wt(qt({},e),{jsonData:Wt(qt({},e.jsonData),{keepCookies:t})}))}})),n().createElement(t.InlineField,{htmlFor:"advanced-http-timeout",label:"Timeout",labelWidth:24,tooltip:"HTTP request timeout in seconds",disabled:e.readOnly,grow:!0},n().createElement(t.Input,{id:"advanced-http-timeout",type:"number",min:0,placeholder:"Timeout in seconds","aria-label":"Timeout in seconds",value:e.jsonData.timeout,onChange:t=>{r(Wt(qt({},e),{jsonData:Wt(qt({},e.jsonData),{timeout:parseInt(t.currentTarget.value,10)})}))}})))},Ht=()=>{const e=(0,t.useStyles2)(Yt);return n().createElement("hr",{className:e.horizontalDivider})},Yt=e=>({horizontalDivider:(0,a.css)({borderTop:`1px solid ${e.colors.border.weak}`,margin:e.spacing(2,0),width:"100%"})});function Gt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Vt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Gt(e,t,r[t])}))}return e}function Qt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const Zt=e=>({space:(0,a.css)({width:"100%",height:e.spacing(2)})});function Kt(e){return Kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kt(e)}function Xt(e){return Xt=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},Xt(e)}function er(e,t){return er=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},er(e,t)}function tr(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function rr(e,t,r){return rr=tr()?Reflect.construct:function(e,t,r){var n=[null];n.push.apply(n,t);var a=new(Function.bind.apply(e,n));return r&&er(a,r.prototype),a},rr.apply(null,arguments)}function nr(e){var t="function"==typeof Map?new Map:void 0;return nr=function(e){if(null===e||(r=e,-1===Function.toString.call(r).indexOf("[native code]")))return e;var r;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return rr(e,arguments,Xt(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),er(n,e)},nr(e)}function ar(e){return function(e){if(Array.isArray(e))return or(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||ir(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ir(e,t){if(e){if("string"==typeof e)return or(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?or(e,t):void 0}}function or(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var sr=Object.prototype.hasOwnProperty;function lr(e,t){return(e=e.slice()).push(t),e}function cr(e,t){return(t=t.slice()).unshift(e),t}var ur=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&er(e,t)}(a,e);var t,r,n=(t=a,r=tr(),function(){var e,n=Xt(t);if(r){var a=Xt(this).constructor;e=Reflect.construct(n,arguments,a)}else e=n.apply(this,arguments);return function(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}(this,e)});function a(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),(t=n.call(this,'JSONPath should not be called with "new" (it prevents return of (unwrapped) scalar values)')).avoidNew=!0,t.value=e,t.name="NewError",t}return a}(nr(Error));function pr(e,t,r,n,a){if(!(this instanceof pr))try{return new pr(e,t,r,n,a)}catch(e){if(!e.avoidNew)throw e;return e.value}"string"==typeof e&&(a=n,n=r,r=t,t=e,e=null);var i=e&&"object"===Kt(e);if(e=e||{},this.json=e.json||r,this.path=e.path||t,this.resultType=e.resultType||"value",this.flatten=e.flatten||!1,this.wrap=!sr.call(e,"wrap")||e.wrap,this.sandbox=e.sandbox||{},this.preventEval=e.preventEval||!1,this.parent=e.parent||null,this.parentProperty=e.parentProperty||null,this.callback=e.callback||n||null,this.otherTypeCallback=e.otherTypeCallback||a||function(){throw new TypeError("You must supply an otherTypeCallback callback option with the @other() operator.")},!1!==e.autostart){var o={path:i?e.path:t};i?"json"in e&&(o.json=e.json):o.json=r;var s=this.evaluate(o);if(!s||"object"!==Kt(s))throw new ur(s);return s}}function dr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){dr(e,t,r[t])}))}return e}function hr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}pr.prototype.evaluate=function(e,t,r,n){var a=this,i=this.parent,o=this.parentProperty,s=this.flatten,l=this.wrap;if(this.currResultType=this.resultType,this.currPreventEval=this.preventEval,this.currSandbox=this.sandbox,r=r||this.callback,this.currOtherTypeCallback=n||this.otherTypeCallback,t=t||this.json,(e=e||this.path)&&"object"===Kt(e)&&!Array.isArray(e)){if(!e.path&&""!==e.path)throw new TypeError('You must supply a "path" property when providing an object argument to JSONPath.evaluate().');if(!sr.call(e,"json"))throw new TypeError('You must supply a "json" property when providing an object argument to JSONPath.evaluate().');t=e.json,s=sr.call(e,"flatten")?e.flatten:s,this.currResultType=sr.call(e,"resultType")?e.resultType:this.currResultType,this.currSandbox=sr.call(e,"sandbox")?e.sandbox:this.currSandbox,l=sr.call(e,"wrap")?e.wrap:l,this.currPreventEval=sr.call(e,"preventEval")?e.preventEval:this.currPreventEval,r=sr.call(e,"callback")?e.callback:r,this.currOtherTypeCallback=sr.call(e,"otherTypeCallback")?e.otherTypeCallback:this.currOtherTypeCallback,i=sr.call(e,"parent")?e.parent:i,o=sr.call(e,"parentProperty")?e.parentProperty:o,e=e.path}if(i=i||null,o=o||null,Array.isArray(e)&&(e=pr.toPathString(e)),(e||""===e)&&t){var c=pr.toPathArray(e);"$"===c[0]&&c.length>1&&c.shift(),this._hasParentSelector=null;var u=this._trace(c,t,["$"],i,o,r).filter((function(e){return e&&!e.isParentSelector}));return u.length?l||1!==u.length||u[0].hasArrExpr?u.reduce((function(e,t){var r=a._getPreferredOutput(t);return s&&Array.isArray(r)?e=e.concat(r):e.push(r),e}),[]):this._getPreferredOutput(u[0]):l?[]:void 0}},pr.prototype._getPreferredOutput=function(e){var t=this.currResultType;switch(t){case"all":var r=Array.isArray(e.path)?e.path:pr.toPathArray(e.path);return e.pointer=pr.toPointer(r),e.path="string"==typeof e.path?e.path:pr.toPathString(e.path),e;case"value":case"parent":case"parentProperty":return e[t];case"path":return pr.toPathString(e[t]);case"pointer":return pr.toPointer(e.path);default:throw new TypeError("Unknown result type")}},pr.prototype._handleCallback=function(e,t,r){if(t){var n=this._getPreferredOutput(e);e.path="string"==typeof e.path?e.path:pr.toPathString(e.path),t(n,r,e)}},pr.prototype._trace=function(e,t,r,n,a,i,o,s){var l,c=this;if(!e.length)return l={path:r,value:t,parent:n,parentProperty:a,hasArrExpr:o},this._handleCallback(l,i,"value"),l;var u=e[0],p=e.slice(1),d=[];function f(e){Array.isArray(e)?e.forEach((function(e){d.push(e)})):d.push(e)}if(("string"!=typeof u||s)&&t&&sr.call(t,u))f(this._trace(p,t[u],lr(r,u),t,u,i,o));else if("*"===u)this._walk(u,p,t,r,n,a,i,(function(e,t,r,n,a,i,o,s){f(c._trace(cr(e,r),n,a,i,o,s,!0,!0))}));else if(".."===u)f(this._trace(p,t,r,n,a,i,o)),this._walk(u,p,t,r,n,a,i,(function(e,t,r,n,a,i,o,s){"object"===Kt(n[e])&&f(c._trace(cr(t,r),n[e],lr(a,e),n,e,s,!0))}));else{if("^"===u)return this._hasParentSelector=!0,{path:r.slice(0,-1),expr:p,isParentSelector:!0};if("~"===u)return l={path:lr(r,u),value:a,parent:n,parentProperty:null},this._handleCallback(l,i,"property"),l;if("$"===u)f(this._trace(p,t,r,null,null,i,o));else if(/^(\x2D?[0-9]*):(\x2D?[0-9]*):?([0-9]*)$/.test(u))f(this._slice(u,p,t,r,n,a,i));else if(0===u.indexOf("?(")){if(this.currPreventEval)throw new Error("Eval [?(expr)] prevented in JSONPath expression.");this._walk(u,p,t,r,n,a,i,(function(e,t,r,n,a,i,o,s){c._eval(t.replace(/^\?\(((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?)\)$/,"$1"),n[e],e,a,i,o)&&f(c._trace(cr(e,r),n,a,i,o,s,!0))}))}else if("("===u[0]){if(this.currPreventEval)throw new Error("Eval [(expr)] prevented in JSONPath expression.");f(this._trace(cr(this._eval(u,t,r[r.length-1],r.slice(0,-1),n,a),p),t,r,n,a,i,o))}else if("@"===u[0]){var h=!1,m=u.slice(1,-2);switch(m){case"scalar":t&&["object","function"].includes(Kt(t))||(h=!0);break;case"boolean":case"string":case"undefined":case"function":Kt(t)===m&&(h=!0);break;case"integer":!Number.isFinite(t)||t%1||(h=!0);break;case"number":Number.isFinite(t)&&(h=!0);break;case"nonFinite":"number"!=typeof t||Number.isFinite(t)||(h=!0);break;case"object":t&&Kt(t)===m&&(h=!0);break;case"array":Array.isArray(t)&&(h=!0);break;case"other":h=this.currOtherTypeCallback(t,r,n,a);break;case"null":null===t&&(h=!0);break;default:throw new TypeError("Unknown value type "+m)}if(h)return l={path:r,value:t,parent:n,parentProperty:a},this._handleCallback(l,i,"value"),l}else if("`"===u[0]&&t&&sr.call(t,u.slice(1))){var g=u.slice(1);f(this._trace(p,t[g],lr(r,g),t,g,i,o,!0))}else if(u.includes(",")){var v,y=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=ir(e))){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,i=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw i}}}}(u.split(","));try{for(y.s();!(v=y.n()).done;){var b=v.value;f(this._trace(cr(b,p),t,r,n,a,i,!0))}}catch(e){y.e(e)}finally{y.f()}}else!s&&t&&sr.call(t,u)&&f(this._trace(p,t[u],lr(r,u),t,u,i,o,!0))}if(this._hasParentSelector)for(var w=0;w<d.length;w++){var x=d[w];if(x&&x.isParentSelector){var O=this._trace(x.expr,t,x.path,n,a,i,o);if(Array.isArray(O)){d[w]=O[0];for(var k=O.length,E=1;E<k;E++)w++,d.splice(w,0,O[E])}else d[w]=O}}return d},pr.prototype._walk=function(e,t,r,n,a,i,o,s){if(Array.isArray(r))for(var l=r.length,c=0;c<l;c++)s(c,e,t,r,n,a,i,o);else r&&"object"===Kt(r)&&Object.keys(r).forEach((function(l){s(l,e,t,r,n,a,i,o)}))},pr.prototype._slice=function(e,t,r,n,a,i,o){if(Array.isArray(r)){var s=r.length,l=e.split(":"),c=l[2]&&Number.parseInt(l[2])||1,u=l[0]&&Number.parseInt(l[0])||0,p=l[1]&&Number.parseInt(l[1])||s;u=u<0?Math.max(0,u+s):Math.min(s,u),p=p<0?Math.max(0,p+s):Math.min(s,p);for(var d=[],f=u;f<p;f+=c)this._trace(cr(f,t),r,n,a,i,o,!0).forEach((function(e){d.push(e)}));return d}},pr.prototype._eval=function(e,t,r,n,a,i){e.includes("@parentProperty")&&(this.currSandbox._$_parentProperty=i,e=e.replace(/@parentProperty/g,"_$_parentProperty")),e.includes("@parent")&&(this.currSandbox._$_parent=a,e=e.replace(/@parent/g,"_$_parent")),e.includes("@property")&&(this.currSandbox._$_property=r,e=e.replace(/@property/g,"_$_property")),e.includes("@path")&&(this.currSandbox._$_path=pr.toPathString(n.concat([r])),e=e.replace(/@path/g,"_$_path")),e.includes("@root")&&(this.currSandbox._$_root=this.json,e=e.replace(/@root/g,"_$_root")),/@([\t-\r \)\.\[\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF])/.test(e)&&(this.currSandbox._$_v=t,e=e.replace(/@([\t-\r \)\.\[\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF])/g,"_$_v$1"));try{return this.vm.runInNewContext(e,this.currSandbox)}catch(t){throw console.log(t),new Error("jsonPath: "+t.message+": "+e)}},pr.cache={},pr.toPathString=function(e){for(var t=e,r=t.length,n="$",a=1;a<r;a++)/^(~|\^|@(?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\(\))$/.test(t[a])||(n+=/^[\*0-9]+$/.test(t[a])?"["+t[a]+"]":"['"+t[a]+"']");return n},pr.toPointer=function(e){for(var t=e,r=t.length,n="",a=1;a<r;a++)/^(~|\^|@(?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\(\))$/.test(t[a])||(n+="/"+t[a].toString().replace(/~/g,"~0").replace(/\//g,"~1"));return n},pr.toPathArray=function(e){var t=pr.cache;if(t[e])return t[e].concat();var r=[],n=e.replace(/@(?:null|boolean|number|string|integer|undefined|nonFinite|scalar|array|object|function|other)\(\)/g,";$&;").replace(/['\[](\??\((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\))['\]]/g,(function(e,t){return"[#"+(r.push(t)-1)+"]"})).replace(/\[["']((?:(?!['\]])[\s\S])*)["']\]/g,(function(e,t){return"['"+t.replace(/\./g,"%@%").replace(/~/g,"%%@@%%")+"']"})).replace(/~/g,";~;").replace(/["']?\.["']?(?!(?:(?!\[)[\s\S])*\])|\[["']?/g,";").replace(/%@%/g,".").replace(/%%@@%%/g,"~").replace(/(?:;)?(\^+)(?:;)?/g,(function(e,t){return";"+t.split("").join(";")+";"})).replace(/;;;|;;/g,";..;").replace(/;$|'?\]|'$/g,"").split(";").map((function(e){var t=e.match(/#([0-9]+)/);return t&&t[1]?r[t[1]]:e}));return t[e]=n,t[e].concat()},pr.prototype.vm={runInNewContext:function(e,t){var r=Object.keys(t),n=[];!function(e,r,n){for(var a=e.length,i=0;i<a;i++)o=e[i],"function"==typeof t[o]&&r.push(e.splice(i--,1)[0]);var o}(r,n);var a=r.map((function(e,r){return t[e]})),i=n.reduce((function(e,r){var n=t[r].toString();return/function/.test(n)||(n="function "+n),"var "+r+"="+n+";"+e}),"");/(["'])use strict\1/.test(e=i+e)||r.includes("arguments")||(e="var arguments = undefined;"+e);var o=(e=e.replace(/;[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*$/,"")).lastIndexOf(";"),s=o>-1?e.slice(0,o+1)+" return "+e.slice(o+1):" return "+e;return rr(Function,ar(r).concat([s])).apply(void 0,ar(a))}};const mr=({query:e,onChange:r,onRunQuery:a,editorContext:i})=>{const{experimentalGroupByField:o}=e,{experimentalMetricField:s}=e,{experimentalVariableTextField:l}=e,{experimentalVariableValueField:c}=e,u=e.fields.map((e=>{if("jsonpath"===e.language){if(e.name)return e.name;const t=pr.toPathArray(e.jsonPath);return t[t.length-1]}return e.name})).filter((e=>e)).map((e=>({label:e,value:e})));return n().createElement(n().Fragment,null,n().createElement(t.InfoBox,{severity:"warning",style:{maxWidth:"700px",whiteSpace:"normal"}},n().createElement("p",null,"The features listed here are experimental. They might change or be removed without notice. In the tooltip for\n          each feature, there's a link to a pull request where you can submit feedback for that feature.")),"default"===i&&n().createElement(n().Fragment,null,n().createElement(t.InlineFieldRow,null,n().createElement(t.InlineField,{labelWidth:12,label:"Group by",tooltip:n().createElement(n().Fragment,null,n().createElement("p",null,"Groups the query result into multiple results. This can be useful when you want to graph multiple time series in the same panel."),n().createElement("a",{href:"https://github.com/grafana/grafana-json-datasource/issues/36",target:"_blank",rel:"noreferrer"},"Share feedback"))},n().createElement(t.Select,{placeholder:"Field",width:20,isClearable:!0,value:u.find((e=>e.value===o)),options:u,onChange:t=>{var n,i;return i=null===(n=t)||void 0===n?void 0:n.value,r(hr(fr({},e),{experimentalGroupByField:i})),void a()}}))),n().createElement(t.InlineFieldRow,null,n().createElement(t.InlineField,{labelWidth:12,label:"Metric",tooltip:n().createElement(n().Fragment,null,n().createElement("p",null,"Set the display name of the selected field to the name of the query result."),n().createElement("a",{href:"https://github.com/grafana/grafana-json-datasource/issues/36",target:"_blank",rel:"noreferrer"},"Share feedback"))},n().createElement(t.Select,{placeholder:"Field",width:20,isClearable:!0,value:u.find((e=>e.value===s)),options:u,onChange:t=>{var n,i;return i=null===(n=t)||void 0===n?void 0:n.value,r(hr(fr({},e),{experimentalMetricField:i})),void a()}})))),"variables"===i&&n().createElement(n().Fragment,null,n().createElement(t.InlineFieldRow,null,n().createElement(t.InlineField,{labelWidth:15,label:"Variable text",tooltip:n().createElement(n().Fragment,null,n().createElement("p",null,"Field to use for the text label of a variable query."),n().createElement("a",{href:"https://github.com/grafana/grafana-json-datasource/issues/79",target:"_blank",rel:"noreferrer"},"Share feedback"))},n().createElement(t.Select,{placeholder:"Field",width:20,isClearable:!0,value:u.find((e=>e.value===l)),options:u,onChange:t=>{var n,i;return i=null===(n=t)||void 0===n?void 0:n.value,r(hr(fr({},e),{experimentalVariableTextField:i})),void a()}}))),n().createElement(t.InlineFieldRow,null,n().createElement(t.InlineField,{labelWidth:15,label:"Variable value",tooltip:n().createElement(n().Fragment,null,n().createElement("p",null,"Field to use for the value of a variable query."),n().createElement("a",{href:"https://github.com/grafana/grafana-json-datasource/issues/79",target:"_blank",rel:"noreferrer"},"Share feedback"))},n().createElement(t.Select,{placeholder:"Field",width:20,isClearable:!0,value:u.find((e=>e.value===c)),options:u,onChange:t=>{var n,i;return i=null===(n=t)||void 0===n?void 0:n.value,r(hr(fr({},e),{experimentalVariableValueField:i})),void a()}})))))},gr=({query:e,onBlur:r,onChange:a})=>{const i=[(0,t.BracesPlugin)(),(0,t.SlatePrism)({onlyIn:e=>"code_block"===e.type,getSyntax:()=>"js"})];return n().createElement(t.QueryField,{additionalPlugins:i,query:e,onRunQuery:r,onChange:a,portalOrigin:"jsonapi",placeholder:"$sum(orders.(price*quantity))"})};function vr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yr(e){return pr((t=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){vr(e,t,r[t])}))}return e}({},e),r=null!=(r={preventEval:!0})?r:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r}(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})),t));var t,r}function br(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}const wr=(xr=function*(e,t){const{value:r}=e,n={suggestions:[]};if(!r)return n;const a=r.document.getTextsAtRange(r.selection),i=1===a.size?a.first().getText():null;if(!i)return n;const o=i.slice(0,r.selection.anchor.offset),s=/\[$/;if(!(/[a-zA-Z0-9]$/.test(o)||/[\$\]a-zA-Z0-9]\.$/.test(o)||/@\.$/.test(o)||s.test(o)))return n;if(s.test(o))return{suggestions:[{label:"Operators",items:[{label:"*",documentation:"Returns all elements."},{label:":",documentation:"Returns a slice of the array."},{label:"?",insertText:"?()",move:-1,documentation:"Returns elements based on a filter expression."}]}]};const l=yr({path:o.lastIndexOf("[")>o.lastIndexOf("]")?o.slice(0,o.lastIndexOf("[")+1)+":]":i.slice(0,i.lastIndexOf(".")),json:yield t()});return"object"!=typeof l[0]?n:{suggestions:[{label:"Elements",items:Object.entries(l[0]).map((([e,t])=>Array.isArray(t)?{label:e,insertText:e+"[]",move:-1,documentation:`_array (${t.length})_`}:{label:e,documentation:`_${typeof t}_\n\n**Preview:**\n\n\`${t}\``}))}]}},Or=function(){var e=this,t=arguments;return new Promise((function(r,n){var a=xr.apply(e,t);function i(e){br(a,r,n,i,o,"next",e)}function o(e){br(a,r,n,i,o,"throw",e)}i(void 0)}))},function(e,t){return Or.apply(this,arguments)});var xr,Or;const kr=({query:e,onBlur:r,onChange:a,onData:i})=>{const o=[(0,t.BracesPlugin)(),(0,t.SlatePrism)({onlyIn:e=>"code_block"===e.type,getSyntax:()=>"js"})];return n().createElement(t.QueryField,{additionalPlugins:o,query:e,cleanText:e=>e.replace(/[{}[\]="(),!~+\-*/^%\|\$@\.]/g,"").trim(),onTypeahead:e=>wr(e,i),onRunQuery:r,onChange:a,portalOrigin:"jsonapi",placeholder:"$.items[*].name"})};function Er(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Er(e,t,r[t])}))}return e}function Sr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const Pr=({value:e=[],onChange:r,limit:a,onComplete:i})=>{const o=t=>n=>{r(e.map(((r,a)=>t===a?Sr(jr({},e[t]),{jsonPath:n}):r)))},s=t=>n=>{r(e.map(((r,a)=>t===a?Sr(jr({},e[t]),{type:"auto"===n.value?void 0:n.value}):r)))},l=t=>n=>{r(e.map(((r,a)=>t===a?Sr(jr({},e[t]),{name:n.currentTarget.value}):r)))},c=(t,n)=>()=>{(!a||e.length<a)&&r([...e.slice(0,t+1),jr({name:"",jsonPath:""},n),...e.slice(t+1)])},u=t=>()=>{r([...e.slice(0,t),...e.slice(t+1)])};var p,d,f;return n().createElement(n().Fragment,null,e.map(((h,m)=>{return n().createElement(t.InlineFieldRow,{key:m},n().createElement(t.InlineField,{label:"Field",tooltip:n().createElement("div",null,"A ",n().createElement("a",{href:"https://goessner.net/articles/JsonPath/"},"JSON Path")," query that selects one or more values from a JSON object."),grow:!0},"jsonata"===h.language?n().createElement(gr,{onBlur:()=>r(e),onChange:o(m),query:h.jsonPath}):n().createElement(kr,{onBlur:()=>r(e),onChange:o(m),query:h.jsonPath,onData:i})),n().createElement(t.InlineField,null,n().createElement(t.Select,{value:null!==(p=h.language)&&void 0!==p?p:"jsonpath",width:14,onChange:(g=m,t=>{r(e.map(((r,n)=>g===n?Sr(jr({},e[g]),{language:t.value}):r)))}),options:[{label:"JSONPath",value:"jsonpath"},{label:"JSONata",value:"jsonata"}]})),n().createElement(t.InlineField,{label:"Type",tooltip:"If Auto is set, the JSON property type is used to detect the field type."},n().createElement(t.Select,{value:null!==(d=h.type)&&void 0!==d?d:"auto",width:12,onChange:s(m),options:[{label:"Auto",value:"auto"},{label:"String",value:"string"},{label:"Number",value:"number"},{label:"Time",value:"time"},{label:"Boolean",value:"boolean"}]})),n().createElement(t.InlineField,{label:"Alias",tooltip:"If left blank, the field uses the name of the queried element."},n().createElement(t.Input,{width:12,value:h.name,onChange:l(m)})),(!a||e.length<a)&&n().createElement(t.Button,{variant:"secondary",onClick:c(m,{language:null!==(f=h.language)&&void 0!==f?f:"jsonpath"}),title:"plus",icon:"plus"}),e.length>1?n().createElement(t.Button,{variant:"secondary",onClick:u(m),icon:"minus",title:"minus"}):null);var g})))};var Dr=c(236),Tr=c.n(Dr);let Ar;Ar="undefined"!=typeof window?window:"undefined"!=typeof self?self:c.g;let Fr=null,Cr=null;const _r=Ar.clearTimeout,Nr=Ar.setTimeout,$r=Ar.cancelAnimationFrame||Ar.mozCancelAnimationFrame||Ar.webkitCancelAnimationFrame,Ir=Ar.requestAnimationFrame||Ar.mozRequestAnimationFrame||Ar.webkitRequestAnimationFrame;null==$r||null==Ir?(Fr=_r,Cr=function(e){return Nr(e,20)}):(Fr=function([e,t]){$r(e),_r(t)},Cr=function(e){const t=Ir((function(){_r(r),e()})),r=Nr((function(){$r(t),e()}),20);return[t,r]});class Mr extends r.Component{constructor(...e){super(...e),this.state={height:this.props.defaultHeight||0,scaledHeight:this.props.defaultHeight||0,scaledWidth:this.props.defaultWidth||0,width:this.props.defaultWidth||0},this._autoSizer=null,this._detectElementResize=null,this._parentNode=null,this._resizeObserver=null,this._timeoutId=null,this._onResize=()=>{this._timeoutId=null;const{disableHeight:e,disableWidth:t,onResize:r}=this.props;if(this._parentNode){var n,a,i,o;const s=window.getComputedStyle(this._parentNode)||{},l=parseFloat(null!==(n=s.paddingLeft)&&void 0!==n?n:"0"),c=parseFloat(null!==(a=s.paddingRight)&&void 0!==a?a:"0"),u=parseFloat(null!==(i=s.paddingTop)&&void 0!==i?i:"0"),p=parseFloat(null!==(o=s.paddingBottom)&&void 0!==o?o:"0"),d=this._parentNode.getBoundingClientRect(),f=d.height-u-p,h=d.width-l-c,m=this._parentNode.offsetHeight-u-p,g=this._parentNode.offsetWidth-l-c;(e||this.state.height===m&&this.state.scaledHeight===f)&&(t||this.state.width===g&&this.state.scaledWidth===h)||(this.setState({height:m,width:g,scaledHeight:f,scaledWidth:h}),"function"==typeof r&&r({height:m,scaledHeight:f,scaledWidth:h,width:g}))}},this._setRef=e=>{this._autoSizer=e}}componentDidMount(){const{nonce:e}=this.props;this._autoSizer&&this._autoSizer.parentNode&&this._autoSizer.parentNode.ownerDocument&&this._autoSizer.parentNode.ownerDocument.defaultView&&this._autoSizer.parentNode instanceof this._autoSizer.parentNode.ownerDocument.defaultView.HTMLElement&&(this._parentNode=this._autoSizer.parentNode,null!=this._parentNode&&("undefined"!=typeof ResizeObserver?(this._resizeObserver=new ResizeObserver((()=>{this._timeoutId=setTimeout(this._onResize,0)})),this._resizeObserver.observe(this._parentNode)):(this._detectElementResize=function(e){let t,r,n,a,i,o,s;const l="undefined"!=typeof document&&document.attachEvent;if(!l){o=function(e){const t=e.__resizeTriggers__,r=t.firstElementChild,n=t.lastElementChild,a=r.firstElementChild;n.scrollLeft=n.scrollWidth,n.scrollTop=n.scrollHeight,a.style.width=r.offsetWidth+1+"px",a.style.height=r.offsetHeight+1+"px",r.scrollLeft=r.scrollWidth,r.scrollTop=r.scrollHeight},i=function(e){return e.offsetWidth!==e.__resizeLast__.width||e.offsetHeight!==e.__resizeLast__.height},s=function(e){if(e.target.className&&"function"==typeof e.target.className.indexOf&&e.target.className.indexOf("contract-trigger")<0&&e.target.className.indexOf("expand-trigger")<0)return;const t=this;o(this),this.__resizeRAF__&&Fr(this.__resizeRAF__),this.__resizeRAF__=Cr((function(){i(t)&&(t.__resizeLast__.width=t.offsetWidth,t.__resizeLast__.height=t.offsetHeight,t.__resizeListeners__.forEach((function(r){r.call(t,e)})))}))};let e=!1,l="";n="animationstart";const c="Webkit Moz O ms".split(" ");let u="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),p="";{const t=document.createElement("fakeelement");if(void 0!==t.style.animationName&&(e=!0),!1===e)for(let r=0;r<c.length;r++)if(void 0!==t.style[c[r]+"AnimationName"]){p=c[r],l="-"+p.toLowerCase()+"-",n=u[r],e=!0;break}}r="resizeanim",t="@"+l+"keyframes "+r+" { from { opacity: 0; } to { opacity: 0; } } ",a=l+"animation: 1ms "+r+"; "}return{addResizeListener:function(i,c){if(l)i.attachEvent("onresize",c);else{if(!i.__resizeTriggers__){const l=i.ownerDocument,c=Ar.getComputedStyle(i);c&&"static"===c.position&&(i.style.position="relative"),function(r){if(!r.getElementById("detectElementResize")){const n=(t||"")+".resize-triggers { "+(a||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',i=r.head||r.getElementsByTagName("head")[0],o=r.createElement("style");o.id="detectElementResize",o.type="text/css",null!=e&&o.setAttribute("nonce",e),o.styleSheet?o.styleSheet.cssText=n:o.appendChild(r.createTextNode(n)),i.appendChild(o)}}(l),i.__resizeLast__={},i.__resizeListeners__=[],(i.__resizeTriggers__=l.createElement("div")).className="resize-triggers";const u=l.createElement("div");u.className="expand-trigger",u.appendChild(l.createElement("div"));const p=l.createElement("div");p.className="contract-trigger",i.__resizeTriggers__.appendChild(u),i.__resizeTriggers__.appendChild(p),i.appendChild(i.__resizeTriggers__),o(i),i.addEventListener("scroll",s,!0),n&&(i.__resizeTriggers__.__animationListener__=function(e){e.animationName===r&&o(i)},i.__resizeTriggers__.addEventListener(n,i.__resizeTriggers__.__animationListener__))}i.__resizeListeners__.push(c)}},removeResizeListener:function(e,t){if(l)e.detachEvent("onresize",t);else if(e.__resizeListeners__.splice(e.__resizeListeners__.indexOf(t),1),!e.__resizeListeners__.length){e.removeEventListener("scroll",s,!0),e.__resizeTriggers__.__animationListener__&&(e.__resizeTriggers__.removeEventListener(n,e.__resizeTriggers__.__animationListener__),e.__resizeTriggers__.__animationListener__=null);try{e.__resizeTriggers__=!e.removeChild(e.__resizeTriggers__)}catch(e){}}}}}(e),this._detectElementResize.addResizeListener(this._parentNode,this._onResize)),this._onResize()))}componentWillUnmount(){this._parentNode&&(this._detectElementResize&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize),null!==this._timeoutId&&clearTimeout(this._timeoutId),this._resizeObserver&&(this._resizeObserver.observe(this._parentNode),this._resizeObserver.disconnect()))}render(){const{children:e,defaultHeight:t,defaultWidth:n,disableHeight:a=!1,disableWidth:i=!1,nonce:o,onResize:s,style:l={},tagName:c="div",...u}=this.props,{height:p,scaledHeight:d,scaledWidth:f,width:h}=this.state,m={overflow:"visible"},g={};let v=!1;return a||(0===p&&(v=!0),m.height=0,g.height=p,g.scaledHeight=d),i||(0===h&&(v=!0),m.width=0,g.width=h,g.scaledWidth=f),(0,r.createElement)(c,{ref:this._setRef,style:{...m,...l},...u},!v&&e(g))}}const Rr={cacheDurationSeconds:300,method:"GET",queryParams:"",urlPath:"",fields:[{jsonPath:""}]},zr=({columns:e,values:r,onChange:i,addRowLabel:o,onBlur:s})=>{const l=(0,t.useTheme)(),c=Ur(l),u=e=>{i([...r.slice(0,e+1),["",""],...r.slice(e+1)])};return 0===r.length?n().createElement(t.Button,{variant:"secondary",onClick:()=>{u(0)}},o):n().createElement("table",{className:c.root},n().createElement("thead",{className:c.thead},n().createElement("tr",{className:c.row},e.map(((e,t)=>n().createElement("th",{key:t,className:c.th},e))),n().createElement("th",{className:c.th}))),n().createElement("tbody",{className:c.tbody},r.map(((e,o)=>n().createElement("tr",{key:o,className:c.row},e.map(((e,t)=>n().createElement("td",{key:t,className:c.td},n().createElement("input",{value:e,onChange:e=>((e,t,n)=>{i(r.map((([r,a],i)=>t===i?0===e?[n,a]:1===e?[r,n]:[r,a]:[r,a])))})(t,o,e.currentTarget.value),onBlur:s,className:c.input})))),n().createElement("td",{className:c.td},n().createElement("div",{className:a.css`
                  display: flex;
                  & > * {
                    margin-right: ${l.spacing.xs};
                  }
                  & > *:last-child {
                    margin-right: 0;
                  }
                `},n().createElement("a",{className:a.css`
                    display: flex;
                    background: ${l.colors.bg2};
                    padding: ${l.spacing.xs} ${l.spacing.sm};
                    align-items: center;
                    border-radius: ${l.border.radius.sm};
                  `,onClick:()=>u(o)},n().createElement(t.Icon,{name:"plus"})),n().createElement("a",{className:a.css`
                    display: flex;
                    background: ${l.colors.bg2};
                    padding: ${l.spacing.xs} ${l.spacing.sm};
                    align-items: center;
                    border-radius: ${l.border.radius.sm};
                  `,onClick:()=>{return e=o,void i([...r.slice(0,e),...r.slice(e+1)]);var e}},n().createElement(t.Icon,{name:"minus"})))))))))},Ur=(0,t.stylesFactory)((e=>({root:a.css`
      table-layout: auto;
      border: 1px solid ${e.colors.formInputBorder};
      border-collapse: separate;
      border-radius: ${e.border.radius.sm};
      border-spacing: 0;
      border-left: 0;
      width: 100%;
    `,thead:a.css`
      display: table-header-group;
      vertical-align: middle;
      border-color: inherit;
      border-collapse: separate;

      &:first-child tr:first-child th:first-child {
        border-radius: ${e.border.radius.sm} 0 0 0;
      }
      &:last-child tr:last-child th:first-child {
        border-radius: 0 0 0 ${e.border.radius.sm};
      }
    `,tbody:a.css`
      &:first-child tr:first-child td:first-child {
        border-radius: ${e.border.radius.sm} 0 0 0;
      }

      &:last-child tr:last-child td:first-child {
        border-radius: 0 0 0 ${e.border.radius.sm};
      }
    `,input:a.css`
      outline: none;
      border: 0;
      background: transparent;
      width: 100%;
    `,row:a.css`
      display: table-row;
      vertical-align: inherit;
      border-color: inherit;
    `,th:a.css`
      padding: ${e.spacing.xs} ${e.spacing.sm};
      border-left: solid ${e.colors.formInputBorder} 1px;
      font-size: ${e.typography.size.sm};
      color: ${e.colors.textSemiWeak};
      font-weight: ${e.typography.weight.regular};

      &:last-child {
        border-left: 0;
      }
    `,td:a.css`
      padding: ${e.spacing.xs} ${e.spacing.sm};
      border: 1px solid transparent;
      border-left: solid ${e.colors.formInputBorder} 1px;
      border-top: solid ${e.colors.formInputBorder} 1px;
      background-color: ${e.colors.formInputBg};
      &:last-child {
        border-left: 0;
        width: 32px;
        padding-left: 0;
        padding-right: ${e.spacing.xs};
      }
    `}))),Lr=({method:e,onMethodChange:r,path:a,onPathChange:i})=>{var o;return n().createElement(t.InlineFieldRow,null,n().createElement(t.InlineField,null,n().createElement(t.Select,{value:e,options:[{label:"GET",value:"GET"},{label:"POST",value:"POST"}],onChange:e=>r(null!==(o=e.value)&&void 0!==o?o:"GET")})),n().createElement(t.InlineField,{grow:!0},n().createElement(t.Input,{placeholder:"/orders/${orderId}",value:a,onChange:e=>i(e.currentTarget.value)})))};function Br(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Br(e,t,r[t])}))}return e}function Wr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const Jr=["authorization","proxy-authorization","x-api-key"],Hr=({query:e,onChange:i,onRunQuery:o,fieldsTab:s,experimentalTab:l})=>{const[c,u]=(0,r.useState)("plaintext"),[p,d]=(0,r.useState)(0),f=(0,t.useTheme)(),h=Tr()(e,Rr),m=e=>{i(Wr(qr({},h),{body:e})),o()};var g,v,y,b;const w=[{title:"Fields",content:s},{title:"Path",content:n().createElement(Lr,{method:null!==(g=h.method)&&void 0!==g?g:"GET",onMethodChange:e=>{i(Wr(qr({},h),{method:e})),o()},path:null!==(v=h.urlPath)&&void 0!==v?v:"",onPathChange:e=>{i(Wr(qr({},h),{urlPath:e})),o()}})},{title:"Params",content:n().createElement(zr,{addRowLabel:"Add param",columns:["Key","Value"],values:null!==(y=h.params)&&void 0!==y?y:[],onChange:e=>{i(Wr(qr({},h),{params:e})),o()},onBlur:()=>o()})},{title:"Headers",content:n().createElement(zr,{addRowLabel:"Add header",columns:["Key","Value"],values:null!==(b=h.headers)&&void 0!==b?b:[],onChange:e=>{i(Wr(qr({},h),{headers:e})),o()},onBlur:()=>o()})},{title:"Body",content:n().createElement(n().Fragment,null,n().createElement(t.InlineFieldRow,null,n().createElement(t.InlineField,{label:"Syntax highlighting"},n().createElement(t.RadioButtonGroup,{value:c,onChange:e=>u(null!=e?e:"plaintext"),options:[{label:"Text",value:"plaintext"},{label:"JSON",value:"json"},{label:"XML",value:"xml"}]}))),n().createElement(t.InlineFieldRow,null,n().createElement(Mr,{disableHeight:!0,className:a.css`
                margin-bottom: ${f.spacing.sm};
              `},(({width:e})=>n().createElement(t.CodeEditor,{value:h.body||"",language:c,width:e,height:"200px",showMiniMap:!1,showLineNumbers:!0,onBlur:m})))))},{title:"Experimental",content:l}];var x;return n().createElement(n().Fragment,null,n().createElement(t.InlineFieldRow,null,n().createElement(t.InlineField,null,n().createElement(t.RadioButtonGroup,{onChange:e=>d(null!=e?e:0),value:p,options:w.map(((e,t)=>({label:e.title,value:t})))})),n().createElement(t.InlineField,{label:"Cache Time",tooltip:"Time in seconds that the response will be cached in Grafana after receiving it."},n().createElement(t.Segment,{value:{label:Yr(h.cacheDurationSeconds),value:h.cacheDurationSeconds},options:[0,5,10,30,60,120,300,600,1800,3600,7200,18e3].map((e=>({label:Yr(e),value:e,description:e?"":"Response is not cached at all"}))),onChange:({value:e})=>i(Wr(qr({},h),{cacheDurationSeconds:e}))}))),"GET"===h.method&&h.body&&n().createElement(t.InfoBox,{severity:"warning",style:{maxWidth:"700px",whiteSpace:"normal"}},"GET requests can't have a body. The body you've defined will be ignored."),(null!==(x=h.headers)&&void 0!==x?x:[]).map((([e,t])=>e.toLowerCase())).find((e=>Jr.includes(e)))&&n().createElement(t.InfoBox,{severity:"warning",style:{maxWidth:"700px",whiteSpace:"normal"}},"It looks like you're adding credentials in the header. Since queries are stored unencrypted, it's strongly recommended that you add any secrets to the data source config instead."),w[p].content)},Yr=e=>e<60?e+"s":e<3600?e/60+"m":e/3600+"h";function Gr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Vr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Gr(e,t,r[t])}))}return e}function Qr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const Zr=e=>{const{query:t,editorContext:r,onChange:a,onRunQuery:i}=e;return n().createElement(Hr,Qr(Vr({},e),{editorContext:r||"default",fieldsTab:n().createElement(Pr,{value:t.fields,onChange:e=>{a(Qr(Vr({},t),{fields:e})),i()},limit:e.limitFields,onComplete:()=>e.datasource.metadataRequest(e.query,e.range)}),experimentalTab:n().createElement(mr,{query:t,onChange:a,onRunQuery:i,editorContext:r||"default"})}))};function Kr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Xr=c(3545),en=c(9952),tn=c.n(en),rn=c(5980),nn=c.n(rn),an=c(4195),on=c.n(an);function sn(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function ln(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){sn(i,n,a,o,s,"next",e)}function s(e){sn(i,n,a,o,s,"throw",e)}o(void 0)}))}}function cn(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class un{get(e,t,r,n,a){var i=this;return ln((function*(){const o={};let s=0;(null!=r?r:[]).forEach((([e,t])=>{e&&(o[e+"__"+s]=t,s++)})),i.params.forEach(((e,t)=>{o[t]=e}));const l=i._request(e,t,o,n,a);return(yield l.toPromise()).data}))()}test(){var e=this;return ln((function*(){const t={};return e.params.forEach(((e,r)=>{t[r]=e})),e._request("GET","",t).toPromise()}))()}cachedGet(e,t,r,n,a,i){var o=this;return ln((function*(){if(!e)return yield o.get(t,r,n,a,i);let s=o.baseUrl+r;n&&Object.keys(n).length>0&&(s=s+(s.search(/\?/)>=0?"&":"?")+n.map((([e,t])=>`${encodeURIComponent(e)}=${encodeURIComponent(t)}`)).join("&")),o.lastCacheDuration!==e&&o.cache.del(s),o.lastCacheDuration=e;const l=o.cache.get(s);if(l)return Promise.resolve(l);const c=yield o.get(t,r,n,a,i);return o.cache.put(s,c,1e3*e),c}))()}_request(e,t,r,n,a){const i={};(null!=n?n:[]).filter((([e,t])=>e)).forEach((([e,t])=>{i[e]=t}));const o={url:this.baseUrl+t,method:e,headers:i};if("GET"!==o.method&&a&&(o.data=a),o.url=o.url.replace(/[\/]+/g,"/"),r&&Object.keys(r).length>0&&(o.url=o.url+(o.url.search(/\?/)>=0?"&":"?")+Object.entries(r).map((([e,t])=>`${encodeURIComponent(e.replace(/__\d+$/,""))}=${encodeURIComponent(t)}`)).join("&")),(s=o.url).endsWith("/..")||s.includes("/../")||s.includes("/..?"))throw new Error("URL path contains unsafe characters");var s;return(0,Xr.getBackendSrv)().fetch(o)}constructor(e,t){cn(this,"cache",void 0),cn(this,"baseUrl",void 0),cn(this,"params",void 0),cn(this,"lastCacheDuration",void 0),this.baseUrl=e,this.params=new URLSearchParams("?"+t),this.cache=new(on().Cache)}}var pn=c(8283),dn=c.n(pn);const fn=t=>t.every((e=>null===e))?e.FieldType.string:t.filter((e=>null!==e)).every((e=>dn()(e,[dn().defaultFormat,"YYYY-MM-DD"],!0).isValid()))?e.FieldType.time:t.every((e=>"number"==typeof e))?e.FieldType.number:t.every((e=>"boolean"==typeof e))?e.FieldType.boolean:e.FieldType.string;var hn=c(7693),mn=c.n(hn);function gn(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function vn(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){gn(i,n,a,o,s,"next",e)}function s(e){gn(i,n,a,o,s,"throw",e)}o(void 0)}))}}function yn(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){yn(e,t,r[t])}))}return e}function wn(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}class xn extends e.DataSourceApi{metadataRequest(e,t){var r=this;return vn((function*(){return r.requestJson(e,On({},t))}))()}query(t){var r=this;return vn((function*(){(t=>{t.app!==e.CoreApp.Dashboard&&t.app!==e.CoreApp.PanelViewer&&t.targets.forEach((e=>{(0,Xr.reportInteraction)("grafana_json_query_executed",{app:t.app,cacheDurationSeconds:e.cacheDurationSeconds,method:e.method})}))})(t);const n=yield t.targets.filter((e=>!e.hide)).flatMap((e=>r.doRequest(e,t.range,t.scopedVars)));return{data:(yield Promise.all(n)).flatMap((e=>e))}}))()}metricFindQuery(e,t){var r=this;return vn((function*(){const n=(yield r.doRequest(e,t.range))[0];if(!n.fields.length)return[];var a;const i=null!==(a=n.fields.find((t=>t.name===e.experimentalVariableTextField)))&&void 0!==a?a:n.fields[0];var o;const s=null!==(o=n.fields.find((t=>t.name===e.experimentalVariableValueField)))&&void 0!==o?o:i;return Array.from({length:n.length}).map(((e,t)=>({text:i.values.get(t),value:s.values.get(t)})))}))()}testDatasource(){var e=this;return vn((function*(){const t="Cannot connect to API";try{const r=yield e.api.test();return 200===r.status?{status:"success",message:"Success"}:{status:"error",message:r.statusText?r.statusText:t}}catch(e){if(nn().isString(e))return{status:"error",message:e};{let r="JSON API: ";return r+=e.statusText?e.statusText:t,e.data&&e.data.error&&e.data.error.code&&(r+=": "+e.data.error.code+". "+e.data.error.message),{status:"error",message:r}}}}))()}doRequest(t,r,n){var a=this;return vn((function*(){const i=On(n,r),o=yield a.requestJson(t,i);if(!o)throw new Error("Query returned empty data");var s;const l=(null!==(s=t.fields)&&void 0!==s?s:[]).filter((e=>e.jsonPath)).map(((n,a)=>{if("jsonata"===n.language){const l=tn()(n.jsonPath),c={};(0,Xr.getTemplateSrv)().getVariables().map((e=>({name:e.name,value:jn(e.name)}))).forEach((e=>{c[e.name]=e.value})),Sn.map((e=>({name:e,value:jn(e)}))).forEach((e=>{c[e.name]=e.value})),r&&(c.__unixEpochFrom=r.from.valueOf(),c.__unixEpochTo=r.to.valueOf(),c.__isoFrom=r.from.toISOString(),c.__isoTo=r.to.toISOString());const u=l.evaluate(o,c),p=Array.isArray(u)?u:[u];var s;return{name:i(null!==(s=n.name)&&void 0!==s?s:"")||(t.fields.length>1?`result${a}`:"result"),type:n.type?n.type:fn(p),values:new e.ArrayVector(p),config:{}}}{const t=i(n.jsonPath),r=yr({path:t,json:o}),a=pr.toPathArray(t),s=n.type?n.type:fn(r),c=((t,r)=>{switch(r){case e.FieldType.time:if(t.filter((e=>e)).every((e=>"string"==typeof e)))return t.map((e=>null!==e?mn()(e).valueOf():e));if(t.filter((e=>e)).every((e=>"number"==typeof e))){const e=1e12;return t.filter((e=>e)).every((t=>t<e))?t.map((e=>null!==e?1e3*e:e)):t}throw new Error("Unsupported time property");case e.FieldType.string:return t.every((e=>"string"==typeof e))?t:t.map((e=>null===e?e:"object"==typeof e?JSON.stringify(e):e.toString()));case e.FieldType.number:return t.every((e=>"number"==typeof e))?t:t.map((e=>null!==e?parseFloat(e):e));case e.FieldType.boolean:return t.every((e=>"boolean"==typeof e))?t:t.map((e=>{if(null===e)return e;switch(e.toString()){case"0":case"false":case"FALSE":case"False":return!1;case"1":case"true":case"TRUE":case"True":return!0;default:throw new Error("Found non-boolean values in a field of type boolean: "+e.toString())}}));default:throw new Error("Unsupported field type")}})(r,s);var l;return{name:i(null!==(l=n.name)&&void 0!==l?l:"")||a[a.length-1],type:s,values:new e.ArrayVector(c),config:{}}}})),c=l.map((e=>e.values.length));if(Array.from(new Set(c)).length>1)throw new Error("Fields have different lengths");return(t.experimentalGroupByField?En((0,e.toDataFrame)({name:t.refId,refId:t.refId,fields:l}),t.experimentalGroupByField):[(0,e.toDataFrame)({name:t.refId,refId:t.refId,fields:l})]).map((e=>wn(bn({},e),{fields:e.fields.map((r=>r.name===t.experimentalMetricField?wn(bn({},r),{config:{displayNameFromDS:e.name}}):r))})))}))()}requestJson(e,t){var r=this;return vn((function*(){const n=([e,r])=>[t(e),t(r)];if("GET"!==e.method&&"POST"!==e.method)throw new Error(`Invalid method ${e.method}`);var a,i;return yield r.api.cachedGet(e.cacheDurationSeconds,e.method,t(e.urlPath),(null!==(a=e.params)&&void 0!==a?a:[]).map(n),(null!==(i=e.headers)&&void 0!==i?i:[]).map(n),t(e.body))}))()}constructor(e){super(e),yn(this,"api",void 0),yn(this,"annotations",{}),this.api=new un(e.url,e.jsonData.queryParams||"")}}const On=(e,t)=>r=>kn((0,Xr.getTemplateSrv)().replace(r,e),t),kn=(e,t)=>t?e.replace(/\$__unixEpochFrom\(\)/g,t.from.unix().toString()).replace(/\$__unixEpochTo\(\)/g,t.to.unix().toString()).replace(/\$__isoFrom\(\)/g,t.from.toISOString()).replace(/\$__isoTo\(\)/g,t.to.toISOString()):e,En=(t,r)=>{const n=t.fields.find((e=>e.name===r));return n?[...new Set(n.values.toArray())].map((r=>{const a=t.fields.filter((e=>e.name.toString()!==n.name)).map((t=>wn(bn({},t),{values:new e.ArrayVector(t.values.toArray().filter(((e,t)=>n.values.get(t)===r)))})));return(0,e.toDataFrame)({name:r,refId:t.refId,fields:a})})):[t]},jn=e=>{const t=[];return(0,Xr.getTemplateSrv)().replace(`$${e}`,{},(e=>(Array.isArray(e)?t.push(...e):t.push(e),""))),t},Sn=["__dashboard","__from","__to","__interval","__interval_ms","__name","__org","__user","__range","__rate_interval","timeFilter","__timeFilter"],Pn=new e.DataSourcePlugin(xn).setConfigEditor((({options:e,onOptionsChange:r})=>{const a=(0,t.useStyles2)(Zt);return n().createElement(n().Fragment,null,n().createElement(m,{dataSourceName:"JSON API",docsLink:"https://grafana.com/docs/plugins/marcusolsson-json-datasource/latest/",hasRequiredFields:!1}),n().createElement(Ht,null),n().createElement(U,{config:e,onChange:r,urlPlaceholder:"http://localhost:8080"}),n().createElement(Ht,null),n().createElement(bt,function({config:e,onChange:t}){return{selectedMethod:Ft(e),onAuthMethodSelect:Ct(e,t),basicAuth:_t(e,t),TLS:Nt(e,t),customHeaders:$t(e,t),readOnly:e.readOnly}}({config:e,onChange:r})),n().createElement(Ht,null),n().createElement(C,{title:"Additional settings",isCollapsible:!0},n().createElement(Jt,{config:e,onChange:r}),n().createElement("div",{className:a.space}),n().createElement(t.Field,{label:"Query string",description:"Add a custom query string to your queries."},n().createElement(t.Input,{width:40,value:e.jsonData.queryParams,onChange:t=>{r(Qt(Vt({},e),{jsonData:Qt(Vt({},e.jsonData),{queryParams:t.currentTarget.value})}))},spellCheck:!1,placeholder:"limit=100"}))))})).setQueryEditor(Zr).setVariableQueryEditor((e=>{const{query:t,onChange:r}=e,a=Tr()(t,Rr);return n().createElement(Zr,(i=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Kr(e,t,r[t])}))}return e}({},e),o=null!=(o={onRunQuery:()=>{},onChange:e=>{e&&r(e,e.fields[0].jsonPath)},query:a,limitFields:2,editorContext:"variables"})?o:{},Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(o)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r}(Object(o)).forEach((function(e){Object.defineProperty(i,e,Object.getOwnPropertyDescriptor(o,e))})),i));var i,o}))})(),u})()));
//# sourceMappingURL=module.js.map