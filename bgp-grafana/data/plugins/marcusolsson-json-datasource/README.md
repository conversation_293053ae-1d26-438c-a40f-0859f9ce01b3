# JSON API data source for Grafana

The Grafana JSON Datasource plugin empowers you to seamlessly integrate JSON data into Grafana. JSON is a versatile and widely used data format, and with this plugin, you can easily transform your JSON data into meaningful visualizations within Grafana dashboards.

## Documentation

For comprehensive instructions on setting up and configuring the Grafana JSON API Datasource, please consult our official [documentation](https://grafana.com/docs/plugins/marcus<PERSON>son-json-datasource/latest/).

> **Maintenance**: Now the plugin is being maintained by Grafana Labs. Big thanks to [<PERSON>](https://github.com/marcus<PERSON><PERSON>) for the awesome work!
