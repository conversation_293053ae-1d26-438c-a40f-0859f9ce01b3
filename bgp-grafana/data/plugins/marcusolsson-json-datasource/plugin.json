{"$schema": "https://github.com/grafana/grafana/raw/main/docs/sources/developers/plugins/plugin.schema.json", "annotations": true, "dependencies": {"grafanaDependency": ">=8.5.13", "grafanaVersion": "8.5.x", "plugins": []}, "id": "ma<PERSON><PERSON><PERSON><PERSON>-json-datasource", "info": {"author": {"name": "Grafana Labs", "url": "https://grafana.com"}, "build": {"time": 1712242345047, "repo": "https://github.com/grafana/grafana-json-datasource", "branch": "main", "hash": "82ca3f5fbe36c5c5daa46c82473f36e7d7e5c657", "build": 111}, "description": "A data source plugin for loading JSON APIs into Grafana.", "keywords": ["json", "api"], "links": [{"name": "Documentation", "url": "https://grafana.com/docs/plugins/marc<PERSON><PERSON><PERSON>-json-datasource/latest/"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/grafana/grafana-json-datasource"}, {"name": "License", "url": "https://github.com/grafana/grafana-json-datasource/blob/main/LICENSE"}], "logos": {"large": "img/logo.svg", "small": "img/logo.svg"}, "screenshots": [{"name": "Explore (Dark)", "path": "img/dark.png"}, {"name": "Explore (Light)", "path": "img/light.png"}], "updated": "2024-04-04", "version": "1.3.13"}, "logs": true, "metrics": true, "name": "JSON API", "type": "datasource"}