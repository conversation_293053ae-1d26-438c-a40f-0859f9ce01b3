
-----<PERSON><PERSON><PERSON> PGP SIGNED MESSAGE-----
Hash: SHA512

{
  "manifestVersion": "2.0.0",
  "signatureType": "grafana",
  "signedByOrg": "grafana",
  "signedByOrgName": "Grafana Labs",
  "plugin": "marc<PERSON><PERSON><PERSON>-json-datasource",
  "version": "1.3.13",
  "time": 1712242345607,
  "keyId": "7e4d0c6a708866e7",
  "files": {
    "CHANGELOG.md": "85c8a449af64482c7bb7b528150dca890f4894bf3563434a313c93e94818b42e",
    "LICENSE": "1fc73e522961e161c9379bf3933e639068753fee8330b0f4e9b3c57ced59cc64",
    "README.md": "d1b7113bfa3672593fefc00303208c55be007a3a5611ce19ea00afe941fe1927",
    "img/dark.png": "52a33e32b68f611273ef247dce0e60c3553fa2e54bf47b44ff93a7336bd3dbb9",
    "img/light.png": "caa7b8fa6179e4e388de0380e122b8531eb16ff6b8f3eaa23faa1f362363b0da",
    "img/logo.svg": "970745c7099fd3fe4f8c8cac1de5e38410fbee44e02bac0534cefa9f6c21d984",
    "module.js": "a6fda865ef9aeaa7608428152a917e6eca71e87d1c2cc3603d989547136c7a27",
    "module.js.map": "8f079365e8741adff9e4c0007b127079f62324c7f60956b9c423b5af01505bce",
    "plugin.json": "b056a61fccea24ab13ea3036a81a4fb0b4828ccccea45768f08a0a92e011abcc"
  }
}
-----BEGIN PGP SIGNATURE-----
Version: OpenPGP.js v4.10.11
Comment: https://openpgpjs.org

wrkEARMKAAYFAmYOvqkAIQkQfk0ManCIZucWIQTzOyW2kQdOhGNlcPN+TQxq
cIhm50F3AgkB5LkyClx7zzTKxd19D4Nol8v5DdEZ+ENSiBWwqGjlJs4YYSgw
g2VwiZizQewsVuERdUK2ji+FRadlXaiEy8TQ8GkCCQGqP80oYlMk9IKrWxS9
RFdnP6QxGhjgDQCqQqcUrI2+JYK1k4MHLLE9eqgWb7bDALBaGbmnO++vBUy8
1RgyAvvykw==
=TfG8
-----END PGP SIGNATURE-----
