{"version": 3, "sources": ["../src/module.js"], "names": ["loadPluginCss", "GeoLoopCtrl", "dark", "light"], "mappings": ";;;;;;;;AACQA,mB,kBAAAA,a;;AACDC,iB;;;AAFP;AAIAD,oBAAc;AACZE,cAAM,uDADM;AAEZC,eAAO;AAFK,OAAd;;AAKA;;2BAEEF,W", "file": "module.js", "sourcesContent": ["/* eslint import/no-extraneous-dependencies: 0 */\nimport {loadPluginCss} from 'app/plugins/sdk';\nimport GeoLoopCtrl from './geoloop_ctrl';\n\nloadPluginCss({\n  dark: 'plugins/citilogics-geoloop-panel/css/geoloop.dark.css',\n  light: 'plugins/citilogics-geoloop-panel/css/geoloop.light.css'\n});\n\n/* eslint import/prefer-default-export: 0 */\nexport {\n  GeoLoopCtrl as PanelCtrl\n};\n"]}