{"version": 3, "sources": ["../src/map_renderer.js"], "names": ["link", "scope", "elem", "attrs", "ctrl", "mapContainer", "find", "events", "on", "render", "map", "setTimeout", "resize", "renderingCompleted", "GeoLoop", "mapCenterMoved", "panToMapCenter", "legend", "panel", "showLegend", "createLegend", "updateRamp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAEe,WAASA,IAAT,CAAcC,KAAd,EAAqBC,IAArB,EAA2BC,KAA3B,EAAkCC,IAAlC,EAAwC;AACrD,QAAMC,eAAeH,KAAKI,IAAL,CAAU,eAAV,CAArB;AACA;;AAEAF,SAAKG,MAAL,CAAYC,EAAZ,CAAe,QAAf,EAAyB,YAAM;AAC7BC;AACA,UAAIL,KAAKM,GAAT,EAAc;AACZC,mBAAW,YAAM;AACfP,eAAKM,GAAL,CAASE,MAAT;AACD,SAFD,EAEG,GAFH;AAGD;AACDR,WAAKS,kBAAL;AACD,KARD;;AAUA,aAASJ,MAAT,GAAkB;AAChB,UAAI,CAACL,KAAKM,GAAV,EAAe;AACb;AACAN,aAAKM,GAAL,GAAW,IAAII,OAAJ,CAAYV,IAAZ,EAAkBC,aAAa,CAAb,CAAlB,CAAX;AACD;;AAEDD,WAAKM,GAAL,CAASE,MAAT;;AAEA,UAAIR,KAAKW,cAAT,EAAyBX,KAAKM,GAAL,CAASM,cAAT;;AAEzB,UAAI,CAACZ,KAAKM,GAAL,CAASO,MAAV,IAAoBb,KAAKc,KAAL,CAAWC,UAAnC,EAA+Cf,KAAKM,GAAL,CAASU,YAAT;;AAE/ChB,WAAKiB,UAAL;AACAjB,WAAKM,GAAL,CAASY,eAAT;AACD;AACF;;qBA7BuBtB,I;;;;AAFjBc,a", "file": "map_renderer.js", "sourcesContent": ["import GeoLoop from './geoloop';\n\nexport default function link(scope, elem, attrs, ctrl) {\n  const mapContainer = elem.find('.mapcontainer');\n  // console.log('found: ', mapContainer);\n\n  ctrl.events.on('render', () => {\n    render();\n    if (ctrl.map) {\n      setTimeout(() => {\n        ctrl.map.resize();\n      }, 500);\n    }\n    ctrl.renderingCompleted();\n  });\n\n  function render() {\n    if (!ctrl.map) {\n      // console.log('creating new map');\n      ctrl.map = new GeoLoop(ctrl, mapContainer[0]);\n    }\n\n    ctrl.map.resize();\n\n    if (ctrl.mapCenterMoved) ctrl.map.panToMapCenter();\n\n    if (!ctrl.map.legend && ctrl.panel.showLegend) ctrl.map.createLegend();\n\n    ctrl.updateRamp();\n    ctrl.map.drawLayerFrames();\n  }\n}\n"]}