{"version": 3, "sources": ["../src/geoloop_ctrl.js"], "names": ["MetricsPanelCtrl", "TimeSeries", "kbn", "contextSrv", "_", "d3", "<PERSON><PERSON><PERSON><PERSON>", "DataFormatter", "panelDefaults", "mbApi<PERSON>ey", "mapStyle", "mapCenterLatitude", "mapCenterLongitude", "initialZoom", "userInteractionEnabled", "animationSpeed", "animationPause", "geoIdTag", "geoIdPath", "geo", "location", "contents", "callback", "renderType", "sizeRamp", "codeTo", "fixedValue", "measurement", "auto", "min", "max", "minValue", "maxValue", "showLegend", "legendPosition", "colorRamp", "scaleName", "GeoLoopCtrl", "$scope", "$injector", "dataCharacteristics", "opts", "renderTypes", "colorRamps", "interpolateCubehelixDefault", "interpolate<PERSON><PERSON><PERSON>", "interpolateWarm", "interpolateCool", "interpolate<PERSON><PERSON><PERSON>", "interpolateMagma", "interpolateInferno", "interpolatePlasma", "mapStyles", "featureTypes", "layerTypes", "defaults", "panel", "setMapProviderOpts", "dataFormatter", "events", "on", "onInitEditMode", "bind", "onDataReceived", "onPanelTeardown", "onDataSnapshotLoad", "loadGeo", "lonLatStr", "Object", "keys", "center", "map", "getCenter", "lng", "lat", "coords", "split", "strVal", "Number", "trim", "mapCenterMoved", "render", "remove", "hardRefresh", "updateRamp", "user", "lightTheme", "saturationClass", "stopAnimation", "clearFrames", "setStyle", "updateGeoDataFeatures", "reload", "snapshotLocationData", "window", "$", "ajax", "type", "url", "contentType", "jsonpCallback", "dataType", "success", "res", "console", "log", "fail", "addEditorTab", "dataList", "dashboard", "snapshot", "series", "seriesH<PERSON>ler", "getCharacteristics", "snapshotData", "seriesData", "datapoints", "alias", "target", "flotpairs", "getFlotPairs", "nullPointMode", "setZoom", "features", "getSource", "removeSource", "timeValues", "for<PERSON>ach", "tv", "feature", "fname", "properties", "keyedSeries", "geoKeySearch", "reStr", "reg", "RegExp", "matches", "match", "length", "featureId", "reduce", "obj", "key", "point", "time", "val", "addSource", "data", "dc", "colorInterpolator", "inputRange", "theRamp", "scaleSequential", "domain", "interpolator", "sizeInterpolator", "px", "py", "scope", "elem", "attrs", "ctrl", "templateUrl"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACQA,sB,kBAAAA,gB;;AACDC,gB;;AACAC,S;;AACCC,gB,gBAAAA,U;;AAEDC,O;;AACKC,Q;;AACLC,iB;;AACAC,mB;;;;;;;;;;;;;;;;;;;;;AAGDC,mB,GAAgB;AACpBC,kBAAU,cADU;AAEpBC,kBAAU,aAFU,EAEK;AACzBC,2BAAmB,CAHC;AAIpBC,4BAAoB,CAJA;AAKpBC,qBAAa,CALO;AAMpBC,gCAAwB,IANJ;AAOpBC,wBAAgB,CAPI,EAOD;AACnBC,wBAAgB,GARI,EAQC;AACrBC,kBAAU,QATU;AAUpBC,mBAAW,IAVS;AAWpBC,aAAK;AACHC,oBAAU,KADP,EACc;AACjBC,oBAAU,QAFP,EAEiB;AACpBC,oBAAU,MAHP,CAGc;AAHd,SAXe;AAgBpBC,oBAAY,MAhBQ,EAgBA;AACpBC,kBAAU;AACRC,kBAAQ,OADA,EACS;AACjBC,sBAAY,CAFJ;AAGRC,uBAAa,kBAHL;AAIRC,gBAAM,KAJE;AAKRC,eAAK,CALG;AAMRC,eAAK,EANG;AAORC,oBAAU,CAPF;AAQRC,oBAAU,GARF;AASRC,sBAAY,IATJ;AAURC,0BAAgB;AAVR,SAjBU;AA6BpBC,mBAAW;AACTV,kBAAQ,OADC,EACQ;AACjBC,sBAAY,SAFH;AAGTC,uBAAa,kBAHJ;AAITC,gBAAM,KAJG;AAKTG,oBAAU,CALD;AAMTC,oBAAU,GAND;AAOTI,qBAAW,SAPF,EAOa;AACtBH,sBAAY,IARH;AASTC,0BAAgB;AATP;AA7BS,O;;AA0CDG,iB;;;AACnB,6BAAYC,MAAZ,EAAoBC,SAApB,EAA+BpC,UAA/B,EAA2C;AAAA;;AAAA,gIACnCmC,MADmC,EAC3BC,SAD2B;;AAGzC,gBAAKC,mBAAL,GAA2B,EAA3B;;AAEA,gBAAKC,IAAL,GAAY;AACVC,yBAAa,CAAC,MAAD,EAAS,OAAT,EAAkB,SAAlB,CADH;AAEVC,wBAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BZ,2BAAatC,GAAGuC,2BA7BN;AA8BV,yBAAWvC,GAAGwC,kBA9BJ;AA+BV,sBAAQxC,GAAGyC,eA/BD;AAgCV,sBAAQzC,GAAG0C,eAhCD;AAiCV,yBAAW1C,GAAG2C,kBAjCJ;AAkCV,uBAAS3C,GAAG4C,gBAlCF;AAmCV,yBAAW5C,GAAG6C,kBAnCJ;AAoCV,wBAAU7C,GAAG8C;AApCH,aAFF;AAwCVC,uBAAW;AACT,yBAAW,aADF;AAET,0BAAY,cAFH;AAGT,uBAAS,UAHA;AAIT,sBAAQ,SAJC;AAKT,2BAAa,cALJ;AAMT,mCAAqB,uBANZ;AAOT,yBAAW,gBAPF;AAQT,+BAAiB;AARR,aAxCD;AAkDVC,0BAAc;AACZ,uBAAS,OADG;AAEZ,sBAAQ,MAFI;AAGZ,yBAAW;AAHC,aAlDJ;AAuDVC,wBAAY;AACV,uBAAS,QADC;AAEV,yBAAW,MAFD;AAGV,sBAAQ;AAHE;AAvDF,WAAZ;AA6DA;AACAlD,YAAEmD,QAAF,CAAW,MAAKC,KAAhB,EAAuBhD,aAAvB;AACAJ,YAAEmD,QAAF,CAAW,MAAKC,KAAhB,EAAuBhD,cAAc2B,SAArC;AACA/B,YAAEmD,QAAF,CAAW,MAAKC,KAAhB,EAAuBhD,cAAcgB,QAArC;AACApB,YAAEmD,QAAF,CAAW,MAAKC,KAAhB,EAAuBhD,cAAcW,GAArC;AACA,gBAAKsC,kBAAL;;AAEA,gBAAKC,aAAL,GAAqB,IAAInD,aAAJ,QAAwBL,GAAxB,CAArB;;AAEA,gBAAKyD,MAAL,CAAYC,EAAZ,CAAe,gBAAf,EAAiC,MAAKC,cAAL,CAAoBC,IAApB,OAAjC;AACA,gBAAKH,MAAL,CAAYC,EAAZ,CAAe,eAAf,EAAgC,MAAKG,cAAL,CAAoBD,IAApB,OAAhC;AACA,gBAAKH,MAAL,CAAYC,EAAZ,CAAe,gBAAf,EAAiC,MAAKI,eAAL,CAAqBF,IAArB,OAAjC;AACA,gBAAKH,MAAL,CAAYC,EAAZ,CAAe,oBAAf,EAAqC,MAAKK,kBAAL,CAAwBH,IAAxB,OAArC;;AAEA,gBAAKI,OAAL;AACA,gBAAKC,SAAL,GAAiB,MAAKX,KAAL,CAAW5C,kBAAX,GAAgC,GAAhC,GAAsC,MAAK4C,KAAL,CAAW7C,iBAAlE;;AAEA;AACA;AApFyC;AAqF1C;;;;gDAEqB;AACpB,mBAAO,gEAAgE,KAAK6C,KAAL,CAAWrB,SAAX,CAAqBC,SAArF,GAAiG,MAAxG;AACD;;;0CACe;AACd,mBAAOgC,OAAOC,IAAP,CAAY,KAAK5B,IAAL,CAAUE,UAAtB,CAAP;AACD;;;+CAEoB;AACnB,gBAAM2B,SAAS,KAAKC,GAAL,CAASA,GAAT,CAAaC,SAAb,EAAf;AACA,iBAAKhB,KAAL,CAAW5C,kBAAX,GAAgC0D,OAAOG,GAAvC;AACA,iBAAKjB,KAAL,CAAW7C,iBAAX,GAA+B2D,OAAOI,GAAtC;AACA,iBAAKP,SAAL,GAAiB,KAAKX,KAAL,CAAW5C,kBAAX,GAAgC,GAAhC,GAAsC,KAAK4C,KAAL,CAAW7C,iBAAlE;AACD;;;4CAEiB;AAChB,gBAAMgE,SAAS,KAAKR,SAAL,CAAeS,KAAf,CAAqB,GAArB,EAA0BL,GAA1B,CAA8B,UAACM,MAAD,EAAY;AACvD,qBAAOC,OAAOD,OAAOE,IAAP,EAAP,CAAP;AACD,aAFc,CAAf;AAGA,iBAAKvB,KAAL,CAAW5C,kBAAX,GAAgC+D,OAAO,CAAP,CAAhC;AACA,iBAAKnB,KAAL,CAAW7C,iBAAX,GAA+BgE,OAAO,CAAP,CAA/B;;AAEA,iBAAKK,cAAL,GAAsB,IAAtB;AACA,iBAAKC,MAAL;AACD;;;yCAEc;AACb,gBAAI,KAAKV,GAAT,EAAc;AACZ,mBAAKA,GAAL,CAASW,MAAT;AACD;AACD,iBAAKX,GAAL,GAAW,IAAX;AACA,iBAAKU,MAAL;AACA,iBAAKE,WAAL;AACD;;;wCAEa;AACZ,iBAAKC,UAAL;AACA,iBAAKlB,OAAL,CAAa,IAAb;AACD;;;+CAEoB;AAAA;;AACnB,gBAAI/D,WAAWkF,IAAX,CAAgBC,UAApB,EAAgC;AAC9B,mBAAKC,eAAL,GAAuB,EAAvB;AACD,aAFD,MAEO;AACL,mBAAKA,eAAL,GAAuB,YAAvB;AACD;;AAED,gBAAI,KAAKhB,GAAT,EAAc;AACZ,mBAAKA,GAAL,CAASiB,aAAT;AACA,mBAAKjB,GAAL,CAASkB,WAAT;AACA,mBAAKlB,GAAL,CAASA,GAAT,CAAamB,QAAb,CAAsB,4BAA4B,KAAKlC,KAAL,CAAW9C,QAA7D,EAAuEkD,EAAvE,CAA0E,YAA1E,EAAwF,YAAM;AAC5F,uBAAK+B,qBAAL;AACA,uBAAKV,MAAL;AACD,eAHD;AAID;AACF;;;kCAEOW,M,EAAQ;AAAA;;AACd,gBAAI,KAAKrB,GAAL,IAAY,CAACqB,MAAjB,EAAyB;AACvB;AACD;;AAED,gBAAI,KAAKpC,KAAL,CAAWqC,oBAAf,EAAqC;AACnC,mBAAK1E,GAAL,GAAW,KAAKqC,KAAL,CAAWqC,oBAAtB;AACA;AACD;;AAED,gBAAI,KAAKrC,KAAL,CAAWrC,GAAX,CAAeC,QAAf,KAA4B,KAAhC,EAAuC;AACrC,kBAAI,CAAC,KAAKoC,KAAL,CAAWrC,GAAX,CAAeE,QAApB,EAA8B;AAC5B;AACD;AACDyE,qBAAOC,CAAP,CAASC,IAAT,CAAc;AACZC,sBAAM,KADM;AAEZC,qBAAK,KAAK1C,KAAL,CAAWrC,GAAX,CAAeE,QAAf,GAA0B,aAFnB;AAGZ8E,6BAAa,kBAHD;AAIZC,+BAAe,KAAK5C,KAAL,CAAWrC,GAAX,CAAeG,QAJlB;AAKZ+E,0BAAU,OALE;AAMZC,yBAAS,iBAACC,GAAD,EAAS;AAChBC,0BAAQC,GAAR,CAAY,oBAAZ;AACA,yBAAKtF,GAAL,GAAWoF,GAAX;AACA,yBAAKZ,qBAAL;AACA,yBAAKV,MAAL;AACD;AAXW,eAAd,EAYGyB,IAZH,CAYQ,UAACH,GAAD,EAAS;AACfC,wBAAQC,GAAR,CAAY,iBAAZ,EAA+BF,GAA/B;AACA,uBAAKpF,GAAL,GAAW,IAAX;AACA,uBAAK8D,MAAL;AACD,eAhBD;AAiBD,aArBD,MAqBO,IAAI,KAAKzB,KAAL,CAAWrC,GAAX,CAAeC,QAAf,KAA4B,MAAhC,EAAwC;AAC7C;AACD;AACF;;;4CAEiB;AAChB,gBAAI,KAAKmD,GAAT,EAAc,KAAKA,GAAL,CAASW,MAAT;AACf;;;2CAEgB;AACfsB,oBAAQC,GAAR,CAAY,gBAAZ;AACA,iBAAKE,YAAL,CAAkB,SAAlB,EAA6B,8DAA7B;AACD;;;yCAEcC,Q,EAAU;AACvB;AACA,gBAAI,CAACA,QAAL,EAAe;;AAEf,gBAAI,KAAKC,SAAL,CAAeC,QAAf,IAA2B,KAAK3F,GAApC,EAAyC;AACvC,mBAAKqC,KAAL,CAAWqC,oBAAX,GAAkC,KAAK1E,GAAvC;AACD;;AAED,iBAAK4F,MAAL,GAAcH,SAASrC,GAAT,CAAa,KAAKyC,aAAL,CAAmBlD,IAAnB,CAAwB,IAAxB,CAAb,CAAd;AACA;AACA,iBAAKtB,mBAAL,GAA2B,KAAKkB,aAAL,CAAmBuD,kBAAnB,EAA3B;AACA,iBAAK7B,UAAL;AACA,iBAAKO,qBAAL;AACA,iBAAKV,MAAL;AACD;;;6CAEkBiC,Y,EAAc;AAC/B,iBAAKnD,cAAL,CAAoBmD,YAApB;AACD;;;wCAEaC,U,EAAY;AACxB,gBAAMJ,SAAS,IAAI9G,UAAJ,CAAe;AAC5BmH,0BAAYD,WAAWC,UADK;AAE5BC,qBAAOF,WAAWG;AAFU,aAAf,CAAf;;AAKAP,mBAAOQ,SAAP,GAAmBR,OAAOS,YAAP,CAAoB,KAAKhE,KAAL,CAAWiE,aAA/B,CAAnB;AACA,mBAAOV,MAAP;AACD;;;oCAES;AACR,iBAAKxC,GAAL,CAASmD,OAAT,CAAiB,KAAKlE,KAAL,CAAW3C,WAAX,IAA0B,CAA3C;AACD;;;yCAEc;AACb,iBAAKoE,MAAL;AACD;;;kDAEuB;AAAA;;AACtB,gBAAI,CAAC,KAAK9D,GAAN,IAAa,CAAC,KAAKA,GAAL,CAASwG,QAA3B,EAAqC;AACnCnB,sBAAQC,GAAR,CAAY,uBAAZ;AACA;AACD;AACD,gBAAI,KAAKlC,GAAL,IAAY,KAAKA,GAAL,CAASA,GAAT,CAAaqD,SAAb,CAAuB,KAAvB,CAAhB,EAA+C;AAC7C;AACA,mBAAKrD,GAAL,CAASA,GAAT,CAAasD,YAAb,CAA0B,KAA1B;AACD;;AAED;AACA,iBAAKrF,mBAAL,CAAyBsF,UAAzB,CAAoCC,OAApC,CAA4C,UAACC,EAAD,EAAQ;AAClD,qBAAK7G,GAAL,CAASwG,QAAT,CAAkBI,OAAlB,CAA0B,UAACE,OAAD,EAAa;AACrC,oBAAMC,QAAQ,OAAOF,EAArB;AACA,oBAAIC,QAAQE,UAAR,IAAsBF,QAAQE,UAAR,CAAmBD,KAAnB,CAA1B,EAAqD;AACnD,yBAAOD,QAAQE,UAAR,CAAmBD,KAAnB,CAAP;AACD;AACF,eALD;AAMD,aAPD;;AAUA;AACA,gBAAME,cAAc,EAApB;AACA,gBAAMC,eAAe,KAAK7E,KAAL,CAAWvC,QAAX,GAAsB,GAA3C;AACA,gBAAMqH,QAAQD,eAAe,WAA7B;AACA,gBAAME,MAAM,IAAIC,MAAJ,CAAWF,KAAX,CAAZ;AACA,iBAAKvB,MAAL,CAAYgB,OAAZ,CAAoB,UAAChB,MAAD,EAAY;AAC9B;AACA,kBAAM0B,UAAU1B,OAAOM,KAAP,CAAaqB,KAAb,CAAmBH,GAAnB,CAAhB;AACA;AACA,kBAAIE,WAAWA,QAAQE,MAAR,GAAiB,CAAhC,EAAmC;AACjCP,4BAAYK,QAAQ,CAAR,CAAZ,IAA0B1B,MAA1B;AACD;AACF,aAPD;;AASA;AACA;;AAEA;AACA,iBAAK5F,GAAL,CAASwG,QAAT,CAAkBI,OAAlB,CAA0B,UAACE,OAAD,EAAa;AACrC,kBAAI,CAACA,QAAQE,UAAb,EAAyB;AACvBF,wBAAQE,UAAR,GAAqB,EAArB;AACD;AACD;AACA;AACA,kBAAMS,YAAY,OAAKpF,KAAL,CAAWtC,SAAX,CAAqB0D,KAArB,CAA2B,GAA3B,EAAgCiE,MAAhC,CAAuC,UAACC,GAAD,EAAMC,GAAN;AAAA,uBAAcD,IAAIC,GAAJ,CAAd;AAAA,eAAvC,EAA+Dd,OAA/D,CAAlB;AACA,kBAAIW,aAAaR,WAAjB,EAA8B;AAC5B,oBAAMrB,SAASqB,YAAYQ,SAAZ,CAAf;AACA7B,uBAAOK,UAAP,CAAkBW,OAAlB,CAA0B,UAACiB,KAAD,EAAW;AACnC,sBAAMC,OAAOD,MAAM,CAAN,CAAb;AACA,sBAAME,MAAMF,MAAM,CAAN,CAAZ;AACAf,0BAAQE,UAAR,CAAmB,OAAOc,IAA1B,IAAkCC,GAAlC;AACD,iBAJD;AAKD;AACF,aAfD;;AAiBA,gBAAI,KAAK/H,GAAL,IAAY,KAAKoD,GAArB,EAA0B;AACxBiC,sBAAQC,GAAR,CAAY,0BAAZ;AACA,mBAAKlC,GAAL,CAASA,GAAT,CAAa4E,SAAb,CAAuB,KAAvB,EAA8B;AAC5BlD,sBAAM,SADsB;AAE5BmD,sBAAM,KAAKjI;AAFiB,eAA9B;AAID,aAND,MAOK;AACHqF,sBAAQC,GAAR,CAAY,kCAAZ;AACD;AACF;;;uCAGY;AAAA;;AAAE;AACb,gBAAM4C,KAAK,KAAK7G,mBAAhB;AACA,gBAAI,KAAKgB,KAAL,CAAWrB,SAAX,CAAqBV,MAArB,KAAgC,OAApC,EAA6C;AAC3C,mBAAK+B,KAAL,CAAW8F,iBAAX,GAA+B,YAAM;AAAE,uBAAO,OAAK9F,KAAL,CAAWrB,SAAX,CAAqBT,UAA5B;AAAyC,eAAhF;AACD,aAFD,MAEO;AACL,kBAAM6H,aAAa,KAAK/F,KAAL,CAAWrB,SAAX,CAAqBP,IAArB,GAA4B,CAACyH,GAAGxH,GAAJ,EAASwH,GAAGvH,GAAZ,CAA5B,GAA+C,CAAC,KAAK0B,KAAL,CAAWrB,SAAX,CAAqBJ,QAAtB,EAAgC,KAAKyB,KAAL,CAAWrB,SAAX,CAAqBH,QAArD,CAAlE;AACA,kBAAMwH,UAAU,KAAK/G,IAAL,CAAUE,UAAV,CAAqB,KAAKa,KAAL,CAAWrB,SAAX,CAAqBC,SAA1C,CAAhB;AACA;AACA;AACA,mBAAKoB,KAAL,CAAW8F,iBAAX,GAA+BjJ,GAAGoJ,eAAH,GAAqBC,MAArB,CAA4BH,UAA5B,EAAwCI,YAAxC,CAAqDH,OAArD,CAA/B;AACD;;AAED,gBAAI,KAAKhG,KAAL,CAAWhC,QAAX,CAAoBC,MAApB,KAA+B,OAAnC,EAA4C;AAC1C,mBAAK+B,KAAL,CAAWoG,gBAAX,GAA8B,YAAM;AAAE,uBAAO,OAAKpG,KAAL,CAAWhC,QAAX,CAAoBE,UAA3B;AAAwC,eAA9E;AACD,aAFD,MAEO;AACL,kBAAMmI,KAAK,KAAKrG,KAAL,CAAWhC,QAAX,CAAoBI,IAApB,GAA2B,CAACyH,GAAGxH,GAAJ,EAASwH,GAAGvH,GAAZ,CAA3B,GAA8C,CAAC,KAAK0B,KAAL,CAAWhC,QAAX,CAAoBO,QAArB,EAA+B,KAAKyB,KAAL,CAAWhC,QAAX,CAAoBQ,QAAnD,CAAzD;AACA,kBAAM8H,KAAK,CAAC,KAAKtG,KAAL,CAAWhC,QAAX,CAAoBK,GAArB,EAA0B,KAAK2B,KAAL,CAAWhC,QAAX,CAAoBM,GAA9C,CAAX;AACA,mBAAK0B,KAAL,CAAWoG,gBAAX,GAA8B,UAACV,GAAD,EAAS;AAAE,uBAAOY,GAAG,CAAH,IAAU,CAACZ,MAAMW,GAAG,CAAH,CAAP,KAAiBC,GAAG,CAAH,IAAQA,GAAG,CAAH,CAAzB,CAAD,IAAqCD,GAAG,CAAH,IAAQA,GAAG,CAAH,CAA7C,CAAhB;AAAuE,eAAhH;AACD;AACF;;;+BAIIE,K,EAAOC,I,EAAMC,K,EAAOC,I,EAAM;AAC7B5J,wBAAYyJ,KAAZ,EAAmBC,IAAnB,EAAyBC,KAAzB,EAAgCC,IAAhC;AACD;;;;QAjUsClK,gB;;yBAApBqC,W;;AAoUrBA,kBAAY8H,WAAZ,GAA0B,aAA1B", "file": "geoloop_ctrl.js", "sourcesContent": ["/* eslint import/no-extraneous-dependencies: 0 */\nimport {MetricsPanelCtrl} from 'app/plugins/sdk';\nimport TimeSeries from 'app/core/time_series2';\nimport kbn from 'app/core/utils/kbn';\nimport {contextSrv} from 'app/core/core';\n\nimport _ from 'lodash';\nimport * as d3 from './libs/d3';\nimport mapRenderer from './map_renderer';\nimport DataFormatter from './data_formatter';\nimport './css/geoloop-panel.css!';\n\nconst panelDefaults = {\n  mbApiKey: 'pk.eyXXXXXXX',\n  mapStyle: 'streets-v10', // see opts below\n  mapCenterLatitude: 0,\n  mapCenterLongitude: 0,\n  initialZoom: 8,\n  userInteractionEnabled: true,\n  animationSpeed: 1, // # of seconds animation time per day of data\n  animationPause: 500, // millisecond pause at end of animation loop\n  geoIdTag: 'geo_id',\n  geoIdPath: 'id',\n  geo: {\n    location: 'url', // one of: url, text\n    contents: 'xxxxxx', // either the jsonp url or the json text itself\n    callback: 'data' // named callback in jsonp contents\n  },\n  renderType: 'line', // one of: line,point,polygon\n  sizeRamp: {\n    codeTo: 'fixed', // or 'measurement'\n    fixedValue: 5,\n    measurement: 'measurement_name',\n    auto: false,\n    min: 1,\n    max: 10,\n    minValue: 0,\n    maxValue: 100,\n    showLegend: true,\n    legendPosition: 'l'\n  },\n  colorRamp: {\n    codeTo: 'fixed', // or 'measurement'\n    fixedValue: '#0000ff',\n    measurement: 'measurement_name',\n    auto: false,\n    minValue: 1,\n    maxValue: 100,\n    scaleName: 'viridis', // one of D3's color ramps\n    showLegend: true,\n    legendPosition: 'l'\n  },\n};\n\nexport default class GeoLoopCtrl extends MetricsPanelCtrl {\n  constructor($scope, $injector, contextSrv) {\n    super($scope, $injector);\n\n    this.dataCharacteristics = {};\n\n    this.opts = {\n      renderTypes: ['line', 'point', 'polygon'],\n      colorRamps: { /*\n      for some reason, the extra d3-scale-chromatic library is hard to import ??\n        'BrBG': scale.interpolateBrBG,\n        'PRGn': scale.interpolatePRGn,\n        'PiYG': scale.interpolatePiYG,\n        'PuOr': scale.interpolatePuOr,\n        'RdBu': scale.interpolateRdBu,\n        'RdGy': scale.interpolateRdGy,\n        'RdYlBu': scale.interpolateRdYlBu,\n        'RdYlGn': scale.interpolateRdYlGn,\n        'Spectral': scale.interpolateSpectral,\n        'Blues': scale.interpolateBlues,\n        'Greens': scale.interpolateGreens,\n        'Greys': scale.interpolateGreys,\n        'Oranges': scale.interpolateOranges,\n        'Purples': scale.interpolatePurples,\n        'Reds': scale.interpolateReds,\n        'BuGn': scale.interpolateBuGn,\n        'BuPu': scale.interpolateBuPu,\n        'GnBu': scale.interpolateGnBu,\n        'OrRd': scale.interpolateOrRd,\n        'PuBuGn': scale.interpolatePuBuGn,\n        'PuBu': scale.interpolatePuBu,\n        'PuRd': scale.interpolatePuRd,\n        'RdPu': scale.interpolateRdPu,\n        'YlGnBu': scale.interpolateYlGnBu,\n        'YlGn': scale.interpolateYlGn,\n        'YlOrBr': scale.interpolateYlOrBr,\n        'YlOrRd': scale.interpolateYlOrRd */\n        'cubehelix': d3.interpolateCubehelixDefault,\n        'rainbow': d3.interpolateRainbow,\n        'warm': d3.interpolateWarm,\n        'cool': d3.interpolateCool,\n        'viridis': d3.interpolateViridis,\n        'magma': d3.interpolateMagma,\n        'inferno': d3.interpolateInferno,\n        'plasma': d3.interpolatePlasma\n      },\n      mapStyles: {\n        'streets': 'streets-v10',\n        'outdoors': 'outdoors-v10',\n        'light': 'light-v9',\n        'dark': 'dark-v9',\n        'satellite': 'satellite-v9',\n        'satellite-streets': 'satellite-streets-v10',\n        'traffic': 'traffic-day-v2',\n        'traffic-night': 'traffic-night-v2'\n      },\n      featureTypes: {\n        'Point': 'point',\n        'Line': 'line',\n        'Polygon': 'polygon'\n      },\n      layerTypes: {\n        'point': 'circle',\n        'polygon': 'fill',\n        'line': 'line'\n      }\n    };\n    /* set defaults: */\n    _.defaults(this.panel, panelDefaults);\n    _.defaults(this.panel, panelDefaults.colorRamp);\n    _.defaults(this.panel, panelDefaults.sizeRamp);\n    _.defaults(this.panel, panelDefaults.geo);\n    this.setMapProviderOpts();\n\n    this.dataFormatter = new DataFormatter(this, kbn);\n\n    this.events.on('init-edit-mode', this.onInitEditMode.bind(this));\n    this.events.on('data-received', this.onDataReceived.bind(this));\n    this.events.on('panel-teardown', this.onPanelTeardown.bind(this));\n    this.events.on('data-snapshot-load', this.onDataSnapshotLoad.bind(this));\n\n    this.loadGeo();\n    this.lonLatStr = this.panel.mapCenterLongitude + ',' + this.panel.mapCenterLatitude;\n\n    //$scope.$root.onAppEvent('show-dash-editor', this.doMapResize());\n    //$scope.$root.onAppEvent('hide-dash-editor', this.doMapResize());\n  }\n\n  getColorScaleImgUrl() {\n    return '/public/plugins/citilogics-geoloop-panel/images/colorRamps/' + this.panel.colorRamp.scaleName + '.png';\n  }\n  getColorNames() {\n    return Object.keys(this.opts.colorRamps);\n  }\n\n  setLocationFromMap() {\n    const center = this.map.map.getCenter();\n    this.panel.mapCenterLongitude = center.lng;\n    this.panel.mapCenterLatitude = center.lat;\n    this.lonLatStr = this.panel.mapCenterLongitude + ',' + this.panel.mapCenterLatitude;\n  }\n\n  setNewMapCenter() {\n    const coords = this.lonLatStr.split(',').map((strVal) => {\n      return Number(strVal.trim());\n    });\n    this.panel.mapCenterLongitude = coords[0];\n    this.panel.mapCenterLatitude = coords[1];\n\n    this.mapCenterMoved = true;\n    this.render();\n  }\n\n  hardResetMap() {\n    if (this.map) {\n      this.map.remove();\n    }\n    this.map = null;\n    this.render();\n    this.hardRefresh();\n  }\n\n  hardRefresh() {\n    this.updateRamp();\n    this.loadGeo(true);\n  }\n\n  setMapProviderOpts() {\n    if (contextSrv.user.lightTheme) {\n      this.saturationClass = '';\n    } else {\n      this.saturationClass = 'map-darken';\n    }\n\n    if (this.map) {\n      this.map.stopAnimation();\n      this.map.clearFrames();\n      this.map.map.setStyle('mapbox://styles/mapbox/' + this.panel.mapStyle).on('style.load', () => {\n        this.updateGeoDataFeatures();\n        this.render();\n      });\n    }\n  }\n\n  loadGeo(reload) {\n    if (this.map && !reload) {\n      return;\n    }\n\n    if (this.panel.snapshotLocationData) {\n      this.geo = this.panel.snapshotLocationData;\n      return;\n    }\n\n    if (this.panel.geo.location === 'url') {\n      if (!this.panel.geo.contents) {\n        return;\n      }\n      window.$.ajax({\n        type: 'GET',\n        url: this.panel.geo.contents + '?callback=?',\n        contentType: 'application/json',\n        jsonpCallback: this.panel.geo.callback,\n        dataType: 'jsonp',\n        success: (res) => {\n          console.log('downloaded geojson');\n          this.geo = res;\n          this.updateGeoDataFeatures();\n          this.render();\n        }\n      }).fail((res) => {\n        console.log('error in ajax: ', res);\n        this.geo = null;\n        this.render();\n      });\n    } else if (this.panel.geo.location === 'text') {\n      // nothing\n    }\n  }\n\n  onPanelTeardown() {\n    if (this.map) this.map.remove();\n  }\n\n  onInitEditMode() {\n    console.log('init edit mode');\n    this.addEditorTab('GeoLoop', 'public/plugins/citilogics-geoloop-panel/partials/editor.html');\n  }\n\n  onDataReceived(dataList) {\n    // console.log('ctrl recieved data: ', dataList);\n    if (!dataList) return;\n\n    if (this.dashboard.snapshot && this.geo) {\n      this.panel.snapshotLocationData = this.geo;\n    }\n\n    this.series = dataList.map(this.seriesHandler.bind(this));\n    // console.log('series: ', this.series);\n    this.dataCharacteristics = this.dataFormatter.getCharacteristics();\n    this.updateRamp();\n    this.updateGeoDataFeatures();\n    this.render();\n  }\n\n  onDataSnapshotLoad(snapshotData) {\n    this.onDataReceived(snapshotData);\n  }\n\n  seriesHandler(seriesData) {\n    const series = new TimeSeries({\n      datapoints: seriesData.datapoints,\n      alias: seriesData.target,\n    });\n\n    series.flotpairs = series.getFlotPairs(this.panel.nullPointMode);\n    return series;\n  }\n\n  setZoom() {\n    this.map.setZoom(this.panel.initialZoom || 1);\n  }\n\n  toggleLegend() {\n    this.render();\n  }\n\n  updateGeoDataFeatures() {\n    if (!this.geo || !this.geo.features) {\n      console.log('no geo or no features');\n      return;\n    }\n    if (this.map && this.map.map.getSource('geo')) {\n      // console.log('geojson source found. removing...');\n      this.map.map.removeSource('geo');\n    }\n\n    // clear timeseries data from geojson data\n    this.dataCharacteristics.timeValues.forEach((tv) => {\n      this.geo.features.forEach((feature) => {\n        const fname = 'f-' + tv;\n        if (feature.properties && feature.properties[fname]) {\n          delete feature.properties[fname];\n        }\n      });\n    });\n\n\n    // organize the series data - using the \"tag\" user has selected for correspondence with feature.id:\n    const keyedSeries = {};\n    const geoKeySearch = this.panel.geoIdTag + ':';\n    const reStr = geoKeySearch + ' ([^,}]+)';\n    const reg = new RegExp(reStr);\n    this.series.forEach((series) => {\n      // expect series.alias to be of the form --> \"measure.aggregator {tagKey: tagVal, tagKey: tagVal}\"\n      const matches = series.alias.match(reg);\n      // console.log('matches: ', matches);\n      if (matches && matches.length > 1) {\n        keyedSeries[matches[1]] = series;\n      }\n    });\n\n    // console.log('features: ', this.geo.features);\n    // console.log('keyed series: ', keyedSeries);\n\n    // put data into features.\n    this.geo.features.forEach((feature) => {\n      if (!feature.properties) {\n        feature.properties = {};\n      }\n      // this funny business below deserializes the dot-notation path name and resolves the feature id\n      // the user has specified.\n      const featureId = this.panel.geoIdPath.split('.').reduce((obj, key) => obj[key], feature);\n      if (featureId in keyedSeries) {\n        const series = keyedSeries[featureId];\n        series.datapoints.forEach((point) => {\n          const time = point[1];\n          const val = point[0];\n          feature.properties['f-' + time] = val;\n        });\n      }\n    });\n\n    if (this.geo && this.map) {\n      console.log('adding geojson source...');\n      this.map.map.addSource('geo', {\n        type: 'geojson',\n        data: this.geo\n      });\n    }\n    else {\n      console.log('not adding source because no map');\n    }\n  }\n\n\n  updateRamp() { // dc :: data characteristics (dc{timeValues, min, max})\n    const dc = this.dataCharacteristics;\n    if (this.panel.colorRamp.codeTo === 'fixed') {\n      this.panel.colorInterpolator = () => { return this.panel.colorRamp.fixedValue; };\n    } else {\n      const inputRange = this.panel.colorRamp.auto ? [dc.min, dc.max] : [this.panel.colorRamp.minValue, this.panel.colorRamp.maxValue];\n      const theRamp = this.opts.colorRamps[this.panel.colorRamp.scaleName];\n      // console.log('color ramp name: ', this.panel.colorRamp.scaleName);\n      // console.log('color ramp: ', theRamp);\n      this.panel.colorInterpolator = d3.scaleSequential().domain(inputRange).interpolator(theRamp);\n    }\n\n    if (this.panel.sizeRamp.codeTo === 'fixed') {\n      this.panel.sizeInterpolator = () => { return this.panel.sizeRamp.fixedValue; };\n    } else {\n      const px = this.panel.sizeRamp.auto ? [dc.min, dc.max] : [this.panel.sizeRamp.minValue, this.panel.sizeRamp.maxValue];\n      const py = [this.panel.sizeRamp.min, this.panel.sizeRamp.max];\n      this.panel.sizeInterpolator = (val) => { return py[0] + (((val - px[0]) * (py[1] - py[0])) / (px[1] - px[0])); };\n    }\n  }\n\n\n/* eslint class-methods-use-this: 0 */\n  link(scope, elem, attrs, ctrl) {\n    mapRenderer(scope, elem, attrs, ctrl);\n  }\n}\n\nGeoLoopCtrl.templateUrl = 'module.html';\n"]}