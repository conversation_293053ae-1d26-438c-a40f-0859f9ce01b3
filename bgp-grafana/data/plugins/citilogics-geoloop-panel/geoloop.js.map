{"version": 3, "sources": ["../src/geoloop.js"], "names": ["moment", "mapboxgl", "d3", "GeoLoop", "ctrl", "mapContainer", "createMap", "frames", "currentFrameIndex", "animation", "console", "log", "mapCenterLonLat", "parseFloat", "panel", "mapCenterLongitude", "mapCenterLatitude", "accessToken", "mbApi<PERSON>ey", "map", "Map", "container", "style", "mapStyle", "center", "zoom", "initialZoom", "interactive", "userInteractionEnabled", "legend", "data", "needToRedrawFrames", "stopAnimation", "clearFrames", "createFrames", "startAnimation", "for<PERSON>ach", "item", "<PERSON><PERSON><PERSON>er", "dataCharacteristics", "timeValues", "geo", "isSourceLoaded", "createFramesSafely", "setTimeout", "sizeIsDynamic", "sizeRamp", "codeTo", "colorIsDynamic", "colorRamp", "featureType", "renderType", "layerType", "opts", "layerTypes", "sizeStops", "colorStops", "minInput", "maxInput", "auto", "min", "max", "minValue", "maxValue", "nStops", "iStop", "stop", "push", "colorInterpolator", "time", "frameName", "pp", "geoFilter", "duration", "property", "type", "stops", "fixedValue", "add<PERSON><PERSON>er", "id", "source", "paint", "filter", "slider", "select", "attr", "length", "setInterval", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "old<PERSON><PERSON><PERSON>", "newFrame", "opacitySelectors", "selector", "setPaintProperty", "tstamp", "timeStr", "unix", "format", "text", "resize", "panTo", "mapCenterMoved", "zoomFactor", "setZoom", "parseInt", "remove"], "mappings": ";;;;;;;;;;;;;;;AACOA,Y;;AACAC,c;;AACKC,Q;;;;;;;;;;;;;;;;;;;;;AAGSC,a;AACnB,yBAAYC,IAAZ,EAAkBC,YAAlB,EAAgC;AAAA;;AAC9B,eAAKD,IAAL,GAAYA,IAAZ;AACA,eAAKC,YAAL,GAAoBA,YAApB;AACA,eAAKC,SAAL;AACA,eAAKC,MAAL,GAAc,EAAd,CAJ8B,CAIZ;AAClB,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKC,SAAL,GAAiB,EAAjB;AACD;;;;sCAEW;AACVC,oBAAQC,GAAR,CAAY,gBAAZ;AACA,gBAAMC,kBAAkB,CAACC,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBC,kBAA3B,CAAD,EAAiDF,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBE,iBAA3B,CAAjD,CAAxB;AACAf,qBAASgB,WAAT,GAAuB,KAAKb,IAAL,CAAUU,KAAV,CAAgBI,QAAvC;AACA,iBAAKC,GAAL,GAAW,IAAIlB,SAASmB,GAAb,CAAiB;AAC1BC,yBAAW,KAAKhB,YADU;AAE1BiB,qBAAO,4BAA4B,KAAKlB,IAAL,CAAUU,KAAV,CAAgBS,QAFzB;AAG1BC,sBAAQZ,eAHkB;AAI1Ba,oBAAMZ,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBY,WAA3B,CAJoB;AAK1BC,2BAAa,KAAKvB,IAAL,CAAUU,KAAV,CAAgBc;AALH,aAAjB,CAAX;AAOD;;;yCAEc;AACb,iBAAKC,MAAL,GAAc,EAAd;AACD;;;+CAEoB;AACnB,iBAAKA,MAAL,GAAc,EAAd;AACA,mBAAO,IAAP;AACD;;;4CAEiB;AAChB,gBAAMC,OAAO,KAAK1B,IAAL,CAAU0B,IAAvB;AACA,gBAAI,KAAKC,kBAAL,CAAwBD,IAAxB,CAAJ,EAAmC;AACjC,mBAAKE,aAAL;AACA,mBAAKC,WAAL;AACA,mBAAKC,YAAL,CAAkBJ,IAAlB;AACA,mBAAKK,cAAL;AACD;AACF;;;wCAEa;AAAA;;AACZ,iBAAK5B,MAAL,CAAY6B,OAAZ,CAAoB,UAACC,IAAD,EAAU;AAC5B,oBAAKlB,GAAL,CAASmB,WAAT,CAAqB,OAAOD,IAA5B;AACD,aAFD;AAGA,iBAAK9B,MAAL,GAAc,EAAd;AACD;;;yCAEc;AAAA;;AACb,gBAAI,CAAC,KAAKH,IAAL,CAAUmC,mBAAV,CAA8BC,UAAnC,EAA+C;AAC7C9B,sBAAQC,GAAR,CAAY,sBAAZ;AACA;AACD;;AAED,gBAAI,CAAC,KAAKP,IAAL,CAAUqC,GAAf,EAAoB;AAClB/B,sBAAQC,GAAR,CAAY,aAAZ;AACA;AACD;;AAED,gBAAI,KAAKQ,GAAL,CAASuB,cAAT,CAAwB,KAAxB,CAAJ,EAAoC;AAClC,mBAAKC,kBAAL;AACD,aAFD,MAEO;AACL;AACA;AACA;AACA;AACAC,yBAAW,YAAM;AACf;AACA,oBAAI,OAAKzB,GAAL,CAASuB,cAAT,CAAwB,KAAxB,CAAJ,EAAoC;AAClC,yBAAKC,kBAAL;AACD,iBAFD,MAEO;AACLjC,0BAAQC,GAAR,CAAY,4CAAZ;AACD;AACF,eAPD,EAOG,IAPH;AAQD;AACF;;;+CAEoB;AAAA;;AACnB,gBAAMkC,gBAAiB,KAAKzC,IAAL,CAAUU,KAAV,CAAgBgC,QAAhB,CAAyBC,MAAzB,KAAoC,aAA3D;AACA,gBAAMC,iBAAkB,KAAK5C,IAAL,CAAUU,KAAV,CAAgBmC,SAAhB,CAA0BF,MAA1B,KAAqC,aAA7D;AACA,gBAAMG,cAAc,KAAK9C,IAAL,CAAUU,KAAV,CAAgBqC,UAApC;AACA,gBAAMC,YAAY,KAAKhD,IAAL,CAAUiD,IAAV,CAAeC,UAAf,CAA0BJ,WAA1B,CAAlB;AACA,gBAAIK,YAAY,CAAC,CAAC,CAAD,EAAI,CAAJ,CAAD,EAAS,CAAC,GAAD,EAAM,EAAN,CAAT,CAAhB;AACA,gBAAMC,aAAa,EAAnB;;AAEA,gBAAIX,aAAJ,EAAmB;AACjB;AACA,kBAAIY,WAAW,CAAf;AACA,kBAAIC,WAAW,CAAf;AACA,kBAAI,KAAKtD,IAAL,CAAUU,KAAV,CAAgBgC,QAAhB,CAAyBa,IAA7B,EAAmC;AACjCF,2BAAW,KAAKrD,IAAL,CAAUmC,mBAAV,CAA8BqB,GAAzC;AACAF,2BAAW,KAAKtD,IAAL,CAAUmC,mBAAV,CAA8BsB,GAAzC;AACD,eAHD,MAGO;AACLJ,2BAAW5C,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBgC,QAAhB,CAAyBgB,QAApC,CAAX;AACAJ,2BAAW7C,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBgC,QAAhB,CAAyBiB,QAApC,CAAX;AACD;;AAEDR,0BAAY,CAAC,CAACE,QAAD,EAAW5C,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBgC,QAAhB,CAAyBc,GAApC,CAAX,CAAD,EAAuD,CAACF,QAAD,EAAW7C,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBgC,QAAhB,CAAyBe,GAApC,CAAX,CAAvD,CAAZ;AACA;AACD;;AAED,gBAAIb,cAAJ,EAAoB;AAClB;AACA,kBAAIS,YAAW,CAAf;AACA,kBAAIC,YAAW,CAAf;AACA,kBAAI,KAAKtD,IAAL,CAAUU,KAAV,CAAgBmC,SAAhB,CAA0BU,IAA9B,EAAoC;AAClCF,4BAAW,KAAKrD,IAAL,CAAUmC,mBAAV,CAA8BqB,GAAzC;AACAF,4BAAW,KAAKtD,IAAL,CAAUmC,mBAAV,CAA8BsB,GAAzC;AACD,eAHD,MAGO;AACLJ,4BAAW5C,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBmC,SAAhB,CAA0Ba,QAArC,CAAX;AACAJ,4BAAW7C,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBmC,SAAhB,CAA0Bc,QAArC,CAAX;AACD;;AAED,kBAAMC,SAAS,EAAf;;AAEA,mBAAK,IAAIC,QAAQ,CAAjB,EAAoBA,SAASD,MAA7B,EAAqCC,SAAS,CAA9C,EAAiD;AAC/C,oBAAMC,OAAOT,YAAaQ,QAAQD,MAAT,IAAoBN,YAAWD,SAA/B,CAAzB;AACAD,2BAAWW,IAAX,CAAgB,CAACD,IAAD,EAAO,KAAK9D,IAAL,CAAUU,KAAV,CAAgBsD,iBAAhB,CAAkCF,IAAlC,CAAP,CAAhB;AACD;;AAED;AACD;;AAED,iBAAK9D,IAAL,CAAUmC,mBAAV,CAA8BC,UAA9B,CAAyCJ,OAAzC,CAAiD,UAACiC,IAAD,EAAU;AACzD,kBAAMC,YAAY,OAAOD,IAAzB;AACA;AACA,kBAAME,KAAK,EAAX,CAHyD,CAG1C;AACf,kBAAIC,YAAY,EAAhB;AACA,kBAAItB,gBAAgB,MAApB,EAA4B;AAC1BsB,4BAAY,CAAC,IAAD,EAAO,OAAP,EAAgB,YAAhB,CAAZ;AACAD,mBAAG,cAAH,IAAqB,CAArB;AACAA,mBAAG,yBAAH,IAAgC,EAAEE,UAAU,CAAZ,EAAhC;AACAF,mBAAG,YAAH,IAAmB1B,gBAAgB;AACjC6B,4BAAUJ,SADuB;AAEjCK,wBAAM,aAF2B;AAGjCC,yBAAOrB;AAH0B,iBAAhB,GAIf1C,WAAW,OAAKT,IAAL,CAAUU,KAAV,CAAgBgC,QAAhB,CAAyB+B,UAApC,CAJJ;AAKAN,mBAAG,YAAH,IAAmBvB,iBAAiB;AAClC0B,4BAAUJ,SADwB;AAElCK,wBAAM,aAF4B;AAGlCC,yBAAOpB;AAH2B,iBAAjB,GAIf3C,WAAW,OAAKT,IAAL,CAAUU,KAAV,CAAgBmC,SAAhB,CAA0B4B,UAArC,CAJJ;AAKD,eAdD,MAcO,IAAI3B,gBAAgB,OAApB,EAA6B;AAClCsB,4BAAY,CAAC,IAAD,EAAO,OAAP,EAAgB,OAAhB,CAAZ;AACAD,mBAAG,gBAAH,IAAuB,CAAvB;AACAA,mBAAG,2BAAH,IAAkC,EAAEE,UAAU,CAAZ,EAAlC;AACAF,mBAAG,eAAH,IAAsB1B,gBAAgB;AACpC6B,4BAAUJ,SAD0B;AAEpCK,wBAAM,aAF8B;AAGpCC,yBAAOrB;AAH6B,iBAAhB,GAIlB1C,WAAW,OAAKT,IAAL,CAAUU,KAAV,CAAgBgC,QAAhB,CAAyB+B,UAApC,CAJJ;AAKAN,mBAAG,cAAH,IAAqBvB,iBAAiB;AACpC0B,4BAAUJ,SAD0B;AAEpCK,wBAAM,aAF8B;AAGpCC,yBAAOpB;AAH6B,iBAAjB,GAIjB,OAAKpD,IAAL,CAAUU,KAAV,CAAgBmC,SAAhB,CAA0B4B,UAJ9B;AAKD,eAdM,MAcA,IAAI3B,gBAAgB,SAApB,EAA+B;AACpCsB,4BAAY,CAAC,IAAD,EAAO,OAAP,EAAgB,SAAhB,CAAZ;AACAD,mBAAG,cAAH,IAAqB,CAArB;AACAA,mBAAG,yBAAH,IAAgC,EAAEE,UAAU,CAAZ,EAAhC;AACAF,mBAAG,YAAH,IAAmBvB,iBAAiB;AAClC0B,4BAAUJ,SADwB;AAElCK,wBAAM,aAF4B;AAGlCC,yBAAOpB;AAH2B,iBAAjB,GAIf,OAAKpD,IAAL,CAAUU,KAAV,CAAgBmC,SAAhB,CAA0B4B,UAJ9B;AAKD;;AAED,qBAAK1D,GAAL,CAAS2D,QAAT,CAAkB;AAChBC,oBAAI,OAAOV,IADK;AAEhBM,sBAAMvB,SAFU;AAGhB4B,wBAAQ,KAHQ;AAIhBC,uBAAOV,EAJS;AAKhBW,wBAAQV;AALQ,eAAlB;;AAQA,qBAAKjE,MAAL,CAAY4D,IAAZ,CAAiBE,IAAjB;AACD,aArDD;;AAuDA;AACA,gBAAMc,SAASjF,GAAGkF,MAAH,CAAU,UAAU,KAAKhF,IAAL,CAAUU,KAAV,CAAgBiE,EAA1B,GAA+B,SAAzC,EACZM,IADY,CACP,KADO,EACA,CADA,EAEZA,IAFY,CAEP,KAFO,EAEA,KAAK9E,MAAL,CAAY+E,MAFZ,CAAf;AAID;;;2CAEgB;AAAA;;AACf,gBAAI,KAAK7E,SAAT,EAAoB;AAClB,mBAAKuB,aAAL;AACD;;AAED,iBAAKvB,SAAL,GAAiB8E,YAAY,YAAM;AACjC,qBAAKC,SAAL;AACD,aAFgB,EAEd,GAFc,CAAjB;AAGD;;;0CAEe;AACdC,0BAAc,KAAKhF,SAAnB;AACA,iBAAKA,SAAL,GAAiB,IAAjB;AACD;;;sCAEW;AACV,gBAAI,CAAC,KAAKU,GAAV,EAAe;AACb;AACD;AACD,gBAAI,KAAKZ,MAAL,CAAY+E,MAAZ,KAAuB,CAA3B,EAA8B;AAC5B;AACA;AACD;AACD,gBAAMI,WAAW,OAAO,KAAKnF,MAAL,CAAY,KAAKC,iBAAjB,CAAxB;AACA,iBAAKA,iBAAL,IAA0B,CAA1B;AACA,gBAAI,KAAKA,iBAAL,IAA0B,KAAKD,MAAL,CAAY+E,MAA1C,EAAkD;AAChD,mBAAK9E,iBAAL,GAAyB,CAAzB;AACD;AACD,gBAAMmF,WAAW,OAAO,KAAKpF,MAAL,CAAY,KAAKC,iBAAjB,CAAxB;;AAEA,gBAAMoF,mBAAmB;AACvB,uBAAS,gBADc;AAEvB,yBAAW,cAFY;AAGvB,sBAAQ;AAHe,aAAzB;AAKA,gBAAMC,WAAWD,iBAAiB,KAAKxF,IAAL,CAAUU,KAAV,CAAgBqC,UAAjC,CAAjB;;AAEA,iBAAKhC,GAAL,CAAS2E,gBAAT,CAA0BH,QAA1B,EAAoCE,QAApC,EAA8C,CAA9C;AACA,iBAAK1E,GAAL,CAAS2E,gBAAT,CAA0BJ,QAA1B,EAAoCG,QAApC,EAA8C,CAA9C;AACA,gBAAME,SAAS,KAAKxF,MAAL,CAAY,KAAKC,iBAAjB,IAAsC,GAArD;AACA,gBAAMwF,UAAUhG,OAAOiG,IAAP,CAAYF,MAAZ,EAAoBG,MAApB,CAA2B,qBAA3B,CAAhB;AACA;;AAEA;AACAhG,eAAGkF,MAAH,CAAU,UAAU,KAAKhF,IAAL,CAAUU,KAAV,CAAgBiE,EAA1B,GAA+B,OAAzC,EAAkDoB,IAAlD,CAAuDH,OAAvD;AACA;AACA9F,eAAGkF,MAAH,CAAU,UAAU,KAAKhF,IAAL,CAAUU,KAAV,CAAgBiE,EAA1B,GAA+B,SAAzC,EAAoDL,QAApD,CAA6D,OAA7D,EAAsE,KAAKlE,iBAA3E;AACD;;;mCAEQ;AACP,iBAAKW,GAAL,CAASiF,MAAT;AACD;;;2CAEgB;AACf,iBAAKjF,GAAL,CAASkF,KAAT,CAAe,CAACxF,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBC,kBAA3B,CAAD,EAAiDF,WAAW,KAAKT,IAAL,CAAUU,KAAV,CAAgBE,iBAA3B,CAAjD,CAAf;AACA,iBAAKZ,IAAL,CAAUkG,cAAV,GAA2B,KAA3B;AACD;;;kCAEOC,U,EAAY;AAClB,iBAAKpF,GAAL,CAASqF,OAAT,CAAiBC,SAASF,UAAT,EAAqB,EAArB,CAAjB;AACD;;;mCAEQ;AACP,gBAAI,KAAKpF,GAAT,EAAc;AACZ,mBAAKA,GAAL,CAASuF,MAAT;AACD;AACD,iBAAKvF,GAAL,GAAW,IAAX;AACD;;;;;;yBA7PkBhB,O", "file": "geoloop.js", "sourcesContent": ["/* eslint-disable id-length, no-unused-vars */\nimport moment from 'moment';\nimport mapboxgl from './libs/mapbox-gl';\nimport * as d3 from './libs/d3';\n/* eslint-disable id-length, no-unused-vars */\n\nexport default class GeoLoop {\n  constructor(ctrl, mapContainer) {\n    this.ctrl = ctrl;\n    this.mapContainer = mapContainer;\n    this.createMap();\n    this.frames = []; // list of timestamps\n    this.currentFrameIndex = 0;\n    this.animation = {};\n  }\n\n  createMap() {\n    console.log('rebuilding map');\n    const mapCenterLonLat = [parseFloat(this.ctrl.panel.mapCenterLongitude), parseFloat(this.ctrl.panel.mapCenterLatitude)];\n    mapboxgl.accessToken = this.ctrl.panel.mbApiKey;\n    this.map = new mapboxgl.Map({\n      container: this.mapContainer,\n      style: 'mapbox://styles/mapbox/' + this.ctrl.panel.mapStyle,\n      center: mapCenterLonLat,\n      zoom: parseFloat(this.ctrl.panel.initialZoom),\n      interactive: this.ctrl.panel.userInteractionEnabled\n    });\n  }\n\n  createLegend() {\n    this.legend = {};\n  }\n\n  needToRedrawFrames() {\n    this.legend = {};\n    return true;\n  }\n\n  drawLayerFrames() {\n    const data = this.ctrl.data;\n    if (this.needToRedrawFrames(data)) {\n      this.stopAnimation();\n      this.clearFrames();\n      this.createFrames(data);\n      this.startAnimation();\n    }\n  }\n\n  clearFrames() {\n    this.frames.forEach((item) => {\n      this.map.removeLayer('f-' + item);\n    });\n    this.frames = [];\n  }\n\n  createFrames() {\n    if (!this.ctrl.dataCharacteristics.timeValues) {\n      console.log('no series to display');\n      return;\n    }\n\n    if (!this.ctrl.geo) {\n      console.log('no geo data');\n      return;\n    }\n\n    if (this.map.isSourceLoaded('geo')) {\n      this.createFramesSafely();\n    } else {\n      // console.log('no geo source in map. maybe not loaded?');\n      // this is stupid to use setTimeout.\n      // but mapbox doesn't seem to have a on-source-loaded event that reliably works\n      // for this purpose.\n      setTimeout(() => {\n        // console.log('waited for layer to load.');\n        if (this.map.isSourceLoaded('geo')) {\n          this.createFramesSafely();\n        } else {\n          console.log('still no geo source. try refresh manually?');\n        }\n      }, 1000);\n    }\n  }\n\n  createFramesSafely() {\n    const sizeIsDynamic = (this.ctrl.panel.sizeRamp.codeTo === 'measurement');\n    const colorIsDynamic = (this.ctrl.panel.colorRamp.codeTo === 'measurement');\n    const featureType = this.ctrl.panel.renderType;\n    const layerType = this.ctrl.opts.layerTypes[featureType];\n    let sizeStops = [[0, 1], [100, 10]];\n    const colorStops = [];\n\n    if (sizeIsDynamic) {\n      // populate the sizeStops array with the input/output values\n      let minInput = 0;\n      let maxInput = 1;\n      if (this.ctrl.panel.sizeRamp.auto) {\n        minInput = this.ctrl.dataCharacteristics.min;\n        maxInput = this.ctrl.dataCharacteristics.max;\n      } else {\n        minInput = parseFloat(this.ctrl.panel.sizeRamp.minValue);\n        maxInput = parseFloat(this.ctrl.panel.sizeRamp.maxValue);\n      }\n\n      sizeStops = [[minInput, parseFloat(this.ctrl.panel.sizeRamp.min)], [maxInput, parseFloat(this.ctrl.panel.sizeRamp.max)]];\n      // console.log('size stops: ', sizeStops);\n    }\n\n    if (colorIsDynamic) {\n      // populate the sizeStops array with the input/output values\n      let minInput = 0;\n      let maxInput = 1;\n      if (this.ctrl.panel.colorRamp.auto) {\n        minInput = this.ctrl.dataCharacteristics.min;\n        maxInput = this.ctrl.dataCharacteristics.max;\n      } else {\n        minInput = parseFloat(this.ctrl.panel.colorRamp.minValue);\n        maxInput = parseFloat(this.ctrl.panel.colorRamp.maxValue);\n      }\n\n      const nStops = 25;\n\n      for (let iStop = 0; iStop <= nStops; iStop += 1) {\n        const stop = minInput + ((iStop / nStops) * (maxInput - minInput));\n        colorStops.push([stop, this.ctrl.panel.colorInterpolator(stop)]);\n      }\n\n      // console.log('color stops: ', colorStops);\n    }\n\n    this.ctrl.dataCharacteristics.timeValues.forEach((time) => {\n      const frameName = 'f-' + time;\n      // create new map layer for this animation frame (name is the time code)\n      const pp = {}; // paint properties\n      let geoFilter = [];\n      if (featureType === 'line') {\n        geoFilter = ['==', '$type', 'LineString'];\n        pp['line-opacity'] = 0;\n        pp['line-opacity-transition'] = { duration: 0 };\n        pp['line-width'] = sizeIsDynamic ? {\n          property: frameName,\n          type: 'exponential',\n          stops: sizeStops\n        } : parseFloat(this.ctrl.panel.sizeRamp.fixedValue);\n        pp['line-color'] = colorIsDynamic ? {\n          property: frameName,\n          type: 'exponential',\n          stops: colorStops\n        } : parseFloat(this.ctrl.panel.colorRamp.fixedValue);\n      } else if (featureType === 'point') {\n        geoFilter = ['==', '$type', 'Point'];\n        pp['circle-opacity'] = 0;\n        pp['circle-opacity-transition'] = { duration: 0 };\n        pp['circle-radius'] = sizeIsDynamic ? {\n          property: frameName,\n          type: 'exponential',\n          stops: sizeStops\n        } : parseFloat(this.ctrl.panel.sizeRamp.fixedValue);\n        pp['circle-color'] = colorIsDynamic ? {\n          property: frameName,\n          type: 'exponential',\n          stops: colorStops\n        } : this.ctrl.panel.colorRamp.fixedValue;\n      } else if (featureType === 'polygon') {\n        geoFilter = ['==', '$type', 'Polygon'];\n        pp['fill-opacity'] = 0;\n        pp['fill-opacity-transition'] = { duration: 0 };\n        pp['fill-color'] = colorIsDynamic ? {\n          property: frameName,\n          type: 'exponential',\n          stops: colorStops\n        } : this.ctrl.panel.colorRamp.fixedValue;\n      }\n\n      this.map.addLayer({\n        id: 'f-' + time,\n        type: layerType,\n        source: 'geo',\n        paint: pp,\n        filter: geoFilter,\n      });\n\n      this.frames.push(time);\n    });\n\n    // get slider component, set min/max/value\n    const slider = d3.select('#map_' + this.ctrl.panel.id + '_slider')\n      .attr('min', 0)\n      .attr('max', this.frames.length);\n\n  }\n\n  startAnimation() {\n    if (this.animation) {\n      this.stopAnimation();\n    }\n\n    this.animation = setInterval(() => {\n      this.stepFrame();\n    }, 200);\n  }\n\n  stopAnimation() {\n    clearInterval(this.animation);\n    this.animation = null;\n  }\n\n  stepFrame() {\n    if (!this.map) {\n      return;\n    }\n    if (this.frames.length === 0) {\n      // console.log('skipping animation: no frames');\n      return;\n    }\n    const oldFrame = 'f-' + this.frames[this.currentFrameIndex];\n    this.currentFrameIndex += 1;\n    if (this.currentFrameIndex >= this.frames.length) {\n      this.currentFrameIndex = 0;\n    }\n    const newFrame = 'f-' + this.frames[this.currentFrameIndex];\n\n    const opacitySelectors = {\n      'point': 'circle-opacity',\n      'polygon': 'fill-opacity',\n      'line': 'line-opacity'\n    };\n    const selector = opacitySelectors[this.ctrl.panel.renderType];\n\n    this.map.setPaintProperty(newFrame, selector, 1);\n    this.map.setPaintProperty(oldFrame, selector, 0);\n    const tstamp = this.frames[this.currentFrameIndex] / 1e3;\n    const timeStr = moment.unix(tstamp).format('YYYY-MM-DD HH:mm:ss');\n    // console.log('time is ', timeStr);\n\n    // set time string in legend\n    d3.select('#map_' + this.ctrl.panel.id + '_date').text(timeStr);\n    // set slider position to indicate time-location\n    d3.select('#map_' + this.ctrl.panel.id + '_slider').property('value', this.currentFrameIndex);\n  }\n\n  resize() {\n    this.map.resize();\n  }\n\n  panToMapCenter() {\n    this.map.panTo([parseFloat(this.ctrl.panel.mapCenterLongitude), parseFloat(this.ctrl.panel.mapCenterLatitude)]);\n    this.ctrl.mapCenterMoved = false;\n  }\n\n  setZoom(zoomFactor) {\n    this.map.setZoom(parseInt(zoomFactor, 10));\n  }\n\n  remove() {\n    if (this.map) {\n      this.map.remove();\n    }\n    this.map = null;\n  }\n}\n"]}