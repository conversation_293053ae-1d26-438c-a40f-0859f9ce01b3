{"version": 3, "sources": ["../src/data_formatter.js"], "names": ["DataFormatter", "ctrl", "kbn", "timeValues", "Set", "min", "max", "series", "length", "point", "for<PERSON>ach", "stats", "datapoints", "pt", "add", "dc", "Array", "from", "sort", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACqBA,mB;AACnB,+BAAYC,IAAZ,EAAkBC,GAAlB,EAAuB;AAAA;;AACrB,eAAKD,IAAL,GAAYA,IAAZ;AACA,eAAKC,GAAL,GAAWA,GAAX;AACD;;;;+CAEoB;AACnB,gBAAMC,aAAa,IAAIC,GAAJ,EAAnB;AACA,gBAAIC,MAAM,CAAV;AACA,gBAAIC,MAAM,CAAV;;AAEA,gBAAI,KAAKL,IAAL,CAAUM,MAAV,IAAoB,KAAKN,IAAL,CAAUM,MAAV,CAAiBC,MAAjB,GAA0B,CAA9C,IAAmD,KAAKP,IAAL,CAAUM,MAAV,CAAiB,CAAjB,EAAoBC,MAApB,KAA+B,CAAtF,EAAyF;AACvF,kBAAMC,QAAQ,KAAKR,IAAL,CAAUM,MAAV,CAAiB,CAAjB,EAAoB,CAApB,CAAd;AACAF,oBAAMI,KAAN;AACAH,oBAAMG,KAAN;AACD;;AAED,gBAAI,KAAKR,IAAL,CAAUM,MAAV,IAAoB,KAAKN,IAAL,CAAUM,MAAV,CAAiBC,MAAjB,GAA0B,CAAlD,EAAqD;AACnD,mBAAKP,IAAL,CAAUM,MAAV,CAAiBG,OAAjB,CAAyB,UAACH,MAAD,EAAY;AACnCF,sBAAOE,OAAOI,KAAP,CAAaN,GAAb,GAAmBA,GAApB,GAA2BE,OAAOI,KAAP,CAAaN,GAAxC,GAA8CA,GAApD;AACAC,sBAAOC,OAAOI,KAAP,CAAaL,GAAb,GAAmBA,GAApB,GAA2BC,OAAOI,KAAP,CAAaL,GAAxC,GAA8CA,GAApD;AACAC,uBAAOK,UAAP,CAAkBF,OAAlB,CAA0B,UAACG,EAAD,EAAQ;AAChCV,6BAAWW,GAAX,CAAeD,GAAG,CAAH,CAAf;AACD,iBAFD;AAGD,eAND;AAOD;AACD,gBAAME,KAAK;AACTZ,0BAAYa,MAAMC,IAAN,CAAWd,UAAX,EAAuBe,IAAvB,EADH;AAETb,mBAAKA,GAFI;AAGTC,mBAAKA;AAHI,aAAX;;AAMAa,oBAAQC,GAAR,CAAY,wBAAZ,EAAsCL,EAAtC;AACA,mBAAOA,EAAP;AACD;;;;;;yBAlCkBf,a", "file": "data_formatter.js", "sourcesContent": ["\nexport default class DataFormatter {\n  constructor(ctrl, kbn) {\n    this.ctrl = ctrl;\n    this.kbn = kbn;\n  }\n\n  getCharacteristics() {\n    const timeValues = new Set();\n    let min = 0;\n    let max = 0;\n\n    if (this.ctrl.series && this.ctrl.series.length > 0 && this.ctrl.series[0].length === 2) {\n      const point = this.ctrl.series[0][0];\n      min = point;\n      max = point;\n    }\n\n    if (this.ctrl.series && this.ctrl.series.length > 0) {\n      this.ctrl.series.forEach((series) => {\n        min = (series.stats.min < min) ? series.stats.min : min;\n        max = (series.stats.max > max) ? series.stats.max : max;\n        series.datapoints.forEach((pt) => {\n          timeValues.add(pt[1]);\n        });\n      });\n    }\n    const dc = {\n      timeValues: Array.from(timeValues).sort(),\n      min: min,\n      max: max\n    };\n\n    console.log('data characteristics: ', dc);\n    return dc;\n  }\n}\n"]}