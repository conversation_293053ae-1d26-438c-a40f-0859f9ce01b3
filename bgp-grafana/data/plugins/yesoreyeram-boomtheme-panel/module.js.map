{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///external \"react\"", "webpack:///external \"@grafana/ui\"", "webpack:///../node_modules/react-style-tag/es/constants.js", "webpack:///../node_modules/prop-types/index.js", "webpack:///external \"react-dom\"", "webpack:///external \"@grafana/data\"", "webpack:///../node_modules/cssbeautify/cssbeautify.js", "webpack:///../node_modules/stylis/stylis.js", "webpack:///../node_modules/prop-types/factoryWithThrowingShims.js", "webpack:///../node_modules/prop-types/lib/ReactPropTypesSecret.js", "webpack:///../node_modules/process/browser.js", "webpack:///../node_modules/tslib/tslib.es6.js", "webpack:///../node_modules/react-parm/es/utils.js", "webpack:///../node_modules/react-parm/es/index.js", "webpack:///../node_modules/react-style-tag/es/blob.js", "webpack:///../node_modules/react-style-tag/es/options.js", "webpack:///../node_modules/react-style-tag/es/styles.js", "webpack:///../node_modules/react-style-tag/es/Style.js", "webpack:///./BoomThemeStyle.ts", "webpack:///./config.ts", "webpack:///./utils.ts", "webpack:///./BoomTheme.ts", "webpack:///./editors/ThemePicker.tsx", "webpack:///./editors/ThemeEditor.tsx", "webpack:///./editors/ThemesEditor.tsx", "webpack:///./module.ts", "webpack:///./Panel.tsx"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "__WEBPACK_EXTERNAL_MODULE__0__", "__WEBPACK_EXTERNAL_MODULE__1__", "BEAUTIFY_OPTIONS", "autosemicolon", "indent", "IS_PRODUCTION", "process", "env", "_React$version$split$", "version", "split", "slice", "map", "Number", "REACT_MAJOR_VERSION", "REACT_MINOR_VERSION", "HAS_UNSAFE_METHODS", "__WEBPACK_EXTERNAL_MODULE__4__", "__WEBPACK_EXTERNAL_MODULE__5__", "style", "opt", "options", "blocks", "ch", "ch2", "str", "state", "State", "depth", "quote", "comment", "trimRight", "index", "length", "formatted", "openbracesuffix", "isWhitespace", "isQuote", "isName", "indexOf", "appendIndent", "openBlock", "closeBlock", "last", "char<PERSON>t", "push", "arguments", "openbrace", "String", "replace", "Start", "AtRule", "Block", "Selector", "Ruleset", "Property", "Separator", "Expression", "URL", "charCodeAt", "substr", "join", "factory", "nullptn", "formatptn", "colonptn", "cursorptn", "transformptn", "animationptn", "propertiesptn", "elementptn", "selectorptn", "andptn", "escapeptn", "invalidptn", "keyframeptn", "plcholdrptn", "readonlyptn", "beforeptn", "afterptn", "tailptn", "whiteptn", "pseudoptn", "writingptn", "supportsptn", "propertyptn", "selfptn", "pseudofmt", "dimensionptn", "imgsrcptn", "webkit", "moz", "ms", "column", "line", "pattern", "cascade", "prefix", "escape", "compress", "semicolon", "preserve", "array", "plugins", "plugged", "should", "unkwn", "keyed", "nscopealt", "nscope", "compile", "parent", "current", "body", "id", "selector", "result", "bracket", "parentheses", "first", "second", "code", "tail", "trail", "peak", "counter", "context", "at<PERSON>le", "pseudo", "caret", "format", "insert", "invert", "eof", "eol", "char", "chars", "child", "out", "children", "flat", "trim", "delimited", "substring", "proxy", "select", "supports", "vendor", "padding", "element", "Array", "elements", "j", "size", "isolate", "selectors", "scope", "k", "level", "input", "third", "cache", "hash", "declare", "list", "items", "isNaN", "parseFloat", "animation", "test", "content", "match", "group", "parents", "at", "next", "stylis", "set", "this", "constructor", "output", "minify", "use", "plugin", "ReactPropTypesSecret", "emptyFunction", "emptyFunctionWithReset", "resetWarningCache", "shim", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "err", "Error", "getShim", "isRequired", "ReactPropTypes", "bool", "func", "number", "string", "symbol", "any", "arrayOf", "elementType", "instanceOf", "node", "objectOf", "oneOf", "oneOfType", "shape", "exact", "checkPropTypes", "PropTypes", "cachedSetTimeout", "cachedClearTimeout", "defaultSetTimout", "defaultClearTimeout", "runTimeout", "fun", "setTimeout", "e", "clearTimeout", "currentQueue", "queue", "draining", "queueIndex", "cleanUpNextTick", "concat", "drainQueue", "timeout", "len", "run", "marker", "runClearTimeout", "<PERSON><PERSON>", "noop", "nextTick", "args", "apply", "title", "browser", "argv", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "binding", "cwd", "chdir", "dir", "umask", "__read", "iterator", "ar", "done", "error", "bindSetState", "instance", "setState", "Component", "createRefCreator", "ref", "component", "logInvalidInstanceError", "createElementRef", "method", "_len", "extraArgs", "_key", "_len2", "_key2", "getUrl", "defaultObject", "window", "webkitURL", "reset", "hasBlobSupport", "support", "Blob", "createObjectURL", "createGetCachedLinkHref", "href", "currentStyle", "type", "getLinkHref", "GLOBAL_OPTIONS", "hasSourceMap", "isCompressed", "isMinified", "isPrefixed", "getCoalescedOption", "option", "rawStyle", "_ref", "global", "keyframe", "_extends", "assign", "target", "source", "componentDidMount", "relocateNode", "componentWillUpdate", "_ref2", "returnNode", "componentDidUpdate", "_ref3", "_ref4", "getStyleForState", "previousProps", "componentWillUnmount", "_ref5", "_ref6", "_ref7", "document", "originalParent", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "head", "append<PERSON><PERSON><PERSON>", "_ref8", "willUpdateMethod", "_PureComponent", "Style", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_classCallCheck", "_this", "self", "ReferenceError", "_possibleConstructorReturn", "linkHref", "getCachedLinkHref", "subClass", "superClass", "writable", "configurable", "setPrototypeOf", "__proto__", "_inherits", "render", "_props", "obj", "keys", "createElement", "rel", "propTypes", "setGlobalOptions", "for<PERSON>ach", "BoomThemeStyleProps", "CONFIG", "DARK", "index_id", "DEFAULT", "LIGHT", "BASE_THEME", "BG_IMAGE", "NONE", "STYLE", "PANEL_CONTAINER_BG_COLOR", "toLowerCase", "BaseTheme", "theme", "CustomStyle", "text", "ExternalURL", "BackgroundImage", "url", "color", "getThemeCSSFile", "fileName", "performance", "appFiles", "getEntries", "filter", "endsWith", "getActiveThemeName", "themes", "styles", "addStyle", "deleteStyle", "splice", "constructTheme", "getThemeContent", "textAlign", "themeName", "className", "marginLeft", "marginRight", "onClick", "onChange", "onViewChange", "editorVisibility", "setEditorVisibility", "styleType", "onTitleChange", "onStylePropertyChange", "propertyName", "replaceValue", "defaultThemes", "label", "justifyContent", "isOpen", "on<PERSON><PERSON><PERSON>", "css", "currentTarget", "baseTheme", "find", "rows", "PanelBackground", "ThemesEditorOptions", "path", "category", "editor", "onThemeChange", "updatedTheme", "deleteTheme", "newTheme", "replaceVariables", "runTimeThemeState", "setRunTimeThemeState", "runTimeTheme", "setRunTimeTheme", "themeOptions", "activeTheme", "disableThemePicker", "setPanelOptions", "builder", "addCustomEditor", "addBooleanSwitch", "addTextInput", "setMigrationHandler", "panel", "newOptions", "activeThemeId"], "mappings": ";iGACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QA0Df,OArDAF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,IAIjBlC,EAAoBA,EAAoBmC,EAAI,I,gBClFrDhC,EAAOD,QAAUkC,G,cCAjBjC,EAAOD,QAAUmC,G,8BCAjB,sIAKWC,EAAmB,CAC5BC,eAAe,EACfC,OAAQ,MAMCC,KAAmBC,IAAWA,EAAQC,KAE7CC,EAAwB,IAAMC,QAAQC,MAAM,KAAKC,MAAM,EAAG,GAAGC,IAAIC,QACjEC,EAAsBN,EAAsB,GAC5CO,EAAsBP,EAAsB,GAOrCQ,EAAqBF,EAAsB,IAA8B,KAAxBA,GAA8BC,GAAuB,I,kCCP/GhD,EAAOD,QAAU,EAAQ,EAAR,I,cCjBnBC,EAAOD,QAAUmD,G,cCAjBlD,EAAOD,QAAUoD,G,iBC6BhB,WAEG,aA+aInD,EAAOD,QA7aX,SAAqBqD,EAAOC,GAExB,IAAIC,EAA2CC,EAC3CC,EAAIC,EAAKC,EAAKC,EAAOC,EAAOC,EAAOC,EAAOC,EAG1CC,EAJSC,EAAQ,EAAGC,EAASd,EAAMc,OAAgBC,EAAY,GAE/DC,GAAkB,EAClBhC,GAAgB,EAcpB,SAASiC,EAAa/D,GAClB,MAAc,MAANA,GAAqB,OAANA,GAAsB,OAANA,GAAsB,OAANA,GAAsB,OAANA,EAG3E,SAASgE,EAAQhE,GACb,MAAc,MAANA,GAAsB,MAANA,EAI5B,SAASiE,EAAOjE,GACZ,OAAQkD,GAAM,KAAOA,GAAM,KACtBA,GAAM,KAAOA,GAAM,KACnBA,GAAM,KAAOA,GAAM,KACpB,WAAWgB,QAAQlE,IAAM,EAGjC,SAASmE,IACL,IAAIxE,EACJ,IAAKA,EAAI4D,EAAO5D,EAAI,EAAGA,GAAK,EACxBkE,GAAab,EAAQjB,OAI7B,SAASqC,IACLP,EAAYH,EAAUG,GAClBC,EACAD,GAAa,MAEbA,GAAa,KACbM,IACAN,GAAa,KAEL,OAARV,IACAU,GAAa,MAEjBN,GAAS,EAGb,SAASc,IACL,IAAIC,EACJf,GAAS,GACTM,EAAYH,EAAUG,IAERD,OAAS,GAAK9B,GAEX,OADbwC,EAAOT,EAAUU,OAAOV,EAAUD,OAAS,KACd,MAATU,IAChBT,GAAa,KAIrBA,GAAa,KACbM,IACAN,GAAa,IACbZ,EAAOuB,KAAKX,GACZA,EAAY,GAkChB,SAlG8B,KAD9Bb,EAAUyB,UAAUb,OAAS,EAAIb,EAAM,IACpBhB,SACfiB,EAAQjB,OAAS,QAEY,iBAAtBiB,EAAQ0B,YACfZ,EAAyC,gBAAtBd,EAAQ0B,WAEM,kBAA1B1B,EAAQlB,gBACfA,EAAgBkB,EAAQlB,eA6DxB4B,EADAiB,OAAOpD,UAAUmC,UACL,SAAUhC,GAClB,OAAOA,EAAEgC,aAID,SAAUhC,GAClB,OAAOA,EAAEkD,QAAQ,OAAQ,KAgBjCrB,EAAQ,EACRF,GAbAC,EAAQ,CACJuB,MAAO,EACPC,OAAQ,EACRC,MAAO,EACPC,SAAU,EACVC,QAAS,EACTC,SAAU,EACVC,UAAW,EACXC,WAAY,EACZC,IAAK,IAIKR,MACdpB,GAAU,EACVR,EAAS,GAGTH,EAAQA,EAAM8B,QAAQ,QAAS,MAExBjB,EAAQC,GAMX,GALAV,EAAKJ,EAAMyB,OAAOZ,GAClBR,EAAML,EAAMyB,OAAOZ,EAAQ,GAC3BA,GAAS,EAGLK,EAAQR,GACRK,GAAaX,EACTA,IAAOM,IACPA,EAAQ,MAED,OAAPN,GAAeC,IAAQK,IAEvBK,GAAaV,EACbQ,GAAS,QAMjB,GAAIK,EAAQd,GACRW,GAAaX,EACbM,EAAQN,OAKZ,GAAIO,EACAI,GAAaX,EACF,MAAPA,GAAsB,MAARC,IACdM,GAAU,EACVI,GAAaV,EACbQ,GAAS,QAIjB,GAAW,MAAPT,GAAsB,MAARC,EAAlB,CAQA,GAAIE,IAAUC,EAAMuB,MAAO,CAEvB,GAAsB,IAAlB5B,EAAOW,QACHG,EAAab,IAA4B,IAArBW,EAAUD,OAC9B,SAKR,GAAIV,GAAM,KAAOA,EAAGoC,WAAW,IAAM,IAAK,CACtCjC,EAAQC,EAAMuB,MACdhB,GAAaX,EACb,SAIJ,GAAIe,EAAOf,IAAe,MAAPA,EAAa,CAK5B,GAAmB,KAFnBE,EAAMM,EAAUG,IAERD,OAIAX,EAAOW,OAAS,IAChBC,EAAY,aAKhB,GAAmC,MAA/BT,EAAImB,OAAOnB,EAAIQ,OAAS,IACW,MAA/BR,EAAImB,OAAOnB,EAAIQ,OAAS,GAE5BC,EAAYT,EAAM,YAIlB,KAEgB,OADZD,EAAMU,EAAUU,OAAOV,EAAUD,OAAS,KACD,IAAtBT,EAAImC,WAAW,IAGlCzB,EAAYA,EAAU0B,OAAO,EAAG1B,EAAUD,OAAS,GAI/DC,GAAaX,EACbG,EAAgB,MAAPH,EAAcI,EAAMwB,OAASxB,EAAM0B,SAC5C,UAIR,GAAI3B,IAAUC,EAAMwB,OAqBpB,GAAIzB,IAAUC,EAAMyB,MAiDpB,GAAI1B,IAAUC,EAAM0B,SAoBpB,GAAI3B,IAAUC,EAAM2B,QAgCpB,GAAI5B,IAAUC,EAAM4B,SA2BpB,GAAI7B,IAAUC,EAAM6B,UAiBpB,GAAI9B,IAAUC,EAAM8B,WAoChB/B,IAAUC,EAAM+B,KAIL,MAAPnC,GAAcW,EAAUU,OAAOV,EAAUD,OAAS,IAAM,OACxDC,GAAaX,EACbG,EAAQC,EAAM8B,YAOtBvB,GAAaX,MAjDb,CAGI,GAAW,MAAPA,EAAY,CACZmB,IACAhB,EAAQC,EAAMuB,MACVtB,EAAQ,IACRF,EAAQC,EAAMyB,OAElB,SAIJ,GAAW,MAAP7B,EAAY,CACZW,EAAYH,EAAUG,GACtBA,GAAa,MACbR,EAAQC,EAAM2B,QACd,SAKJ,GAFApB,GAAaX,EAEF,MAAPA,GAC+C,MAA3CW,EAAUU,OAAOV,EAAUD,OAAS,IACW,MAA3CC,EAAUU,OAAOV,EAAUD,OAAS,IACO,MAA3CC,EAAUU,OAAOV,EAAUD,OAAS,GAAY,CAGpDP,EAAQC,EAAM+B,IACd,cA9CZ,CAGI,IAAKtB,EAAab,GAAK,CACnBW,GAAaX,EACbG,EAAQC,EAAM8B,WACd,SAIApB,EAAQb,KACRE,EAAQC,EAAM8B,gBAtCtB,CAGI,GAAW,MAAPlC,EAAY,CACZW,EAAYH,EAAUG,GACtBA,GAAa,KACbR,EAAQC,EAAM8B,WACVrB,EAAaZ,KACbE,EAAQC,EAAM6B,WAElB,SAIJ,GAAW,MAAPjC,EAAY,CACZmB,IACAhB,EAAQC,EAAMuB,MACVtB,EAAQ,IACRF,EAAQC,EAAMyB,OAElB,SAGJlB,GAAaX,MAvDjB,CAGI,GAAW,MAAPA,EAAY,CACZmB,IACAhB,EAAQC,EAAMuB,MACVtB,EAAQ,IACRF,EAAQC,EAAMyB,OAElB,SAIJ,GAAW,OAAP7B,EAAa,CACbW,EAAYH,EAAUG,GACtBA,GAAa,KACb,SAIJ,IAAKE,EAAab,GAAK,CACnBW,EAAYH,EAAUG,GACtBA,GAAa,KACbM,IACAN,GAAaX,EACbG,EAAQC,EAAM4B,SACd,SAEJrB,GAAaX,MAhDjB,CAGI,GAAW,MAAPA,EAAY,CACZkB,IACAf,EAAQC,EAAM2B,QACd,SAIJ,GAAW,MAAP/B,EAAY,CACZmB,IACAhB,EAAQC,EAAMuB,MACd,SAGJhB,GAAaX,MAjEjB,CAGI,GAAIe,EAAOf,GAAK,CAKZ,GAAmB,KAFnBE,EAAMM,EAAUG,IAERD,OAIAX,EAAOW,OAAS,IAChBC,EAAY,aAIhB,GAAmC,MAA/BT,EAAImB,OAAOnB,EAAIQ,OAAS,GACxBC,EAAYT,EAAM,YAIlB,KAEgB,OADZD,EAAMU,EAAUU,OAAOV,EAAUD,OAAS,KACD,IAAtBT,EAAImC,WAAW,IAGlCzB,EAAYA,EAAU0B,OAAO,EAAG1B,EAAUD,OAAS,GAK/DO,IACAN,GAAaX,EACbG,EAAQC,EAAM0B,SACd,SAIJ,GAAW,MAAP9B,EAAY,CACZmB,IACAhB,EAAQC,EAAMuB,MACd,SAGJhB,GAAaX,MAlEjB,CAGI,GAAW,MAAPA,EAAY,CACZW,GAAaX,EACbG,EAAQC,EAAMuB,MACd,SAIJ,GAAW,MAAP3B,EAAY,CACZE,EAAMM,EAAUG,GAChBO,IACAf,EAAiB,eAARD,EAAwBE,EAAM2B,QAAU3B,EAAMyB,MACvD,SAGJlB,GAAaX,QA7EbO,GAAU,EACVI,GAAaX,EACbW,GAAaV,EACbQ,GAAS,EAqRjB,OAFAE,EAAYZ,EAAOuC,KAAK,IAAM3B,GA1atC,I,gBClBiEnE,EAAgB,QAGrD,SAAS+F,EAASzC,GAE7C,aAuCA,IAAI0C,EAAU,QACVC,EAAY,YACZC,EAAW,OACXC,EAAY,UACZC,EAAe,sBACfC,EAAe,qBACfC,EAAgB,qBAChBC,EAAa,YACbC,EAAc,SACdC,EAAS,oBACTC,EAAY,sDACZC,EAAa,OACbC,EAAc,qBACdC,EAAc,aACdC,EAAc,gBACdC,EAAY,oBACZC,EAAW,gBACXC,EAAU,qBACVC,EAAW,UACXC,EAAY,iBACZC,EAAa,qBAEbC,EAAc,kBACdC,EAAc,eACdC,EAAU,eACVC,EAAY,8BAEZC,EAAe,mCACfC,EAAY,sBAGZC,EAAS,WACTC,EAAM,QACNC,EAAK,OA0CLC,EAAS,EACTC,EAAO,EACPC,EAAU,EAEVC,EAAU,EACVC,EAAS,EACTC,EAAS,EACTC,EAAW,EACXC,EAAY,EACZC,EAAW,EAGXC,EAAQ,GAGRC,EAAU,GACVC,EAAU,EACVC,EAAS,KAWTC,EAAQ,EAGRC,EAAQ,EACRpH,EAAM,GAGNqH,EAAY,GACZC,EAAS,GAYb,SAASC,EAASC,EAAQC,EAASC,EAAMC,EAAItF,GAmC5C,IAlCA,IA8BIuF,EACAC,EA/BAC,EAAU,EACVvF,EAAU,EACVwF,EAAc,EACdzF,EAAQ,EAER0F,EAAQ,EACRC,EAAS,EACTC,EAAO,EACPC,EAAO,EACPC,EAAQ,EACRC,EAAO,EAEPC,EAAU,EACVC,EAAU,EACVC,EAAS,EACTC,EAAS,EACTC,EAAQ,EACRC,EAAS,EACTC,EAAS,EACTC,EAAS,EACTnG,EAAS,EACToG,EAAMpB,EAAKhF,OACXqG,GAAMD,EAAM,EAEZE,GAAO,GACPC,GAAQ,GACRC,GAAQ,GACRC,GAAM,GACNC,GAAW,GACXC,GAAO,GAKJX,EAAQI,GAAK,CAiBnB,GAhBAZ,EAAOR,EAAKtD,WAAWsE,GAGnBA,IAAUK,IAETxG,EAAUD,EAAQyF,EAAcD,IAAY,IAC/B,IAAZvF,IACH2F,EA9Ga,KA8GN3F,EA3HE,GAaI,IAiHdD,EAAQyF,EAAcD,EAAU,EAChCgB,IACAC,MAIExG,EAAUD,EAAQyF,EAAcD,IAAY,EAAG,CAElD,GAAIY,IAAUK,KACTJ,EAAS,IACZM,GAAQA,GAAMvF,QAAQe,EAAW,KAG9BwE,GAAMK,OAAO5G,OAAS,GAAG,CAC5B,OAAQwF,GACP,KAzIM,GA0IN,KA5II,EA6IJ,KAtJU,GAuJV,KA/IS,GAgJT,KAjJQ,GAkJP,MAED,QACCe,IAASvB,EAAKrE,OAAOqF,GAIvBR,EAhKW,GAqKb,GAAe,IAAXU,EACH,OAAQV,GAEP,KAtKY,IAuKZ,KAxKa,IAyKb,KA1KW,GA2KX,KAxJa,GAyJb,KA1Ja,GA2Jb,KA1KiB,GA2KjB,KA1KkB,GA2KlB,KA/JO,GAgKNU,EAAS,EAGV,KA1KK,EA2KL,KA5KU,GA6KV,KA9KS,GA+KT,KA3KO,GA4KN,MAGD,QAOC,IANAA,EAAS,EACTlG,EAASgG,EACTV,EAAQE,EACRQ,IACAR,EA/LU,GAiMHxF,EAASoG,GACf,OAAQpB,EAAKtD,WAAW1B,MACvB,KA5LM,GA6LN,KA5LO,GA6LP,KArMQ,KAsMLgG,EACFR,EAAOF,EACPtF,EAASoG,EACT,MAED,KA1LI,GA2LCH,EAAS,MACVD,EACFR,EAAOF,GAGT,KA/MS,IAgNRtF,EAASoG,GASf,OAAQZ,GACP,KA1Na,IAgOZ,IAJAF,GADAiB,GAAQA,GAAMK,QACAlF,WAAW,GACzBkE,EAAU,EACV5F,IAAWgG,EAEJA,EAAQI,GAAK,CACnB,OAAQZ,EAAOR,EAAKtD,WAAWsE,IAC9B,KAlOU,IAmOTJ,IACA,MAED,KAvOW,IAwOVA,IACA,MAED,KAxNW,GAyNV,OAAQL,EAASP,EAAKtD,WAAWsE,EAAQ,IAExC,KAhOE,GAiOF,KA5NS,GA6NRA,EAAQa,GAAUtB,EAAQS,EAAOK,GAAKrB,GAGxC,MAGD,KAlPW,GAmPVQ,IAGD,KAxPe,GAyPdA,IAID,KA7OW,GA8OX,KA/OW,GAgPV,KAAOQ,IAAUK,IACZrB,EAAKtD,WAAWsE,KAAWR,KAOlC,GAAgB,IAAZI,EACH,MAGDI,IASD,OANAQ,GAAQxB,EAAK8B,UAAU9G,EAAQgG,GAzPzB,IA2PFV,IACHA,GAASiB,GAAQA,GAAMvF,QAAQc,EAAS,IAAI8E,QAAQlF,WAAW,IAGxD4D,GAEP,KA/QG,GAsRF,OANIW,EAAS,IACZM,GAAQA,GAAMvF,QAAQe,EAAW,KAGlCwD,EAASgB,GAAM7E,WAAW,IAGzB,KA7PO,IA8PP,KApQI,IAqQJ,KApQO,IAqQP,KAvRG,GAwRFwD,EAAWH,EACX,MAED,QACCG,EAAWb,EA0Bb,GArBArE,GADAwG,GAAQ3B,EAAQE,EAASG,EAAUsB,GAAOjB,EAAQ5F,EAAM,IACzCK,OAGXoE,EAAW,GAAgB,IAAXpE,IACnBA,EAASuG,GAAMvG,QAIZuE,EAAU,IAEbY,EAAS4B,GAxPL,EAwPkBP,GADtBtB,EAAW8B,EAAO3C,EAAOkC,GAAOJ,GACOpB,EAASlB,EAAMD,EAAQ5D,EAAQuF,EAAQ5F,EAAOsF,GACrFsB,GAAQrB,EAAStD,KAAK,SAEP,IAAXuD,GAC+C,KAA7CnF,GAAUwG,GAAQrB,EAAOyB,QAAQ5G,UACrCuF,EAAS,EACTiB,GAAQ,KAKPxG,EAAS,EACZ,OAAQuF,GACP,KAtSM,IAuSLgB,GAAQA,GAAMvF,QAAQmC,EAAa8D,IAEpC,KApSM,IAqSN,KA3SG,IA4SH,KA7TE,GA8TDT,GAAQD,GAAQ,IAAMC,GAAQ,IAC9B,MAED,KAjTM,IAmTLA,IADAD,GAAQA,GAAMvF,QAAQ0B,EAAa,SAAWgC,EAAQ,EAAIpH,EAAM,MAChD,IAAMkJ,GAAQ,IAG7BA,GADc,IAAXxC,GAA4B,IAAXA,GAAgBkD,GAAO,IAAIV,GAAO,GAC9C,IAAM/C,EAAS+C,GAAQ,IAAMA,GAE7B,IAAMA,GAEf,MAED,QACCA,GAAQD,GAAQC,GArTf,MAuTGvB,IACMwB,IAAOD,GAAhBA,GAAuB,SAK1BA,GAAQ,GAGT,MAGD,QACCA,GAAQ3B,EAAQE,EAASiC,EAAOjC,EAASwB,GAAOJ,GAASK,GAAOvB,EAAItF,EAAM,GAI5E+G,IAAYF,GAGZX,EAAU,EACVK,EAAS,EACTH,EAAS,EACTE,EAAS,EACTE,EAAS,EACTL,EAAS,EACTS,GAAQ,GACRC,GAAQ,GACRhB,EAAOR,EAAKtD,aAAasE,GACzB,MAED,KA1Xc,IA2Xd,KA5XY,GA+XX,IAAKhG,GAFLuG,IAASN,EAAS,EAAIM,GAAMvF,QAAQe,EAAW,IAAMwE,IAAOK,QAExC5G,QAAU,EAuB7B,OArBe,IAAX+F,IApXC,MAqXJT,EAAQiB,GAAM7E,WAAW,KAGF4D,EAAQ,IAAMA,EAAQ,OAC5CtF,GAAUuG,GAAQA,GAAMvF,QAAQ,IAAK,MAAMhB,QAKzCuE,EAAU,QACoF,KAA5FY,EAAS4B,GA9UT,EA8UsBR,GAAOxB,EAASD,EAAQjB,EAAMD,EAAQ6C,GAAIzG,OAAQiF,EAAItF,EAAOsF,KACrC,KAA7CjF,GAAUuG,GAAQpB,EAAOyB,QAAQ5G,UACrCuG,GAAQ,QAKXjB,EAAQiB,GAAM7E,WAAW,GACzB6D,EAASgB,GAAM7E,WAAW,GAElB4D,GACP,KA/XI,EAgYH,MAED,KAhZE,GAiZD,GAzXK,MAyXDC,GAxXE,KAwXmBA,EAAoB,CAC5CoB,IAAQJ,GAAQvB,EAAKrE,OAAOqF,GAC5B,MAGF,QACC,GAhZI,KAgZAO,GAAM7E,WAAW1B,EAAO,GAC3B,MAGDyG,IAAO/I,EAAS6I,GAAOjB,EAAOC,EAAQgB,GAAM7E,WAAW,IAM1DmE,EAAU,EACVK,EAAS,EACTH,EAAS,EACTE,EAAS,EACTE,EAAS,EACTI,GAAQ,GACRf,EAAOR,EAAKtD,aAAasE,IAO5B,OAAQR,GACP,KAjbY,GAkbZ,KAnbW,GAqbV,GAAI3F,EAAUD,EAAQyF,EAAcD,EAAUjB,IAAc,EAG3D,OAAQwB,GACP,KA5biB,GA6bjB,KA/aY,GAgbZ,KA/aY,GAgbZ,KAzbG,GA0bH,KA7aM,IA8aN,KAhbY,GAibZ,KAvbK,GAwbL,KAjbK,GAkbL,KApbY,GAqbZ,KA5bK,GA6bL,KAzbM,GA0bN,KA3bM,GA4bN,KA5cU,GA6cV,KA3cW,IA4cX,KA7cY,IA8cX,MAED,QAEKI,EAAS,IACZG,EAAS,GAhcC,KAucVrG,EACHA,EAAU,EACAkE,EAAU8B,IAAY,GAhctB,MAgc2BZ,GAAmBsB,GAAMvG,OAAS,IACvEiG,EAAS,EACTM,IAAS,MAINhC,EAAUE,EAAQ,GACrBsC,GAvaO,EAuaMR,GAAOxB,EAASD,EAAQjB,EAAMD,EAAQ6C,GAAIzG,OAAQiF,EAAItF,EAAOsF,GAI3ErB,EAAS,EACTC,IACA,MAED,KA5ea,GA6eb,KA5ee,IA6ed,GAAIhE,EAAUD,EAAQyF,EAAcD,IAAY,EAAG,CAClDxB,IACA,MAGF,QAQC,OANAA,IAGA0C,GAAOtB,EAAKrE,OAAOqF,GAGXR,GACP,KAnfK,EAofL,KAlfO,GAmfN,GAAI5F,EAAQwF,EAAUvF,IAAY,EACjC,OAAQ4F,GACP,KAhfI,GAifJ,KAhfI,GAifJ,KAzfE,EA0fF,KAxfI,GAyfHa,GAAO,GACP,MAED,QA5fI,KA6fCd,IACHc,GAAO,KAKX,MAGD,KAzfM,EA0fLA,GAAO,MACP,MAED,KA5fU,GA6fTA,GAAO,MACP,MAED,KA/fa,GAggBZA,GAAO,MACP,MAGD,KAlhBK,GAohBA1G,EAAQC,EAAUuF,IAAY,GAAKrB,EAAU,IAChDoC,EAAS,EACTF,EAAS,EACTK,GAAO,KAAOA,IAEf,MAID,KAAK,IACJ,GAAI1G,EAAQC,EAAUuF,EAAUtB,IAAY,GAAKiC,EAAS,EACzD,OAAQC,EAAQD,GAEf,KAAK,EA7gBK,MA8gBLN,GA7hBD,KA6hByBT,EAAKtD,WAAWsE,EAAM,KACjDlC,EAAU2B,GAIZ,KAAK,EAlhBE,MAmhBFC,IACH5B,EAAU4B,GAKd,MAGD,KA5iBO,GA6iBF9F,EAAQC,EAAUuF,IAAY,IACjCW,EAASC,GAEV,MAGD,KApjBO,GAqjBFnG,EAAUwF,EAAczF,EAAQwF,IAAY,IAC/Ca,EAAS,EACTK,IAAQ,MAET,MAGD,KAzjBa,GA0jBb,KA3jBa,GA4jBI,IAAZzG,IACHD,EAAQA,IAAU4F,EAAO,EAAe,IAAV5F,EAAc4F,EAAO5F,GAEpD,MAGD,KA/kBa,GAglBRA,EAAQC,EAAUwF,IAAgB,GACrCD,IAED,MAED,KAplBc,GAqlBTxF,EAAQC,EAAUwF,IAAgB,GACrCD,IAED,MAGD,KA7lBkB,GA8lBbxF,EAAQC,EAAUuF,IAAY,GACjCC,IAED,MAED,KApmBiB,GAqmBhB,GAAIzF,EAAQC,EAAUuF,IAAY,EAAG,CACpC,GAAgB,IAAZS,EACH,OAAa,EAALJ,EAAe,EAANC,GAEhB,KAAK,IACJ,MAGD,QACCE,EAAU,EACVC,EAAU,EAKbR,IAED,MAED,KAjnBI,GAknBCxF,EAAUwF,EAAczF,EAAQwF,EAAUW,EAASD,IAAW,IACjEA,EAAS,GAEV,MAGD,KAnnBM,GAonBN,KA/mBa,GAgnBZ,GAAIlG,EAAQwF,EAAUC,EAAc,EACnC,MAGD,OAAQxF,GAEP,KAAK,EACJ,OAAa,EAAL2F,EAAkC,EAAzBR,EAAKtD,WAAWsE,EAAM,IAEtC,KAAK,IACJnG,EA1nBQ,GA2nBR,MAGD,KAAK,IACJG,EAASgG,EACTnG,EAroBC,GAyoBH,MAGD,KA5oBI,GAKO,KAwoBN2F,GA7oBD,KA6oByBC,GAAiBzF,EAAS,IAAMgG,IAEzB,KAA9BhB,EAAKtD,WAAW1B,EAAO,KAC1ByG,IAAOzB,EAAK8B,UAAU9G,EAAQgG,EAAM,IAErCM,GAAO,GACPzG,EAAU,IAQf,GAAgB,IAAZA,EAAe,CAGlB,GAAIkE,EAAUnE,EAAQwF,EAAUU,IAAW,GAhpBjC,MAgpBsCb,GA7qBrC,KA6qBwDO,EAClE,OAAQA,GACP,KA/pBK,GAgqBL,KAzpBK,IA0pBL,KA5pBW,GA6pBX,KA5pBI,GA6pBJ,KA/qBgB,GAgrBhB,KAjrBe,GAkrBd,GAAgB,IAAZK,EAAe,CAElB,OAAQJ,GACP,KA/qBA,EAgrBA,KA9qBE,GA+qBF,KAnrBI,GAorBJ,KAnrBK,GAorBJa,IAAc,KACd,MAED,QACCA,GAAO,KAAOA,IAhrBb,KAgrBqBd,EAAiB,GAAK,MAG9CS,EAAS,OAGT,OAAQT,GACP,KApsBY,GAssBPO,EAAS,IAAMC,GAAkB,MAATP,IAC3BM,EAAS,GAEVF,IAAYD,EACZ,MAED,KA3sBa,GA4sBkB,IAAzBC,IAAYD,KAChBK,EAAS,EACTK,IAAQ,MAMZ,MAED,KAjtBG,EAktBH,KAhtBK,GAitBJ,OAAQb,GACP,KArsBE,EAssBF,KA5tBQ,IA6tBR,KA9tBS,IA+tBT,KAhuBO,GAiuBP,KAjtBG,GAktBH,KAzsBM,GA0sBN,KA1tBC,EA2tBD,KAztBG,GA0tBH,KA9tBK,GA+tBL,KA9tBM,GA+tBL,MAED,QAEiB,IAAZI,IACHI,EAAS,EACTK,IAAQ,OASdC,IAASD,GA3uBF,KA8uBHd,GAhvBC,IAgvBiBA,IACrBG,EAAOH,IAOXE,EAAQD,EACRA,EAAOD,EAGPQ,IAcD,GAXAhG,EAASyG,GAAIzG,OAGRoE,EAAW,GACC,IAAXpE,GAAoC,IAApB0G,GAAS1G,QAAuC,IAAtB+E,EAAQ,GAAG/E,QAAkB,IA9uBlE,MA+uBJiF,GAAoC,IAAnBF,EAAQ/E,SAAiB+D,EAAU,EAAIY,EAAYC,KAAYG,EAAQ,MAC5F/E,EAAS+E,EAAQnD,KAAK,KAAK5B,OAAS,GAKnCA,EAAS,EAAG,CAKf,GAHAkF,EAAuB,IAAZnB,GAxvBE,MAwvBekB,EAue9B,SAAkBF,GACjB,IAAK,IAA8DoC,EAASC,EAAnErL,EAAI,EAAGiE,EAAS+E,EAAQ/E,OAAQkF,EAAWmC,MAAMrH,GAA2BjE,EAAIiE,IAAUjE,EAAG,CAKrG,IAHA,IAAIuL,EAAWvC,EAAQhJ,GAAG0C,MAAM4D,GAC5BoE,EAAM,GAEDc,EAAI,EAAGC,EAAO,EAAG/B,EAAO,EAAGD,EAAO,EAAGxJ,EAAIsL,EAAStH,OAAQuH,EAAIvL,IAAKuL,EAE3E,KAAgD,KAA3CC,GAAQJ,EAAUE,EAASC,IAAIvH,SAAiBhE,EAAI,GAAzD,CAQA,GAJAyJ,EAAOgB,EAAI/E,WAAW+E,EAAIzG,OAAO,GACjCwF,EAAO4B,EAAQ1F,WAAW,GAC1ByF,EAAU,GAEA,IAANI,EAEH,OAAQ9B,GACP,KAhwCM,GAiwCN,KAzvCO,IA0vCP,KA5vCa,GA6vCb,KA5vCM,GA6vCN,KAxwCO,GAywCP,KAjxCiB,GAkxChB,MAED,QACC0B,EAAU,IAKb,OAAQ3B,GACP,KAlxCM,GAmxCL4B,EAAUD,EAAUxC,EAErB,KA1wCQ,IA2wCR,KA7wCc,GA8wCd,KA7wCO,GA8wCP,KAzxCQ,GA0xCR,KAjyCmB,GAkyCnB,KAnyCkB,GAoyCjB,MAED,KApyCc,GAqyCbyC,EAAUD,EAAUC,EAAUzC,EAC9B,MAED,KA5xCQ,GA6xCP,OAA8B,EAAtByC,EAAQ1F,WAAW,GAA6B,EAAtB0F,EAAQ1F,WAAW,IAEpD,KAAK,IACJ,GAAIuC,EAAS,EAAG,CACfmD,EAAUD,EAAUC,EAAQN,UAAU,EAAGU,EAAO,GAChD,MAIF,SACKD,EAAI,GAAKD,EAASC,EAAE,GAAGvH,OAAS,KACnCoH,EAAUD,EAAUxC,EAAYyC,GAInC,MAED,KA/yCQ,GAgzCPD,EAAU,GAEX,QAEEC,EADGI,EAAO,GAAKJ,EAAQ9G,QAAQ,KAAO,EAC5B6G,EAAUC,EAAQpG,QAAQiC,EAAW,KAAO0B,EAAY,MAExDwC,EAAUC,EAAUzC,EAKjC8B,GAAOW,EAGRlC,EAASnJ,GAAK0K,EAAIzF,QAAQe,EAAW,IAAI6E,OAG1C,OAAO1B,EA5jBwCuC,CAAQ1C,GAAWA,EAG7DR,EAAU,QAGE,KAFfY,EAAS4B,GA1tBA,EA0tBaN,GAAKvB,EAAUJ,EAAQjB,EAAMD,EAAQ5D,EAAQiF,EAAItF,EAAOsF,KAE3B,KAAzBwB,GAAMtB,GAAQnF,OACvC,OAAO2G,GAAOF,GAAMC,GAMtB,GAFAD,GAAMvB,EAAStD,KAAK,KAAO,IAAM6E,GAAM,IAEnCzC,EAAOF,GAAY,EAAG,CAIzB,OAHe,IAAXE,GAAiBkD,GAAOT,GAAK,KAChC3C,EAAU,GAEHA,GAEP,KAvwBW,IAwwBV2C,GAAMA,GAAIzF,QAAQ4B,EAAa,YAAc6D,GAC7C,MAGD,KA7wBc,IA8wBbA,GACCA,GAAIzF,QAAQ2B,EAAa,KAAOc,EAAS,YACzCgD,GAAIzF,QAAQ2B,EAAa,aACzB8D,GAAIzF,QAAQ2B,EAAa,iBAAyB8D,GAMrD3C,EAAU,GAIZ,OAAO6C,GAAOF,GAAMC,GAWrB,SAASM,EAAQlC,EAAQC,EAASoB,GACjC,IAAIuB,EAAY3C,EAAQ6B,OAAOnI,MAAM6D,GACjCmE,EAAMiB,EAEN1H,EAAS0H,EAAU1H,OACnBhE,EAAI8I,EAAO9E,OAEf,OAAQhE,GAEP,KAAK,EACL,KAAK,EACJ,IAAK,IAAID,EAAI,EAAGmJ,EAAiB,IAANlJ,EAAU,GAAK8I,EAAO,GAAK,IAAK/I,EAAIiE,IAAUjE,EACxE0K,EAAI1K,GAAK4L,EAAMzC,EAAUuB,EAAI1K,GAAIoK,EAAQnK,GAAG4K,OAE7C,MAGD,QACU7K,EAAI,EAAR,IAAWwL,EAAI,EAApB,IAAuBd,EAAM,GAAI1K,EAAIiE,IAAUjE,EAC9C,IAAK,IAAI6L,EAAI,EAAGA,EAAI5L,IAAK4L,EACxBnB,EAAIc,KAAOI,EAAM7C,EAAO8C,GAAK,IAAKF,EAAU3L,GAAIoK,EAAQnK,GAAG4K,OAM/D,OAAOH,EAYR,SAASkB,EAAO7C,EAAQC,EAASoB,EAAQ0B,GACxC,IAAI3C,EAAWH,EACXS,EAAON,EAASxD,WAAW,GAO/B,OAJI8D,EAAO,KACVA,GAAQN,EAAWA,EAAS0B,QAAQlF,WAAW,IAGxC8D,GAEP,KA32BQ,GA42BP,OAAQzB,EAAU8D,GACjB,KAAK,EACL,KAAK,EACJ,GAA6B,IAAzB/C,EAAO8B,OAAO5G,OACjB,MAGF,QACC,OAAOkF,EAASlE,QAAQuB,EAAQ,KAAKuC,EAAO8B,QAG9C,MAGD,KAr3BU,GAs3BT,OAAQ1B,EAASxD,WAAW,IAE3B,KAAK,IACJ,GAAIuC,EAAS,GAAKF,EAAU,EAC3B,OAAOmB,EAASlE,QAAQwB,EAAW,MAAMxB,QAAQuB,EAAQ,KAAKqC,GAE/D,MAED,QAEC,OAAOE,EAAO8B,OAAS1B,EAASlE,QAAQuB,EAAQ,KAAKuC,EAAO8B,QAI/D,QAEC,GAAIT,EAAOpC,EAAU,GAAKmB,EAAS5E,QAAQ,MAAQ,EAClD,OAAO4E,EAASlE,QAAQuB,GAv4BhB,KAu4ByBuC,EAAOpD,WAAW,GAAe,GAAK,MAAMoD,EAAO8B,QAKvF,OAAO9B,EAASI,EAYjB,SAASxH,EAAUoK,EAAOxC,EAAOC,EAAQwC,GACxC,IAGIC,EAHAjI,EAAQ,EACR0G,EAAMqB,EAAQ,IACdG,EAAc,EAAN3C,EAAmB,EAAPC,EAAmB,EAANwC,EAIrC,GAAa,MAATE,EACH,OA+PF,SAAoBH,GACnB,IAAI9H,EAAS8H,EAAM9H,OACfD,EAAQ+H,EAAMxH,QAAQ,IAAK,GAAK,EAChC4H,EAAUJ,EAAMhB,UAAU,EAAG/G,GAAO6G,OACpCH,EAAMqB,EAAMhB,UAAU/G,EAAOC,EAAO,GAAG4G,OAE3C,OAAQkB,EAAMpG,WAAW,GAAGgD,GAC3B,KAAK,EACJ,MAGD,KA9qCS,GAgrCR,GAA6B,MAAzBoD,EAAMpG,WAAW,IACpB,MAIF,QAEC,IAAIyG,EAAO1B,EAAIhI,OAAOgI,EAAM,GAAItE,IAEvBpG,EAAI,EAAb,IAAgBgE,EAAQ,EAAGC,EAASmI,EAAKnI,OAAQjE,EAAIiE,EAAQD,EAAQ,IAAKhE,EAAG,CAI5E,IAHA,IAAIiB,EAAQmL,EAAKpM,GACbqM,EAAQpL,EAAMyB,MAAM2D,GAEjBpF,EAAQoL,EAAMrI,IAAQ,CAC5B,IAAI4F,EAAO3I,EAAM0E,WAAW,GAE5B,GAAc,IAAVgD,IAEFiB,EArsCE,IAqsCWA,EAAO,IAAQA,EAAO,IAAMA,EAAO,KAjsCtC,KAisC8CA,GAlsCpD,KAosCJA,GApsCI,KAosCa3I,EAAM0E,WAAW,IAGnC,OAAQ2G,MAAMC,WAAWtL,MAAmC,IAAxBA,EAAMsD,QAAQ,OACjD,KAAK,EACJ,OAAQtD,GAEP,IAAK,WAAY,IAAK,YAAa,IAAK,YAAa,IAAK,UAC1D,IAAK,SAAU,IAAK,WAAY,IAAK,OAAQ,IAAK,OAAQ,IAAK,SAC/D,IAAK,OAAQ,IAAK,UAAW,IAAK,WAAY,IAAK,cACnD,IAAK,SAAU,IAAK,UAAW,IAAK,oBAAqB,IAAK,UAC9D,IAAK,UAAW,IAAK,QAAS,IAAK,aAAc,IAAK,WACrD,MAED,QACCA,GAASM,GAOd8K,EAAMrI,KAAW/C,EAGlByJ,IAAc,IAAN1K,EAAU,GAAK,KAAOqM,EAAMxG,KAAK,MAO5C,OAFA6E,EAAMyB,EAAUzB,EAAM,IAEP,IAAXzC,GAA4B,IAAXA,GAAgBkD,GAAOT,EAAK,GACzChD,EAASgD,EAAMA,EAEhBA,EAnUC8B,CAAU9B,GACX,GAAe,IAAXzC,GAA4B,IAAXA,IAAiBkD,GAAOT,EAAK,GACxD,OAAOA,EAIR,OAAQwB,GAEP,KAAK,KAEJ,OAA8B,KAAvBxB,EAAI/E,WAAW,IAAa+B,EAASgD,EAAMA,EAAMA,EAGzD,KAAK,IAEJ,OAA6B,MAAtBA,EAAI/E,WAAW,GAAa+B,EAASgD,EAAMA,EAAMA,EAGzD,KAAK,IAEJ,OAA6B,MAAtBA,EAAI/E,WAAW,GAAa+B,EAASgD,EAAMA,EAAMA,EAGzD,KAAK,KACJ,GAA0B,MAAtBA,EAAI/E,WAAW,GAClB,MAKF,KAAK,IACL,KAAK,IACJ,OAAO+B,EAASgD,EAAMA,EAGvB,KAAK,IACJ,OAAOhD,EAASgD,EAAM/C,EAAM+C,EAAMA,EAInC,KAAK,KACL,KAAK,IACJ,OAAOhD,EAASgD,EAAM/C,EAAM+C,EAAM9C,EAAK8C,EAAMA,EAG9C,KAAK,IAEJ,OAn9BQ,KAm9BJA,EAAI/E,WAAW,GACX+B,EAASgD,EAAMA,EAInBA,EAAInG,QAAQ,aAAc,IAAM,EAC5BmG,EAAIzF,QAAQwC,EAAW,KAAKC,EAAO,MAAQgD,EAG5CA,EAGR,KAAK,IACJ,GAh+BQ,KAg+BJA,EAAI/E,WAAW,GAClB,OAAQ+E,EAAI/E,WAAW,IAEtB,KAAK,IACJ,OAAO+B,EAAS,OAASgD,EAAIzF,QAAQ,QAAS,IAAMyC,EAASgD,EAAM9C,EAAK8C,EAAIzF,QAAQ,OAAQ,YAAcyF,EAG3G,KAAK,IACJ,OAAOhD,EAASgD,EAAM9C,EAAK8C,EAAIzF,QAAQ,SAAU,YAAcyF,EAGhE,KAAK,GACJ,OAAOhD,EAASgD,EAAM9C,EAAK8C,EAAIzF,QAAQ,QAAS,kBAAoByF,EAKvE,OAAOhD,EAASgD,EAAM9C,EAAK8C,EAAMA,EAGlC,KAAK,IACJ,OAAOhD,EAASgD,EAAM9C,EAAfF,QAAmCgD,EAAMA,EAGjD,KAAK,KAEJ,GAA0B,KAAtBA,EAAI/E,WAAW,GAClB,MAID,OADAsG,EAAQvB,EAAIK,UAAUL,EAAInG,QAAQ,IAAK,KAAKU,QAAQ,QAAS,IAAIA,QAAQ,gBAAiB,WACnFyC,EAAS,WAAauE,EAAQvE,EAASgD,EAAM9C,EAAK,YAAcqE,EAAQvB,EAGhF,KAAK,KACJ,OAAOxE,EAAUuG,KAAK/B,GAAOA,EAAIzF,QAAQgB,EAAU,IAAMyB,GAAUgD,EAAIzF,QAAQgB,EAAU,IAAM0B,GAAO+C,EAAMA,EAG7G,KAAK,IAIJ,OAFA1G,GADAiI,EAAQvB,EAAIK,UAAU,IAAIF,QACZtG,QAAQ,KAAO,EAErB0H,EAAMtG,WAAW,GAAGsG,EAAMtG,WAAW3B,IAE5C,KAAK,IACJiI,EAAQvB,EAAIzF,QAAQkC,EAAY,MAChC,MAGD,KAAK,IACJ8E,EAAQvB,EAAIzF,QAAQkC,EAAY,SAChC,MAGD,KAAK,IACJ8E,EAAQvB,EAAIzF,QAAQkC,EAAY,MAChC,MAED,QACC,OAAOuD,EAIT,OAAOhD,EAASgD,EAAM9C,EAAKqE,EAAQvB,EAGpC,KAAK,KACJ,IAAkC,IAA9BA,EAAInG,QAAQ,SAAU,GACzB,OAAOmG,EAIT,KAAK,IAIJ,OAHA1G,GAAS0G,EAAMqB,GAAO9H,OAAS,GAGvBiI,GAFRD,GAAmC,KAA1BvB,EAAI/E,WAAW3B,GAAgB0G,EAAIK,UAAU,EAAG/G,GAAS0G,GAAKK,UAAUgB,EAAMxH,QAAQ,IAAK,GAAK,GAAGsG,QAEvFlF,WAAW,IAA0B,EAApBsG,EAAMtG,WAAW,KAEtD,KAAK,IAEJ,GAAIsG,EAAMtG,WAAW,GAAK,IACzB,MAIF,KAAK,IACJ+E,EAAMA,EAAIzF,QAAQgH,EAAOvE,EAAOuE,GAAO,IAAIvB,EAC3C,MAID,KAAK,IACL,KAAK,IACJA,EACCA,EAAIzF,QAAQgH,EAAOvE,GAAQwE,EAAO,IAAM,UAAY,IAAI,OAAO,IAC/DxB,EAAIzF,QAAQgH,EAAOvE,EAAOuE,GAAO,IACjCvB,EAAIzF,QAAQgH,EAAOrE,EAAGqE,EAAM,OAAO,IACnCvB,EAKH,OAAOA,EAAM,IAGd,KAAK,IACJ,GA1kCQ,KA0kCJA,EAAI/E,WAAW,GAClB,OAAQ+E,EAAI/E,WAAW,IAEtB,KAAK,IAEJ,OADAsG,EAAQvB,EAAIzF,QAAQ,SAAU,IACvByC,EAASgD,EAAMhD,EAAS,OAASuE,EAAQrE,EAAK,QAAUqE,EAAQvB,EAGxE,KAAK,IACJ,OAAOhD,EAASgD,EAAM9C,EAAK,aAAe8C,EAAIzF,QAAQqC,EAAS,IAAMoD,EAGtE,QACC,OAAOhD,EAASgD,EAAM9C,EAAK,iBAAmB8C,EAAIzF,QAAQ,gBAAiB,IAAIA,QAAQqC,EAAS,IAAMoD,EAIzG,MAGD,KAAK,IACL,KAAK,IAEJ,GAjmCQ,KAimCJA,EAAI/E,WAAW,IAAqC,MAAtB+E,EAAI/E,WAAW,GAChD,MAIF,KAAK,IACL,KAAK,IACJ,IAAiC,IAA7B6B,EAAaiF,KAAKV,GAErB,OAAwE,OAAnEE,EAAQF,EAAMhB,UAAUgB,EAAMxH,QAAQ,KAAO,IAAIoB,WAAW,GACzDhE,EAASoK,EAAM9G,QAAQ,UAAW,kBAAmBsE,EAAOC,EAAQwC,GAAO/G,QAAQ,kBAAmB,YAEtGyF,EAAIzF,QAAQgH,EAAOvE,EAASuE,GAASvB,EAAIzF,QAAQgH,EAAOtE,EAAMsE,EAAMhH,QAAQ,QAAS,KAAOyF,EAErG,MAGD,KAAK,IAIJ,GAHAA,EAAMhD,EAASgD,GAA6B,MAAtBA,EAAI/E,WAAW,GAAaiC,EAAK8C,EAAM,IAAMA,EAG/DlB,EAASwC,IAAU,KAA8B,MAAvBtB,EAAI/E,WAAW,KAAe+E,EAAInG,QAAQ,YAAa,IAAM,EAC1F,OAAOmG,EAAIK,UAAU,EAAGL,EAAInG,QAAQ,IAAK,IAAM,GAAGU,QAAQkB,EAAc,KAAOuB,EAAS,MAAQgD,EAOnG,OAAOA,EAUR,SAASS,GAAQuB,EAAS5C,GACzB,IAAI9F,EAAQ0I,EAAQnI,QAAoB,IAAZuF,EAAgB,IAAM,KAC9CvI,EAAMmL,EAAQ3B,UAAU,EAAe,IAAZjB,EAAgB9F,EAAQ,IACnD/C,EAAQyL,EAAQ3B,UAAU/G,EAAQ,EAAG0I,EAAQzI,OAAS,GAE1D,OAAOwE,EAAmB,IAAZqB,EAAgBvI,EAAMA,EAAI0D,QAAQsC,EAAW,MAAOtG,EAAO6I,GAU1E,SAASoB,GAAUyB,EAAOC,GACzB,IAAIlC,EAAM/I,EAASiL,EAAOA,EAAMjH,WAAW,GAAIiH,EAAMjH,WAAW,GAAIiH,EAAMjH,WAAW,IAErF,OAAO+E,IAAQkC,EAAM,IAAMlC,EAAIzF,QAAQoC,EAAa,YAAY0D,UAAU,GAAK,IAAI6B,EAAM,IA4L1F,SAAS5B,GAAOlB,EAAS4C,EAASf,EAAWkB,EAAS/E,EAAMD,EAAQ5D,EAAQiF,EAAItF,EAAOkJ,GACtF,IAAK,IAA0BC,EAAtB/M,EAAI,EAAG0K,EAAMgC,EAAe1M,EAAIwI,IAAWxI,EACnD,OAAQ+M,EAAOxE,EAAQvI,GAAGG,KAAK6M,GAAQlD,EAASY,EAAKiB,EAAWkB,EAAS/E,EAAMD,EAAQ5D,EAAQiF,EAAItF,EAAOkJ,IACzG,UAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,KACJ,MAED,QACCpC,EAAMqC,EAIT,GAAIrC,IAAQgC,EACV,OAAOhC,EAWV,SAASI,GAAWrB,EAAMzF,EAAOC,EAAQgF,GACxC,IAAK,IAAIjJ,EAAIgE,EAAQ,EAAGhE,EAAIiE,IAAUjE,EACrC,OAAQiJ,EAAKtD,WAAW3F,IAEvB,KA72Ce,GA82Cd,GAn3CO,KAm3CHyJ,GAn3CG,KAo3CFR,EAAKtD,WAAW3F,EAAI,IAAgBgE,EAAQ,IAAMhE,EACrD,OAAOA,EAAI,EAGb,MAGD,KAn4CW,GAo4CV,GAv3Cc,KAu3CVyJ,EACH,OAAOzJ,EAAI,EAMf,OAAOA,EAuER,SAASiN,GAAK5J,GACb,IAAK,IAAI9C,KAAQ8C,EAAS,CACzB,IAAIpC,EAAQoC,EAAQ9C,GACpB,OAAQA,GACP,IAAK,WAAYoI,EAAc,EAAN1H,EAAS,MAClC,IAAK,SAAUiH,EAAe,EAANjH,EAAS,MACjC,IAAK,UAAW+G,EAAgB,EAAN/G,EAAS,MACnC,IAAK,WAAYkH,EAAiB,EAANlH,EAAS,MACrC,IAAK,YAAamH,EAAkB,EAANnH,EAAS,MACvC,IAAK,WAAYoH,EAAiB,EAANpH,EAAS,MACrC,IAAK,SACJwH,EAAS,KAEJxH,EAEuB,mBAAVA,EACjBgH,EAAS,GAETA,EAAS,EACTQ,EAASxH,GALTgH,EAAS,GAUb,OAAOgF,GAUR,SAASD,GAAQ7D,EAAU4C,GAC1B,QAAa,IAATmB,MAAmBA,KAAKC,cAAgBH,GAC3C,OAAOlH,EAAQqD,GAIhB,IAAI9H,EAAK8H,EACLM,EAAOpI,EAAGsE,WAAW,GAGrB8D,EAAO,KACVA,GAAQpI,EAAKA,EAAGwJ,QAAQlF,WAAW,IAIhCgD,EAAQ,IACXpH,EAAMF,EAAG4D,QAAQyB,EAtgDD,KAsgDa+C,EAAuB,GAAK,MAI1DA,EAAO,EAGS,IAAZzB,EACHa,EAASxH,EAETuH,EAAYvH,EAGb,IACI+H,EADAuC,EAAY,CAAC9C,GAIbL,EAAU,QAGE,KAFfY,EAAS4B,IAj+CC,EAi+CYe,EAAOJ,EAAWA,EAAW7D,EAAMD,EAAQ,EAAG,EAAG,EAAG,KAE/B,iBAAXuB,IAC/B2C,EAAQ3C,GAKV,IAAIgE,EAAStE,EAAQR,EAAOqD,EAAWI,EAAO,EAAG,GAoBjD,OAjBIvD,EAAU,QAIE,KAHfY,EAAS4B,IA9+CC,EA8+CYoC,EAAQzB,EAAWA,EAAW7D,EAAMD,EAAQuF,EAAOnJ,OAAQ,EAAG,EAAG,KAGlC,iBAArBmJ,EAAShE,KACxCK,EAAO,GAKTlI,EAAM,GACNsH,EAAS,GACTD,EAAY,GACZb,EAAU,EACVD,EAAO,EACPD,EAAS,EAEFM,EAASsB,GAAS,EAAI2D,EA1I9B,SAAiBA,GAChB,OAAOA,EACLnI,QAAQe,EAAW,IACnBf,QAAQ6B,EAAW,IACnB7B,QAAQ8B,EAAU,MAClB9B,QAAQ+B,EAAS,MACjB/B,QAAQgC,EAAU,KAoIkBoG,CAAOD,GAU9C,OAPAJ,GAAY,IA/HZ,SAASM,EAAKC,GACb,OAAQA,GACP,UAAK,EACL,KAAK,KACJ/E,EAAUD,EAAQtE,OAAS,EAC3B,MAED,QACC,GAAsB,mBAAXsJ,EACVhF,EAAQC,KAAa+E,OACf,GAAsB,iBAAXA,EACjB,IAAK,IAAIvN,EAAI,EAAGiE,EAASsJ,EAAOtJ,OAAQjE,EAAIiE,IAAUjE,EACrDsN,EAAIC,EAAOvN,SAGZ0I,EAAiB,IAAP6E,EAKZ,OAAOD,GA4GTN,GAAY,IAAIC,QAEA,IAAZ5J,GACH4J,GAAI5J,GAGE2J,GAnpD6ElH,CAAQ,O,6BCF7F,IAAI0H,EAAuB,EAAQ,GAEnC,SAASC,KACT,SAASC,KACTA,EAAuBC,kBAAoBF,EAE3C1N,EAAOD,QAAU,WACf,SAAS8N,EAAKC,EAAOC,EAAUC,EAAeC,EAAUC,EAAcC,GACpE,GAAIA,IAAWV,EAAf,CAIA,IAAIW,EAAM,IAAIC,MACZ,mLAKF,MADAD,EAAI5N,KAAO,sBACL4N,GAGR,SAASE,IACP,OAAOT,EAFTA,EAAKU,WAAaV,EAMlB,IAAIW,EAAiB,CACnBjG,MAAOsF,EACPY,KAAMZ,EACNa,KAAMb,EACNc,OAAQd,EACRlM,OAAQkM,EACRe,OAAQf,EACRgB,OAAQhB,EAERiB,IAAKjB,EACLkB,QAAST,EACThD,QAASuC,EACTmB,YAAanB,EACboB,WAAYX,EACZY,KAAMrB,EACNsB,SAAUb,EACVc,MAAOd,EACPe,UAAWf,EACXgB,MAAOhB,EACPiB,MAAOjB,EAEPkB,eAAgB7B,EAChBC,kBAAmBF,GAKrB,OAFAc,EAAeiB,UAAYjB,EAEpBA,I,6BCnDTxO,EAAOD,QAFoB,gD,cCR3B,IAOI2P,EACAC,EARApN,EAAUvC,EAAOD,QAAU,GAU/B,SAAS6P,IACL,MAAM,IAAIvB,MAAM,mCAEpB,SAASwB,IACL,MAAM,IAAIxB,MAAM,qCAsBpB,SAASyB,EAAWC,GAChB,GAAIL,IAAqBM,WAErB,OAAOA,WAAWD,EAAK,GAG3B,IAAKL,IAAqBE,IAAqBF,IAAqBM,WAEhE,OADAN,EAAmBM,WACZA,WAAWD,EAAK,GAE3B,IAEI,OAAOL,EAAiBK,EAAK,GAC/B,MAAME,GACJ,IAEI,OAAOP,EAAiBtP,KAAK,KAAM2P,EAAK,GAC1C,MAAME,GAEJ,OAAOP,EAAiBtP,KAAK+M,KAAM4C,EAAK,MAvCnD,WACG,IAEQL,EADsB,mBAAfM,WACYA,WAEAJ,EAEzB,MAAOK,GACLP,EAAmBE,EAEvB,IAEQD,EADwB,mBAAjBO,aACcA,aAEAL,EAE3B,MAAOI,GACLN,EAAqBE,GAjB7B,GAwEA,IAEIM,EAFAC,EAAQ,GACRC,GAAW,EAEXC,GAAc,EAElB,SAASC,IACAF,GAAaF,IAGlBE,GAAW,EACPF,EAAajM,OACbkM,EAAQD,EAAaK,OAAOJ,GAE5BE,GAAc,EAEdF,EAAMlM,QACNuM,KAIR,SAASA,IACL,IAAIJ,EAAJ,CAGA,IAAIK,EAAUZ,EAAWS,GACzBF,GAAW,EAGX,IADA,IAAIM,EAAMP,EAAMlM,OACVyM,GAAK,CAGP,IAFAR,EAAeC,EACfA,EAAQ,KACCE,EAAaK,GACdR,GACAA,EAAaG,GAAYM,MAGjCN,GAAc,EACdK,EAAMP,EAAMlM,OAEhBiM,EAAe,KACfE,GAAW,EAnEf,SAAyBQ,GACrB,GAAIlB,IAAuBO,aAEvB,OAAOA,aAAaW,GAGxB,IAAKlB,IAAuBE,IAAwBF,IAAuBO,aAEvE,OADAP,EAAqBO,aACdA,aAAaW,GAExB,IAEWlB,EAAmBkB,GAC5B,MAAOZ,GACL,IAEI,OAAON,EAAmBvP,KAAK,KAAMyQ,GACvC,MAAOZ,GAGL,OAAON,EAAmBvP,KAAK+M,KAAM0D,KAgD7CC,CAAgBJ,IAiBpB,SAASK,EAAKhB,EAAKxH,GACf4E,KAAK4C,IAAMA,EACX5C,KAAK5E,MAAQA,EAYjB,SAASyI,KA5BTzO,EAAQ0O,SAAW,SAAUlB,GACzB,IAAImB,EAAO,IAAI3F,MAAMxG,UAAUb,OAAS,GACxC,GAAIa,UAAUb,OAAS,EACnB,IAAK,IAAIjE,EAAI,EAAGA,EAAI8E,UAAUb,OAAQjE,IAClCiR,EAAKjR,EAAI,GAAK8E,UAAU9E,GAGhCmQ,EAAMtL,KAAK,IAAIiM,EAAKhB,EAAKmB,IACJ,IAAjBd,EAAMlM,QAAiBmM,GACvBP,EAAWW,IASnBM,EAAKlP,UAAU+O,IAAM,WACjBzD,KAAK4C,IAAIoB,MAAM,KAAMhE,KAAK5E,QAE9BhG,EAAQ6O,MAAQ,UAChB7O,EAAQ8O,SAAU,EAClB9O,EAAQC,IAAM,GACdD,EAAQ+O,KAAO,GACf/O,EAAQG,QAAU,GAClBH,EAAQgP,SAAW,GAInBhP,EAAQiP,GAAKR,EACbzO,EAAQkP,YAAcT,EACtBzO,EAAQmP,KAAOV,EACfzO,EAAQoP,IAAMX,EACdzO,EAAQqP,eAAiBZ,EACzBzO,EAAQsP,mBAAqBb,EAC7BzO,EAAQuP,KAAOd,EACfzO,EAAQwP,gBAAkBf,EAC1BzO,EAAQyP,oBAAsBhB,EAE9BzO,EAAQ0P,UAAY,SAAUzR,GAAQ,MAAO,IAE7C+B,EAAQ2P,QAAU,SAAU1R,GACxB,MAAM,IAAI6N,MAAM,qCAGpB9L,EAAQ4P,IAAM,WAAc,MAAO,KACnC5P,EAAQ6P,MAAQ,SAAUC,GACtB,MAAM,IAAIhE,MAAM,mCAEpB9L,EAAQ+P,MAAQ,WAAa,OAAO,I,+CCzD7B,SAASC,EAAO7R,EAAGgB,GACtB,IAAIrB,EAAsB,mBAAXW,QAAyBN,EAAEM,OAAOwR,UACjD,IAAKnS,EAAG,OAAOK,EACf,IAAmBK,EAAYkP,EAA3BhQ,EAAII,EAAED,KAAKM,GAAO+R,EAAK,GAC3B,IACI,WAAc,IAAN/Q,GAAgBA,KAAM,MAAQX,EAAId,EAAE+M,QAAQ0F,MAAMD,EAAG3N,KAAK/D,EAAEG,OAExE,MAAOyR,GAAS1C,EAAI,CAAE0C,MAAOA,GAC7B,QACI,IACQ5R,IAAMA,EAAE2R,OAASrS,EAAIJ,EAAU,SAAII,EAAED,KAAKH,GAElD,QAAU,GAAIgQ,EAAG,MAAMA,EAAE0C,OAE7B,OAAOF,E,2CC/HAG,EAAe,SAAsBC,GAC9C,OAAOA,EAASC,SAAWD,EAASC,SAASrR,KAAKoR,IAYzC,EAAmB,SAA0B3R,GACtD,QAASA,GAASA,aAAiB,IAAM6R,WAyBhCC,EAAmB,SAA0BvS,GACtD,OAAO,SAAUoS,EAAUI,GACzB,OAAO,EAAiBJ,GAAY,SAAUK,GAC5C,OAAOL,EAASI,GAAOxS,EAAOyS,SAC5BC,ICrCuBH,GDkDD,SAA0BE,GACtD,MAAO,CAAEA,UAAWA,EAAW5H,QAAS,sBAAY4H,OCvCtBF,GDmDV,SAAkB9R,GACtC,OAAOA,KChEF,IAwBIkS,EAAmBJ,EAAiB,eAepC,EAAe,SAAsBH,EAAUQ,GACxD,IAAK,IAAIC,EAAOvO,UAAUb,OAAQqP,EAAYhI,MAAM+H,EAAO,EAAIA,EAAO,EAAI,GAAIE,EAAO,EAAGA,EAAOF,EAAME,IACnGD,EAAUC,EAAO,GAAKzO,UAAUyO,GAGlC,OAAO,EAAiBX,GAAYD,EAAaC,IAAa,WAC5D,IAAK,IAAIY,EAAQ1O,UAAUb,OAAQgN,EAAO3F,MAAMkI,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAChFxC,EAAKwC,GAAS3O,UAAU2O,GAG1B,OAAOL,EAAOjT,KAAKyS,EAAUA,EAAU3B,EAAMqC,SAC3CJ,GC7DKQ,EAAS,WAClB,IAAIC,EAAgB,GAEhBjO,EAAMiO,EAEND,EAAS,WACX,OAAOhO,IAAQiO,EAAgBjO,EAAMA,EAAwB,oBAAXkO,OAAyBA,OAAOlO,KAAOkO,OAAOC,UAAYF,GAO9G,OAJAD,EAAOI,MAAQ,WACb,OAAOpO,EAAMiO,GAGRD,EAbW,GA4CTK,EAAiB,WAC1B,IAAIC,GAAU,EAEVD,EAAiB,WACnB,OAAOC,IAAYA,EAvBI,oBAAXJ,QAAiD,mBAAhBA,OAAOK,MAA2D,mBAA7BP,IAASQ,iBAAkC,WAC7H,IAGE,OAFA,IAAIN,OAAOK,MAEJ,EACP,MAAOvB,GACP,OAAO,GANoH,KA8B/H,OAJAqB,EAAeD,MAAQ,WACrB,OAAOE,GAAU,GAGZD,EAXmB,GAmCjBI,EAA0B,WACnC,OACMC,EAAO,KACPC,EAAe,KAEZ,SAAUlR,GACf,OAAOA,IAAUkR,EAAeD,EAAgCA,GAAxBC,EAAelR,GAlBpC,SAAqBA,GAC5C,OAAO4Q,IAAmBL,IAASQ,gBAAgB,IAAIN,OAAOK,KAAK,CAAC9Q,GAAQ,CAAEmR,KAAM,cAAiB,KAiB1BC,CAAYpR,GAAgB,MALhG,IACDiR,EACAC,G,OCpFGG,EAAiB,CAC1BC,cAAe,IACfC,cAAc,EACdC,WAAY,IACZC,YAAY,GAaHC,EAAqB,SAA4BhH,EAAOiH,GACjE,MAAgC,kBAAlBjH,EAAMiH,GAAwBjH,EAAMiH,GAAUN,EAAeM,I,gCCLlE,EAAqB,SAA4BC,EAAUC,GACpE,IAAIN,EAAeM,EAAKN,aACpBE,EAAaI,EAAKJ,WACtB,OAAO,IAAI,IAAO,CAChBzM,SAAUuM,EACVO,QAAQ,EACRC,UAAU,EACVjN,OAAQ2M,GAJH,CAKJ,GAAIG,IAcE,EAAoB,SAA2BA,EAAUlH,GAClE,OAAOA,EAAM8G,WAAa,EAAmBI,EAAUlH,GAAS,IAAS,EAAmBkH,EAAUlH,GAAQ,MC1C5GsH,EAAWzU,OAAO0U,QAAU,SAAUC,GAAU,IAAK,IAAIrV,EAAI,EAAGA,EAAI8E,UAAUb,OAAQjE,IAAK,CAAE,IAAIsV,EAASxQ,UAAU9E,GAAI,IAAK,IAAIuB,KAAO+T,EAAc5U,OAAOkB,UAAUC,eAAe1B,KAAKmV,EAAQ/T,KAAQ8T,EAAO9T,GAAO+T,EAAO/T,IAAY,OAAO8T,GAsChP,IAAIE,EAAoB,SAA2BP,GACxD,IAAI/F,EAAO+F,EAAK/F,KAEhB,OAAOuG,EADYR,EAAKQ,cACJvG,IAaXwG,EAAsB,SAA6BC,GAC5D,IAAIzG,EAAOyG,EAAMzG,MAGjB0G,EAFiBD,EAAMC,YAEZ1G,IAmBF2G,EAAqB,SAA4BC,EAAOC,GACjE,IAAIC,EAAmBF,EAAME,iBACzB9G,EAAO4G,EAAM5G,KACbuG,EAAeK,EAAML,aACrB3H,EAAQgI,EAAMhI,MACdgF,EAAWgD,EAAMhD,SACjBmD,EAAgBF,EAAM,GAE1BN,EAAavG,GAETpB,EAAMlD,WAAaqL,EAAcrL,UACnCkI,EAASkD,IAeFE,EAAuB,SAA8BC,GAC9D,IAAIjH,EAAOiH,EAAMjH,KAEjB,OAAO0G,EADUO,EAAMP,YACL1G,IAaT,EAAmB,SAA0BkH,GACtD,IAAItI,EAAQsI,EAAMtI,MAClB,MAAO,CACL1K,MAAO,EAAkB0K,EAAMlD,UAAY,GAAI,CAC7C+J,aAAcG,EAAmBhH,EAAO,gBACxC8G,WAAYE,EAAmBhH,EAAO,cACtC+G,WAAYC,EAAmBhH,EAAO,kBAejC2H,EAAe,SAAsB5C,EAAUwD,GACxD,IAAInH,EAAOmH,EAAM,GAEO,oBAAbC,UAA4BpH,IACrC2D,EAAS0D,eAAiBrH,EAAKsH,WAE/B3D,EAAS0D,eAAeE,YAAYvH,GACpCoH,SAASI,KAAKC,YAAYzH,KAcnB0G,EAAa,SAAoB/C,EAAU+D,GACpD,IAAI1H,EAAO0H,EAAM,GAEjB,GAAwB,oBAAbN,UAA4BpH,EACrC,IACEoH,SAASI,KAAKD,YAAYvH,GAC1B2D,EAAS0D,eAAeI,YAAYzH,GACpC,MAAOyD,IAEP,QACAE,EAAS3D,KAAO,KAChB2D,EAAS0D,eAAiB,OAK5BM,EAAmB,IAAqB,6BAA+B,sBAEvE,EAAQ,SAAUC,GAGpB,SAASC,EAAMjJ,IAlLjB,SAAyB+E,EAAUmE,GAAe,KAAMnE,aAAoBmE,GAAgB,MAAM,IAAIC,UAAU,qCAmL5GC,CAAgB/J,KAAM4J,GAEtB,IAAII,EAnLR,SAAoCC,EAAMhX,GAAQ,IAAKgX,EAAQ,MAAM,IAAIC,eAAe,6DAAgE,OAAOjX,GAAyB,iBAATA,GAAqC,mBAATA,EAA8BgX,EAAPhX,EAmLlNkX,CAA2BnK,KAAM2J,EAAe1W,KAAK+M,KAAMW,IAgBvE,OAdAqJ,EAAM3B,kBAAoB,EAAa2B,EAAO3B,GAC9C2B,EAAMtB,mBAAqB,EAAasB,EAAOtB,GAC/CsB,EAAMN,GAAoB,EAAaM,EAAOzB,GAC9CyB,EAAMjB,qBAAuB,EAAaiB,EAAOjB,GACjDiB,EAAMI,SAAW,KACjBJ,EAAMjI,KAAO,KACbiI,EAAMZ,eAAiB,KACvBY,EAAMK,kBAAoBpD,IAC1B+C,EAAMnB,iBAAmB,EAAamB,EAAO,GAC7CA,EAAM1B,aAAe,EAAa0B,EAAO1B,GACzC0B,EAAMvB,WAAa,EAAauB,EAAOvB,GAGvCuB,EAAMxT,MAAQ,EAAiB,CAAEmK,MAAOA,IACjCqJ,EAwDT,OAzPF,SAAmBM,EAAUC,GAAc,GAA0B,mBAAfA,GAA4C,OAAfA,EAAuB,MAAM,IAAIT,UAAU,kEAAoES,GAAeD,EAAS5V,UAAYlB,OAAOY,OAAOmW,GAAcA,EAAW7V,UAAW,CAAEuL,YAAa,CAAElM,MAAOuW,EAAU5W,YAAY,EAAO8W,UAAU,EAAMC,cAAc,KAAeF,IAAY/W,OAAOkX,eAAiBlX,OAAOkX,eAAeJ,EAAUC,GAAcD,EAASK,UAAYJ,GA4K/dK,CAAUhB,EAAOD,GAsCjBC,EAAMlV,UAAUmW,OAAS,WACvB,IAAIC,EAAS9K,KAAKW,MAKdA,GAJkBmK,EAAOrN,SACHqN,EAAOvD,aACTuD,EAAOtD,aACPsD,EAAOpD,WA7NnC,SAAkCqD,EAAKC,GAAQ,IAAI7C,EAAS,GAAI,IAAK,IAAIrV,KAAKiY,EAAWC,EAAK3T,QAAQvE,IAAM,GAAkBU,OAAOkB,UAAUC,eAAe1B,KAAK8X,EAAKjY,KAAcqV,EAAOrV,GAAKiY,EAAIjY,IAAM,OAAOqV,EA8NnM,CAAyB2C,EAAQ,CAAC,WAAY,eAAgB,eAAgB,gBAEtF7U,EAAQ+J,KAAKxJ,MAAMP,MAGvB,OAAI0R,EAAmB3H,KAAKW,MAAO,iBAC7BkG,IAGA,IAAMoE,cAAc,OAAQhD,EAAS,GAAItH,EAAO,CAC9CuG,KAAMlH,KAAKqK,kBAAkBpU,GAC7B6P,IAAKG,EAAiBjG,KAAM,QAC5BkL,IAAK,gBAYN,IAAMD,cACX,QACAhD,EAAS,CACPnC,IAAKG,EAAiBjG,KAAM,SAC3BW,GACH1K,IAIG2T,EA9EG,CA+EV,iBAEF,EAAMuB,UAAY,CAChB1N,SAAU,IAAUgE,OAAOL,WAC3BmG,aAAc,IAAUjG,KACxBtF,GAAI,IAAUyF,OACd+F,aAAc,IAAUlG,KACxBmG,WAAY,IAAUnG,KACtBoG,WAAY,IAAUpG,MAExB,EAAM8J,iBFxOwB,SAA0BjV,GACtD,OAAO3C,OAAOwX,KAAK7U,GAASkV,SAAQ,SAAUzD,GAC5C,OAAON,EAAe3S,eAAeiT,IAAsC,kBAApBzR,EAAQyR,KAA0BN,EAAeM,GAAUzR,EAAQyR,QEyO/G,IC9QH0D,ED8QG,IE/QFC,EACE,CACXC,KAAM,CACJxP,GAAI,OACJyP,UAAW,IACXpY,KAAM,cAERqY,QAAS,CACP1P,GAAI,UACJyP,UAAW,IACXpY,KAAM,iBAERsY,MAAO,CACL3P,GAAI,QACJyP,UAAW,IACXpY,KAAM,gBAfCkY,EAmBS,YAnBTA,EAwBG,CACZK,WAAY,YACZC,SAAU,UACVC,KAAM,OACNC,MAAO,QACPC,yBAA0B,2BAC1BxT,IAAK,QD7BT,SAAY8S,GACV,wBACA,4BACA,sBACA,oBACA,6CACA,cANF,CAAYA,MAAmB,KAgD/B,MAGE,SAAYlE,EAA2BzG,GAErC,OADAX,KAAKoH,KAAOA,EACJA,EAAK6E,eACX,KAAKX,EAAoBY,UACvBlM,KAAKW,MAAQ,CACXwL,MAAOxL,GAASA,EAAMwL,MAAQxL,EAAMwL,MAAQ,IAE9C,MACF,KAAKb,EAAoBc,YACvBpM,KAAKW,MAAQ,CACX0L,KAAM1L,GAASA,EAAM0L,KAAO1L,EAAM0L,KAAO,IAE3C,MACF,KAAKf,EAAoBgB,YAKzB,KAAKhB,EAAoBiB,gBACvBvM,KAAKW,MAAQ,CACX6L,IAAK7L,GAASA,EAAM6L,IAAM7L,EAAM6L,IAAM,IAExC,MACF,KAAKjB,EAAoBS,yBACvBhM,KAAKW,MAAQ,CACX8L,MAAO9L,GAASA,EAAM8L,MAAQ9L,EAAM8L,MAAQ,IAE9C,MACF,QACEzM,KAAKW,MAAQ,KE/ER+L,EAAkB,SAAUzY,GACvC,IAAI0Y,EAAW,GACf,GAAI,CAAC,OAAQ,SAAStV,QAAQpD,EAAKgY,gBAAkB,GAAKvF,OAAOkG,YAAa,CAC5E,IAAIC,EAAWnG,OAAOkG,YACnBE,aACApX,KAAI,SAACoN,GAAM,OAAAA,EAAA,QACXiK,QAAO,SAACjK,GAAM,OAAAA,EAAEkK,SAAF,UACdD,QAAO,SAACjK,GAAM,OAAAA,EAAEzL,QAAQ,uBAAV,KACbwV,GAAYA,EAAS9V,OAAS,IAChC4V,EACEE,EAAS,GAAG9U,QAAQ,qBAAsB,yBAAyB9D,EAAKgY,cAAa,KAAKxW,MAAM,GAAI,GAAK,QAG/G,OAAOkX,GAEIM,EAAqB,SAAUC,EAAqBpW,GAC/D,OAAQA,GACN,KAAM,IACJ,MAAO,kBACT,KAAM,IACJ,MAAO,eACT,KAAM,IACJ,MAAO,gBACT,QACE,OAAOoW,EAAOpW,IAAUoW,EAAOpW,GAAOzD,KAAO6Z,EAAOpW,GAAOzD,KAAO,oBCjBxE,aAGE,WAAY8C,GACV6J,KAAK3M,KAAO8C,EAAQ9C,MAAQkY,EAC5BvL,KAAKmN,OACuB,IAA1BhX,EAAQgX,OAAOpW,OACX,CACE,IAAI,EAAeuU,EAAoBY,UAAW,CAAEC,MAAOZ,EAAmBG,QAAQ1P,KACtF,IAAI,EAAesP,EAAoBiB,gBAAiB,CAAEC,IAAK,KAC/D,IAAI,EAAelB,EAAoBgB,YAAa,CAAEE,IAAK,KAC3D,IAAI,EAAelB,EAAoBc,YAAa,CAAEC,KAAM,MAE9DlW,EAAQgX,OAoElB,OAlEE,YAAAC,SAAA,SAAShG,GACPpH,KAAKmN,OAAOxV,KAAK,IAAI,EAAeyP,EAAM,MAE5C,YAAAiG,YAAA,SAAYvW,GACVkJ,KAAKmN,OAAOG,OAAOxW,EAAO,IAEpB,YAAAyW,eAAR,SAAuBJ,QAAA,IAAAA,MAAA,IACrB,IAAIjN,EAAS,GA4Cb,OA3CAiN,EAAO9B,SAAQ,SAACpV,GACVA,EAAMmR,OAASmE,EAAoB/S,IACjCvC,EAAM0K,OAA6B,KAApB1K,EAAM0K,MAAM6L,MAC7BtM,GAAU,gBAAgBjK,EAAM0K,MAAM6L,IAAG,6BAGlCvW,EAAMmR,OAASmE,EAAoBK,WACxC3V,EAAM0K,OAA+B,KAAtB1K,EAAM0K,MAAMwL,QACzBlW,EAAM0K,MAAMwL,MAAMF,gBAAkBV,EAAmBC,KAAKxP,GAC9DkE,GAAU,gBAAgBwM,EAAgB,QAAO,gCAExCzW,EAAM0K,MAAMwL,MAAMF,gBAAkBV,EAAmBI,MAAM3P,KACtEkE,GAAU,gBAAgBwM,EAAgB,SAAQ,kCAI7CzW,EAAMmR,OAASmE,EAAoBQ,MACxC9V,EAAM0K,OAA8B,KAArB1K,EAAM0K,MAAM0L,OAC7BnM,IAAajK,EAAM0K,MAAM0L,MAAQ,IAAE,0BAG5BpW,EAAMmR,OAASmE,EAAoBM,SACxC5V,EAAM0K,OAA6B,KAApB1K,EAAM0K,MAAM6L,MAC7BtM,GAAU,uJAEGjK,EAAM0K,MAAM6L,IAAG,kMAQrBvW,EAAMmR,OAASmE,EAAoBS,0BACxC/V,EAAM0K,OAA+B,KAAtB1K,EAAM0K,MAAM8L,QAC7BvM,GAAU,8DAEIjK,EAAM0K,MAAM8L,MAAK,YAM9BvM,GAET,YAAAsN,gBAAA,WACE,IAAItN,EAAS,GAUb,OATIF,KAAKmN,QAAUnN,KAAKmN,OAAOpW,OAAS,IACtCmJ,GAAUF,KAAKuN,eAAevN,KAAKmN,OAAOJ,QAAO,SAAC9W,GAAU,OAAAA,EAAMmR,OAASmE,EAAf,QAC5DrL,GAAUF,KAAKuN,eAAevN,KAAKmN,OAAOJ,QAAO,SAAC9W,GAAU,OAAAA,EAAMmR,OAASmE,EAAf,UAC5DrL,GAAUF,KAAKuN,eACbvN,KAAKmN,OAAOJ,QACV,SAAC9W,GAAU,OAAAA,EAAMmR,OAASmE,EAAoB/S,KAAOvC,EAAMmR,OAASmE,EAAzD,WAIVrL,GAEX,EAjFA,GCDa,EAAc,SAACS,GAK1B,OACE,yBAAK1K,MAAO,CAAEwX,UAAW,WACvB,8BACE9M,EAAMuM,QAAU,IACfxX,KAAI,SAACyW,GAAU,OAAAA,EAAA,QACf9I,OANe,CAAC,eAAgB,kBAOhC3N,KAAI,SAACgY,EAAW5W,GACf,OACE,0BACE6W,UAAU,oBACV1X,MAAO,CAAE2X,WAAsB,IAAV9W,EAAc,IAAM,OAAQ+W,YAAa,QAC9DC,QAAS,WAAM,OAfN,SAACJ,GACpB/M,EAAMoN,SAASL,GAcUM,CAAA,IACf3Z,IAAKyC,GAEJ4W,Q,OChBF,EAAc,SAAC,G,IAAE3Z,EAAK,QAAEga,EAAQ,WACrC,IAA0C,oBAAS,GAAM,GAAxDE,EAAgB,KAAEC,EAAmB,KAEtCd,EAAW,SAACe,GAChB,IAAIhC,EAAQpY,EACZoY,EAAMgB,OAAShB,EAAMgB,QAAU,GAC/BhB,EAAMgB,OAAOxV,KAAK,IAAI,EAAewW,EAAW,OAChDJ,EAAS5B,IAGLiC,EAAgB,SAACnK,GACrB,IAAIkI,EAAQpY,EACZoY,EAAM9Y,KAAO4Q,EACb8J,EAAS5B,IAGLkC,EAAwB,SAACvX,EAAewX,EAAsBC,GAClE,IAAIpC,EAAQpY,EACZoY,EAAMgB,OAAOrW,GAAO6J,MAAQwL,EAAMgB,OAAOrW,GAAO6J,OAAS,GACzDwL,EAAMgB,OAAOrW,GAAO6J,MAAM2N,GAAgBC,EAC1CR,EAAS5B,IAELqC,EAAmC,CACvC,CAAEza,MAAO,UAAW0a,MAAO,WAC3B,CAAE1a,MAAO,OAAQ0a,MAAO,QACxB,CAAE1a,MAAO,QAAS0a,MAAO,UAE3B,OACE,oCACE,0BAAMd,UAAU,eAAe1X,MAAO,CAAEyY,eAAgB,UACrD3a,EAAMV,MAGT,uBAAGsa,UAAU,kCAAkCG,QAAS,WAAM,OAAAI,GAAA,MAE9D,kBAAC,QAAK,CAACS,OAAQV,EAAkBW,UAAW,WAAM,OAAAV,GAAA,IAA4BjK,MAAO,QAAQlQ,EAAMV,MACjG,kBAAC,QAAK,oBACN,kBAAC,QAAK,CAACwb,IAAK,GAAI9a,MAAOA,EAAMV,KAAM0a,SAAU,SAACjL,GAAM,OAAAsL,EAActL,EAAEgM,cAAhB,UACpD,6BACC/a,EAAMoZ,OAAOzX,KAAI,SAACO,EAAOa,GACxB,OAAQb,EAAMmR,MACZ,KAAKkE,EAAoBY,UACvB,IAAM6C,EAA6BP,EAAcQ,MAAK,SAAChb,GAAM,OAAAA,EAAED,QAAUkC,EAAM0K,MAAlB,UAAkC,CAC7F8N,MAAO,UACP1a,MAAO,WAET,OACE,oCACE,kBAAC,QAAK,mBACN,kBAAC,mBAAgB,CACfA,MAAOgb,EAAUhb,MACjBoC,QAASqY,EACTT,SAAU,SAACjL,GAAM,OAAAuL,EAAsBvX,EAAO,QAA7B,MAEnB,8BAGN,KAAKwU,EAAoBiB,gBACvB,OACE,oCACE,kBAAC,QAAK,yBACN,kBAAC,QAAK,CACJsC,IAAK,GACL9a,MAAOkC,EAAM0K,MAAM6L,IACnBuB,SAAU,SAACjL,GAAM,OAAAuL,EAAsBvX,EAAO,MAAOgM,EAAEgM,cAAtC,UAEnB,8BAGN,KAAKxD,EAAoBgB,YACvB,OACE,oCACE,kBAAC,QAAK,yBACN,kBAAC,QAAK,CACJuC,IAAK,GACL9a,MAAOkC,EAAM0K,MAAM6L,IACnBuB,SAAU,SAACjL,GAAM,OAAAuL,EAAsBvX,EAAO,MAAOgM,EAAEgM,cAAtC,UAEnB,8BAGN,KAAKxD,EAAoBc,YACvB,OACE,oCACE,kBAAC,QAAK,6BACN,kBAAC,WAAQ,CACPrY,MAAOkC,EAAM0K,MAAM0L,KACnB4C,KAAM,EACNlB,SAAU,SAACjL,GAAM,OAAAuL,EAAsBvX,EAAO,OAAQgM,EAAEgM,cAAvC,UAEnB,8BAGN,KAAKxD,EAAoB4D,gBACvB,OACE,oCACE,kBAAC,QAAK,uBACN,kBAAC,QAAK,CACJnb,MAAOkC,EAAM0K,MAAM8L,MACnBsB,SAAU,SAACjL,GAAM,OAAAuL,EAAsBvX,EAAO,QAASgM,EAAEgM,cAAxC,QACjB/T,OACE,6BACE,kBAAC,cAAW,CACV0R,MAAOxW,EAAM0K,MAAM8L,MACnBsB,SAAU,SAACjL,GAAM,OAAAuL,EAAsBvX,EAAO,QAA7B,SAKzB,8BAGN,IAAK,MACH,OACE,oCACE,kBAAC,QAAK,iCACN,kBAAC,QAAK,CACJ+X,IAAK,GACL9a,MAAOkC,EAAM0K,MAAM6L,IACnBuB,SAAU,SAACjL,GAAM,OAAAuL,EAAsBvX,EAAO,MAAOgM,EAAEgM,cAAtC,UAEnB,8BAGN,IAAK,OACL,QACE,OAAO,yCAGb,yBAAKnB,UAAU,eACb,6BACC5Z,EAAMoZ,OAAOJ,QAAO,SAAClY,GAAM,OAAAA,EAAEuS,OAASkE,EAAX,eAA4CvU,OAAS,GAC/E,oCACE,4BAAQ4W,UAAU,kBAAkBG,QAAS,WAAM,OAAAV,EAAS9B,EAAT,eAAyC,oBAI3F,MAEJvX,EAAMoZ,OAAOJ,QAAO,SAAClY,GAAM,OAAAA,EAAEuS,OAASkE,EAAX,eAA4CvU,OAAS,GAC/E,oCACE,4BAAQ4W,UAAU,kBAAkBG,QAAS,WAAM,OAAAV,EAAS9B,EAAT,eAAyC,kBAI3F,MAEJvX,EAAMoZ,OAAOJ,QAAO,SAAClY,GAAM,OAAAA,EAAEuS,OAASkE,EAAX,mBAAgDvU,OAAS,GACnF,oCACE,4BAAQ4W,UAAU,kBAAkBG,QAAS,WAAM,OAAAV,EAAS9B,EAAT,mBAA6C,gBAI/F,MAEJvX,EAAMoZ,OAAOJ,QAAO,SAAClY,GAAM,OAAAA,EAAEuS,OAASkE,EAAX,mBAAgDvU,OAAS,GACnF,oCACE,4BAAQ4W,UAAU,kBAAkBG,QAAS,WAAM,OAAAV,EAAS9B,EAAT,mBAA6C,sBAI/F,MAEL,6BACA,6BACA,4BAAQqC,UAAU,kBAAkBG,QAAS,WAAM,OAAAI,GAAA,KAA0B,UCtH1EiB,EAA8C,CACzDnT,GAAI,SACJ3I,KAAM,SACN+b,KAAM,SACNC,SAAU,CAAC,UACXC,OArDa,SAAC,G,IAAEvb,EAAK,QAAEga,EAAQ,WAezBwB,EAAgB,SAACpD,EAAkBrV,GACvC,IAAIoW,EAASnZ,GAAS,GACtBmZ,EAAOpW,GAASqV,EAChB4B,EAASb,IAEX,OACE,oCACGnZ,aAAK,EAALA,EAAO2B,KAAI,SAACyW,EAAOrV,GAAU,OAC5B,yBAAKzC,IAAKyC,GACR,6BACA,kBAAC,EAAW,CACV/C,MAAOoY,EACP4B,SAAU,SAACyB,GACTD,EAAcC,EAAc1Y,MAGhC,uBACE6W,UAAU,sCACV1J,MAAO,QAAQkI,EAAM9Y,KACrBya,QAAS,WAAM,OAxBL,SAAChX,GACnB,IAAIoW,EAAsBnZ,GAAS,GACnCmZ,EAAOI,OAAOxW,EAAO,GACrBiX,EAASb,GAqBcuC,CAAA,UAIrB,6BACA,yBAAK9B,UAAU,eACb,4BAAQA,UAAU,kBAAkBG,QAvCzB,WACf,IAAIZ,EAAsBnZ,GAAS,GAC/B2b,EAAsB,IAAI,EAAU,CACtCrc,KAAM,cAAa6Z,EAAOnW,OAAS,GACnCoW,OAAQ,KAEVD,EAAOvV,KAAK+X,GACZ3B,EAASb,KAgCgD,qBClD7D,uCAKO,IAAM,EAAS,IAAI,eCgBL,SAAC,G,MAAE/W,EAAO,UAAEwZ,EAAgB,mBACzC,IAA4C,oBAAS,GAAM,GAA1DC,EAAiB,KAAEC,EAAoB,KACxC,IAAkC,mBAAS,IAAG,GAA7CC,EAAY,KAAEC,EAAe,KAOhC7P,EAAS,GA2Bb,OAzBc,QAAd,EAAA/J,EAAQ+W,cAAM,SAAE7B,SAAQ,SAAC2E,EAAyBlZ,GAChD,IAAMqV,EAAQ,IAAI,EAAU6D,GACxBJ,EACEE,IAAiB3D,EAAM9Y,KACzB6M,GAAUiM,EAAMqB,kBACU,iBAAjBsC,EACT5P,EAAS,gBAAgBwM,EAAgB,QAAO,oBAEtB,kBAAjBoD,IACT5P,EAAS,gBAAgBwM,EAAgB,SAAQ,qBAIL,iBAA1CiD,EAAiBxZ,EAAQ8Z,aAC3B/P,EAAS,gBAAgBwM,EAAgB,QAAO,oBAEG,kBAA1CiD,EAAiBxZ,EAAQ8Z,aAClC/P,EAAS,gBAAgBwM,EAAgB,SAAQ,oBAExCiD,EAAiBxZ,EAAQ8Z,eAAiB9D,EAAM9Y,OACzD6M,GAAUiM,EAAMqB,sBAMpB,oCACE,kBAAC,EAAK,KAAEtN,GACP/J,EAAQ+Z,mBAAqB,qCAAQ,kBAAC,EAAW,CAAChD,OAAQ/W,EAAQ+W,OAAQa,SAnClD,SAACL,GAC5BmC,GAAqB,GACrBE,EAAgBrC,UDrBjByC,iBAAgB,SAACC,GAYhB,OAXAA,EAAQC,gBAAgBlB,GACxBiB,EAAQE,iBAAiB,CACvBjd,KAAM,oBACN+b,KAAM,qBACNC,SAAU,CAAC,oBAEbe,EAAQG,aAAa,CACnBld,KAAM,gBACN+b,KAAM,cACNC,SAAU,CAAC,oBAENe,KAERI,qBAAoB,SAACC,G,UACdC,EAAa,CACjBR,mBAAoD,QAAlC,EAAEO,EAAMta,QAAQ+Z,0BAAkB,QAAIO,EAAMP,mBAC9DD,YAAsC,QAA3B,EAAEQ,EAAMta,QAAQ8Z,mBAAW,QAAIhD,EAAmBwD,EAAMvD,OAAQuD,EAAME,eACjFzD,OAA4B,QAAtB,EAAEuD,EAAMta,QAAQ+W,cAAM,QAAIuD,EAAMvD,QAKxC,cAHOuD,EAAMvD,cACNuD,EAAME,qBACNF,EAAMP,mBACNQ", "file": "module.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 11);\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__0__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1__;", "import React from 'react';\n\n/**\n * @constant {Object} BEAUTIFY_OPTIONS the options to pass to cssbeautify\n */\nexport var BEAUTIFY_OPTIONS = {\n  autosemicolon: true,\n  indent: '  '\n};\n\n/**\n * @constant {boolean} IS_PRODUCTION is the runtime in the production environment\n */\nexport var IS_PRODUCTION = !!(process && process.env && process.env.NODE_ENV === 'production');\n\nvar _React$version$split$ = React.version.split('.').slice(0, 2).map(Number),\n    REACT_MAJOR_VERSION = _React$version$split$[0],\n    REACT_MINOR_VERSION = _React$version$split$[1];\n\n/**\n * @constant {boolean} HAS_UNSAFE_METHODS have lifecycle methods been prefixed with `UNSAFE_` in the React version\n */\n\n\nexport var HAS_UNSAFE_METHODS = REACT_MAJOR_VERSION > 16 || REACT_MAJOR_VERSION === 16 && REACT_MINOR_VERSION >= 3;", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__4__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__5__;", "/*\n Copyright (C) 2013 Sencha Inc.\n Copyright (C) 2012 Sencha Inc.\n Copyright (C) 2011 Sencha Inc.\n\n Author: <PERSON><PERSON>.\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights\n to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n*/\n\n/*jslint continue: true, indent: 4 */\n/*global exports:true, module:true, window:true */\n\n(function () {\n\n    'use strict';\n\n    function cssbeautify(style, opt) {\n\n        var options, index = 0, length = style.length, blocks, formatted = '',\n            ch, ch2, str, state, State, depth, quote, comment,\n            openbracesuffix = true,\n            autosemicolon = false,\n            trimRight;\n\n        options = arguments.length > 1 ? opt : {};\n        if (typeof options.indent === 'undefined') {\n            options.indent = '    ';\n        }\n        if (typeof options.openbrace === 'string') {\n            openbracesuffix = (options.openbrace === 'end-of-line');\n        }\n        if (typeof options.autosemicolon === 'boolean') {\n            autosemicolon = options.autosemicolon;\n        }\n\n        function isWhitespace(c) {\n            return (c === ' ') || (c === '\\n') || (c === '\\t') || (c === '\\r') || (c === '\\f');\n        }\n\n        function isQuote(c) {\n            return (c === '\\'') || (c === '\"');\n        }\n\n        // FIXME: handle Unicode characters\n        function isName(c) {\n            return (ch >= 'a' && ch <= 'z') ||\n                (ch >= 'A' && ch <= 'Z') ||\n                (ch >= '0' && ch <= '9') ||\n                '-_*.:#[]'.indexOf(c) >= 0;\n        }\n\n        function appendIndent() {\n            var i;\n            for (i = depth; i > 0; i -= 1) {\n                formatted += options.indent;\n            }\n        }\n\n        function openBlock() {\n            formatted = trimRight(formatted);\n            if (openbracesuffix) {\n                formatted += ' {';\n            } else {\n                formatted += '\\n';\n                appendIndent();\n                formatted += '{';\n            }\n            if (ch2 !== '\\n') {\n                formatted += '\\n';\n            }\n            depth += 1;\n        }\n\n        function closeBlock() {\n            var last;\n            depth -= 1;\n            formatted = trimRight(formatted);\n\n            if (formatted.length > 0 && autosemicolon) {\n                last = formatted.charAt(formatted.length - 1);\n                if (last !== ';' && last !== '{') {\n                    formatted += ';';\n                }\n            }\n\n            formatted += '\\n';\n            appendIndent();\n            formatted += '}';\n            blocks.push(formatted);\n            formatted = '';\n        }\n\n        if (String.prototype.trimRight) {\n            trimRight = function (s) {\n                return s.trimRight();\n            };\n        } else {\n            // old Internet Explorer\n            trimRight = function (s) {\n                return s.replace(/\\s+$/, '');\n            };\n        }\n\n        State = {\n            Start: 0,\n            AtRule: 1,\n            Block: 2,\n            Selector: 3,\n            Ruleset: 4,\n            Property: 5,\n            Separator: 6,\n            Expression: 7,\n            URL: 8\n        };\n\n        depth = 0;\n        state = State.Start;\n        comment = false;\n        blocks = [];\n\n        // We want to deal with LF (\\n) only\n        style = style.replace(/\\r\\n/g, '\\n');\n\n        while (index < length) {\n            ch = style.charAt(index);\n            ch2 = style.charAt(index + 1);\n            index += 1;\n\n            // Inside a string literal?\n            if (isQuote(quote)) {\n                formatted += ch;\n                if (ch === quote) {\n                    quote = null;\n                }\n                if (ch === '\\\\' && ch2 === quote) {\n                    // Don't treat escaped character as the closing quote\n                    formatted += ch2;\n                    index += 1;\n                }\n                continue;\n            }\n\n            // Starting a string literal?\n            if (isQuote(ch)) {\n                formatted += ch;\n                quote = ch;\n                continue;\n            }\n\n            // Comment\n            if (comment) {\n                formatted += ch;\n                if (ch === '*' && ch2 === '/') {\n                    comment = false;\n                    formatted += ch2;\n                    index += 1;\n                }\n                continue;\n            }\n            if (ch === '/' && ch2 === '*') {\n                comment = true;\n                formatted += ch;\n                formatted += ch2;\n                index += 1;\n                continue;\n            }\n\n            if (state === State.Start) {\n\n                if (blocks.length === 0) {\n                    if (isWhitespace(ch) && formatted.length === 0) {\n                        continue;\n                    }\n                }\n\n                // Copy white spaces and control characters\n                if (ch <= ' ' || ch.charCodeAt(0) >= 128) {\n                    state = State.Start;\n                    formatted += ch;\n                    continue;\n                }\n\n                // Selector or at-rule\n                if (isName(ch) || (ch === '@')) {\n\n                    // Clear trailing whitespaces and linefeeds.\n                    str = trimRight(formatted);\n\n                    if (str.length === 0) {\n                        // If we have empty string after removing all the trailing\n                        // spaces, that means we are right after a block.\n                        // Ensure a blank line as the separator.\n                        if (blocks.length > 0) {\n                            formatted = '\\n\\n';\n                        }\n                    } else {\n                        // After finishing a ruleset or directive statement,\n                        // there should be one blank line.\n                        if (str.charAt(str.length - 1) === '}' ||\n                                str.charAt(str.length - 1) === ';') {\n\n                            formatted = str + '\\n\\n';\n                        } else {\n                            // After block comment, keep all the linefeeds but\n                            // start from the first column (remove whitespaces prefix).\n                            while (true) {\n                                ch2 = formatted.charAt(formatted.length - 1);\n                                if (ch2 !== ' ' && ch2.charCodeAt(0) !== 9) {\n                                    break;\n                                }\n                                formatted = formatted.substr(0, formatted.length - 1);\n                            }\n                        }\n                    }\n                    formatted += ch;\n                    state = (ch === '@') ? State.AtRule : State.Selector;\n                    continue;\n                }\n            }\n\n            if (state === State.AtRule) {\n\n                // ';' terminates a statement.\n                if (ch === ';') {\n                    formatted += ch;\n                    state = State.Start;\n                    continue;\n                }\n\n                // '{' starts a block\n                if (ch === '{') {\n                    str = trimRight(formatted);\n                    openBlock();\n                    state = (str === '@font-face') ? State.Ruleset : State.Block;\n                    continue;\n                }\n\n                formatted += ch;\n                continue;\n            }\n\n            if (state === State.Block) {\n\n                // Selector\n                if (isName(ch)) {\n\n                    // Clear trailing whitespaces and linefeeds.\n                    str = trimRight(formatted);\n\n                    if (str.length === 0) {\n                        // If we have empty string after removing all the trailing\n                        // spaces, that means we are right after a block.\n                        // Ensure a blank line as the separator.\n                        if (blocks.length > 0) {\n                            formatted = '\\n\\n';\n                        }\n                    } else {\n                        // Insert blank line if necessary.\n                        if (str.charAt(str.length - 1) === '}') {\n                            formatted = str + '\\n\\n';\n                        } else {\n                            // After block comment, keep all the linefeeds but\n                            // start from the first column (remove whitespaces prefix).\n                            while (true) {\n                                ch2 = formatted.charAt(formatted.length - 1);\n                                if (ch2 !== ' ' && ch2.charCodeAt(0) !== 9) {\n                                    break;\n                                }\n                                formatted = formatted.substr(0, formatted.length - 1);\n                            }\n                        }\n                    }\n\n                    appendIndent();\n                    formatted += ch;\n                    state = State.Selector;\n                    continue;\n                }\n\n                // '}' resets the state.\n                if (ch === '}') {\n                    closeBlock();\n                    state = State.Start;\n                    continue;\n                }\n\n                formatted += ch;\n                continue;\n            }\n\n            if (state === State.Selector) {\n\n                // '{' starts the ruleset.\n                if (ch === '{') {\n                    openBlock();\n                    state = State.Ruleset;\n                    continue;\n                }\n\n                // '}' resets the state.\n                if (ch === '}') {\n                    closeBlock();\n                    state = State.Start;\n                    continue;\n                }\n\n                formatted += ch;\n                continue;\n            }\n\n            if (state === State.Ruleset) {\n\n                // '}' finishes the ruleset.\n                if (ch === '}') {\n                    closeBlock();\n                    state = State.Start;\n                    if (depth > 0) {\n                        state = State.Block;\n                    }\n                    continue;\n                }\n\n                // Make sure there is no blank line or trailing spaces inbetween\n                if (ch === '\\n') {\n                    formatted = trimRight(formatted);\n                    formatted += '\\n';\n                    continue;\n                }\n\n                // property name\n                if (!isWhitespace(ch)) {\n                    formatted = trimRight(formatted);\n                    formatted += '\\n';\n                    appendIndent();\n                    formatted += ch;\n                    state = State.Property;\n                    continue;\n                }\n                formatted += ch;\n                continue;\n            }\n\n            if (state === State.Property) {\n\n                // ':' concludes the property.\n                if (ch === ':') {\n                    formatted = trimRight(formatted);\n                    formatted += ': ';\n                    state = State.Expression;\n                    if (isWhitespace(ch2)) {\n                        state = State.Separator;\n                    }\n                    continue;\n                }\n\n                // '}' finishes the ruleset.\n                if (ch === '}') {\n                    closeBlock();\n                    state = State.Start;\n                    if (depth > 0) {\n                        state = State.Block;\n                    }\n                    continue;\n                }\n\n                formatted += ch;\n                continue;\n            }\n\n            if (state === State.Separator) {\n\n                // Non-whitespace starts the expression.\n                if (!isWhitespace(ch)) {\n                    formatted += ch;\n                    state = State.Expression;\n                    continue;\n                }\n\n                // Anticipate string literal.\n                if (isQuote(ch2)) {\n                    state = State.Expression;\n                }\n\n                continue;\n            }\n\n            if (state === State.Expression) {\n\n                // '}' finishes the ruleset.\n                if (ch === '}') {\n                    closeBlock();\n                    state = State.Start;\n                    if (depth > 0) {\n                        state = State.Block;\n                    }\n                    continue;\n                }\n\n                // ';' completes the declaration.\n                if (ch === ';') {\n                    formatted = trimRight(formatted);\n                    formatted += ';\\n';\n                    state = State.Ruleset;\n                    continue;\n                }\n\n                formatted += ch;\n\n                if (ch === '(') {\n                    if (formatted.charAt(formatted.length - 2) === 'l' &&\n                            formatted.charAt(formatted.length - 3) === 'r' &&\n                            formatted.charAt(formatted.length - 4) === 'u') {\n\n                        // URL starts with '(' and closes with ')'.\n                        state = State.URL;\n                        continue;\n                    }\n                }\n\n                continue;\n            }\n\n            if (state === State.URL) {\n\n\n                // ')' finishes the URL (only if it is not escaped).\n                if (ch === ')' && formatted.charAt(formatted.length - 1 !== '\\\\')) {\n                    formatted += ch;\n                    state = State.Expression;\n                    continue;\n                }\n            }\n\n            // The default action is to copy the character (to prevent\n            // infinite loop).\n            formatted += ch;\n        }\n\n        formatted = blocks.join('') + formatted;\n\n        return formatted;\n    }\n\n    if (typeof exports !== 'undefined') {\n        // Node.js module.\n        module.exports = exports = cssbeautify;\n    } else if (typeof window === 'object') {\n        // Browser loading.\n        window.cssbeautify = cssbeautify;\n    }\n\n}());\n", "/*\n *          __        ___\n *    _____/ /___  __/ (_)____\n *   / ___/ __/ / / / / / ___/\n *  (__  ) /_/ /_/ / / (__  )\n * /____/\\__/\\__, /_/_/____/\n *          /____/\n *\n * light - weight css preprocessor @licence MIT\n */\n(function (factory) {/* eslint-disable */\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? (module['exports'] = factory(null)) :\n\t\ttypeof define === 'function' && define['amd'] ? define(factory(null)) :\n\t\t\t(window['stylis'] = factory(null))\n}(/** @param {*=} options */function factory (options) {/* eslint-disable */\n\n\t'use strict'\n\n\t/**\n\t * Notes\n\t *\n\t * The ['<method name>'] pattern is used to support closure compiler\n\t * the jsdoc signatures are also used to the same effect\n\t *\n\t * ----\n\t *\n\t * int + int + int === n4 [faster]\n\t *\n\t * vs\n\t *\n\t * int === n1 && int === n2 && int === n3\n\t *\n\t * ----\n\t *\n\t * switch (int) { case ints...} [faster]\n\t *\n\t * vs\n\t *\n\t * if (int == 1 && int === 2 ...)\n\t *\n\t * ----\n\t *\n\t * The (first*n1 + second*n2 + third*n3) format used in the property parser\n\t * is a simple way to hash the sequence of characters\n\t * taking into account the index they occur in\n\t * since any number of 3 character sequences could produce duplicates.\n\t *\n\t * On the other hand sequences that are directly tied to the index of the character\n\t * resolve a far more accurate measure, it's also faster\n\t * to evaluate one condition in a switch statement\n\t * than three in an if statement regardless of the added math.\n\t *\n\t * This allows the vendor prefixer to be both small and fast.\n\t */\n\n\tvar nullptn = /^\\0+/g /* matches leading null characters */\n\tvar formatptn = /[\\0\\r\\f]/g /* matches new line, null and formfeed characters */\n\tvar colonptn = /: */g /* splits animation rules */\n\tvar cursorptn = /zoo|gra/ /* assert cursor varient */\n\tvar transformptn = /([,: ])(transform)/g /* vendor prefix transform, older webkit */\n\tvar animationptn = /,+\\s*(?![^(]*[)])/g /* splits multiple shorthand notation animations */\n\tvar propertiesptn = / +\\s*(?![^(]*[)])/g /* animation properties */\n\tvar elementptn = / *[\\0] */g /* selector elements */\n\tvar selectorptn = /,\\r+?/g /* splits selectors */\n\tvar andptn = /([\\t\\r\\n ])*\\f?&/g /* match & */\n\tvar escapeptn = /:global\\(((?:[^\\(\\)\\[\\]]*|\\[.*\\]|\\([^\\(\\)]*\\))*)\\)/g /* matches :global(.*) */\n\tvar invalidptn = /\\W+/g /* removes invalid characters from keyframes */\n\tvar keyframeptn = /@(k\\w+)\\s*(\\S*)\\s*/ /* matches @keyframes $1 */\n\tvar plcholdrptn = /::(place)/g /* match ::placeholder varient */\n\tvar readonlyptn = /:(read-only)/g /* match :read-only varient */\n\tvar beforeptn = /\\s+(?=[{\\];=:>])/g /* matches \\s before ] ; = : */\n\tvar afterptn = /([[}=:>])\\s+/g /* matches \\s after characters [ } = : */\n\tvar tailptn = /(\\{[^{]+?);(?=\\})/g /* matches tail semi-colons ;} */\n\tvar whiteptn = /\\s{2,}/g /* matches repeating whitespace */\n\tvar pseudoptn = /([^\\(])(:+) */g /* pseudo element */\n\tvar writingptn = /[svh]\\w+-[tblr]{2}/ /* match writing mode property values */\n\tvar gradientptn = /([\\w-]+t\\()/g /* match *gradient property */\n\tvar supportsptn = /\\(\\s*(.*)\\s*\\)/g /* match supports (groups) */\n\tvar propertyptn = /([\\s\\S]*?);/g /* match properties leading semicolon */\n\tvar selfptn = /-self|flex-/g /* match flex- and -self in align-self: flex-*; */\n\tvar pseudofmt = /[^]*?(:[rp][el]a[\\w-]+)[^]*/ /* extrats :readonly or :placholder from selector */\n\tvar trimptn = /[ \\t]+$/ /* match tail whitspace */\n\tvar dimensionptn = /stretch|:\\s*\\w+\\-(?:conte|avail)/ /* match max/min/fit-content, fill-available */\n\tvar imgsrcptn = /([^-])(image-set\\()/\n\n\t/* vendors */\n\tvar webkit = '-webkit-'\n\tvar moz = '-moz-'\n\tvar ms = '-ms-'\n\n\t/* character codes */\n\tvar SEMICOLON = 59 /* ; */\n\tvar CLOSEBRACES = 125 /* } */\n\tvar OPENBRACES = 123 /* { */\n\tvar OPENPARENTHESES = 40 /* ( */\n\tvar CLOSEPARENTHESES = 41 /* ) */\n\tvar OPENBRACKET = 91 /* [ */\n\tvar CLOSEBRACKET = 93 /* ] */\n\tvar NEWLINE = 10 /* \\n */\n\tvar CARRIAGE = 13 /* \\r */\n\tvar TAB = 9 /* \\t */\n\tvar AT = 64 /* @ */\n\tvar SPACE = 32 /*   */\n\tvar AND = 38 /* & */\n\tvar DASH = 45 /* - */\n\tvar UNDERSCORE = 95 /* _ */\n\tvar STAR = 42 /* * */\n\tvar COMMA = 44 /* , */\n\tvar COLON = 58 /* : */\n\tvar SINGLEQUOTE = 39 /* ' */\n\tvar DOUBLEQUOTE = 34 /* \" */\n\tvar FOWARDSLASH = 47 /* / */\n\tvar GREATERTHAN = 62 /* > */\n\tvar PLUS = 43 /* + */\n\tvar TILDE = 126 /* ~ */\n\tvar NULL = 0 /* \\0 */\n\tvar FORMFEED = 12 /* \\f */\n\tvar VERTICALTAB = 11 /* \\v */\n\n\t/* special identifiers */\n\tvar KEYFRAME = 107 /* k */\n\tvar MEDIA = 109 /* m */\n\tvar SUPPORTS = 115 /* s */\n\tvar PLACEHOLDER = 112 /* p */\n\tvar READONLY = 111 /* o */\n\tvar IMPORT = 105 /* <at>i */\n\tvar CHARSET = 99 /* <at>c */\n\tvar DOCUMENT = 100 /* <at>d */\n\tvar PAGE = 112 /* <at>p */\n\n\tvar column = 1 /* current column */\n\tvar line = 1 /* current line numebr */\n\tvar pattern = 0 /* :pattern */\n\n\tvar cascade = 1 /* #id h1 h2 vs h1#id h2#id  */\n\tvar prefix = 1 /* vendor prefix */\n\tvar escape = 1 /* escape :global() pattern */\n\tvar compress = 0 /* compress output */\n\tvar semicolon = 0 /* no/semicolon option */\n\tvar preserve = 0 /* preserve empty selectors */\n\n\t/* empty reference */\n\tvar array = []\n\n\t/* plugins */\n\tvar plugins = []\n\tvar plugged = 0\n\tvar should = null\n\n\t/* plugin context */\n\tvar POSTS = -2\n\tvar PREPS = -1\n\tvar UNKWN = 0\n\tvar PROPS = 1\n\tvar BLCKS = 2\n\tvar ATRUL = 3\n\n\t/* plugin newline context */\n\tvar unkwn = 0\n\n\t/* keyframe animation */\n\tvar keyed = 1\n\tvar key = ''\n\n\t/* selector namespace */\n\tvar nscopealt = ''\n\tvar nscope = ''\n\n\t/**\n\t * Compile\n\t *\n\t * @param {Array<string>} parent\n\t * @param {Array<string>} current\n\t * @param {string} body\n\t * @param {number} id\n\t * @param {number} depth\n\t * @return {string}\n\t */\n\tfunction compile (parent, current, body, id, depth) {\n\t\tvar bracket = 0 /* brackets [] */\n\t\tvar comment = 0 /* comments /* // or /* */\n\t\tvar parentheses = 0 /* functions () */\n\t\tvar quote = 0 /* quotes '', \"\" */\n\n\t\tvar first = 0 /* first character code */\n\t\tvar second = 0 /* second character code */\n\t\tvar code = 0 /* current character code */\n\t\tvar tail = 0 /* previous character code */\n\t\tvar trail = 0 /* character before previous code */\n\t\tvar peak = 0 /* previous non-whitespace code */\n\n\t\tvar counter = 0 /* count sequence termination */\n\t\tvar context = 0 /* track current context */\n\t\tvar atrule = 0 /* track @at-rule context */\n\t\tvar pseudo = 0 /* track pseudo token index */\n\t\tvar caret = 0 /* current character index */\n\t\tvar format = 0 /* control character formating context */\n\t\tvar insert = 0 /* auto semicolon insertion */\n\t\tvar invert = 0 /* inverted selector pattern */\n\t\tvar length = 0 /* generic length address */\n\t\tvar eof = body.length /* end of file(length) */\n\t\tvar eol = eof - 1 /* end of file(characters) */\n\n\t\tvar char = '' /* current character */\n\t\tvar chars = '' /* current buffer of characters */\n\t\tvar child = '' /* next buffer of characters */\n\t\tvar out = '' /* compiled body */\n\t\tvar children = '' /* compiled children */\n\t\tvar flat = '' /* compiled leafs */\n\t\tvar selector /* generic selector address */\n\t\tvar result /* generic address */\n\n\t\t// ...build body\n\t\twhile (caret < eof) {\n\t\t\tcode = body.charCodeAt(caret)\n\n\t\t\t// eof varient\n\t\t\tif (caret === eol) {\n\t\t\t\t// last character + noop context, add synthetic padding for noop context to terminate\n\t\t\t\tif (comment + quote + parentheses + bracket !== 0) {\n\t\t\t\t\tif (comment !== 0) {\n\t\t\t\t\t\tcode = comment === FOWARDSLASH ? NEWLINE : FOWARDSLASH\n\t\t\t\t\t}\n\n\t\t\t\t\tquote = parentheses = bracket = 0\n\t\t\t\t\teof++\n\t\t\t\t\teol++\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (comment + quote + parentheses + bracket === 0) {\n\t\t\t\t// eof varient\n\t\t\t\tif (caret === eol) {\n\t\t\t\t\tif (format > 0) {\n\t\t\t\t\t\tchars = chars.replace(formatptn, '')\n\t\t\t\t\t}\n\n\t\t\t\t\tif (chars.trim().length > 0) {\n\t\t\t\t\t\tswitch (code) {\n\t\t\t\t\t\t\tcase SPACE:\n\t\t\t\t\t\t\tcase TAB:\n\t\t\t\t\t\t\tcase SEMICOLON:\n\t\t\t\t\t\t\tcase CARRIAGE:\n\t\t\t\t\t\t\tcase NEWLINE: {\n\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\tchars += body.charAt(caret)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tcode = SEMICOLON\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// auto semicolon insertion\n\t\t\t\tif (insert === 1) {\n\t\t\t\t\tswitch (code) {\n\t\t\t\t\t\t// false flags\n\t\t\t\t\t\tcase OPENBRACES:\n\t\t\t\t\t\tcase CLOSEBRACES:\n\t\t\t\t\t\tcase SEMICOLON:\n\t\t\t\t\t\tcase DOUBLEQUOTE:\n\t\t\t\t\t\tcase SINGLEQUOTE:\n\t\t\t\t\t\tcase OPENPARENTHESES:\n\t\t\t\t\t\tcase CLOSEPARENTHESES:\n\t\t\t\t\t\tcase COMMA: {\n\t\t\t\t\t\t\tinsert = 0\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// ignore\n\t\t\t\t\t\tcase TAB:\n\t\t\t\t\t\tcase CARRIAGE:\n\t\t\t\t\t\tcase NEWLINE:\n\t\t\t\t\t\tcase SPACE: {\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// valid\n\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\tinsert = 0\n\t\t\t\t\t\t\tlength = caret\n\t\t\t\t\t\t\tfirst = code\n\t\t\t\t\t\t\tcaret--\n\t\t\t\t\t\t\tcode = SEMICOLON\n\n\t\t\t\t\t\t\twhile (length < eof) {\n\t\t\t\t\t\t\t\tswitch (body.charCodeAt(length++)) {\n\t\t\t\t\t\t\t\t\tcase NEWLINE:\n\t\t\t\t\t\t\t\t\tcase CARRIAGE:\n\t\t\t\t\t\t\t\t\tcase SEMICOLON: {\n\t\t\t\t\t\t\t\t\t\t++caret\n\t\t\t\t\t\t\t\t\t\tcode = first\n\t\t\t\t\t\t\t\t\t\tlength = eof\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tcase COLON: {\n\t\t\t\t\t\t\t\t\t\tif (format > 0) {\n\t\t\t\t\t\t\t\t\t\t\t++caret\n\t\t\t\t\t\t\t\t\t\t\tcode = first\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tcase OPENBRACES: {\n\t\t\t\t\t\t\t\t\t\tlength = eof\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// token varient\n\t\t\t\tswitch (code) {\n\t\t\t\t\tcase OPENBRACES: {\n\t\t\t\t\t\tchars = chars.trim()\n\t\t\t\t\t\tfirst = chars.charCodeAt(0)\n\t\t\t\t\t\tcounter = 1\n\t\t\t\t\t\tlength = ++caret\n\n\t\t\t\t\t\twhile (caret < eof) {\n\t\t\t\t\t\t\tswitch (code = body.charCodeAt(caret)) {\n\t\t\t\t\t\t\t\tcase OPENBRACES: {\n\t\t\t\t\t\t\t\t\tcounter++\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcase CLOSEBRACES: {\n\t\t\t\t\t\t\t\t\tcounter--\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcase FOWARDSLASH: {\n\t\t\t\t\t\t\t\t\tswitch (second = body.charCodeAt(caret + 1)) {\n\t\t\t\t\t\t\t\t\t\t// /*, //\n\t\t\t\t\t\t\t\t\t\tcase STAR:\n\t\t\t\t\t\t\t\t\t\tcase FOWARDSLASH: {\n\t\t\t\t\t\t\t\t\t\t\tcaret = delimited(second, caret, eol, body)\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// given \"[\" === 91 & \"]\" === 93 hence forth 91 + 1 + 1 === 93\n\t\t\t\t\t\t\t\tcase OPENBRACKET: {\n\t\t\t\t\t\t\t\t\tcode++\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// given \"(\" === 40 & \")\" === 41 hence forth 40 + 1 === 41\n\t\t\t\t\t\t\t\tcase OPENPARENTHESES: {\n\t\t\t\t\t\t\t\t\tcode++\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// quote tail delimiter is identical to the head delimiter hence noop,\n\t\t\t\t\t\t\t\t// fallthrough clauses have been shifted to the correct tail delimiter\n\t\t\t\t\t\t\t\tcase DOUBLEQUOTE:\n\t\t\t\t\t\t\t\tcase SINGLEQUOTE: {\n\t\t\t\t\t\t\t\t\twhile (caret++ < eol) {\n\t\t\t\t\t\t\t\t\t\tif (body.charCodeAt(caret) === code) {\n\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (counter === 0) {\n\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tcaret++\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tchild = body.substring(length, caret)\n\n\t\t\t\t\t\tif (first === NULL) {\n\t\t\t\t\t\t\tfirst = (chars = chars.replace(nullptn, '').trim()).charCodeAt(0)\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tswitch (first) {\n\t\t\t\t\t\t\t// @at-rule\n\t\t\t\t\t\t\tcase AT: {\n\t\t\t\t\t\t\t\tif (format > 0) {\n\t\t\t\t\t\t\t\t\tchars = chars.replace(formatptn, '')\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tsecond = chars.charCodeAt(1)\n\n\t\t\t\t\t\t\t\tswitch (second) {\n\t\t\t\t\t\t\t\t\tcase DOCUMENT:\n\t\t\t\t\t\t\t\t\tcase MEDIA:\n\t\t\t\t\t\t\t\t\tcase SUPPORTS:\n\t\t\t\t\t\t\t\t\tcase DASH: {\n\t\t\t\t\t\t\t\t\t\tselector = current\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t\t\tselector = array\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tchild = compile(current, selector, child, second, depth+1)\n\t\t\t\t\t\t\t\tlength = child.length\n\n\t\t\t\t\t\t\t\t// preserve empty @at-rule\n\t\t\t\t\t\t\t\tif (preserve > 0 && length === 0) {\n\t\t\t\t\t\t\t\t\tlength = chars.length\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// execute plugins, @at-rule context\n\t\t\t\t\t\t\t\tif (plugged > 0) {\n\t\t\t\t\t\t\t\t\tselector = select(array, chars, invert)\n\t\t\t\t\t\t\t\t\tresult = proxy(ATRUL, child, selector, current, line, column, length, second, depth, id)\n\t\t\t\t\t\t\t\t\tchars = selector.join('')\n\n\t\t\t\t\t\t\t\t\tif (result !== void 0) {\n\t\t\t\t\t\t\t\t\t\tif ((length = (child = result.trim()).length) === 0) {\n\t\t\t\t\t\t\t\t\t\t\tsecond = 0\n\t\t\t\t\t\t\t\t\t\t\tchild = ''\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tif (length > 0) {\n\t\t\t\t\t\t\t\t\tswitch (second) {\n\t\t\t\t\t\t\t\t\t\tcase SUPPORTS: {\n\t\t\t\t\t\t\t\t\t\t\tchars = chars.replace(supportsptn, supports)\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tcase DOCUMENT:\n\t\t\t\t\t\t\t\t\t\tcase MEDIA:\n\t\t\t\t\t\t\t\t\t\tcase DASH: {\n\t\t\t\t\t\t\t\t\t\t\tchild = chars + '{' + child + '}'\n\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tcase KEYFRAME: {\n\t\t\t\t\t\t\t\t\t\t\tchars = chars.replace(keyframeptn, '$1 $2' + (keyed > 0 ? key : ''))\n\t\t\t\t\t\t\t\t\t\t\tchild = chars + '{' + child + '}'\n\n\t\t\t\t\t\t\t\t\t\t\tif (prefix === 1 || (prefix === 2 && vendor('@'+child, 3))) {\n\t\t\t\t\t\t\t\t\t\t\t\tchild = '@' + webkit + child + '@' + child\n\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\tchild = '@' + child\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t\t\t\tchild = chars + child\n\n\t\t\t\t\t\t\t\t\t\t\tif (id === PAGE) {\n\t\t\t\t\t\t\t\t\t\t\t\tchild = (out += child, '')\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tchild = ''\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// selector\n\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\tchild = compile(current, select(current, chars, invert), child, id, depth+1)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tchildren += child\n\n\t\t\t\t\t\t// reset\n\t\t\t\t\t\tcontext = 0\n\t\t\t\t\t\tinsert = 0\n\t\t\t\t\t\tpseudo = 0\n\t\t\t\t\t\tformat = 0\n\t\t\t\t\t\tinvert = 0\n\t\t\t\t\t\tatrule = 0\n\t\t\t\t\t\tchars = ''\n\t\t\t\t\t\tchild = ''\n\t\t\t\t\t\tcode = body.charCodeAt(++caret)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\tcase CLOSEBRACES:\n\t\t\t\t\tcase SEMICOLON: {\n\t\t\t\t\t\tchars = (format > 0 ? chars.replace(formatptn, '') : chars).trim()\n\n\t\t\t\t\t\tif ((length = chars.length) > 1) {\n\t\t\t\t\t\t\t// monkey-patch missing colon\n\t\t\t\t\t\t\tif (pseudo === 0) {\n\t\t\t\t\t\t\t\tfirst = chars.charCodeAt(0)\n\n\t\t\t\t\t\t\t\t// first character is a letter or dash, buffer has a space character\n\t\t\t\t\t\t\t\tif ((first === DASH || first > 96 && first < 123)) {\n\t\t\t\t\t\t\t\t\tlength = (chars = chars.replace(' ', ':')).length\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// execute plugins, property context\n\t\t\t\t\t\t\tif (plugged > 0) {\n\t\t\t\t\t\t\t\tif ((result = proxy(PROPS, chars, current, parent, line, column, out.length, id, depth, id)) !== void 0) {\n\t\t\t\t\t\t\t\t\tif ((length = (chars = result.trim()).length) === 0) {\n\t\t\t\t\t\t\t\t\t\tchars = '\\0\\0'\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tfirst = chars.charCodeAt(0)\n\t\t\t\t\t\t\tsecond = chars.charCodeAt(1)\n\n\t\t\t\t\t\t\tswitch (first) {\n\t\t\t\t\t\t\t\tcase NULL: {\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcase AT: {\n\t\t\t\t\t\t\t\t\tif (second === IMPORT || second === CHARSET) {\n\t\t\t\t\t\t\t\t\t\tflat += chars + body.charAt(caret)\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t\tif (chars.charCodeAt(length-1) === COLON) {\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tout += property(chars, first, second, chars.charCodeAt(2))\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// reset\n\t\t\t\t\t\tcontext = 0\n\t\t\t\t\t\tinsert = 0\n\t\t\t\t\t\tpseudo = 0\n\t\t\t\t\t\tformat = 0\n\t\t\t\t\t\tinvert = 0\n\t\t\t\t\t\tchars = ''\n\t\t\t\t\t\tcode = body.charCodeAt(++caret)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// parse characters\n\t\t\tswitch (code) {\n\t\t\t\tcase CARRIAGE:\n\t\t\t\tcase NEWLINE: {\n\t\t\t\t\t// auto insert semicolon\n\t\t\t\t\tif (comment + quote + parentheses + bracket + semicolon === 0) {\n\t\t\t\t\t\t// valid non-whitespace characters that\n\t\t\t\t\t\t// may precede a newline\n\t\t\t\t\t\tswitch (peak) {\n\t\t\t\t\t\t\tcase CLOSEPARENTHESES:\n\t\t\t\t\t\t\tcase SINGLEQUOTE:\n\t\t\t\t\t\t\tcase DOUBLEQUOTE:\n\t\t\t\t\t\t\tcase AT:\n\t\t\t\t\t\t\tcase TILDE:\n\t\t\t\t\t\t\tcase GREATERTHAN:\n\t\t\t\t\t\t\tcase STAR:\n\t\t\t\t\t\t\tcase PLUS:\n\t\t\t\t\t\t\tcase FOWARDSLASH:\n\t\t\t\t\t\t\tcase DASH:\n\t\t\t\t\t\t\tcase COLON:\n\t\t\t\t\t\t\tcase COMMA:\n\t\t\t\t\t\t\tcase SEMICOLON:\n\t\t\t\t\t\t\tcase OPENBRACES:\n\t\t\t\t\t\t\tcase CLOSEBRACES: {\n\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t// current buffer has a colon\n\t\t\t\t\t\t\t\tif (pseudo > 0) {\n\t\t\t\t\t\t\t\t\tinsert = 1\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// terminate line comment\n\t\t\t\t\tif (comment === FOWARDSLASH) {\n\t\t\t\t\t\tcomment = 0\n\t\t\t\t\t} else if (cascade + context === 0 && id !== KEYFRAME && chars.length > 0) {\n\t\t\t\t\t\tformat = 1\n\t\t\t\t\t\tchars += '\\0'\n\t\t\t\t\t}\n\n\t\t\t\t\t// execute plugins, newline context\n\t\t\t\t\tif (plugged * unkwn > 0) {\n\t\t\t\t\t\tproxy(UNKWN, chars, current, parent, line, column, out.length, id, depth, id)\n\t\t\t\t\t}\n\n\t\t\t\t\t// next line, reset column position\n\t\t\t\t\tcolumn = 1\n\t\t\t\t\tline++\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t\tcase SEMICOLON:\n\t\t\t\tcase CLOSEBRACES: {\n\t\t\t\t\tif (comment + quote + parentheses + bracket === 0) {\n\t\t\t\t\t\tcolumn++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tdefault: {\n\t\t\t\t\t// increment column position\n\t\t\t\t\tcolumn++\n\n\t\t\t\t\t// current character\n\t\t\t\t\tchar = body.charAt(caret)\n\n\t\t\t\t\t// remove comments, escape functions, strings, attributes and prepare selectors\n\t\t\t\t\tswitch (code) {\n\t\t\t\t\t\tcase TAB:\n\t\t\t\t\t\tcase SPACE: {\n\t\t\t\t\t\t\tif (quote + bracket + comment === 0) {\n\t\t\t\t\t\t\t\tswitch (tail) {\n\t\t\t\t\t\t\t\t\tcase COMMA:\n\t\t\t\t\t\t\t\t\tcase COLON:\n\t\t\t\t\t\t\t\t\tcase TAB:\n\t\t\t\t\t\t\t\t\tcase SPACE: {\n\t\t\t\t\t\t\t\t\t\tchar = ''\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t\t\tif (code !== SPACE) {\n\t\t\t\t\t\t\t\t\t\t\tchar = ' '\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// escape breaking control characters\n\t\t\t\t\t\tcase NULL: {\n\t\t\t\t\t\t\tchar = '\\\\0'\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcase FORMFEED: {\n\t\t\t\t\t\t\tchar = '\\\\f'\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcase VERTICALTAB: {\n\t\t\t\t\t\t\tchar = '\\\\v'\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// &\n\t\t\t\t\t\tcase AND: {\n\t\t\t\t\t\t\t// inverted selector pattern i.e html &\n\t\t\t\t\t\t\tif (quote + comment + bracket === 0 && cascade > 0) {\n\t\t\t\t\t\t\t\tinvert = 1\n\t\t\t\t\t\t\t\tformat = 1\n\t\t\t\t\t\t\t\tchar = '\\f' + char\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// ::p<l>aceholder, l\n\t\t\t\t\t\t// :read-on<l>y, l\n\t\t\t\t\t\tcase 108: {\n\t\t\t\t\t\t\tif (quote + comment + bracket + pattern === 0 && pseudo > 0) {\n\t\t\t\t\t\t\t\tswitch (caret - pseudo) {\n\t\t\t\t\t\t\t\t\t// ::placeholder\n\t\t\t\t\t\t\t\t\tcase 2: {\n\t\t\t\t\t\t\t\t\t\tif (tail === PLACEHOLDER && body.charCodeAt(caret-3) === COLON) {\n\t\t\t\t\t\t\t\t\t\t\tpattern = tail\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t// :read-only\n\t\t\t\t\t\t\t\t\tcase 8: {\n\t\t\t\t\t\t\t\t\t\tif (trail === READONLY) {\n\t\t\t\t\t\t\t\t\t\t\tpattern = trail\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// :<pattern>\n\t\t\t\t\t\tcase COLON: {\n\t\t\t\t\t\t\tif (quote + comment + bracket === 0) {\n\t\t\t\t\t\t\t\tpseudo = caret\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// selectors\n\t\t\t\t\t\tcase COMMA: {\n\t\t\t\t\t\t\tif (comment + parentheses + quote + bracket === 0) {\n\t\t\t\t\t\t\t\tformat = 1\n\t\t\t\t\t\t\t\tchar += '\\r'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// quotes\n\t\t\t\t\t\tcase DOUBLEQUOTE:\n\t\t\t\t\t\tcase SINGLEQUOTE: {\n\t\t\t\t\t\t\tif (comment === 0) {\n\t\t\t\t\t\t\t\tquote = quote === code ? 0 : (quote === 0 ? code : quote)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// attributes\n\t\t\t\t\t\tcase OPENBRACKET: {\n\t\t\t\t\t\t\tif (quote + comment + parentheses === 0) {\n\t\t\t\t\t\t\t\tbracket++\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcase CLOSEBRACKET: {\n\t\t\t\t\t\t\tif (quote + comment + parentheses === 0) {\n\t\t\t\t\t\t\t\tbracket--\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// functions\n\t\t\t\t\t\tcase CLOSEPARENTHESES: {\n\t\t\t\t\t\t\tif (quote + comment + bracket === 0) {\n\t\t\t\t\t\t\t\tparentheses--\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcase OPENPARENTHESES: {\n\t\t\t\t\t\t\tif (quote + comment + bracket === 0) {\n\t\t\t\t\t\t\t\tif (context === 0) {\n\t\t\t\t\t\t\t\t\tswitch (tail*2 + trail*3) {\n\t\t\t\t\t\t\t\t\t\t// :matches\n\t\t\t\t\t\t\t\t\t\tcase 533: {\n\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t// :global, :not, :nth-child etc...\n\t\t\t\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t\t\t\tcounter = 0\n\t\t\t\t\t\t\t\t\t\t\tcontext = 1\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tparentheses++\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcase AT: {\n\t\t\t\t\t\t\tif (comment + parentheses + quote + bracket + pseudo + atrule === 0) {\n\t\t\t\t\t\t\t\tatrule = 1\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// block/line comments\n\t\t\t\t\t\tcase STAR:\n\t\t\t\t\t\tcase FOWARDSLASH: {\n\t\t\t\t\t\t\tif (quote + bracket + parentheses > 0) {\n\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tswitch (comment) {\n\t\t\t\t\t\t\t\t// initialize line/block comment context\n\t\t\t\t\t\t\t\tcase 0: {\n\t\t\t\t\t\t\t\t\tswitch (code*2 + body.charCodeAt(caret+1)*3) {\n\t\t\t\t\t\t\t\t\t\t// //\n\t\t\t\t\t\t\t\t\t\tcase 235: {\n\t\t\t\t\t\t\t\t\t\t\tcomment = FOWARDSLASH\n\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t// /*\n\t\t\t\t\t\t\t\t\t\tcase 220: {\n\t\t\t\t\t\t\t\t\t\t\tlength = caret\n\t\t\t\t\t\t\t\t\t\t\tcomment = STAR\n\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// end block comment context\n\t\t\t\t\t\t\t\tcase STAR: {\n\t\t\t\t\t\t\t\t\tif (code === FOWARDSLASH && tail === STAR && length + 2 !== caret) {\n\t\t\t\t\t\t\t\t\t\t// /*<!> ... */, !\n\t\t\t\t\t\t\t\t\t\tif (body.charCodeAt(length+2) === 33) {\n\t\t\t\t\t\t\t\t\t\t\tout += body.substring(length, caret+1)\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tchar = ''\n\t\t\t\t\t\t\t\t\t\tcomment = 0\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// ignore comment blocks\n\t\t\t\t\tif (comment === 0) {\n\t\t\t\t\t\t// aggressive isolation mode, divide each individual selector\n\t\t\t\t\t\t// including selectors in :not function but excluding selectors in :global function\n\t\t\t\t\t\tif (cascade + quote + bracket + atrule === 0 && id !== KEYFRAME && code !== SEMICOLON) {\n\t\t\t\t\t\t\tswitch (code) {\n\t\t\t\t\t\t\t\tcase COMMA:\n\t\t\t\t\t\t\t\tcase TILDE:\n\t\t\t\t\t\t\t\tcase GREATERTHAN:\n\t\t\t\t\t\t\t\tcase PLUS:\n\t\t\t\t\t\t\t\tcase CLOSEPARENTHESES:\n\t\t\t\t\t\t\t\tcase OPENPARENTHESES: {\n\t\t\t\t\t\t\t\t\tif (context === 0) {\n\t\t\t\t\t\t\t\t\t\t// outside of an isolated context i.e nth-child(<...>)\n\t\t\t\t\t\t\t\t\t\tswitch (tail) {\n\t\t\t\t\t\t\t\t\t\t\tcase TAB:\n\t\t\t\t\t\t\t\t\t\t\tcase SPACE:\n\t\t\t\t\t\t\t\t\t\t\tcase NEWLINE:\n\t\t\t\t\t\t\t\t\t\t\tcase CARRIAGE: {\n\t\t\t\t\t\t\t\t\t\t\t\tchar = char + '\\0'\n\t\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t\t\t\t\tchar = '\\0' + char + (code === COMMA ? '' : '\\0')\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tformat = 1\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t// within an isolated context, sleep untill it's terminated\n\t\t\t\t\t\t\t\t\t\tswitch (code) {\n\t\t\t\t\t\t\t\t\t\t\tcase OPENPARENTHESES: {\n\t\t\t\t\t\t\t\t\t\t\t\t// :globa<l>(\n\t\t\t\t\t\t\t\t\t\t\t\tif (pseudo + 7 === caret && tail === 108) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tpseudo = 0\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\tcontext = ++counter\n\t\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tcase CLOSEPARENTHESES: {\n\t\t\t\t\t\t\t\t\t\t\t\tif ((context = --counter) === 0) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tformat = 1\n\t\t\t\t\t\t\t\t\t\t\t\t\tchar += '\\0'\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcase TAB:\n\t\t\t\t\t\t\t\tcase SPACE: {\n\t\t\t\t\t\t\t\t\tswitch (tail) {\n\t\t\t\t\t\t\t\t\t\tcase NULL:\n\t\t\t\t\t\t\t\t\t\tcase OPENBRACES:\n\t\t\t\t\t\t\t\t\t\tcase CLOSEBRACES:\n\t\t\t\t\t\t\t\t\t\tcase SEMICOLON:\n\t\t\t\t\t\t\t\t\t\tcase COMMA:\n\t\t\t\t\t\t\t\t\t\tcase FORMFEED:\n\t\t\t\t\t\t\t\t\t\tcase TAB:\n\t\t\t\t\t\t\t\t\t\tcase SPACE:\n\t\t\t\t\t\t\t\t\t\tcase NEWLINE:\n\t\t\t\t\t\t\t\t\t\tcase CARRIAGE: {\n\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t\t\t\t// ignore in isolated contexts\n\t\t\t\t\t\t\t\t\t\t\tif (context === 0) {\n\t\t\t\t\t\t\t\t\t\t\t\tformat = 1\n\t\t\t\t\t\t\t\t\t\t\t\tchar += '\\0'\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// concat buffer of characters\n\t\t\t\t\t\tchars += char\n\n\t\t\t\t\t\t// previous non-whitespace character code\n\t\t\t\t\t\tif (code !== SPACE && code !== TAB) {\n\t\t\t\t\t\t\tpeak = code\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// tail character codes\n\t\t\ttrail = tail\n\t\t\ttail = code\n\n\t\t\t// visit every character\n\t\t\tcaret++\n\t\t}\n\n\t\tlength = out.length\n\n\t\t// preserve empty selector\n \t\tif (preserve > 0) {\n \t\t\tif (length === 0 && children.length === 0 && (current[0].length === 0) === false) {\n \t\t\t\tif (id !== MEDIA || (current.length === 1 && (cascade > 0 ? nscopealt : nscope) === current[0])) {\n\t\t\t\t\tlength = current.join(',').length + 2\n \t\t\t\t}\n \t\t\t}\n\t\t}\n\n\t\tif (length > 0) {\n\t\t\t// cascade isolation mode?\n\t\t\tselector = cascade === 0 && id !== KEYFRAME ? isolate(current) : current\n\n\t\t\t// execute plugins, block context\n\t\t\tif (plugged > 0) {\n\t\t\t\tresult = proxy(BLCKS, out, selector, parent, line, column, length, id, depth, id)\n\n\t\t\t\tif (result !== void 0 && (out = result).length === 0) {\n\t\t\t\t\treturn flat + out + children\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tout = selector.join(',') + '{' + out + '}'\n\n\t\t\tif (prefix*pattern !== 0) {\n\t\t\t\tif (prefix === 2 && !vendor(out, 2))\n\t\t\t\t\tpattern = 0\n\n\t\t\t\tswitch (pattern) {\n\t\t\t\t\t// ::read-only\n\t\t\t\t\tcase READONLY: {\n\t\t\t\t\t\tout = out.replace(readonlyptn, ':'+moz+'$1')+out\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\t// ::placeholder\n\t\t\t\t\tcase PLACEHOLDER: {\n\t\t\t\t\t\tout = (\n\t\t\t\t\t\t\tout.replace(plcholdrptn, '::' + webkit + 'input-$1') +\n\t\t\t\t\t\t\tout.replace(plcholdrptn, '::' + moz + '$1') +\n\t\t\t\t\t\t\tout.replace(plcholdrptn, ':' + ms + 'input-$1') + out\n\t\t\t\t\t\t)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tpattern = 0\n\t\t\t}\n\t\t}\n\n\t\treturn flat + out + children\n\t}\n\n\t/**\n\t * Select\n\t *\n\t * @param {Array<string>} parent\n\t * @param {string} current\n\t * @param {number} invert\n\t * @return {Array<string>}\n\t */\n\tfunction select (parent, current, invert) {\n\t\tvar selectors = current.trim().split(selectorptn)\n\t\tvar out = selectors\n\n\t\tvar length = selectors.length\n\t\tvar l = parent.length\n\n\t\tswitch (l) {\n\t\t\t// 0-1 parent selectors\n\t\t\tcase 0:\n\t\t\tcase 1: {\n\t\t\t\tfor (var i = 0, selector = l === 0 ? '' : parent[0] + ' '; i < length; ++i) {\n\t\t\t\t\tout[i] = scope(selector, out[i], invert, l).trim()\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t}\n\t\t\t// >2 parent selectors, nested\n\t\t\tdefault: {\n\t\t\t\tfor (var i = 0, j = 0, out = []; i < length; ++i) {\n\t\t\t\t\tfor (var k = 0; k < l; ++k) {\n\t\t\t\t\t\tout[j++] = scope(parent[k] + ' ', selectors[i], invert, l).trim()\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn out\n\t}\n\n\t/**\n\t * Scope\n\t *\n\t * @param {string} parent\n\t * @param {string} current\n\t * @param {number} invert\n\t * @param {number} level\n\t * @return {string}\n\t */\n\tfunction scope (parent, current, invert, level) {\n\t\tvar selector = current\n\t\tvar code = selector.charCodeAt(0)\n\n\t\t// trim leading whitespace\n\t\tif (code < 33) {\n\t\t\tcode = (selector = selector.trim()).charCodeAt(0)\n\t\t}\n\n\t\tswitch (code) {\n\t\t\t// &\n\t\t\tcase AND: {\n\t\t\t\tswitch (cascade + level) {\n\t\t\t\t\tcase 0:\n\t\t\t\t\tcase 1: {\n\t\t\t\t\t\tif (parent.trim().length === 0) {\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tdefault: {\n\t\t\t\t\t\treturn selector.replace(andptn, '$1'+parent.trim())\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t}\n\t\t\t// :\n\t\t\tcase COLON: {\n\t\t\t\tswitch (selector.charCodeAt(1)) {\n\t\t\t\t\t// g in :global\n\t\t\t\t\tcase 103: {\n\t\t\t\t\t\tif (escape > 0 && cascade > 0) {\n\t\t\t\t\t\t\treturn selector.replace(escapeptn, '$1').replace(andptn, '$1'+nscope)\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\tdefault: {\n\t\t\t\t\t\t// :hover\n\t\t\t\t\t\treturn parent.trim() + selector.replace(andptn, '$1'+parent.trim())\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tdefault: {\n\t\t\t\t// html &\n\t\t\t\tif (invert*cascade > 0 && selector.indexOf('\\f') > 0) {\n\t\t\t\t\treturn selector.replace(andptn, (parent.charCodeAt(0) === COLON ? '' : '$1')+parent.trim())\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn parent + selector\n\t}\n\n\t/**\n\t * Property\n\t *\n\t * @param {string} input\n\t * @param {number} first\n\t * @param {number} second\n\t * @param {number} third\n\t * @return {string}\n\t */\n\tfunction property (input, first, second, third) {\n\t\tvar index = 0\n\t\tvar out = input + ';'\n\t\tvar hash = (first*2) + (second*3) + (third*4)\n\t\tvar cache\n\n\t\t// animation: a, n, i characters\n\t\tif (hash === 944) {\n\t\t\treturn animation(out)\n\t\t} else if (prefix === 0 || (prefix === 2 && !vendor(out, 1))) {\n\t\t\treturn out\n\t\t}\n\n\t\t// vendor prefix\n\t\tswitch (hash) {\n\t\t\t// text-decoration/text-size-adjust/text-shadow/text-align/text-transform: t, e, x\n\t\t\tcase 1015: {\n\t\t\t\t// text-shadow/text-align/text-transform, a\n\t\t\t\treturn out.charCodeAt(10) === 97 ? webkit + out + out : out\n\t\t\t}\n\t\t\t// filter/fill f, i, l\n\t\t\tcase 951: {\n\t\t\t\t// filter, t\n\t\t\t\treturn out.charCodeAt(3) === 116 ? webkit + out + out : out\n\t\t\t}\n\t\t\t// color/column, c, o, l\n\t\t\tcase 963: {\n\t\t\t\t// column, n\n\t\t\t\treturn out.charCodeAt(5) === 110 ? webkit + out + out : out\n\t\t\t}\n\t\t\t// box-decoration-break, b, o, x\n\t\t\tcase 1009: {\n\t\t\t\tif (out.charCodeAt(4) !== 100) {\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t\t// mask, m, a, s\n\t\t\t// clip-path, c, l, i\n\t\t\tcase 969:\n\t\t\tcase 942: {\n\t\t\t\treturn webkit + out + out\n\t\t\t}\n\t\t\t// appearance: a, p, p\n\t\t\tcase 978: {\n\t\t\t\treturn webkit + out + moz + out + out\n\t\t\t}\n\t\t\t// hyphens: h, y, p\n\t\t\t// user-select: u, s, e\n\t\t\tcase 1019:\n\t\t\tcase 983: {\n\t\t\t\treturn webkit + out + moz + out + ms + out + out\n\t\t\t}\n\t\t\t// background/backface-visibility, b, a, c\n\t\t\tcase 883: {\n\t\t\t\t// backface-visibility, -\n\t\t\t\tif (out.charCodeAt(8) === DASH) {\n\t\t\t\t\treturn webkit + out + out\n\t\t\t\t}\n\n\t\t\t\t// image-set(...)\n\t\t\t\tif (out.indexOf('image-set(', 11) > 0) {\n\t\t\t\t\treturn out.replace(imgsrcptn, '$1'+webkit+'$2') + out\n\t\t\t\t}\n\n\t\t\t\treturn out\n\t\t\t}\n\t\t\t// flex: f, l, e\n\t\t\tcase 932: {\n\t\t\t\tif (out.charCodeAt(4) === DASH) {\n\t\t\t\t\tswitch (out.charCodeAt(5)) {\n\t\t\t\t\t\t// flex-grow, g\n\t\t\t\t\t\tcase 103: {\n\t\t\t\t\t\t\treturn webkit + 'box-' + out.replace('-grow', '') + webkit + out + ms + out.replace('grow', 'positive') + out\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// flex-shrink, s\n\t\t\t\t\t\tcase 115: {\n\t\t\t\t\t\t\treturn webkit + out + ms + out.replace('shrink', 'negative') + out\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// flex-basis, b\n\t\t\t\t\t\tcase 98: {\n\t\t\t\t\t\t\treturn webkit + out + ms + out.replace('basis', 'preferred-size') + out\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn webkit + out + ms + out + out\n\t\t\t}\n\t\t\t// order: o, r, d\n\t\t\tcase 964: {\n\t\t\t\treturn webkit + out + ms + 'flex' + '-' + out + out\n\t\t\t}\n\t\t\t// justify-items/justify-content, j, u, s\n\t\t\tcase 1023: {\n\t\t\t\t// justify-content, c\n\t\t\t\tif (out.charCodeAt(8) !== 99) {\n\t\t\t\t\tbreak\n\t\t\t\t}\n\n\t\t\t\tcache = out.substring(out.indexOf(':', 15)).replace('flex-', '').replace('space-between', 'justify')\n\t\t\t\treturn webkit + 'box-pack' + cache + webkit + out + ms + 'flex-pack' + cache + out\n\t\t\t}\n\t\t\t// cursor, c, u, r\n\t\t\tcase 1005: {\n\t\t\t\treturn cursorptn.test(out) ? out.replace(colonptn, ':' + webkit) + out.replace(colonptn, ':' + moz) + out : out\n\t\t\t}\n\t\t\t// writing-mode, w, r, i\n\t\t\tcase 1000: {\n\t\t\t\tcache = out.substring(13).trim()\n\t\t\t\tindex = cache.indexOf('-') + 1\n\n\t\t\t\tswitch (cache.charCodeAt(0)+cache.charCodeAt(index)) {\n\t\t\t\t\t// vertical-lr\n\t\t\t\t\tcase 226: {\n\t\t\t\t\t\tcache = out.replace(writingptn, 'tb')\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\t// vertical-rl\n\t\t\t\t\tcase 232: {\n\t\t\t\t\t\tcache = out.replace(writingptn, 'tb-rl')\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\t// horizontal-tb\n\t\t\t\t\tcase 220: {\n\t\t\t\t\t\tcache = out.replace(writingptn, 'lr')\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\tdefault: {\n\t\t\t\t\t\treturn out\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn webkit + out + ms + cache + out\n\t\t\t}\n\t\t\t// position: sticky\n\t\t\tcase 1017: {\n\t\t\t\tif (out.indexOf('sticky', 9) === -1) {\n\t\t\t\t\treturn out\n\t\t\t\t}\n\t\t\t}\n\t\t\t// display(flex/inline-flex/inline-box): d, i, s\n\t\t\tcase 975: {\n\t\t\t\tindex = (out = input).length - 10\n\t\t\t\tcache = (out.charCodeAt(index) === 33 ? out.substring(0, index) : out).substring(input.indexOf(':', 7) + 1).trim()\n\n\t\t\t\tswitch (hash = cache.charCodeAt(0) + (cache.charCodeAt(7)|0)) {\n\t\t\t\t\t// inline-\n\t\t\t\t\tcase 203: {\n\t\t\t\t\t\t// inline-box\n\t\t\t\t\t\tif (cache.charCodeAt(8) < 111) {\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t// inline-box/sticky\n\t\t\t\t\tcase 115: {\n\t\t\t\t\t\tout = out.replace(cache, webkit+cache)+';'+out\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\t// inline-flex\n\t\t\t\t\t// flex\n\t\t\t\t\tcase 207:\n\t\t\t\t\tcase 102: {\n\t\t\t\t\t\tout = (\n\t\t\t\t\t\t\tout.replace(cache, webkit+(hash > 102 ? 'inline-' : '')+'box')+';'+\n\t\t\t\t\t\t\tout.replace(cache, webkit+cache)+';'+\n\t\t\t\t\t\t\tout.replace(cache, ms+cache+'box')+';'+\n\t\t\t\t\t\t\tout\n\t\t\t\t\t\t)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn out + ';'\n\t\t\t}\n\t\t\t// align-items, align-center, align-self: a, l, i, -\n\t\t\tcase 938: {\n\t\t\t\tif (out.charCodeAt(5) === DASH) {\n\t\t\t\t\tswitch (out.charCodeAt(6)) {\n\t\t\t\t\t\t// align-items, i\n\t\t\t\t\t\tcase 105: {\n\t\t\t\t\t\t\tcache = out.replace('-items', '')\n\t\t\t\t\t\t\treturn webkit + out + webkit + 'box-' + cache + ms + 'flex-' + cache + out\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// align-self, s\n\t\t\t\t\t\tcase 115: {\n\t\t\t\t\t\t\treturn webkit + out + ms + 'flex-item-' + out.replace(selfptn, '') + out\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// align-content\n\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\treturn webkit + out + ms + 'flex-line-pack' + out.replace('align-content', '').replace(selfptn, '') + out\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t}\n\t\t\t// min/max\n\t\t\tcase 973:\n\t\t\tcase 989: {\n\t\t\t\t// min-/max- height/width/block-size/inline-size\n\t\t\t\tif (out.charCodeAt(3) !== DASH || out.charCodeAt(4) === 122) {\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t\t// height/width: min-content / width: max-content\n\t\t\tcase 931:\n\t\t\tcase 953: {\n\t\t\t\tif (dimensionptn.test(input) === true) {\n\t\t\t\t\t// stretch\n\t\t\t\t\tif ((cache = input.substring(input.indexOf(':') + 1)).charCodeAt(0) === 115)\n\t\t\t\t\t\treturn property(input.replace('stretch', 'fill-available'), first, second, third).replace(':fill-available', ':stretch')\n\t\t\t\t\telse\n\t\t\t\t\t\treturn out.replace(cache, webkit + cache) + out.replace(cache, moz + cache.replace('fill-', '')) + out\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t}\n\t\t\t// transform, transition: t, r, a\n\t\t\tcase 962: {\n\t\t\t\tout = webkit + out + (out.charCodeAt(5) === 102 ? ms + out : '') + out\n\n\t\t\t\t// transitions\n\t\t\t\tif (second + third === 211 && out.charCodeAt(13) === 105 && out.indexOf('transform', 10) > 0) {\n\t\t\t\t\treturn out.substring(0, out.indexOf(';', 27) + 1).replace(transformptn, '$1' + webkit + '$2') + out\n\t\t\t\t}\n\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\n\t\treturn out\n\t}\n\n\t/**\n\t * Vendor\n\t *\n\t * @param {string} content\n\t * @param {number} context\n\t * @return {boolean}\n\t */\n\tfunction vendor (content, context) {\n\t\tvar index = content.indexOf(context === 1 ? ':' : '{')\n\t\tvar key = content.substring(0, context !== 3 ? index : 10)\n\t\tvar value = content.substring(index + 1, content.length - 1)\n\n\t\treturn should(context !== 2 ? key : key.replace(pseudofmt, '$1'), value, context)\n\t}\n\n\t/**\n\t * Supports\n\t *\n\t * @param {string} match\n\t * @param {string} group\n\t * @return {string}\n\t */\n\tfunction supports (match, group) {\n\t\tvar out = property(group, group.charCodeAt(0), group.charCodeAt(1), group.charCodeAt(2))\n\n\t\treturn out !== group+';' ? out.replace(propertyptn, ' or ($1)').substring(4) : '('+group+')'\n\t}\n\n\t/**\n\t * Animation\n\t *\n\t * @param {string} input\n\t * @return {string}\n\t */\n\tfunction animation (input) {\n\t\tvar length = input.length\n\t\tvar index = input.indexOf(':', 9) + 1\n\t\tvar declare = input.substring(0, index).trim()\n\t\tvar out = input.substring(index, length-1).trim()\n\n\t\tswitch (input.charCodeAt(9)*keyed) {\n\t\t\tcase 0: {\n\t\t\t\tbreak\n\t\t\t}\n\t\t\t// animation-*, -\n\t\t\tcase DASH: {\n\t\t\t\t// animation-name, n\n\t\t\t\tif (input.charCodeAt(10) !== 110) {\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t\t// animation/animation-name\n\t\t\tdefault: {\n\t\t\t\t// split in case of multiple animations\n\t\t\t\tvar list = out.split((out = '', animationptn))\n\n\t\t\t\tfor (var i = 0, index = 0, length = list.length; i < length; index = 0, ++i) {\n\t\t\t\t\tvar value = list[i]\n\t\t\t\t\tvar items = value.split(propertiesptn)\n\n\t\t\t\t\twhile (value = items[index]) {\n\t\t\t\t\t\tvar peak = value.charCodeAt(0)\n\n\t\t\t\t\t\tif (keyed === 1 && (\n\t\t\t\t\t\t\t// letters\n\t\t\t\t\t\t\t(peak > AT && peak < 90) || (peak > 96 && peak < 123) || peak === UNDERSCORE ||\n\t\t\t\t\t\t\t// dash but not in sequence i.e --\n\t\t\t\t\t\t\t(peak === DASH && value.charCodeAt(1) !== DASH)\n\t\t\t\t\t\t)) {\n\t\t\t\t\t\t\t// not a number/function\n\t\t\t\t\t\t\tswitch (isNaN(parseFloat(value)) + (value.indexOf('(') !== -1)) {\n\t\t\t\t\t\t\t\tcase 1: {\n\t\t\t\t\t\t\t\t\tswitch (value) {\n\t\t\t\t\t\t\t\t\t\t// not a valid reserved keyword\n\t\t\t\t\t\t\t\t\t\tcase 'infinite': case 'alternate': case 'backwards': case 'running':\n\t\t\t\t\t\t\t\t\t\tcase 'normal': case 'forwards': case 'both': case 'none': case 'linear':\n\t\t\t\t\t\t\t\t\t\tcase 'ease': case 'ease-in': case 'ease-out': case 'ease-in-out':\n\t\t\t\t\t\t\t\t\t\tcase 'paused': case 'reverse': case 'alternate-reverse': case 'inherit':\n\t\t\t\t\t\t\t\t\t\tcase 'initial': case 'unset': case 'step-start': case 'step-end': {\n\t\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t\t\t\tvalue += key\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\titems[index++] = value\n\t\t\t\t\t}\n\n\t\t\t\t\tout += (i === 0 ? '' : ',') + items.join(' ')\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tout = declare + out + ';'\n\n\t\tif (prefix === 1 || (prefix === 2 && vendor(out, 1)))\n\t\t\treturn webkit + out + out\n\n\t\treturn out\n\t}\n\n\t/**\n\t * Isolate\n\t *\n\t * @param {Array<string>} current\n\t */\n\tfunction isolate (current) {\n\t\tfor (var i = 0, length = current.length, selector = Array(length), padding, element; i < length; ++i) {\n\t\t\t// split individual elements in a selector i.e h1 h2 === [h1, h2]\n\t\t\tvar elements = current[i].split(elementptn)\n\t\t\tvar out = ''\n\n\t\t\tfor (var j = 0, size = 0, tail = 0, code = 0, l = elements.length; j < l; ++j) {\n\t\t\t\t// empty element\n\t\t\t\tif ((size = (element = elements[j]).length) === 0 && l > 1) {\n\t\t\t\t\tcontinue\n\t\t\t\t}\n\n\t\t\t\ttail = out.charCodeAt(out.length-1)\n\t\t\t\tcode = element.charCodeAt(0)\n\t\t\t\tpadding = ''\n\n\t\t\t\tif (j !== 0) {\n\t\t\t\t\t// determine if we need padding\n\t\t\t\t\tswitch (tail) {\n\t\t\t\t\t\tcase STAR:\n\t\t\t\t\t\tcase TILDE:\n\t\t\t\t\t\tcase GREATERTHAN:\n\t\t\t\t\t\tcase PLUS:\n\t\t\t\t\t\tcase SPACE:\n\t\t\t\t\t\tcase OPENPARENTHESES:  {\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\tpadding = ' '\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tswitch (code) {\n\t\t\t\t\tcase AND: {\n\t\t\t\t\t\telement = padding + nscopealt\n\t\t\t\t\t}\n\t\t\t\t\tcase TILDE:\n\t\t\t\t\tcase GREATERTHAN:\n\t\t\t\t\tcase PLUS:\n\t\t\t\t\tcase SPACE:\n\t\t\t\t\tcase CLOSEPARENTHESES:\n\t\t\t\t\tcase OPENPARENTHESES: {\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\tcase OPENBRACKET: {\n\t\t\t\t\t\telement = padding + element + nscopealt\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\tcase COLON: {\n\t\t\t\t\t\tswitch (element.charCodeAt(1)*2 + element.charCodeAt(2)*3) {\n\t\t\t\t\t\t\t// :global\n\t\t\t\t\t\t\tcase 530: {\n\t\t\t\t\t\t\t\tif (escape > 0) {\n\t\t\t\t\t\t\t\t\telement = padding + element.substring(8, size - 1)\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// :hover, :nth-child(), ...\n\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\tif (j < 1 || elements[j-1].length < 1) {\n\t\t\t\t\t\t\t\t\telement = padding + nscopealt + element\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t\tcase COMMA: {\n\t\t\t\t\t\tpadding = ''\n\t\t\t\t\t}\n\t\t\t\t\tdefault: {\n\t\t\t\t\t\tif (size > 1 && element.indexOf(':') > 0) {\n\t\t\t\t\t\t\telement = padding + element.replace(pseudoptn, '$1' + nscopealt + '$2')\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\telement = padding + element + nscopealt\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tout += element\n\t\t\t}\n\n\t\t\tselector[i] = out.replace(formatptn, '').trim()\n\t\t}\n\n\t\treturn selector\n\t}\n\n\t/**\n\t * Proxy\n\t *\n\t * @param {number} context\n\t * @param {string} content\n\t * @param {Array<string>} selectors\n\t * @param {Array<string>} parents\n\t * @param {number} line\n\t * @param {number} column\n\t * @param {number} length\n\t * @param {number} id\n\t * @param {number} depth\n\t * @param {number} at\n\t * @return {(string|void|*)}\n\t */\n\tfunction proxy (context, content, selectors, parents, line, column, length, id, depth, at) {\n\t\tfor (var i = 0, out = content, next; i < plugged; ++i) {\n\t\t\tswitch (next = plugins[i].call(stylis, context, out, selectors, parents, line, column, length, id, depth, at)) {\n\t\t\t\tcase void 0:\n\t\t\t\tcase false:\n\t\t\t\tcase true:\n\t\t\t\tcase null: {\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t\tdefault: {\n\t\t\t\t\tout = next\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (out !== content) {\n\t\t  return out\n\t\t}\n\t}\n\n\t/**\n\t * @param {number} code\n\t * @param {number} index\n\t * @param {number} length\n\t * @param {string} body\n\t * @return {number}\n\t */\n\tfunction delimited (code, index, length, body) {\n\t\tfor (var i = index + 1; i < length; ++i) {\n\t\t\tswitch (body.charCodeAt(i)) {\n\t\t\t\t// /*\n\t\t\t\tcase FOWARDSLASH: {\n\t\t\t\t\tif (code === STAR) {\n\t\t\t\t\t\tif (body.charCodeAt(i - 1) === STAR &&  index + 2 !== i) {\n\t\t\t\t\t\t\treturn i + 1\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t\t// //\n\t\t\t\tcase NEWLINE: {\n\t\t\t\t\tif (code === FOWARDSLASH) {\n\t\t\t\t\t\treturn i + 1\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn i\n\t}\n\n\t/**\n\t * @param {number} type\n\t * @param {number} index\n\t * @param {number} length\n\t * @param {number} find\n\t * @param {string} body\n\t * @return {number}\n\t */\n\tfunction match (type, index, length, body) {\n\t\tfor (var i = index + 1; i < length; ++i) {\n\t\t\tswitch (body.charCodeAt(i)) {\n\t\t\t\tcase type: {\n\t\t\t\t\treturn i\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn i\n\t}\n\n\t/**\n\t * Minify\n\t *\n\t * @param {(string|*)} output\n\t * @return {string}\n\t */\n\tfunction minify (output) {\n\t\treturn output\n\t\t\t.replace(formatptn, '')\n\t\t\t.replace(beforeptn, '')\n\t\t\t.replace(afterptn, '$1')\n\t\t\t.replace(tailptn, '$1')\n\t\t\t.replace(whiteptn, ' ')\n\t}\n\n\t/**\n\t * Use\n\t *\n\t * @param {(Array<function(...?)>|function(...?)|number|void)?} plugin\n\t */\n\tfunction use (plugin) {\n\t\tswitch (plugin) {\n\t\t\tcase void 0:\n\t\t\tcase null: {\n\t\t\t\tplugged = plugins.length = 0\n\t\t\t\tbreak\n\t\t\t}\n\t\t\tdefault: {\n\t\t\t\tif (typeof plugin === 'function') {\n\t\t\t\t\tplugins[plugged++] = plugin\n\t\t\t\t}\telse if (typeof plugin === 'object') {\n\t\t\t\t\tfor (var i = 0, length = plugin.length; i < length; ++i) {\n\t\t\t\t\t\tuse(plugin[i])\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tunkwn = !!plugin|0\n\t\t\t\t}\n\t\t\t}\n \t\t}\n\n \t\treturn use\n\t}\n\n\t/**\n\t * Set\n\t *\n\t * @param {*} options\n\t */\n\tfunction set (options) {\n\t\tfor (var name in options) {\n\t\t\tvar value = options[name]\n\t\t\tswitch (name) {\n\t\t\t\tcase 'keyframe': keyed = value|0; break\n\t\t\t\tcase 'global': escape = value|0; break\n\t\t\t\tcase 'cascade': cascade = value|0; break\n\t\t\t\tcase 'compress': compress = value|0; break\n\t\t\t\tcase 'semicolon': semicolon = value|0; break\n\t\t\t\tcase 'preserve': preserve = value|0; break\n\t\t\t\tcase 'prefix':\n\t\t\t\t\tshould = null\n\n\t\t\t\t\tif (!value) {\n\t\t\t\t\t\tprefix = 0\n\t\t\t\t\t} else if (typeof value !== 'function') {\n\t\t\t\t\t\tprefix = 1\n\t\t\t\t\t} else {\n\t\t\t\t\t\tprefix = 2\n\t\t\t\t\t\tshould = value\n\t\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn set\n\t}\n\n\t/**\n\t * Stylis\n\t *\n\t * @param {string} selector\n\t * @param {string} input\n\t * @return {*}\n\t */\n\tfunction stylis (selector, input) {\n\t\tif (this !== void 0 && this.constructor === stylis) {\n\t\t\treturn factory(selector)\n\t\t}\n\n\t\t// setup\n\t\tvar ns = selector\n\t\tvar code = ns.charCodeAt(0)\n\n\t\t// trim leading whitespace\n\t\tif (code < 33) {\n\t\t\tcode = (ns = ns.trim()).charCodeAt(0)\n\t\t}\n\n\t\t// keyframe/animation namespace\n\t\tif (keyed > 0) {\n\t\t\tkey = ns.replace(invalidptn, code === OPENBRACKET ? '' : '-')\n\t\t}\n\n\t\t// reset, used to assert if a plugin is moneky-patching the return value\n\t\tcode = 1\n\n\t\t// cascade/isolate\n\t\tif (cascade === 1) {\n\t\t\tnscope = ns\n\t\t} else {\n\t\t\tnscopealt = ns\n\t\t}\n\n\t\tvar selectors = [nscope]\n\t\tvar result\n\n\t\t// execute plugins, pre-process context\n\t\tif (plugged > 0) {\n\t\t\tresult = proxy(PREPS, input, selectors, selectors, line, column, 0, 0, 0, 0)\n\n\t\t\tif (result !== void 0 && typeof result === 'string') {\n\t\t\t\tinput = result\n\t\t\t}\n\t\t}\n\n\t\t// build\n\t\tvar output = compile(array, selectors, input, 0, 0)\n\n\t\t// execute plugins, post-process context\n\t\tif (plugged > 0) {\n\t\t\tresult = proxy(POSTS, output, selectors, selectors, line, column, output.length, 0, 0, 0)\n\n\t\t\t// bypass minification\n\t\t\tif (result !== void 0 && typeof(output = result) !== 'string') {\n\t\t\t\tcode = 0\n\t\t\t}\n\t\t}\n\n\t\t// reset\n\t\tkey = ''\n\t\tnscope = ''\n\t\tnscopealt = ''\n\t\tpattern = 0\n\t\tline = 1\n\t\tcolumn = 1\n\n\t\treturn compress*code === 0 ? output : minify(output)\n\t}\n\n\tstylis['use'] = use\n\tstylis['set'] = set\n\n\tif (options !== void 0) {\n\t\tset(options)\n\t}\n\n\treturn stylis\n}));\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error(\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n    err.name = 'Invariant Violation';\n    throw err;\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "// external dependencies\nimport React from 'react';\nimport { findD<PERSON>Node } from 'react-dom';\n\n/**\n * @function bindSetState\n *\n * @description\n * bind the setState method to the component instance to ensure it can be used in a functional way\n *\n * @param {ReactComponent} instance the instance to bind setState to\n * @returns {void}\n */\nexport var bindSetState = function bindSetState(instance) {\n  return instance.setState = instance.setState.bind(instance);\n};\n\n/**\n * @function isClassComponent\n *\n * @description\n * is the value passed a valid react component class instance\n *\n * @param {any} value the value to test\n * @returns {boolean} is the value a react component instance\n */\nexport var isClassComponent = function isClassComponent(value) {\n  return !!value && value instanceof React.Component;\n};\n\n/**\n * @function logInvalidInstanceError\n *\n * @description\n * notify the user that the instance passed is invalid\n *\n * @param {string} type the type of creator being called\n * @returns {void}\n */\nexport var logInvalidInstanceError = function logInvalidInstanceError(type) {\n  return console.error('The instance provided for use with the ' + type + ' is not a valid React component instance.');\n}; // eslint-disable-line no-console\n\n/**\n * @function createRefCreator\n *\n * @description\n * create a method that will assign a ref value to the instance passed\n *\n * @param {function} getter the function that gets the component value for the ref\n * @returns {function(ReactComponent, string): function((HTMLElement|Component)): void} the ref create\n */\nexport var createRefCreator = function createRefCreator(getter) {\n  return function (instance, ref) {\n    return isClassComponent(instance) ? function (component) {\n      return instance[ref] = getter(component);\n    } : logInvalidInstanceError('ref');\n  };\n};\n\n/**\n * @function getNamespacedRef\n *\n * @description\n * get the ref that is a combination of the raw component and the component's underlying HTML element\n *\n * @param {ReactComponent} component the component to assin\n * @returns {{component: ReactComponent, element: HTMLElement}} the namespaced ref\n */\nexport var getNamespacedRef = function getNamespacedRef(component) {\n  return { component: component, element: findDOMNode(component) };\n};\n\n/**\n * @function identity\n *\n * @description\n * return the first parameter passed\n *\n * @param {any} value the value to pass through\n * @returns {any} the first parameter passed\n */\nexport var identity = function identity(value) {\n  return value;\n};", "function _objectWithoutProperties(obj, keys) { var target = {}; for (var i in obj) { if (keys.indexOf(i) >= 0) continue; if (!Object.prototype.hasOwnProperty.call(obj, i)) continue; target[i] = obj[i]; } return target; }\n\n// external dependencies\nimport React from 'react';\nimport { findDOMNode } from 'react-dom';\n\n// utils\nimport { bindSetState, createRefCreator, getNamespacedRef, identity, isClassComponent, logInvalidInstanceError } from './utils';\n\n/**\n * @function createCombinedRef\n *\n * @description\n * create a ref that assigns both the raw component and the underlying HTML element to the instance on a namespace\n *\n * @param {ReactComponent} instance the instance to assign to\n * @param {string} ref the instance value name\n * @returns {{component: ReactComponent, element: HTMLElement}} the combined ref\n */\nexport var createCombinedRef = createRefCreator(getNamespacedRef);\n\n/**\n * @function createComponentRef\n *\n * @description\n * create a ref that assigns the component itself to the instance\n *\n * @param {ReactComponent} instance the instance to assign to\n * @param {string} ref the instance value name\n * @returns {ReactComponent} the component ref\n */\nexport var createComponentRef = createRefCreator(identity);\n\n/**\n * @function createElementRef\n *\n * @description\n * create a ref that assigns the component's underlying HTML element to the instance\n *\n * @param {ReactComponent} instance the instance to assign to\n * @param {string} ref the instance value name\n * @returns {HTMLElement} the element ref\n */\nexport var createElementRef = createRefCreator(findDOMNode);\n\n/**\n * @function createMethod\n *\n * @description\n * create a method that is a pure version of the lifecycle / instance method passed to it\n *\n * the conditional function return is to ensure the method is called with as performant a way as possible\n *\n * @param {ReactComponent} instance the instance the method is assigned to\n * @param {function} method the instance method\n * @param {Array<any>} extraArgs additional args to pass to the method\n * @returns {function(...Array<any>): any} the method with the instance passed as value\n */\nexport var createMethod = function createMethod(instance, method) {\n  for (var _len = arguments.length, extraArgs = Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    extraArgs[_key - 2] = arguments[_key];\n  }\n\n  return isClassComponent(instance) ? bindSetState(instance) && function () {\n    for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return method.call(instance, instance, args, extraArgs);\n  } : logInvalidInstanceError('method');\n}; // eslint-disable-line no-console\n\n/**\n * @function createComponent\n *\n * @description\n * create a component from the render method and any options passed\n *\n * @param {function} render the function to render the component\n * @param {Object} [options={}] the options to render the component with\n * @param {function} [getInitialState] the method to get the initial state with\n * @param {boolean} [isPure] is PureComponent used\n * @param {Object} [state] the initial state\n * @returns {ReactComponent} the component class\n */\nvar createComponent = function createComponent(render) {\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  var getInitialState = _ref.getInitialState,\n      isPure = _ref.isPure,\n      state = _ref.state,\n      options = _objectWithoutProperties(_ref, ['getInitialState', 'isPure', 'state']);\n\n  var Constructor = isPure ? React.PureComponent : React.Component;\n\n  function ParmComponent(initialProps) {\n    var _this = this;\n\n    Constructor.call(this, initialProps);\n\n    this.state = typeof getInitialState === 'function' ? createMethod(this, getInitialState)() : state || null;\n\n    Object.keys(options).forEach(function (key) {\n      _this[key] = typeof options[key] === 'function' ? createMethod(_this, options[key]) : options[key];\n    });\n\n    this.render = createMethod(this, render);\n\n    return this;\n  }\n\n  ParmComponent.prototype = Object.create(Constructor.prototype);\n\n  ParmComponent.displayName = render.displayName || render.name || 'ParmComponent';\n  ParmComponent.propTypes = render.propTypes;\n  ParmComponent.contextTypes = render.contextTypes;\n  ParmComponent.childContextTypes = render.childContextTypes;\n  ParmComponent.defaultProps = render.defaultProps;\n\n  return ParmComponent;\n};\nexport { createComponent };", "/**\n * @function getUrl\n *\n * @description\n * get the URL used to generate blobs\n *\n * @returns {Object} the URL object to generate blobs with\n */\nexport var getUrl = function () {\n  var defaultObject = {};\n\n  var URL = defaultObject;\n\n  var getUrl = function getUrl() {\n    return URL !== defaultObject ? URL : URL = typeof window !== 'undefined' ? window.URL || window.webkitURL : defaultObject;\n  };\n\n  getUrl.reset = function () {\n    return URL = defaultObject;\n  };\n\n  return getUrl;\n}();\n\n/**\n * @function getHasBlobSupport\n *\n * @description\n * get whether Blobs are supported in the runtime\n *\n * @returns {boolean} are Blobs supported\n */\nexport var getHasBlobSupport = function getHasBlobSupport() {\n  return typeof window !== 'undefined' && typeof window.Blob === 'function' && typeof getUrl().createObjectURL === 'function' && function () {\n    try {\n      new window.Blob();\n\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }();\n};\n\n/**\n * @function hasBlobSupport\n *\n * @description\n * a cached reference to determine Blob support in the runtime\n *\n * @returns {boolean} are Blobs supported\n */\nexport var hasBlobSupport = function () {\n  var support = false;\n\n  var hasBlobSupport = function hasBlobSupport() {\n    return support || (support = getHasBlobSupport());\n  };\n\n  hasBlobSupport.reset = function () {\n    return support = false;\n  };\n\n  return hasBlobSupport;\n}();\n\n/**\n * @function getLinkHref\n *\n * @description\n * get the href of the link based on the style string Blob\n *\n * @param {string} style the style to create the Blob from\n * @returns {string} the data URI built from the Blob\n */\nexport var getLinkHref = function getLinkHref(style) {\n  return hasBlobSupport() ? getUrl().createObjectURL(new window.Blob([style], { type: 'text/css' })) : null;\n};\n\n/**\n * @function createGetCachedLinkHref\n *\n * @description\n * create a cached version of the getLinkHref\n *\n * @returns {function(string): string} the cached version of getLinkHref\n */\nexport var createGetCachedLinkHref = function createGetCachedLinkHref() {\n  return function () {\n    var href = null,\n        currentStyle = null;\n\n    return function (style) {\n      return style === currentStyle ? href : (currentStyle = style) ? href = getLinkHref(style) : href = null;\n    };\n  }();\n};", "// constants\nimport { IS_PRODUCTION } from './constants';\n\n/**\n * @constant {Object} GLOBAL_OPTIONS the global options to apply as fallback to local props\n */\nexport var GLOBAL_OPTIONS = {\n  hasSourceMap: !IS_PRODUCTION,\n  isCompressed: true,\n  isMinified: IS_PRODUCTION,\n  isPrefixed: true\n};\n\n/**\n * @function getCoalescedOption\n *\n * @description\n * get the option either from props if it exists, or globally\n *\n * @param {Object} props the props to the specific instance\n * @param {string} option the option to coalesce\n * @returns {boolean} the coalesced option\n */\nexport var getCoalescedOption = function getCoalescedOption(props, option) {\n  return typeof props[option] === 'boolean' ? props[option] : GLOBAL_OPTIONS[option];\n};\n\n/**\n * @function setGlobalOptions\n *\n * @description\n * set the options passed to be global\n *\n * @param {Object} options the objects to apply globally\n * @returns {void}\n */\nexport var setGlobalOptions = function setGlobalOptions(options) {\n  return Object.keys(options).forEach(function (option) {\n    return GLOBAL_OPTIONS.hasOwnProperty(option) && typeof options[option] === 'boolean' && (GLOBAL_OPTIONS[option] = options[option]);\n  });\n};", "// external dependencies\nimport beautify from 'cssbeautify';\nimport <PERSON><PERSON><PERSON> from 'stylis';\n\n// constants\nimport { BEAUTIFY_OPTIONS } from './constants';\n\n/**\n * @function getProcessedStyles\n *\n * @description\n * get the styles processed by stylis\n *\n * @param {string} rawStyle the style to process\n * @param {Object} props the props passed to the component\n * @param {boolean} props.isCompressed is compressed CSS output requested\n * @param {boolean} props.isPrefixed is vendor-prefixed CSS requested\n * @returns {string} the processed styles\n */\nexport var getProcessedStyles = function getProcessedStyles(rawStyle, _ref) {\n  var isCompressed = _ref.isCompressed,\n      isPrefixed = _ref.isPrefixed;\n  return new Stylis({\n    compress: isCompressed,\n    global: false,\n    keyframe: false,\n    prefix: isPrefixed\n  })('', rawStyle);\n};\n\n/**\n * @function getRenderedStyles\n *\n * @description\n * get the styles rendered in the HTML tag\n *\n * @param {string} rawStyle the style to process\n * @param {Object} props the props passed to the component\n * @param {boolean} props.isMinified is minified CSS output requested\n * @returns {string} the styles to use in the rendered tag\n */\nexport var getRenderedStyles = function getRenderedStyles(rawStyle, props) {\n  return props.isMinified ? getProcessedStyles(rawStyle, props) : beautify(getProcessedStyles(rawStyle, props), BEAUTIFY_OPTIONS);\n};", "var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nfunction _objectWithoutProperties(obj, keys) { var target = {}; for (var i in obj) { if (keys.indexOf(i) >= 0) continue; if (!Object.prototype.hasOwnProperty.call(obj, i)) continue; target[i] = obj[i]; } return target; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n// external dependencies\nimport PropTypes from 'prop-types';\nimport React, { PureComponent } from 'react';\nimport { createElementRef, createMethod } from 'react-parm';\n\n// blob\nimport { createGetCachedLinkHref, hasBlobSupport } from './blob';\n\n// constants\nimport { HAS_UNSAFE_METHODS } from './constants';\n\n// options\nimport { getCoalescedOption, setGlobalOptions } from './options';\n\n// styles\nimport { getRenderedStyles } from './styles';\n\n/**\n * @function componentDidMount\n *\n * @description\n * on mount, relocate the node\n *\n * @param {ReactComponent} instance the component instance\n * @param {HTMLElement} instance.node the node to render the styles into\n * @param {function} instance.relocateNote the method to relocate the node to the head\n * @returns {void}\n */\nexport var componentDidMount = function componentDidMount(_ref) {\n  var node = _ref.node,\n      relocateNode = _ref.relocateNode;\n  return relocateNode(node);\n};\n\n/**\n * @function componentWillUpdate\n *\n * @description\n * before the update occurs, if the sourceMap requirements have changed, return the node to its original position\n *\n * @param {ReactComponent} instance the component instance\n * @param {HTMLElement} instance.node the node to render the styles into\n * @param {function} instance.returnNode the method to return the node to its original parent\n */\nexport var componentWillUpdate = function componentWillUpdate(_ref2) {\n  var node = _ref2.node,\n      returnNode = _ref2.returnNode;\n\n  returnNode(node);\n};\n\n/**\n * @function componentDidUpdate\n *\n * @description\n * on update, if the sourceMap requirements have changed then relocate the new node to the head,\n * and if the styles have changed then set them in staet\n *\n * @param {ReactComponent} instance the component instance\n * @param {function} instance.getStyleForState the method to get the new rendered style\n * @param {HTMLElement} instance.node the node to render the styles into\n * @param {function} instance.relocateNote the method to relocate the node to the head\n * @param {Object} instance.props the new props of the component\n * @param {function} instance.setState the method to set the state of the component\n * @param {Array<any>} args the arguments passed to the function\n * @param {Object} previousProps the previous props of the component\n */\nexport var componentDidUpdate = function componentDidUpdate(_ref3, _ref4) {\n  var getStyleForState = _ref3.getStyleForState,\n      node = _ref3.node,\n      relocateNode = _ref3.relocateNode,\n      props = _ref3.props,\n      setState = _ref3.setState;\n  var previousProps = _ref4[0];\n\n  relocateNode(node);\n\n  if (props.children !== previousProps.children) {\n    setState(getStyleForState);\n  }\n};\n\n/**\n * @function componentWillUnmount\n *\n * @description\n * prior to unmount, return the node to its original parent\n *\n * @param {ReactComponent} instance the component instance\n * @param {HTMLElement} instance.node the node to render the styles into\n * @param {function} instance.returnNode the method to return the node to its original parent\n * @returns {void}\n */\nexport var componentWillUnmount = function componentWillUnmount(_ref5) {\n  var node = _ref5.node,\n      returnNode = _ref5.returnNode;\n  return returnNode(node);\n};\n\n/**\n * @function getStyleForState\n *\n * @description\n * get the styles to be used for the rendered tag\n *\n * @param {ReactComponent} instance the component instance\n * @param {Object} instance.props the new props of the component\n * @returns {{style: string}} the style strng to use in the rendered tag\n */\nexport var getStyleForState = function getStyleForState(_ref6) {\n  var props = _ref6.props;\n  return {\n    style: getRenderedStyles(props.children || '', {\n      isCompressed: getCoalescedOption(props, 'isCompressed'),\n      isMinified: getCoalescedOption(props, 'isMinified'),\n      isPrefixed: getCoalescedOption(props, 'isPrefixed')\n    })\n  };\n};\n\n/**\n * @function relocateNode\n *\n * @description\n * relocate the node to the bottom of the head\n *\n * @param {ReactComponent} instance the component instance\n * @param {Array<any>} args the arguments passed to the function\n * @param {HTMLElement} node the node to render the styles into\n */\nexport var relocateNode = function relocateNode(instance, _ref7) {\n  var node = _ref7[0];\n\n  if (typeof document !== 'undefined' && node) {\n    instance.originalParent = node.parentNode;\n\n    instance.originalParent.removeChild(node);\n    document.head.appendChild(node);\n  }\n};\n\n/**\n * @function returnNode\n *\n * @description\n * return the node to the original parent\n *\n * @param {ReactComponent} instance the component instance\n * @param {Array<any>} args the arguments passed to the function\n * @param {HTMLElement} node the node to render the styles into\n */\nexport var returnNode = function returnNode(instance, _ref8) {\n  var node = _ref8[0];\n\n  if (typeof document !== 'undefined' && node) {\n    try {\n      document.head.removeChild(node);\n      instance.originalParent.appendChild(node);\n    } catch (error) {\n      // ignore the error\n    } finally {\n      instance.node = null;\n      instance.originalParent = null;\n    }\n  }\n};\n\nvar willUpdateMethod = HAS_UNSAFE_METHODS ? 'UNSAFE_componentWillUpdate' : 'componentWillUpdate';\n\nvar Style = function (_PureComponent) {\n  _inherits(Style, _PureComponent);\n\n  function Style(props) {\n    _classCallCheck(this, Style);\n\n    var _this = _possibleConstructorReturn(this, _PureComponent.call(this, props));\n\n    _this.componentDidMount = createMethod(_this, componentDidMount);\n    _this.componentDidUpdate = createMethod(_this, componentDidUpdate);\n    _this[willUpdateMethod] = createMethod(_this, componentWillUpdate);\n    _this.componentWillUnmount = createMethod(_this, componentWillUnmount);\n    _this.linkHref = null;\n    _this.node = null;\n    _this.originalParent = null;\n    _this.getCachedLinkHref = createGetCachedLinkHref();\n    _this.getStyleForState = createMethod(_this, getStyleForState);\n    _this.relocateNode = createMethod(_this, relocateNode);\n    _this.returnNode = createMethod(_this, returnNode);\n\n\n    _this.state = getStyleForState({ props: props });\n    return _this;\n  }\n\n  // lifecycle methods\n\n  // eslint-disable-next-line react/sort-comp\n\n\n  // instance values\n\n\n  // static methods\n\n\n  // instance methods\n\n\n  Style.prototype.render = function render() {\n    var _props = this.props,\n        childrenIgnored = _props.children,\n        hasSourceMapIgnored = _props.hasSourceMap,\n        isMinifiedIgnored = _props.isCompressed,\n        isPrefixedIgnored = _props.isPrefixed,\n        props = _objectWithoutProperties(_props, ['children', 'hasSourceMap', 'isCompressed', 'isPrefixed']);\n\n    var style = this.state.style;\n\n\n    if (getCoalescedOption(this.props, 'hasSourceMap')) {\n      if (hasBlobSupport()) {\n        return (\n          /* eslint-disable prettier */\n          React.createElement('link', _extends({}, props, {\n            href: this.getCachedLinkHref(style),\n            ref: createElementRef(this, 'node'),\n            rel: 'stylesheet'\n          }))\n          /* eslint-enable */\n\n        );\n      }\n\n      /* eslint-disable no-console */\n      console.error('To support sourcemaps for react-style-tag you need Blob support, and the browser you are using does not currently support it. You should include a polyfill prior to the rendering of this component.');\n      /* eslint-enable */\n    }\n\n    return React.createElement(\n      'style',\n      _extends({\n        ref: createElementRef(this, 'node')\n      }, props),\n      style\n    );\n  };\n\n  return Style;\n}(PureComponent);\n\nStyle.propTypes = {\n  children: PropTypes.string.isRequired,\n  hasSourceMap: PropTypes.bool,\n  id: PropTypes.string,\n  isCompressed: PropTypes.bool,\n  isMinified: PropTypes.bool,\n  isPrefixed: PropTypes.bool\n};\nStyle.setGlobalOptions = setGlobalOptions;\n\n\nexport default Style;", "import { CONFIG } from './config';\nexport enum BoomThemeStyleProps {\n  BaseTheme = 'basetheme',\n  BackgroundImage = 'bgimage',\n  CustomStyle = 'style',\n  ExternalURL = 'url',\n  PanelBackground = 'panel-container-bg-color',\n  None = 'none',\n}\ninterface BoomThemeStyleInterface {\n  type: BoomThemeStyleProps;\n}\ninterface BoomThemeStyleBaseThemeInterface extends BoomThemeStyleInterface {\n  type: BoomThemeStyleProps.BaseTheme;\n  props: {\n    theme: 'default' | 'dark' | 'light';\n  };\n}\ninterface BoomThemeStyleBgImageInterface extends BoomThemeStyleInterface {\n  type: BoomThemeStyleProps.BackgroundImage;\n  props: {\n    url: string;\n  };\n}\ninterface BoomThemeStyleStyleInterface extends BoomThemeStyleInterface {\n  type: BoomThemeStyleProps.CustomStyle;\n  props: {\n    text: string;\n  };\n}\ninterface BoomThemeStyleURLInterface extends BoomThemeStyleInterface {\n  type: BoomThemeStyleProps.ExternalURL;\n  props: {\n    url: string;\n  };\n}\ninterface BoomThemePanelContainerBGColorInterface extends BoomThemeStyleInterface {\n  type: BoomThemeStyleProps.PanelBackground;\n  props: {\n    color: string;\n  };\n}\nexport type BoomThemeStyleType =\n  | BoomThemeStyleBaseThemeInterface\n  | BoomThemeStyleBgImageInterface\n  | BoomThemeStyleStyleInterface\n  | BoomThemeStyleURLInterface\n  | BoomThemePanelContainerBGColorInterface;\n\nexport class BoomThemeStyle implements BoomThemeStyleInterface {\n  type: BoomThemeStyleProps;\n  props: any;\n  constructor(type: BoomThemeStyleProps, props: any) {\n    this.type = type;\n    switch (type.toLowerCase()) {\n      case BoomThemeStyleProps.BaseTheme:\n        this.props = {\n          theme: props && props.theme ? props.theme : '',\n        };\n        break;\n      case BoomThemeStyleProps.CustomStyle:\n        this.props = {\n          text: props && props.text ? props.text : '',\n        };\n        break;\n      case BoomThemeStyleProps.ExternalURL:\n        this.props = {\n          url: props && props.url ? props.url : '',\n        };\n        break;\n      case BoomThemeStyleProps.BackgroundImage:\n        this.props = {\n          url: props && props.url ? props.url : '',\n        };\n        break;\n      case CONFIG.THEME_STYLES.PANEL_CONTAINER_BG_COLOR:\n        this.props = {\n          color: props && props.color ? props.color : '',\n        };\n        break;\n      default:\n        this.props = {};\n        break;\n    }\n  }\n}\n", "export const CONFIG = {\n  BASE_THEMES: {\n    DARK: {\n      id: 'dark',\n      index_id: -2000,\n      name: 'Dark Theme',\n    },\n    DEFAULT: {\n      id: 'default',\n      index_id: -1000,\n      name: 'Default Theme',\n    },\n    LIGHT: {\n      id: 'light',\n      index_id: -3000,\n      name: 'Light Theme',\n    },\n  },\n  DEFAULT_THEME_BG_IMAGE: `https://images.unsplash.com/photo-1524334228333-0f6db392f8a1`, // Image Credits : https://unsplash.com/photos/fX-qWsXl5x8\n  DEFAULT_THEME_NAME: 'New Theme',\n  DEFAULT_THEME_STYLE: `.panel-container {\n    background-color: rgba(0,0,0,0.3);\n}`,\n  FIRST_THEME_NAME: 'Night Theme',\n  THEME_STYLES: {\n    BASE_THEME: 'basetheme',\n    BG_IMAGE: 'bgimage',\n    NONE: 'none',\n    STYLE: 'style',\n    PANEL_CONTAINER_BG_COLOR: 'panel-container-bg-color',\n    URL: 'url',\n  },\n};\n", "import { BoomTheme } from './BoomTheme';\n\nexport const getThemeCSSFile = function (mode: 'dark' | 'light'): string {\n  let fileName = '';\n  if (['dark', 'light'].indexOf(mode.toLowerCase()) > -1 && window.performance) {\n    let appFiles = window.performance\n      .getEntries()\n      .map((e) => e.name)\n      .filter((e) => e.endsWith('.js'))\n      .filter((e) => e.indexOf('/public/build/app.') > -1);\n    if (appFiles && appFiles.length > 0) {\n      fileName =\n        appFiles[0].replace(`/public/build/app.`, `/public/build/grafana.${mode.toLowerCase()}.`).slice(0, -3) + '.css';\n    }\n  }\n  return fileName;\n};\nexport const getActiveThemeName = function (themes: BoomTheme[], index: number): string {\n  switch (index) {\n    case -1000:\n      return 'Grafana Default';\n    case -2000:\n      return 'Grafana Dark';\n    case -3000:\n      return 'Grafana Light';\n    default:\n      return themes[index] && themes[index].name ? themes[index].name : 'Grafana Default';\n  }\n};\n", "import { BoomThemeStyle, BoomThemeStyleProps } from './BoomThemeStyle';\nimport { getThemeCSSFile } from './utils';\nimport { CONFIG } from './config';\n\ninterface BoomThemeInterface {\n  name: string;\n  styles: BoomThemeStyle[];\n}\n\nexport class BoomTheme {\n  name: string;\n  styles: BoomThemeStyle[];\n  constructor(options: BoomThemeInterface) {\n    this.name = options.name || CONFIG.DEFAULT_THEME_NAME;\n    this.styles =\n      options.styles.length === 0\n        ? [\n            new BoomThemeStyle(BoomThemeStyleProps.BaseTheme, { theme: CONFIG.BASE_THEMES.DEFAULT.id }),\n            new BoomThemeStyle(BoomThemeStyleProps.BackgroundImage, { url: '' }),\n            new BoomThemeStyle(BoomThemeStyleProps.ExternalURL, { url: '' }),\n            new BoomThemeStyle(BoomThemeStyleProps.CustomStyle, { text: `` }),\n          ]\n        : options.styles;\n  }\n  addStyle(type: BoomThemeStyleProps): void {\n    this.styles.push(new BoomThemeStyle(type, {}));\n  }\n  deleteStyle(index: number): void {\n    this.styles.splice(index, 1);\n  }\n  private constructTheme(styles: any[] = []): string {\n    let output = ``;\n    styles.forEach((style) => {\n      if (style.type === CONFIG.THEME_STYLES.URL) {\n        if (style.props && style.props.url !== '') {\n          output += `@import url('${style.props.url}');\n                    `;\n        }\n      } else if (style.type === CONFIG.THEME_STYLES.BASE_THEME) {\n        if (style.props && style.props.theme !== '') {\n          if (style.props.theme.toLowerCase() === CONFIG.BASE_THEMES.DARK.id) {\n            output += `@import url('${getThemeCSSFile('dark')}');\n                        `;\n          } else if (style.props.theme.toLowerCase() === CONFIG.BASE_THEMES.LIGHT.id) {\n            output += `@import url('${getThemeCSSFile('light')}');\n                        `;\n          }\n        }\n      } else if (style.type === CONFIG.THEME_STYLES.STYLE) {\n        if (style.props && style.props.text !== '') {\n          output += `${style.props.text || ''}\n                    `;\n        }\n      } else if (style.type === CONFIG.THEME_STYLES.BG_IMAGE) {\n        if (style.props && style.props.url !== '') {\n          output += `\n.main-view, .sidemenu, .sidemenu-open .sidemenu, .navbar, .dashboard-container,.dashboard-container > div, .page-toolbar  {\n    background: url(\"${style.props.url}\") no-repeat center center fixed;\n    -webkit-background-size: cover;\n    -moz-background-size: cover;\n    -o-background-size: cover;\n    background-size: cover;\n}\n                    `;\n        }\n      } else if (style.type === CONFIG.THEME_STYLES.PANEL_CONTAINER_BG_COLOR) {\n        if (style.props && style.props.color !== '') {\n          output += `\n.panel-container, .page-toolbar {\n    background-color: ${style.props.color}\n}\n`;\n        }\n      }\n    });\n    return output;\n  }\n  getThemeContent(): string {\n    let output = '';\n    if (this.styles && this.styles.length > 0) {\n      output += this.constructTheme(this.styles.filter((style) => style.type === CONFIG.THEME_STYLES.URL));\n      output += this.constructTheme(this.styles.filter((style) => style.type === CONFIG.THEME_STYLES.STYLE));\n      output += this.constructTheme(\n        this.styles.filter(\n          (style) => style.type !== CONFIG.THEME_STYLES.URL && style.type !== CONFIG.THEME_STYLES.STYLE\n        )\n      );\n    }\n    return output;\n  }\n}\n", "import React from 'react';\nimport { BoomTheme } from './../BoomTheme';\n\ninterface ThemePickerProps {\n  themes: BoomTheme[];\n  onChange: (activeTheme: string) => void;\n}\n\nexport const ThemePicker = (props: ThemePickerProps) => {\n  const onViewChange = (themeName: string) => {\n    props.onChange(themeName);\n  };\n  const defaultThemes = ['Grafana Dark', 'Grafana Light'];\n  return (\n    <div style={{ textAlign: 'center' }}>\n      <br />\n      {(props.themes || [])\n        .map((theme) => theme.name)\n        .concat(defaultThemes)\n        .map((themeName, index: number) => {\n          return (\n            <span\n              className=\"btn btn-secondary\"\n              style={{ marginLeft: index === 0 ? '0' : '10px', marginRight: '10px' }}\n              onClick={() => onViewChange(themeName)}\n              key={index}\n            >\n              {themeName}\n            </span>\n          );\n        })}\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport { SelectableValue } from '@grafana/data';\nimport { Modal, Label, Input, TextArea, RadioButtonGroup, ColorPicker } from '@grafana/ui';\nimport { BoomTheme } from '../BoomTheme';\nimport { BoomThemeStyle, BoomThemeStyleProps } from './../BoomThemeStyle';\n\ninterface EditorProps {\n  value: BoomTheme;\n  onChange: (value: BoomTheme) => void;\n}\n\nexport const ThemeEditor = ({ value, onChange }: EditorProps) => {\n  const [editorVisibility, setEditorVisibility] = useState(false);\n\n  const addStyle = (styleType: BoomThemeStyleProps) => {\n    let theme = value;\n    theme.styles = theme.styles || [];\n    theme.styles.push(new BoomThemeStyle(styleType, null));\n    onChange(theme);\n  };\n\n  const onTitleChange = (title: string) => {\n    let theme = value;\n    theme.name = title;\n    onChange(theme);\n  };\n\n  const onStylePropertyChange = (index: number, propertyName: string, replaceValue: unknown) => {\n    let theme = value;\n    theme.styles[index].props = theme.styles[index].props || {};\n    theme.styles[index].props[propertyName] = replaceValue;\n    onChange(theme);\n  };\n  const defaultThemes: SelectableValue[] = [\n    { value: 'default', label: 'Default' },\n    { value: 'dark', label: 'Dark' },\n    { value: 'light', label: 'Light' },\n  ];\n  return (\n    <>\n      <span className=\"btn width-18\" style={{ justifyContent: 'start' }}>\n        {value.name}\n      </span>\n\n      <i className=\"fa fa-edit btn btn-primary px-2\" onClick={() => setEditorVisibility(true)}></i>\n\n      <Modal isOpen={editorVisibility} onDismiss={() => setEditorVisibility(false)} title={`Edit ${value.name}`}>\n        <Label>Theme Theme</Label>\n        <Input css={{}} value={value.name} onChange={(e) => onTitleChange(e.currentTarget.value)}></Input>\n        <br />\n        {value.styles.map((style, index: number) => {\n          switch (style.type) {\n            case BoomThemeStyleProps.BaseTheme:\n              const baseTheme: SelectableValue = defaultThemes.find((t) => t.value === style.props.theme) || {\n                label: 'Default',\n                value: 'default',\n              };\n              return (\n                <>\n                  <Label>Base Theme</Label>\n                  <RadioButtonGroup\n                    value={baseTheme.value}\n                    options={defaultThemes}\n                    onChange={(e) => onStylePropertyChange(index, 'theme', e)}\n                  ></RadioButtonGroup>\n                  <br />\n                </>\n              );\n            case BoomThemeStyleProps.BackgroundImage:\n              return (\n                <>\n                  <Label>Background Image</Label>\n                  <Input\n                    css={{}}\n                    value={style.props.url}\n                    onChange={(e) => onStylePropertyChange(index, 'url', e.currentTarget.value)}\n                  ></Input>\n                  <br />\n                </>\n              );\n            case BoomThemeStyleProps.ExternalURL:\n              return (\n                <>\n                  <Label>External CSS URL</Label>\n                  <Input\n                    css={{}}\n                    value={style.props.url}\n                    onChange={(e) => onStylePropertyChange(index, 'url', e.currentTarget.value)}\n                  ></Input>\n                  <br />\n                </>\n              );\n            case BoomThemeStyleProps.CustomStyle:\n              return (\n                <>\n                  <Label>Additional CSS Style</Label>\n                  <TextArea\n                    value={style.props.text}\n                    rows={6}\n                    onChange={(e) => onStylePropertyChange(index, 'text', e.currentTarget.value)}\n                  ></TextArea>\n                  <br />\n                </>\n              );\n            case BoomThemeStyleProps.PanelBackground:\n              return (\n                <>\n                  <Label>Panel BG Color</Label>\n                  <Input\n                    value={style.props.color}\n                    onChange={(e) => onStylePropertyChange(index, 'color', e.currentTarget.value)}\n                    prefix={\n                      <div>\n                        <ColorPicker\n                          color={style.props.color}\n                          onChange={(e) => onStylePropertyChange(index, 'color', e)}\n                        />\n                      </div>\n                    }\n                  ></Input>\n                  <br />\n                </>\n              );\n            case 'url':\n              return (\n                <>\n                  <Label>Panel Container BG Color</Label>\n                  <Input\n                    css={{}}\n                    value={style.props.url}\n                    onChange={(e) => onStylePropertyChange(index, 'url', e.currentTarget.value)}\n                  ></Input>\n                  <br />\n                </>\n              );\n            case 'none':\n            default:\n              return <></>;\n          }\n        })}\n        <div className=\"text-center\">\n          <br />\n          {value.styles.filter((s) => s.type === BoomThemeStyleProps.ExternalURL).length < 1 && (\n            <>\n              <button className=\"btn btn-success\" onClick={() => addStyle(BoomThemeStyleProps.ExternalURL)}>\n                Add external CSS\n              </button>\n              &nbsp;&nbsp;\n            </>\n          )}\n          {value.styles.filter((s) => s.type === BoomThemeStyleProps.CustomStyle).length < 1 && (\n            <>\n              <button className=\"btn btn-success\" onClick={() => addStyle(BoomThemeStyleProps.CustomStyle)}>\n                Add custom CSS\n              </button>\n              &nbsp;&nbsp;\n            </>\n          )}\n          {value.styles.filter((s) => s.type === BoomThemeStyleProps.BackgroundImage).length < 1 && (\n            <>\n              <button className=\"btn btn-success\" onClick={() => addStyle(BoomThemeStyleProps.BackgroundImage)}>\n                Add BG Image\n              </button>\n              &nbsp;&nbsp;\n            </>\n          )}\n          {value.styles.filter((s) => s.type === BoomThemeStyleProps.PanelBackground).length < 1 && (\n            <>\n              <button className=\"btn btn-success\" onClick={() => addStyle(BoomThemeStyleProps.PanelBackground)}>\n                Add Panel BG Color\n              </button>\n              &nbsp;&nbsp;\n            </>\n          )}\n          <br />\n          <br />\n          <button className=\"btn btn-success\" onClick={() => setEditorVisibility(false)}>\n            OK\n          </button>\n        </div>\n      </Modal>\n    </>\n  );\n};\n", "import React from 'react';\nimport { PanelOptionsEditorItem } from '@grafana/data';\nimport { BoomTheme } from './../BoomTheme';\nimport { ThemeEditor } from './ThemeEditor';\n\ninterface EditorProps {\n  value: BoomTheme[];\n  onChange: (value: BoomTheme[]) => void;\n}\n\nconst Editor = ({ value, onChange }: EditorProps) => {\n  const addTheme = () => {\n    let themes: BoomTheme[] = value || [];\n    let newTheme: BoomTheme = new BoomTheme({\n      name: `New Theme ${themes.length + 1}`,\n      styles: [],\n    });\n    themes.push(newTheme);\n    onChange(themes);\n  };\n  const deleteTheme = (index: number) => {\n    let themes: BoomTheme[] = value || [];\n    themes.splice(index, 1);\n    onChange(themes);\n  };\n  const onThemeChange = (theme: BoomTheme, index: number) => {\n    let themes = value || [];\n    themes[index] = theme;\n    onChange(themes);\n  };\n  return (\n    <>\n      {value?.map((theme, index) => (\n        <div key={index}>\n          <br />\n          <ThemeEditor\n            value={theme}\n            onChange={(updatedTheme) => {\n              onThemeChange(updatedTheme, index);\n            }}\n          />\n          <i\n            className=\"btn fa fa-trash btn btn-danger px-2\"\n            title={`Edit ${theme.name}`}\n            onClick={() => deleteTheme(index)}\n          ></i>\n        </div>\n      ))}\n      <br />\n      <div className=\"text-center\">\n        <button className=\"btn btn-primary\" onClick={addTheme}>\n          Add New Theme\n        </button>\n      </div>\n    </>\n  );\n};\n\nexport const ThemesEditorOptions: PanelOptionsEditorItem = {\n  id: 'themes',\n  name: 'Themes',\n  path: 'themes',\n  category: ['Themes'],\n  editor: Editor,\n};\n", "import { PanelPlugin, PanelModel } from '@grafana/data';\nimport { Panel, PanelOptions } from './Panel';\nimport { ThemesEditorOptions } from './editors/ThemesEditor';\nimport { getActiveThemeName } from './utils';\n\nexport const plugin = new PanelPlugin<PanelOptions>(Panel)\n  .setPanelOptions((builder) => {\n    builder.addCustomEditor(ThemesEditorOptions);\n    builder.addBooleanSwitch({\n      name: 'Hide Theme Picker',\n      path: 'disableThemePicker',\n      category: ['Themes Options'],\n    });\n    builder.addTextInput({\n      name: 'Default Theme',\n      path: 'activeTheme',\n      category: ['Themes Options'],\n    });\n    return builder;\n  })\n  .setMigrationHandler((panel: PanelModel<PanelOptions> & Record<string, any>) => {\n    const newOptions = {\n      disableThemePicker: panel.options.disableThemePicker ?? panel.disableThemePicker,\n      activeTheme: panel.options.activeTheme ?? getActiveThemeName(panel.themes, panel.activeThemeId),\n      themes: panel.options.themes ?? panel.themes,\n    };\n    delete panel.themes;\n    delete panel.activeThemeId;\n    delete panel.disableThemePicker;\n    return newOptions;\n  });\n", "import React, { useState } from 'react';\nimport { PanelData, PanelProps } from '@grafana/data';\nimport { Style } from 'react-style-tag';\nimport { BoomTheme } from './BoomTheme';\nimport { ThemePicker } from './editors/ThemePicker';\nimport { getThemeCSSFile } from './utils';\n\nexport interface PanelOptions {\n  disableThemePicker: boolean;\n  activeTheme: string;\n  themes: BoomTheme[];\n}\n\ninterface Props extends PanelProps<PanelOptions> {\n  options: PanelOptions;\n  data: PanelData;\n  width: number;\n  height: number;\n  onOptionsChange: (options: PanelOptions) => void;\n}\n\nexport const Panel = ({ options, replaceVariables }: Props) => {\n  const [runTimeThemeState, setRunTimeThemeState] = useState(false);\n  const [runTimeTheme, setRunTimeTheme] = useState('');\n\n  const onRuntimeThemeChange = (themeName: string) => {\n    setRunTimeThemeState(true);\n    setRunTimeTheme(themeName);\n  };\n\n  let output = '';\n\n  options.themes?.forEach((themeOptions: BoomTheme, index) => {\n    const theme = new BoomTheme(themeOptions);\n    if (runTimeThemeState) {\n      if (runTimeTheme === theme.name) {\n        output += theme.getThemeContent();\n      } else if (runTimeTheme === 'Grafana Dark') {\n        output = `@import url('${getThemeCSSFile('dark')}');\n            `;\n      } else if (runTimeTheme === 'Grafana Light') {\n        output = `@import url('${getThemeCSSFile('light')}');\n            `;\n      }\n    } else {\n      if (replaceVariables(options.activeTheme) === 'Grafana Dark') {\n        output = `@import url('${getThemeCSSFile('dark')}');\n            `;\n      } else if (replaceVariables(options.activeTheme) === 'Grafana Light') {\n        output = `@import url('${getThemeCSSFile('light')}');\n            `;\n      } else if (replaceVariables(options.activeTheme) === theme.name) {\n        output += theme.getThemeContent();\n      }\n    }\n  });\n\n  return (\n    <>\n      <Style>{output}</Style>\n      {options.disableThemePicker ? <></> : <ThemePicker themes={options.themes} onChange={onRuntimeThemeChange} />}\n    </>\n  );\n};\n"], "sourceRoot": ""}