server {
    listen       8888;
    listen  [::]:8888;
    server_name  localhost;

    #access_log  /var/log/nginx/host.access.log  main;

    # location / {
    #     root   /usr/share/nginx/html;
    #     index  index.html index.htm;
    # }


    location / {  
        proxy_pass http://front:3000/;   
        proxy_read_timeout 90;   
        proxy_http_version 1.1;   
        proxy_set_header Upgrade $http_upgrade;   
        proxy_set_header Connection $http_connection;  
        proxy_set_header Host $host;  
        proxy_set_header X-Real-IP $remote_addr;  
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;  
        proxy_cache_bypass $http_upgrade; 
    }

    location /api/v1/eval {
        proxy_pass http://front:3000/api/v1/eval;   
        proxy_read_timeout 90;   
        proxy_http_version 1.1;   
        proxy_set_header Upgrade $http_upgrade;   
        proxy_set_header Connection $http_connection;  
        proxy_set_header Host $host;  
        proxy_set_header X-Real-IP $remote_addr;  
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;  
        proxy_cache_bypass $http_upgrade; 
    }

    location /api/v1/ngalert {
        proxy_pass http://front:3000/api/v1/ngalert;   
        proxy_read_timeout 90;   
        proxy_http_version 1.1;   
        proxy_set_header Upgrade $http_upgrade;   
        proxy_set_header Connection $http_connection;  
        proxy_set_header Host $host;  
        proxy_set_header X-Real-IP $remote_addr;  
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;  
        proxy_cache_bypass $http_upgrade; 
    }

    location /api/v1/ {  
        proxy_pass http://jsonhub:80/;   
        proxy_read_timeout 90;   
        proxy_http_version 1.1;   
        proxy_set_header Upgrade $http_upgrade;   
        proxy_set_header Connection $http_connection;  
        proxy_set_header Host $host;  
        proxy_set_header X-Real-IP $remote_addr;  
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;  
        proxy_cache_bypass $http_upgrade; 
    }

    location /api/live/ws {
        proxy_pass http://front:3000/api/live/ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_set_header Origin "";
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}

