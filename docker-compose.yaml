version: '3'
services:
  db:
    build: mysql
    image: mysql:8.3.0
    container_name: bgp-mysql
    ports:
      - "23306:3306"
    volumes:
      - ./bgp-mysql/init:/docker-entrypoint-initdb.d
      - ./bgp-mysql/data:/var/lib/mysql
      - ./bgp-mysql/conf:/etc/mysql/conf.d
    environment:
      MYSQL_ROOT_PASSWORD: bgproot
      MYSQL_USER: bgp
      MYSQL_PASSWORD: bgpuser
    restart: always
    healthcheck:
        test: ["CMD", "mysqladmin" ,"ping", "-h", "localhost"]
        timeout: 20s
        retries: 10
    command:
      - --innodb-buffer-pool-size=20G --key_buffer_size=4G --sql_mode= 
  # crawler:
  #   build: ./bgp-python
  #   image: bgp-crawler:latest
  #   container_name: bgp-crawler
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
  #   volumes:
  #     - ./bgp-python:/app
  #   environment:
  #     DB_HOST: db
  #     DB_PORT: 3306
  #     DB_USER: root
  #     DB_PASSWORD: bgproot
  #     DB_NAME: bgpdata
  #     HTTP_PROXY: 
  #     HTTPS_PROXY: 
  #     NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  #   privileged: true
  #   shm_size: 2g
  #   restart: always
  # stat:
  #   build: ./bgp-stat
  #   image: bgpstat:v6
  #   container_name: bgpstat
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
  #   volumes:
  #     - ./bgp-stat:/bgp
  #   environment:
  #     DB_HOST: db
  #     DB_PORT: 3306
  #     DB_USER: root
  #     DB_PASSWORD: bgproot
  #     DB_NAME: bgpdata
  #     DB_TABLE: bgpcountryreach
  #     HTTP_PROXY: 
  #     HTTPS_PROXY: 
  #     NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  #   restart: always
  # stat-init:
  #   build: ./bgp-stat
  #   image: bgpstat:v6
  #   container_name: bgpstat-init
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
  #   volumes:
  #     - ./bgp-stat-init:/bgp
  #   environment:
  #     DB_HOST: db
  #     DB_PORT: 3306
  #     DB_USER: root
  #     DB_PASSWORD: bgproot
  #     DB_NAME: bgpdata
  #     DB_TABLE: bgpcountryreach
  #     HTTP_PROXY: 
  #     HTTPS_PROXY: 
  #     NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  #   restart: always
  rss:
    image: damoeb/rss-proxy:2.1
    container_name: bgp-rss
    ports:
      - "28080:8080"
    environment:
      APP_API_GATEWAY_URL: https://127.0.0.1
      HTTP_PROXY: 
      HTTPS_PROXY: 
      NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  front:
    build: grafana
    image: grafana/grafana-oss:latest
    container_name: bgp-grafana
    depends_on:
      db:
        condition: service_healthy
    links:
      - db
      # - rss
      - jsonhub
    ports:
      - "23000:3000"
    volumes:
      - ./bgp-grafana/data:/var/lib/grafana
    environment:
      GF_SECURITY_ALLOW_EMBEDDING: true
      GF_PANELS_DISABLE_SANITIZE_HTML: true
      # HTTP_PROXY: 
      # HTTPS_PROXY: 
      # NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
    restart: always
  jsonhub:
    build: ./json-hub
    image: jsonhub:latest
    container_name: jsonhub
    depends_on:
      db:
        condition: service_healthy
    links:
      - db
    ports:
      - "8082:80"
    volumes:
      - ./json-hub/app:/code/app
    restart: always
    environment:
      HTTP_PROXY: 
      HTTPS_PROXY: 
      NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  # ioda:
  #   build: ./ioda
  #   image: ioda:latest
  #   container_name: ioda
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   links:
  #     - db
  #   volumes:
  #     - ./ioda:/app
  #   environment:
  #     DB_HOST: db
  #     DB_PORT: 3306
  #     DB_USER: root
  #     DB_PASSWORD: bgproot
  #     HTTP_PROXY: 
  #     HTTPS_PROXY: 
  #     NO_PROXY: 172.18.*.*,127.0.0.1,localhost,192.168.*.*
  #   restart: always
  active_server:
    build: ./activeServer
    image: active_server:latest
    container_name: active_server
    volumes:
      - ./activeServer:/app
    environment:  # used during container running
      DB_HOST: db
      DB_PORT: 3306
      DB_USER: root
      DB_PASSWORD: bgproot
      VPS_HOST: *************
      VPS_USER: root
      VPS_PORT: 22
      VPS_KEY: 7O0srQb0v1
      DEBUG_FLAG: false
    restart: always
    depends_on:
      db:
        condition: service_healthy
    links:
      - db
  active_server_funboost:
    build: ./activeServer_funboost
    image: active_server_funboost:latest
    container_name: active_server_funboost
    volumes:
      - ./activeServer_funboost:/app
    environment:  # used during container running
      DB_HOST: db
      DB_PORT: 3306
      DB_USER: root
      DB_PASSWORD: bgproot
      VPS_HOST: *************
      VPS_USER: root
      VPS_PORT: 22
      VPS_KEY: 7O0srQb0v1
      DEBUG_FLAG: false
    restart: always
    depends_on:
      db:
        condition: service_healthy
    links:
      - db
  detection_algo:
    build: ./anomaly-detection
    image: bgpdet:v1
    container_name: detection_algo
    volumes:
      - ./anomaly-detection:/app
    environment:  # used during container running
      DB_HOST: db
      DB_PORT: 3306
      DB_USER: root
      DB_PASSWORD: bgproot
      # DEBUG: 1
    restart: always
    depends_on:
      db:
        condition: service_healthy
    links:
      - db
  nginx_server:
    build: ./nginx
    image: nginx:latest
    container_name: nginx_server
    volumes:
      - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d/:/etc/nginx/conf.d/
      - ./nginx/logs:/var/log/nginx
    links:
      - jsonhub
      - front
    ports:
      - "8888:8888"
    restart: always
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  db:
    driver: local
  redis_data: