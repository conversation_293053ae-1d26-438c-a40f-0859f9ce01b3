from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.schedulers.background import BackgroundScheduler
from loguru import logger
import pandas as pd
from datetime import timedelta, datetime, UTC
import sys
import time
import json
from sqlalchemy.types import VARCHAR
from models.bgp import *
from settings import *
from db_op import save_db

DEBUG = os.environ.get('DEBUG', False)
UNSEEN = os.environ.get('UNSEEN', False)
if not DEBUG:
    min_level = 'INFO'
else:
    min_level = 'DEBUG'

def my_filter(record):
    return record["level"].no >= logger.level(min_level).no

logger.remove()
logger.add(sys.stderr, filter=my_filter)

save_type = 'csv'   # 'xlxs
as_info = {}
logger.info('read as_info_all.json')
with open('data/as_info_all.json', 'r') as f:
    as_info = json.load(f)
logger.info('read blacklist.json')
with open('data/blacklist.json', 'r') as f:
    blacklist = json.load(f)


def prefix_to_ipnum(prefix_list):
    ipnum = 0
    for prefix in prefix_list:
        net_suffix = prefix.split("/")[1]
        ipnum += 2**(32-int(net_suffix))
    return ipnum


def perform_asn_thres_detection(data: pd.DataFrame, group_key='asn', key='index', detection='any', **kwargs):
    thres = kwargs.get('thres', 0.2)
    res, res_df = [], []
    # df = data[data[key]<thres].copy()
    df = data.copy()
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df['time'] = df['time'] + timedelta(hours=8)    #.dt.tz_localize('UTC').dt.tz_convert('Asia/Singapore')

    seen_as = data[group_key].apply(lambda x: str(x)).unique()
    unseen_as = set(as_info.keys()) - set(seen_as)
    for asn in unseen_as:
        asn_info_ = as_info.get(asn, {})
        asn_country = asn_info_.get('Country', 'UNKNOW')
        asn_name = asn_info_.get('name', 'UNKNOW')
        asn_org = asn_info_.get('org', 'UNKNOW')
        asn_prefix = asn_info_.get('Prefixes4', [])
        if len(asn_prefix) and int(asn) not in blacklist['asn']:
            # 'time', 'asn', 'reach_count', 'total_count', 'reach_ratio', 'country', 'asn_name', 'asn_org', 'tag'
            res.append((df['time'].max(), int(asn), 0, prefix_to_ipnum(asn_prefix), 0, asn_country, asn_name, asn_org, 'unseen'))
    logger.warning(f'unseen asn: {len(res)}, empty asn: {len(unseen_as)-len(res)}')
    if not UNSEEN:
        res = []
    if detection == 'any':
        for name, gp in df.groupby(group_key):
            asn_info_ = as_info.get(str(name), {})
            asn_country = asn_info_.get('Country', 'UNKNOW')
            asn_name = asn_info_.get('name', 'UNKNOW')
            asn_org = asn_info_.get('org', 'UNKNOW')
            int_df = gp[gp[key] < thres]
            if len(int_df):
                int_df.sort_values('time', inplace=True)
                int_df = int_df.iloc[-1]
                int_df['country'] =  asn_country
                int_df['asn_name'] =  asn_name
                int_df['asn_org'] =  asn_org
                res_df.append(int_df)
        res_df = pd.concat(res_df, axis=1, ignore_index=True).T
    elif detection == 'avg':
        gp = df.groupby(group_key, as_index=False).mean()
        int_df = gp[gp[key] < thres].copy()
        if len(int_df):
            int_df.loc[:, 'country'] = int_df[group_key].apply(lambda x: as_info.get(str(x), {}).get('Country', 'UNKNOW'))
            int_df.loc[:, 'asn_name'] = int_df[group_key].apply(lambda x: as_info.get(str(x), {}).get('name', 'UNKNOW'))
            int_df.loc[:, 'asn_org'] = int_df[group_key].apply(lambda x: as_info.get(str(x), {}).get('org', 'UNKNOW'))
            res_df = int_df.sort_index('time')
    elif detection == 'all':
        for name, gp in df.groupby(group_key):
            asn_info_ = as_info.get(str(name), {})
            asn_country = asn_info_.get('Country', 'UNKNOW')
            asn_name = asn_info_.get('name', 'UNKNOW')
            asn_org = asn_info_.get('org', 'UNKNOW')
            if gp[key].max() < thres:
                int_df = gp.sort_values('time')
                int_df = int_df.iloc[-1]
                int_df['country'] =  asn_country
                int_df['asn_name'] =  asn_name
                int_df['asn_org'] =  asn_org
                res_df.append(int_df)
        res_df = pd.concat(res_df, axis=1, ignore_index=True).T
    else:
        logger.error(f'no support for {detection}')
    if res_df:
        cols = ['time', 'asn', 'reach_count', 'total_count', 'reach_ratio', 'country', 'asn_name', 'asn_org', 'tag']
        res_df['tag'] = 'detection'
        res_df = res_df[cols]
        res = pd.DataFrame(res, columns=cols)
        res = pd.concat([res_df, res], axis=0, ignore_index=True)
        res['create_time'] = datetime.now(UTC)
    return res
    

def perform_asn_std_detection(data: pd.DataFrame, group_key='asn', key='index', detection='any', **kwargs):
    sigma = kwargs.get('sigma', 3)
    res, res_df = [], []
    df = data.copy()
    df['time'] = pd.to_datetime(df['time'], unit='s', utc=True)
    latest_time = df['time'].max()
    logger.info(f'{latest_time=}')
    seen_as = data[group_key].apply(lambda x: str(x)).unique()
    unseen_as = set(as_info.keys()) - set(seen_as)
    for asn in unseen_as:
        asn_info_ = as_info.get(asn, {})
        asn_country = asn_info_.get('Country', 'UNKNOW')
        asn_name = asn_info_.get('name', 'UNKNOW')
        asn_org = asn_info_.get('org', 'UNKNOW')
        asn_prefix = asn_info_.get('Prefixes4', [])
        if len(asn_prefix) and int(asn) not in blacklist['asn']:
            # 'time', 'asn', 'reach_count', 'total_count', 'reach_ratio', 'country', 'asn_name', 'asn_org', 'tag'
            res.append((df['time'].max(), int(asn), 0, prefix_to_ipnum(asn_prefix), 0, asn_country, asn_name, asn_org, 'unseen'))
    logger.warning(f'unseen asn: {len(res)}, empty asn: {len(unseen_as)-len(res)}')
    if not UNSEEN:
        res = []
    for name, gp in df.groupby(group_key):
        asn_info_ = as_info.get(str(name), {})
        asn_country = asn_info_.get('Country', 'UNKNOW')
        asn_name = asn_info_.get('name', 'UNKNOW')
        asn_org = asn_info_.get('org', 'UNKNOW')

        # upper_bound = gp[key].mean() + gp[key].std() * sigma
        lower_bound = gp[key].mean() - gp[key].std() * sigma
        if gp[key].std() < 0.01:
            lower_bound -= 0.01
        latest_data = gp.iloc[-1].copy()
        if latest_data[key] < lower_bound and latest_data['time']==latest_time:
            latest_data['country'] =  asn_country
            latest_data['asn_name'] =  asn_name
            latest_data['asn_org'] =  asn_org
            latest_data['tag'] = 'detection'
            res_df.append(latest_data)
        elif latest_data['time']!=latest_time:
            latest_data['country'] =  asn_country
            latest_data['asn_name'] =  asn_name
            latest_data['asn_org'] =  asn_org
            latest_data['tag'] = 'missing'
            res_df.append(latest_data)
    if res_df:
        res_df = pd.concat(res_df, axis=1, ignore_index=True).T
        cols = ['time', 'asn', 'reach_count', 'total_count', 'reach_ratio', 'country', 'asn_name', 'asn_org', 'tag']
        res_df = res_df[cols]
        res = pd.DataFrame(res, columns=cols)
        res = pd.concat([res_df, res], axis=0, ignore_index=True)
        res['create_time'] = datetime.now(UTC)
    return res


def perform_country_std_detection(data: pd.DataFrame, group_key='country_name', key='index', detection='any', **kwargs):
    sigma = kwargs.get('sigma', 3)
    res_df = []
    df = data.copy()
    df['time'] = pd.to_datetime(df['time'], unit='s', utc=True)
    latest_time = df['time'].max()
    logger.info(f'{latest_time=}')
    for name, gp in df.groupby(group_key):
        # upper_bound = gp[key].mean() + gp[key].std() * sigma
        lower_bound = gp[key].mean() - gp[key].std() * sigma
        if gp[key].std() < 0.01:
            lower_bound -= 0.01
        latest_data = gp.iloc[-1].copy()
        if latest_data[key] < lower_bound and latest_data['time']==latest_time:
            logger.debug(f'{gp[key].mean()=}, {gp[key].std()=}, {len(gp)=}')
            latest_data['tag'] = 'detection'
            res_df.append(latest_data)
        elif latest_data['time']!=latest_time:
            latest_data['tag'] = 'missing'
            res_df.append(latest_data)
    if res_df:
        res_df = pd.concat(res_df, axis=1, ignore_index=True).T
        cols = ['time', 'country_name', 'reach_count', 'total_count', 'reach_ratio', 'tag']
        res_df = res_df[cols]
        res_df['create_time'] = datetime.now(UTC)
    return res_df


def write_output(res, output_file, data_src, detection_type, method):
    if not len(res):
        logger.info('no anomaly detected')
    else:
        # # 'time', 'asn', 'reach_count', 'total_count', 'reach_ratio', 'country', 'asn_name', 'asn_org', 'tag'
        db_res = res.rename({'country': 'country_name', 'time': 'data_time'}, axis=1)
        db_res['detection_type'] = detection_type
        db_res['data_src'] = data_src
        db_res['method'] = method
        if data_src == 'asn':
            logger.info(f'anomaly: {len(res)}, asn: {len(res.asn.unique())}, country: {len(res.country.unique())}')
            res.rename({'asn': 'ASN', 'country': '国家', 'asn_name': '自治域名', 
                        'asn_org': '注册组织', 'time': '更新时间', 'reach_ratio': '连通率',
                        'reach_count': '前缀数量', 'total_count': '前缀总量', 'tag': '标签', 'create_time': '创建时间'}, axis=1, inplace=True)
            if save_type == 'csv':
                res.to_csv(output_file)
            elif save_type == 'xlsx':
                res.to_excel(output_file)
            logger.info(f'write to {output_file}')
            idx_labels = ['create_time', 'asn', 'detection_type', 'tag']
            db_res.set_index(idx_labels, inplace=True)
            if not DEBUG:
                save_db(db_res, 'bgpasoutage', idx_labels, dtype={'detection_type': VARCHAR(20),
                                                                'reach_ratio': FLOAT,
                                                                'country_name': VARCHAR(128),
                                                                'asn_name': VARCHAR(128),
                                                                'asn_org': VARCHAR(128),
                                                                'tag': VARCHAR(20),
                                                                'data_src': VARCHAR(20),
                                                                'method': VARCHAR(20),
                                                                })
        elif data_src == 'country':
            logger.info(f'anomaly: {len(res)}, country: {len(res.country_name.unique())}')
            # cols = ['time', 'country_name', 'reach_count', 'total_count', 'reach_ratio', 'tag']
            res.rename({'country_name': '国家', 'time': '更新时间', 'reach_ratio': '连通率', 
                        'reach_count': '前缀数量', 'total_count': '前缀总量', 'tag': '标签', 
                        'create_time': '创建时间'}, axis=1, inplace=True)
            if save_type == 'csv':
                res.to_csv(output_file)
            elif save_type == 'xlsx':
                res.to_excel(output_file)
            logger.info(f'write to {output_file}')
            idx_labels = ['create_time', 'country_name', 'detection_type', 'tag']
            db_res.set_index(idx_labels, inplace=True)
            if not DEBUG:
                save_db(db_res, 'bgpcountryoutage', idx_labels, dtype={'detection_type': VARCHAR(20),
                                                                        'reach_ratio': FLOAT,
                                                                        'country_name': VARCHAR(128),
                                                                        'tag': VARCHAR(20),
                                                                        'data_src': VARCHAR(20),
                                                                        'method': VARCHAR(20),
                                                                        })



def bgp_detection(data_src='asn', detection='any', last_hours=24, method='thres_detection', **kwargs):
    logger.info(f'start bgp detection, {data_src=}, {method=}, {last_hours=}, {kwargs=}')
    key = 'reach_ratio'
    engine = create_engine("mysql+pymysql://{user}:{password}@{host}:{port}/{db}?charset=utf8"
                    .format(user=mysql_user, password=mysql_password, 
                            host=mysql_host, port=mysql_port, db=mysql_db), 
                            echo=False)
    DBSession = sessionmaker(bind=engine)
    session = DBSession()
    timestamp = int(time.time())
    last_timestamp = timestamp - last_hours * 60 * 60
    os.makedirs(f'outputs/{data_src}', exist_ok=True)
    now_date = datetime.now(UTC).strftime("%Y%m%d%H")
    output_file = f'outputs/{data_src}/{method}_{now_date}.{save_type}'
    if data_src == 'asn':
        statement = session.query(BGPASReach).filter(BGPASReach.time<timestamp, 
                                                     BGPASReach.time>last_timestamp).statement
        group_key = 'asn'

    elif data_src == 'country':
        statement = session.query(BGPCountryReach).filter(BGPCountryReach.time<timestamp, 
                                                          BGPCountryReach.time>last_timestamp).statement
        group_key = 'country_name'
    else:
        logger.error(f'{data_src} not found')
        return
    df = pd.read_sql(statement, session.bind)
    logger.info(f'read data from db: {len(df)}')

    # perform detection
    # thres detection
    if method == 'thres_detection':
        if data_src == 'asn':
            res = perform_asn_thres_detection(df, group_key, key, detection, **kwargs)
        elif data_src == 'country':
            pass
    # std detection
    elif method == 'std_detection':
        if data_src == 'asn':
            res = perform_asn_std_detection(df, group_key, key, detection, **kwargs)
        elif data_src == 'country':
            res = perform_country_std_detection(df, group_key, key, detection, **kwargs)

    write_output(res, output_file, data_src, detection, method)
    return

def run_scheduler():
    scheduler = BackgroundScheduler(logger=logger)

    # bgp_detection(data_src='asn', detection='any', last_hours=24, method='thres_detection', **kwargs)
    # scheduler.add_job(bgp_detection, 
    #                   trigger='cron', 
    #                   hour='7', minute='30',
    #                   id="bgp_asn_detection_daily", 
    #                   args=['asn', 'avg', 24*3, 'thres_detection', {'thres': 0.4}])
    scheduler.add_job(bgp_detection, 
                      trigger='cron', 
                      hour='*/2', minute='55',
                      id="bgp_asn_detection_hourly", 
                      args=['asn', 'any', 24*3, 'std_detection'],
                      kwargs={'sigma': 3.5})
    scheduler.add_job(bgp_detection, 
                      trigger='cron', 
                      hour='*/2', minute='50',
                      id="bgp_country_detection_hourly", 
                      args=['country', 'any', 24*7, 'std_detection'],
                      kwargs={'sigma': 3})
    job_defaults = {
        'coalesce': False,
        'max_instances': 10  # 设置同时运行的特定作业最大实例数
    }
    scheduler.configure(job_defaults=job_defaults)

    try:
        scheduler.start()
        while True:
            time.sleep(20)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown(wait=False)
        logger.info('KeyboardInterrupt: scheduler shutdown')


if __name__ == '__main__':
    run_scheduler()

    # bgp_detection(data_src='asn', detection='any', last_hours=24, method='thres_detection', thres=0.5)
    # bgp_detection(data_src='country', detection='any', last_hours=24, method='thres_detection', thres=0.5)


    
    # bgp_detection(data_src='country', detection='any', last_hours=24*7, method='std_detection', sigma=3.5)
    # bgp_detection(data_src='asn', detection='any', last_hours=24*3, method='std_detection', sigma=3)


