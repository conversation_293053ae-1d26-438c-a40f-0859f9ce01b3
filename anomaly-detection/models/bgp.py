from sqlalchemy import Column, BIGINT, FLOAT, DATETIME, VARCHAR
from sqlalchemy.ext.declarative import declarative_base


Base = declarative_base()

class BGPASReach(Base):
    __tablename__ = 'bgpasreach'

    time = Column(BIGINT, primary_key=True)
    asn = Column(BIGINT, primary_key=True)
    reach_count = Column(BIGINT)
    total_count = Column(BIGINT)
    reach_ratio = Column(FLOAT)


class BGPCountryReach(Base):
    __tablename__ = 'bgpcountryreach'

    time = Column(BIGINT, primary_key=True)
    country_name = Column(VARCHAR(128), primary_key=True)
    reach_count = Column(BIGINT)
    total_count = Column(BIGINT)
    reach_ratio = Column(FLOAT)


class BGPStat(Base):
    __tablename__ = 'bgpstat'

    time = Column(BIGINT, primary_key=True)
    country_name = Column(VARCHAR(128), primary_key=True)
    achievable_rate = Column(FLOAT)

class BGPASOutage(Base):

    __tablename__ = 'bgpasoutage'

    data_time = Column(DATETIME, primary_key=True)
    asn = Column(BIGINT, primary_key=True)
    detection_type = Column(VARCHAR(20), primary_key=True)
    create_time = Column(DATETIME)
    asn_name = Column(VARCHAR(50))
    country_name = Column(VARCHAR(128))
    asn_org = Column(VARCHAR(128))
    reach_count = Column(BIGINT)
    total_count = Column(BIGINT)
    reach_ratio = Column(FLOAT)
    
