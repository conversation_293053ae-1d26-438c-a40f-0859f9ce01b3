import json
import re
from tqdm import tqdm
from config import settings
from config import setting
import os
from common import utils
import pandas as pd
from common.mysql_conn import MysqlDB
from apscheduler.schedulers.blocking import BlockingScheduler
import datetime
import paramiko
from loguru import logger
logger.add('server.log')

def fetch_results(datasource_type):
    logger.info('Start {} fetching'.format(datasource_type))
    local_file_ls = sftp_process(datasource_type)
    logger.info('fetch {} files'.format(len(local_file_ls)))
    return local_file_ls


def process_scan_results(filepath, country_prov_pd):
    collector = set()
    now_timestamp = int(os.path.basename(filepath).split('.')[0].split('_')[-1])
    try:
        with open(filepath, "r", encoding='utf-8') as f:
            s = f.read()
            s = re.sub(r"},\s*]", "}]", s)
            result_json = json.loads(s)
            # result_json = json.loads(f.read())
        if result_json:
            try:
                for ip in result_json:
                    collector.add(ip['ip'])
            except Exception as e:
                logger.error(f"{filepath} read faild {e}")

        flag = save_scan_data(now_timestamp, collector, country_prov_pd)

        if setting.DEBUG_FLAG != 'true' and flag:
            os.unlink(filepath)
    except Exception as e:
        logger.error('save {} meet: {}'.format(filepath, e))


def process_web_results(filepath, country_prov_pd=None):
    now_timestamp = int(os.path.basename(filepath).split('.')[0].split('_')[-1])
    try:
        with open(filepath, "r", encoding='utf-8') as f:
            result_json = json.loads(f.read())
        if result_json:
            flag = save_web_data(now_timestamp, result_json)

        if setting.DEBUG_FLAG != 'true' and flag:
            os.unlink(filepath)
    except Exception as e:
        logger.error('save {} meet: {}'.format(filepath, e))


def process_citrix_results(filepath, country_prov_pd=None):
    """Process vulAliveLine (citrix scan) results - route to appropriate handler based on file type"""
    now_timestamp = int(os.path.basename(filepath).split('.')[0].split('_')[-1])
    filename = os.path.basename(filepath)

    try:
        with open(filepath, "r", encoding='utf-8') as f:
            result_json = json.loads(f.read())

        if result_json:
            # Check scan_type to determine which table to use
            scan_type = result_json.get('scan_info', {}).get('scan_type', 'unified')

            if scan_type == 'detailed' or filename.startswith('citrix_scan_'):
                # Save to detailed citrix table
                flag = save_citrix_detailed_data(now_timestamp, result_json)
                logger.info(f'Processed detailed citrix data from {filename}')
            else:
                # Save to unified citrix table (original format)
                flag = save_citrix_data(now_timestamp, result_json)
                logger.info(f'Processed unified citrix data from {filename}')

        if setting.DEBUG_FLAG != 'true' and flag:
            os.unlink(filepath)
    except Exception as e:
        logger.error('save {} meet: {}'.format(filepath, e))


def get_last_time(src, time_key):
    last_time = None
    try:
        if src == 'aliveScan':
            mysql_DB = MysqlDB(setting.mysql_scan_db)
            last_time = mysql_DB.get_last_time(setting.mysql_scan_stat_tb, time_key)
        elif src == 'webStatus':
            mysql_DB = MysqlDB(setting.mysql_webstatus_db)
            last_time = mysql_DB.get_last_time(setting.mysql_web_status_stat_tb, time_key)
        elif src == 'vulAliveLine':
            mysql_DB = MysqlDB(setting.mysql_citrix_db)
            last_time = mysql_DB.get_last_time(setting.mysql_citrix_stat_tb, time_key)
    except Exception as e:
        logger.error(f'get_last_time error: {e}, return None')
    return last_time


def save_scan_data(_timestamp, data, _country_prov_pd):
    flag = True
    mysql_DB = MysqlDB(settings.mysql_scan_db)
    mysql_DB.create_table(settings.sql_create_scan_tb)
    mysql_DB.create_table(settings.sql_create_scan_stat_tb)
    try:
        mysql_DB.insert(settings.sql_insert_scan_tb, mysql_DB.gen_scan_insert_list(_timestamp, data))
        mysql_DB.insert(settings.sql_insert_scan_stat_tb, mysql_DB.gen_scan_stat_insert_list(_timestamp, data, _country_prov_pd))
    except Exception as e:
        logger.error(f'save_scan_data: {e}')
        flag = False
    finally:
        mysql_DB.close()
    if flag:
        logger.success('save scan {} to db finished'.format(str(_timestamp)))
    return flag


def save_web_data(_timestamp, data):
    flag = True
    mysql_DB = MysqlDB(settings.mysql_webstatus_db)
    mysql_DB.create_table(settings.sql_create_request_db)
    mysql_DB.create_table(settings.sql_create_request_stat_db)
    try:
        if type(data['web'][0]) == list:
            mysql_DB.insert(settings.sql_insert_request_db, [tuple(i) for i in data['web']])
            mysql_DB.insert(settings.sql_insert_request_stat_db, [tuple(i) for i in data['web_status']])
        else:
            mysql_DB.insert(settings.sql_insert_request_db, data['web'])
            mysql_DB.insert(settings.sql_insert_request_stat_db, data['web_status'])
    except Exception as e:
        logger.error(f'save_web_data: {e}')
        flag = False
    finally:
        mysql_DB.close()
    if flag:
        logger.success('save web {} to db finished'.format(str(_timestamp)))
    return flag


def save_citrix_data(_timestamp, data):
    """Save vulAliveLine (citrix scan) data to database"""
    flag = True
    mysql_DB = MysqlDB(settings.mysql_citrix_db)
    mysql_DB.create_table(settings.sql_create_citrix_tb)
    mysql_DB.create_table(settings.sql_create_citrix_stat_tb)
    try:
        if type(data['citrix_scan'][0]) == list:
            mysql_DB.insert(settings.sql_insert_citrix_tb, mysql_DB.gen_citrix_insert_list(_timestamp, [tuple(i) for i in data['citrix_scan']]))
            mysql_DB.insert(settings.sql_insert_citrix_stat_tb, mysql_DB.gen_citrix_stat_insert_list(_timestamp, [tuple(i) for i in data['citrix_stats']]))
        else:
            mysql_DB.insert(settings.sql_insert_citrix_tb, mysql_DB.gen_citrix_insert_list(_timestamp, data['citrix_scan']))
            mysql_DB.insert(settings.sql_insert_citrix_stat_tb, mysql_DB.gen_citrix_stat_insert_list(_timestamp, data['citrix_stats']))
    except Exception as e:
        logger.error(f'save_citrix_data: {e}')
        flag = False
    finally:
        mysql_DB.close()
    if flag:
        logger.success('save citrix {} to db finished'.format(str(_timestamp)))
    return flag


def save_citrix_detailed_data(_timestamp, data):
    """Save detailed vulAliveLine (citrix scan) data to database"""
    flag = True
    mysql_DB = MysqlDB(settings.mysql_citrix_db)
    mysql_DB.create_table(settings.sql_create_citrix_detailed_tb)
    mysql_DB.create_table(settings.sql_create_citrix_detailed_stat_tb)
    try:
        # Process detailed results
        detailed_insert_list = mysql_DB.gen_citrix_detailed_insert_list(_timestamp, data['results'])
        mysql_DB.insert(settings.sql_insert_citrix_detailed_tb, detailed_insert_list)

        # Process detailed statistics
        stats = data['scan_info']['stats']
        detailed_stat_insert_list = mysql_DB.gen_citrix_detailed_stat_insert_list(_timestamp, stats)
        mysql_DB.insert(settings.sql_insert_citrix_detailed_stat_tb, detailed_stat_insert_list)

    except Exception as e:
        logger.error(f'save_citrix_detailed_data: {e}')
        flag = False
    finally:
        mysql_DB.close()
    if flag:
        logger.success('save citrix detailed {} to db finished'.format(str(_timestamp)))
    return flag


def process_exist_files(src, target_pd):
    last_time = get_last_time(src, 'request_time')
    if not last_time:
        return
    logger.info(f'last_time: {last_time}')
    if src == 'aliveScan':
        path = setting.scan_results_save_dir
        process_fn = process_scan_results
    elif src == 'webStatus':
        path = setting.webstatus_results_save_dir
        process_fn = process_web_results
    elif src == 'vulAliveLine':
        path = setting.citrix_results_save_dir
        process_fn = process_citrix_results
    for file in os.listdir(path):
        if file.endswith('.json'):
            tmp_timestamp = int(os.path.basename(file).split('.')[0].split('_')[-1])
            if tmp_timestamp > last_time:
                logger.info(f'exist new file: {file}')
                process_fn(os.path.join(path, file), target_pd)


def main(target_pd):
    for i_datasource in setting.remote_datasource.keys():
        process_exist_files(i_datasource, target_pd)
        results_file = fetch_results(i_datasource)
        if i_datasource == 'aliveScan':
            for i in results_file:
                process_scan_results(i, target_pd)
        elif i_datasource == 'webStatus':
            for i in results_file:
                process_web_results(i)
        elif i_datasource == 'vulAliveLine':
            for i in results_file:
                process_citrix_results(i)

    logger.info('END')


def sftp_process(datasource_type):
    transport = paramiko.Transport((setting.vps_host, int(setting.vps_port)))
    transport.connect(username=setting.vps_user, password=setting.vps_key)
    sftp = paramiko.SFTPClient.from_transport(transport)
    timestamp = []
    local_file_ls = []

    for i in sftp.listdir(setting.remote_datasource[datasource_type]):
        if i.endswith('.json'):
            tmp_timestamp = int(os.path.basename(i).split('.')[0].split('_')[-1])
            timestamp.append(tmp_timestamp)
    timestamp.sort()
    max_timestamp = max(timestamp)
    if len(timestamp) == 1:
        pass
    else:
        for ts_n, i in (enumerate(timestamp)):
            if datasource_type == 'aliveScan':
                filename = 'masscan_{}.json'.format(str(i))
                local_filepath = setting.scan_results_save_dir.joinpath(filename).__str__()
            elif datasource_type == 'webStatus':
                filename = 'web_{}.json'.format(str(i))
                local_filepath = setting.webstatus_results_save_dir.joinpath(filename).__str__()
            elif datasource_type == 'vulAliveLine':
                filename = 'citrix_{}.json'.format(str(i))
                local_filepath = setting.citrix_results_save_dir.joinpath(filename).__str__()

            remote_filepath = setting.remote_datasource[datasource_type]+'/'+filename

            if i != max_timestamp and not os.path.exists(local_filepath):
                logger.info(f'{ts_n+1}/{len(timestamp)}: new data {remote_filepath}')
                try:
                    sftp.get(remote_filepath, local_filepath)
                    if setting.DEBUG_FLAG != 'true':
                        sftp.unlink(remote_filepath)
                    local_file_ls.append(local_filepath)
                except Exception as e:
                    logger.error('fetch {} meet {}'.format(local_filepath, e))
    transport.close()
    logger.info(f'local files: {local_file_ls}')
    return local_file_ls


def run_scheduler():
    target_pd = pd.read_csv(settings.data_storage_dir.joinpath('merged_sample_ip.csv'))

    # Add database initialization before starting scheduler
    scan_db = MysqlDB(settings.mysql_scan_db)
    scan_db.create_table(settings.sql_create_scan_tb)
    scan_db.create_table(settings.sql_create_scan_stat_tb)  # Explicitly create scan_stat table
    scan_db.close()

    web_db = MysqlDB(settings.mysql_webstatus_db)
    web_db.create_table(settings.sql_create_request_db)
    web_db.create_table(settings.sql_create_request_stat_db)
    web_db.close()

    citrix_db = MysqlDB(settings.mysql_citrix_db)
    citrix_db.create_table(settings.sql_create_citrix_tb)
    citrix_db.create_table(settings.sql_create_citrix_stat_tb)
    citrix_db.create_table(settings.sql_create_citrix_detailed_tb)
    citrix_db.create_table(settings.sql_create_citrix_detailed_stat_tb)
    citrix_db.close()

    scheduler = BlockingScheduler(timezone=settings.TIMEZONE)
    start_time = (datetime.datetime.utcnow().replace(tzinfo=None) + datetime.timedelta(seconds=10)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(main, 'date', run_date=start_time, id="reqeust_init", name="reqeust_init", args=[target_pd, ])
    scheduler.add_job(main, 'interval', minutes=60, id="reqeust", name="reqeust", args=[target_pd, ])
    job_defaults = {
        'coalesce': False,
        'max_instances': 20
    }
    scheduler.configure(job_defaults=job_defaults, timezone=settings.TIMEZONE)
    scheduler.start()


if __name__ == '__main__':
    run_scheduler()
    #masscan(settings.data_storage_dir.joinpath('target_ip_list.txt'))
