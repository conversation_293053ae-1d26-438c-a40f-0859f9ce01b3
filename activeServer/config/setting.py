# coding=utf-8
import pathlib
import warnings
import os

# 配置文件传参
mysql_host = os.environ.get('DB_HOST', '127.0.0.1')
mysql_port = int(os.environ.get('DB_PORT', 3306))
mysql_scan_db = os.environ.get('DB_SCAN_NAME', 'scan')
mysql_scan_tb = os.environ.get('DB_SCAN_TABLE', 'scan')
mysql_scan_stat_tb = os.environ.get('DB_SCAN_STAT_TABLE', 'scan_stat')
mysql_webstatus_db = os.environ.get('DB_WEB_NAME', 'web_status')
mysql_web_reqeust_tb = os.environ.get('DB_WEB_TABLE', 'web_reqeust')
mysql_web_status_stat_tb = os.environ.get('DB_WEBSTAT_TABLE', 'web_status_stat')
mysql_citrix_db = os.environ.get('DB_CITRIX_NAME', 'citrix_scan')
mysql_citrix_tb = os.environ.get('DB_CITRIX_TABLE', 'citrix_scan')
mysql_citrix_stat_tb = os.environ.get('DB_CITRIX_STAT_TABLE', 'citrix_stat')
mysql_citrix_detailed_tb = os.environ.get('DB_CITRIX_DETAILED_TABLE', 'citrix_detailed')
mysql_citrix_detailed_stat_tb = os.environ.get('DB_CITRIX_DETAILED_STAT_TABLE', 'citrix_detailed_stat')
mysql_user = os.environ.get('DB_USER', 'root')
mysql_password = os.environ.get('DB_PASSWORD', '1qaz2wsx')
DEBUG_FLAG = os.environ.get('DEBUG_FLAG', "true")

remote_datasource = {
    'aliveScan': '/root/activeClient/aliveScanClient/results',
    'webStatus': '/root/activeClient/webStatusMonitor/results',
    'vulAliveLine': '/root/activeClient/vulAliveLine/results'
}
vps_host = os.environ.get('VPS_HOST', '*************')
vps_port = int(os.environ.get('VPS_PORT', 22))
vps_user = os.environ.get('VPS_USER', 'root')
vps_key = os.environ.get('VPS_KEY', '7O0srQb0v1')

nodes_config_file = 'nodes.json'
tasks_config_file = 'task.json'


# 禁用所有警告信息
warnings.filterwarnings("ignore")

# 路径设置
relative_directory = pathlib.Path(__file__).parent.parent  # 代码相对路径
data_storage_dir = relative_directory.joinpath('data')  # 数据存放目录
scan_results_save_dir = relative_directory.joinpath('scan_results')  # 结果保存目录
webstatus_results_save_dir = relative_directory.joinpath('webstatus_results')  # 结果保存目录
citrix_results_save_dir = relative_directory.joinpath('citrix_results')  # vulAliveLine结果保存目录
chrome_USER_AGENT_FILE = data_storage_dir.joinpath('chrome.txt')
firefox_USER_AGENT_FILE = data_storage_dir.joinpath('firefox.txt')

# 数据库操作
TIMEZONE = "UTC"
mysql_charset = 'utf8'  # 只能写utf8，不能写utf-8
sql_create_scan_tb = f"CREATE TABLE if not exists {mysql_scan_tb} ( \
                request_time BIGINT(20) DEFAULT NULL, \
                ip varchar(20) DEFAULT NULL);"

sql_create_scan_stat_tb = f"CREATE TABLE if not exists {mysql_scan_stat_tb} ( \
                request_time BIGINT(20) DEFAULT NULL, \
                country_code varchar(12) DEFAULT NULL, \
                province varchar(128) DEFAULT NULL, \
                total INT(10) DEFAULT NULL, \
                alive_cnt INT(10) DEFAULT NULL);"

sql_insert_scan_tb = f"INSERT INTO {mysql_scan_tb} ( \
                request_time, ip) VALUES (%s, %s);"

sql_insert_scan_stat_tb = f"INSERT INTO {mysql_scan_stat_tb} ( \
                request_time, country_code, province, total, alive_cnt) VALUES (%s, %s, %s, %s, %s);"

sql_create_request_db = f"CREATE TABLE if not exists {mysql_web_reqeust_tb} ( \
                request_time BIGINT(20) DEFAULT NULL, \
                country_code varchar(12) DEFAULT NULL, \
                org varchar(256) DEFAULT NULL, \
                url varchar(256) DEFAULT NULL, \
                reason varchar(1024) DEFAULT NULL, \
                request INT(4) DEFAULT NULL, \
                status INT(4) DEFAULT NULL, \
                alive  INT(2) DEFAULT NULL);"
                #'achievable_rate' float(1,4) DEFAULT NULL);'''

sql_create_request_stat_db = f"CREATE TABLE if not exists {mysql_web_status_stat_tb} ( \
                request_time BIGINT(20) DEFAULT NULL, \
                country_code varchar(12) DEFAULT NULL, \
                total INT(10) DEFAULT NULL, \
                request_cnt  INT(10) DEFAULT NULL, \
                dead_cnt  INT(10) DEFAULT NULL, \
                alive_cnt  INT(10) DEFAULT NULL);"

sql_create_citrix_tb = f"CREATE TABLE if not exists {mysql_citrix_tb} ( \
                request_time BIGINT(20) DEFAULT NULL, \
                country_code varchar(12) DEFAULT NULL, \
                ip varchar(20) DEFAULT NULL, \
                https_accessible INT(2) DEFAULT NULL, \
                status_code INT(4) DEFAULT NULL, \
                last_modified varchar(256) DEFAULT NULL, \
                before_target_date INT(2) DEFAULT NULL, \
                server varchar(256) DEFAULT NULL, \
                content_length INT(10) DEFAULT NULL, \
                response_time FLOAT(10,3) DEFAULT NULL, \
                error varchar(1024) DEFAULT NULL);"

sql_create_citrix_stat_tb = f"CREATE TABLE if not exists {mysql_citrix_stat_tb} ( \
                request_time BIGINT(20) DEFAULT NULL, \
                country_code varchar(12) DEFAULT NULL, \
                total INT(10) DEFAULT NULL, \
                successful INT(10) DEFAULT NULL, \
                failed INT(10) DEFAULT NULL, \
                before_target_date INT(10) DEFAULT NULL, \
                after_target_date INT(10) DEFAULT NULL, \
                no_last_modified INT(10) DEFAULT NULL);"

sql_create_citrix_detailed_tb = f"CREATE TABLE if not exists {mysql_citrix_detailed_tb} ( \
                request_time BIGINT(20) DEFAULT NULL, \
                country_code varchar(12) DEFAULT NULL, \
                ip varchar(20) DEFAULT NULL, \
                port varchar(10) DEFAULT NULL, \
                ip_port varchar(32) DEFAULT NULL, \
                protocol varchar(10) DEFAULT NULL, \
                https_accessible INT(2) DEFAULT NULL, \
                http_accessible INT(2) DEFAULT NULL, \
                rtsp_accessible INT(2) DEFAULT NULL, \
                status_code INT(4) DEFAULT NULL, \
                last_modified varchar(256) DEFAULT NULL, \
                last_modified_parsed varchar(256) DEFAULT NULL, \
                before_target_date INT(2) DEFAULT NULL, \
                server varchar(256) DEFAULT NULL, \
                content_length INT(10) DEFAULT NULL, \
                response_time FLOAT(10,3) DEFAULT NULL, \
                error varchar(1024) DEFAULT NULL, \
                response_headers TEXT DEFAULT NULL, \
                response_body TEXT DEFAULT NULL);"

sql_create_citrix_detailed_stat_tb = f"CREATE TABLE if not exists {mysql_citrix_detailed_stat_tb} ( \
                request_time BIGINT(20) DEFAULT NULL, \
                country_code varchar(12) DEFAULT NULL, \
                total INT(10) DEFAULT NULL, \
                successful INT(10) DEFAULT NULL, \
                failed INT(10) DEFAULT NULL, \
                before_target_date INT(10) DEFAULT NULL, \
                after_target_date INT(10) DEFAULT NULL, \
                no_last_modified INT(10) DEFAULT NULL, \
                https_count INT(10) DEFAULT NULL, \
                http_count INT(10) DEFAULT NULL, \
                rtsp_count INT(10) DEFAULT NULL);"

sql_insert_request_db = f"INSERT INTO {mysql_web_reqeust_tb} ( \
                request_time, country_code, org, url, reason, request, status, alive) VALUES (%s, %s, %s, %s, %s, %s, %s, %s);"

sql_insert_request_stat_db = f"INSERT INTO {mysql_web_status_stat_tb} ( \
                request_time, country_code, total, request_cnt, dead_cnt, alive_cnt) VALUES (%s, %s, %s, %s, %s, %s);"

sql_insert_citrix_tb = f"INSERT INTO {mysql_citrix_tb} ( \
                request_time, country_code, ip, https_accessible, status_code, last_modified, before_target_date, server, content_length, response_time, error) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);"

sql_insert_citrix_stat_tb = f"INSERT INTO {mysql_citrix_stat_tb} ( \
                request_time, country_code, total, successful, failed, before_target_date, after_target_date, no_last_modified) VALUES (%s, %s, %s, %s, %s, %s, %s, %s);"

sql_insert_citrix_detailed_tb = f"INSERT INTO {mysql_citrix_detailed_tb} ( \
                request_time, country_code, ip, port, ip_port, protocol, https_accessible, http_accessible, rtsp_accessible, status_code, last_modified, last_modified_parsed, before_target_date, server, content_length, response_time, error, response_headers, response_body) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);"

sql_insert_citrix_detailed_stat_tb = f"INSERT INTO {mysql_citrix_detailed_stat_tb} ( \
                request_time, country_code, total, successful, failed, before_target_date, after_target_date, no_last_modified, https_count, http_count, rtsp_count) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);"

# sql_fetch_scan_last_time = f"SELECT MAX(time) FROM {mysql_scan_stat_tb}"
sql_fetch_last_time = f"SELECT MAX(%s) FROM %s;"
