# coding=utf-8
import pymysql
from config import setting
import json
import arrow
from loguru import logger


def get_db_connect(host, port, user, password, db, charset='utf8mb4'):
    """连接数据库并返回数据库连接-数据库不存在则创建数据库"""
    conn = pymysql.connect(host=host, port=port, user=user, password=password,
                           charset=charset, autocommit=True, cursorclass=pymysql.cursors.DictCursor)
    conn.cursor().execute('CREATE DATABASE IF NOT EXISTS %s;' % db)
    conn.select_db(db)
    return conn


class MysqlDB:
    def __init__(self, _db=setting.mysql_webstatus_db):
    # def __init__(self, _db):
        # 创建连接对象
        self.conn = get_db_connect(host=setting.mysql_host, password=setting.mysql_password, user=setting.mysql_user,
                               db=_db, port=setting.mysql_port, charset=setting.mysql_charset)

    def create_table(self, sql_command):
        cur = self.conn.cursor()
        try:
            res = cur.execute(sql_command)  # 只是帮你执行sql语句，不会返回执行结果
            self.conn.commit()
            #print('create or check table success ')
        except Exception as e:
            logger.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return 

    def insert(self, sql_command, insertlist):
        cur = self.conn.cursor()
        try:
            cur.executemany(sql_command, insertlist)
            self.conn.commit()
        except Exception as e:
            logger.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return

    def insert_one(self, sql_command, item):
        cur = self.conn.cursor()
        try:
            cur.execute(sql_command, item)
            self.conn.commit()
        except Exception as e:
            logger.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return

    def close(self):
        self.conn.close()

    def get_last_time(self, tb, time_key='time'):
        cur = self.conn.cursor()
        last_timestamp = 0  # Default to 0 instead of empty string
        try:
            cur.execute(setting.sql_fetch_last_time % (time_key, tb))
            result = cur.fetchone()
            if result and result[f'MAX({time_key})']:  # Add NULL check
                last_timestamp = int(result[f'MAX({time_key})'])
            self.conn.commit()
        except Exception as e:
            logger.error(f'get_last_time error: {e}')
            self.conn.rollback()
        finally:
            cur.close()
        return last_timestamp  # Always return integer

    @staticmethod
    def gen_scan_insert_list(_time, data):
        insert_list = []  # 新建一个空列表用来存储元组数据
        for ip in list(data):
            tmp_item = (_time, ip)  # 构造元组
            insert_list.append(tmp_item)  # [(),(),()...]
        return insert_list

    @staticmethod
    def gen_scan_stat_insert_list(_time, data, _country_prov_pd):
        alive_stat = dict()
        country_prov_dict = _country_prov_pd.groupby(['country_code', 'province'])['ip'].count().to_dict()
        insert_list = []
        for ip in list(data):
            tmp_country_prov = tuple(_country_prov_pd[_country_prov_pd['ip'] == ip][['country_code','province']].to_numpy().tolist()[0])
            if tmp_country_prov not in alive_stat:
                alive_stat[tmp_country_prov] = 1
            else:
                alive_stat[tmp_country_prov] += 1
        for index, _value in alive_stat.items():
            tmp_item = (_time, index[0], index[1], country_prov_dict[index] , _value)  # 构造元组
            insert_list.append(tmp_item)  # [(),(),()...]
        # logger.info(insert_list)
        return insert_list

    @staticmethod
    def gen_request_insert_list(_time: str, data: json):
        insert_list = []  # 新建一个空列表用来存储元组数据
        for country, rate in data.items():
            tmp_item = (_time, country, rate)  # 构造元组
            insert_list.append(tmp_item)  # [(),(),()...]
        return insert_list

    @staticmethod
    def gen_citrix_insert_list(_time, data):
        """Generate insert list for citrix scan data"""
        insert_list = []
        for item in data:
            # item is a tuple: (timestamp, country, ip, https_accessible, status_code, last_modified, before_target_date, server, content_length, response_time, error)
            # Replace timestamp with _time parameter
            tmp_item = (_time, item[1], item[2], item[3], item[4], item[5], item[6], item[7], item[8], item[9], item[10])
            insert_list.append(tmp_item)
        return insert_list

    @staticmethod
    def gen_citrix_stat_insert_list(_time, data):
        """Generate insert list for citrix statistics data"""
        insert_list = []
        for item in data:
            # item is a tuple: (timestamp, country, total, successful, failed, before_target_date, after_target_date, no_last_modified)
            # Replace timestamp with _time parameter
            tmp_item = (_time, item[1], item[2], item[3], item[4], item[5], item[6], item[7])
            insert_list.append(tmp_item)
        return insert_list

    @staticmethod
    def gen_citrix_detailed_insert_list(_time, data):
        """Generate insert list for detailed citrix scan data"""
        import json
        insert_list = []
        for result in data:
            # Convert response_headers dict to JSON string
            response_headers_json = json.dumps(result.get('response_headers', {})) if result.get('response_headers') else None

            tmp_item = (
                _time,
                'global',  # country_code
                result.get('ip', ''),
                result.get('port', ''),
                result.get('ip_port', ''),
                result.get('protocol', ''),
                1 if result.get('https_accessible', False) else 0,
                1 if result.get('http_accessible', False) else 0,
                1 if result.get('rtsp_accessible', False) else 0,
                result.get('status_code', 0),
                result.get('last_modified', ''),
                result.get('last_modified_parsed', ''),
                1 if result.get('before_target_date', False) else 0,
                result.get('server', ''),
                result.get('content_length', 0),
                result.get('response_time', 0.0),
                result.get('error', ''),
                response_headers_json,
                result.get('response_body', '')[:10240] if result.get('response_body') else ''  # Limit to 10KB
            )
            insert_list.append(tmp_item)
        return insert_list

    @staticmethod
    def gen_citrix_detailed_stat_insert_list(_time, stats):
        """Generate insert list for detailed citrix statistics data"""
        # Count protocol usage from the stats
        https_count = stats.get('https_count', 0)
        http_count = stats.get('http_count', 0)
        rtsp_count = stats.get('rtsp_count', 0)

        # If protocol counts are not available, estimate from successful connections
        if https_count == 0 and http_count == 0 and rtsp_count == 0:
            # Fallback: assume most successful connections are HTTP
            https_count = stats.get('successful', 0) // 4  # Rough estimate
            http_count = stats.get('successful', 0) - https_count
            rtsp_count = 0

        tmp_item = (
            _time,
            'global',  # country_code
            stats.get('total', 0),
            stats.get('successful', 0),
            stats.get('failed', 0),
            stats.get('before_target_date', 0),
            stats.get('after_target_date', 0),
            stats.get('no_last_modified', 0),
            https_count,
            http_count,
            rtsp_count
        )
        return [tmp_item]


if __name__ == '__main__':
    #with open('upd-file_20230812.0915.json', 'r', encoding='utf-8') as f:
    #    tmp_data = json.loads(f.read())
    import pandas as pd
    target_pd = pd.read_csv(setting.data_storage_dir.joinpath('merged_sample_ip.csv'))
    a = MysqlDB()
    print(a.gen_scan_stat_insert_list(234625462, ["***************", "***************", '**************'], target_pd))