# coding=utf-8
import math
import time
from config import settings
from config.log import logger
from pathlib import Path
from netaddr import *
import arrow
import numpy as np
import os, re, random
import shutil
import datetime
import requests
import stat
import socket
from requests.adapters import HTTPAdapter
from urllib3.util.ssl_ import create_urllib3_context
requests.packages.urllib3.disable_warnings()
IP_RE = re.compile(r'^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$')  # pylint: disable=line-too-long


class CipherAdapter(HTTPAdapter):
    def init_poolmanager(self, *args, **kwargs):
        context = create_urllib3_context(ciphers='DEFAULT:@SECLEVEL=2')
        kwargs['ssl_context'] = context
        return super(Cip<PERSON><PERSON>dapter, self).init_poolmanager(*args, **kwargs)

    def proxy_manager_for(self, *args, **kwargs):
        context = create_urllib3_context(ciphers='DEFAULT:@SECLEVEL=2')
        kwargs['ssl_context'] = context
        return super(CipherAdapter, self).proxy_manager_for(*args, **kwargs)


def get_data(filepath):
    #root_folder = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
    #data_file = os.path.join(root_folder, filepath)
    try:
        with open(filepath, 'r') as fp:
            data = [_.strip() for _ in fp.readlines()]
        return data
    except:
        raise FileNotFoundError("%s" % filepath)


def listfile_all(path, recursion=False, extend_name=[]):
    """

    :param path:
    :param recursion:
    :param extend_name:
    :return:
    """
    all_file_name = []
    all_dir_name = []

    for file in os.listdir(path):
        subfile_path = os.path.join(path, file)
        if os.path.isdir(subfile_path):
            all_dir_name.append(subfile_path)
            if recursion==True:
                #print(subfile_path)
                all_file_name += listfile_all(subfile_path, True, extend_name)
            else:
                continue
        elif len(extend_name) == 0:
            all_file_name.append(subfile_path)
        elif os.path.splitext(subfile_path)[1] in extend_name:
            all_file_name.append(subfile_path)
        else:
            # print(subfile_path)
            # not selected file_extend
            pass

    return (all_file_name, all_dir_name)


def delete_dir(rootdir, delete_rootdir=False):
    def del_rw(action, name, exc):
        os.chmod(name, stat.S_IWRITE)
        os.remove(name)

    try:
        filelist = os.listdir(rootdir)
    except:
        print('%s is not exist' % rootdir)
        return False

    for f in filelist:
        filepath = os.path.join(rootdir, f)
        if os.path.isfile(filepath):
            os.remove(filepath)
        elif os.path.isdir(filepath):
            shutil.rmtree(filepath, onerror=del_rw)

    if delete_rootdir:
        shutil.rmtree(rootdir, onerror=del_rw)

    return True


def copydir(ori_dir, output_dir):
    """

    :param ori_dir:
    :param output_dir:
    :return:
    """
    try:
        shutil.copytree(ori_dir, output_dir)
    except Exception as e:
        print(e)


def charsets(res):
    _charset = requests.utils.get_encoding_from_headers(res.headers)
    if _charset == 'ISO-8859-1':
        __charset = requests.utils.get_encodings_from_content(res.text)
        if __charset:
            _charset = __charset[0]
        else:
            _charset = res.apparent_encoding

    return _charset


def check_chinese(check_str):
   for ch in check_str.decode('utf-8'):
       if u'\u4e00' <= ch <= u'\u9fff':
             return True
   return False


def check_time(str1, str2):
    """

    :param str1:
    :param str2:
    :return:
    """
    date1 = datetime.datetime.strptime(str1[11:16], "%H:%M")
    date2 = datetime.datetime.strptime(str2[11:16], "%H:%M")
    num = (date1 - date2).seconds
    return num


def clean_str(_string, punctuation=' '):
    import re
    #  输出中英文、数字及所有标点，过滤特殊符号
    _string = re.sub(r'[^\s\w\n0-9\u0020-\u007f\u2000-\u206f\u3000-\u303f\uff00-\uffef]+', punctuation, _string.strip(' '), re.I | re.U)
    # $指字符串结尾的
    _string = re.sub(r'([\s]+)$', '\n', _string.strip(' '))

    return _string


def time_transfer(publish_time_input, GMT_FORMAT='%a %b %d %H:%M:%S UTC %Y'):

    publish_time_output = datetime.datetime.strptime(publish_time_input, GMT_FORMAT).strftime('%Y-%m-%d %H:%M:%S')

    return publish_time_output


def get_pwd():
    '''
    print("__file__= %s" % __file__)
    print("os.path.realpath(__file__)= %s" % os.path.realpath(__file__))
    print("os.path.dirname(os.path.realpath(__file__))= %s" % os.path.dirname(os.path.realpath(__file__)))
    print("os.path.split(os.path.realpath(__file__))= %s" % os.path.split(os.path.realpath(__file__))[0])
    print("os.path.abspath(__file__)= %s" % os.path.abspath(__file__))
    print("os.getcwd()= %s" % os.getcwd())
    print("sys.path[0]= %s" % sys.path[0])
    print("sys.argv[0]= %s" % sys.argv[0])
    '''
    return os.getcwd()


def my_mkdir(path):
    path = str(path).replace('\\', '/')
    if not os.path.exists(path):
        exists_flag = False
        iter = 1
        while not exists_flag:
            iter_path = '/'.join(path.split('/')[:iter])
            if iter_path == path:
                exists_flag = True
            if os.path.exists(iter_path):
                iter += 1
            else:
                os.makedirs(iter_path)
    return


def clean_filename(sourcestring, removestring ="\"|%:/,\\[]<>*?"):
    return ''.join([c for c in sourcestring if c not in removestring])


def looks_like_ip(maybe_ip):
    """Does the given str look like an IP address?"""
    if not maybe_ip[0].isdigit():
        return False

    try:
        socket.inet_aton(maybe_ip)
        return True
    except (AttributeError, UnicodeError):
        if IP_RE.match(maybe_ip):
            return True
    except socket.error:
        return False


def sort_dc_by_value(_dc, reverse=False, _top=None):
    # default small to large
    keys = list(_dc.keys())
    values = list(_dc.values())
    if reverse:
        sorted_value_index = np.argsort(values)[::-1]  # from large to small
    else:
        sorted_value_index = np.argsort(values)
    if isinstance(_top, int):
        sorted_value_index = sorted_value_index[:_top]

    sorted_dict = {keys[i]: values[i] for i in sorted_value_index}
    return sorted_dict


def get_utctime_str_format(_format='YYYY-MM-DDTHH:mm:ssZZ'):
    return arrow.utcnow().format(_format)


def get_pastdays_utc_timestamp(_hours=144):
    return math.floor(arrow.utcnow().shift(hours=-_hours).timestamp())


def get_utc_timestamp(only_int=True):
    if only_int:
        return math.floor(arrow.utcnow().timestamp())
    else:
        #datetime.utcnow().timestamp() 这个时间不对，最后一个会转换时区
        #datetime.now(timezone.utc).timestamp()  可以使用这个
        return arrow.utcnow().timestamp()


def move_file_to_dir(filepath, dirpath):
    filename = os.path.basename(filepath)
    shutil.move(filepath, os.path.join(dirpath, filename))


def utctime_to_timestamp(format_time, _timezone='Asia/Shanghai'):
    return arrow.get(format_time, tzinfo=_timezone).to('UTC').timestamp()


def targettime_to_utctime(format_time, _timezone='Europe/Kiev', format="YYYY-MM-DD HH:mm:ss"):
    return arrow.get(format_time, tzinfo=_timezone).to('UTC').format(format)


def targettime_to_utctimestamp(targettime, _timezone='Europe/Kiev'):
    return arrow.get(targettime).to(_timezone).to('UTC').timestamp()


def utctimestamp_to_targetarea_fmt(timestamp, _timezone='Europe/Kiev', format="YYYY-MM-DD HH:mm:ss"):
    return arrow.get(timestamp, tzinfo='UTC').to(_timezone).format(format)


def formattime_transfor(format_time, in_format="%Y-%m-%d %H:%M:%S", out_format="%Y%m%d_%H%M%S"):
    #time_array = datetime.datetime.strptime(format_time, in_format)
    #return datetime.datetime.strftime(out_format, time_array)
    time_array = time.strptime(format_time, in_format)
    return time.strftime(out_format, time_array)


def CIDR2IP(cidr):
    ip_set = IPSet([cidr])
    return [i.__str__() for i in ip_set]


def IP2CIDR(startip, endip):
    iprange = IPRange(startip, endip)
    return [i.__str__() for i in iprange.cidrs()]


def IP2INT(ip):
    return int(IPAddress(ip))


def INT2IP(ip_int):
    return str(IPAddress(ip_int))


def CIDR2INTrange(cidr):
    ip_range = IPSet([cidr]).iprange()
    return [ip_range.first, ip_range.last]


def get_classname(classobj):
    return classobj.__class__.__name__


def charsets(res):
    _charset = requests.utils.get_encoding_from_headers(res.headers)
    if _charset == 'ISO-8859-1':
        __charset = requests.utils.get_encodings_from_content(res.text)
        if __charset:
            _charset = __charset[0]
        else:
            _charset = res.apparent_encoding

    return _charset


def extract_root_page(url):
    if url.startswith('http'):
        return '/'.join(str(url).split('/')[:3])
    else:
        return 'https://' + url.split('/')[0]


def check_chinese(check_str):
   for ch in check_str.decode('utf-8'):
       if u'\u4e00' <= ch <= u'\u9fff':
             return True
   return False


def check_time(str1, str2):
    """

    :param str1:
    :param str2:
    :return:
    """
    date1 = datetime.datetime.strptime(str1[11:16], "%H:%M")
    date2 = datetime.datetime.strptime(str2[11:16], "%H:%M")
    num = (date1 - date2).seconds
    return num


def sort_time(time_str_ls, time_format="%Y-%m-%dT%H:%M:%SZ"):
    """

    :param time_str_ls:
    :return:
    """
    return time_str_ls.sort(key=lambda date: datetime.datetime.strptime(date, time_format))


def clean_str(_string, punctuation=' '):
    import re
    #  输出中英文、数字及所有标点，过滤特殊符号
    _string = re.sub(r'[^\s\w\n0-9\u0020-\u007f\u2000-\u206f\u3000-\u303f\uff00-\uffef]+', punctuation, _string.strip(' '), re.I | re.U)
    # $指字符串结尾的
    _string = re.sub(r'([\s]+)$', '\n', _string.strip(' '))

    return _string


def time_transfer(publish_time_input, GMT_FORMAT='%a %b %d %H:%M:%S UTC %Y'):
    publish_time_output = datetime.datetime.strptime(publish_time_input, GMT_FORMAT).strftime('%Y-%m-%d %H:%M:%S')
    return publish_time_output


def get_pwd():
    '''
    print("__file__= %s" % __file__)
    print("os.path.realpath(__file__)= %s" % os.path.realpath(__file__))
    print("os.path.dirname(os.path.realpath(__file__))= %s" % os.path.dirname(os.path.realpath(__file__)))
    print("os.path.split(os.path.realpath(__file__))= %s" % os.path.split(os.path.realpath(__file__))[0])
    print("os.path.abspath(__file__)= %s" % os.path.abspath(__file__))
    print("os.getcwd()= %s" % os.getcwd())
    print("sys.path[0]= %s" % sys.path[0])
    print("sys.argv[0]= %s" % sys.argv[0])
    '''
    return os.getcwd()


def my_mkdir(path):
    path = str(path).replace('\\', '/')
    if not os.path.exists(path):
        exists_flag = False
        iter = 1
        while not exists_flag:
            iter_path = '/'.join(path.split('/')[:iter])
            if iter_path == path:
                exists_flag = True
            if os.path.exists(iter_path):
                iter += 1
            else:
                os.makedirs(iter_path)
    return


def filter_emoji(desstr, restr=''):
    # 过滤表情
    res = re.compile(u'[\U00010000-\U0010ffff\\uD800-\\uDBFF\\uDC00-\\uDFFF]')
    return res.sub(restr, desstr)


def _decode(value, code="gbk"):
        try:
            value = value.decode(code, "ignore")
        except:
            pass
        return value


def _encode(value, code="utf-8"):
    if isinstance(value, str):
        try:
            value = value.encode(code)
        except UnicodeEncodeError:
            value = value.encode("utf-8", "replace")
    return value


def time_sleep(wait_time=None):
    if wait_time:
        time.sleep(random.choice(range(0, math.ceil(wait_time)*10))/10)
    else:
        time.sleep(random.choice(range(5, 10)))


def get_fake_ua():
    chrome_ua = get_data(settings.chrome_USER_AGENT_FILE)
    firefox_ua = get_data(settings.firefox_USER_AGENT_FILE)
    return random.choice(chrome_ua + firefox_ua)


def check_response(method, resp):
    """
    检查响应 输出非正常响应返回json的信息

    :param method: 请求方法
    :param resp: 响应体
    :return: 是否正常响应
    """
    if resp.status_code == 200 and resp.content:
        return True
    logger.log('ALERT', f'{method} {resp.url} {resp.status_code} - '
                        f'{resp.reason} {len(resp.content)}')
    content_type = resp.headers.get('Content-Type')
    if content_type and 'json' in content_type and resp.content:
        try:
            msg = resp.json()
        except Exception as e:
            logger.log('DEBUG', e.args)
        else:
            logger.log('ALERT', msg)
    return False


def check_dir(dir_path):
    if not dir_path.exists():
        logger.log('INFOR', f'{dir_path} does not exist, directory will be created')
        dir_path.mkdir(parents=True, exist_ok=True)


def check_path(path, name, fmt):
    """
    检查结果输出目录路径

    :param path: 保存路径
    :param name: 导出名字
    :param fmt: 保存格式
    :return: 保存路径
    """
    filename = f'{name}.{fmt}'
    default_path = settings.result_save_dir.joinpath(filename)
    if isinstance(path, str):
        path = repr(path).replace('\\', '/')  # 将路径中的反斜杠替换为正斜杠
        path = path.replace('\'', '')  # 去除多余的转义
    else:
        path = default_path
    path = Path(path)
    if not path.suffix:  # 输入是目录的情况
        path = path.joinpath(filename)
    parent_dir = path.parent
    if not parent_dir.exists():
        logger.log('ALERT', f'{parent_dir} does not exist, directory will be created')
        parent_dir.mkdir(parents=True, exist_ok=True)
    if path.exists():
        logger.log('ALERT', f'The {path} exists and will be overwritten')
    return path


def save_to_file(path, data):
    """
    保存数据到文件

    :param path: 保存路径
    :param data: 待存数据
    :return: 保存成功与否
    """
    try:
        with open(path, 'w', errors='ignore', newline='') as file:
            file.write(data)
            return True
    except TypeError:
        with open(path, 'wb') as file:
            file.write(data)
            return True
    except Exception as e:
        logger.log('ERROR', e.args)
        return False


def get_timestring(fmt="%Y-%m-%d %H:%M:%S"):
    return time.strftime(fmt, time.localtime(time.time()))


def get_filepaths(_root_dir, _filepaths, _filetype='', _method='onepass'):
    for i in os.listdir(_root_dir):
        tmp_path = os.path.join(_root_dir, i)
        if os.path.isdir(tmp_path):
            if _method == 'iterate':
                get_filepaths(tmp_path, _filepaths, _filetype, _method)
            else:
                pass
        elif os.path.splitext(tmp_path)[-1] == _filetype and len(_filetype) > 0:
            _filepaths.append(tmp_path)
        elif len(_filetype) == 0:
            _filepaths.append(tmp_path)


def clear_dir(dirpath):
    try:
        shutil.rmtree(dirpath)
    except Exception as e:
        print(e)
    if not os.path.exists(dirpath):
        os.mkdir(dirpath)
    return


def url_filter(url):
    non_legal_char = ['#']
    for i in non_legal_char:
        url = url.replace(i, '')
    return url


def get_request_count():
    return os.cpu_count() * 16


class reCAPTCHAERROR(Exception):
    pass


class StateExtractor:
    def __init__(self, _string, country_code):
        self.input_string = _string
        self.country_code = country_code
        self.data_dir = r'E:\dadalang\tw_IXP\data'
        self.state_fullname_dc = dict()
        self.state_shortname_dc = dict()
        self.load_state_dc()

    def load_state_dc(self):
        with open(os.path.join(self.data_dir, '{}_state.txt'.format(self.country_code)), 'r', encoding='utf-8') as f:
            raw_country_data = f.read().strip()
            if self.country_code == 'US':
                for i in raw_country_data.split('\n'):
                    tmp_str = i.split(',')
                    self.state_shortname_dc[tmp_str[1]] = tmp_str[0]
            else:
                for i in raw_country_data.split('\n'):
                    tmp_str = i.split(',')
                    self.state_shortname_dc[tmp_str[2]] = tmp_str[0]
                    self.state_fullname_dc[tmp_str[1]] = tmp_str[0]

    def extract_state(self):
        if self.country_code == 'US':
            for k, v in self.state_shortname_dc.items():
                if re.search(' ', self.input_string.lower()):
                    if re.search(' {} '.format(k), self.input_string.lower()):
                        return v
                    if re.search(' {},'.format(k), self.input_string.lower()):
                        return v
                    if re.search(' {} '.format(v), self.input_string.lower()):
                        return v
                    if re.search(' {},'.format(v), self.input_string.lower()):
                        return v
                else:
                    if re.search(k, self.input_string.lower()):
                        return v
                    if re.search(v, self.input_string.lower()):
                        return v
        else:
            for k, v in self.state_shortname_dc.items():
                if re.search(' {} '.format(k), self.input_string.lower()):
                    return v
                if re.search(' {},'.format(k), self.input_string.lower()):
                    return v
            for k, v in self.state_fullname_dc.items():
                if re.search(' {} '.format(k), self.input_string.lower()):
                    return v
                if re.search(' {},'.format(k), self.input_string.lower()):
                    return v
        return None

    def run(self):
        if not self.input_string:
            return None
        else:
            return self.extract_state()


def get_sample_banner(headers):
    temp_list = []
    server = headers.get('Server')
    if server:
        temp_list.append(server)
    via = headers.get('Via')
    if via:
        temp_list.append(via)
    power = headers.get('X-Powered-By')
    if power:
        temp_list.append(power)
    banner = ','.join(temp_list)
    return banner


def decode_resp_text(resp):
    content = resp.content
    if not content:
        return str('')
    try:
        # 先尝试用utf-8严格解码
        content = str(content, encoding='utf-8', errors='strict')
    except (LookupError, TypeError, UnicodeError):
        try:
            # 再尝试用gb18030严格解码
            content = str(content, encoding='gb18030', errors='strict')
        except (LookupError, TypeError, UnicodeError):
            # 最后尝试自动解码
            content = str(content, errors='replace')
    return content


def remove_invalid_string(string):
    # Excel文件中单元格值不能直接存储以下非法字符
    return re.sub(r'[\000-\010]|[\013-\014]|[\016-\037]', r'', string)


if __name__ == '__main__':



    #print(gen_fake_header())
    #print(url_filter('https://api.geoapify.com/v1/geocode/search?text=Chunghwa Telecom - Internation#al Business Group (CHTI) 5F(R511), # 31, Aikuo East Road, Taipei,Taiwan, 106 R.O.C&format=json&apiKey=894f7888d6b44b1e9e318ef668248ff4'))
    '''
    print(get_proxy_from_pool())
    t = []
    get_filepaths(r'E:\dadalang\tw_IXP\results\cn', t)
    print([os.path.basename(i).split('.')[0] for i in t])
    se = StateExtractor('VA', 'US')
    output = se.run()
    print(output)
    '''
    #import pathlib
    #pathlib.Path(__file__).parent.parent
    '''
    print(CIDR2IP('**********/21'))
    print(CIDR2INTrange('**********/21'))'''
    '''
    a = other2utc('2022-11-01 00:00:00')
    print(a)
    print(utc2other('2022-11-01 00:00:00'))
    print(formattime_transfor('2016-05-05 20:28:54'))
    print(IP2CIDR('************', '**************'))
    print(CIDR2IP('************/31'))'''
    #print(formattime2timestamp('**********/21'))
    format = "%Y-%m-%d %H:%M:%S"
    native_time = time.strptime('2022-11-01 00:00:00', format)
    print(native_time)
    utc_time = arrow.get('2022-11-01 00:00:00').to('UTC')
    utc_time = arrow.get('2022-11-01 00:00:00').to('UTC')
    print(utc_time.timestamp())
    print(arrow.get(1668895200).to('Europe/Kiev'))
    print(arrow.get('2022-11-01 00:00:00').to('Europe/Kiev'))
    print(arrow.get(1673301600).to('UTC'))
    print(arrow.get(1673301600).to('UTC'))
    print(arrow.get('2022-11-01 00:00:00', tzinfo='Europe/Kiev').to('UTC').timestamp())
    print(targettime_to_utctimestamp('2022-11-01 00:00:00'))

    print(get_fake_ua())
