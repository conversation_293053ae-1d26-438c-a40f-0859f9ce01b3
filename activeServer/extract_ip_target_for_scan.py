import random
import os

import pandas as pd
from netaddr import *
import pycountry_convert as pc

def typical_sampling(group, typical_N_dict):
    name = group.name
    n = typical_N_dict[name]
    return group.sample(n=n, random_state=42)


def gen_sample_num(group, min_num):
    typical_N_dict = {}
    for k, v in group.items():
        if v:
            if v >= min_num:
                typical_N_dict[k] = min_num
            else:
                typical_N_dict[k] = v
        else:
            typical_N_dict[k] = 0

    return typical_N_dict


def save_tmp_ip():
    name_list = ['ip_num', 'id', 'ip_min', 'ip_max', 'continent', 'country_code', 'country', 'province', 'city',
                 'street', 'lon', 'lat', 'radius', 'zipcode', 'timezone', 'dwtype', 'orgtype', 'algorithm', 'code1',
                 'org1', 'org2', 'org3', 'org4', 'org5', 'num1', 'num2']
    data = pd.read_csv(r'E:\dadalang\0_data\iplocation\landmark_2023m04_geo2.csv', encoding='utf-8', header=None, names=name_list)
    data = data.loc[data['org4']!='国家行政机构']
    # data = pd.read_csv('landmark_2023m04_geo2.csv', encoding='utf-8', header=None, names=name_list)
    # data.groupby(['country_code', 'province', 'city']).count()

    typical_N_dict = gen_sample_num(data.groupby(['country_code', 'province', 'city'])['ip_num'].count(), 30)
    output_sample_ip = data.groupby(['country_code', 'province', 'city']).apply(typical_sampling, typical_N_dict)

    output_sample_ip.to_csv(r'E:\dadalang\9network_fluctuations\新建文件夹\sample_ip_2.csv')


def merge_ip():
    data1 = pd.read_csv(r'E:\dadalang\9network_fluctuations\新建文件夹\sample_ip_1.csv', encoding='utf-8')
    data2 = pd.read_csv(r'E:\dadalang\9network_fluctuations\新建文件夹\sample_ip_2.csv', encoding='utf-8')

    out = pd.concat([data1, data2], ignore_index=True)
    tmp = out.iloc[:, 4:]

    typical_N_dict = gen_sample_num(tmp.groupby(['country_code.1', 'province.1', 'city.1'])['ip_num'].count(), 30)
    output = tmp.groupby(['country_code.1', 'province.1', 'city.1']).apply(typical_sampling, typical_N_dict)

    output.to_csv('finall_sample.csv')


def CIDR2IP(cidr):
    ip_set = IPSet([cidr])
    return [i.__str__() for i in ip_set]


def IP2CIDR(startip, endip):
    iprange = IPRange(startip, endip)
    return [i.__str__() for i in iprange.cidrs()]


def IP2INT(ip):
    return int(IPAddress(ip))


def INT2IP(ip_int):
    return str(IPAddress(int(ip_int)))


def sample_ip(_line, output_sampled):
    iprange = IPRange(_line['start_ip'], _line['end_ip'])
    iprange_list = [i.__str__() for i in iprange]
    new_iprange_list = [i for i in iprange_list if not i.endswith('.0') and not i.endswith('.255')]
    if not new_iprange_list:
        return
    if len(new_iprange_list) <= 3:
        random_choice_ip_list = new_iprange_list
    else:
        random_choice_ip_list = random.choices(new_iprange_list, k=3)

    output_sampled += [{'ip': i, 'country_code': _line['country_code'], 'province': _line['province'], 'city': _line['city'], 'lon': _line['lon'], 'lat': _line['lat']} for i in random_choice_ip_list]


def extract_cn_ip():
    name_list = ['start_ip', 'end_ip', 'continent', 'country_code', 'province', 'city', 'lat', 'lon']
    global_ip_location = pd.read_csv(r'E:\dadalang\0_data\iplocation\dbip-city-lite-2023-10.csv', encoding='utf-8', header=None, names=name_list)
    cn_ip_location = global_ip_location.loc[(global_ip_location['country_code'] == 'CN') & (global_ip_location['start_ip'].str.contains('\.'))]  # 只ipv4
    typical_N_dict = gen_sample_num(cn_ip_location.groupby(['country_code', 'province', 'city'])['start_ip'].count(), 2)  # 网段
    output = cn_ip_location.groupby(['country_code', 'province', 'city']).apply(typical_sampling, typical_N_dict)
    output.to_csv(r'E:\dadalang\9network_fluctuations\新建文件夹\sample_cn_ip.csv', index=False)
    tmp = pd.read_csv(r'E:\dadalang\9network_fluctuations\新建文件夹\sample_cn_ip.csv')
    output_sampled = []
    tmp.apply(sample_ip, args=(output_sampled, ), axis=1)
    return pd.DataFrame(output_sampled)


def extract_area_ip(country_code, sample_ip_num=5):
    name_list = ['start_ip', 'end_ip', 'continent', 'country_code', 'province', 'city', 'lat', 'lon']
    global_ip_location = pd.read_csv(r'E:\ang\0_data\iplocation\dbip-city-lite-2023-10.csv', encoding='utf-8',
                                     header=None, names=name_list)
    if type(country_code) == str:
        area_ip_location = global_ip_location.loc[(global_ip_location['country_code'] == country_code) & (
            global_ip_location['start_ip'].str.contains('\.'))]  # 只ipv4
        typical_N_dict = gen_sample_num(
            area_ip_location.groupby(['country_code', 'province', 'city'])['start_ip'].count(), sample_ip_num)  # 网段
        output = area_ip_location.groupby(['country_code', 'province', 'city']).apply(typical_sampling, typical_N_dict)
        output.to_csv(r'E:\ang\9network_fluctuations\新建文件夹\sample_{}_ip.csv'.format(country_code.lower()),
                      index=False)
        tmp = pd.read_csv(r'E:\ang\9network_fluctuations\新建文件夹\sample_{}_ip.csv'.format(country_code.lower()))
    elif type(country_code) == list:
        area_ip_location = global_ip_location.loc[(global_ip_location['country_code'].isin(country_code)) & (
            global_ip_location['start_ip'].str.contains('\.'))]  # 只ipv4
        typical_N_dict = gen_sample_num(
            area_ip_location.groupby(['country_code', 'province', 'city'])['start_ip'].count(), sample_ip_num)  # 网段
        output = area_ip_location.groupby(['country_code', 'province', 'city']).apply(typical_sampling, typical_N_dict)
        output.to_csv(r'E:\ang\9network_fluctuations\新建文件夹\sample_{}_ip.csv'.format('_'.join([i.lower() for i in country_code])), index=False)
        tmp = pd.read_csv(r'E:\ang\9network_fluctuations\新建文件夹\sample_{}_ip.csv'.format('_'.join([i.lower() for i in country_code])))

    output_sampled = []
    tmp.apply(sample_ip, args=(output_sampled, ), axis=1)
    return pd.DataFrame(output_sampled)

# 2.25添加分洲际
def get_continent(country_code):
    try:
        # 转换为大写字母
        country_code = country_code.upper()
        # 获取大洲代码
        continent_code = pc.country_alpha2_to_continent_code(country_code)
        # 转换代码为大洲全称
        return pc.convert_continent_code_to_continent_name(continent_code)
    except:
        return 'Unknown'

def separate_data_by_continent():
    # Read CSV and add continent column
    df = pd.read_csv('data/merged_sample_ip.csv')
    df['continent'] = df['country_code'].apply(get_continent)
    
    # Ensure the output directory exists
    os.makedirs('data/continent_data', exist_ok=True)
    # Create directory if it doesn't exist
    
    # Get unique continents
    continents = df['continent'].unique()
    
    # Separate and save data for each continent
    for continent in continents:
        # Filter data for the current continent
        continent_df = df[df['continent'] == continent]
        
        # Create a filename
        filename = f'data/continent_data/{continent.lower()}_data.csv'
        
        # Save to CSV
        continent_df.to_csv(filename, index=False)
        print(f'Saved {len(continent_df)} records for {continent} to {filename}')

if __name__ == '__main__':
    # 1st split 2 part
    #save_tmp_ip()

    # 2nd merge 2 part
    #merge_ip()

    # 3rd add ip
    '''data = pd.read_csv('data/finall_sample.csv')
    data['ip'] = data['ip_num'].apply(INT2IP)
    tmp = data.iloc[:,4:]
    tmp.rename(columns={'country_code.1.1':'country_code', 'country.1.1': 'country', 'city.1.1': 'city', 'province.1.1': 'province'}, inplace=True)'''
    #tmp = pd.read_csv('data/finall_sample_ipstr.csv')
    #tmp[['ip', 'country_code', 'province', 'city', 'lon', 'lat']].to_csv('data/finall_sample_ipstr1.csv', encoding='utf-8', index=False)

    '''
    ipset = IPSet()
    for i in data['ip'].tolist():
        ipset.add(i.strip())
    with open('merged_cidr.txt', 'w', encoding='utf-8') as ff:
        ff.write('\n'.join([i.__str__() for i in ipset.iter_cidrs()]))'''

    # 4th process area ip
    '''
    tmp = pd.read_csv('data/finall_sample_ipstr.csv')
    cn_ip = extract_cn_ip()
    merged_sample_ip = cn_ip._append(tmp, ignore_index=True).drop_duplicates(subset=['ip'], keep='last')
    #merged_sample_ip.to_csv('data/merged_sample_ip.csv', encoding='utf-8', index=False)
    final_merged_sample_ip = merged_sample_ip.drop_duplicates(subset=['ip'], keep='last').reset_index(drop=True)
    final_merged_sample_ip.to_csv('data/merged_sample_ip.csv', index=False)
    final_merged_sample_ip['ip'].to_csv('data/merged_sample_ip.csv', encoding='utf-8', index=False)'''

    # 5th add other area
    # last_data = pd.read_csv('data/merged_sample_ip.csv')
    # area_ip = extract_area_ip(['PS', 'IL', 'LB'])

    # merged_sample_ip = area_ip._append(last_data, ignore_index=True).drop_duplicates(subset=['ip'], keep='last')
    # # merged_sample_ip.to_csv('data/merged_sample_ip.csv', encoding='utf-8', index=False)
    # final_merged_sample_ip = merged_sample_ip.drop_duplicates(subset=['ip'], keep='last').reset_index(drop=True)
    # final_merged_sample_ip.to_csv('data/merged_sample_ip1.csv', index=False)
    # with open('data/target_ip_list1.txt', 'w', encoding='utf-8') as f:
    #     f.write('\n'.join(final_merged_sample_ip['ip'].tolist()))

    # 6th split by Continent
    separate_data_by_continent()