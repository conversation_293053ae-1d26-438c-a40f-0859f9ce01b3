#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(__file__))

from main import save_citrix_data, save_citrix_detailed_data
import json

# Test unified format data (citrix_*.json)
unified_data = {
    "scan_info": {
        "timestamp": 1751848619,
        "target_date": "2025-06-17",
        "total_ips": 2,
        "stats": {
            "total": 2,
            "successful": 2,
            "failed": 0,
            "before_target_date": 1,
            "after_target_date": 1,
            "no_last_modified": 0
        },
        "scan_type": "unified"
    },
    "citrix_scan": [
        [1751848619, "global", "***********:80", True, 200, "Wed, 26 Apr 2017 08:06:08 GMT", True, "nginx/1.14.0", 1024, 1.5, "", "http"],
        [1751848619, "global", "***********:443", True, 200, "Wed, 18 Jun 2025 12:21:23 GMT", False, "Apache/2.4.41", 2048, 2.1, "", "https"]
    ],
    "citrix_stats": [
        [1751848619, "global", 2, 2, 0, 1, 1, 0]
    ]
}

# Test detailed format data (citrix_scan_*.json)
detailed_data = {
    "scan_info": {
        "timestamp": 1751848619,
        "target_date": "2025-06-17",
        "total_ips": 2,
        "stats": {
            "total": 2,
            "successful": 2,
            "failed": 0,
            "before_target_date": 1,
            "after_target_date": 1,
            "no_last_modified": 0
        },
        "scan_type": "detailed"
    },
    "results": [
        {
            "ip": "***********",
            "port": "80",
            "ip_port": "***********:80",
            "protocol": "http",
            "timestamp": "2025-07-06T20:36:00",
            "https_accessible": False,
            "http_accessible": True,
            "rtsp_accessible": False,
            "last_modified": "Wed, 26 Apr 2017 08:06:08 GMT",
            "last_modified_parsed": "2017-04-26T08:06:08+00:00",
            "before_target_date": True,
            "error": None,
            "response_time": 1.5,
            "status_code": 200,
            "response_headers": {"Server": "nginx/1.14.0", "Content-Type": "text/html"},
            "response_body": "<html><head><title>Test</title></head></html>",
            "content_length": 1024,
            "server": "nginx/1.14.0"
        },
        {
            "ip": "***********",
            "port": "443",
            "ip_port": "***********:443",
            "protocol": "https",
            "timestamp": "2025-07-06T20:36:01",
            "https_accessible": True,
            "http_accessible": False,
            "rtsp_accessible": False,
            "last_modified": "Wed, 18 Jun 2025 12:21:23 GMT",
            "last_modified_parsed": "2025-06-18T12:21:23+00:00",
            "before_target_date": False,
            "error": None,
            "response_time": 2.1,
            "status_code": 200,
            "response_headers": {"Server": "Apache/2.4.41", "Content-Type": "text/html"},
            "response_body": "<html><head><title>Secure Test</title></head></html>",
            "content_length": 2048,
            "server": "Apache/2.4.41"
        }
    ]
}

print("Testing citrix database functions...")

print("\n1. Testing unified format (citrix_*.json) -> citrix_scan & citrix_stat tables")
try:
    result = save_citrix_data(1751848619, unified_data)
    print(f"Unified format save result: {result}")
except Exception as e:
    print(f"Error saving unified format: {e}")

print("\n2. Testing detailed format (citrix_scan_*.json) -> citrix_detailed & citrix_detailed_stat tables")
try:
    result = save_citrix_detailed_data(1751848619, detailed_data)
    print(f"Detailed format save result: {result}")
except Exception as e:
    print(f"Error saving detailed format: {e}")

print("\nTest completed!")
