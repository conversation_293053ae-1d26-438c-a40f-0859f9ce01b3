import sys
import pathlib
from loguru import logger
import threading


log_relative_directory = pathlib.Path(__file__).parent.parent
log_result_save_dir = log_relative_directory.joinpath('results')
log_path = log_result_save_dir.joinpath('log.log')

stdout_fmt = '<cyan>{time:HH:mm:ss,SSS}</cyan> ' \
             '[<level>{level: <5}</level>] ' \
             '<blue>{module}</blue>:<cyan>{line}</cyan> - ' \
             '<level>{message}</level>'

logfile_fmt = '<light-green>{time:YYYY-MM-DD HH:mm:ss,SSS}</light-green> ' \
              '[<level>{level: <5}</level>] ' \
              '<cyan>{process.name}({process.id})</cyan>:' \
              '<cyan>{thread.name: <18}({thread.id: <5})</cyan> | ' \
              '<blue>{module}</blue>.<blue>{function}</blue>:' \
              '<blue>{line}</blue> - <level>{message}</level>'


class LOGGER:
    def __init__(self, _log_path=log_path):
        #logger.__init__(self)
        self._log_path = _log_path
        logger.remove()
        '''logger.level(name='TRACE', color='<cyan><bold>', icon='✏️')
        logger.level(name='DEBUG', color='<blue><bold>', icon='🐞 ')
        logger.level(name='INFOR', color='<green><bold>', icon='ℹ️')
        logger.level(name='QUITE', color='<green><bold>', icon='🤫 ')
        logger.level(name='ALERT', color='<yellow><bold>', icon='⚠️')
        logger.level(name='ERROR', color='<red><bold>', icon='❌️')
        logger.level(name='FATAL', color='<RED><bold>', icon='☠️')

        '''
        logger.level(name='TRACE', color='<cyan><bold>', icon='✏️')
        logger.level(name='DEBUG', color='<blue><bold>', icon='🐞 ')
        logger.level(name='INFOR', no=20, color='<green><bold>', icon='ℹ️')
        logger.level(name='QUITE', no=25, color='<green><bold>', icon='🤫 ')
        logger.level(name='ALERT', no=30, color='<yellow><bold>', icon='⚠️')
        logger.level(name='ERROR', color='<red><bold>', icon='❌️')
        logger.level(name='FATAL', no=50, color='<RED><bold>', icon='☠️')

        logger.add(sys.stderr, level='INFOR', format=stdout_fmt, enqueue=True)
        logger.add(self._log_path, level='DEBUG', format=logfile_fmt, enqueue=True, encoding='utf-8')
        self.lock = threading.Lock()

    def log(self, LevelName, msg):
        self.lock.acquire()
        logger.log(LevelName, msg)
        self.lock.release()


