# coding=utf-8
import pathlib
import warnings
import os

proxy = os.environ.get('PROXY', '')

enable_request_proxy = True
enable_proxy_pool = False
request_proxy_ls = [{'http': proxy, 'https': proxy}]
#request_proxy_ls = [{'http': 'socks5://192.168.1.117:10808', 'https': 'socks5://192.168.1.117:10808'}]
proxy_pool_url = ''

warnings.filterwarnings("ignore")

relative_directory = pathlib.Path(__file__).parent.parent
data_storage_dir = relative_directory.joinpath('data')
result_save_dir = relative_directory.joinpath('results')
temp_save_dir = result_save_dir.joinpath('temp')
USER_AGENT_FILE = data_storage_dir.joinpath('chrome.txt')


request_thread_count = None
request_timeout_second = (13, 27)
request_ssl_verify = False
request_allow_redirect = True
request_redirect_limit = 10

request_default_headers = {
    'Accept': 'text/html,application/xhtml+xml,'
              'application/xml;q=0.9,*/*;q=0.8',
    'Accept-Encoding': 'gzip, deflate',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Cache-Control': 'max-age=0',
    'DNT': '1',
    'Referer': 'https://www.google.com/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                  '(KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36',
    'Upgrade-Insecure-Requests': '1',
    'X-Forwarded-For': '127.0.0.1'
}
enable_random_ua = True
enable_banner_identify = False

TIMEZONE = 'UTC'