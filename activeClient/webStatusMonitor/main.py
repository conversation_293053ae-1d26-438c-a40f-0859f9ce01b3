import json
from threading import Thread
from queue import Queue
from common.utils import CipherAdapter
import tqdm
import requests
from bs4 import BeautifulSoup
from common import utils
from config import settings
from config.log import LOGGER
from apscheduler.schedulers.blocking import BlockingScheduler
import datetime
request_log = LOGGER('results/web_status.log')


def req_thread_count():
    count = settings.request_thread_count
    if isinstance(count, int):
        count = max(16, count)
    else:
        count = utils.get_request_count()
    request_log.log('DEBUG', f'Number of request threads {count}')
    return count


def get_html_title(markup):
    soup = BeautifulSoup(markup, 'html.parser')

    title = soup.title
    if title:
        return title.text

    h1 = soup.h1
    if h1:
        return h1.text

    h2 = soup.h2
    if h2:
        return h2.text

    h3 = soup.h3
    if h3:
        return h3.text

    desc = soup.find('meta', attrs={'name': 'description'})
    if desc:
        return desc['content']

    word = soup.find('meta', attrs={'name': 'keywords'})
    if word:
        return word['content']

    text = soup.text
    if len(text) <= 200:
        return repr(text)

    return 'None'


def get_jump_urls(history):
    urls = list()
    for resp in history:
        urls.append(str(resp.url))
    return urls


def get_progress_bar(total):
    bar = tqdm.tqdm()
    bar.total = total
    bar.desc = 'Request Progress'
    bar.ncols = 80
    return bar


def get_resp(url, session, max_retry=3):
    timeout = settings.request_timeout_second
    redirect = settings.request_allow_redirect
    proxy = utils.get_proxy()
    flag = 0
    while flag < max_retry:
        flag += 1
        if flag > 1:
            url = url.replace("http://","https://")
        try:
            resp = session.get(url, timeout=timeout, allow_redirects=redirect, proxies=proxy)
            resp.close()
            utils.time_sleep(1)
            break
        except requests.exceptions.ReadTimeout as e:
            resp = e
            continue
        except Exception as e:
            request_log.log('DEBUG', e.args)
            resp = e

    return resp


def request(urls_queue, resp_queue, session):
    while not urls_queue.empty():
        org, url = urls_queue.get()
        resp = get_resp(url, session)
        resp_queue.put((org, url, resp))
        urls_queue.task_done()


def progress(bar, total, urls_queue):
    while True:
        remaining = urls_queue.qsize()
        done = total - remaining
        bar.n = done
        bar.update()
        if remaining == 0:
            break

def get_session():
    header = utils.gen_fake_header()
    verify = settings.request_ssl_verify
    redirect_limit = settings.request_redirect_limit
    session = requests.Session()
    session.trust_env = False
    session.headers = header
    session.verify = verify
    session.max_redirects = redirect_limit
    #session.mount('http://', CipherAdapter())
    #session.mount('https://', CipherAdapter())
    return session

def gen_new_info(resp):
    info = {}
    if isinstance(resp, Exception):
        info['reason'] = str(resp.args)
        info['request'] = 0
        info['alive'] = 0
        info['status'] = 0
        return info
    info['reason'] = resp.reason
    code = resp.status_code
    info['status'] = code
    info['request'] = 1
    if code == 400 or code >= 500:
        info['alive'] = 0
    else:
        info['alive'] = 1
    headers = resp.headers
    if settings.enable_banner_identify:
        info['banner'] = utils.get_sample_banner(headers)
    '''info['header'] = json.dumps(dict(headers))
    history = resp.history
    info['history'] = json.dumps(get_jump_urls(history))
    text = utils.decode_resp_text(resp)
    title = get_html_title(text).strip()
    info['title'] = utils.remove_invalid_string(title)
    info['response'] = utils.remove_invalid_string(text)'''
    return info


def save(_timestamp, total, country, resp_queue, collector):
    try:
        i = 0
        alive_count = 0
        nan_request_count = 0
        while True:
            if not resp_queue.empty():
                i += 1
                org, url, resp = resp_queue.get()
                new_info = gen_new_info(resp)
                if new_info['alive']:
                    alive_count += 1
                if new_info['request'] == 0:
                    nan_request_count += 1
                collector['web'].append((_timestamp, country, org, url, new_info['reason'], new_info['request'], new_info['status'], new_info['alive']))
                resp_queue.task_done()
            if i >= total:
                break
        collector['web_status'].append((_timestamp, country, total, total-nan_request_count, total-alive_count-nan_request_count, alive_count))
    except Exception as e:
        request_log.log('ERROR', 'process {} data meet {}'.format(country, e))

    request_log.log('INFOR', 'collect {} results finished'.format(country))


def bulk_request(_timestamp, country, org_url_data, collector, ret=False):
    request_log.log('INFOR', 'Requesting {} urls in bulk'.format(country))
    resp_queue = Queue()
    urls_queue = Queue()
    task_count = len(org_url_data)
    for org, _url in org_url_data.items():
        if _url:
            url = utils.extract_root_page(_url)
            urls_queue.put((org, url))
    session = get_session()
    thread_count = req_thread_count()
    if task_count <= thread_count:
        thread_count = task_count
    #thread_count = 1
    bar = get_progress_bar(task_count)

    progress_thread = Thread(target=progress, name='ProgressThread',
                             args=(bar, task_count, urls_queue), daemon=True)
    progress_thread.start()

    for i in range(thread_count):
        request_thread = Thread(target=request, name=f'RequestThread-{i}',
                                args=(urls_queue, resp_queue, session), daemon=True)
        request_thread.start()
    if ret:
        urls_queue.join()
        return resp_queue

    save_thread = Thread(target=save, name=f'SaveThread', args=(_timestamp, task_count, country, resp_queue, collector), daemon=True)
    save_thread.start()
    urls_queue.join()
    save_thread.join()

    #save(_timestamp, task_count, country, resp_queue, collector)


def run_request(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        tmp_data = json.loads(f.read())
    request_log.log('INFOR', f'Start requesting urls')
    utctimestamp = utils.get_utc_timestamp()
    collector = {}
    collector['web'] = []
    collector['web_status'] = []
    for country, org_url_data in tmp_data.items():
        bulk_request(utctimestamp, country, org_url_data, collector)

    with open(settings.result_save_dir.joinpath('web_{}.json'.format(str(utctimestamp))), 'w', encoding='utf-8') as f:
        f.write(json.dumps(collector, ensure_ascii=False))
    request_log.log('INFOR', f'Finished request')


def run_scheduler():
    file_path = settings.data_storage_dir.joinpath('target_org_url.json')
    scheduler = BlockingScheduler(timezone=settings.TIMEZONE)
    start_time = (datetime.datetime.utcnow().replace(tzinfo=None) + datetime.timedelta(seconds=10)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(run_request, 'date', run_date=start_time, id="reqeust_init", name="reqeust_init", args=[file_path, ])
    scheduler.add_job(run_request, 'interval', minutes=120, id="reqeust", name="reqeust", args=[file_path, ])
    job_defaults = {
        'coalesce': False,
        'max_instances': 20
    }
    scheduler.configure(job_defaults=job_defaults, timezone=settings.TIMEZONE)
    scheduler.start()


if __name__ == '__main__':
    run_scheduler()
    #run_request(settings.data_storage_dir.joinpath('target_org_url.json'))
