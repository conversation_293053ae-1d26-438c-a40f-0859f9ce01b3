import pathlib
import warnings
import os

MASSCAN_RATE = os.environ.get('MASSCAN_RATE', 200)
KEEP_RESULT = os.environ.get('KEEP_RESULT', 1)

warnings.filterwarnings("ignore")

relative_directory = pathlib.Path(__file__).parent.parent
data_storage_dir = relative_directory.joinpath('data')
result_save_dir = relative_directory.joinpath('results')
chrome_USER_AGENT_FILE = data_storage_dir.joinpath('chrome.txt')
firefox_USER_AGENT_FILE = data_storage_dir.joinpath('firefox.txt')

TIMEZONE = "UTC"