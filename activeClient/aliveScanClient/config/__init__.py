import importlib


class Settings(object):
    def __init__(self):
        #for attr in dir(default):
        #    setattr(self, attr, getattr(default, attr))
        setting_modules = ['config.setting', 'config.log']
        for setting_module in setting_modules:
            setting = importlib.import_module(setting_module)
            for attr in dir(setting):
                setattr(self, attr, getattr(setting, attr))


settings = Settings()
