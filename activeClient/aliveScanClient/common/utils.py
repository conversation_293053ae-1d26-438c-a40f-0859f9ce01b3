import math
from config import settings
import arrow
import random
def get_data(filepath):
    try:
        with open(filepath, 'r') as fp:
            data = [_.strip() for _ in fp.readlines()]
        return data
    except:
        raise FileNotFoundError("%s" % filepath)

def get_utc_timestamp(only_int=True):
    if only_int:
        return math.floor(arrow.utcnow().timestamp())
    else:
        return arrow.utcnow().timestamp()

def get_fake_ua():
    chrome_ua = get_data(settings.chrome_USER_AGENT_FILE)
    firefox_ua = get_data(settings.firefox_USER_AGENT_FILE)
    return random.choice(chrome_ua + firefox_ua)

if __name__ == '__main__':
    print(get_fake_ua())
