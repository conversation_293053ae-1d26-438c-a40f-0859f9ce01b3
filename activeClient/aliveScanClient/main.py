import json
from config import settings
from config import setting
import os
from common import utils
from config.log import LOGGER
import re
from apscheduler.schedulers.blocking import BlockingScheduler
import datetime
scan_log = LOGGER('results/scan.log')


def masscan(ip_list_file):
    scan_log.log('INFOR', f'Start scan')
    now_timestamp = utils.get_utc_timestamp()
    user_agent = utils.get_fake_ua()
    output = setting.result_save_dir.joinpath(f'masscan_{str(now_timestamp)}.json')
    cmd = f'masscan --ping -iL \"{ip_list_file}\" --rate={settings.MASSCAN_RATE} -oJ \"{output}\"  --http-user-agent \"{user_agent}\"'
    # masscan -p80,8000-9000 ************* --rate=10000 --banners --source-port 61000 --http-user-agent "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:87.0) Gecko/20100101 Firefox/87.0" -Pn
    os.system(cmd)
    scan_log.log('INFOR', f'Finished scan')

    collector = set()
    with open(output, "r") as f:
        s = f.read()
        s = re.sub(r"},\s*]", "}]", s)
        result_json = json.loads(s)
    if result_json:
        try:
            for ip in result_json:
                collector.add(ip['ip'])
        except Exception as e:
            scan_log.log('ERROR', f"masscan results read faild {e}")
            os.unlink(output)

    if not setting.KEEP_RESULT:
        os.unlink(output)


def run_scheduler():
    file_path = settings.data_storage_dir.joinpath('target_ip_list.txt')

    scheduler = BlockingScheduler(timezone=settings.TIMEZONE)
    start_time = (datetime.datetime.utcnow().replace(tzinfo=None) + datetime.timedelta(seconds=10)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(masscan, 'date', run_date=start_time, id="scan_init", name="scan_init", args=[file_path, ])
    scheduler.add_job(masscan, 'interval', minutes=60, id="scan", name="scan", args=[file_path, ])
    job_defaults = {
        'coalesce': False,
        'max_instances': 20
    }
    scheduler.configure(job_defaults=job_defaults, timezone=settings.TIMEZONE)
    scheduler.start()


if __name__ == '__main__':
    run_scheduler()
