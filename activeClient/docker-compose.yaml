version: '3'
services:
  # alive_scan:
  #   build: ./aliveScanClient
  #   image: alive_scan:latest
  #   container_name: alivescan
  #   volumes:
  #     - ./aliveScanClient:/app
  #   environment:  # used during container running
  #     MASSCAN_RATE: 200
  #   ports:
  #     - "6100:6100"
  #   restart: always
  # web_status:
  #   build: ./webStatusMonitor
  #   image: web_status_monitor:latest
  #   container_name: webstatusmonitor
  #   volumes:
  #     - ./webStatusMonitor:/app
  #   restart: always
  vul_alive_line:
    build: ./vulAliveLine
    image: vul_alive_line:latest
    container_name: vulaliveline
    volumes:
      - ./vulAliveLine:/app
    environment:  # used during container running
      MASSCAN_RATE: 200
      KEEP_RESULT: 0
    restart: always