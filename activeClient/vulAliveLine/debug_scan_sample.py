#!/usr/bin/env python3
"""Debug script to analyze a sample of the actual scan results"""

import sys
import os
sys.path.append('.')

from main import check_https_last_modified, load_ip_port_list, PROTOCOL_MAPPING

def debug_scan_sample():
    """Debug a small sample from the actual target list"""
    print("Debugging actual scan sample:")
    print("=" * 60)
    
    try:
        # Load the actual IP list
        all_ip_ports = load_ip_port_list()
        
        # Take a sample from different parts of the list
        sample_indices = [0, 100, 500, 1000, 2000, 5000, 10000, 20000, 30000, 40000]
        sample_ip_ports = []
        
        for idx in sample_indices:
            if idx < len(all_ip_ports):
                sample_ip_ports.append(all_ip_ports[idx])
        
        print(f"Testing {len(sample_ip_ports)} samples from different parts of the list:")
        
        results_with_dates = []
        results_without_dates = []
        unparseable_dates = []
        risk_dates = []
        safe_dates = []
        
        for i, ip_port in enumerate(sample_ip_ports):
            print(f"\n🔍 Sample {i+1}: {ip_port}")
            
            try:
                result = check_https_last_modified(ip_port, timeout=5)
                
                success = result.get('protocol') is not None
                last_modified = result.get('last_modified')
                before_target = result.get('before_target_date')
                
                print(f"   Success: {success}")
                print(f"   Protocol: {result.get('protocol', 'None')}")
                print(f"   Status: {result.get('status_code', 'None')}")
                print(f"   Last-Modified: {last_modified or 'None'}")
                
                if success:
                    if last_modified:
                        results_with_dates.append({
                            'ip_port': ip_port,
                            'last_modified': last_modified,
                            'before_target': before_target
                        })
                        
                        if before_target is True:
                            risk_dates.append(ip_port)
                            print(f"   ⚠️  RISK: Date is before 2025-06-17")
                        elif before_target is False:
                            safe_dates.append(ip_port)
                            print(f"   ✅ SAFE: Date is after 2025-06-17")
                        else:
                            unparseable_dates.append(ip_port)
                            print(f"   ❓ UNPARSEABLE: Could not parse date")
                    else:
                        results_without_dates.append(ip_port)
                        print(f"   ❓ NO DATE: No Last-Modified header")
                else:
                    print(f"   ❌ FAILED: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   💥 EXCEPTION: {e}")
        
        print(f"\n📊 Sample Analysis Summary:")
        print(f"   📅 Results with dates: {len(results_with_dates)}")
        print(f"   ❓ Results without dates: {len(results_without_dates)}")
        print(f"   ⚠️  Risk dates (before 2025-06-17): {len(risk_dates)}")
        print(f"   ✅ Safe dates (after 2025-06-17): {len(safe_dates)}")
        print(f"   ❓ Unparseable dates: {len(unparseable_dates)}")
        
        if results_with_dates:
            print(f"\n📋 All dates found:")
            for result in results_with_dates:
                status = "⚠️ RISK" if result['before_target'] is True else "✅ SAFE" if result['before_target'] is False else "❓ UNKNOWN"
                print(f"   {status}: {result['ip_port']} -> {result['last_modified']}")
        
        if len(risk_dates) == 0 and len(results_with_dates) > 0:
            print(f"\n🤔 Analysis: All found dates are AFTER 2025-06-17")
            print(f"   This might explain why your scan shows Risk: 0")
            print(f"   The devices might have been updated recently")
        
    except Exception as e:
        print(f"❌ Error during sample analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_scan_sample()
