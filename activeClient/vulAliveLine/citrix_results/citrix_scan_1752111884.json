{"scan_info": {"timestamp": 1752111884, "target_date": "2025-06-17", "total_ips": 2, "stats": {"total": 2, "successful": 2, "failed": 0, "before_target_date": 1, "after_target_date": 0, "no_last_modified": 1, "http_accessible": 1, "https_accessible": 1, "rtsp_accessible": 0, "upnp_accessible": 0}, "scan_type": "detailed"}, "results": [{"ip": "*******", "port": "80", "ip_port": "*******:80", "protocol": "http", "protocols_tried": ["http"], "timestamp": "2025-07-08T10:00:00", "https_accessible": false, "http_accessible": true, "rtsp_accessible": false, "upnp_accessible": false, "last_modified": "Wed, 08 Jan 2025 03:14:44 GMT", "last_modified_parsed": "2025-01-08T03:14:44+00:00", "before_target_date": true, "error": null, "response_time": 1.5, "status_code": 200, "response_headers": {"Server": "nginx"}, "response_body": "<html>test</html>", "content_length": 100, "server": "nginx"}, {"ip": "*******", "port": "443", "ip_port": "*******:443", "protocol": "https", "protocols_tried": ["https"], "timestamp": "2025-07-08T10:01:00", "https_accessible": true, "http_accessible": false, "rtsp_accessible": false, "upnp_accessible": false, "last_modified": null, "last_modified_parsed": null, "before_target_date": null, "error": null, "response_time": 2.1, "status_code": 200, "response_headers": {"Server": "Apache"}, "response_body": "<html>test2</html>", "content_length": 150, "server": "Apache"}]}