#!/usr/bin/env python3
"""
Test script to verify staggered scanning functionality
"""
import time
import threading
from datetime import datetime

def simulate_citrix_scan():
    """Simulate Citrix scan"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🔵 Starting Citrix scan...")
    time.sleep(5)  # Simulate scan duration
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🔵 Citrix scan completed")

def simulate_sharepoint_scan():
    """Simulate SharePoint scan"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🟢 Starting SharePoint scan...")
    time.sleep(5)  # Simulate scan duration
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🟢 SharePoint scan completed")

def test_staggered_execution():
    """Test staggered execution of scans"""
    print("Testing staggered scan execution...")
    print("=" * 50)
    
    # Start Citrix scan immediately
    citrix_thread = threading.Thread(target=simulate_citrix_scan)
    citrix_thread.start()
    
    # Wait 2 seconds, then start SharePoint scan
    time.sleep(2)
    sharepoint_thread = threading.Thread(target=simulate_sharepoint_scan)
    sharepoint_thread.start()
    
    # Wait for both to complete
    citrix_thread.join()
    sharepoint_thread.join()
    
    print("=" * 50)
    print("✅ Staggered execution test completed")

if __name__ == '__main__':
    test_staggered_execution()
