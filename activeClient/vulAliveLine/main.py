import json
import time
import random
import os
from datetime import datetime, timezone
import requests
from apscheduler.schedulers.blocking import BlockingScheduler
import datetime as dt
import pathlib
import threading
from concurrent.futures import ThreadPoolExecutor

# Disable SSL warnings
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Simple configuration and logging
class SimpleLogger:
    def __init__(self, log_file=None, result_log_file=None):
        self.log_file = log_file
        self.result_log_file = result_log_file

    def log(self, level, message):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {level}: {message}"
        print(log_message)

        # Write to log file if specified
        if self.log_file:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(log_message + '\n')
            except Exception as e:
                print(f"[{timestamp}] ERROR: Failed to write to log file: {e}")

    def log_result_only(self, level, message):
        """Log only to result file, not to scan.log or console"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {level}: {message}"

        # Only write to result log file
        if self.result_log_file:
            try:
                with open(self.result_log_file, 'a', encoding='utf-8') as f:
                    f.write(log_message + '\n')
            except Exception as e:
                print(f"[{timestamp}] ERROR: Failed to write to result log file: {e}")

# Proxy Pool Manager
class ProxyPool:
    def __init__(self, proxy_file=None):
        self.proxies = []
        self.current_index = 0
        self.lock = threading.Lock()
        self.failed_proxies = set()
        self.proxy_stats = {}

        if proxy_file:
            self.load_proxies_from_file(proxy_file)
        else:
            # Add some free proxy sources for testing
            self.load_default_proxies()

    def load_proxies_from_file(self, proxy_file):
        """Load proxies from file"""
        try:
            with open(proxy_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # Support formats: ip:port, ip:port:username:password, http://ip:port
                        if '://' not in line:
                            line = f'http://{line}'
                        self.proxies.append(line)

            scan_log.log('INFO', f'Loaded {len(self.proxies)} proxies from {proxy_file}')
        except Exception as e:
            scan_log.log('WARNING', f'Failed to load proxies from file: {e}')
            self.load_default_proxies()

    def load_default_proxies(self):
        """Load proxies from free proxy sources"""
        scan_log.log('INFO', 'Fetching proxies from free proxy sources...')

        # Try to fetch from multiple free proxy sources
        fetched_proxies = []

        # Source 1: Free Proxy List API
        try:
            fetched_proxies.extend(self.fetch_from_free_proxy_list())
        except Exception as e:
            scan_log.log('WARNING', f'Failed to fetch from free-proxy-list: {e}')

        # Source 2: ProxyList API
        try:
            fetched_proxies.extend(self.fetch_from_proxylist_geonode())
        except Exception as e:
            scan_log.log('WARNING', f'Failed to fetch from proxylist-geonode: {e}')

        # Source 3: GitHub proxy lists
        try:
            fetched_proxies.extend(self.fetch_from_github_sources())
        except Exception as e:
            scan_log.log('WARNING', f'Failed to fetch from GitHub sources: {e}')

        # Remove duplicates and format
        unique_proxies = list(set(fetched_proxies))
        self.proxies = [f'http://{proxy}' if not proxy.startswith('http') else proxy for proxy in unique_proxies]

        if self.proxies:
            scan_log.log('INFO', f'Loaded {len(self.proxies)} proxies from free sources')
            # Save fetched proxies to file for future use
            self.save_proxies_to_file()
        else:
            scan_log.log('INFO', 'No proxies fetched - using direct connection')

    def get_proxy(self):
        """Get next available proxy with smart selection"""
        if not self.proxies:
            return None

        with self.lock:
            # Filter out failed proxies
            available_proxies = [p for p in self.proxies if p not in self.failed_proxies]

            if not available_proxies:
                # If all proxies failed, try to auto-update
                scan_log.log('WARNING', 'All proxies failed, attempting auto-update...')
                try:
                    self.auto_update_proxies()
                    available_proxies = [p for p in self.proxies if p not in self.failed_proxies]
                except Exception as e:
                    scan_log.log('ERROR', f'Auto-update failed: {e}')

                if not available_proxies:
                    # Reset failed proxies as last resort
                    scan_log.log('WARNING', 'Resetting failed proxy list as last resort')
                    self.failed_proxies.clear()
                    available_proxies = self.proxies

            if available_proxies:
                # Smart proxy selection: prefer proxies with better success rates
                if self.proxy_stats:
                    # Sort by success rate (success / (success + failed))
                    def get_success_rate(proxy):
                        if proxy in self.proxy_stats:
                            stats = self.proxy_stats[proxy]
                            total = stats['success'] + stats['failed']
                            return stats['success'] / total if total > 0 else 0
                        return 0.5  # Default rate for untested proxies

                    available_proxies.sort(key=get_success_rate, reverse=True)

                proxy = available_proxies[self.current_index % len(available_proxies)]
                self.current_index += 1
                return proxy

        return None

    def mark_proxy_failed(self, proxy):
        """Mark a proxy as failed"""
        with self.lock:
            self.failed_proxies.add(proxy)
            if proxy not in self.proxy_stats:
                self.proxy_stats[proxy] = {'success': 0, 'failed': 0}
            self.proxy_stats[proxy]['failed'] += 1

        scan_log.log('DEBUG', f'Marked proxy as failed: {proxy}')

    def mark_proxy_success(self, proxy):
        """Mark a proxy as successful"""
        with self.lock:
            if proxy not in self.proxy_stats:
                self.proxy_stats[proxy] = {'success': 0, 'failed': 0}
            self.proxy_stats[proxy]['success'] += 1

    def get_stats(self):
        """Get proxy usage statistics"""
        return {
            'total_proxies': len(self.proxies),
            'failed_proxies': len(self.failed_proxies),
            'available_proxies': len(self.proxies) - len(self.failed_proxies),
            'proxy_stats': self.proxy_stats
        }

    def test_proxy(self, proxy, test_url='http://httpbin.org/ip', timeout=10):
        """Test if a proxy is working"""
        try:
            session = requests.Session()
            session.proxies = {
                'http': proxy,
                'https': proxy
            }

            response = session.get(test_url, timeout=timeout, verify=False)
            if response.status_code == 200:
                return True, f"Proxy working - Response: {response.text[:100]}"
            else:
                return False, f"HTTP {response.status_code}"

        except Exception as e:
            return False, str(e)

    def test_all_proxies(self):
        """Test all proxies and return results"""
        if not self.proxies:
            return []

        scan_log.log('INFO', f'Testing {len(self.proxies)} proxies...')
        results = []

        for proxy in self.proxies:
            working, message = self.test_proxy(proxy)
            results.append({
                'proxy': proxy,
                'working': working,
                'message': message
            })

            status = "✅ WORKING" if working else "❌ FAILED"
            scan_log.log('INFO', f'{status}: {proxy} - {message}')

        working_count = sum(1 for r in results if r['working'])
        scan_log.log('INFO', f'Proxy test completed: {working_count}/{len(self.proxies)} proxies working')

        return results

    def fetch_from_free_proxy_list(self):
        """Fetch proxies from free-proxy-list.net API"""
        proxies = []
        try:
            import requests

            # Use a simple HTTP request to get proxy data
            url = "https://www.proxy-list.download/api/v1/get?type=http"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                proxy_list = response.text.strip().split('\n')
                for proxy in proxy_list:
                    if ':' in proxy and len(proxy.split(':')) == 2:
                        proxies.append(proxy.strip())

            scan_log.log('DEBUG', f'Fetched {len(proxies)} proxies from proxy-list.download')

        except Exception as e:
            scan_log.log('DEBUG', f'Error fetching from proxy-list.download: {e}')

        return proxies[:50]  # Limit to 50 proxies

    def fetch_from_proxylist_geonode(self):
        """Fetch proxies from proxylist.geonode.com API"""
        proxies = []
        try:
            import requests

            url = "https://proxylist.geonode.com/api/proxy-list?limit=50&page=1&sort_by=lastChecked&sort_type=desc&protocols=http%2Chttps"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    for proxy_info in data['data']:
                        if 'ip' in proxy_info and 'port' in proxy_info:
                            proxy = f"{proxy_info['ip']}:{proxy_info['port']}"
                            proxies.append(proxy)

            scan_log.log('DEBUG', f'Fetched {len(proxies)} proxies from proxylist.geonode.com')

        except Exception as e:
            scan_log.log('DEBUG', f'Error fetching from proxylist.geonode.com: {e}')

        return proxies

    def fetch_from_github_sources(self):
        """Fetch proxies from GitHub proxy repositories"""
        proxies = []
        github_sources = [
            "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
            "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt",
            "https://raw.githubusercontent.com/proxy4parsing/proxy-list/main/http.txt"
        ]

        for source_url in github_sources:
            try:
                import requests

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }

                response = requests.get(source_url, headers=headers, timeout=10)
                if response.status_code == 200:
                    proxy_list = response.text.strip().split('\n')
                    for proxy in proxy_list:
                        proxy = proxy.strip()
                        if ':' in proxy and len(proxy.split(':')) == 2:
                            proxies.append(proxy)

                scan_log.log('DEBUG', f'Fetched {len(proxy_list)} proxies from {source_url}')

            except Exception as e:
                scan_log.log('DEBUG', f'Error fetching from {source_url}: {e}')
                continue

        return proxies[:100]  # Limit to 100 proxies total

    def save_proxies_to_file(self):
        """Save current proxy list to file"""
        try:
            proxy_file = data_storage_dir.joinpath('proxy_list.txt')
            with open(proxy_file, 'w', encoding='utf-8') as f:
                f.write("# Auto-generated proxy list\n")
                f.write(f"# Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("# Format: ip:port\n\n")

                for proxy in self.proxies:
                    # Remove http:// prefix for file storage
                    clean_proxy = proxy.replace('http://', '').replace('https://', '')
                    f.write(f"{clean_proxy}\n")

            scan_log.log('INFO', f'Saved {len(self.proxies)} proxies to {proxy_file}')

        except Exception as e:
            scan_log.log('WARNING', f'Failed to save proxies to file: {e}')

    def auto_update_proxies(self):
        """Automatically update proxy list from free sources"""
        scan_log.log('INFO', 'Starting automatic proxy update...')

        old_count = len(self.proxies)

        # Clear current proxies and fetch new ones
        self.proxies = []
        self.failed_proxies.clear()
        self.proxy_stats.clear()

        # Fetch new proxies
        self.load_default_proxies()

        new_count = len(self.proxies)
        scan_log.log('INFO', f'Proxy update completed: {old_count} -> {new_count} proxies')

        # Test a sample of new proxies
        if self.proxies:
            sample_size = min(10, len(self.proxies))
            sample_proxies = random.sample(self.proxies, sample_size)

            scan_log.log('INFO', f'Testing {sample_size} sample proxies...')
            working_count = 0

            for proxy in sample_proxies:
                working, _ = self.test_proxy(proxy, timeout=5)
                if working:
                    working_count += 1

            success_rate = (working_count / sample_size * 100) if sample_size > 0 else 0
            scan_log.log('INFO', f'Sample test results: {working_count}/{sample_size} working ({success_rate:.1f}%)')

        return new_count

    def cleanup_bad_proxies(self, min_success_rate=0.1, min_attempts=5):
        """Remove proxies with consistently poor performance"""
        if not self.proxy_stats:
            return 0

        removed_count = 0
        proxies_to_remove = []

        for proxy, stats in self.proxy_stats.items():
            total_attempts = stats['success'] + stats['failed']
            if total_attempts >= min_attempts:
                success_rate = stats['success'] / total_attempts
                if success_rate < min_success_rate:
                    proxies_to_remove.append(proxy)

        # Remove bad proxies
        for proxy in proxies_to_remove:
            if proxy in self.proxies:
                self.proxies.remove(proxy)
                removed_count += 1
            if proxy in self.failed_proxies:
                self.failed_proxies.remove(proxy)
            if proxy in self.proxy_stats:
                del self.proxy_stats[proxy]

        if removed_count > 0:
            scan_log.log('INFO', f'Cleaned up {removed_count} consistently failing proxies')
            # Save updated proxy list
            self.save_proxies_to_file()

        return removed_count

    def get_proxy_quality_report(self):
        """Generate a quality report for current proxies"""
        if not self.proxy_stats:
            return "No proxy usage statistics available"

        report_lines = []
        report_lines.append("📊 Proxy Quality Report")
        report_lines.append("=" * 50)

        # Sort proxies by success rate
        proxy_performance = []
        for proxy, stats in self.proxy_stats.items():
            total = stats['success'] + stats['failed']
            success_rate = (stats['success'] / total * 100) if total > 0 else 0
            proxy_performance.append({
                'proxy': proxy,
                'success_rate': success_rate,
                'total_attempts': total,
                'success': stats['success'],
                'failed': stats['failed']
            })

        proxy_performance.sort(key=lambda x: x['success_rate'], reverse=True)

        # Top performers
        report_lines.append("🏆 Top 5 Performing Proxies:")
        for i, proxy_info in enumerate(proxy_performance[:5]):
            report_lines.append(f"  {i+1}. {proxy_info['proxy']} - "
                              f"{proxy_info['success_rate']:.1f}% success "
                              f"({proxy_info['success']}/{proxy_info['total_attempts']})")

        # Bottom performers
        if len(proxy_performance) > 5:
            report_lines.append("\n⚠️  Bottom 5 Performing Proxies:")
            for i, proxy_info in enumerate(proxy_performance[-5:]):
                report_lines.append(f"  {i+1}. {proxy_info['proxy']} - "
                                  f"{proxy_info['success_rate']:.1f}% success "
                                  f"({proxy_info['success']}/{proxy_info['total_attempts']})")

        # Overall statistics
        total_attempts = sum(p['total_attempts'] for p in proxy_performance)
        total_success = sum(p['success'] for p in proxy_performance)
        overall_rate = (total_success / total_attempts * 100) if total_attempts > 0 else 0

        report_lines.append(f"\n📈 Overall Statistics:")
        report_lines.append(f"  Total Proxies: {len(self.proxies)}")
        report_lines.append(f"  Tested Proxies: {len(proxy_performance)}")
        report_lines.append(f"  Failed Proxies: {len(self.failed_proxies)}")
        report_lines.append(f"  Overall Success Rate: {overall_rate:.1f}%")
        report_lines.append(f"  Total Attempts: {total_attempts}")

        return "\n".join(report_lines)

# Global configuration
TARGET_DATE = datetime(2025, 6, 17, tzinfo=timezone.utc)  # Target date for Last-Modified comparison
TIMEZONE = "UTC"
USE_PROXY = False  # Set to False to disable proxy usage

# Scanning speed configuration
SCAN_MODE = "fast"  # Options: "conservative", "normal", "fast", "aggressive"

# Global protocol mapping for IP:PORT combinations
PROTOCOL_MAPPING = {}

# Scan target configurations - easily add new scan targets here
SCAN_TARGETS = {
    'citrix': {
        'file_path': 'data/citrix_target_ip.txt',
        'output_prefix': 'citrix',
        'results_dir': 'citrix_results',
        'log_file': 'scan.log',
        'result_log_file': 'result.log',
        'description': 'Citrix NetScaler Gateway devices'
    },
    'sharepoint': {
        'file_path': 'data/sharepoint_target_url.txt',
        'output_prefix': 'sharepoint',
        'results_dir': 'sharepoint_results',
        'log_file': 'sharepoint_scan.log',
        'result_log_file': 'sharepoint_result.log',
        'description': 'SharePoint servers'
    },
    'sharepoint-test': {
        'file_path': 'data/sharepoint_target_url_test.txt',
        'output_prefix': 'sharepoint_test',
        'results_dir': 'sharepoint_results',
        'log_file': 'sharepoint_test_scan.log',
        'result_log_file': 'sharepoint_test_result.log',
        'description': 'SharePoint servers (test)'
    },
    'citrix-test': {
        'file_path': 'data/citrix_target_ip_test.txt',
        'output_prefix': 'citrix_test',
        'results_dir': 'citrix_results',
        'log_file': 'citrix_test_scan.log',
        'result_log_file': 'citrix_test_result.log',
        'description': 'Citrix NetScaler Gateway devices (test)'
    }
}

# Speed configurations
SPEED_CONFIGS = {
    "conservative": {
        "max_workers": 5,
        "batch_size": 30,
        "timeout": 10,
        "request_delay": (2.0, 5.0),
        "batch_delay": (10.0, 15.0),
        "retries": 3
    },
    "normal": {
        "max_workers": 10,
        "batch_size": 50,
        "timeout": 5,
        "request_delay": (1.0, 3.0),
        "batch_delay": (5.0, 10.0),
        "retries": 2
    },
    "fast": {
        "max_workers": 20,
        "batch_size": 100,
        "timeout": 3,
        "request_delay": (0.1, 0.5),
        "batch_delay": (1.0, 2.0),
        "retries": 1
    },
    "aggressive": {
        "max_workers": 100,
        "batch_size": 500,
        "timeout": 1,
        "request_delay": (0.01, 0.1),
        "batch_delay": (0.1, 0.5),
        "retries": 0
    }
}

# Get current speed config
CURRENT_CONFIG = SPEED_CONFIGS.get(SCAN_MODE, SPEED_CONFIGS["fast"])

# Directory settings
relative_directory = pathlib.Path(__file__).parent
data_storage_dir = relative_directory.joinpath('data')
result_save_dir = relative_directory.joinpath('results')

# Ensure directories exist
result_save_dir.mkdir(exist_ok=True)

# Initialize logger with log files
log_file_path = result_save_dir.joinpath('scan.log')
result_log_file_path = result_save_dir.joinpath('result.log')
scan_log = SimpleLogger(log_file=log_file_path, result_log_file=result_log_file_path)

# Initialize proxy pool
proxy_file_path = data_storage_dir.joinpath('proxy_list.txt')
proxy_pool = ProxyPool(proxy_file_path if proxy_file_path.exists() else None)

def get_utc_timestamp():
    """Get UTC timestamp"""
    return int(time.time())

def get_fake_ua():
    """Get random User-Agent"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ]
    return random.choice(user_agents)



def parse_last_modified(last_modified_str):
    """Parse Last-Modified header field"""
    if not last_modified_str:
        return None

    try:
        # HTTP date format: "Wed, 21 Oct 2015 07:28:00 GMT"
        return datetime.strptime(last_modified_str, "%a, %d %b %Y %H:%M:%S %Z").replace(tzinfo=timezone.utc)
    except ValueError:
        try:
            # Try other common formats
            return datetime.strptime(last_modified_str, "%a, %d %b %Y %H:%M:%S GMT").replace(tzinfo=timezone.utc)
        except ValueError:
            scan_log.log('WARNING', f"Cannot parse Last-Modified date format: {last_modified_str}")
            return None

def is_before_target_date(last_modified):
    """Check if date is before target date"""
    target_date = datetime(2025, 6, 17, tzinfo=timezone.utc)
    return last_modified < target_date

def try_rtsp_protocol(ip, port, timeout=3):
    """Try RTSP protocol for streaming services"""
    import socket

    result = {
        'success': False,
        'protocol': 'rtsp',
        'error': None,
        'response_time': None,
        'status_code': None,
        'response_headers': {},
        'response_body': None,
        'content_length': 0,
        'server': None,
        'last_modified': None
    }

    start_time = time.time()

    try:
        # Try to connect to RTSP port
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        sock.connect((ip, int(port)))

        # Send RTSP OPTIONS request
        rtsp_request = f"OPTIONS rtsp://{ip}:{port}/ RTSP/1.0\r\nCSeq: 1\r\nUser-Agent: VulAliveLine-Scanner\r\n\r\n"
        sock.send(rtsp_request.encode())

        # Receive response
        response_data = sock.recv(1024).decode('utf-8', errors='ignore')
        sock.close()

        result['success'] = True
        result['response_time'] = time.time() - start_time
        result['response_body'] = response_data[:1024]  # Limit response
        result['content_length'] = len(response_data)

        # Parse RTSP response for server info
        lines = response_data.split('\n')
        for line in lines:
            if line.startswith('Server:'):
                result['server'] = line.split(':', 1)[1].strip()
            elif line.startswith('RTSP/'):
                # Extract status code from RTSP response
                parts = line.split()
                if len(parts) >= 2:
                    try:
                        result['status_code'] = int(parts[1])
                    except ValueError:
                        result['status_code'] = 200  # Assume success if we got a response

        if not result['status_code']:
            result['status_code'] = 200  # Default to success if we got any response

    except socket.timeout:
        result['error'] = "RTSP timeout"
    except socket.error as e:
        result['error'] = f"RTSP socket error: {str(e)}"
    except Exception as e:
        result['error'] = f"RTSP error: {str(e)}"

    return result

def try_upnp_protocol(ip, port, timeout=3):
    """Try UPnP protocol - simplified for Citrix NetScaler Gateway devices"""
    result = {
        'success': False,
        'protocol': 'upnp',
        'error': None,
        'response_time': None,
        'status_code': None,
        'response_headers': {},
        'response_body': None,
        'content_length': 0,
        'server': None,
        'last_modified': None
    }

    start_time = time.time()

    try:
        # Simple UPnP paths for Citrix NetScaler Gateway
        upnp_paths = [
            '/',
            '/vpn/index.html',
            '/logon/LogonPoint/index.html',
            '/citrix/XenApp',
            '/description.xml',
            '/rootDesc.xml',
            '/device.xml'
        ]

        # Standard HTTP headers
        headers = {
            'User-Agent': 'VulAliveLine-Scanner/1.0',
            'Accept': '*/*',
            'Connection': 'close'
        }

        for path in upnp_paths:
            try:
                url = f"http://{ip}:{port}{path}"
                response = requests.get(url, headers=headers, timeout=timeout,
                                      verify=False, allow_redirects=False)

                # If we get any successful response, use it
                if response.status_code < 500:
                    result['success'] = True
                    result['response_time'] = time.time() - start_time
                    result['status_code'] = response.status_code
                    result['response_headers'] = dict(response.headers)
                    result['response_body'] = response.text[:1024]
                    result['content_length'] = len(response.content)

                    # Extract server information
                    if 'Server' in response.headers:
                        result['server'] = response.headers['Server']
                    elif 'server' in response.headers:
                        result['server'] = response.headers['server']

                    # Extract Last-Modified if present
                    if 'Last-Modified' in response.headers:
                        result['last_modified'] = response.headers['Last-Modified']
                    elif 'last-modified' in response.headers:
                        result['last_modified'] = response.headers['last-modified']

                    break

            except requests.exceptions.RequestException:
                continue

        if not result['success']:
            result['error'] = "No UPnP endpoints responded"

    except Exception as e:
        result['error'] = f"UPnP error: {str(e)}"

    return result

def try_single_protocol(ip, port, protocol, timeout=None, use_proxy=True):
    """Try a single protocol for an IP:PORT combination"""
    if timeout is None:
        timeout = CURRENT_CONFIG["timeout"]

    result = {
        'success': False,
        'protocol': protocol,
        'error': None,
        'response_time': None,
        'status_code': None,
        'response_headers': None,
        'response_body': None,
        'content_length': None,
        'server': None,
        'last_modified': None,
        'proxy_used': None
    }

    # Handle RTSP protocol separately
    if protocol == 'rtsp':
        return try_rtsp_protocol(ip, port, timeout)

    # Handle UPnP protocol separately
    if protocol == 'upnp':
        return try_upnp_protocol(ip, port, timeout)

    start_time = time.time()

    try:
        # Construct URL
        if protocol == 'https':
            if port == '443':
                url = f"https://{ip}"
            else:
                url = f"https://{ip}:{port}"
        else:  # http
            if port == '80':
                url = f"http://{ip}"
            else:
                url = f"http://{ip}:{port}"

        # Use more realistic headers to avoid detection
        headers = {
            'User-Agent': get_fake_ua(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'close',
            'Cache-Control': 'max-age=0',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'DNT': '1',
        }

        # Configure session for better connection handling
        session = requests.Session()
        session.headers.update(headers)

        # Set up proxy if available and enabled
        proxy_used = None
        if use_proxy:
            proxy = proxy_pool.get_proxy()
            if proxy:
                proxy_used = proxy
                session.proxies = {
                    'http': proxy,
                    'https': proxy
                }
                result['proxy_used'] = proxy

        # Set up adapters with retry strategy
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        retry_strategy = Retry(
            total=CURRENT_CONFIG["retries"],  # Use dynamic retry count
            backoff_factor=0.1,  # Shorter wait time between retries
            status_forcelist=[429, 500, 502, 503, 504],  # HTTP status codes to retry
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Send request with session
        response = session.get(url, timeout=timeout, verify=False)

        # Mark proxy as successful if used
        if proxy_used:
            proxy_pool.mark_proxy_success(proxy_used)

        result['success'] = True
        result['response_time'] = time.time() - start_time
        result['status_code'] = response.status_code
        result['response_headers'] = dict(response.headers)
        result['server'] = response.headers.get('Server')
        result['last_modified'] = response.headers.get('Last-Modified')

        # Store response body (limit to first 10KB)
        try:
            response_text = response.text[:10240]
            result['response_body'] = response_text
            result['content_length'] = len(response.content)
        except Exception as e:
            result['response_body'] = f"Error reading response body: {str(e)}"
            result['content_length'] = 0

    except requests.exceptions.Timeout:
        result['error'] = "Request timeout"
        if proxy_used:
            proxy_pool.mark_proxy_failed(proxy_used)
    except requests.exceptions.SSLError as e:
        result['error'] = f"SSL error: {str(e)}"
        if proxy_used:
            proxy_pool.mark_proxy_failed(proxy_used)
    except requests.exceptions.ConnectionError as e:
        error_msg = str(e)
        if "Connection refused" in error_msg:
            result['error'] = "Connection refused - service not running or port closed"
        elif "Name or service not known" in error_msg:
            result['error'] = "DNS resolution failed - invalid hostname/IP"
        elif "Network is unreachable" in error_msg:
            result['error'] = "Network unreachable - routing issue"
        elif "Connection timed out" in error_msg:
            result['error'] = "Connection timeout - firewall or network issue"
        else:
            result['error'] = f"Connection error: {error_msg}"

        # Mark proxy as failed for connection errors (might be proxy issue)
        if proxy_used:
            proxy_pool.mark_proxy_failed(proxy_used)
    except requests.exceptions.RequestException as e:
        result['error'] = f"HTTP request error: {str(e)}"
        if proxy_used:
            proxy_pool.mark_proxy_failed(proxy_used)
    except Exception as e:
        result['error'] = f"Unexpected error: {str(e)}"
        if proxy_used:
            proxy_pool.mark_proxy_failed(proxy_used)

    return result

def check_https_last_modified(ip_port, timeout=None):
    """Check HTTP/HTTPS service Last-Modified header with protocol fallback"""
    # Initialize default result first
    result = {
        'ip': '',
        'port': '',
        'ip_port': ip_port,
        'protocol': None,
        'protocols_tried': [],
        'timestamp': datetime.now().isoformat(),
        'https_accessible': False,
        'http_accessible': False,
        'rtsp_accessible': False,
        'upnp_accessible': False,
        'last_modified': None,
        'last_modified_parsed': None,
        'before_target_date': None,
        'error': None,
        'response_time': None,
        'status_code': None,
        'response_headers': None,
        'response_body': None,
        'content_length': None,
        'server': None
    }

    try:
        if timeout is None:
            timeout = CURRENT_CONFIG["timeout"]

        # Parse IP:PORT
        if ':' in ip_port:
            ip, port = ip_port.split(':', 1)
        else:
            ip, port = ip_port, '443'  # Default to 443 if no port specified

        result['ip'] = ip
        result['port'] = port

        # Get original protocol from mapping if available
        original_protocol = PROTOCOL_MAPPING.get(ip_port)

        # Get protocols to try for this port
        protocols_to_try = get_protocols_for_port(port, original_protocol)

        # Try each protocol until one succeeds
        for protocol in protocols_to_try:
            protocol_result = try_single_protocol(ip, port, protocol, timeout, use_proxy=USE_PROXY)
            result['protocols_tried'].append(protocol)

            if protocol_result['success']:
                # Success! Update result with this protocol's data
                result['protocol'] = protocol
                if protocol == 'https':
                    result['https_accessible'] = True
                elif protocol == 'http':
                    result['http_accessible'] = True
                elif protocol == 'rtsp':
                    result['rtsp_accessible'] = True
                elif protocol == 'upnp':
                    result['upnp_accessible'] = True

                result['response_time'] = protocol_result['response_time']
                result['status_code'] = protocol_result['status_code']
                result['response_headers'] = protocol_result['response_headers']
                result['response_body'] = protocol_result['response_body']
                result['content_length'] = protocol_result['content_length']
                result['server'] = protocol_result['server']
                result['last_modified'] = protocol_result['last_modified']

                # Parse Last-Modified date
                if result['last_modified']:
                    parsed_date = parse_last_modified(result['last_modified'])
                    if parsed_date:
                        result['last_modified_parsed'] = parsed_date.isoformat()
                        result['before_target_date'] = is_before_target_date(parsed_date)

                scan_log.log('DEBUG', f"Successfully checked {ip_port} with {protocol.upper()}: Status={result['status_code']}, Last-Modified={result['last_modified']}")
                break
            else:
                # Log failed attempt only to file, with special handling for 554 port
                if scan_log.log_file:
                    if port == '554' and 'Connection refused' in str(protocol_result['error']):
                        scan_log.log('DEBUG', f"Port 554 connection refused for {ip_port} with {protocol.upper()} - this may be normal for RTSP ports")
                    else:
                        scan_log.log('DEBUG', f"Failed {protocol.upper()} for {ip_port}: {protocol_result['error']}")

        # If all protocols failed, use the last error
        if not result['protocol']:
            result['error'] = f"All protocols failed. Tried: {', '.join(protocols_to_try)}"
            if scan_log.log_file:
                scan_log.log('DEBUG', f"All protocols failed for {ip_port}")

        # Add dynamic delay based on scan mode
        delay_range = CURRENT_CONFIG["request_delay"]
        time.sleep(random.uniform(delay_range[0], delay_range[1]))

    except Exception as e:
        # If any unexpected error occurs, ensure we return a valid result
        result['error'] = f"Unexpected error in check_https_last_modified: {str(e)}"
        scan_log.log('ERROR', f"Unexpected error checking {ip_port}: {e}")

    return result

def bulk_check_https(open_ip_ports, max_workers=10, batch_size=100):
    """Bulk check HTTP/HTTPS service Last-Modified headers with batching"""
    scan_log.log('INFO', f'Start checking HTTP/HTTPS Last-Modified for {len(open_ip_ports)} IP:PORT combinations')
    scan_log.log('INFO', f'Using batch processing: {batch_size} IPs per batch with {max_workers} concurrent workers')

    results = []
    stats = {
        'total': len(open_ip_ports),
        'successful': 0,
        'failed': 0,
        'before_target_date': 0,
        'after_target_date': 0,
        'no_last_modified': 0,
        'http_accessible': 0,
        'https_accessible': 0,
        'rtsp_accessible': 0,
        'upnp_accessible': 0
    }

    # Process in batches to avoid overwhelming targets
    from concurrent.futures import ThreadPoolExecutor, as_completed
    import math

    total_batches = math.ceil(len(open_ip_ports) / batch_size)

    try:
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min((batch_num + 1) * batch_size, len(open_ip_ports))
            batch_ip_ports = open_ip_ports[start_idx:end_idx]

            scan_log.log('INFO', f'Processing batch {batch_num + 1}/{total_batches} ({len(batch_ip_ports)} IP:PORT combinations)')

            try:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # Submit batch tasks
                    future_to_ip_port = {executor.submit(check_https_last_modified, ip_port): ip_port for ip_port in batch_ip_ports}

                    # Process completed tasks in this batch
                    for future in as_completed(future_to_ip_port):
                        ip_port = future_to_ip_port[future]
                        try:
                            result = future.result()

                            # Ensure result has all required fields (defensive programming)
                            if not isinstance(result, dict):
                                scan_log.log('ERROR', f'Invalid result type for {ip_port}: {type(result)}')
                                stats['failed'] += 1
                                continue

                            # Create a default result template and merge with actual result
                            default_result = {
                                'ip': '',
                                'port': '',
                                'ip_port': ip_port,
                                'protocol': None,
                                'protocols_tried': [],
                                'timestamp': datetime.now().isoformat(),
                                'https_accessible': False,
                                'http_accessible': False,
                                'rtsp_accessible': False,
                                'upnp_accessible': False,
                                'last_modified': None,
                                'last_modified_parsed': None,
                                'before_target_date': None,
                                'error': None,
                                'response_time': None,
                                'status_code': None,
                                'response_headers': None,
                                'response_body': None,
                                'content_length': None,
                                'server': None
                            }

                            # Merge actual result with default template
                            for key, value in result.items():
                                if key in default_result:
                                    default_result[key] = value

                            result = default_result
                            results.append(result)

                            # Update statistics
                            if result['https_accessible'] or result['http_accessible'] or result['rtsp_accessible'] or result['upnp_accessible']:
                                stats['successful'] += 1

                                # Count protocol-specific successes
                                if result['https_accessible']:
                                    stats['https_accessible'] += 1
                                if result['http_accessible']:
                                    stats['http_accessible'] += 1
                                if result['rtsp_accessible']:
                                    stats['rtsp_accessible'] += 1
                                if result['upnp_accessible']:
                                    stats['upnp_accessible'] += 1

                                if result['before_target_date'] is True:
                                    stats['before_target_date'] += 1
                                elif result['before_target_date'] is False:
                                    stats['after_target_date'] += 1
                                else:
                                    stats['no_last_modified'] += 1
                            else:
                                stats['failed'] += 1

                        except Exception as e:
                            scan_log.log('ERROR', f'Error processing {ip_port}: {e}')
                            scan_log.log('DEBUG', f'Result type: {type(result) if "result" in locals() else "undefined"}')
                            if 'result' in locals() and isinstance(result, dict):
                                scan_log.log('DEBUG', f'Result keys: {list(result.keys())}')
                            stats['failed'] += 1

            except KeyboardInterrupt:
                scan_log.log('WARNING', f'Batch {batch_num + 1} interrupted by user')
                raise  # Re-raise to be caught by outer handler
            except Exception as e:
                scan_log.log('ERROR', f'Error in batch {batch_num + 1}: {e}')
                # Continue with next batch

            # Show progress after each batch
            completed = stats['successful'] + stats['failed']
            progress_pct = (completed / stats['total'] * 100) if stats['total'] > 0 else 0
            scan_log.log('INFO', f'Batch {batch_num + 1} completed. Progress: {completed}/{stats["total"]} ({progress_pct:.1f}%) - '
                       f'Success: {stats["successful"]}, Failed: {stats["failed"]}, Risk: {stats["before_target_date"]}')

            # Add dynamic delay between batches based on scan mode
            if batch_num < total_batches - 1:  # Don't sleep after the last batch
                delay_range = CURRENT_CONFIG["batch_delay"]
                batch_delay = random.uniform(delay_range[0], delay_range[1])
                scan_log.log('INFO', f'Waiting {batch_delay:.1f} seconds before next batch...')
                time.sleep(batch_delay)

    except KeyboardInterrupt:
        scan_log.log('WARNING', 'Bulk checking interrupted by user')
        # Update stats to reflect actual processed count
        stats['total'] = len(results)
        raise  # Re-raise to be caught by caller
    except Exception as e:
        scan_log.log('ERROR', f'Unexpected error in bulk checking: {e}')
        # Update stats to reflect actual processed count
        stats['total'] = len(results)

    scan_log.log('INFO', f'Finished HTTP/HTTPS checking - Total: {stats["total"]}, '
               f'Success: {stats["successful"]}, Risk IP:PORT combinations: {stats["before_target_date"]}')

    return results, stats

def is_https_port(port):
    """Check if port should use HTTPS protocol"""
    # Common HTTPS ports
    https_ports = {443, 8443, 9443, 10443}

    try:
        port_num = int(port)
        return port_num in https_ports
    except ValueError:
        return False

def get_protocols_for_port(port, original_protocol=None):
    """Get protocol for a given port - simplified for Citrix NetScaler Gateway"""
    try:
        port_num = int(port)

        # If we have original protocol information, use it directly
        if original_protocol:
            if original_protocol == 'https':
                return ['https']
            elif original_protocol == 'http':
                return ['http']
            elif original_protocol == 'upnp':
                return ['upnp']

        # HTTPS ports - use HTTPS
        if port_num in {443, 8443, 9443, 10443, 4443}:
            return ['https']

        # All other ports - use HTTP
        else:
            return ['http']

    except ValueError:
        return ['http']

def parse_target_entry(line):
    """Parse a single target entry and return (ip, port, protocol) tuple"""
    line = line.strip()

    if line.startswith('http://'):
        # HTTP URL format: http://ip:port
        url_part = line[7:]  # Remove 'http://'
        if ':' in url_part:
            ip, port = url_part.split(':', 1)
            return ip, port, 'http'
        else:
            return url_part, '80', 'http'  # Default HTTP port

    elif line.startswith('https://'):
        # HTTPS URL format: https://ip:port
        url_part = line[8:]  # Remove 'https://'
        if ':' in url_part:
            ip, port = url_part.split(':', 1)
            return ip, port, 'https'
        else:
            return url_part, '443', 'https'  # Default HTTPS port

    elif line.startswith('upnp '):
        # UPnP format: upnp ip:port
        upnp_part = line[5:]  # Remove 'upnp '
        if ':' in upnp_part:
            ip, port = upnp_part.split(':', 1)
            return ip, port, 'upnp'
        else:
            return upnp_part, '1900', 'upnp'  # Default UPnP port

    elif ':' in line:
        # Legacy IP:PORT format - treat as HTTP
        ip, port = line.split(':', 1)
        return ip, port, 'http'

    else:
        # Single IP - treat as HTTP with default port
        return line, '80', 'http'

def load_ip_port_list(ip_file_path="data/citrix_target_ip.txt"):
    """Load IP:PORT list from file with support for HTTP, HTTPS, and UPnP protocols"""
    global PROTOCOL_MAPPING

    try:
        parsed_entries = []
        protocol_counts = {'http': 0, 'https': 0, 'upnp': 0}

        with open(ip_file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()

                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue

                try:
                    ip, port, protocol = parse_target_entry(line)

                    # Create IP:PORT combination for processing
                    ip_port = f"{ip}:{port}"

                    # Store with protocol information
                    parsed_entries.append({
                        'ip_port': ip_port,
                        'ip': ip,
                        'port': port,
                        'protocol': protocol,
                        'original_line': line
                    })

                    protocol_counts[protocol] += 1

                except Exception as e:
                    scan_log.log('WARNING', f"Failed to parse line {line_num}: '{line}' - {e}")
                    continue

        # Remove duplicates based on IP:PORT combination
        unique_entries = {}
        for entry in parsed_entries:
            key = entry['ip_port']
            if key not in unique_entries:
                unique_entries[key] = entry

        unique_list = list(unique_entries.values())

        # Populate global protocol mapping
        PROTOCOL_MAPPING.clear()
        for entry in unique_list:
            PROTOCOL_MAPPING[entry['ip_port']] = entry['protocol']

        scan_log.log('INFO', f"Loaded {len(parsed_entries)} entries, {len(unique_list)} unique IP:PORT combinations from {ip_file_path}")
        scan_log.log('INFO', f"Protocol distribution - HTTP: {protocol_counts['http']}, "
                           f"HTTPS: {protocol_counts['https']}, UPnP: {protocol_counts['upnp']}")

        # Return list of IP:PORT strings for compatibility with existing code
        return [entry['ip_port'] for entry in unique_list]

    except Exception as e:
        scan_log.log('ERROR', f"Failed to load IP:PORT list from {ip_file_path}: {e}")
        return []

def save_results(results, stats, timestamp, scan_target='citrix'):
    """Save scan results in two formats to target-specific results directory"""
    try:
        # Get target configuration
        target_config = SCAN_TARGETS.get(scan_target, SCAN_TARGETS['citrix'])

        # Create results directory if it doesn't exist
        results_dir = pathlib.Path(target_config['results_dir'])
        results_dir.mkdir(exist_ok=True)

        # 1. Save detailed results with full response data (target_scan_*.json)
        detailed_output_file = results_dir.joinpath(f'{target_config["output_prefix"]}_scan_{timestamp}.json')
        scan_data = {
            'scan_info': {
                'timestamp': timestamp,
                'target_date': '2025-06-17',
                'total_ips': stats['total'],
                'stats': stats,
                'scan_type': 'detailed',  # Mark as detailed format for database processing
                'scan_target': scan_target,
                'description': target_config['description']
            },
            'results': results
        }

        with open(detailed_output_file, 'w', encoding='utf-8') as f:
            json.dump(scan_data, f, indent=2, ensure_ascii=False)

        scan_log.log('INFO', f'Detailed results saved to {detailed_output_file}')

        # 2. Save simplified statistical results (target_*.json) - consistent with other components
        collector = {
            'scan_info': {
                'timestamp': timestamp,
                'target_date': '2025-06-17',
                'total_ips': stats['total'],
                'stats': stats,
                'scan_type': 'unified',  # Mark as unified format for database processing
                'scan_target': scan_target,
                'description': target_config['description']
            },
            f'{target_config["output_prefix"]}_scan': [],
            f'{target_config["output_prefix"]}_stats': []
        }

        # Add each IP:PORT result
        for result in results:
            accessible = result['https_accessible'] or result['http_accessible'] or result['rtsp_accessible']
            collector[f'{target_config["output_prefix"]}_scan'].append((
                timestamp,
                'global',  # country placeholder
                result['ip_port'],  # Use IP:PORT combination
                accessible,
                result['status_code'] or 0,
                result['last_modified'] or '',
                result['before_target_date'] if result['before_target_date'] is not None else False,
                result['server'] or '',
                result['content_length'] or 0,
                result['response_time'] or 0,
                result['error'] or '',
                result['protocol'] or 'unknown'  # Add protocol information
            ))

        # Add statistical information
        collector[f'{target_config["output_prefix"]}_stats'].append((
            timestamp,
            'global',
            stats['total'],
            stats['successful'],
            stats['failed'],
            stats['before_target_date'],
            stats['after_target_date'],
            stats['no_last_modified']
        ))

        # Save unified format results
        unified_output_file = results_dir.joinpath(f'{target_config["output_prefix"]}_{timestamp}.json')
        with open(unified_output_file, 'w', encoding='utf-8') as f:
            json.dump(collector, f, ensure_ascii=False)

        scan_log.log('INFO', f'Unified results saved to {unified_output_file}')

        # Also save to local results directory for backward compatibility
        local_detailed = result_save_dir.joinpath(f'{target_config["output_prefix"]}_scan_{timestamp}.json')
        local_unified = result_save_dir.joinpath(f'{target_config["output_prefix"]}_{timestamp}.json')

        with open(local_detailed, 'w', encoding='utf-8') as f:
            json.dump(scan_data, f, indent=2, ensure_ascii=False)

        with open(local_unified, 'w', encoding='utf-8') as f:
            json.dump(collector, f, ensure_ascii=False)

        scan_log.log('INFO', f'Results also saved locally to results directory')

        return detailed_output_file, unified_output_file

    except Exception as e:
        scan_log.log('ERROR', f'Failed to save results: {e}')

def perform_scan(scan_target='citrix'):
    """Perform scan for specified target type"""
    try:
        # Get target configuration
        target_config = SCAN_TARGETS.get(scan_target, SCAN_TARGETS['citrix'])

        # Create target-specific logger
        target_log_file = result_save_dir.joinpath(target_config['log_file'])
        target_result_log_file = result_save_dir.joinpath(target_config['result_log_file'])
        target_logger = SimpleLogger(log_file=target_log_file, result_log_file=target_result_log_file)

        # Don't modify global scan_log, use target_logger directly
        target_logger.log_result_only('INFO', f'Starting {target_config["description"]} scan')
        target_logger.log('INFO', f'Starting {target_config["description"]} vulnerability scan')
        target_logger.log('INFO', f'Target file: {target_config["file_path"]}')
        target_logger.log('INFO', f'Scan mode: {SCAN_MODE}')
        target_logger.log('INFO', f'Using proxy: {USE_PROXY}')

        start_time = time.time()

        # Load target IP list
        ip_port_list = load_ip_port_list(target_config['file_path'])

        if not ip_port_list:
            target_logger.log('ERROR', f'No valid IP:PORT combinations found in {target_config["file_path"]}')
            return

        # Perform bulk scanning
        results, stats = bulk_check_https(
            ip_port_list,
            max_workers=CURRENT_CONFIG["max_workers"],
            batch_size=CURRENT_CONFIG["batch_size"],
            target_logger=target_logger
        )

        end_time = time.time()
        duration = end_time - start_time

        # Generate timestamp
        timestamp = get_utc_timestamp()

        # Save results
        save_results(results, stats, timestamp, scan_target)

        # Print summary
        print_summary(stats, duration, scan_target)

        target_logger.log_result_only('INFO', f'{target_config["description"]} scan completed successfully')

        return results, stats

    except KeyboardInterrupt:
        target_logger.log('WARNING', f'{target_config["description"]} scan interrupted by user')
        target_logger.log_result_only('WARNING', f'{target_config["description"]} scan interrupted by user')
        return None, None
    except Exception as e:
        target_logger.log('ERROR', f'Error in {target_config["description"]} scan: {e}')
        target_logger.log_result_only('ERROR', f'Error in {target_config["description"]} scan: {e}')
        return None, None

def print_summary(stats, duration, scan_target='citrix'):
    """Print scan summary and log to result file only"""
    success_rate = (stats['successful'] / stats['total'] * 100) if stats['total'] > 0 else 0

    # Get target configuration
    target_config = SCAN_TARGETS.get(scan_target, SCAN_TARGETS['citrix'])

    # Get proxy statistics
    proxy_stats = proxy_pool.get_stats()

    # Create summary lines
    summary_lines = [
        "\n" + "="*80,
        f"📊 {target_config['description']} Scan Summary Report",
        "="*80,
        f"🎯 Target Date: June 17, 2025",
        f"📅 Scan Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        f"⏱️  Duration: {duration//60:.0f} minutes {duration%60:.0f} seconds",
        "-" * 80,
        f"📋 Total IP:PORT Combinations: {stats['total']:,}",
        f"🟢 Successful Checks: {stats['successful']:,} ({success_rate:.1f}%)",
        f"🔴 Failed Checks: {stats['failed']:,}",
        "-" * 80,
        "🌐 Protocol Distribution:",
        f"   🔒 HTTPS Accessible: {stats['https_accessible']:,}",
        f"   🌐 HTTP Accessible: {stats['http_accessible']:,}",
        f"   📺 RTSP Accessible: {stats['rtsp_accessible']:,}",
        f"   🔌 UPnP Accessible: {stats['upnp_accessible']:,}",
        "-" * 80,
        "📊 Last-Modified Analysis Results:",
        f"   ⚠️  Before Target Date (Potential Risk): {stats['before_target_date']:,}",
        f"   ✅ After Target Date (Relatively Safe): {stats['after_target_date']:,}",
        f"   ❓ No Last-Modified Header: {stats['no_last_modified']:,}",
        "-" * 80,
        "🌐 Proxy Pool Statistics:",
        f"   📡 Total Proxies: {proxy_stats['total_proxies']}",
        f"   ✅ Available Proxies: {proxy_stats['available_proxies']}",
        f"   ❌ Failed Proxies: {proxy_stats['failed_proxies']}",
        "-" * 80
    ]

    # Add risk assessment if applicable
    if stats['before_target_date'] > 0:
        risk_percentage = (stats['before_target_date'] / stats['successful'] * 100) if stats['successful'] > 0 else 0
        summary_lines.append(f"⚠️  Risk Assessment: {risk_percentage:.1f}% of successfully checked IP:PORT combinations may have Citrix vulnerability risk")

    summary_lines.append("="*80)

    # Print to console and log to result file only (not scan.log)
    for line in summary_lines:
        print(line)
        scan_log.log_result_only('INFO', line)

def run_citrix_scan():
    """Run complete Citrix scanning process"""
    return perform_scan('citrix')

def run_sharepoint_scan():
    """Run complete SharePoint scanning process"""
    return perform_scan('sharepoint')

def run_all_scans():
    """Run all configured scans"""
    scan_log.log('INFO', 'Starting all configured scans')
    scan_log.log_result_only('INFO', 'Starting all configured scans')

    all_results = {}

    for scan_target in SCAN_TARGETS.keys():
        scan_log.log('INFO', f'Starting {SCAN_TARGETS[scan_target]["description"]} scan')
        results, stats = perform_scan(scan_target)
        all_results[scan_target] = {'results': results, 'stats': stats}

        # Add a delay between different scans to avoid overwhelming targets
        if scan_target != list(SCAN_TARGETS.keys())[-1]:  # Not the last scan
            scan_log.log('INFO', 'Waiting 60 seconds before next scan...')
            time.sleep(60)

    scan_log.log('INFO', 'All scans completed')
    scan_log.log_result_only('INFO', 'All scans completed')

    return all_results

def update_proxy_pool():
    """Update proxy pool from free sources"""
    scan_log.log('INFO', 'Scheduled proxy pool update started')
    try:
        new_count = proxy_pool.auto_update_proxies()
        scan_log.log('INFO', f'Proxy pool updated successfully: {new_count} proxies available')
    except Exception as e:
        scan_log.log('ERROR', f'Failed to update proxy pool: {e}')

def cleanup_proxy_pool():
    """Clean up consistently failing proxies"""
    scan_log.log('INFO', 'Scheduled proxy pool cleanup started')
    try:
        removed_count = proxy_pool.cleanup_bad_proxies()
        scan_log.log('INFO', f'Proxy pool cleanup completed: removed {removed_count} failing proxies')
    except Exception as e:
        scan_log.log('ERROR', f'Failed to cleanup proxy pool: {e}')

def run_scheduler():
    """Run scheduler with staggered scans every 14 hours"""
    scheduler = BlockingScheduler(timezone=TIMEZONE)

    # Execute Citrix scan immediately
    citrix_start_time = (dt.datetime.now().replace(tzinfo=None) + dt.timedelta(seconds=10)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(run_citrix_scan, 'date', run_date=citrix_start_time, id="citrix_scan_init", name="citrix_scan_init")

    # Execute SharePoint scan 30 minutes after Citrix
    sharepoint_start_time = (dt.datetime.now().replace(tzinfo=None) + dt.timedelta(minutes=30)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(run_sharepoint_scan, 'date', run_date=sharepoint_start_time, id="sharepoint_scan_init", name="sharepoint_scan_init")

    # Schedule Citrix scan every 14 hours (840 minutes)
    scheduler.add_job(run_citrix_scan, 'interval', minutes=840, id="citrix_scan", name="citrix_scan")

    # Schedule SharePoint scan every 14 hours (840 minutes), offset by 30 minutes
    sharepoint_interval_start = (dt.datetime.now().replace(tzinfo=None) + dt.timedelta(minutes=870)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(run_sharepoint_scan, 'date', run_date=sharepoint_interval_start, id="sharepoint_scan_first_interval", name="sharepoint_scan_first_interval")
    scheduler.add_job(run_sharepoint_scan, 'interval', minutes=840, id="sharepoint_scan", name="sharepoint_scan")

    # Update proxy pool every 6 hours (360 minutes)
    proxy_update_start = (dt.datetime.now().replace(tzinfo=None) + dt.timedelta(minutes=60)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(update_proxy_pool, 'date', run_date=proxy_update_start, id="proxy_update_init", name="proxy_update_init")
    scheduler.add_job(update_proxy_pool, 'interval', minutes=360, id="proxy_update", name="proxy_update")

    # Clean up failing proxies every 12 hours (720 minutes)
    proxy_cleanup_start = (dt.datetime.now().replace(tzinfo=None) + dt.timedelta(hours=2)).strftime("%Y-%m-%d %H:%M:%S")
    scheduler.add_job(cleanup_proxy_pool, 'date', run_date=proxy_cleanup_start, id="proxy_cleanup_init", name="proxy_cleanup_init")
    scheduler.add_job(cleanup_proxy_pool, 'interval', minutes=720, id="proxy_cleanup", name="proxy_cleanup")

    job_defaults = {
        'coalesce': False,
        'max_instances': 3  # Allow multiple scans to run simultaneously
    }
    scheduler.configure(job_defaults=job_defaults, timezone=TIMEZONE)

    scan_log.log('INFO', 'Starting staggered multi-target scan scheduler (every 14 hours)')
    scan_log.log('INFO', 'Citrix scan: immediate start, then every 14 hours')
    scan_log.log('INFO', 'SharePoint scan: 30 minutes after Citrix, then every 14 hours')
    scan_log.log('INFO', f'Configured scan targets: {", ".join([k for k in SCAN_TARGETS.keys() if not k.endswith("-test")])}')
    scan_log.log('INFO', 'Starting proxy pool update scheduler (every 6 hours)')
    scan_log.log('INFO', 'Starting proxy pool cleanup scheduler (every 12 hours)')
    scheduler.start()

def test_proxies():
    """Test all configured proxies"""
    print("🌐 Testing Proxy Pool")
    print("=" * 50)

    if not proxy_pool.proxies:
        print("❌ No proxies configured")
        print("   Add proxies to data/proxy_list.txt file")
        return

    results = proxy_pool.test_all_proxies()

    print("\n📊 Proxy Test Summary:")
    print("-" * 30)
    working_proxies = [r for r in results if r['working']]
    failed_proxies = [r for r in results if not r['working']]

    print(f"✅ Working Proxies: {len(working_proxies)}")
    for result in working_proxies:
        print(f"   {result['proxy']}")

    print(f"\n❌ Failed Proxies: {len(failed_proxies)}")
    for result in failed_proxies:
        print(f"   {result['proxy']} - {result['message']}")

def set_scan_mode(mode):
    """Set scanning mode and update global configuration"""
    global SCAN_MODE, CURRENT_CONFIG
    if mode in SPEED_CONFIGS:
        SCAN_MODE = mode
        CURRENT_CONFIG = SPEED_CONFIGS[mode]
        scan_log.log('INFO', f'Scan mode set to: {mode.upper()}')
        scan_log.log('INFO', f'Configuration: Workers={CURRENT_CONFIG["max_workers"]}, '
                           f'Batch={CURRENT_CONFIG["batch_size"]}, '
                           f'Timeout={CURRENT_CONFIG["timeout"]}s, '
                           f'Retries={CURRENT_CONFIG["retries"]}')
    else:
        scan_log.log('ERROR', f'Invalid scan mode: {mode}. Available modes: {list(SPEED_CONFIGS.keys())}')

if __name__ == '__main__':
    import sys

    # Check if running in Docker environment
    is_docker = any([
        pathlib.Path('/.dockerenv').exists(),
        'DOCKER_CONTAINER' in os.environ,
        'container' in os.environ
    ])

    print("Multi-Target HTTP/HTTPS Scanner with Proxy Support")
    if is_docker:
        print("🐳 Running in Docker container mode")

    print("Usage:")
    print("  python main.py                         - Start scheduler (scan every 14 hours)")
    print("  python main.py --once                  - Execute all scans once")
    print("  python main.py --citrix                - Execute Citrix scan only")
    print("  python main.py --sharepoint            - Execute SharePoint scan only")
    print("  python main.py --target <TARGET>       - Execute specific target scan")
    print("  python main.py --test-proxy            - Test all configured proxies")
    print("  python main.py --update-proxy          - Update proxy pool from free sources")
    print("  python main.py --proxy-report          - Show proxy quality report")
    print("  python main.py --cleanup-proxy         - Remove consistently failing proxies")
    print("  python main.py --mode <MODE>           - Set scan mode and run all scans")
    print("")
    print("Available scan targets:")
    for target, config in SCAN_TARGETS.items():
        if not target.endswith('-test'):  # Hide test targets from main usage
            print(f"  {target:<12} - {config['description']} ({config['file_path']})")
    print("")
    print("Available scan modes:")
    print("  conservative - Slow but gentle (5 workers, 30 batch, 10s timeout)")
    print("  normal       - Balanced speed (10 workers, 50 batch, 5s timeout)")
    print("  fast         - Fast scanning (20 workers, 100 batch, 3s timeout) [DEFAULT]")
    print("  aggressive   - Maximum speed (100 workers, 500 batch, 1s timeout)")
    print("")
    print(f"Current mode: {SCAN_MODE.upper()}")
    print("Note: Directly checks HTTP/HTTPS Last-Modified headers for IP:PORT combinations")
    print("      Uses HTTPS for ports 443, 8443, 9443, 10443 and HTTP for all other ports")
    print("      Supports proxy rotation from data/proxy_list.txt file")
    print("      No port scanning - directly tests all IP:PORT combinations for vulnerabilities")
    print("      Citrix and SharePoint scans run with 30-minute offset to avoid conflicts")
    print()

    if len(sys.argv) > 1:
        if sys.argv[1] == '--once':
            scan_log.log('INFO', f'Running all scans in {SCAN_MODE.upper()} mode')
            run_all_scans()
        elif sys.argv[1] == '--citrix':
            scan_log.log('INFO', f'Running Citrix scan in {SCAN_MODE.upper()} mode')
            run_citrix_scan()
        elif sys.argv[1] == '--sharepoint':
            scan_log.log('INFO', f'Running SharePoint scan in {SCAN_MODE.upper()} mode')
            run_sharepoint_scan()
        elif sys.argv[1] == '--target' and len(sys.argv) > 2:
            target = sys.argv[2].lower()
            if target in SCAN_TARGETS:
                scan_log.log('INFO', f'Running {target} scan in {SCAN_MODE.upper()} mode')
                perform_scan(target)
            else:
                print(f"Unknown target: {target}")
                print(f"Available targets: {', '.join(SCAN_TARGETS.keys())}")
        elif sys.argv[1] == '--test-proxy':
            test_proxies()
        elif sys.argv[1] == '--update-proxy':
            print("🔄 Updating proxy pool from free sources...")
            update_proxy_pool()
            print("✅ Proxy pool update completed")
        elif sys.argv[1] == '--proxy-report':
            print(proxy_pool.get_proxy_quality_report())
        elif sys.argv[1] == '--cleanup-proxy':
            print("🧹 Cleaning up consistently failing proxies...")
            removed = proxy_pool.cleanup_bad_proxies()
            print(f"✅ Removed {removed} failing proxies")
        elif sys.argv[1] == '--mode' and len(sys.argv) > 2:
            mode = sys.argv[2].lower()
            set_scan_mode(mode)
            scan_log.log('INFO', f'Running all scans in {mode.upper()} mode')
            run_all_scans()
        else:
            print(f"Unknown option: {sys.argv[1]}")
            print("Use --help to see available options")
    else:
        scan_log.log('INFO', f'Starting scheduler mode in {SCAN_MODE.upper()} mode')
        run_scheduler()
