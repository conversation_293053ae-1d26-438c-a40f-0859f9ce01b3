# Citrix Target IP Scanner (vulAliveLine)

这是一个专门用于扫描Citrix目标IP地址443端口的工具，用于检测HTTP响应中的Last-Modified字段日期，以评估潜在的安全风险。

## 功能特性

- 🎯 **大规模IP扫描**: 使用masscan快速扫描4万多个IP地址的443端口
- 🕒 **日期分析**: 解析HTTP响应中的Last-Modified字段，判断是否在2025年6月17日之前
- 🛡️ **防封禁机制**: 内置请求间隔控制、随机延迟、User-Agent轮换等防封禁措施
- ⏰ **12小时调度**: 每12小时自动扫描一次，避免过度扫描
- 📊 **详细报告**: 生成与alivescan和webStatusMonitor一致的结果格式
- 🚀 **高性能**: 基于masscan进行端口扫描，然后多线程检查HTTPS服务
- 🐳 **Docker支持**: 使用aevela/masscan:ASN镜像，内置masscan工具

## 安装和使用

### Docker运行（推荐）

vulAliveLine已集成到activeClient的docker-compose.yaml中：

```bash
# 在activeClient目录下启动所有服务
cd activeClient
docker-compose up -d

# 只启动vulAliveLine服务
docker-compose up -d vul_alive_line

# 查看vulAliveLine日志
docker-compose logs -f vul_alive_line

# 执行单次扫描
docker-compose exec vul_alive_line python3 main.py --once
```

### 本地运行

1. 安装依赖：
```bash
pip3 install -r requirements.txt
```

2. 准备IP列表文件：
确保 `data/citrix_target_ip.txt` 文件存在，每行一个IP地址。

3. 运行扫描：
```bash
# 启动调度器（每12小时扫描一次）
python3 main.py

# 执行单次扫描
python3 main.py --once

# 运行测试
python3 test_scanner.py
```

## 配置说明

### 扫描参数

- `max_concurrent`: 最大并发数（默认20）
- `batch_size`: 批次大小（默认300）
- `request_delay`: 请求间隔（0.5-2.0秒）
- `batch_delay`: 批次间延迟（3.0-8.0秒）
- `max_retries`: 最大重试次数（默认3）

### 调度配置

- 每12小时执行一次扫描（720分钟间隔）
- 启动后立即执行一次初始扫描
- 使用APScheduler进行任务调度

## 输出结果

扫描结果保存在 `results/` 目录下：

- `citrix_scan_{timestamp}.json`: 详细的JSON格式结果
- `citrix_{timestamp}.json`: 与其他组件一致的统一格式结果
- `masscan_443_{timestamp}.json`: masscan原始扫描结果（可选保留）

### 结果字段说明

- `ip`: 目标IP地址
- `https_accessible`: HTTPS是否可访问
- `last_modified`: 原始Last-Modified头信息
- `last_modified_parsed`: 解析后的日期
- `before_target_date`: 是否在目标日期（2025-06-17）之前
- `error`: 错误信息（如果有）
- `response_time`: 响应时间

### 统一格式结果

与alivescan和webStatusMonitor保持一致的数据格式：
- `citrix_scan`: 每个IP的扫描结果
- `citrix_stats`: 汇总统计信息

## 风险评估

- ⚠️ **风险IP**: Last-Modified在2025年6月17日之前的IP可能存在Citrix漏洞风险
- ✅ **相对安全**: Last-Modified在2025年6月17日之后的IP相对安全
- ❓ **未知状态**: 无Last-Modified字段的IP需要进一步分析

## 测试

运行测试脚本验证功能：
```bash
python test_scanner.py
```

## 注意事项

1. **合规使用**: 请确保只扫描授权的IP地址
2. **网络礼仪**: 工具已内置防封禁机制，请勿修改为更激进的扫描参数
3. **资源消耗**: 大规模扫描会消耗较多网络带宽和系统资源
4. **结果解读**: 扫描结果仅供参考，需要结合其他安全工具进行综合分析

## 故障排除

- 如果遇到大量超时，可以增加 `timeout` 参数
- 如果被目标服务器封禁，可以增加 `request_delay` 和 `batch_delay`
- 查看 `citrix_scanner.log` 获取详细的错误信息
