#!/usr/bin/env python3
"""Test script to verify file saving functionality"""

import sys
import os
import json
import pathlib
sys.path.append('.')

from main import save_results, get_utc_timestamp

def test_save_function():
    """Test the save_results function"""
    print("Testing save_results function:")
    print("=" * 60)
    
    # Create test data
    timestamp = get_utc_timestamp()
    
    test_results = [
        {
            'ip': '*******',
            'port': '80',
            'ip_port': '*******:80',
            'protocol': 'http',
            'protocols_tried': ['http'],
            'timestamp': '2025-07-08T10:00:00',
            'https_accessible': False,
            'http_accessible': True,
            'rtsp_accessible': False,
            'upnp_accessible': False,
            'last_modified': 'Wed, 08 Jan 2025 03:14:44 GMT',
            'last_modified_parsed': '2025-01-08T03:14:44+00:00',
            'before_target_date': True,
            'error': None,
            'response_time': 1.5,
            'status_code': 200,
            'response_headers': {'Server': 'nginx'},
            'response_body': '<html>test</html>',
            'content_length': 100,
            'server': 'nginx'
        },
        {
            'ip': '*******',
            'port': '443',
            'ip_port': '*******:443',
            'protocol': 'https',
            'protocols_tried': ['https'],
            'timestamp': '2025-07-08T10:01:00',
            'https_accessible': True,
            'http_accessible': False,
            'rtsp_accessible': False,
            'upnp_accessible': False,
            'last_modified': None,
            'last_modified_parsed': None,
            'before_target_date': None,
            'error': None,
            'response_time': 2.1,
            'status_code': 200,
            'response_headers': {'Server': 'Apache'},
            'response_body': '<html>test2</html>',
            'content_length': 150,
            'server': 'Apache'
        }
    ]
    
    test_stats = {
        'total': 2,
        'successful': 2,
        'failed': 0,
        'before_target_date': 1,
        'after_target_date': 0,
        'no_last_modified': 1,
        'http_accessible': 1,
        'https_accessible': 1,
        'rtsp_accessible': 0,
        'upnp_accessible': 0
    }
    
    print(f"Test timestamp: {timestamp}")
    print(f"Test results count: {len(test_results)}")
    print(f"Test stats: {test_stats}")
    
    # Call save_results function
    try:
        save_results(test_results, test_stats, timestamp)
        print(f"\n✅ save_results function completed without errors")
        
        # Check if files were created
        citrix_results_dir = pathlib.Path('citrix_results')
        results_dir = pathlib.Path('results')
        
        expected_files = [
            citrix_results_dir / f'citrix_scan_{timestamp}.json',
            citrix_results_dir / f'citrix_{timestamp}.json',
            results_dir / f'citrix_scan_{timestamp}.json',
            results_dir / f'citrix_{timestamp}.json'
        ]
        
        print(f"\n📁 Checking for expected files:")
        for file_path in expected_files:
            if file_path.exists():
                size = file_path.stat().st_size
                print(f"   ✅ {file_path} (size: {size} bytes)")
                
                # Check file content
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"      📄 JSON structure: {list(data.keys())}")
                    if 'scan_info' in data:
                        print(f"      📊 Scan type: {data['scan_info'].get('scan_type', 'unknown')}")
                except Exception as e:
                    print(f"      ❌ Error reading file: {e}")
            else:
                print(f"   ❌ {file_path} (NOT FOUND)")
        
        # List all files in directories
        print(f"\n📂 All files in citrix_results directory:")
        if citrix_results_dir.exists():
            for file in citrix_results_dir.iterdir():
                if file.is_file():
                    size = file.stat().st_size
                    print(f"   📄 {file.name} (size: {size} bytes)")
        else:
            print(f"   ❌ citrix_results directory does not exist")
            
        print(f"\n📂 All files in results directory:")
        if results_dir.exists():
            for file in results_dir.iterdir():
                if file.is_file() and file.name.endswith('.json'):
                    size = file.stat().st_size
                    print(f"   📄 {file.name} (size: {size} bytes)")
        else:
            print(f"   ❌ results directory does not exist")
            
    except Exception as e:
        print(f"❌ Error during save_results test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_save_function()
