# VPS Download Configuration
# Copy this file to download_config and modify the values

# VPS connection settings
VPS_IP=your_vps_ip_here
SSH_USER=ubuntu
SSH_PORT=22
SSH_KEY=~/.ssh/id_rsa

# Remote paths on VPS
REMOTE_BASE_PATH=/home/<USER>/Documents/net-watcher/activeClient/vulAliveLine
CITRIX_REMOTE_PATH=${REMOTE_BASE_PATH}/citrix_results
SHAREPOINT_REMOTE_PATH=${REMOTE_BASE_PATH}/sharepoint_results

# Local paths
LOCAL_BASE_PATH=./downloaded_results
CITRIX_LOCAL_PATH=${LOCAL_BASE_PATH}/citrix_results
SHAREPOINT_LOCAL_PATH=${LOCAL_BASE_PATH}/sharepoint_results

# Download options
DOWNLOAD_CITRIX=true
DOWNLOAD_SHAREPOINT=true
SHOW_PROGRESS=true
COMPRESS_TRANSFER=true
