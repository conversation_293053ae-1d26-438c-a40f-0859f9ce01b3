#!/bin/bash

# Advanced script to download citrix_results and sharepoint_results from VPS
# Supports configuration file and various options

set -e  # Exit on any error

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/download_config"

# Default values
VPS_IP=""
SSH_USER="ubuntu"
SSH_PORT="22"
SSH_KEY="~/.ssh/id_rsa"
REMOTE_BASE_PATH="/home/<USER>/Documents/net-watcher/activeClient/vulAliveLine"
LOCAL_BASE_PATH="./downloaded_results"
DOWNLOAD_CITRIX="true"
DOWNLOAD_SHAREPOINT="true"
SHOW_PROGRESS="true"
COMPRESS_TRANSFER="true"
DRY_RUN="false"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -c, --config FILE       Use custom config file (default: ./download_config)"
    echo "  -i, --ip IP             VPS IP address"
    echo "  -u, --user USER         SSH username (default: ubuntu)"
    echo "  -p, --port PORT         SSH port (default: 22)"
    echo "  -k, --key PATH          SSH private key path (default: ~/.ssh/id_rsa)"
    echo "  -r, --remote PATH       Remote base path"
    echo "  -l, --local PATH        Local base path (default: ./downloaded_results)"
    echo "  --citrix-only           Download only Citrix results"
    echo "  --sharepoint-only       Download only SharePoint results"
    echo "  --dry-run               Show what would be downloaded without downloading"
    echo "  --no-progress           Don't show progress during transfer"
    echo "  --no-compress           Don't compress data during transfer"
    echo ""
    echo "Examples:"
    echo "  $0 -i ************* -u root"
    echo "  $0 --config my_config --citrix-only"
    echo "  $0 --dry-run"
}

# Function to load configuration file
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        print_color $BLUE "📋 Loading configuration from: $CONFIG_FILE"
        source "$CONFIG_FILE"
    else
        print_color $YELLOW "⚠️  Configuration file not found: $CONFIG_FILE"
        print_color $YELLOW "   You can copy download_config.example to download_config and modify it"
    fi
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -i|--ip)
                VPS_IP="$2"
                shift 2
                ;;
            -u|--user)
                SSH_USER="$2"
                shift 2
                ;;
            -p|--port)
                SSH_PORT="$2"
                shift 2
                ;;
            -k|--key)
                SSH_KEY="$2"
                shift 2
                ;;
            -r|--remote)
                REMOTE_BASE_PATH="$2"
                shift 2
                ;;
            -l|--local)
                LOCAL_BASE_PATH="$2"
                shift 2
                ;;
            --citrix-only)
                DOWNLOAD_CITRIX="true"
                DOWNLOAD_SHAREPOINT="false"
                shift
                ;;
            --sharepoint-only)
                DOWNLOAD_CITRIX="false"
                DOWNLOAD_SHAREPOINT="true"
                shift
                ;;
            --dry-run)
                DRY_RUN="true"
                shift
                ;;
            --no-progress)
                SHOW_PROGRESS="false"
                shift
                ;;
            --no-compress)
                COMPRESS_TRANSFER="false"
                shift
                ;;
            *)
                print_color $RED "❌ Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Function to validate configuration
validate_config() {
    if [[ -z "$VPS_IP" ]]; then
        print_color $RED "❌ VPS IP address is required"
        print_color $YELLOW "   Use -i option or set VPS_IP in config file"
        exit 1
    fi
    
    if [[ ! -f "$SSH_KEY" ]]; then
        print_color $RED "❌ SSH key file not found: $SSH_KEY"
        exit 1
    fi
    
    # Check if rsync is available
    if ! command -v rsync &> /dev/null; then
        print_color $RED "❌ rsync is not installed. Please install rsync first:"
        echo "   Ubuntu/Debian: sudo apt-get install rsync"
        echo "   CentOS/RHEL: sudo yum install rsync"
        echo "   macOS: brew install rsync"
        exit 1
    fi
}

# Function to show configuration
show_config() {
    print_color $CYAN "🚀 Download Configuration"
    echo "=================================="
    print_color $BLUE "📡 VPS: ${SSH_USER}@${VPS_IP}:${SSH_PORT}"
    print_color $BLUE "🔑 SSH Key: ${SSH_KEY}"
    print_color $BLUE "📂 Remote base: ${REMOTE_BASE_PATH}"
    print_color $BLUE "💾 Local base: ${LOCAL_BASE_PATH}"
    print_color $BLUE "🎯 Download Citrix: ${DOWNLOAD_CITRIX}"
    print_color $BLUE "🎯 Download SharePoint: ${DOWNLOAD_SHAREPOINT}"
    print_color $BLUE "📊 Show Progress: ${SHOW_PROGRESS}"
    print_color $BLUE "🗜️  Compress Transfer: ${COMPRESS_TRANSFER}"
    if [[ "$DRY_RUN" == "true" ]]; then
        print_color $YELLOW "🔍 DRY RUN MODE - No files will be downloaded"
    fi
    echo "=================================="
}

# Function to download directory
download_directory() {
    local remote_path="$1"
    local local_path="$2"
    local description="$3"
    
    print_color $CYAN "\n📥 Downloading ${description}..."
    echo "   From: ${SSH_USER}@${VPS_IP}:${remote_path}"
    echo "   To: ${local_path}"
    
    # Create local directory
    mkdir -p "${local_path}"
    
    # Build rsync command
    local rsync_opts="-av"
    
    if [[ "$COMPRESS_TRANSFER" == "true" ]]; then
        rsync_opts="${rsync_opts}z"
    fi
    
    if [[ "$SHOW_PROGRESS" == "true" ]]; then
        rsync_opts="${rsync_opts} --progress --human-readable"
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        rsync_opts="${rsync_opts} --dry-run"
    fi
    
    local ssh_opts="ssh -p ${SSH_PORT} -i ${SSH_KEY} -o StrictHostKeyChecking=no"
    
    # Execute rsync
    if rsync ${rsync_opts} -e "${ssh_opts}" "${SSH_USER}@${VPS_IP}:${remote_path}/" "${local_path}/"; then
        print_color $GREEN "✅ ${description} download completed"
        
        if [[ "$DRY_RUN" != "true" ]]; then
            # List downloaded files
            local file_count=$(find "${local_path}" -type f 2>/dev/null | wc -l)
            if [ ${file_count} -gt 0 ]; then
                print_color $GREEN "📋 Downloaded ${file_count} files:"
                find "${local_path}" -type f -exec ls -lh {} \; 2>/dev/null | head -10 | awk '{print "   📄 " $9 " (" $5 ") - " $6 " " $7 " " $8}'
                if [ ${file_count} -gt 10 ]; then
                    echo "   ... and $((file_count - 10)) more files"
                fi
            else
                print_color $YELLOW "⚠️  No files found in ${local_path}"
            fi
        fi
        return 0
    else
        print_color $RED "❌ ${description} download failed"
        return 1
    fi
}

# Main function
main() {
    print_color $PURPLE "🎯 VPS Results Downloader"
    echo ""
    
    # Parse arguments first
    parse_args "$@"
    
    # Load configuration
    load_config
    
    # Validate configuration
    validate_config
    
    # Show configuration
    show_config
    
    # Confirm if not dry run
    if [[ "$DRY_RUN" != "true" ]]; then
        echo ""
        read -p "Continue with download? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_color $YELLOW "Download cancelled by user"
            exit 0
        fi
    fi
    
    local success_count=0
    local total_count=0
    
    # Download citrix_results
    if [[ "$DOWNLOAD_CITRIX" == "true" ]]; then
        total_count=$((total_count + 1))
        local citrix_remote="${REMOTE_BASE_PATH}/citrix_results"
        local citrix_local="${LOCAL_BASE_PATH}/citrix_results"
        
        if download_directory "${citrix_remote}" "${citrix_local}" "Citrix results"; then
            success_count=$((success_count + 1))
        fi
    fi
    
    # Download sharepoint_results
    if [[ "$DOWNLOAD_SHAREPOINT" == "true" ]]; then
        total_count=$((total_count + 1))
        local sharepoint_remote="${REMOTE_BASE_PATH}/sharepoint_results"
        local sharepoint_local="${LOCAL_BASE_PATH}/sharepoint_results"
        
        if download_directory "${sharepoint_remote}" "${sharepoint_local}" "SharePoint results"; then
            success_count=$((success_count + 1))
        fi
    fi
    
    # Summary
    print_color $CYAN "\n📊 Download Summary"
    echo "==================="
    print_color $GREEN "✅ Successful: ${success_count}/${total_count}"
    
    if [[ "$DRY_RUN" != "true" ]]; then
        print_color $BLUE "📂 Files saved to: $(realpath ${LOCAL_BASE_PATH})"
        
        # Show total downloaded size
        if command -v du &> /dev/null && [[ -d "${LOCAL_BASE_PATH}" ]]; then
            local total_size=$(du -sh "${LOCAL_BASE_PATH}" 2>/dev/null | cut -f1)
            print_color $BLUE "💾 Total downloaded size: ${total_size}"
        fi
    fi
    
    echo ""
    if [[ ${success_count} -eq ${total_count} ]]; then
        print_color $GREEN "🎉 All downloads completed successfully!"
        exit 0
    else
        print_color $YELLOW "⚠️  Some downloads failed. Check the output above for details."
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
