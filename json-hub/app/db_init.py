import setting
import pymysql
from loguru import logger

def create_table(conn, create_sql=setting.sql_create_gtr_tb):
    cur = conn.cursor()
    try:
        res = cur.execute(create_sql)  # 只是帮你执行sql语句，不会返回执行结果
        conn.commit()
        logger.info('create or check table success ')
    except Exception as e:
        logger.error(e)
        conn.rollback()
    finally:
        cur.close()
    return

if __name__ == '__main__':
    conn = pymysql.connect(host='127.0.0.1', password=setting.mysql_password, user=setting.mysql_user,
                        db=setting.mysql_db,
                        port=23306, charset=setting.mysql_charset)
    create_table(conn, setting.sql_create_gtr_tb)
    create_table(conn, setting.sql_create_cloudflare_tb)
    create_table(conn, setting.sql_create_ooni_tb)
    create_table(conn, setting.sql_create_ioda_tb)