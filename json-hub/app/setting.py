# -*- coding: utf-8 -*-
import os
VERSION = "0.5"

mysql_host = os.environ.get('DB_HOST', 'db')
mysql_port = int(os.environ.get('DB_PORT', 3306))
mysql_db = os.environ.get('DB_NAME', 'bgpdata')
mysql_tb = os.environ.get('DB_TABLE', 'bgpcountryreach')
mysql_user = os.environ.get('DB_USER', 'root')
mysql_password = os.environ.get('DB_PASSWORD', 'bgproot')
proxy = os.environ.get('PROXY', '')

# ############### database config ###################
mysql_charset = 'utf8'  # 只能写utf8，不能写utf-8
gtr_tb = 'gtr'
cloudflare_tb = 'cloudflare'
ooni_tb = 'ooni'
ioda_tb = 'ioda'

last_ip_sql = f'''SELECT n.`time`, n.country_name, n.reach_ratio 
FROM {mysql_tb} n 
INNER JOIN (
  SELECT country_name, MAX(`time`) AS `time`
  FROM {mysql_tb} GROUP BY country_name
) AS max USING (country_name, `time`) ORDER BY reach_ratio'''

last_n_ip_sql = f'''SET sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''));
SELECT `time`, country_name, reach_ratio
FROM (
    SELECT *, ROW_NUMBER() OVER (PARTITION BY country_name ORDER BY `time` DESC) AS row_num
    FROM {mysql_tb}
) AS x
WHERE row_num <= %s ORDER BY reach_ratio
'''

last_n_avg_ip_sql = f'''
SELECT `time`, country_name, AVG(reach_ratio) as "reach_ratio"
FROM (
    SELECT *, ROW_NUMBER() OVER (PARTITION BY country_name ORDER BY `time` DESC) AS row_num
    FROM {mysql_tb}
) AS x
WHERE row_num <= %s GROUP BY country_name ORDER BY reach_ratio
'''

db_eng = f'''mysql+mysqldb://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db}?charset=utf8mb4'''


sql_create_gtr_tb = f"CREATE TABLE if not exists {gtr_tb} ( \
                      region varchar(20), \
                      product bigint(20), \
                      timestamp BIGINT(20),\
                      ind double DEFAULT 0, \
                      PRIMARY KEY (region, product, timestamp));"
sql_create_cloudflare_tb = f"CREATE TABLE if not exists {cloudflare_tb} ( \
                            location varchar(20), \
                            timestamp BIGINT(20),\
                            total double DEFAULT 0, \
                            http double DEFAULT 0, \
                            PRIMARY KEY (location, timestamp));"

sql_create_ooni_tb = f"CREATE TABLE if not exists {ooni_tb} ( \
                            location varchar(20), \
                            time_grain varchar(20), \
                            app varchar(20), \
                            timestamp varchar(20), \
                            ratio double DEFAULT 0, \
                            measurement_count int DEFAULT 0, \
                            ok_count int DEFAULT 0, \
                            PRIMARY KEY (location, time_grain, app, timestamp));"

sql_create_ioda_tb = f"CREATE TABLE if not exists {ioda_tb} ( \
                            location varchar(20), \
                            type varchar(20), \
                            timestamp varchar(20), \
                            ind double DEFAULT 0, \
                            PRIMARY KEY (location, type, timestamp));"
