convert_dict = {
    'United States': 'United States of America',
    'Russian Federation': 'Russia',
    'Korea, Republic of': 'South Korea',
    "Korea, Democratic People's Republic of": "North Korea",
    'Czechia': 'Czech Republic',
    "Bolivia, Plurinational State of": "Bolivia",
    "Venezuela, Bolivarian Republic of": 'Venezuela',
    "Congo, The Democratic Republic of the": "Democratic Republic of the Congo",
    "Lao People's Democratic Republic": "Laos",
    "Viet Nam": 'Vietnam', 
    "Taiwan, Province of China": 'Taiwan',
    "Iran, Islamic Republic of": "Iran",
    "Tanzania, United Republic of": "United Republic of Tanzania",
    "Congo": "Republic of the Congo",
    "Syrian Arab Republic": "Syria",
    "Palestine, State of": "West Bank",
    "Côte d'Ivoire": "Ivory Coast",
    "Serbia": "Republic of Serbia",
    "Moldova, Republic of": "Moldova",
    "North Macedonia": "Macedonia",
}

unconvert_dict = {}
for c in convert_dict:
    unconvert_dict[convert_dict[c]] = c


def convert_specific_country(country_name):

    return convert_dict.get(country_name, country_name)


def unconvert_specific_country(country_name):
    return unconvert_dict.get(country_name, country_name)