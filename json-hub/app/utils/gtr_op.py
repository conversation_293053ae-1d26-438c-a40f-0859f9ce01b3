import pandas as pd
from loguru import logger
from app.api import gtr
import datetime

def convert_strs_to_nums(str_list, sep=','):
    nums = []
    for window_size in str_list.split(sep):
        try:
            nums.append(int(window_size))
        except Exception as e:
            continue
    return nums

def calc_gtr_ma(traffic_series, window_sizes):
    ma_df_list = [traffic_series]
    for size in window_sizes:
        df = traffic_series.rolling(window=size).mean()
        ma_df_list.append(df)
    df = pd.concat(ma_df_list, axis=1)
    df.dropna(inplace=True)
    # df.reset_index(inplace=True)
    # res = df.values.tolist()
    return df

def extract_gtr(resp):
    gtr_resp = []
    gtr_list = resp[0][1]
    for gtr in gtr_list:
        gtr_ts = gtr[0]
        gtr_value = gtr[1][0][1]
        gtr_resp.append([gtr_ts, gtr_value])
    logger.info(len(gtr_resp))
    return gtr_resp

def get_previous_gtr(report: gtr.ApiTrafficRepository, region, product, start, end, previous_size):
    pre_hour = previous_size // 2 + previous_size % 2
    end = datetime.datetime.utcfromtimestamp(end / 1000) - datetime.timedelta(hours=pre_hour)
    start = datetime.datetime.utcfromtimestamp(start / 1000) - datetime.timedelta(hours=pre_hour)
    pre_series = report.get_traffic(region, product, start, end)
    pre_series.index = pre_series.index + pre_hour * 60 * 60 * 1000
    return pre_series