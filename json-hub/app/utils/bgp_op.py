from app.mysql_conn import MysqlDB
from loguru import logger

def get_rate(rate_type='ip'):
    LAST_WEEK_NUMS = 7 * 24 // 2
    rate = {}
    if rate_type == 'ip':
        try:
            mysql_DB = MysqlDB()
            rate_list = mysql_DB.get_last_iprate()
            last_week_avg_rate_list = mysql_DB.get_last_n_avg_iprate(LAST_WEEK_NUMS)
            mysql_DB.close()
            avg_rate_dict = {}
            for avg_rate_item in last_week_avg_rate_list:
                if len(avg_rate_item) != 3:
                    continue
                avg_rate_dict[avg_rate_item[1]] = avg_rate_item[2]
            for rate_item in rate_list:
                if len(rate_item) != 3:
                    continue
                country = rate_item[1]
                rate_ratio = round(rate_item[2] / avg_rate_dict.get(country, rate_item[2]), 4)
                rate[country] = rate_ratio if rate_ratio <=1 else 1.0

        except Exception as e:
            logger.error(e)
    return rate