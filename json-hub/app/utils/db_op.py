from sqlalchemy import create_engine
from app import setting
from loguru import logger
from sqlalchemy.dialects.mysql import insert

import pandas as pd



def insert_on_conflict_update(table, conn, keys, data_iter):
    # update columns "b" and "c" on primary key conflict
    data = [dict(zip(keys, row)) for row in data_iter]
    stmt = (
        insert(table.table)
        .values(data)
    )
    stmt = stmt.on_duplicate_key_update(
        **{
            column_name: getattr(stmt.inserted, column_name)
            for column_name in keys
            })
    result = conn.execute(stmt)
    return result.rowcount

# 保存查询的结果
def save_db(data: pd.DataFrame, tb_name: str, inx_label: list, **args):
    try:
        engine = create_engine(setting.db_eng)

        # Execute the to_sql for writting DF into SQL
        data.to_sql(tb_name, engine, if_exists='append', index=True,
                    index_label=inx_label, method=insert_on_conflict_update, **args)
    except Exception as e:
        logger.error(f'save_db error: {e}')


# 检查是否已有数据并读取
def check_and_read_db():
    engine = create_engine(setting.db_eng)
    pass


    
