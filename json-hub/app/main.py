from fastapi import FastAP<PERSON>, responses

import json
import os
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import pycountry
import datetime
import pandas as pd
import sys

from app.api import gtr, ioda, cloudflare, ooni
from app.utils.country_op import *
from app.utils.bgp_op import *
from app.utils.gtr_op import *

min_level = "INFO"
SECONDS_PER_DAY = 24 * 60 * 60

def my_filter(record):
    return record["level"].no >= logger.level(min_level).no

logger.remove()
logger.add(sys.stderr, filter=my_filter)

app = FastAPI()

origins = [
    "http://127.0.0.1:23000",
    "http://localhost:23000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# def calc_gtr_ma(data, size):
#     logger.info(len(data))
#     df = pd.DataFrame(data, columns=['time', 'value'])
#     logger.info(df.values.tolist()[:10])
#     # df['time'] = pd.to_datetime(df['time'])
#     df.set_index('time', inplace=True)
#     df = df.rolling(window=size).mean()
#     logger.info(df)
#     df.dropna(inplace=True)
#     df.reset_index(inplace=True)
#     res = df.values.tolist()
#     return res

@app.get("/ioda")
async def get_ioda(region:str,
                   start:int=None, 
                   end:int=None,
                   pre_days:int=7,
                   shift:int=0):
    region = countryname2code(region)
    try:
        logger.info(f'{region=}, {start=}, {end=}, {pre_days=}, {shift=}')
        if start > 10e10: start = start // 1000
        if end > 10e10: end = end // 1000
        if shift: start -= shift
        pre_start = start - pre_days * SECONDS_PER_DAY
        pre_end = end - pre_days * SECONDS_PER_DAY
        cur_res = ioda.perform_ioda_api(region, start, end)

        pre_res = ioda.perform_ioda_api(region, pre_start, pre_end)
        pre_res.index = pre_res.index + SECONDS_PER_DAY * pre_days
        res = pd.concat([cur_res, pre_res], axis=1).reset_index().dropna()
        return {'data': res.values.tolist()}
    except Exception as error:
        logger.warning(f"Failed to get traffic for {region}: {str(error)}", )
        return {'error': -1, 'msg': str(error)}

@app.get("/ooni")
async def get_ooni(region:str,
                   start:str, 
                   end:str,
                   prod_list:str):
    region = countryname2code(region)
    try:
        logger.info(f'{region=},{start=},{end=},{prod_list=}')
        cur_res = ooni.get_traffic(country_code=region, start_dt=start, end_dt=end, prod_list=prod_list)
        cur_res = cur_res.reset_index().dropna()
        return {'data': cur_res.values.tolist()}
    except Exception as error:
        logger.warning(f"Failed to get traffic for {region}: {str(error)}", )
        return {'error': -1, 'msg': str(error)}
    

@app.get("/cloudflare")
async def get_cloudflare(region:str,
                         start:int=None, 
                         end:int=None,
                         pre_days:int=7,
                         datatype:str='traffic',
                         shift:int=0):
    region = countryname2code(region)
    try:
        logger.info(f'{region=}, {start=}, {end=}, {pre_days=}, {datatype=}, {shift=}')
        dt_format = "%Y-%m-%dT%H:%M:%SZ"
        if start > 10e10: start = start // 1000
        if end > 10e10: end = end // 1000
        if shift: start -= shift
        start = datetime.datetime.utcfromtimestamp(start)
        end = datetime.datetime.utcfromtimestamp(end)
        start_dt = start.strftime(dt_format)
        end_dt = end.strftime(dt_format)
        cur_res = cloudflare.get_traffic(region, start_dt, end_dt)
        pre_start = start - datetime.timedelta(days=pre_days)
        pre_end = end - datetime.timedelta(days=pre_days)
        pre_start_dt = pre_start.strftime(dt_format)
        pre_end_dt = pre_end.strftime(dt_format)
        pre_res = cloudflare.get_traffic(region, pre_start_dt, pre_end_dt)
        pre_res.index = pre_res.index + SECONDS_PER_DAY * pre_days
        res = pd.concat([cur_res, pre_res], axis=1).reset_index().dropna()
        return {'data': res.values.tolist()}
    except Exception as error:
        logger.warning(f"Failed to get traffic for {region}: {str(error)}", )
        return {'error': -1, 'msg': str(error)}


@app.get("/gtr")
async def get_gtr(region:str, 
                  product:int, 
                  start:int=None, 
                  end:int=None, 
                  window_size_list:str='48',
                  pre_days_list:str='336',
                  shift:int=0):
    region = countryname2code(region)
    product = gtr.ProductId(product)
    if not end:
        end = int(datetime.datetime.now().timestamp() * 1000)
    if not start:
        start = int(end - gtr.DEFAULT_INTERVAL_DAYS * 24 * 60 * 60 * 1000)
    logger.info(f'{region=}, {start=}, {end=}, {product=}, {window_size_list=}, {pre_days_list=}, {shift=}')
    if shift: start -= shift * 1000
    window_sizes = convert_strs_to_nums(window_size_list)
    previous_days = convert_strs_to_nums(pre_days_list)
    report = gtr.ApiTrafficRepository()
    try:
        max_hour = 0
        if window_sizes:
            max_size = max(window_sizes)
            max_hour = max_size // 2 + max_size % 2

        pre_df_list = []
        for pre_day in previous_days:
            previous_size = pre_day * 48
            pre_traffic_series = get_previous_gtr(report, region, product, start, end, previous_size)
            pre_df_list.append(pre_traffic_series)
        pre_df = pd.concat(pre_df_list, axis=1, ignore_index=True)
        logger.debug(pre_df)

        end = datetime.datetime.utcfromtimestamp(end // 1000)
        start = datetime.datetime.utcfromtimestamp(start // 1000) - datetime.timedelta(hours=max_hour)

        traffic_series = report.get_traffic(region, product, start, end)
        if traffic_series.empty:
            logger.info(f"No traffic for product {product} in region {region}")
            return {'data': []}
        res = calc_gtr_ma(traffic_series, window_sizes)
        res = pd.concat([res, pre_df], axis=1).reset_index().dropna()
        logger.debug(res)
        return {'data': res.values.tolist()}
    except Exception as error:
        logger.warning(f"Failed to get traffic for {region}, {product}: {str(error)}", )
        return {'error': -1, 'msg': str(error)}

@app.get("/outage/asn")
async def get_asn_outage(start:str='', 
                         end:str=''):
    try:
        res = []
        logger.info(f'{start=}, {end=}')
        mysql_DB = MysqlDB()
        outages_list = mysql_DB.get_asn_outage(start, end)
        for outage in outages_list:
            country = convert_specific_country(outage[0])
            res.append((country, outage[1]))
        return {'data': res}
    except Exception as error:
        logger.warning(f"Failed to get asn outage {start}, {end}: {str(error)}", )
        return {'error': -1, 'msg': str(error)}
    

@app.get("/geojson/{data_src}")
async def get_geojson(data_src: str):
    res = {}
    if data_src == 'bgp.geojson':
        try:
            with open('app/data/countries.geojson', 'r') as f:
                res =json.load(f)
                return res
        except Exception as error:
            logger.error(str(error))
            return {'error': -1, 'msg': str(error)}
    elif data_src == 'ip.geojson':
        res = []
        try:
            ip_rate = get_rate(rate_type='ip')
            print(ip_rate)
            for k in ip_rate:
                if k == 'UK':
                    country_name = 'United Kingdom'
                    res.append({'name': country_name,
                                'value': ip_rate[k]})
                else:
                    country = pycountry.countries.get(alpha_2=k)
                    if country:
                        res.append({'name': convert_specific_country(country.name),
                                    'value': ip_rate[k]})
            return {'data': res}
        except Exception as error:
            logger.error(str(error))
            return {'error': -1, 'msg': str(error)} 
    else:
        return {'error': -1, 'msg': 'data_src not found'} 
    
@app.get("/images")
async def get_image(img_name: str):
    path = f'app/data/{img_name}'
    if os.path.exists(path):
        return responses.FileResponse(path)
    else:
        return 
    
countries = {}
for country in pycountry.countries:
    countries[country.name] = country.alpha_2
    
def countryname2code(country_name):
    if len(country_name) <= 2:
        return country_name
    country_name = unconvert_specific_country(country_name)
    return countries.get(country_name, country_name)

    
    
    