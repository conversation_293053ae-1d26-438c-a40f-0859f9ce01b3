import pymysql
from app import setting
from loguru import logger

class MysqlDB:
    def __init__(self):
        # 创建连接对象
        self.conn = pymysql.connect(host=setting.mysql_host, password=setting.mysql_password, user=setting.mysql_user,
                               db=setting.mysql_db,
                               port=setting.mysql_port, charset=setting.mysql_charset)

    def create_table(self, create_sql=setting.sql_create_gtr_tb):
        cur = self.conn.cursor()
        try:
            res = cur.execute(create_sql)  # 只是帮你执行sql语句，不会返回执行结果
            self.conn.commit()
            logger.info('create or check table success ')
        except Exception as e:
            logger.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return


    def close(self):
        self.conn.close()


    def get_last_iprate(self):
        cur = self.conn.cursor()
        try:
            cur.execute(setting.last_ip_sql)  # 只是帮你执行sql语句，不会返回执行结果
            ip_rate = cur.fetchall()
            # logger.debug(len(ip_rate))
            self.conn.commit()
        except Exception as e:
            logger.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return ip_rate
    
    def get_last_n_iprate(self, n):
        cur = self.conn.cursor()
        try:
            cur.execute(setting.last_n_ip_sql, n)  # 只是帮你执行sql语句，不会返回执行结果
            ip_rate = cur.fetchall()
            # logger.debug(len(ip_rate))
            self.conn.commit()
        except Exception as e:
            logger.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return ip_rate
    
    def get_last_n_avg_iprate(self, n):
        cur = self.conn.cursor()
        try:
            cur.execute(setting.last_n_avg_ip_sql, n)  # 只是帮你执行sql语句，不会返回执行结果
            ip_rate = cur.fetchall()
            # logger.debug(len(ip_rate))
            self.conn.commit()
        except Exception as e:
            logger.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return ip_rate
    
    def get_asn_outage(self, start, end):
        get_asn_outage_sql = f"""
        SELECT country_name as country, COUNT(data_time) as alert_num FROM bgpasoutage  
        WHERE country_name!='UNKNOW' AND data_time>'{start}' AND data_time<'{end}'  
        GROUP BY country_name ORDER BY alert_num DESC
        """
        cur = self.conn.cursor()
        try:
            cur.execute(get_asn_outage_sql)  # 只是帮你执行sql语句，不会返回执行结果
            outage = cur.fetchall()
            self.conn.commit()
        except Exception as e:
            logger.error(e)
            self.conn.rollback()
        finally:
            cur.close()
        return outage