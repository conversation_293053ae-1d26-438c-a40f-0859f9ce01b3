import requests
from loguru import logger
import pandas as pd
import pycountry
import numpy as np
from app.utils import db_op
from sqlalchemy.types import VARCHAR


api_url = "https://api.ioda.inetintel.cc.gatech.edu/v2/signals/raw/"


def parse_data(data):
    time_list = list(range(data['from'], data['until'], data['step']))
    assert len(data['values']) == len(time_list)
    df = pd.DataFrame(data['values'], index=time_list, columns=[data['datasource']])
    return df


def region_to_id(region_name:str):
    ioda_regions = pd.read_csv('app/data/ioda_regions.csv')
    ioda_regions.set_index('name', inplace=True) 
    region_dict = ioda_regions['code'].to_dict()
    # region_dict = {'Gaza': 1226, 'Gaza Strip': 1226, 'Yunnan': 645, 'Kursk': 3435, 'Voronezh': 3450,
    #                'Tatarstan': 3467, 'Rio de Janeiro': 499, 'Lima': 3128, 'Casablanca': 2456,
    #                'Seoul': 2150, 'Damascus': 3924, 'Taipei': 4197, 'Dagestan': 3478, 'Ingush': 3413,
    #                'Penza': 3446, 'Kurgan': 3453, 'Moskovskaya': 3437, 'Petersburg': 3423,
    #                'Mandalay': 2750, 'Yangon': 2752, 'Kachin': 2760, 'Rakhine': 2754, 'Shan': 2758,
    #                'Kayin': 2749, 'Heilongjiang': 657
    #                }
    return region_dict.get(region_name, region_name)

default_cols = ['gtr-norm', 'merit-nt', 'bgp', 'ping-slash24']

# /IL?from=1696818134&until=1698027734&maxPoints=3000&sourceParams=WEB_SEARCH
def perform_ioda_api(country_code:str, start:int, end:int, step=1800):
    res_dict = {}
    country_ = pycountry.countries.get(alpha_2=country_code)
    if country_:
        url = api_url + 'country/' + country_code
    elif country_code.lower().startswith('as'):
        url = api_url + 'asn/' + country_code.lower().split('as')[-1]
    else:
        url = api_url + 'region/' + str(region_to_id(country_code.capitalize()))
    try:
        res = requests.get(url, 
                           params={'from': start, 'until': end,
                                   'sourceParams': 'WEB_SEARCH'})
        res = res.json()['data'][0]
        for data in res:
            # if data['datasource'] == 'ping-slash24':
            #     continue
            res_dict[data['datasource']] = parse_data(data)
        ioda_res = []
        for col in default_cols:
            if col in res_dict:
                ioda_res.append(res_dict[col])
                data = res_dict[col].rename(columns={col: 'ind'})
                data.index.names = ['timestamp']
                data.reset_index(inplace=True)
                data['location'] = country_code
                data['type'] = col
                idx_labels = ['location', 'type', 'timestamp']
                data.set_index(idx_labels, inplace=True)
                db_op.save_db(data, 'ioda', idx_labels, dtype={'location': VARCHAR(20),
                                                               'type': VARCHAR(20)})

            elif len(res_dict):
                fake_df = list(res_dict.values())[0]
                empty_df = pd.DataFrame([0] * len(fake_df), index=fake_df.index, columns=[col])
                ioda_res.append(empty_df)
        ioda_res = pd.concat(ioda_res, axis=1).bfill()#.ffill().dropna()
        ioda_res = ioda_res / (ioda_res.max() + 1e-12)
        # fix some datetime
        ioda_res.iloc[ioda_res.index==1731985200, :] = np.nan
        ioda_res = ioda_res.bfill()
        ioda_res = ioda_res.ffill()
        # ioda_res = ioda_res.fillna(1.0)
        # ioda_res[ioda_res<0.9] = 0.9 + random.randint(0, 100)/100 * 0.02
        # ioda_res = ioda_res.reset_index()
        # logger.info(ioda_res)
    except Exception as e:
        logger.error(e)
    return ioda_res
