import requests
from loguru import logger
import pandas as pd
from app.utils import db_op
from sqlalchemy.types import VARCHAR


api_url = "https://api.ooni.io/api/v1"

def save_ooni_data(data, country_code, prob, time_grain, aixs_key):
    data = data.rename(columns={prob + '_ratio': 'ratio', aixs_key: 'timestamp'})
    data['location'] = country_code
    data['time_grain'] = time_grain
    data['app'] = prob
    idx_labels = ['location', 'time_grain', 'app', 'timestamp']
    data.set_index(idx_labels, inplace=True)
    db_op.save_db(data, 'ooni', idx_labels, dtype={'location': VARCHAR(20),
                                                         'time_grain': VARCHAR(20),
                                                         'app': VARCHAR(20), 
                                                         'timestamp': VARCHAR(20)})

def get_traffic(country_code:str, start_dt:str, end_dt:str, prod_list:str):
    res = []
    endpoint = "/aggregation"
    url = api_url + endpoint
    df_list = []
    for prob in prod_list.split(','):
        try:
            time_grain = 'day'
            aixs_key = 'measurement_start_day'
            params={'test_name': prob,
                    'probe_cc': country_code,
                    'since': start_dt,
                    'until': end_dt,
                    'time_grain': time_grain,
                    'axis_x': aixs_key,
                    'format': 'JSON'}
            res = requests.get(url, params=params)
            res = res.json()['result']
            res = pd.DataFrame(res).fillna(0)[[aixs_key, 'measurement_count', 'ok_count']]
            res[prob + '_ratio'] = res['ok_count'] / res['measurement_count']
            save_ooni_data(res, country_code, prob, time_grain, aixs_key)
            res = res.set_index(aixs_key).rename({'measurement_count': prob + '_total', 'ok_count': prob + '_ok'})
            df_list.append(res)
        except Exception as e:
            logger.error(e)
    res = pd.concat(df_list, axis=1).bfill().dropna()
    return res

