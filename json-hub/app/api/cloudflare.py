import requests
from loguru import logger
import pandas as pd
import numpy as np
import urllib

from app.utils import db_op
from sqlalchemy.types import VARCHAR


# name=all&product=ALL&dateStart=2023-10-17&dateEnd=2023-10-19&format=json&aggInterval=1h
token = "h2IUjl00uAuyF70wfKOR1Mf8LLbHQStY2rw5xYbQ"
api_url = "https://api.cloudflare.com/client/v4/radar"

def get_traffic(country_code:str, start_dt:str, end_dt:str):
    res = []
    endpoint = "/netflows/timeseries"
    url = api_url + endpoint
    try:

        if country_code.lower().startswith('as'):
            asn =country_code.lower().split('as')[-1]
            params={'name': 'total',
                    'product': 'ALL',
                    'asn': asn,
                    'dateStart': start_dt,
                    'dateEnd': end_dt}
            qs1 = urllib.parse.urlencode(params)
            params={'name': 'http',
                    'product': 'HTTP',
                    'asn': asn,
                    'dateStart': start_dt,
                    'dateEnd': end_dt}
            qs2 = urllib.parse.urlencode(params)
        else:
            params={'name': 'total',
                    'product': 'ALL',
                    'location': country_code,
                    'dateStart': start_dt,
                    'dateEnd': end_dt}
            qs1 = urllib.parse.urlencode(params)
            params={'name': 'http',
                    'product': 'HTTP',
                    'location': country_code,
                    'dateStart': start_dt,
                    'dateEnd': end_dt}
            qs2 = urllib.parse.urlencode(params)
        res = requests.get(url+f'?{qs1}&{qs2}', headers={"Authorization": f"Bearer {token}",
                                        "Content-Type": "application/json"})
        res = res.json()['result']
        # logger.info(res)
        total_ts = res['total']['timestamps']
        total_value = res['total']['values']
        df_total = pd.DataFrame(total_value, index=total_ts, columns=['total'])
        # res = requests.get(url, 
        #                    params={'name': 'http', 'product': 'HTTP',
        #                            'location': country_code,
        #                            'dateStart': start_dt,
        #                            'dateEnd': end_dt},
        #                     headers={"Authorization": f"Bearer {token}",
        #                                 "Content-Type": "application/json"})
        # res = res.json()['result']
        http_ts = res['http']['timestamps']
        http_value = res['http']['values']
        df_http = pd.DataFrame(http_value, index=http_ts, columns=['http'])
        res = pd.concat([df_total, df_http], axis=1).bfill().dropna()
        res.index = pd.to_datetime(res.index).values.astype(np.int64) // 10 **9
        res.index.names = ['timestamp']
        data = res.reset_index()
        data['location'] = country_code
        idx_labels = ['location', 'timestamp']
        data.set_index(idx_labels, inplace=True)
        db_op.save_db(data, 'cloudflare', idx_labels, dtype={'location': VARCHAR(20)})
    except Exception as e:
        logger.error(e)
    return res
